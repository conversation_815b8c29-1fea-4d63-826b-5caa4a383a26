<template>
  <div
    :id="containerId"
    :style="{ height: '100%' }"/>
</template>

<script>
export default {
  name: 'flaw-bars-chart',
  props: {
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    chartData2: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 46
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
        window.addEventListener('resize', this.resizeChart)
      }
      const maxY = this.getMax()
      // console.log(maxY)
      const options = {
        tooltip: {
          confine: true,
          show: this.showToolbox,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          padding: 10,
          formatter: function(params) {
            const data = params[0].data
            let note = `<p style="line-height: 24px">${data.name}
                        &emsp;
                        <b style="font-weight: bold">${data.value}</b></p>`
            return note
          }
        },
        color: this.color,
        grid: {
          top: this.showLegend ? '18%' : '15%',
          left: '0%',
          right: '1%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: this.xData,
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              interval: 0,
              rotate: this.labelRotate || 0
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            nameTextStyle: {
              color: '#fff',
              nameLocation: 'center'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'right',
              formatter: value => {
                if (value >= 10000) {
                  return value / 10000 + 'w'
                }
                return value
              }
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          },
          {
            name: '',
            type: 'value',
            nameTextStyle: {
              color: '#fff'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'left'
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        series: [
          // bar1
          {
            type: 'bar',
            barGap: '100%',
            z: 9,
            barWidth: this.barWidth || 60 / this.chartData.length + '%',
            barMaxWidth: this.barWidth || 12,
            showBackground: this.barBackground,
            backgroundStyle: {
              color: 'rgba(232, 236, 239, 0.3)'
            },
            markPoint: {
              symbolSize: 5
            },
            label: {
              show: true,
              position: 'inside',
              color: '#fff',
              textBorderWidth: 0
            },
            data: this.chartData.map(item => {
              item.itemStyle = {
                color: item.finished ? '#19BE6B' : '#FF2855'
              }
              item.label = {
                show: item.show
              }
              return item
            })
          },
          {
            type: 'bar',
            barGap: '100%',
            z: 2,
            barWidth: this.barWidth || 60 / this.chartData.length + '%',
            barMaxWidth: this.barWidth || 12,
            showBackground: this.barBackground,
            backgroundStyle: {
              color: 'rgba(232, 236, 239, 0.3)'
            },
            markPoint: {
              symbolSize: 5
            },
            label: {
              show: true,
              position: 'top',
              color: '#fff',
              textBorderWidth: 0,
              formatter: params => {
                if (params.data.act > params.data.value) {
                  return ''
                } else {
                  return params.data.value
                }
              }
            },
            data: this.chartData.map(item => {
              return {
                value: item.plan,
                act: item.value,
                label: {
                  show: item.show
                },
                itemStyle: {
                  color: 'transparent',
                  borderColor: item.finished ? '#19BE6B' : '#FF2855',
                  borderWidth: 1
                }
              }
            })
          },
          // 实绩
          {
            type: 'bar',
            barGap: '0',
            barWidth: this.barWidth || 60 / this.chartData.length + '%',
            barMaxWidth: this.barWidth || 12,
            showBackground: this.barBackground,
            backgroundStyle: {
              color: 'rgba(232, 236, 239, 0.3)'
            },
            markPoint: {
              symbolSize: 5
            },
            label: {
              show: true,
              position: 'inside',
              color: '#fff',
              textBorderWidth: 0
            },
            data: this.chartData2.map(item => {
              item.label = {
                show: item.show
              }
              item.itemStyle = {
                // color: item.finished ? '#19BE6B' : '#FF2855'
                color: '#555'
              }
              return item
            })
          },
          // 实绩
          {
            type: 'bar',
            barGap: '-30%',
            barCategoryGap: '10%',
            barWidth: this.barWidth || 60 / this.chartData.length + '%',
            barMaxWidth: this.barWidth || 12,
            showBackground: this.barBackground,
            backgroundStyle: {
              color: 'rgba(232, 236, 239, 0.3)'
            },
            markPoint: {
              symbolSize: 5
            },
            label: {
              show: true,
              position: 'top',
              color: '#fff',
              textBorderWidth: 0,
              formatter: params => {
                if (params.data.act > params.data.value) {
                  return ''
                } else {
                  return params.data.value
                }
              }
            },
            data: this.chartData2.map(item => {
              return {
                value: item.plan,
                act: item.value,
                label: {
                  show: item.show
                },
                itemStyle: {
                  color: 'transparent',
                  // borderColor: item.finished ? '#19BE6B' : '#FF2855',
                  borderColor: '#fff',
                  borderWidth: 1
                }
              }
            })
          }
        ]
      }
      this.myChart.setOption(options)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    getMax() {
      let max1 = Math.max(
        ...[].concat(...this.chartData.map(item => [item.value, item.plan]))
      )
      let max2 = Math.max(
        ...[].concat(...this.chartData2.map(item => [item.value, item.plan]))
      )
      let maxMax = 0
      maxMax = max1 >= max2 ? max1 : max2
      // 向上取整计算倍数
      const maxLength = maxMax
        .toString()
        .split('.')
        .pop().length
      // 向上取整
      maxMax = this.formatInt(maxMax, maxLength - 2)
      // console.log(maxMax)
      if (max1 >= max2) {
        return this.YmaxvalAndinterval(
          maxMax,
          max2,
          this.chartlcm(maxMax, max2),
          5
        )
      } else {
        return this.YmaxvalAndinterval(
          max1,
          maxMax,
          this.chartlcm(max1, maxMax),
          5
        )
      }
    },
    /**
     * 将数字取整为10的倍数
     * @param {Number} num 需要取整的值
     * @param {Boolean} ceil 是否向上取整
     * @param {Number} prec 需要用0占位的数量
     */
    formatInt(num, prec = 2, ceil = true) {
      const len = String(num).length
      if (len <= prec) {
        return num
      }
      const mult = Math.pow(10, prec)
      return ceil ? Math.ceil(num / mult) * mult : Math.floor(num / mult) * mult
    },
    // echarts专用求最大公约数 不含小数
    chartlcm(a, b) {
      var result = 1
      for (var i = 1; i <= a && i <= b; i++) {
        if (a % i == 0 && b % i == 0) {
          result = i
        }
        if (result > 1 && i >= 10)
          //公约数大于10的时候 直接跳出 避免y轴刻度太多  (如果不介意刻度太多可以把这一段去掉）
          break
      }
      return result
    },
    // 获取echarts  多Y轴的最大值和间隔值 lcmval 最大公约数   divisor 间隔数量
    YmaxvalAndinterval(m, n, lcmval, divisor = 5) {
      var interval1 = Math.ceil(m / lcmval)
      var interval2 = Math.ceil(n / lcmval)

      if (lcmval !== 1 && lcmval !== 0 && lcmval <= 10) {
        return { max1: m, max2: n, interval1, interval2 }
      }

      m = Math.ceil(m / divisor) * divisor

      n = Math.ceil(n / divisor) * divisor

      interval1 = Math.ceil(m / divisor)

      interval2 = Math.ceil(n / divisor)

      return { max1: m, max2: n, interval1, interval2 }
    },

    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
