<template>
  <div 
    :id="containerId" 
    :style="{ height: height + 'px' }"/>
</template>

<script>
export default {
  name: 'line-chart',
  props: {
    height: {
      type: Number,
      default: 200
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return []
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showSymbol: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId),
          'custom'
        )
        this.myChart.getZr().on('click', params => {
          this.$emit('selected', params)
        })
        window.addEventListener('resize', this.resizeChart)
      }
      const options = {
        title: {
          text: 'Stacked Line',
          show: false
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          show: this.showLegend,
          right: 10,
          itemHeight: this.showSymbol ? 8 : 0, // 修改icon图形大小
          itemWidth: 25, // 修改icon图形大小
          lineStyle: {
            join: 'bevel'
          },
          textStyle: {
            color: '#334681',
            fontSize: 12
          }
        },
        grid: {
          top: this.showLegend ? '18%' : '3%',
          left: '0.5%',
          right: '2.5%',
          bottom: '1%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.xData.length ? this.xData : null,
          axisLine: {
            lineStyle: {
              color: 'rgb(234,235,240)'
            }
          },
          minorTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              // 使用深浅的间隔色
              type: 'dashed',
              color: 'rgba(234,235,240,0.5)'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#8590B3',
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgb(234,235,240)'
            }
          },
          minorTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              // 使用深浅的间隔色
              type: 'dashed',
              color: 'rgb(234,235,240)'
            }
          },
          axisLabel: {
            color: '#8590B3',
            fontSize: 12
          }
        },
        series: this.chartData.map(item => {
          return {
            name: item.name || '',
            type: 'line',
            symbol: 'emptyCircle',
            showSymbol: this.showSymbol,
            symbolSize: 6,
            data: item.data,
            smooth: true,
            areaStyle: {
              color: 'rgba(14,156,255,0.2)'
            },
            lineStyle: {
              normal: {
                width: 2
              }
            }
          }
        })
      }
      this.myChart.setOption(options)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c === 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style scoped>
</style>
