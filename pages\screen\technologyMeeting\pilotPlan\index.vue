<template>
  <div class="content">
    <div class="content-item">
      <screen-border :title="cDateZH + '份产品、工艺试制计划'">
        <template v-slot:headerRight>
          <span
            class="screen-btn"
            @click="changeTab(2)">
            关闭
          </span>
          <span
            class="screen-btn"
            @click="clearFilter('tableEl')">
            清除过滤
          </span>
          <span
            v-command="'/screen/technologyMeeting/edit'"
            class="screen-btn"
            @click="pilotPlan1.dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            编辑新增计划
          </span>
          <span
            v-command="'/screen/technologyMeeting/edit'"
            class="screen-btn"
            @click="pilotPlan2.dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            编辑转接计划
          </span>
        </template>
        <div 
          ref="table1" 
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            ref="tableEl"
            :data="showDataList"
            :span-method="arraySpanMethod"
            :max-height="pilotPlan1.maxHeight"
            :row-class-name="totalClass"
            size="mini"
            class="center-table"
            border>
            <el-table-column
              property="serialNumber"
              label="编号"
              fixed="left"
              width="60"/>
            <el-table-column
              property="department"
              label="申报部门"
              fixed="left"
              width="85"/>
            <el-table-column
              property="stlgrd"
              label="钢种"
              fixed="left"
              width="70"/>
            <el-table-column
              property="tpPurpose"
              label="试制目的"
              min-width="120">
              <template v-slot="{row}">
                <text-display :text="row.tpPurpose"/>
              </template>
            </el-table-column>
            <el-table-column
              :label="'品种\n成熟度'"
              property="varietyMaturity"
              width="70"/>
            <el-table-column
              :label="'坯料\n钢种'"
              property="billetStlgrd"
              width="60">
              <template v-slot="{row}">
                <text-display :text="row.billetStlgrd"/>
              </template>
            </el-table-column>
            <el-table-column
              :label="'坯料尺寸\n(厚*宽)\nmm'"
              property="blankSize"
              width="65"/>
            <el-table-column
              :label="'试制量\n(t)'"
              property="tpQuantity"
              width="45"/>
            <el-table-column
              :label="'炼钢量\n(t)'"
              property="steelmakingCapacity"
              width="45"
            />
            <el-table-column
              :label="'轧制量\n(t)'"
              property="rollingQuantity"
              width="45"/>
            <el-table-column
              :label="'热处理\n量(t)'"
              property="htCapacity"
              width="45"/>
            <el-table-column
              property="htProcess"
              label="热处理工序"
              width="45"/>
            <el-table-column
              :label="'轧制\n产线'"
              property="plt"
              width="35"/>
            <el-table-column
              :label="'成品规格\n(mm*mm)'"
              property="fgSpec"
              width="70">
              <template v-slot="{row}">
                <text-display :text="row.fgSpec"/>
              </template>
            </el-table-column>
            <el-table-column
              :label="'交货\n状态'"
              property="deliveryStatus"
              width="45"/>
            <el-table-column
              property="tpDisposal"
              label="试制品处置"
              min-width="120">
              <template v-slot="{row}">
                <text-display :text="row.tpDisposal"/>
              </template>
            </el-table-column>
            <el-table-column
              property="testTime"
              label="试验时间节点"
              min-width="120">
              <template v-slot="{row}">
                <text-display :text="row.testTime"/>
              </template>
            </el-table-column>
            <el-table-column
              property="head"
              label="负责人"
              width="50"/>
            <el-table-column
              property="testOrderNo"
              label="测试订单号"
              width="60"/>
            <el-table-column
              property="testOrderTime"
              label="测试订单下发时间"
              width="60"/>
            <el-table-column
              property="testPlanTime"
              label="试验方案下发时间"
              width="60"/>
            <el-table-column
              label="第一周">
              <el-table-column
                property="firstWeekPlan"
                label="周计划"
                width="135">
                <template v-slot="{row}">
                  <div class="text-left">
                    <template v-if="row.firstWeekPlan">
                      <text-display :text="'周计划：' + row.firstWeekPlan"/>
                    </template>
                    <template v-if="row.firstWeek === '未完成'">
                      <p class="table-line"><text-display :text="'未完成原因：' + row.firstWeekPlanResult"/></p>
                      <p class="table-line"><text-display :text="'责任部门：' + row.firstWeekPlt"/></p>
                    </template>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' },{ text: '', value: '' }]"
                :filter-method="filterFirstWeek"
                filter-placement="bottom-end"
                property="firstWeek"
                label="完成情况"
                width="75"/>
            </el-table-column>
            <el-table-column
              label="第二周">
              <el-table-column
                property="secondWeek"
                label="周计划"
                width="135">
                <template v-slot="{row}">
                  <div class="text-left">
                    <template v-if="row.secondWeekPlan">
                      <text-display :text="'周计划：' + row.secondWeekPlan"/>
                    </template>
                    <template v-if="row.secondWeek === '未完成'">
                      <p class="table-line"><text-display :text="'未完成原因：' + row.secondWeekPlanResult"/></p>
                      <p class="table-line"><text-display :text="'责任部门：' + row.secondWeekPlt"/></p>
                    </template>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' },{ text: '', value: '' }]"
                :filter-method="filterSecondWeek"
                filter-placement="bottom-end"
                property="secondWeek"
                label="完成情况"
                width="75"/>
            </el-table-column>
            <el-table-column
              label="第三周">
              <el-table-column
                property="thirdWeekPlan"
                label="周计划"
                width="135">
                <template v-slot="{row}">
                  <div class="text-left">
                    <template v-if="row.thirdWeekPlan">
                      <text-display :text="'周计划：' + row.thirdWeekPlan"/>
                    </template>
                    <template v-if="row.thirdWeek === '未完成'">
                      <p class="table-line"><text-display :text="'未完成原因：' + row.thirdWeekPlanResult"/></p>
                      <p class="table-line"><text-display :text="'责任部门：' + row.thirdWeekPlt"/></p>
                    </template>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' },{ text: '', value: '' }]"
                :filter-method="filterThirdWeek"
                filter-placement="bottom-end"
                property="thirdWeek"
                label="完成情况"
                width="75"/>
            </el-table-column>
            <el-table-column
              label="第四周">
              <el-table-column
                property="fourthWeekPlan"
                label="周计划"
                width="135">
                <template v-slot="{row}">
                  <div class="text-left">
                    <div class="text-left">
                      <template v-if="row.fourthWeekPlan">
                        <text-display :text="'周计划：' + row.fourthWeekPlan"/>
                      </template>
                      <template v-if="row.fourthWeek === '未完成'">
                        <p class="table-line"><text-display :text="'未完成原因：' + row.fourthWeekPlanResult"/></p>
                        <p class="table-line"><text-display :text="'责任部门：' + row.fourthWeekPlt"/></p>
                      </template>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' },{ text: '', value: '' }]"
                :filter-method="filterFourthWeek"
                filter-placement="bottom-end"
                property="fourthWeek"
                label="完成情况"
                width="75"/>
            </el-table-column>
            <el-table-column
              :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' },{ text: '', value: '' }]"
              :filter-method="filterMonth"
              :label="'月度\n完成情况'"
              filter-placement="bottom-end"
              property="completionMonth"
              width="70">
              <template v-slot="{row}">
                {{ row.completionMonth }}
                <div class="text-left">
                  <template v-if="row.completionMonth === '未完成'">
                    <p class="table-line"><text-display :text="'未完成原因：' + row.incompleteReason || ''"/></p>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :label="'未完成\n原因'"
              property="reason"
              width="80"/>
          </el-table>
        </div>
      </screen-border>
    </div>
    <!--试制计划1-->
    <el-dialog
      :visible.sync="pilotPlan1.dialogVisible"
      :width="'95%'"
      :top="'50px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="产品试制/试验计划填报">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-command="'/screen/technologyMeeting/pilotManage'"
              class="screen-btn"
              @click="setLockFlag(lockFlag !== '1' ? '1' : '0' )">
              {{ lockFlag !== '1' ? '锁定' : '解锁' }}
            </span>
            <span 
              v-if="lockFlag === '1'" 
              class="red">当月已锁定</span>
            <span
              class="screen-btn"
              @click="clearFilter('editTable1')">
              清除过滤
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM'"
              type="month"
              style="width: 120px"
              @change="changeDate" />
            <template>
              <el-upload
                v-command="'/screen/technologyMeeting/pilotManage'"
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview1"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  v-if="canEditMonth"
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
            </template>
            <span
              class="screen-btn"
              @click="exportpilotPlan(1, 'pilotPlan1')">
              导出
            </span>
          </div>
          {{ cDateZH }}新增试制计划
        </div>
      </template>
      <el-form :disabled="!canEditMonth">
        <el-table
          v-loading="loading"
          ref="editTable1"
          :data="pilotPlan1.gridData"
          :max-height="tableMaxHeight"
          :row-class-name="totalClass"
          class="center-table"
          border>
          <el-table-column
            property="serialNumber"
            fixed="left"
            label="编号"
            width="60"/>
          <el-table-column
            property="she"
            fixed="left"
            label="申报部门"
            width="100">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.department">
                <el-option
                  v-for="(item, index) in departmentList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.department }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="num"
            fixed="left"
            label="钢种"
            width="70">
            <template v-slot="{ row, $index }">
              <el-input 
                v-if="$index === editIndex" 
                v-model="row.stlgrd"/>
              <template v-else>{{ row.stlgrd }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="试制目的"
            min-width="100">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.tpPurpose"
                :rows="4"
                type="textarea"/>
              <template v-else>
                <text-display :text="row.tpPurpose"/>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'品种\n成熟度'"
            property="varietyMaturity"
            width="70">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.varietyMaturity">
                <el-option
                  v-for="(item, index) in varietyList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.varietyMaturity }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'坯料\n钢种'"
            property="time"
            width="75">
            <template v-slot="{ row, $index }">
              <el-input 
                v-if="$index === editIndex" 
                v-model="row.billetStlgrd"/>
              <template v-else>{{ row.billetStlgrd }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'坯料尺寸\n(厚*宽)\nmm'"
            property="proofResult"
            width="80">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.blankSize"/>
              <template v-else>{{ row.blankSize }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'试制量\n(t)'"
            property="content"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input 
                v-if="$index === editIndex" 
                v-model="row.tpQuantity"/>
              <template v-else>{{ row.tpQuantity }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'炼钢量\n(t)'"
            property="discrepancy"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input 
                v-if="$index === editIndex" 
                v-model="row.steelmakingCapacity"/>
              <template v-else>{{ row.steelmakingCapacity }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'轧制量\n(t)'"
            property="proofResult"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input 
                v-if="$index === editIndex" 
                v-model="row.rollingQuantity"/>
              <template v-else>{{ row.rollingQuantity }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'热处理\n量(t)'"
            property="proofResult"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                :disabled="row.htDisabled"
                v-model="row.htCapacity"/>
              <template v-else>{{ row.htCapacity }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="热处理工序"
            width="60">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.htProcess"
                @change="htProcessChange($event, pilotPlan1.gridData)">
                <el-option
                  v-for="(item, index) in heatList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.htProcess }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'轧制\n产线'"
            property="proofResult"
            width="50">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.plt">
                <el-option
                  v-for="(item, index) in pList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.plt }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'成品规格\n(mm*mm)'"
            property="proofResult"
            width="80">
            <template v-slot="{ row, $index }">
              <el-input 
                v-if="$index === editIndex" 
                v-model="row.fgSpec"/>
              <text-display 
                v-else 
                :text="row.fgSpec"/>
            </template>
          </el-table-column>
          <el-table-column
            :label="'交货\n状态'"
            property="proofResult"
            width="60">
            <template v-slot="{ row, $index }">

              <el-select
                v-if="$index === editIndex"
                v-model="row.deliveryStatus">
                <el-option
                  v-for="(item, index) in handleList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.deliveryStatus }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="试制品处置"
            min-width="100">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.tpDisposal">
                <el-option
                  v-for="(item, index) in simpleHandleList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>
                <text-display :text="row.tpDisposal"/>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="试验时间节点"
            min-width="100">
            <template 
              v-slot="{ row, $index}" >
              <el-input
                v-if="$index === editIndex"
                v-model="row.testTime"
                :rows="4"
                type="textarea"/>
              <text-display 
                v-else 
                :text="row.testTime"/>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="负责人"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input 
                v-if="$index === editIndex" 
                v-model="row.head"
                :disabled="true"/>
              <template v-else>{{ row.head }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="测试订单号"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.testOrderNo"/>
              <template v-else>{{ row.testOrderNo }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="testOrderTime"
            label="测试订单下发时间"
            width="115">
            <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.testOrderTime"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>{{ row.testOrderTime }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="testPlanTime"
            label="试验方案下发时间"
            width="115">
            <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.testPlanTime"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>{{ row.testPlanTime }}</template>
            </template>
          </el-table-column>

          <el-table-column
            label="第一周">
            <el-table-column
              property="firstWeekPlan"
              label="周计划"
              width="120">
              <template v-slot="{ row, $index }">
                <template
                  v-if="$index === editIndex">
                  <el-input
                    v-model="row.firstWeekPlan"
                    :rows="2"
                    class="screen-inp"
                    placeholder="输入周计划"
                    type="textarea"/>
                  <template v-if="row.firstWeek === '未完成'">
                    <el-input
                      v-model="row.firstWeekPlanResult"
                      :rows="2"
                      class="screen-inp"
                      placeholder="输入未完成原因"
                      type="textarea"/>
                    <el-select
                      v-model="row.firstWeekPlt"
                      placeholder="选择责任部门"
                      clearable>
                      <el-option
                        v-for="(item, index) in dutyList"
                        :key="index"
                        :value="item">
                        {{ item }}
                      </el-option>
                    </el-select>
                  </template>
                </template>
                <div 
                  v-else 
                  class="text-left">
                  <template v-if="row.firstWeekPlan">
                    <text-display :text="'周计划：' + row.firstWeekPlan"/>
                  </template>
                  <template v-if="row.firstWeek === '未完成'">
                    <p class="table-line"><text-display :text="'未完成原因：' + row.firstWeekPlanResult"/></p>
                    <p class="table-line"><text-display :text="'责任部门：' + row.firstWeekPlt"/></p>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' }]"
              :filter-method="filterFirstWeek"
              filter-placement="bottom-end"
              property="firstWeek"
              label="完成情况"
              width="80">
              <template v-slot="{ row, $index }">
                <el-select
                  v-if="$index === editIndex"
                  v-model="row.firstWeek"
                  clearable>
                  <el-option
                    v-for="(item, index) in statusList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
                <template v-else>{{ row.firstWeek }}</template>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="第二周">
            <el-table-column
              property="secondWeek"
              label="周计划"
              width="120">
              <template v-slot="{ row, $index }">
                <template
                  v-if="$index === editIndex">
                  <el-input
                    v-model="row.secondWeekPlan"
                    :rows="2"
                    class="screen-inp"
                    placeholder="输入周计划"
                    type="textarea"/>
                  <template v-if="row.secondWeek === '未完成'">
                    <el-input
                      v-model="row.secondWeekPlanResult"
                      :rows="2"
                      class="screen-inp"
                      placeholder="输入未完成原因"
                      type="textarea"/>
                    <el-select
                      v-model="row.secondWeekPlt"
                      placeholder="选择责任部门"
                      clearable>
                      <el-option
                        v-for="(item, index) in dutyList"
                        :key="index"
                        :value="item">
                        {{ item }}
                      </el-option>
                    </el-select>
                  </template>
                </template>
                <div
                  v-else
                  class="text-left">
                  <template v-if="row.secondWeekPlan">
                    <text-display :text="'周计划：' + row.secondWeekPlan"/>
                  </template>
                  <template v-if="row.secondWeek === '未完成'">
                    <p class="table-line"><text-display :text="'未完成原因：' + row.secondWeekPlanResult"/></p>
                    <p class="table-line"><text-display :text="'责任部门：' + row.secondWeekPlt"/></p>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' }]"
              :filter-method="filterSecondWeek"
              filter-placement="bottom-end"
              property="thirdWeek"
              label="完成情况"
              width="80">
              <template v-slot="{ row, $index }">
                <el-select
                  v-if="$index === editIndex"
                  v-model="row.secondWeek"
                  clearable>
                  <el-option
                    v-for="(item, index) in statusList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
                <template v-else>{{ row.secondWeek }}</template>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="第三周">
            <el-table-column
              property="thirdWeekPlan"
              label="周计划"
              width="120">
              <template v-slot="{ row, $index }">
                <template
                  v-if="$index === editIndex">
                  <el-input
                    v-model="row.thirdWeekPlan"
                    :rows="2"
                    class="screen-inp"
                    placeholder="输入周计划"
                    type="textarea"/>
                  <template v-if="row.thirdWeek === '未完成'">
                    <el-input
                      v-model="row.thirdWeekPlanResult"
                      :rows="2"
                      class="screen-inp"
                      placeholder="输入未完成原因"
                      type="textarea"/>
                    <el-select
                      v-model="row.thirdWeekPlt"
                      placeholder="选择责任部门"
                      clearable>
                      <el-option
                        v-for="(item, index) in dutyList"
                        :key="index"
                        :value="item">
                        {{ item }}
                      </el-option>
                    </el-select>
                  </template>
                </template>
                <div
                  v-else
                  class="text-left">
                  <template v-if="row.thirdWeekPlan">
                    <text-display :text="'周计划：' + row.thirdWeekPlan"/>
                  </template>
                  <template v-if="row.thirdWeek === '未完成'">
                    <p class="table-line"><text-display :text="'未完成原因：' + row.thirdWeekPlanResult"/></p>
                    <p class="table-line"><text-display :text="'责任部门：' + row.thirdWeekPlt"/></p>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' }]"
              :filter-method="filterThirdWeek"
              filter-placement="bottom-end"
              property="thirdWeek"
              label="完成情况"
              width="80">
              <template v-slot="{ row, $index }">
                <el-select
                  v-if="$index === editIndex"
                  v-model="row.thirdWeek"
                  clearable>
                  <el-option
                    v-for="(item, index) in statusList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
                <template v-else>{{ row.thirdWeek }}</template>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="第四周">
            <el-table-column
              property="fourthWeekPlan"
              label="周计划"
              width="120">
              <template v-slot="{ row, $index }">
                <template
                  v-if="$index === editIndex">
                  <el-input
                    v-model="row.fourthWeekPlan"
                    :rows="2"
                    class="screen-inp"
                    placeholder="输入周计划"
                    type="textarea"/>

                  <template v-if="row.fourthWeek === '未完成'">
                    <el-input
                      v-model="row.fourthWeekPlanResult"
                      :rows="2"
                      class="screen-inp"
                      placeholder="输入未完成原因"
                      type="textarea"/>
                    <el-select
                      v-model="row.fourthWeekPlt"
                      placeholder="选择责任部门"
                      clearable>
                      <el-option
                        v-for="(item, index) in dutyList"
                        :key="index"
                        :value="item">
                        {{ item }}
                      </el-option>
                    </el-select>
                  </template>
                </template>
                <div
                  v-else
                  class="text-left">
                  <template v-if="row.fourthWeekPlan">
                    <text-display :text="'周计划：' + row.fourthWeekPlan"/>
                  </template>
                  <template v-if="row.fourthWeek === '未完成'">
                    <p class="table-line"><text-display :text="'未完成原因：' + row.fourthWeekPlanResult"/></p>
                    <p class="table-line"><text-display :text="'责任部门：' + row.fourthWeekPlt"/></p>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' }]"
              :filter-method="filterFourthWeek"
              filter-placement="bottom-end"
              property="fourthWeek"
              label="完成情况"
              width="80">
              <template v-slot="{ row, $index }">
                <el-select
                  v-if="$index === editIndex"
                  v-model="row.fourthWeek"
                  clearable>
                  <el-option
                    v-for="(item, index) in statusList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
                <template v-else>{{ row.fourthWeek }}</template>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' }]"
            :filter-method="filterMonth"
            :label="'月度\n完成情况'"
            filter-placement="bottom-end"
            property="completionMonth"
            width="75">
            <template v-slot="{ row, $index }">
              <template
                v-if="$index === editIndex">
                <el-select
                  v-model="row.completionMonth"
                  class="screen-inp"
                  clearable>
                  <el-option
                    v-for="(item, index) in statusList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
                <template v-if="row.completionMonth === '未完成'">
                  <el-input
                    v-model="row.incompleteReason"
                    :rows="2"
                    class="screen-inp"
                    placeholder="输入未完成原因"
                    type="textarea"/>
                </template>
              </template>
              <template v-else>
                {{ row.completionMonth }}
                <template v-if="row.completionMonth === '未完成'">
                  <p class="table-line"><text-display :text="'未完成原因：' + row.incompleteReason || ''"/></p>
                </template>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'未完成\n原因'"
            property="proofResult"
            width="80">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.reason"
                clearable>
                <el-option
                  v-for="(item, index) in reasonList"
                  :key="index"
                  :value="item"
                  clearable>
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.reason }}</template>
            </template>
          </el-table-column>

          <el-table-column
            :label="'操作'"
            width="100px"
            fixed="right"
            property="proofResult">
            <template v-slot="{ row, $index}">
              <el-button
                v-if="$index === editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="savepilotPlanItem(row, 1)">保存</el-button>
              <el-button
                v-command="'/screen/technologyMeeting/pilotManage'"
                v-if="row.state === 1"
                class="screen-btn edit-btn"
                type="text"
                @click="processItem(row, 1)">审核确认</el-button>
              <template v-if="(row.head.substr(-6, 100) == userNo || row.head.replace(/[0-9]+/g,'') == userName)">
                <el-button
                  v-if="$index !== editIndex"
                  class="screen-btn edit-btn"
                  type="text"
                  @click="editItem(pilotPlan2.gridData, $index, 1)">编辑</el-button>
                <el-button
                  v-if="$index !== editIndex && row.state !== 1"
                  class="screen-btn edit-btn"
                  type="text"
                  @click="transferItem(row, $index)">接转计划</el-button>
                <el-button
                  v-if="row.id"
                  class="screen-btn edit-btn"
                  type="text"
                  @click="deleteItem(row, $index, 1)">删除</el-button>
              </template>
              <div
                v-command="'/screen/technologyMeeting/pivot'"
                v-else>
                <el-button
                  v-if="$index !== editIndex"
                  class="screen-btn edit-btn"
                  type="text"
                  @click="editItem(pilotPlan2.gridData, $index, 1)">编辑</el-button>
                <el-button
                  v-if="$index !== editIndex && row.state !== 1"
                  class="screen-btn edit-btn"
                  type="text"
                  @click="transferItem(row, $index)">接转计划</el-button>
                <el-button
                  v-if="row.id"
                  class="screen-btn edit-btn"
                  type="text"
                  @click="deleteItem(row, $index, 1)">删除</el-button>
              </div>

            </template>
          </el-table-column>

        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditMonth"
          class="screen-btn"
          style="margin-top: 10px"
          @click="addGridData('pilotPlan1', 1);editIndex = pilotPlan1.gridData.length - 1">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--试制计划2-->
    <el-dialog
      :visible.sync="pilotPlan2.dialogVisible"
      :width="'95%'"
      :top="'50px'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-command="'/screen/technologyMeeting/pilotManage'"
              class="screen-btn"
              @click="setLockFlag(lockFlag !== '1' ? '1' : '0' )">
              {{ lockFlag !== '1' ? '锁定' : '解锁' }}
            </span>
            <span v-if="lockFlag == 1">当月已锁定</span>
            <span
              class="screen-btn"
              @click="clearFilter('editTable2')">
              清除过滤
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM'"
              type="month"
              style="width: 120px"
              @change="changeDate" />
            <template>
              <el-upload
                v-command="'/screen/technologyMeeting/pilotManage'"
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview2"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
            </template>
            <span
              class="screen-btn"
              @click="exportpilotPlan(2, 'pilotPlan2')">
              导出
            </span>
          </div>
          上月转接试制计划
        </div>
      </template>
      <el-form :disabled="!canEditMonth">
        <el-table
          v-loading="loading"
          ref="editTable2"
          :data="pilotPlan2.gridData"
          :max-height="tableMaxHeight"
          :row-class-name="totalClass"
          class="center-table"
          border>
          <el-table-column
            property="index"
            fixed="left"
            label="编号"
            width="60">
            <template v-slot="{ row, $index }">
              <template
                v-if="$index === editIndex">
                <el-input
                  v-model="row.serialNumber"
                  :disabled="!!row.id && !isPilotManage"/>
              </template>
              <template v-else>{{ row.serialNumber }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="she"
            fixed="left"
            label="申报部门"
            width="100">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.department">
                <el-option
                  v-for="(item, index) in departmentList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.department }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="num"
            fixed="left"
            label="钢种"
            width="70">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.stlgrd"/>
              <template v-else>{{ row.stlgrd }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="试制目的"
            min-width="100">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.tpPurpose"
                :rows="4"
                type="textarea"/>
              <template v-else>
                <text-display :text="row.tpPurpose"/>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'品种\n成熟度'"
            property="varietyMaturity"
            width="70">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.varietyMaturity">
                <el-option
                  v-for="(item, index) in varietyList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.varietyMaturity }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'坯料\n钢种'"
            property="time"
            width="75">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.billetStlgrd"/>
              <template v-else>{{ row.billetStlgrd }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'坯料尺寸\n(厚*宽)\nmm'"
            property="proofResult"
            width="80">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.blankSize"/>
              <template v-else>{{ row.blankSize }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'试制量\n(t)'"
            property="content"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.tpQuantity"/>
              <template v-else>{{ row.tpQuantity }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'炼钢量\n(t)'"
            property="discrepancy"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.steelmakingCapacity"/>
              <template v-else>{{ row.steelmakingCapacity }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'轧制量\n(t)'"
            property="proofResult"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.rollingQuantity"/>
              <template v-else>{{ row.rollingQuantity }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'热处理\n量(t)'"
            property="proofResult"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                :disabled="row.htDisabled"
                v-model="row.htCapacity"/>
              <template v-else>{{ row.htCapacity }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="热处理工序"
            width="60">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.htProcess"
                @change="htProcessChange($event, pilotPlan1.gridData)">
                <el-option
                  v-for="(item, index) in heatList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.htProcess }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'轧制\n产线'"
            property="proofResult"
            width="50">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.plt">
                <el-option
                  v-for="(item, index) in pList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.plt }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'成品规格\n(mm*mm)'"
            property="proofResult"
            width="80">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.fgSpec"/>
              <text-display
                v-else
                :text="row.fgSpec"/>
            </template>
          </el-table-column>
          <el-table-column
            :label="'交货\n状态'"
            property="proofResult"
            width="60">
            <template v-slot="{ row, $index }">

              <el-select
                v-if="$index === editIndex"
                v-model="row.deliveryStatus">
                <el-option
                  v-for="(item, index) in handleList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.deliveryStatus }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="试制品处置"
            min-width="100">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.tpDisposal">
                <el-option
                  v-for="(item, index) in simpleHandleList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>
                <text-display :text="row.tpDisposal"/>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="试验时间节点"
            min-width="100">
            <template
              v-slot="{ row, $index}" >
              <el-input
                v-if="$index === editIndex"
                v-model="row.testTime"
                :rows="4"
                type="textarea"/>
              <text-display
                v-else
                :text="row.testTime"/>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="负责人"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.head"
                :disabled="true"/>
              <template v-else>{{ row.head }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="测试订单号"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.testOrderNo"/>
              <template v-else>{{ row.testOrderNo }}</template>
            </template>
          </el-table-column>

          <el-table-column
            property="testOrderTime"
            label="测试订单下发时间"
            width="115">
            <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.testOrderTime"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>{{ row.testOrderTime }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="testPlanTime"
            label="试验方案下发时间"
            width="115">
            <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.testPlanTime"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>{{ row.testPlanTime }}</template>
            </template>
          </el-table-column>
          <el-table-column
            label="第一周">
            <el-table-column
              property="firstWeekPlan"
              label="周计划"
              width="120">
              <template v-slot="{ row, $index }">
                <template
                  v-if="$index === editIndex">
                  <el-input
                    v-model="row.firstWeekPlan"
                    :rows="2"
                    class="screen-inp"
                    placeholder="输入周计划"
                    type="textarea"/>
                  <template v-if="row.firstWeek === '未完成'">
                    <el-input
                      v-model="row.firstWeekPlanResult"
                      :rows="2"
                      class="screen-inp"
                      placeholder="输入未完成原因"
                      type="textarea"/>
                    <el-select
                      v-model="row.firstWeekPlt"
                      placeholder="选择责任部门"
                      clearable>
                      <el-option
                        v-for="(item, index) in dutyList"
                        :key="index"
                        :value="item">
                        {{ item }}
                      </el-option>
                    </el-select>
                  </template>
                </template>
                <div
                  v-else
                  class="text-left">
                  <template v-if="row.firstWeekPlan">
                    <text-display :text="'周计划：' + row.firstWeekPlan"/>
                  </template>
                  <template v-if="row.firstWeek === '未完成'">
                    <p class="table-line"><text-display :text="'未完成原因：' + row.firstWeekPlanResult"/></p>
                    <p class="table-line"><text-display :text="'责任部门：' + row.firstWeekPlt"/></p>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' }]"
              :filter-method="filterFirstWeek"
              filter-placement="bottom-end"
              property="firstWeek"
              label="完成情况"
              width="80">
              <template v-slot="{ row, $index }">
                <el-select
                  v-if="$index === editIndex"
                  v-model="row.firstWeek"
                  clearable>
                  <el-option
                    v-for="(item, index) in statusList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
                <template v-else>{{ row.firstWeek }}</template>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="第二周">
            <el-table-column
              property="secondWeek"
              label="周计划"
              width="120">
              <template v-slot="{ row, $index }">
                <template
                  v-if="$index === editIndex">
                  <el-input
                    v-model="row.secondWeekPlan"
                    :rows="2"
                    class="screen-inp"
                    placeholder="输入周计划"
                    type="textarea"/>
                  <template v-if="row.secondWeek === '未完成'">
                    <el-input
                      v-model="row.secondWeekPlanResult"
                      :rows="2"
                      class="screen-inp"
                      placeholder="输入未完成原因"
                      type="textarea"/>
                    <el-select
                      v-model="row.secondWeekPlt"
                      placeholder="选择责任部门"
                      clearable>
                      <el-option
                        v-for="(item, index) in dutyList"
                        :key="index"
                        :value="item">
                        {{ item }}
                      </el-option>
                    </el-select>
                  </template>
                </template>
                <div
                  v-else
                  class="text-left">
                  <template v-if="row.secondWeekPlan">
                    <text-display :text="'周计划：' + row.secondWeekPlan"/>
                  </template>
                  <template v-if="row.secondWeek === '未完成'">
                    <p class="table-line"><text-display :text="'未完成原因：' + row.secondWeekPlanResult"/></p>
                    <p class="table-line"><text-display :text="'责任部门：' + row.secondWeekPlt"/></p>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' }]"
              :filter-method="filterSecondWeek"
              filter-placement="bottom-end"
              property="thirdWeek"
              label="完成情况"
              width="80">
              <template v-slot="{ row, $index }">
                <el-select
                  v-if="$index === editIndex"
                  v-model="row.secondWeek"
                  clearable>
                  <el-option
                    v-for="(item, index) in statusList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
                <template v-else>{{ row.secondWeek }}</template>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="第三周">
            <el-table-column
              property="thirdWeekPlan"
              label="周计划"
              width="120">
              <template v-slot="{ row, $index }">
                <template
                  v-if="$index === editIndex">
                  <el-input
                    v-model="row.thirdWeekPlan"
                    :rows="2"
                    class="screen-inp"
                    placeholder="输入周计划"
                    type="textarea"/>
                  <template v-if="row.thirdWeek === '未完成'">
                    <el-input
                      v-model="row.thirdWeekPlanResult"
                      :rows="2"
                      class="screen-inp"
                      placeholder="输入未完成原因"
                      type="textarea"/>
                    <el-select
                      v-model="row.thirdWeekPlt"
                      placeholder="选择责任部门"
                      clearable>
                      <el-option
                        v-for="(item, index) in dutyList"
                        :key="index"
                        :value="item">
                        {{ item }}
                      </el-option>
                    </el-select>
                  </template>
                </template>
                <div
                  v-else
                  class="text-left">
                  <template v-if="row.thirdWeekPlan">
                    <text-display :text="'周计划：' + row.thirdWeekPlan"/>
                  </template>
                  <template v-if="row.thirdWeek === '未完成'">
                    <p class="table-line"><text-display :text="'未完成原因：' + row.thirdWeekPlanResult"/></p>
                    <p class="table-line"><text-display :text="'责任部门：' + row.thirdWeekPlt"/></p>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' }]"
              :filter-method="filterThirdWeek"
              filter-placement="bottom-end"
              property="thirdWeek"
              label="完成情况"
              width="80">
              <template v-slot="{ row, $index }">
                <el-select
                  v-if="$index === editIndex"
                  v-model="row.thirdWeek"
                  clearable>
                  <el-option
                    v-for="(item, index) in statusList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
                <template v-else>{{ row.thirdWeek }}</template>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="第四周">
            <el-table-column
              property="fourthWeekPlan"
              label="周计划"
              width="120">
              <template v-slot="{ row, $index }">
                <template
                  v-if="$index === editIndex">
                  <el-input
                    v-model="row.fourthWeekPlan"
                    :rows="2"
                    class="screen-inp"
                    placeholder="输入周计划"
                    type="textarea"/>

                  <template v-if="row.fourthWeek === '未完成'">
                    <el-input
                      v-model="row.fourthWeekPlanResult"
                      :rows="2"
                      class="screen-inp"
                      placeholder="输入未完成原因"
                      type="textarea"/>
                    <el-select
                      v-model="row.fourthWeekPlt"
                      placeholder="选择责任部门"
                      clearable>
                      <el-option
                        v-for="(item, index) in dutyList"
                        :key="index"
                        :value="item">
                        {{ item }}
                      </el-option>
                    </el-select>
                  </template>
                </template>
                <div
                  v-else
                  class="text-left">
                  <template v-if="row.fourthWeekPlan">
                    <text-display :text="'周计划：' + row.fourthWeekPlan"/>
                  </template>
                  <template v-if="row.fourthWeek === '未完成'">
                    <p class="table-line"><text-display :text="'未完成原因：' + row.fourthWeekPlanResult"/></p>
                    <p class="table-line"><text-display :text="'责任部门：' + row.fourthWeekPlt"/></p>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' }]"
              :filter-method="filterFourthWeek"
              filter-placement="bottom-end"
              property="fourthWeek"
              label="完成情况"
              width="80">
              <template v-slot="{ row, $index }">
                <el-select
                  v-if="$index === editIndex"
                  v-model="row.fourthWeek"
                  clearable>
                  <el-option
                    v-for="(item, index) in statusList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
                <template v-else>{{ row.fourthWeek }}</template>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            :filters="[{ text: '已完成', value: '已完成' }, { text: '未完成', value: '未完成' }]"
            :filter-method="filterMonth"
            :label="'月度\n完成情况'"
            filter-placement="bottom-end"
            property="completionMonth"
            width="75">
            <template v-slot="{ row, $index }">
              <template
                v-if="$index === editIndex">
                <el-select
                  v-model="row.completionMonth"
                  class="screen-inp"
                  clearable>
                  <el-option
                    v-for="(item, index) in statusList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
                <template v-if="row.completionMonth === '未完成'">
                  <el-input
                    v-model="row.incompleteReason"
                    :rows="2"
                    class="screen-inp"
                    placeholder="输入未完成原因"
                    type="textarea"/>
                </template>
              </template>
              <template v-else>
                {{ row.completionMonth }}
                <template v-if="row.completionMonth === '未完成'">
                  <p class="table-line"><text-display :text="'未完成原因：' + row.incompleteReason || ''"/></p>
                </template>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'未完成\n原因'"
            property="proofResult"
            width="80">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.reason"
                clearable>
                <el-option
                  v-for="(item, index) in reasonList"
                  :key="index"
                  :value="item"
                  clearable>
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.reason }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'操作'"
            width="100px"
            fixed="right"
            property="proofResult">
            <template v-slot="{ row, $index}">
              <el-button
                v-if="$index === editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="savepilotPlanItem(row, 2)">保存</el-button>
              <el-button
                v-command="'/screen/technologyMeeting/pilotManage'"
                v-if="row.state === 1"
                class="screen-btn edit-btn"
                type="text"
                @click="processItem(row, 2)">审核确认</el-button>
              <template v-if="(row.head.substr(-6, 100) == userNo || row.head.replace(/[0-9]+/g,'') == userName)">
                <el-button
                  v-if="$index !== editIndex"
                  class="screen-btn edit-btn"
                  type="text"
                  @click="editItem(pilotPlan2.gridData, $index, 2)">编辑</el-button>
                <el-button
                  v-if="$index !== editIndex && row.state !== 1"
                  class="screen-btn edit-btn"
                  type="text"
                  @click="transferItem(row, $index)">接转计划</el-button>
                <el-button
                  class="screen-btn edit-btn"
                  type="text"
                  @click="deleteItem(row, $index, 2)">删除</el-button>
              </template>
              <div 
                v-command="'/screen/technologyMeeting/pivot'" 
                v-else>
                <el-button
                  v-if="$index !== editIndex"
                  class="screen-btn edit-btn"
                  type="text"
                  @click="editItem(pilotPlan2.gridData, $index, 2)">编辑</el-button>
                <el-button
                  v-if="$index !== editIndex && row.state !== 1"
                  class="screen-btn edit-btn"
                  type="text"
                  @click="transferItem(row, $index)">接转计划</el-button>
                <el-button
                  class="screen-btn edit-btn"
                  type="text"
                  @click="deleteItem(row, $index, 2)">删除</el-button>
              </div>

            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditMonth"
          class="screen-btn"
          style="margin-top: 10px"
          @click="addGridData('pilotPlan2', 2);editIndex = pilotPlan2.gridData.length - 1">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM'"
            type="month"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  progressReportingDeleteAllById,
  progressReportingFindAllBySetDate,
  progressReportingSave
} from '@/api/screenTechnolagy'
import moment from 'moment'
import TextDisplay from '@/pages/screen/technologyMeeting/component/text-display'
import { findOneUserByUserNo } from '@/api/system'
import {
  findBoardParameterByDateAndPara,
  saveBoardParameter
} from '@/api/screen'
import { mapState } from 'vuex'

export default {
  name: 'pilotPlan',
  components: { TextDisplay, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      editIndex: null,
      tableMaxHeight: null,
      pilotPlan1: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      pilotPlan2: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      departmentList: [
        '技术研发处-能源用钢研发室',
        '技术研发处-结构船板研发室',
        '技术研发处-调质钢研发室',
        '技术研发处-低温容器钢研发室',
        '技术研发处-工艺研究室',
        '研究院-先进结构材料研究所',
        '研究院-腐蚀技术研究所',
        '研究院-用户技术研究所',
        // '研究院-特种材料研究所',
        '研究院-前沿材料研究所',
        //   '研究院-新材料研究所',
        //   '研究院-结构材料研究所',
        '研究院-特种材料研究所',
        '研究院-工艺技术研究所',
        '研究院-特用钢部'
      ],
      varietyList: ['新产品研发', '带订单试制'],
      statusList: ['已完成', '未完成'],
      heatList: ['/', 'Q', 'N', 'T', 'QT', 'NT', 'QQT'],
      pList: ['C1', 'C2', 'C3'],
      handleList: [
        'N',
        'T',
        'NT',
        'QT',
        'QQT',
        'NACT',
        'AR',
        'NR',
        'CR',
        'TMCP',
        'TMCP+T'
      ],
      reasonList: [
        '技术原因',
        '订单交付原因',
        '设备原因',
        '生产原因',
        '检试验原因',
        '外委原因',
        '现场执行原因'
      ],
      dutyList: [
        '生产处',
        '技术研发处',
        '设备处',
        '营销处',
        '检试验',
        '国贸',
        '新材料研究所',
        '第一炼钢厂',
        '中厚板卷厂',
        '中板厂',
        '宽厚板厂',
        '特用钢部'
      ],
      simpleHandleList: [
        '替代正式订单',
        '改判',
        '保留',
        '余材处置',
        '余坯处置',
        '判废'
      ],
      userName: '',
      userNo: '',
      lockFlag: 0
    }
  },
  computed: {
    ...mapState('menu', ['pageButtonPower']),
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'month')
        .format('yyyy-MM')
    },
    cDateZH: function() {
      // 初始化数据
      return moment(this.cDate).format('yyyy年MM月')
    },
    showDataList: function() {
      return this.pilotPlan1.showGridData.concat(this.pilotPlan2.showGridData)
    },
    isPivot: function() {
      return this.pageButtonPower.includes('/screen/technologyMeeting/pivot')
    },
    isPilotManage: function() {
      return this.pageButtonPower.includes(
        '/screen/technologyMeeting/pilotManage'
      )
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = moment(this.selectDate).format('YYYY-MM')
    },
    cDate: function() {
      // 初始化数据
      this.loadData()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = moment(this.selectDate).format('YYYY-MM')
  },
  mounted() {
    this.loadData()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    // 通知改变tab
    changeTab(tab) {
      this.$emit('jumpTab', tab)
    },
    loadData() {
      this.getLockFlag()
      this.findOneUserByUserNo()
      this.calculate()
      this.getpilotPlan(1)
      this.getpilotPlan(2)
    },
    // 获取当月是否锁定
    getLockFlag() {
      post(findBoardParameterByDateAndPara, {
        setDate: this.cDate
      }).then(res => {
        this.lockFlag = this.getParam('pilotPlanLockFlag', res.data)
      })
    },
    setLockFlag(flag) {
      const params = {
        data: [
          {
            parameter: 'pilotPlanLockFlag',
            content: flag,
            setDate: this.cDate
          }
        ]
      }
      post(saveBoardParameter, params).then(res => {
        this.loading = false
        if (res.status === 1) {
          this.lockFlag = flag
          this.$message.success(this.lockFlag != '1' ? '解锁成功' : '锁定成功')
        }
      })
    },
    findOneUserByUserNo() {
      this.userNo = localStorage.getItem('userId')
      post(findOneUserByUserNo, {
        userNo: this.userNo
      }).then(res => {
        if (res.success) {
          this.userName = res.data.userName
        }
      })
    },
    handlePreview1(file) {
      this.handleExcel(file, 1)
    },
    handlePreview2(file) {
      this.handleExcel(file, 2)
    },
    handleExcel(file, type) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          serialNumber: 'A',
          department: 'B',
          stlgrd: 'C',
          tpPurpose: 'D',
          varietyMaturity: 'E',
          billetStlgrd: 'F',
          blankSize: 'G',
          tpQuantity: 'H',
          steelmakingCapacity: 'I',
          rollingQuantity: 'J',
          htCapacity: 'K',
          htProcess: 'L',
          plt: 'M',
          fgSpec: 'N',
          deliveryStatus: 'O',
          tpDisposal: 'P',
          testTime: 'Q',
          testOrderTime: 'R',
          testPlanTime: 'S',
          head: 'T',
          testOrderNo: 'U',
          firstWeekPlan: 'V',
          firstWeek: 'W',
          secondWeekPlan: 'X',
          secondWeek: 'Y',
          thirdWeekPlan: 'Z',
          thirdWeek: 'AA',
          fourthWeekPlan: 'AB',
          fourthWeek: 'AC',
          reason: 'AD'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        const datas = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
        this.savepilotPlan(datas, type)
      })
    },
    exportpilotPlan(type, dataName) {
      const obj = {
        serialNumber: '编号',
        department: '申报部门',
        stlgrd: '钢种',
        tpPurpose: '试制目的',
        varietyMaturity: '品种成熟度',
        billetStlgrd: '坯料钢种',
        blankSize: '"坯料尺寸(厚*宽)mm"',
        tpQuantity: '试制量（t）',
        steelmakingCapacity: '炼钢量（t）',
        rollingQuantity: '轧制量（t）',
        htCapacity: '热处理量（t）',
        htProcess: '热处理工序',
        plt: '轧制产线',
        fgSpec: '成品规格(mm*mm)',
        deliveryStatus: '交货状态',
        tpDisposal: '试制品处置',
        testTime: '试验时间节点',
        head: '负责人',
        testOrderNo: '测试订单号',
        testOrderTime: '测试订单下发时间',
        testPlanTime: '试验方案下发时间',
        firstWeekPlan: '第1周计划',
        firstWeek: '第1周完成情况',
        secondWeekPlan: '第2周计划',
        secondWeek: '第2周完成情况',
        thirdWeekPlan: '第3周计划',
        thirdWeek: '第3周完成情况',
        fourthWeekPlan: '第4周计划',
        fourthWeek: '第4周完成情况',
        completionMonth: '月度完成情况',
        reason: '未完成原因'
      }
      const data = [obj].concat(
        _.cloneDeep(this[dataName].gridData).map(item => {
          const temp = {}
          Object.keys(obj).forEach(name => {
            temp[name] = item[name]
          })
          return temp
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `产品、工艺试制计划（${
          type === 1 ? this.cDate + '新增' : this.cDate + '转接'
        }）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 获取数据
    filterFirstWeek(value, row) {
      return row['firstWeek'] === value
    },
    filterSecondWeek(value, row) {
      return row['secondWeek'] === value
    },
    filterThirdWeek(value, row) {
      return row['thirdWeek'] === value
    },
    filterFourthWeek(value, row) {
      return row['fourthWeek'] === value
    },
    filterMonth(value, row) {
      return row['completionMonth'] === value
    },
    getpilotPlan(type) {
      post(progressReportingFindAllBySetDate, {
        setDate: this.cDate,
        type: type
      }).then(res => {
        //
        this.loading = false
        this.$emit('statusChange', !res.data.length)
        if (!res.data.length) {
          this['pilotPlan' + type].showGridData = []
          this['pilotPlan' + type].gridData = []
          return
        }

        let data = _.sortBy(res.data, 'department').map((item, index) => {
          if (type === 1) {
            item.serialnumber =
              this.cDate.substring(2, 7).replace('-0', '-') + '-' + (index + 1)
          }
          return {
            serialNumber: item.serialnumber,
            department: item.department,
            stlgrd: item.stlgrd,
            billetStlgrd: item.billetstlgrd,
            tpQuantity: item.tpquantity,
            varietyMaturity: item.varietymaturity,
            steelmakingCapacity: item.steelmakingcapacity,
            blankSize: item.blanksize,
            rollingQuantity: item.rollingquantity,
            htProcess: item.htprocess,
            htCapacity: item.htcapacity,
            fgSpec: item.fgspec,
            deliveryStatus: item.deliverystatus,
            plt: item.plt,
            tpPurpose: item.tppurpose,
            tpDisposal: item.tpdisposal,
            testTime: item.testtime,
            testOrderTime: item.testordertime,
            testPlanTime: item.testplantime,
            head: item.head,
            testOrderNo: item.testorderno,
            firstWeekPlan: item.firstweekplan
              ? item.firstweekplan.split('|')[0] || ''
              : '',
            firstWeek: item.firstweek,
            secondWeekPlan: item.secondweekplan
              ? item.secondweekplan.split('|')[0] || ''
              : '',
            secondWeek: item.secondweek,
            thirdWeekPlan: item.thirdweekplan
              ? item.thirdweekplan.split('|')[0] || ''
              : '',
            thirdWeek: item.thirdweek,
            fourthWeekPlan: item.fourthweekplan
              ? item.fourthweekplan.split('|')[0] || ''
              : '',
            fourthWeek: item.fourthweek,
            completionMonth: item.completionmonth,
            reason: item.reason,
            incompleteReason: item.incompletereason || '',
            firstWeekPlanResult: item.firstweekplanresult || '',
            secondWeekPlanResult: item.secondweekplanresult || '',
            thirdWeekPlanResult: item.thirdweekplanresult || '',
            fourthWeekPlanResult: item.fourthweekplanresult || '',
            firstWeekPlt: item.firstweekplt || '',
            secondWeekPlt: item.secondweekplt || '',
            thirdWeekPlt: item.thirdweekplt || '',
            fourthWeekPlt: item.fourthweekplt || '',
            id: item.id,
            type: item.type,
            state: item.state
          }
        })
        this['pilotPlan' + type].gridData = lodash.cloneDeep(data)
        data = data.filter(item => item.state !== 1)
        const group1 = data.filter(
          item => item.department && item.department.startsWith('技术研发处')
        )
        if (group1.length >= 1) {
          data.splice(group1.length, 0, {
            serialNumber: '合计',
            department: '技术研发处周进度',
            firstWeek:
              (
                (group1.filter(item => item.firstWeek === '已完成').length /
                  group1.length) *
                100
              ).toFixed(0) + '%',
            secondWeek:
              (
                (group1.filter(item => item.secondWeek === '已完成').length /
                  group1.length) *
                100
              ).toFixed(0) + '%',
            thirdWeek:
              (
                (group1.filter(item => item.thirdWeek === '已完成').length /
                  group1.length) *
                100
              ).toFixed(0) + '%',
            fourthWeek:
              (
                (group1.filter(item => item.fourthWeek === '已完成').length /
                  group1.length) *
                100
              ).toFixed(0) + '%',
            completionMonth:
              (
                (group1.filter(item => item.completionMonth === '已完成')
                  .length /
                  group1.length) *
                100
              ).toFixed(0) + '%'
          })
        }
        const group2 = data.filter(
          item => item.department && item.department.startsWith('研究院')
        )
        if (group2.length >= 1) {
          data.splice(data.length, 0, {
            serialNumber: '合计',
            department: '研究院周进度',
            firstWeek:
              (
                (group2.filter(item => item.firstWeek === '已完成').length /
                  group2.length) *
                100
              ).toFixed(0) + '%',
            secondWeek:
              (
                (group2.filter(item => item.secondWeek === '已完成').length /
                  group2.length) *
                100
              ).toFixed(0) + '%',
            thirdWeek:
              (
                (group2.filter(item => item.thirdWeek === '已完成').length /
                  group2.length) *
                100
              ).toFixed(0) + '%',
            fourthWeek:
              (
                (group2.filter(item => item.fourthWeek === '已完成').length /
                  group2.length) *
                100
              ).toFixed(0) + '%',
            completionMonth:
              (
                (group2.filter(item => item.completionMonth === '已完成')
                  .length /
                  group2.length) *
                100
              ).toFixed(0) + '%'
          })
        }
        this['pilotPlan' + type].showGridData = data
        this.formatSpanData(this['pilotPlan' + type].showGridData)
      })
    },
    savepilotPlanItem(row, type) {
      let err = []
      let arr = {
        department: '申报部门',
        stlgrd: '钢种',
        tpPurpose: '试制目的',
        varietyMaturity: '品种成熟度',
        billetStlgrd: '坯料钢种',
        blankSize: '"坯料尺寸(厚*宽)mm"',
        tpQuantity: '试制量（t）',
        steelmakingCapacity: '炼钢量（t）',
        rollingQuantity: '轧制量（t）',
        htCapacity: '热处理量（t）',
        htProcess: '热处理工序',
        plt: '轧制产线',
        fgSpec: '成品规格(mm*mm)',
        deliveryStatus: '交货状态',
        tpDisposal: '试制品处置',
        testTime: '试验时间节点',
        testOrderTime: '测试订单下发时间',
        testPlanTime: '试验方案下发时间',
        head: '负责人',
        firstWeekPlan: '第一周计划',
        secondWeekPlan: '第二周计划',
        thirdWeekPlan: '第三周计划',
        fourthWeekPlan: '第四周计划'
      }
      Object.keys(arr).forEach(item => {
        if (row[item] === '' || row[item] === null || row[item] === undefined) {
          err.push(arr[item])
        }
      })
      if (err.length > 0) {
        return this.$message.warning(`请填写${err.join('、')}`)
      }
      if (
        row.firstWeek === '未完成' &&
        (!row.firstWeekPlanResult || !row.firstWeekPlt)
      ) {
        return this.$message.warning(`请填写第一周未完成原因以及责任部门`)
      }
      if (
        row.secondWeek === '未完成' &&
        (!row.secondWeekPlanResult || !row.secondWeekPlt)
      ) {
        return this.$message.warning(`请填写第二周未完成原因以及责任部门`)
      }
      if (
        row.thirdWeek === '未完成' &&
        (!row.thirdWeekPlanResult || !row.thirdWeekPlt)
      ) {
        return this.$message.warning(`请填写第三周未完成原因以及责任部门`)
      }
      if (
        row.fourthWeek === '未完成' &&
        (!row.fourthWeekPlanResult || !row.fourthWeekPlt)
      ) {
        return this.$message.warning(`请填写第四周未完成原因以及责任部门`)
      }
      if (row.completionMonth === '未完成' && !row.reason) {
        return this.$message.warning(`请选择月度未完成原因！`)
      }
      if (type == 2 && !row.serialNumber) {
        return this.$message.warning(`请输填写编号！`)
      }
      this.savepilotPlan([row], type)
    },
    // 保存
    savepilotPlan(items, type) {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: items.map(item => {
          return Object.assign({}, item, {
            type,
            state: !item.id ? this.lockFlag : item.state,
            setDate: this.cDate
          })
        })
      }
      post(progressReportingSave, params)
        .then(res => {
          //
          this.loading = false
          if (res && res !== -1) {
            this.$message.success('保存成功！')
            this.getpilotPlan(type)
            this.editIndex = null
          } else {
            this.$message.warning('保存失败！')
          }
        })
        .catch(err => {
          console.log('err', err)
        })
    },
    editItem(data, index, type) {
      this.editIndex = index
    },
    transferItem(data, index) {
      // 保存
      const nextMonth = this.$moment(this.cDate)
        .add(1, 'month')
        .format('YYYY-MM')
      const params = {
        setDate: nextMonth,
        data: [
          Object.assign({}, data, {
            type: 2,
            setDate: nextMonth,
            firstWeekPlan: '',
            id: '',
            firstWeek: '',
            secondWeekPlan: '',
            secondWeek: '',
            thirdWeekPlan: '',
            thirdWeek: '',
            fourthWeekPlan: '',
            fourthWeek: '',
            completionMonth: '',
            reason: '',
            firstWeekPlanResult: '',
            secondWeekPlanResult: '',
            thirdWeekPlanResult: '',
            fourthWeekPlanResult: '',
            firstWeekPlt: '',
            secondWeekPlt: '',
            thirdWeekPlt: '',
            fourthWeekPlt: ''
          })
        ]
      }
      this.$confirm(
        `是否将试制计划(编号：${data.serialNumber})接转至${nextMonth}月?`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.loading = true
        post(progressReportingSave, params)
          .then(res => {
            //
            this.loading = false
            if (res && res !== -1) {
              this.$message.success('结转成功！')
              // this.getpilotPlan(type)
              // this.editIndex = null
            } else {
              this.$message.warning('结转失败！')
            }
          })
          .catch(err => {
            console.log('err', err)
          })
      })
    },
    deleteItem(data, index, type) {
      this.$confirm(
        `是否确认删除此数据项(编号：${data.serialNumber || ''})?`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        post(progressReportingDeleteAllById, {
          id: data.id
        }).then(res => {
          this.$message.success('删除成功！')
          this.getpilotPlan(type)
        })
      })
    },
    // 审核
    processItem(item, type) {
      this.$confirm(
        `是否确认添加这个试制计划(编号：${item.serialNumber})?`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.savepilotPlan([Object.assign({}, item, { state: 0 })], type)
      })
    },
    addGridData(name, type) {
      this[name].gridData.push({
        head: this.userName
      })
      this.$nextTick(() => {
        this.$refs['editTable' + type].bodyWrapper.scrollTo(0, 10000000)
      })
    },
    htProcessChange($event, data) {
      if ($event === '/') {
        data[this.editIndex].htCapacity = 0
        data[this.editIndex].htDisabled = true
      } else {
        data[this.editIndex].htDisabled = false
      }
    },
    calculate() {
      this.pilotPlan1.maxHeight = this.$refs.table1.offsetHeight
      this.tableMaxHeight = document.body.clientHeight - 240
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
      if (row.serialNumber === '合计') {
        if (columnIndex === 2) {
          return [1, 17]
        } else if ([20, 22, 24, 26].includes(columnIndex)) {
          return [1, 2]
        } else if (
          (columnIndex < 19 && columnIndex > 2) ||
          [19, 21, 23, 25].includes(columnIndex)
        ) {
          return [0, 0]
        } else {
          return [1, 1]
        }
      }
    },
    clearFilter(name) {
      console.log(this.$refs[name])
      this.$refs[name].clearFilter()
    },
    totalClass(row) {
      let str = ''
      if (row.row.type && row.row.type === 2) {
        str += 'color-row'
      }
      if (row.row.serialNumber && row.row.serialNumber.trim() === '合计') {
        str += ' table-total'
      }
      if (row.row.state && row.row.state === 1) {
        str += ' lock-row'
      }
      return str
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.screen-inp {
  margin-bottom: 5px;
}
.edit-btn {
  margin: 0 3px;
  margin-bottom: 5px;
  &:last-child {
    margin-bottom: 0;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
/deep/ .color-row.el-table__row .el-table__cell {
  color: #e6e78d;
}
/deep/ .lock-row.el-table__row .el-table__cell {
  color: #ff0f0f;
}
.table-line {
  padding-top: 3px;
  padding-bottom: 3px;
  border-top: 2px solid #0b2a34;
  &:last-child {
    padding-bottom: 0;
  }
}
</style>
