<template>
  <el-tooltip 
    :popper-class="'screen-text-pop'" 
    placement="top">
    <div 
      slot="content" 
      class="pop-text">{{ text }}</div>
    <div class="dis-text">{{ text }}</div>
  </el-tooltip>
</template>
<script>
export default {
  name: 'text-display',
  props: {
    text: {
      default: '',
      type: String
    }
  }
}
</script>

<style lang="less" scoped>
.dis-text {
  max-height: 43px;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden; //溢出内容隐藏
  text-overflow: ellipsis; //文本溢出部分用省略号表示
  display: -webkit-box; //特别显示模式
  -webkit-line-clamp: 2; //行数
  line-clamp: 2;
  -webkit-box-orient: vertical; //盒子中内容竖直排列
}
.pop-text {
  max-width: 240px;
  font-size: 16px;
  line-height: 1.3;
}
</style>
