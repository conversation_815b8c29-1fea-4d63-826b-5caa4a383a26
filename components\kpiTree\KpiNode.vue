<template>
  <div
    v-if="treeData.name"
    class="tree-wrapper">
    <div
      v-if="!onlyWarning || treeData.warningStatus"
      :class="{
        'warning': treeData.warningStatus,
        'trend': treeData.trendWarningStatus
      }"
      class="tree-root tree-node">
      <Node
        :key="treeData.id"
        :node="treeData"
        :show-chart="showChart"
        :num-name="numName"
        :unit-name="unitName"
        :num-editable="numEditable"
        @handleClick="handleClick"
        @changeStatus="changeStatus"/>
    </div>
    <template v-if="treeData.children && treeData.children.length && !treeData.hiddenChildren">
      <NodeItem
        :node="treeData.children"
        :only-warning="onlyWarning"
        :show-chart="showChart"
        :rank="rank"
        :level="level"
        :num-name="numName"
        :unit-name="unitName"
        :num-editable="numEditable"
        @handleClick="handleClick"/>
    </template>
  </div>
</template>

<script>
import NodeItem from '@/components/kpiTree/NodeItem'
import { getCoreResultValue } from '@/lib/Util'
import Node from '@/components/kpiTree/Node'
export default {
  name: 'KpiNode',
  components: { Node, NodeItem },
  props: {
    node: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {}
    },
    onlyWarning: {
      type: Boolean,
      default: false
    },
    showChart: {
      type: Boolean,
      default: true
    },
    rank: {
      type: Number,
      default: 3
    },
    level: {
      type: Number,
      default: 2
    },
    numName: {
      type: String,
      default: 'coreResultValue'
    },
    unitName: {
      type: String,
      default: 'unit'
    },
    numEditable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      treeData: Object.assign(this.node, {
        hiddenChildren: this.rank < this.level
      })
    }
  },
  watch: {
    node: function() {
      this.treeData = Object.assign({}, this.node, {
        hiddenChildren: this.rank < this.level
      })
    },
    rank: function() {
      this.treeData = Object.assign({}, this.node, {
        hiddenChildren: this.rank < this.level
      })
    }
  },
  methods: {
    handleClick(e) {
      this.$emit('handleClick', e)
    },
    changeStatus() {
      console.log(this.treeData)
      // this.$set(this.treeData, 'hiddenChildren', !this.treeData.hiddenChildren)
      this.treeData.hiddenChildren = !this.treeData.hiddenChildren
    }
  }
}
</script>

<style scoped lang="less">
.tree-wrapper {
  min-height: 120px;
  font-size: 0;
  padding: 20px;
  white-space: nowrap;
}
.tree-root {
  &:before {
    display: none;
  }
  &:after {
    display: none;
  }
}
.tree-node {
  position: relative;
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  padding: 5px 15px;
  border-radius: 4px;
  font-size: 18px;
  background: #eff4fd;
  border-left: 3px solid #5e93ed;
  float: left;
  .node-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .node-describe {
      margin-right: 15px;
      small {
        color: #666;
        font-size: 10px;
      }
    }
    .node-arrow {
      cursor: pointer;
    }
  }
  span.result {
    display: block;
    color: #5e93ed;
    font-weight: 600;
    font-size: 20px;
    line-height: 28px;
    height: 28px;
  }
  &.trend {
    border-color: #ffa958;
    background: #fff6ee;
  }
  &.warning {
    border-color: #f56c6c;
    background: #fef0f0;
  }
}
</style>
