export const state = () => ({
  allMenus: [],
  userMenuList: [],
  pageButtonPower: [],
  menuStore: [], // 所有菜单，包含所有子系统
  showHeader: true,
  menuCollapse: false,
  pageOpenedList: [],
  currentPageName: ''
})
export const mutations = {
  setUserMenuList(state, list) {
    state.userMenuList = list || []
  },
  setAllMenus(state, list) {
    state.allMenus = list || []
  },
  setPageButtonPower(state, list) {
    state.pageButtonPower = list || []
  },
  setMenuStore(state, list) {
    state.menuStore = list || []
  },
  showHeader(state, value) {
    state.showHeader = value
  },
  menuCollapse(state, value) {
    state.menuCollapse = value
  },
  setOpenedList(state, list) {
    state.pageOpenedList = list
  },
  updateOpenedList(state, get) {
    let openedPage = state.pageOpenedList[get.index]
    if (get.params) {
      openedPage.params = get.params
    }
    if (get.query) {
      openedPage.query = get.query
    }
    if (get.fullPath) {
      openedPage.fullPath = get.fullPath
    }
    state.pageOpenedList.splice(get.index, 1, openedPage)
  },
  setPageTag(state, tagObj) {
    state.pageOpenedList.push(tagObj)
  },
  removePageTag(state, path) {
    state.pageOpenedList = state.pageOpenedList.filter(
      item => item.fullPath !== path
    )
  },
  closeOtherPage(state, name) {
    state.pageOpenedList = state.pageOpenedList.filter(
      item =>
        item.path === state.currentPageName ||
        item.fullPath === state.currentPageName ||
        item.fixed
    )
  },
  setCurrentPageName(state, path) {
    state.currentPageName = path
  }
}
