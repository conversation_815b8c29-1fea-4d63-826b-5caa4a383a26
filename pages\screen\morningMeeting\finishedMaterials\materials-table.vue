<template>
  <div class="full-height">
    <screen-border :title="title">
      <template v-slot:headerRight>
        <span
          v-command="'/screen/morningMeeting/edit'"
          class="screen-btn"
          @click="dialogVisible = true">
          <el-icon class="el-icon-edit-outline"/>
          操作
        </span>
      </template>
      <slot name="content"/>
      <div
        v-if="showTable"
        ref="table1"
        class="scroll-wrapper">
        <el-table
          v-loading="loading"
          :data="showGridData"
          :max-height="maxHeight"
          :row-class-name="unPlanedTotalClass"
          :span-method="handleObjectSpan"
          class="center-table"
          border>
          <template
            v-for="(item, index) in setList">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.type === 'index'"
                :key="index"
                :label="item.label"
                :fixed="item.fixed"
                type="index"
                width="100"
              />
              <template v-else>
                <template v-if="item.inputType === 'textarea'">
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label"
                    :fixed="item.fixed"
                    :align="item.align">
                    <template v-slot="{ row }">
                      <div
                        slot="content"
                        v-html="formatText(row[item.keySave], item.split)"
                      />
                    </template>
                  </el-table-column>
                </template>
                <template v-else>
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label"
                    :fixed="item.fixed"
                    :align="item.align">
                    <template v-slot="{ row }">
                      <span :class="getColor(row, item.keySave)">
                        {{ row[item.keySave] }}
                      </span>
                    </template>
                  </el-table-column>
                </template>
              </template>
            </template>
          </template>
        </el-table>
        <div
          class="fail-reason"
          style="height: 80px">
          <span>备注：</span><span>{{ note.text || '无' }}</span>
        </div>
      </div>
    </screen-border>
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEdit">
              <el-dropdown @command="handleProcessedCommand($event)">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportTable">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <el-form :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="gridData"
          :span-method="handleObjectSpan"
          border>
          <template
            v-for="(item, index) in setList">
            <template v-if="1">
              <el-table-column
                v-if="item.type === 'index'"
                :key="index"
                :label="item.label"
                :fixed="item.fixed"
                type="index"
                width="100"
              />
              <template v-else>
                <el-table-column
                  :key="index"
                  :width="item.width || ''"
                  :property="item.keySave"
                  :fixed="item.fixed"
                  :label="item.label">
                  <template v-slot="{ row }">
                    <template v-if="item.inputType === 'textarea'">
                      <el-input 
                        v-model="row[item.keySave]"
                        :rows="4"
                        type="textarea"
                      />
                    </template>
                    <template v-else>
                      <el-input v-model="row[item.keySave]"/>
                    </template>
                  </template>
                </el-table-column>
              </template>
            </template>
          </template>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index)">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
        <br>
        <div class="text-center">
          <span
            v-if="canEdit"
            class="screen-btn"
            @click="addGridData()">
            <el-icon class="el-icon-circle-plus-outline"/>
            增加数据
          </span>
        </div>
        <br>
        <el-form-item label="备注:">
          <el-input
            v-model="note.edit"
            type="textarea"
            placeholder="输入备注"/>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import {
  findBoardParameterByDateAndPara,
  qmsQualityQuery,
  qmsQualitySave,
  saveBoardParameter
} from '@/api/screen'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { boolean } from 'mathjs'

export default {
  name: 'materials-table',
  components: { ScreenBorder },
  props: {
    title: {
      type: String,
      default: ''
    },
    setting: {
      type: Array,
      default: function() {
        return []
      }
    },
    mergeArr: {
      type: Array,
      default: function() {
        return []
      }
    },
    defaultData: {
      type: Array,
      default: function() {
        return []
      }
    },
    selectDate: {
      type: String,
      default: ''
    },
    urlList: {
      type: String,
      default: ''
    },
    urlSave: {
      type: String,
      default: ''
    },
    canEdit: {
      type: Boolean,
      default: true
    },
    showTable: {
      type: Boolean,
      default: true
    },
    heightAuto: {
      type: Boolean,
      default: true
    },
    heightSet: {
      type: Number,
      default: 0
    }
  },
  data: function() {
    return {
      note: {
        text: '',
        edit: ''
      },
      cDate: '',
      loading: false,
      dialogVisible: false,
      showGridData: [],
      gridData: [],
      setList: this.setting,
      importDate: null,
      importDateVisible: false,
      importFunName: '',
      spanArr: {},
      position: 0,
      maxHeight: null,
      ABC: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
        'AA',
        'AB',
        'AC',
        'AD',
        'AE',
        'AF',
        'AG',
        'AH',
        'AI',
        'AJ',
        'AK',
        'AL',
        'AM',
        'AN'
      ]
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.getData()
    },
    setting: function() {
      this.setList = this.setting
      this.$nextTick(() => {
        this.setList.forEach(set => {
          if (this.showGridData[0][set.keySave] === null) {
            set.show = false
          }
        })
      })
    },
    heightSet: function() {
      this.maxHeight = this.heightSet ? this.heightSet : null
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.getData()
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    // 导入文件
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      const obj = {}
      this.setList.forEach((item, index) => {
        obj[item.keySave] = this.ABC[index]
      })
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, obj)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        console.log(sheet)
        this.gridData = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    // 导出表格
    exportTable() {
      const obj = {}
      this.setList.forEach((item, index) => {
        obj[item.keySave] = item.label
      })
      const data = [obj].concat(
        _.cloneDeep(
          this.gridData.map(item => {
            const objRow = {}
            this.setList.forEach(set => {
              objRow[set.keySave] = item[set.keySave]
            })
            return objRow
          })
        )
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `${this.title}（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 获取数据
    async getData() {
      post(this.urlList, {
        setDate: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('YYYY-MM')
      }).then(res => {
        //
        this.loading = false
        const dataArr = res.data.length ? res.data : this.defaultData
        this.setList.forEach(set => {
          if (dataArr[0][set.keySave] === null) {
            set.show = false
          }
        })
        this.showGridData = dataArr.map(item => {
          const obj = {}
          this.setList.forEach(set => {
            obj[set.keySave] = item[set.keyQuery]
          })
          return obj
        })
        this.gridData = _.cloneDeep(this.showGridData)
        this.formatSpanData(this.showGridData)
        this.$nextTick(() => {
          this.$emit('change', this.showGridData)
        })
      })
      // 获取备注参数
      const parameters = await post(findBoardParameterByDateAndPara, {
        setDate: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('YYYY-MM-DD')
      })
      this.note.text = this.note.edit = this.getParam(
        'finishedMaterials',
        parameters.data
      )
      return new Promise(resolve => {
        resolve(true)
      })
    },
    // 更新数据
    saveData() {
      this.loading = true
      // 数据信息
      const params = {
        setDate: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('YYYY-MM'),
        data: this.gridData.map(item => {
          item.setDate = this.$moment(this.cDate)
            .subtract(1, 'day')
            .format('YYYY-MM')
          return item
        })
      }
      post(this.urlSave, params).then(res => {
        //
        this.loading = false
        if (res.status == 1) {
          this.$message.success('数据保存成功！')
          // 保存备注
          const params2 = {
            data: [
              {
                parameter: 'finishedMaterials',
                content: this.note.edit,
                setDate: this.$moment(this.cDate)
                  .subtract(1, 'day')
                  .format('YYYY-MM-DD')
              }
            ]
          }
          post(saveBoardParameter, params2).then(res => {
            this.loading = false
            if (res.status === 1) {
              this.dialogVisible = false
              this.$message.success('备注保存成功！')
            }
          })
          this.getData()
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    // 导入日期数据
    importData(date) {
      post(this.urlList, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.gridData = res.data.map(item => {
          const obj = {}
          this.setList.forEach(set => {
            obj[set.keySave] = item[set.keyQuery]
          })
          return obj
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 执行导入
    importHistoryData() {
      this.importData(this.importDate)
      this.importDateVisible = false
    },
    // 下拉菜单指令
    handleProcessedCommand(command) {
      if (command === 'yesterday') {
        this.importData(
          this.$moment(this.cDate)
            .subtract(1, 'day')
            .format('yyyy-MM-DD')
        )
      } else {
        this.importDate = this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
        this.importDateVisible = true
      }
    },
    // 数据管理
    clearGridData() {
      this.gridData = []
    },
    addGridData() {
      this.gridData.push({})
    },
    delGridData(index) {
      this.gridData.splice(index, 1)
    },
    // 日期改变推送
    changeDate($event) {
      console.log($event)
      this.$emit('dateChange', $event)
    },
    // 计算需要合并的单元格
    formatSpanData(data) {
      this.mergeArr.forEach(keyName => {
        this.spanArr[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
      // console.log(this.spanArr)
    },
    // 合并单元格
    handleObjectSpan({ row, column, rowIndex, columnIndex }) {
      // 需要合并的列
      // [0, 1, 2].includes(columnIndex ), 表示合并前三列
      if (this.mergeArr.includes(column.property)) {
        const _row = this.spanArr[column.property][rowIndex]
        const _col = _row > 0 ? 1 : 0
        // console.log(column.property, rowIndex, _row, _col)
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 生成带换行数据
    formatText(text, split) {
      if (!text) {
        return ''
      }
      if (split) text = text.split(split).join('\n')
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },
    // 计算高度
    calculate() {
      this.showTable &&
        this.heightAuto &&
        (this.maxHeight = this.$refs.table1.offsetHeight - 90)
    },
    getParam(name, list) {
      const match = list.find(item => item.parameter === name)
      return match ? match.content : null
    },
    getColor(row, name) {
      if (!row.target) return ''
      if (name.indexOf('day') === -1) return ''
      if (row.target.indexOf('≥') !== -1) {
        return Number(row[name]) >= Number(row.target.replace('≥', ''))
          ? ''
          : 'red'
      }
      if (row.target.indexOf('≤') !== -1) {
        return Number(row[name]) <= Number(row.target.replace('≤', ''))
          ? ''
          : 'red'
      }
      return ''
    },

    unPlanedTotalClass(row) {
      if (
        row.row.unit &&
        (row.row.unit.trim() === '合计' || row.row.unit.trim() === '小计')
      ) {
        return 'table-total'
      }
      return ''
    }
  }
}
</script>

<style scoped lang="less">
// 大屏按钮
.screen-btn {
  display: inline-block;
  min-width: 68px;
  height: 28px;
  padding: 0 5px;
  background: rgba(31, 198, 255, 0.3);
  border: 1px solid #1fc6ff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  color: #fff;
  &:hover {
    background: rgba(31, 198, 255, 0.6);
    border: 1px solid #1fc6ff;
  }
}

.fail-reason {
  margin-top: 3px;
  display: flex;
  font-size: 14px;
  line-height: 24px;
  border-top: 2px solid rgba(31, 101, 122, 0.3);
  padding-top: 3px;
  color: #ffffff;
  span:first-child {
    width: 50px;
    line-height: 24px;
    font-size: 16px;
    white-space: nowrap;
    color: #ffffff;
  }
  span:last-child {
    flex: 1;
    overflow: auto;
  }
}
.scroll-wrapper {
  height: 100%;
  overflow: auto;
}
/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}
/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
/deep/ .red {
  color: #ff0000;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
  //   width: 80px; /* 对于水平滚动条的设置 */
  height: 25px; /* 对于垂直滚动条的设置 */
}
</style>
