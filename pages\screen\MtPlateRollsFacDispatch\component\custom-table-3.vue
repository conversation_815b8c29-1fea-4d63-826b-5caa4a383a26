<template>
  <div class="full-height">
    <screen-border :title="title">
      <template v-slot:headerRight>
        <span
          v-if="showEdit"
          class="screen-btn"
          @click="dialogVisible = true">
          <el-icon class="el-icon-edit-outline" />
          操作
        </span>
        <!-- <span
          v-command="'/screen/MtPlateRollsFacDispatch/edit'"
          v-if="showEdit"
          class="screen-btn"
          @click="dialogVisible = true">
          <el-icon class="el-icon-edit-outline"/>
          操作
        </span> -->
      </template>
      <slot name="content" />
      <div
        v-if="showTable"
        ref="table1"
        class="scroll-wrapper">
        <el-table
          v-loading="loading"
          :data="showGridData"
          :max-height="maxHeight"
          :span-method="spanMethod"
          class="center-table"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.children"
                :key="index"
                :width="item.width || ''"
                :property="item.keySave"
                :label="item.label"
                :align="item.align">
                <template
                  v-for="(cItem, cIndex) in item.children">
                  <template v-if="item.inputType === 'textarea'">
                    <el-table-column
                      :key="cIndex"
                      :width="cItem.width || ''"
                      :property="cItem.keySave"
                      :label="cItem.label"
                      :align="cItem.align">
                      <template v-slot="{ row }">
                        <div
                          slot="content"
                          v-html="formatText(row[cItem.keySave], cItem.split)"
                        />
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      :key="cIndex"
                      :width="cItem.width || ''"
                      :property="cItem.keySave"
                      :label="cItem.label +(cItem.max ? ' (' + cItem.max + ')' : '') "
                      :align="cItem.align">
                      <template v-slot="{ row }">
                        <span :class="{'red': isColor(row, cItem.max, cItem.keySave)}">
                          {{ row[cItem.keySave] }}
                        </span>
                      </template>
                    </el-table-column>
                  </template>
                </template>
              </el-table-column>
              <template v-else>
                <el-table-column
                  v-if="item.type === 'index'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                />
                <template v-else>
                  <template v-if="item.inputType === 'textarea'">
                    <el-table-column
                      :key="index"
                      :width="item.width || ''"
                      :property="item.keySave"
                      :label="item.label"
                      :align="item.align">
                      <template v-slot="{ row }">
                        <div
                          slot="content"
                          v-html="formatText(row[item.keySave], item.split)"
                        />
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      :key="index"
                      :width="item.width || ''"
                      :property="item.keySave"
                      :label="item.label + (item.max ? ' (' + item.max + ')' : '') "
                      :align="item.align">
                      <template v-slot="{ row }">
                        <span :class="{'red': isColor(row, item.max, item.keySave)}">
                          {{ row[item.keySave] }}
                        </span>
                      </template>
                    </el-table-column>
                  </template>
                </template>
              </template>
            </template>
          </template>
        </el-table>
      </div>
    </screen-border>
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate" />
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-document-checked" />
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="gridData"
          :span-method="spanMethod"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.children"
                :key="index"
                :width="item.width || ''"
                :label="item.label">
                <template
                  v-for="(cItem, cIndex) in item.children">
                  <el-table-column
                    :key="cIndex"
                    :width="cItem.width || ''"
                    :property="cItem.keySave"
                    :label="cItem.label">
                    <template v-slot="{ row }">
                      <template v-if="cItem.inputType === 'textarea'">
                        <el-input
                          v-model="row[cItem.keySave]"
                          :rows="4"
                          type="textarea"
                        />
                      </template>
                      <template v-else>
                        <el-input v-model="row[cItem.keySave]" />
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </el-table-column>
              <template v-else>
                <el-table-column
                  v-if="item.type === 'index'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                />
                <template v-else>
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label">
                    <template v-slot="{ row }">
                      <template v-if="item.inputType === 'textarea'">
                        <el-input
                          v-model="row[item.keySave]"
                          :rows="4"
                          type="textarea"
                        />
                      </template>
                      <template v-else>
                        <el-input v-model="row[item.keySave]" />
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </template>
            </template>
          </template>
        </el-table>
      </el-form>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'" />
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import { qmsQualityQuery, qmsQualitySave } from '@/api/screen'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'

export default {
  name: 'energy-table',
  components: { ScreenBorder },
  props: {
    title: {
      type: String,
      default: ''
    },
    setting: {
      type: Array,
      default: function() {
        return []
      }
    },
    mergeSet: {
      type: Object,
      default: function() {
        return {}
      }
    },
    selectDate: {
      type: String,
      default: ''
    },
    urlList: {
      type: String,
      default: ''
    },
    urlSave: {
      type: String,
      default: ''
    },
    showTable: {
      type: Boolean,
      default: true
    },
    showEdit: {
      type: Boolean,
      default: true
    },
    heightAuto: {
      type: Boolean,
      default: true
    }
  },
  data: function() {
    return {
      cDate: '',
      loading: false,
      dialogVisible: false,
      showGridData: [],
      gridData: [],
      importDate: null,
      importDateVisible: false,
      importFunName: '',
      mergeArr: [],
      spanArr: {},
      position: 0,
      maxHeight: null,
      ABC: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'K'
      ],
      tData: [
        {
          actualNumber_t: '',
          classes: '大夜班',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: ''
        },
        {
          actualNumber_t: '',
          classes: '白班',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: ''
        },
        {
          actualNumber_t: '',
          classes: '小夜班',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: ''
        },
        {
          actualNumber_t: '',
          classes: '合计',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: ''
        }
      ],
      slicingData: [
        {
          actualNumberL: '',
          actualNumberR: '',
          planNumber: '',
          remark: '',
          tonnageL: '',
          tonnageR: '',
          toBeCutMeasure: '',
          totalCuttingYesterday: ''
        }
      ]
    }
  },
  computed: {
    canEditQuality: function() {
      // console.log(moment().format('yyyy-MM-DD'), this.cDate)
      return (
        moment().format('yyyy-MM-DD') <=
        moment(this.cDate)
          .subtract(-1, 'day')
          .format('yyyy-MM-DD')
      )
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getData()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.getData()
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    isColor(row, max, keyName) {
      if (!max) return false
      if (row.classes === '天汇总') {
        return this.judgeStatus(row.classes, '日单耗', max, keyName)
      }
      return this.judgeStatus(row.classes, '班单耗', max, keyName)
    },
    judgeStatus(classes, name, max, keyName) {
      const match = this.showGridData.find(
        item => item.classes === classes && item.name === name
      )
      if (!match) {
        return false
      }
      return match[keyName] > max
    },

    // 导入文件
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = this.ABC[index]
      })
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, obj)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.gridData = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    // 导出表格
    exportTable() {
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = item.label
      })
      const data = [obj].concat(
        _.cloneDeep(
          this.gridData.map(item => {
            const objRow = {}
            this.setting.forEach(set => {
              objRow[set.keySave] = item[set.keySave]
            })
            return objRow
          })
        )
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `${this.title}（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 获取数据
    getData() {
      post(this.urlList, {
        setTime: this.cDate,
        date: this.cDate
      }).then(res => {
        this.loading = false
        console.log('%c res：', 'color: red', res)
        console.log('%c res.tData：', 'color: red', res.tData)
        this.clearGridData()
        if (res.tData.length > 0 && res.slicingData.length > 0) {
          this.tData = res.tData
          console.log('%c tData数据000：', 'color: red', this.tData)
          this.tData[0] = Object.assign({}, this.tData[0], res.slicingData[0])
        } else {
          this.tData[0] = Object.assign({}, this.tData[0], this.slicingData)
        }
        this.tData.push({
          actualNumber_t: this.tData[0].toBeCutMeasure || '',
          classes: '待切割量',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: '',
          actualNumberL: '昨日切割合计',
          actualNumberR: this.tData[0].totalCuttingYesterday || '',
          planNumber: '',
          remark: '',
          tonnageL: '',
          tonnageR: '',
          toBeCutMeasure: '',
          totalCuttingYesterday: ''
        })
        console.log('%c tData数据：', 'color: red', this.tData)
        this.showGridData = this.tData
        console.log('%c showGridData数据：', 'color: red', this.showGridData)
        this.gridData = _.cloneDeep(this.showGridData)
        console.log('%c gridData数据：', 'color: red', this.gridData)
        this.$nextTick(() => {
          this.$emit('change', this.showGridData)
        })
      })
    },
    // 更新数据
    saveData() {
      this.loading = true
      this.clearTData()
      for (let i = 0; i < this.gridData.length - 1; i++) {
        this.tData[i] = {
          actualNumber_t: this.gridData[i].actualNumber_t,
          classes: this.gridData[i].classes,
          planNumber_t: this.gridData[i].planNumber_t,
          remark_t: this.gridData[i].remark_t,
          tonnage_t: this.gridData[i].tonnage_t
        }
      }
      const length = this.gridData.length - 1
      this.slicingData = [
        {
          actualNumberL: this.gridData[0].actualNumberL,
          actualNumberR: this.gridData[0].actualNumberR,
          planNumber: this.gridData[0].planNumber,
          remark: this.gridData[0].remark,
          tonnageL: this.gridData[0].tonnageL,
          tonnageR: this.gridData[0].tonnageR,
          toBeCutMeasure: this.gridData[length].actualNumber_t,
          totalCuttingYesterday: this.gridData[length].actualNumberR
        }
      ]
      // 数据信息
      const params = {
        setTime: this.cDate,
        tData: this.tData,
        slicingData: this.slicingData
      }
      post(this.urlSave, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.dialogVisible = false
          this.getData()
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    // 导入日期数据
    importData(date) {
      post(this.urlList, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.gridData = res.data.map(item => {
          const obj = {}
          this.setting.forEach(set => {
            obj[set.keySave] = item[set.keyQuery]
          })
          return obj
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 执行导入
    importHistoryData() {
      this.importData(this.importDate)
      this.importDateVisible = false
    },
    // 下拉菜单指令
    handleProcessedCommand(command) {
      if (command === 'yesterday') {
        this.importData(
          this.$moment(this.cDate)
            .subtract(1, 'day')
            .format('yyyy-MM-DD')
        )
      } else {
        this.importDate = this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
        this.importDateVisible = true
      }
    },
    clearTData() {
      this.tData = [
        {
          actualNumber_t: '',
          classes: '大夜班',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: ''
        },
        {
          actualNumber_t: '',
          classes: '白班',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: ''
        },
        {
          actualNumber_t: '',
          classes: '小夜班',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: ''
        },
        {
          actualNumber_t: '',
          classes: '合计',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: ''
        }
      ]
    },
    // 数据管理
    clearGridData() {
      this.slicingData = [
        {
          actualNumberL: '',
          actualNumberR: '',
          planNumber: '',
          remark: '',
          tonnageL: '',
          tonnageR: '',
          toBeCutMeasure: '',
          totalCuttingYesterday: ''
        }
      ]
      this.gridData = [
        {
          actualNumber_t: '',
          classes: '大夜班',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: '',
          actualNumberL: '',
          actualNumberR: '',
          planNumber: '',
          remark: '',
          tonnageL: '',
          tonnageR: '',
          toBeCutMeasure: '',
          totalCuttingYesterday: ''
        },
        {
          actualNumber_t: '',
          classes: '白班',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: ''
        },
        {
          actualNumber_t: '',
          classes: '小夜班',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: ''
        },
        {
          actualNumber_t: '',
          classes: '合计',
          planNumber_t: '',
          remark_t: '',
          tonnage_t: ''
        }
      ]
    },
    // 日期改变推送
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    // 生成带换行数据
    formatText(text, split) {
      if (!text) {
        return ''
      }
      if (split) text = text.split(split).join('\n')
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },
    // 计算高度
    calculate() {
      this.showTable &&
        this.heightAuto &&
        (this.maxHeight = this.$refs.table1.offsetHeight)
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (rowIndex < this.gridData.length - 1 && columnIndex > 4) {
        return [this.gridData.length - 1, 1]
      }
      if (rowIndex === this.gridData.length - 1) {
        if (columnIndex === 0) {
          return [1, 2]
        } else if (columnIndex === 2) {
          return [1, 4]
        } else if (columnIndex === 6) {
          return [1, 2]
        } else if (columnIndex === 8) {
          return [1, 3]
        } else {
          return [0, 0]
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
// 大屏按钮
.screen-btn {
  display: inline-block;
  min-width: 68px;
  height: 28px;
  padding: 0 5px;
  background: rgba(31, 198, 255, 0.3);
  border: 1px solid #1fc6ff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  color: #fff;

  &:hover {
    background: rgba(31, 198, 255, 0.6);
    border: 1px solid #1fc6ff;
  }
}

.scroll-wrapper {
  width: 100%;
  height: 100%;
}

.red {
  color: #ff0000;
}

/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}

/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
</style>
