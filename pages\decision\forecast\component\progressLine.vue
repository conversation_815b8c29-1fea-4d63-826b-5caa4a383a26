<template>
  <div
    :class="getClass"
    class="progress-box">
    <div
      class="relative">
      <div class="name">
        <div class="compare">{{ progressData.resultValue ? progressData.resultValue.toFixed(2) : 0 }}/{{ progressData.targetValue || 0 }}</div>
        {{ name }}
      </div>
      <el-progress
        :text-inside="true" 
        :stroke-width="15"
        :percentage="(progressData.percent > 100 ? 100 : progressData.percent) || 0"
        :color="getColor"
        :stroke-linecap="'square'"
        :format="getText"/>
    </div>
  </div>
</template>

<script>
export default {
  name: 'progress-line',
  props: {
    name: {
      default: '',
      type: String
    },
    progressData: {
      default: function() {
        return {}
      },
      type: Object
    }
  },
  computed: {
    getColor: function() {
      if (this.progressData.warningStatus) {
        return '#ffb243'
      }
      if (this.progressData.trendStatus) {
        return '#ffb243'
      }
      return '#00b0f0'
    },
    getClass: function() {
      if (this.progressData.warningStatus) {
        return 'warning'
      }
      if (this.progressData.trendStatus) {
        return 'trend'
      }
      return ''
    }
  },
  methods: {
    getText() {
      return ``
    }
  }
}
</script>

<style scoped lang="less">
.progress-box {
  position: relative;
  margin-bottom: 5px;
  overflow: hidden;
  .name {
    font-size: 14px;
    line-height: 24px;
    color: #6a74a5;
  }
  .compare {
    float: right;
    font-size: 16px;
    font-weight: bold;
    color: #19be6b;
  }
  &.warning .compare {
    color: #ffb243;
  }
  &.trend .compare {
    color: #ffb243;
  }
}
/deep/ .el-progress-bar__outer {
  border-radius: unset;
}
/deep/ .el-progress-bar__inner {
  border-radius: unset;
}
</style>
