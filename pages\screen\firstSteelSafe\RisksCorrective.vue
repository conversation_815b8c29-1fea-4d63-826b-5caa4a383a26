<template>
  <div class="bigBox">
    <screen-border title="隐患整改">
      <template v-slot:headerRight>
        <div class="header">
          <div 
            v-show="!isNew" 
            class="searchCriteria">
            <el-date-picker
              v-model="S_TimeInterval"
              type="datetimerange"
              align="right"
              value-format="yyyy-MM-dd HH:mm:ss"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"/>
            <el-select 
              v-model="S_posts" 
              clearable 
              placeholder="请选择单位">
              <el-option
                v-for="item in areaOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
            <el-select 
              v-model="S_hazardLevel" 
              clearable 
              placeholder="请选择隐患级别">
              <el-option
                v-for="item in hazardLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
            <el-select 
              v-model="S_hazardCategory" 
              clearable 
              placeholder="请选择隐患类别">
              <el-option
                v-for="item in hazardCategoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
            <el-select 
              v-model="S_rectifyProgress" 
              clearable 
              placeholder="请选择整改进度">
              <el-option
                v-for="item in rectifyProgressOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
            <!-- <el-input 
              v-model="liability"
              clearable
              placeholder="请输入责任相关字段"/> -->
            <span
              class="screen-btn search"
              @click="getHiddenDangerRecData">
              搜索
            </span>
          </div>
          <div class="btnBox">
            <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              v-show="isNew"
              class="screen-btn"
              @click="saveNewData">
              <el-icon class="el-icon-printer"/>
              保存
            </span>
            <span
              v-show="isNew"
              class="screen-btn"
              @click="back">
              <el-icon class="el-icon-d-arrow-left"/>
              返回
            </span>
            <span
              v-show="!isNew"
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              v-show="!isNew"
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download"/>
              下载
            </span>
            <span
              v-show="!isNew"
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span>
          </div>
        </div>
      </template>
      <el-table
        v-loading="loading"
        v-show="!isNew"
        id="table"
        :data="tableData"
        element-loading-text="加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        height="calc(100vh - 195px)"
        border>
        <el-table-column
          show-overflow-tooltip
          width="60"
          align="center"
          label="序号">
          <template v-slot="scope">
            <div>{{ scope.$index + 1 }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="隐患所在岗位"
          align="center"
          prop="posts"
          width="100"/>
        <el-table-column
          :show-overflow-tooltip="true"
          label="安全隐患及风险描述"
          prop="describe"
          align="center"/>
        <el-table-column
          label="风险类别"
          prop="riskLevel"
          align="center"/>
        <el-table-column
          label="隐患级别"
          prop="hazardLevel"
          align="center"/>
        <el-table-column
          label="隐患类别"
          prop="hazardCategory"
          align="center"/>
        <el-table-column
          label="整改责任单位"
          prop="rectifyUnit"
          align="center"/>
        <el-table-column
          label="整改责任人"
          prop="rectifyPeople"
          align="center"
          width="100"/>
        <el-table-column
          label="隐患检查时间"
          prop="checkTime"
          align="center"
          width="130">
          <template v-slot="scope">
            {{ scope.row.checkTime.split(' ')[0] }}
          </template>
        </el-table-column>
        <el-table-column
          label="整改计划完成时间"
          prop="completionTime"
          align="center">
          <template v-slot="scope">
            {{ scope.row.completionTime.split(' ')[0] }}
          </template>
        </el-table-column>
        <el-table-column
          label="整改方案"
          prop="rectifyPlan"
          align="center"/>
        <el-table-column
          label="整改进度"
          prop="rectifyProgress"
          align="center"/>
        <el-table-column
          align="center"
          width="150"
          label="整改前图片">
          <template v-slot="scope">
            <div class="actionBox">
              <div class="fileBox">
                <el-checkbox 
                  v-for="(item,index) in scope.row.checkPics" 
                  :key="index"
                  v-model="item.checked">
                  <span 
                    class="fileTitle"
                    @click.prevent="getImgFormat(item.img)">{{ item.name }}</span></el-checkbox>
              </div>
              <div>
                <el-upload
                  ref="upload"
                  :file-list="fileList1"
                  :multiple ="false"
                  :on-exceed="handleExceed"
                  :before-upload="beforeUpload"
                  :on-success="(response, file, fileList)=>handleSuccess(response, file, fileList, scope.row,'examine')"
                  :show-file-list="false"
                  action="">
                  <i class="el-icon-upload actionBtn"/>
                </el-upload>
                <i 
                  v-show="scope.row.checkPics.length!=0"
                  class="el-icon-delete actionBtn" 
                  @click="delExamineImg(scope.row)"/>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          width="150"
          label="整改后图片">
          <template v-slot="scope">
            <div class="actionBox">
              <div class="fileBox">
                <el-checkbox 
                  v-for="(item,index) in scope.row.correctivePics" 
                  :key="index"
                  v-model="item.checked">
                  <span 
                    class="fileTitle"
                    @click.prevent="getImgFormat(item.img)">{{ item.name }}</span></el-checkbox>
              </div>
              <div>
                <el-upload
                  ref="upload"
                  :file-list="fileList2"
                  :multiple ="false"
                  :on-exceed="handleExceed"
                  :before-upload="beforeUpload"
                  :on-success="(response, file, fileList)=>handleSuccess(response, file, fileList, scope.row,'Rectification')"
                  :show-file-list="false"
                  action="">
                  <i class="el-icon-upload actionBtn"/>
                </el-upload>
                <i 
                  v-show="scope.row.correctivePics.length!=0"
                  class="el-icon-delete actionBtn" 
                  @click="delRectificationimg(scope.row)"/>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          property=""
          width="120"
          label="操作">
          <template v-slot="scope">
            <span
              style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
              @click="editRow(scope.row)">编辑</span>
            <span
              style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
              @click="DelRow(scope.row)">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 新增行 -->
      <el-table
        v-show="isNew"
        :data="newData"
        class="newCss"
        height="calc(100vh - 195px)"
        border>
        <el-table-column
          label="隐患所在岗位"
          prop="posts"
          width="150"
          align="center">
          <template v-slot="scope">
            <el-select 
              v-model="scope.row.posts" 
              clearable 
              placeholder="请选择">
              <el-option
                v-for="item in areaOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          label="安全隐患及风险描述"
          prop="describe"
          width="200"
          align="center">
          <template v-slot="scope">
            <el-input
              :rows="4"
              v-model="scope.row.describe"
              type="textarea"/>
          </template>
        </el-table-column>
        <el-table-column
          label="风险类别"
          prop="riskLevel"
          width="160"
          align="center">
          <template v-slot="scope">
            <el-input v-model="scope.row.riskLevel"/>
          </template>
        </el-table-column>
        <el-table-column
          label="隐患级别"
          prop="hazardLevel"
          width="150"
          align="center">
          <template v-slot="scope">
            <el-select 
              v-model="scope.row.hazardLevel" 
              clearable 
              placeholder="请选择">
              <el-option
                v-for="item in hazardLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          label="隐患类别"
          prop="hazardCategory"
          width="150"
          align="center">
          <template v-slot="scope">
            <el-select 
              v-model="scope.row.hazardCategory" 
              clearable 
              placeholder="请选择">
              <el-option
                v-for="item in hazardCategoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          label="整改责任单位"
          prop="rectifyUnit"
          width="150"
          align="center">
          <template v-slot="scope">
            <el-select 
              v-model="scope.row.rectifyUnit" 
              clearable 
              placeholder="请选择">
              <el-option
                v-for="item in areaOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          label="整改责任人"
          prop="rectifyPeople"
          align="center"
          width="120">
          <template v-slot="scope">
            <el-input v-model="scope.row.rectifyPeople"/>
          </template>
        </el-table-column>
        <el-table-column
          label="隐患检查时间"
          prop="checkTime"
          align="center"
          width="160">
          <template v-slot="scope">
            <el-date-picker
              v-model="scope.row.checkTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              type="date"
              placeholder="选择日期时间"/>
          </template>
        </el-table-column>
        <el-table-column
          label="整改计划完成时间"
          prop="completionTime"
          align="center"
          width="180">
          <template v-slot="scope">
            <el-date-picker
              v-model="scope.row.completionTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              type="date"
              placeholder="选择日期时间"/>
          </template>
        </el-table-column>
        <el-table-column
          label="整改方案"
          prop="rectifyPlan"
          align="center">
          <template v-slot="scope">
            <el-input v-model="scope.row.rectifyPlan"/>
          </template>
        </el-table-column>
        <el-table-column
          label="整改进度"
          prop="rectifyProgress"
          width="150"
          align="center">
          <template v-slot="scope">
            <el-select 
              v-model="scope.row.rectifyProgress" 
              clearable 
              placeholder="请选择">
              <el-option
                v-for="item in rectifyProgressOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          property=""
          width="100"
          label="操作">
          <template v-slot="scope">
            <el-button 
              type="danger"
              icon="el-icon-remove-outline" 
              size="mini"
              @click="delRow(scope.row)"/>
          </template>
        </el-table-column>
      </el-table>
    </screen-border>

    <!--修改-->
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="60%"
      top="5vh"
      class="screen-dialog"
      title="编辑">
      <template v-slot:title>
        <div class="custom-dialog-title">
          编辑
        </div>
      </template>
      <div class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">隐患所在岗位</div>
          <el-select 
            v-model="reviseRow.posts" 
            clearable 
            placeholder="请选择">
            <el-option
              v-for="item in areaOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">安全隐患及风险描述</div>
          <el-input
            :rows="4"
            v-model="reviseRow.describe"
            type="textarea"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">风险类别</div>
          <el-input v-model="reviseRow.riskLevel"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">隐患级别</div>
          <el-select 
            v-model="reviseRow.hazardLevel" 
            clearable 
            placeholder="请选择">
            <el-option
              v-for="item in hazardLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">隐患类别</div>
          <el-select 
            v-model="reviseRow.hazardCategory" 
            clearable 
            placeholder="请选择">
            <el-option
              v-for="item in hazardCategoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改责任单位</div>
          <el-select 
            v-model="reviseRow.rectifyUnit" 
            clearable 
            placeholder="请选择">
            <el-option
              v-for="item in areaOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改责任人</div>
          <el-input v-model="reviseRow.rectifyPeople"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">隐患检查时间</div>
          <el-date-picker
            v-model="reviseRow.checkTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            type="date"
            placeholder="选择日期时间"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改计划完成时间</div>
          <el-date-picker
            v-model="reviseRow.completionTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            type="date"
            placeholder="选择日期时间"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改方案</div>
          <el-input v-model="reviseRow.rectifyPlan"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改进度</div>
          <el-select 
            v-model="reviseRow.rectifyProgress" 
            clearable 
            placeholder="请选择">
            <el-option
              v-for="item in rectifyProgressOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="sumbit">
          确定
        </span>
      </div>
    </el-dialog>
    <!-- 图片显示 -->
    <el-image 
      v-show="false"
      ref="previewImg"
      :preview-src-list="srcList"
      src=""/>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { saveAs } from 'file-saver'
import {
  HIDDENDANGERREC,
  HIDDENDANGERADDS,
  HIDDENDANGERBEFORE,
  HIDDENDANGERAFTER,
  HIDDENDANGERDEL
} from '@/api/screenC2'
import moment from 'moment'

export default {
  name: 'SignificantRisks',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      //时间区间
      S_TimeInterval: null,
      S_posts: '',
      S_hazardLevel: '',
      S_hazardCategory: '',
      S_rectifyProgress: '',

      //车间下拉
      areaOptions: [
        {
          value: '原料车间',
          label: '原料车间'
        },
        {
          value: '炼钢车间',
          label: '炼钢车间'
        },
        {
          value: '精炼车间',
          label: '精炼车间'
        },
        {
          value: '连铸车间',
          label: '连铸车间'
        }
      ],

      tableData: [
        {
          id: 1,
          posts: '原料车间',
          describe: '有压热闷现场控制箱未上锁',
          riskLevel: '触电',
          hazardLevel: '一般',
          hazardCategory: '现场',
          rectifyUnit: '原料车间',
          rectifyPeople: '闫辰中',
          checkTime: '2024-05-20',
          completionTime: '2024-05-20',
          rectifyPlan: '上锁管控',
          rectifyProgress: '整改中',
          checkPics: [
            {
              name: '照片111111111111111111111111111111',
              img:
                'https://ts1.cn.mm.bing.net/th/id/R-C.********************************?rik=tfH6k%2fT3hkauqw&riu=http%3a%2f%2fwww.08lr.cn%2fuploads%2fallimg%2f220330%2f1-2300141M0.jpg&ehk=dR6hTo1o7lNsHkpE62oIzMtJ%2bmxktf7%2fx6tp3Zt2uB8%3d&risl=&pid=ImgRaw&r=0',
              checked: false
            },
            {
              name: '照片2',
              img: '',
              checked: false
            }
          ],
          correctivePics: [
            {
              name: '照片3',
              img: '',
              checked: false
            }
          ]
        }
      ],
      loading: false,

      //新增行
      newData: [],
      //隐患级别下拉
      hazardLevelOptions: [
        {
          value: '一般',
          label: '一般'
        },
        {
          value: '较大',
          label: '较大'
        },
        {
          value: '重大',
          label: '重大'
        }
      ],
      //隐患类别下拉
      hazardCategoryOptions: [
        {
          value: '管理',
          label: '管理'
        },
        {
          value: '现场',
          label: '现场'
        }
      ],
      //整改进度下拉
      rectifyProgressOptions: [
        {
          value: '未整改',
          label: '未整改'
        },
        {
          value: '整改中',
          label: '整改中'
        }
      ],
      isNew: false,
      rowNum: 0,

      //编辑弹框
      dialogVisible: false,
      reviseRow: {},

      //上传Excel
      fileList: [],
      //table中图片上传
      fileList1: [],
      fileList2: [],
      uploadFileList: [],

      srcList: []
    }
  },
  watch: {},
  mounted() {
    this.getHiddenDangerRecData()
  },
  methods: {
    //获取安全检查数据
    async getHiddenDangerRecData() {
      this.loading = true
      if (this.S_TimeInterval == '' || this.S_TimeInterval == null) {
        this.S_TimeInterval = ['', '']
      }
      let res = await post(HIDDENDANGERREC, {
        posts: this.S_posts,
        hazardLevel: this.S_hazardLevel,
        hazardCategory: this.S_hazardCategory,
        rectifyProgress: this.S_rectifyProgress,
        startTime: this.S_TimeInterval[0],
        endTime: this.S_TimeInterval[1]
      })

      console.log('隐患整改', res)
      if (res.data) {
        this.loading = false
      }
      if (res.data) {
        res.data.forEach(item => {
          if (item.beforeImgName) {
            item.checkPics = [
              {
                name: item.beforeImgName,
                img: item.beforeImgUrl.replace(
                  'http://172.25.63.72:9123/',
                  '/minoApi/'
                ),
                checked: false
              }
            ]
          } else {
            item.checkPics = []
          }
          if (item.afterImgName) {
            item.correctivePics = [
              {
                name: item.afterImgName,
                img: item.afterImgUrl.replace(
                  'http://172.25.63.72:9123/',
                  '/minoApi/'
                ),
                checked: false
              }
            ]
          } else {
            item.correctivePics = []
          }
        })
        this.tableData = res.data
      }
    },
    //添加行
    addNewRow() {
      //清空搜索条件
      this.S_TimeInterval = null
      this.S_posts = ''
      this.S_hazardLevel = ''
      this.S_hazardCategory = ''
      this.S_rectifyProgress = ''

      this.isNew = true
      this.newData.push({
        rowNum: (this.rowNum += 1),
        posts: '',
        describe: '',
        riskLevel: '',
        hazardLevel: '',
        hazardCategory: '',
        rectifyUnit: '',
        rectifyPeople: '',
        checkTime: '',
        completionTime: '',
        rectifyPlan: '',
        rectifyProgress: ''
      })
    },

    //删除新增行
    delRow(row) {
      this.newData.forEach((item, index) => {
        if (row.rowNum == item.rowNum) {
          this.newData.splice(index, 1)
        }
      })
      // console.log(this.newData)
    },

    //保存新增数据
    async saveNewData() {
      console.log('保存新增数据')
      this.newData.forEach(item => {
        delete item.rowNum
      })

      let res = await post(HIDDENDANGERADDS, this.newData)

      if (res.status == 1) {
        //清空搜索条件
        this.S_TimeInterval = null
        this.S_posts = ''
        this.S_hazardLevel = ''
        this.S_hazardCategory = ''
        this.S_rectifyProgress = ''

        this.getHiddenDangerRecData()
        this.back()
        this.$message.success(res.data)
      } else {
        this.$message.success('新增失败!')
      }
    },
    //返回
    back() {
      this.isNew = false
      this.newData = []
      this.rowNum = 0
    },
    //下载模板
    DownloadExcel() {
      const data = [
        {
          posts: '隐患所在岗位',
          describe: '安全隐患及风险描述',
          riskLevel: '风险类别',
          hazardLevel: '隐患级别',
          hazardCategory: '隐患类别',
          rectifyUnit: '整改责任单位',
          rectifyPeople: '整改责任人',
          checkTime: '隐患检查时间',
          completionTime: '整改计划完成时间',
          rectifyPlan: '整改方案',
          rectifyProgress: '整改进度'
        }
      ]
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:K' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `隐患整改模板.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          '隐患整改.xlsx'
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          posts: 'A',
          describe: 'B',
          riskLevel: 'C',
          hazardLevel: 'D',
          hazardCategory: 'E',
          rectifyUnit: 'F',
          rectifyPeople: 'G',
          checkTime: 'H',
          completionTime: 'I',
          rectifyPlan: 'J',
          rectifyProgress: 'K'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据
        // console.log(list)
        list.forEach(item => {
          item.setDate = this.selectDate
        })
        this.newData = list
        setTimeout(() => {
          this.saveNewData()
        }, 500)
      })
    },

    //编辑
    editRow(row) {
      this.dialogVisible = true
      this.reviseRow = {}
      this.reviseRow = JSON.parse(JSON.stringify(row))
    },
    //编辑确定
    async sumbit() {
      let res = await post(HIDDENDANGERADDS, [this.reviseRow])
      if (res.status == 1) {
        this.dialogVisible = false
        this.getHiddenDangerRecData()
        this.$message.success('编辑成功!')
      }
    },
    //删除
    DelRow(row) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await post(HIDDENDANGERDEL, {
          id: row.id
        })
        if (res.status == 1) {
          this.getHiddenDangerRecData()
          this.$message.success('删除成功!')
        }
      })
    },

    //获取下载图片格式
    async getImgFormat(val) {
      console.log('val', val)
      this.$refs['previewImg'].showViewer = true
      this.srcList = []
      this.srcList.push(val)
      // console.log('row', row)
      //   let Imagelink = row.MINIO_URL.split('?')[0] //解析后预览图片地址
      //   let format = Imagelink.substring(row.MINIO_URL.lastIndexOf('.') + 1) //获取文件格式

      //   if (format == 'png' || format == 'jpeg' || format == 'jpg') {
      //     this.$refs['previewImg'].showViewer = true
      //     this.srcList = []
      //     this.srcList.push(
      //       Imagelink.replace('http://172.25.63.72:9123/', '/picapi/')
      //     )
      //   } else if (format == 'pdf') {
      //     let data = await get(
      //       Imagelink.replace('http://172.25.63.72:9123/', '/picapi/')
      //     )

      //     if (!data) {
      //       return
      //     }
      //     const url = window.URL.createObjectURL(
      //       new Blob([data], { type: 'application/pdf' })
      //     )

      //     window.open(url)
      //   } else {
      //     this.$message.warning('此文件非图片格式或pnf文件格式,无法在线预览')
      //   }
    },

    //删除检查图片
    delExamineImg(row) {
      let bol = row.checkPics.some(item => item.checked)
      if (!bol) {
        this.$message.warning('没有选中的图片,请选择!')
        return
      }
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        row.beforeImgName = ''
        row.beforeImgUrl = ''

        let res = await post(HIDDENDANGERADDS, [row])
        if (res.status == 1) {
          this.getHiddenDangerRecData()
          this.$message.success('删除成功!')
        }
      })
    },
    //删除整改图片
    delRectificationimg(row) {
      let bol = row.correctivePics.some(item => item.checked)
      if (!bol) {
        this.$message.warning('没有选中的图片,请选择!')
        return
      }
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        row.afterImgName = ''
        row.afterImgUrl = ''

        let res = await post(HIDDENDANGERADDS, [row])
        if (res.status == 1) {
          this.getHiddenDangerRecData()
          this.$message.success('删除成功!')
        }
      })
    },

    //上传成功后的回调
    async handleSuccess(response, file, fileList, row, val) {
      this.loading = true

      let formData = new FormData()
      let fileData = {
        id: row.id
      }

      const blob = new Blob([JSON.stringify(fileData)], {
        type: 'application/json'
      })
      formData.append('file', file.raw)
      formData.append('EquipmentTree', blob)

      let res = null
      if (val == 'examine') {
        res = await post(HIDDENDANGERBEFORE, formData)
      } else {
        res = await post(HIDDENDANGERAFTER, formData)
      }
      if (res.includes('http')) {
        this.loading = false
        this.$message.success('上传成功')
        this.getHiddenDangerRecData()
      }
    },

    //超出文件个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      })
      return
    },
    //上传文件之前
    beforeUpload(file) {
      if (file.type != '' || file.type != null || file.type != undefined) {
        //截取文件的后缀，判断文件类型
        const FileExt = file.name.replace(/.+\./, '').toLowerCase()
        //计算文件的大小
        const isLt5M = file.size / 1024 / 1024 < 500 //这里做文件大小限制
        let fileType = ['pdf', 'png', 'jpg'] //设置文件类型
        // 如果大于50M
        if (!isLt5M) {
          this.$message('上传文件大小不能超过 500MB!')
          return false
        }
        //如果文件类型不在允许上传的范围内
        if (fileType.includes(FileExt)) {
          return true
        } else {
          this.$message.error('上传文件格式不正确!')
          return false
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  color: #fff;
  height: calc(100vh - 128px);
  .header {
    display: flex;
    .searchCriteria {
      display: flex;
      align-items: center;
      /deep/.el-input {
        width: 180px;
        margin: 0 5px;
        font-size: 14px;
      }
      /deep/.el-input__inner {
        background: rgba(31, 198, 255, 0.3);
        color: white;
      }
      /deep/.el-date-editor .el-range-separator {
        width: 10%;
      }
      /deep/.el-date-editor .el-range-input,
      /deep/.el-date-editor .el-range-separator {
        color: white;
      }
      /deep/.el-range-editor--small .el-range-separator {
        line-height: 28px;
      }

      .search {
        height: 32px !important;
        line-height: 32px;
        margin-right: 30px;
        border: 1px solid white;
      }
    }
  }
  .newCss {
    /deep/.el-input__inner,
    /deep/.el-textarea__inner {
      background: rgba(31, 198, 255, 0.2);
      color: #fff;
      border-color: rgba(31, 198, 255, 0.6);
    }
    /deep/.el-date-editor.el-input {
      width: 128px;
      .el-input__suffix {
        top: 1px;
      }
    }
    /deep/.el-input__prefix {
      top: 1px;
    }
    /deep/.el-button {
      border-radius: 4px !important;
      font-weight: 700;
      font-size: 20px;
      padding: 4px 20px !important;
    }
  }
  .dialog-body {
    overflow: scroll;
    .dialog-cell {
      margin-bottom: 12px;

      .dialog-cell-title {
        font-size: 16px;
        font-weight: bolder;
        color: #ffffff;
        line-height: 24px;
        margin-bottom: 8px;
      }

      .dialog-cell-title::before {
        content: '1';
        color: #ffffff;
        background: #ffffff;
        width: 8px;
        height: 100%;
        margin-right: 4px;
      }
      /deep/.el-input {
        width: 220px;
      }
    }
  }
  .actionBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .fileBox {
      width: 102px;
      /deep/.el-checkbox {
        margin-right: 0;
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        .el-checkbox__label {
          padding-left: 6px;
          height: 19px;
        }
        .el-checkbox__input {
          top: 2px;
        }
      }
      .fileTitle {
        display: inline-block;
        width: 80px;
        height: 19px;
        border: 2px solid #0b2a34;
        color: white;
        text-align: left;
        cursor: pointer;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //溢出不换行
      }
    }
    .actionBtn {
      font-size: 20px;
      cursor: pointer;
      margin: 4px 0;
    }
  }
}
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
</style>
