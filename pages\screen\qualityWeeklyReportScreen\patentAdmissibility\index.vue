<template>
  <div class="container">
    <div class="header-footer">
      <el-input
        v-model="monthlyReport"
        :rows="3"
        type="textarea"
        class="chart-input"
        resize="none"
      />
      <el-button 
        :loading="submitLoading" 
        type="primary" 
        class="submit-btn"
        icon="el-icon-finished"
        @click="handleSave">提交</el-button>
    </div>
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="专利受理情况">
          <custom-table-export-merge 
            ref="patentAdmissibilityRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'patentAdmissibility'"
            :title="'专利受理情况'" 
            :setting="tableObj.setting" 
            :url-list="tableObj.url.list" 
            :url-save="tableObj.url.save"
            :select-date="selectDate" 
            :dialog-width="'80%'"
          />
        </screen-border>
      </div>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityWeeklyReportScreen/components/screen-border.vue'
import CustomTableExportMerge from '@/pages/screen/qualityWeeklyReportScreen/components/custom-table-export-merge.vue'
import {
  findAllDateRemark,
  saveAllRemark,
  patentAdmissibilityFindAllDate,
  patentAdmissibilitySaveAll
} from '@/api/screen'
import { post } from '@/lib/Util'

export default {
  name: 'PatentAdmissibility',
  components: {
    ScreenBorder,
    CustomTableExportMerge
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      submitLoading: false,
      monthlyReport: '',
      tableObj: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'unit',
            keySave: 'unit',
            label: '单位',
            width: '100'
          },
          {
            label: '提交已受理',
            children: [
              {
                keyQuery: 'acceptedQuota',
                keySave: 'acceptedQuota',
                label: '发明指标',
                width: '220'
              },
              {
                keyQuery: 'acceptedPatent"',
                keySave: 'acceptedPatent"',
                label: '发明专利',
                width: '220'
              },
              {
                keyQuery: 'acceptedNewQuota',
                keySave: 'acceptedNewQuota',
                label: '实用新型指标',
                width: '220'
              },
              {
                keyQuery: 'acceptedNewPatent',
                keySave: 'acceptedNewPatent',
                label: '实用新型专利',
                width: '230'
              }
            ]
          },
          {
            label: '提交暂未受理',
            children: [
              {
                keyQuery: 'notAcceptedQuota',
                keySave: 'notAcceptedQuota',
                label: '发明专利',
                width: '250'
              },
              {
                keyQuery: 'notAcceptedNew',
                keySave: 'notAcceptedNew',
                label: '实用新型',
                width: '250'
              }
            ]
          },
          {
            keyQuery: 'progress',
            keySave: 'progress',
            label: '进度',
            width: '250'
          }
        ],
        url: {
          list: patentAdmissibilityFindAllDate,
          save: patentAdmissibilitySaveAll
        }
      }
    }
  },
  watch: {
    selectDate: function() {
      this.getRemarkInfo()
    }
  },
  created() {
    this.getRemarkInfo()
  },
  methods: {
    async getRemarkInfo() {
      const params = {
        setTime: this.selectDate,
        type: 4 // 1:质量通报 2:非计划统计 3.成材率 4.专利受理情况
      }
      const res = await post(findAllDateRemark, params)
      this.monthlyReport = res.data.length ? res.data[0].remark : ''
    },
    async handleSave() {
      this.submitLoading = true
      const params = {
        setTime: this.selectDate,
        type: 4,
        data: {
          remark: this.monthlyReport
        }
      }
      const res = await post(saveAllRemark, params)
      if (res.status === 1) {
        this.$message.success('已提交')
      } else {
        this.$message.error(res.data)
      }
      this.submitLoading = false
    }
  }
}
</script>

<style scoped lang="less">
.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;

  .header-footer {
    width: 100%;
    margin-bottom: 20px;
    gap: 10px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-between;
    position: relative;
    // padding: 5px 8px;

    .submit-btn {
      width: 68px !important;
      height: 28px !important;
      padding: 0 !important;
      line-height: 26px !important;
      background: rgba(31, 198, 255, 0.3);
      border: 1px solid #1fc6ff;
      color: #fff;
      font-size: 14px;
      position: absolute;
      right: 10px;
      bottom: 10px;

      &:hover {
        background: rgba(31, 198, 255, 0.6);
      }
    }
    /deep/ .el-textarea__inner,
    /deep/ .el-input__inner {
      background: transparent;
      border: 1px solid rgba(31, 198, 255, 0.3);
      color: #fff;
      padding: 10px;
      padding-right: 80px;

      &:focus {
        border-color: #1fc6ff;
      }
    }
  }

  .chart-row {
    margin-bottom: 10px;
    height: 100%;
    flex-direction: column;
  }

  .chart-row,
  .table-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 10px;
    width: 100%;
  }

  .chart-box,
  .table-box {
    width: 100%;
    flex: 1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }
}
</style>
