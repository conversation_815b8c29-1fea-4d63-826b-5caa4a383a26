<template>
  <div class="itemlist-box">
    <div class="itemlist-left">
      <div class="itemlist-left-top">
        <el-row :gutter="20">
          <!--          weight: '', //班组实时产量        数据为空
          avgWeight: '', //班组实时机时产量               数据为空
          inHeatTime: '', //班组平均在炉时间              数据为空
          temperatureRiseFall: '', //班组升降温比         数据为空
          specificationConversion: '', //班组规格转换比   数据为空
          group: '', //班组
          shift: '', //班别
          utilization: '', //利用率                        6#数据为空
          burnerMalfunctions: '', //烧嘴故障数             6#数据为空
          heatTreatmentRatio: '', //有效作业率
          autoSteelLoadRatio: '', //自动装钢率            数据为空
          furnaceRollerCurrent: '', //炉辊电流超限数

          preWeight: '', //前一班组实时产量
          avgPreWeight: '', //前一班组实时机时产量
          preInHeatTime: '', //前一班组平均在炉时间
          preTemperatureRiseFall: '', //前一班组升降温比   无此字段
          preSpecificationConversion: '', //前一班组规格转换比 无此字段
          preGroup: '', //前一班组
          preShift: '', //前一班别
          preFacid: '', //前一炉号
          YoYWeight: '', //产量同比                         无此字段
          YoYAvgWeight: '', //机时产量同比                  无此字段
          YoYInHeatTime: '', //平均在炉时间同比             无此字段
          YoYTemperatureRiseFall: '', //班组升降温比        无此字段
          YoYSpecificationConversion: '' //班组规格转换比同比  无此字段  -->
          <el-col class="item">
            <div class="item-box">
              <div class="item-info">
                <ul>
                  <li>
                    <p class="p">产量</p>
                    <img
                      class="icon-small1"
                      src="../../../../assets/images/screen/icon-list/icon-small1.png"
                      alt=""
                      @click="openDialog('weightOutputDay_','产量')">
                  </li>
                  <li>
                    <span class="num">{{ allData.weight }}</span>
                    <i class="unit">吨</i>
                  </li>
                  <li>
                    <span class="rate">同比</span>
                    <i class="upordown down">{{ allData.YoYWeight }}</i>
                  </li>
                </ul>
              </div>
              <div class="item-icon">
                <img
                  src="../../../../assets/images/screen/icon-list/icon1.png"
                  alt="">
              </div>
            </div>
          </el-col>
          <el-col class="item">
            <div class="item-box">
              <div class="item-info">
                <ul>
                  <li>
                    <p class="p">机时产量</p>
                    <img
                      class="icon-small1"
                      src="../../../../assets/images/screen/icon-list/icon-small1.png"
                      alt=""
                      @click="openDialog('avgWeightOutputDay_','机时产量')">
                  </li>
                  <li>
                    <span class="num">{{ allData.avgWeight }}</span>
                    <i class="unit">吨/h</i>
                  </li>
                  <li>
                    <span class="rate">同比</span>
                    <i class="upordown up">{{ allData.YoYAvgWeight }}</i>
                  </li>
                </ul>
              </div>
              <div class="item-icon">
                <img
                  src="../../../../assets/images/screen/icon-list/icon2.png"
                  alt="">
              </div>
            </div>
          </el-col>
          <el-col class="item">
            <div class="item-box">
              <div class="item-info">
                <ul>
                  <li>
                    <p class="p">利用率</p>
                    <img
                      class="icon-small1"
                      src="../../../../assets/images/screen/icon-list/icon-small1.png"
                      alt=""
                      @click="openDialog('','利用率')">
                  </li>
                  <li>
                    <span class="num">{{ allData.utilization }}</span>
                    <i class="unit">%</i>
                  </li>
                  <li>
                    <span class="rate">同比</span>
                    <i class="upordown up">xxx%</i>
                  </li>
                </ul>
              </div>
              <div class="item-icon">
                <img
                  src="../../../../assets/images/screen/icon-list/icon3.png"
                  alt="">
              </div>
            </div>
          </el-col>
          <el-col class="item">
            <div class="item-box">
              <div class="item-info">
                <ul>
                  <li>
                    <p class="p">有效作业率</p>
                    <img
                      class="icon-small1"
                      src="../../../../assets/images/screen/icon-list/icon-small1.png"
                      alt=""
                      @click="openDialog('effectRatio','有效作业率')">
                  </li>
                  <li>
                    <span class="num">{{ allData.heatTreatmentRatio }}</span>
                    <i class="unit">%</i>
                  </li>
                  <li>
                    <span class="rate">同比</span>
                    <i class="upordown up">XXX%</i>
                  </li>
                </ul>
              </div>
              <div class="item-icon">
                <img
                  src="../../../../assets/images/screen/icon-list/icon3.png"
                  alt="">
              </div>
            </div>
          </el-col>
          <el-col class="item">
            <div class="item-box">
              <div class="item-info">
                <ul>
                  <!--                  <li>
                    <p class="p">自动装钢率</p>
                    <img
                      class="icon-small1"
                      src="../../../../assets/images/screen/icon-list/icon-small1.png"
                      alt="">
                  </li>-->
                  <li>
                    <p class="p">装钢节奏</p>
                    <img
                      class="icon-small1"
                      src="../../../../assets/images/screen/icon-list/icon-small1.png"
                      alt="">
                  </li>
                  <li>
                    <span class="num"> {{ allData.installationSpacing }}</span>
                    <i class="unit">%</i>
                  </li>
                  <li>
                    <span class="rate">同比</span>
                    <i class="upordown down">{{ allData.YoYInstallationSpacing }}%</i>
                  </li>
                </ul>
              </div>
              <div class="item-icon">
                <img
                  src="../../../../assets/images/screen/icon-list/icon3.png"
                  alt="">
              </div>
            </div>
          </el-col>
          <el-col class="item">
            <div class="item-box">
              <div class="item-info">
                <ul>
                  <li>
                    <p class="p">在炉时间</p>
                    <img
                      class="icon-small1"
                      src="../../../../assets/images/screen/icon-list/icon-small1.png"
                      alt=""
                      @click="openDialog('inHeatTimeDay_','升降温比')">
                  </li>
                  <li>
                    <span class="num"> {{ forTime(allData.inHeatTime) }} </span>
                    <i class="unit"/>
                  </li>
                  <li>
                    <span class="rate">同比</span>
                    <i class="upordown up">{{ allData.YoYInHeatTime }}%</i>
                  </li>
                </ul>
              </div>
              <div class="item-icon">
                <img
                  src="../../../../assets/images/screen/icon-list/icon3.png"
                  alt="">
              </div>
            </div>
          </el-col>
          <el-col class="item">
            <div class="item-box">
              <div class="item-info">
                <ul>
                  <li>
                    <p class="p">升降温比</p>
                    <img
                      class="icon-small1"
                      src="../../../../assets/images/screen/icon-list/icon-small1.png"
                      alt=""
                      @click="openDialog('temperatureRiseFallDay_','升降温比')">
                  </li>
                  <li>
                    <span class="num">{{ allData.temperatureRiseFall }}</span>
                    <i class="unit">%</i>
                  </li>
                  <li>
                    <span class="rate">同比</span>
                    <i class="upordown up">{{ allData.YoYTemperatureRiseFall }}%</i>
                  </li>
                </ul>
              </div>
              <div class="item-icon">
                <img
                  src="../../../../assets/images/screen/icon-list/icon4.png"
                  alt="">
              </div>
            </div>
          </el-col>
          <el-col class="item">
            <div class="item-box">
              <div class="item-info">
                <ul>
                  <li>
                    <p class="p">规格转换比</p>
                    <img
                      class="icon-small1"
                      src="../../../../assets/images/screen/icon-list/icon-small1.png"
                      alt=""
                      @click="openDialog('specificationConversionDay_','规格转换比')">
                  </li>
                  <li>
                    <span class="num">{{ allData.specificationConversion }}</span>
                    <i class="unit">%</i>
                  </li>
                  <li>
                    <span class="rate">同比</span>
                    <i class="upordown up">{{ allData.YoYSpecificationConversion }}%</i>
                  </li>
                </ul>
              </div>
              <div class="item-icon">
                <img
                  src="../../../../assets/images/screen/icon-list/icon3.png"
                  alt="">
              </div>
            </div>
          </el-col>
          <el-col class="item">
            <div class="item-box">
              <div class="item-info">
                <ul>
                  <li>
                    <p class="p">烧嘴故障数</p>
                    <img
                      class="icon-small1"
                      src="../../../../assets/images/screen/icon-list/icon-small1.png"
                      alt=""
                      @click="openDialog('burnerMalfunctions','烧嘴故障数')">
                  </li>
                  <li>
                    <span class="num">{{ allData.burnerMalfunctions }}</span>
                    <i class="unit">个</i>
                  </li>
                  <li>
                    <span class="rate">同比</span>
                    <i class="upordown up">XXX%</i>
                  </li>
                </ul>
              </div>
              <div class="item-icon">
                <img
                  src="../../../../assets/images/screen/icon-list/icon4.png"
                  alt="">
              </div>
            </div>
          </el-col>
          <el-col class="item">
            <div class="item-box">
              <div class="item-info">
                <ul>
                  <li>
                    <p class="p">炉辊电流超限数</p>
                    <img
                      class="icon-small1"
                      src="../../../../assets/images/screen/icon-list/icon-small1.png"
                      alt=""
                      @click="openDialog('furnaceRollerCurrent','炉辊电流超限数')">
                  </li>
                  <li>
                    <span class="num">{{ allData.furnaceRollerCurrent }}</span>
                    <i class="unit">个</i>
                  </li>
                  <li>
                    <span class="rate">同比</span>
                    <i class="upordown down">XXX%</i>
                  </li>
                </ul>
              </div>
              <div class="item-icon">
                <img
                  src="../../../../assets/images/screen/icon-list/icon5.png"
                  alt="">
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="itemlist-left-bottom">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="echart-item">
              <p class="echart-p">产量</p>
              <div class="echart-item-box">
                <div style="width: 1100px; height: 143px">
                  <heatEcharts
                    :_height="'100%'"
                    :x-axis="optionList[0].xAxis"
                    :y-axis="optionList[0].yAxis"
                    :legend="optionList[0].legend"
                    :tooltip="optionList[0].tooltip"
                    :series="optionList[0].series"
                    :grid="optionList[0].grid"
                  />
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div
              class="echart-item">
              <p class="echart-p">有效作业率</p>
              <div class="echart-item-box">
                <div style="width: 1100px; height: 143px">
                  <heatEcharts
                    :_height="'100%'"
                    :x-axis="optionList[1].xAxis"
                    :y-axis="optionList[1].yAxis"
                    :legend="optionList[1].legend"
                    :tooltip="optionList[1].tooltip"
                    :series="optionList[1].series"
                    :grid="optionList[1].grid"
                  />
                </div>
              </div>

            </div>
          </el-col>
          <el-col :span="8">
            <div class="echart-item">
              <!--              <p class="echart-p">性能合格率</p>-->
              <p class="echart-p">在炉时间</p>
              <div class="echart-item-box">
                <div style="width: 1100px; height: 143px">
                  <heatEcharts
                    :_height="'100%'"
                    :x-axis="optionList[2].xAxis"
                    :y-axis="optionList[2].yAxis"
                    :legend="optionList[2].legend"
                    :tooltip="optionList[2].tooltip"
                    :series="optionList[2].series"
                    :grid="optionList[2].grid"
                  />
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="itemlist-right">
      <div class="itemlist-right-top">
        <el-table
          :data="itemlistRightToptableData"
          :header-cell-style="{'text-align':'left'}"
          :cell-style="{'text-align':'left'}"
          height="290"
          style="width: 100%">
          <el-table-column
            prop="name"
            label="热处理月度指标"
            width=""/>
          <el-table-column
            prop="value"
            label=""
            width=""/>
            <!--          <el-table-column
          prop="address"
          label="">
          <template slot-scope="scope">
            <span style="color: rgba(31, 198, 255, 1)">查看</span>
          </template>
        </el-table-column>-->
        </el-table>
      </div>
      <div class="itemlist-right-bottom">
        <el-row :gutter="10">
          <el-col
            :span="6"
            class="itemlist-right-bottom-item">
            <div class="itemlist-right-bottom-item-box">
              <div class="itemlist-right-bottom-p">
                <p class="p">煤气总管压力</p>
                <!--                <img
                  class="icon-small1"
                  src="../../../../assets/images/screen/icon-list/icon-small1.png"
                  alt="">-->
              </div>
              <div class="itemlist-right-bottom-c">
                <span>{{ rightBottomData.gasP }}
                  <i v-if="currentCarousel == 0">kPa</i>
                  <i v-if="currentCarousel == 1">kPa</i>
                </span>
              </div>
            </div>
          </el-col>
          <el-col
            :span="6"
            class="itemlist-right-bottom-item">
            <div class="itemlist-right-bottom-item-box">
              <div class="itemlist-right-bottom-p">
                <p class="p">煤气总管流量</p>
                <!--                <img
                  class="icon-small1"
                  src="../../../../assets/images/screen/icon-list/icon-small1.png"
                  alt="">-->
              </div>
              <div class="itemlist-right-bottom-c">
                <span>{{ rightBottomData.gasF }}
                  <i v-if="currentCarousel == 0">m³/h</i>
                  <i v-if="currentCarousel == 1">Nm³/h</i>
                </span>
              </div>
            </div>

          </el-col>
          <el-col
            :span="6"
            class="itemlist-right-bottom-item">
            <div class="itemlist-right-bottom-item-box">
              <div class="itemlist-right-bottom-p">
                <p class="p">氮气总管压力</p>
                <!--                <img
                  class="icon-small1"
                  src="../../../../assets/images/screen/icon-list/icon-small1.png"
                  alt="">-->
              </div>
              <div class="itemlist-right-bottom-c">
                <span>{{ rightBottomData.nitrogenP }}
                  <i v-if="currentCarousel == 0">kPa</i>
                  <i v-if="currentCarousel == 1">MPa</i>
                </span>
              </div>
            </div>
          </el-col>
          <el-col
            :span="6"
            class="itemlist-right-bottom-item">
            <div class="itemlist-right-bottom-item-box">
              <div class="itemlist-right-bottom-p">
                <p class="p">炉体中区炉压</p>
                <!--                <img
                  class="icon-small1"
                  src="../../../../assets/images/screen/icon-list/icon-small1.png"
                  alt="">-->
              </div>
              <div class="itemlist-right-bottom-c">
                <span>{{ rightBottomData.allP }}
                  <i v-if="currentCarousel == 0">Pa</i>
                  <i v-if="currentCarousel == 1">MPa</i>
                </span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import heatEcharts from '@/pages/screen/heatMeeting/component/heatEcharts'
import { post } from '@/lib/Util'
import { ecAndCostC2 } from '@/api/screenEnergy'
import {
  findHeatTreatmentYieldByDate,
  findPerformancePassByDate
} from '@/api/screen'
import moment from 'moment'

export default {
  name: 'itemlist-box',
  components: {
    heatEcharts
  },
  props: {
    currentCarousel: {
      type: Number,
      default: 0 //0: 5#  1:6#
    },
    allData: {
      type: Object,
      default: () => {
        return {}
      } //0: 5#  1:6#
    },
    rightBottomData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data: () => {
    return {
      itemlistRightToptableData: [
        {
          name: '产量',
          value: 'XX 吨',
          btn: '查看'
        },
        {
          name: '能动成本',
          value: 'XX 元/吨',
          btn: '查看'
        },
        {
          name: '性能一次合格率',
          value: 'XX.xx %',
          btn: '查看'
        }
      ],
      optionList: [
        {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            confine: true
          },
          legend: {
            right: '2%',
            top: '3%'
          },
          grid: [
            {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            }
          ],
          xAxis: [
            {
              type: 'category',
              data: ['1', '2', '3', '4', '5', '6', '7', '8', '9'],
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dotted' //'dotted'虚线 'solid'实线
                }
              }
            }
          ],
          series: [
            {
              name: '甲',
              type: 'bar',
              data: [320, 332, 301, 334, 320, 332, 301, 334, 567],
              color: 'rgba(51, 145, 255, 1)'
            },
            {
              name: '乙',
              type: 'bar',
              data: [120, 132, 101, 134, 320, 332, 301, 334, 567],
              color: 'rgba(85, 198, 212, 1)'
            },
            {
              name: '丙',
              type: 'bar',
              data: [220, 182, 191, 234, 320, 332, 301, 334, 567],
              color: 'rgba(102, 204, 106, 1)'
            },
            {
              name: '丁',
              type: 'bar',
              data: [150, 232, 201, 154, 320, 332, 301, 334, 567],
              color: 'rgba(255, 218, 53, 1)'
            }
          ]
        },
        {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            confine: true
          },
          legend: {
            right: '2%',
            top: '3%'
          },
          grid: [
            {
              left: '1%',
              right: '1%',
              bottom: '3%',
              containLabel: true
            }
          ],
          xAxis: [
            {
              type: 'category',
              data: ['1', '2', '3', '4', '5', '6', '7', '8', '9'],
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dotted' //'dotted'虚线 'solid'实线
                }
              }
            }
          ],
          series: [
            {
              name: '甲',
              type: 'bar',
              data: [320, 332, 301, 334, 320, 332, 301, 334, 567],
              color: 'rgba(51, 145, 255, 1)'
            },
            {
              name: '乙',
              type: 'bar',
              data: [120, 132, 101, 134, 320, 332, 301, 334, 567],
              color: 'rgba(85, 198, 212, 1)'
            },
            {
              name: '丙',
              type: 'bar',
              data: [220, 182, 191, 234, 320, 332, 301, 334, 567],
              color: 'rgba(102, 204, 106, 1)'
            },
            {
              name: '丁',
              type: 'bar',
              data: [150, 232, 201, 154, 320, 332, 301, 334, 567],
              color: 'rgba(255, 218, 53, 1)'
            }
          ]
        },
        {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            confine: true
          },
          legend: {
            right: '2%',
            top: '3%'
          },
          grid: [
            {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            }
          ],
          xAxis: [
            {
              type: 'category',
              data: ['1', '2', '3', '4', '5', '6', '7', '8', '9'],
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dotted' //'dotted'虚线 'solid'实线
                }
              }
            }
          ],
          series: [
            {
              name: '甲',
              type: 'bar',
              data: [320, 332, 301, 334, 320, 332, 301, 334, 567],
              color: 'rgba(51, 145, 255, 1)'
            },
            {
              name: '乙',
              type: 'bar',
              data: [120, 132, 101, 134, 320, 332, 301, 334, 567],
              color: 'rgba(85, 198, 212, 1)'
            },
            {
              name: '丙',
              type: 'bar',
              data: [220, 182, 191, 234, 320, 332, 301, 334, 567],
              color: 'rgba(102, 204, 106, 1)'
            },
            {
              name: '丁',
              type: 'bar',
              data: [150, 232, 201, 154, 320, 332, 301, 334, 567],
              color: 'rgba(255, 218, 53, 1)'
            }
          ]
        }
      ]
    }
  },
  watch: {
    currentCarousel: {
      handler(newvalue, old) {
        console.log('jin watch')
        if (newvalue == 0) {
          //5#
          this.getLeftBottomDataFive()
        }
        if (newvalue == 1) {
          //6#
          this.getLeftBottomDataSix()
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.getTopRightData()
    this.getLeftBottomDataFive()
  },
  methods: {
    //产量: http://172.25.63.144:9720/api/mesAPI/HeatTreatmentYield/findHeatTreatmentYieldByDate
    //{"setDate":"2024-05-27"}
    //能动成本 接口地址  /ems//ec-and-cost/c2
    //{"flag":"1","date":"2024-05-27"}
    //性能一次合格率 api/mesAPI/PerformancePass/findPerformancePassByDate
    //{"setDate":"2024--0526"}
    //itemlistRightToptableData
    getTopRightData() {
      let toDay = moment().format('YYYY-MM-DD')
      let yesterDay = moment()
        .subtract(1, 'days')
        .format('YYYY-MM-DD')
      //产量
      post(findHeatTreatmentYieldByDate, {
        setDate: toDay
      }).then(res => {
        this.itemlistRightToptableData[0].value =
          res.data[4].cumulativeoutput + res.data[5].cumulativeoutput + ' 吨'
      })
      //能动成本
      post(ecAndCostC2, { flag: '1', date: toDay }).then(res => {
        if (res) {
          this.itemlistRightToptableData[1].value =
            res.data.cost[1].value + ' 元/吨'
        }
      })
      //性能一次合格率
      post(findPerformancePassByDate, { setDate: yesterDay }).then(res => {
        if (res) {
          this.itemlistRightToptableData[2].value = res.data[1].monthdata + ' %'
        }
      })
    },

    openDialog(params, name) {
      this.$emit('openDialog', params, name)
    },
    getLeftBottomDataFive() {
      const today = moment().format('YYYY-MM-DD HH:mm:ss')
      const thirtyDaysAgo = moment()
        .subtract(30, 'days')
        .format('YYYY-MM-DD HH:mm:ss')
      /*
      * effectRatio5 是5#炉的有效作业率 effectRatio6是6#有效作业率
      * */
      post('ht/HeatBasicData/findWeightByTimeandShiftInTime', {
        startTime: thirtyDaysAgo,
        endTime: today,
        name: 'effectRatio5'
      }).then(res => {
        if (res) {
          console.log('数据5')
          let xData = []
          let yData1 = []
          let yData2 = []
          let yData3 = []
          let yData4 = []
          for (let key in res.data) {
            xData.push(key)
            yData1.push(res.data[key][0])
            yData2.push(res.data[key][1])
            yData3.push(res.data[key][2])
            yData4.push(res.data[key][3])
          }
          this.optionList[1].xAxis[0].data = xData
          this.optionList[1].series[0].data = yData1
          this.optionList[1].series[1].data = yData2
          this.optionList[1].series[2].data = yData3
          this.optionList[1].series[3].data = yData4
        }
      })

      //产量
      post('ht/HeatBasicData/findWeightByTimeandShiftInTime', {
        startTime: thirtyDaysAgo,
        endTime: today,
        name: 'groupWegit_5'
      }).then(res => {
        if (res) {
          let xData = []
          let yData1 = []
          let yData2 = []
          let yData3 = []
          let yData4 = []
          for (let key in res.data) {
            xData.push(key)
            yData1.push(res.data[key][0])
            yData2.push(res.data[key][1])
            yData3.push(res.data[key][2])
            yData4.push(res.data[key][3])
          }
          cosole.log('aaa', xData)
          this.optionList[0].xAxis[0].data = xData
          this.optionList[0].series[0].data = yData1
          this.optionList[0].series[1].data = yData2
          this.optionList[0].series[2].data = yData3
          this.optionList[0].series[3].data = yData4
        }
      })
    },
    forTime(seconds) {
      // 计算小时
      const h = Math.floor(seconds / 3600)
      // 计算剩余的分钟
      const m = Math.floor((seconds % 3600) / 60)
      // 计算剩余的秒数
      const s = seconds % 60

      // 格式化为两位数的字符串
      const hh = String(h).padStart(2, '0')
      const mm = String(m).padStart(2, '0')
      const ss = String(s).padStart(2, '0')

      // 拼接为 "HH:mm:ss" 格式
      return `${hh}:${mm}:${ss}`
    },
    getLeftBottomDataSix() {
      const today = moment().format('YYYY-MM-DD HH:mm:ss')
      const thirtyDaysAgo = moment()
        .subtract(30, 'days')
        .format('YYYY-MM-DD HH:mm:ss')
      /*
      * effectRatio5 是5#炉的有效作业率 effectRatio6是6#有效作业率
      * */
      post('ht/HeatBasicData/findWeightByTimeandShiftInTime', {
        startTime: thirtyDaysAgo,
        endTime: today,
        name: 'effectRatio6'
      }).then(res => {
        if (res) {
          console.log('数据6')
          let xData = []
          let yData1 = []
          let yData2 = []
          let yData3 = []
          let yData4 = []
          for (let key in res.data) {
            xData.push(key)
            yData1.push(res.data[key][0])
            yData2.push(res.data[key][1])
            yData3.push(res.data[key][2])
            yData4.push(res.data[key][3])
          }
          this.optionList[1].xAxis[0].data = xData
          this.optionList[1].series[0].data = yData1
          this.optionList[1].series[1].data = yData2
          this.optionList[1].series[2].data = yData3
          this.optionList[1].series[3].data = yData4
        }
      })
      //产量
      post('ht/HeatBasicData/findWeightByTimeandShiftInTime', {
        startTime: thirtyDaysAgo,
        endTime: today,
        name: 'groupWegit_6'
      }).then(res => {
        if (res) {
          console.log('数据6')
          let xData = []
          let yData1 = []
          let yData2 = []
          let yData3 = []
          let yData4 = []
          for (let key in res.data) {
            xData.push(key)
            yData1.push(res.data[key][0])
            yData2.push(res.data[key][1])
            yData3.push(res.data[key][2])
            yData4.push(res.data[key][3])
          }
          this.optionList[0].xAxis[0].data = xData
          this.optionList[0].series[0].data = yData1
          this.optionList[0].series[1].data = yData2
          this.optionList[0].series[2].data = yData3
          this.optionList[0].series[3].data = yData4
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.itemlist-box {
  width: 100%;
  display: flex;

  .itemlist-left {
    //flex-grow: 5;
    width: 70%;
    margin: 8px;

    .itemlist-left-top {
      .item {
        width: 20%;
        color: #ffffff;
        margin-bottom: 14px;

        .item-box {
          width: 100%;
          background: url('../../../../assets/images/screen/icon-list/itembg.png');
          background-size: 100% 100%;
          height: 111px;
          display: flex;
          padding: 16px;

          .item-info {
            flex-grow: 3;

            > ul {
              > li {
                display: flex;
                margin-bottom: 6px;

                .p {
                  display: inline-block;
                  font-size: 16px;
                  margin-right: 12px;
                }

                .icon-small1 {
                  cursor: pointer;
                }

                .num {
                  font-size: 28px;
                  color: rgba(31, 198, 255, 1);
                }

                .unit {
                  padding-top: 8px;
                  margin-left: 6px;
                }

                .rate {
                  font-size: 14px;
                }

                .upordown {
                  color: rgba(25, 190, 107, 1);
                  border: 1px solid rgba(25, 190, 107, 1);
                  border-radius: 5px;
                  padding: 1px 3px;
                  vertical-align: center;
                  margin-left: 6px;
                  font-size: 14px;
                }

                .up {
                  color: rgba(25, 190, 107, 1);
                  border: 1px solid rgba(25, 190, 107, 1);
                }

                .down {
                  color: rgba(255, 40, 85, 1);
                  border: 1px solid rgba(255, 40, 85, 1);
                }
              }
            }
          }

          .item-icon {
            flex-grow: 1;
          }
        }
      }
    }

    .itemlist-left-bottom {
      .echart-item {
        background: url('../../../../assets/images/screen/icon-list/echartbg.png');
        background-size: 100% 100%;
        height: 143px;
        position: relative;
        /*overflow-x: scroll;
        overflow-y: hidden;*/
        .echart-p {
          position: absolute;
          top: 7%;
          left: 4%;
          color: #ffffff;
          font-size: 16px;
        }
        .echart-item-box {
          width: 100%;
          height: 100%;
          overflow-x: scroll;
          overflow-y: hidden;
        }
      }
    }
  }

  .itemlist-right {
    margin: 8px;
    //flex-grow: 2;
    width: 30%;

    .itemlist-right-top {
      background: url('../../../../assets/images/screen/icon-list/echartbg.png');
      background-size: 100% 100%;
      padding: 14px;
      overflow-y: scroll;

      /deep/ .el-table td.el-table__cell,
      /deep/ .el-table th.el-table__cell.is-leaf {
        border: none !important;
        padding: 5px 0;
      }
    }

    .itemlist-right-bottom {
      margin-top: 14px;

      .itemlist-right-bottom-item {
        color: #fff;

        .itemlist-right-bottom-item-box {
          border: 1px solid rgba(31, 198, 255, 0.5);

          .itemlist-right-bottom-p {
            background-color: rgba(31, 198, 255, 0.5);
            padding: 4px;

            > p {
              display: inline-block;
            }

            > img {
              float: right;
            }
          }

          .itemlist-right-bottom-c {
            height: 28px;
            padding: 3px 4px;

            > span {
              vertical-align: sub;
            }
          }
        }
      }
    }
  }
}
</style>
