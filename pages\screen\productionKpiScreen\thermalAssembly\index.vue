<template>
  <div class="content">
    <iframe 
      src="https://ngsjfx.nisco.cn/webroot/decision/link/88fi" 
      style="height:100%"
      frameborder="0"/>
  </div>
</template>

<script>
import { math } from '@/lib/Math'
import moment from 'moment'
import lodash from 'lodash'
export default {
  name: 'Output',
  data: () => {
    return {}
  }
}
</script>
<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 20px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .slick {
    height: 65px;
    position: relative;
    //top: -15px;
    margin-bottom: 15px;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    width: 90px;
    line-height: 24px;
    font-size: 16px;
    white-space: nowrap;
    color: #ffffff;
  }
  span:last-child {
    flex: 1;
    overflow: auto;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    font-size: 0;
    margin-right: 10px;
    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.green {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.scroll-wrapper {
  height: 100%;
}
.kpi-list {
  font-size: 0;
  .item {
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12.5%;
    img {
      width: 48px;
      margin-right: 10px;
    }
    .name {
      font-size: 18px;
      font-weight: 700;
      line-height: 18px;
      margin-bottom: 10px;
      letter-spacing: 0px;
      text-align: left;
    }
    .num {
      font-size: 32px;
      font-weight: 700;
      line-height: 32px;
      letter-spacing: 0px;
      text-align: left;
    }
    .unit {
      font-size: 20px;
      font-weight: 350;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
    }
  }
}
</style>
