<template>
  <div class="full-height">
    <screen-border
      :title="title"
      :content-class="contentClass">
      <template v-slot:headerRight>
        <template v-if="steelmakingShow">
          <slot name="topRight"/>
        </template>
        <template v-else>
          <!-- <span
            class="screen-btn"
            @click="exportTable">
            导出
          </span> -->
          <button
            v-if="currentTime"
            class="screen-btn"
            @click="getData()">
            <el-icon class="el-icon-search"/>
            查找
          </button>
          <el-popconfirm
            v-else
            title="该时间段数据不稳定，请确认是否查找"
            @confirm="confirmSearch()"
            @cancel="cancelDelete()"
          >
            <el-button
              slot="reference"
              class="screen-btn">查找</el-button>
          </el-popconfirm>
          <!-- <button
            class="screen-btn"
            @click="getData()">
            <el-icon class="el-icon-search"/>
            查找
          </button> -->
          <!-- <el-button
            class="screen-btn"
            type="text"
          >查找</el-button>
          <el-popconfirm
            title="确定要删除这项内容吗？"
            @confirm="confirmSearch()"
            @cancel="cancelDelete()"/> -->

        </template>
        <el-select
          v-model="steelType"
          style="width:200px;margin-left: 5px;"
          filterable
          clearable
          multiple
          collapse-tags
          placeholder="请选择钢种">
          <el-option
            v-for="item in steelTypeAll"
            :key="item"
            :label="item"
            :value="item"/>
        </el-select>
        <!--        <el-input
          v-model="steelType"
          style="width:140px;margin-left: 5px;"
          placeholder="请输入钢种"
        />-->

        <el-date-picker
          v-model="cDate2"
          :clearable="false"
          :size="'small'"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="margin-left: 5px;background-color:#093d4d;color:red"

          @input="$forceUpdate()"/>
          <!-- <template v-else>
          <span
            v-command="'/screen/productionKpiScreen/edit'"
            class="screen-btn"
            @click="dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            操作
          </span>
        </template> -->
      </template>
      <slot name="content"/>
      <div
        v-if="showTable"
        ref="table1"
        class="scroll-wrapper">
        <el-table
          v-loading="loading"
          :data="showGridData"
          :max-height="maxHeight"
          :span-method="spanMethod"
          class="font-table center-table"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.children"
                :key="index"
                :width="item.width || '200px'"
                :property="item.keySave"
                :label="item.label"
                :align="item.align">
                <template
                  v-for="(cItem, cIndex) in item.children">
                  <template v-if="cItem.showChart === true">
                    <el-table-column
                      :key="cIndex"
                      :width="cItem.width || '200px'"
                      :property="cItem.keySave"
                      :label="cItem.label"
                      :align="cItem.align">
                      <template v-slot="{ row, $index }">
                        <el-button
                          type="text"
                          size="small"
                          style="color: #1ec2fa;font-size: 16px;"
                          @click="showChartDialog(row,cItem.keySave,$index)">

                          {{ row[cItem.keySave] }}
                        </el-button>
                        <!-- <div
                          slot="content"
                          v-html="formatText(row[cItem.keySave], cItem.split)"
                        /> -->
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      :key="cIndex"
                      :width="cItem.width || '200px'"
                      :property="cItem.keyQuery"
                      :label="cItem.label"
                      :align="cItem.align"/>
                  </template>
                </template>
              </el-table-column>
              <template v-else>
                <el-table-column
                  v-if="item.type === 'index'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                />
                <template v-else>
                  <template v-if="item.inputType === 'textarea'">
                    <el-table-column
                      :key="index"
                      :width="item.width || '200px'"
                      :property="item.keySave"
                      :label="item.label"
                      :align="item.align">
                      <template v-slot="{ row }">
                        <div
                          slot="content"
                          v-html="formatText(row[item.keySave], item.split)"
                        />
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      :key="index"
                      :width="item.width || '200px'"
                      :property="item.keySave"
                      :label="item.label"
                      :align="item.align"/>
                  </template>
                </template>
              </template>
            </template>
          </template>
        </el-table>
        <!-- <el-pagination
          :total="pagination.total"
          :pager-count="5"
          :page-size="pagination.pageSize"
          :current-page="pagination.pageIndex"
          :page-sizes="pagination.pageSizes"
          :align="pagination.align"
          background
          class="foot"
          layout="total,prev, pager, next,sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/> -->
      </div>
    </screen-border>
    <el-dialog
      :visible.sync="dialogVisible2"
      class="screen-dialog"
      title="判废影响分析">
      <template v-slot:title>
        <div class="custom-dialog-title">
          {{ titleDitail }}
        </div>
      </template>
      <div
        id="chart"
        style="width: 100%; height: 400px;"/>
      <span
        slot="footer"
        class="dialog-footer">
        <el-button @click="dialogVisible2 = false">取消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span> -->
            <!-- <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/> -->
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event)">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <!-- <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span> -->
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportTable">
              导出
            </span>
            <!--<span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span> -->
          </div>
          {{ popTitle || title }}
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="gridData"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.type === 'index'"
                :key="index"
                :label="item.label"
                :align="item.align"
                type="index"
                width="100"
              />
              <template v-else>
                <el-table-column
                  :key="index"
                  :width="item.width || '200px'"
                  :property="item.keySave"
                  :align="item.align"
                  :label="item.label">
                  <template v-slot="{ row }">
                    <template v-if="item.inputType === 'textarea'">
                      <el-input
                        v-model="row[item.keySave]"
                        :disabled="item.disabled"
                        :rows="4"
                        type="textarea"
                      />
                    </template>
                    <template v-else>
                      <el-input
                        v-model="row[item.keySave]"
                        :disabled="item.disabled"/>
                    </template>
                  </template>
                </el-table-column>
              </template>
            </template>
          </template>
          <!-- <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index)">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column> -->
        </el-table>
      </el-form>
      <!-- <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData()">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div> -->
    </el-dialog>
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="secondTablevisible"
      class="screen-dialog"
      width="80%">
      <template v-slot:title>
        <div class="custom-dialog-title">
          {{ secondTitle }}明细
        </div>
      </template>
      <div
        ref="table1"
        class="scroll-wrapper">
        <el-table
          v-loading="loading"
          :data="secondTableData"
          class="font-table center-table"
          border>
          <el-table-column
            type="index"
            label="序号 "
            width="100px"
          />
          <el-table-column
            property="HEATNO"
            label="炉号"
            align="left"
            width="200px"
            class-name="big-font"/>
          <el-table-column
            property="STLGRD"
            label="坯料钢种"
            width="200px"
          />
          <el-table-column
            property="SLABNO"
            label="子坯料号"
            width="200px"
          />
          <el-table-column
            property="STLGRDORDDETAIL"
            align="left"
            width="200px"
            label="坯料原钢种" />
          <el-table-column
            property="ORDNO"
            align="left"
            width="200px"
            label="订单号" />
          <el-table-column
            property="OVERFL"
            align="left"
            width="200px"
            label="坯料种类" />
          <el-table-column
            property="WGT"
            align="left"
            width="200px"
            label="子坯料重量" />
          <el-table-column
            property="PRODDATE"
            align="left"
            width="200px"
            label="母坯生产时间" />
          <el-table-column
            property="OUTPLT"
            align="left"
            width="200px"
            label="流向" />
          <el-table-column
            property="ESTCOMMENT"
            align="left"
            width="400px"
            label="坯料判废原因" />
          <el-table-column
            property="THK"
            align="left"
            width="200px"
            label="入炉坯料厚度" />
          <el-table-column
            property="STLGRDFLAG"
            align="left"
            width="200px"
            label="是否替代轧制" />
          <el-table-column
            property="CADMANANO"
            align="left"
            width="400px"
            label="原因代码" />
          <el-table-column
            property="CADMANANO1"
            align="left"
            width="200px"
            label="CAD代码" />
          <el-table-column
            property="CADCOMMENT"
            align="left"
            width="300px"
            label="CAD锁定详称" />
          <el-table-column
            property="ESTCD"
            align="left"
            width="200px"
            label="处理" />
          <el-table-column
            property="CDSHORTNAME"
            align="left"
            width="400px"
            label="处理名称" />
          <el-table-column
            property="ESTCOMMENT1"
            align="left"
            width="400px"
            label="处理详细内容" />
        </el-table>
      </div>

    </el-dialog>
    <!-- <el-dialog
      :visible.sync="secondTablevisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <span>1111111111</span>
    </el-dialog> -->
  </div>
</template>

<script>
import moment from 'moment'
import * as _ from 'lodash'
import * as echarts from 'echarts'
import { post } from '@/lib/Util'
import {
  qmsQualityQuery,
  qmsQualitySave,
  SlabPanFei,
  findDetailsDateNoPage
} from '@/api/screen'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import CustomTable9 from '@/pages/screen/overallYieldrate/component/custom-table9'

export default {
  name: 'custom-table',
  components: { ScreenBorder, CustomTable9 },
  props: {
    title: {
      type: String,
      default: ''
    },
    flag: {
      type: String,
      default: ''
    },
    popTitle: {
      type: String,
      default: ''
    },
    setting: {
      type: Array,
      default: function() {
        return []
      }
    },
    selectDate: {
      type: String,
      default: ''
    },
    urlList: {
      type: String,
      default: ''
    },
    urlSave: {
      type: String,
      default: ''
    },
    urlSteel: {
      type: String,
      default: ''
    },
    showTable: {
      type: Boolean,
      default: true
    },
    heightAuto: {
      type: Boolean,
      default: true
    },
    heightSet: {
      type: Number,
      default: 0
    },
    steelmakingShow: {
      type: Boolean,
      default: false
    },
    contentClass: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: function() {
        return {}
      }
    },
    color: {
      type: Array,
      default: () => {
        return [
          '#0C75FF',
          '#FF7D00',
          '#00B42A',
          '#91cc75',
          '#fac858',
          '#ee6666',
          '#73c0de',
          '#3ba272',
          '#fc8452',
          '#9a60b4',
          '#ea7ccc'
        ]
      }
    }
  },
  data: function() {
    return {
      cDate: '',
      chartData: [],
      maxHeight: 800,
      cDate2: [
        moment()
          .subtract(1, 'months')
          .format('YYYY-MM-DD'),
        moment(new Date().getTime()).format('YYYY-MM-DD')
      ],
      loading: false,
      titleDitail: '',
      showPopconfirm: false,
      currentTime: false,
      dialogVisible: false,
      dialogVisible2: false,
      chartInstance: null,
      secondTablevisible: false,
      secondTableData: [],
      selectedData: null,
      showGridData: [],
      gridData: [],
      importDate: null,
      importDateVisible: false,
      importFunName: '',
      mergeArr: [],
      spanArr: {},
      position: 0,
      steelType: [],
      steelType2: '',
      rowData: {},
      secondTitle: '',
      steelTypeAll: '',
      pagination: {
        align: 'right',
        pageSizes: [500, 1000, 2000, 3000],
        pageSize: 500,
        pageIndex: 1,
        total: 0
      },
      ABC: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'K'
      ]
    }
  },
  computed: {
    canEditQuality: function() {
      // console.log(moment().format('yyyy-MM-DD'), this.cDate)
      return (
        moment().format('yyyy-MM-DD') <=
        moment(this.cDate)
          .subtract(-1, 'day')
          .format('yyyy-MM-DD')
      )
    }
  },
  watch: {
    // selectDate: function() {
    //   this.cDate = this.selectDate
    // },
    flag: function() {
      // 初始化数据
      this.getData()
    },
    heightSet: function() {
      this.maxHeight = this.heightSet ? this.heightSet : null
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    // this.cDate = this.selectDate
    this.getData()
    this.getSteelData()
  },
  mounted() {
    this.calculate()
    this.checkAnyTimeRange()
    this.$nextTick(() => {
      // 确保 DOM 已经更新
      this.dialogVisible2 = false // 初始状态设为不可见，仅作为示例
      // 你可以在这里安全地初始化图表，但通常你会在 showChartDialog 方法中做这件事
    })
    window.addEventListener('resize', this.calculate)
  },
  beforeDestroy() {
    // 在组件销毁前销毁图表实例，以避免内存泄漏
    if (this.chartInstance) {
      this.chartInstance.dispose()
    }
  },
  methods: {
    queryTableData(val) {
      this.pagination.pageIndex = val
      this.queryTableDataAll()
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // if (!Object.keys(this.mergeSet).length) return [1, 1]
      // if (this.mergeSet[rowIndex + '-' + columnIndex] !== undefined) {
      //   return !this.mergeSet[rowIndex + '-' + columnIndex]
      //     ? [0, 0]
      //     : this.mergeSet[rowIndex + '-' + columnIndex]
      // }
      // return [1, 1]
      if (columnIndex === 0 || columnIndex === 1) {
        const currentValue = row[column.property]
        const preRow = this.showGridData[rowIndex - 1]
        //上一行这一列的数据
        const preValue = preRow ? preRow[column.property] : null
        // 如果当前值和上一行的值相同，则将当前单元格隐藏
        if (currentValue === preValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let rowspan = 1
          // 计算应该合并的行数
          for (let i = rowIndex + 1; i < this.showGridData.length; i++) {
            const nextRow = this.showGridData[i]
            const nextValue = nextRow[column.property]
            if (nextValue === currentValue) {
              rowspan++
            } else {
              break
            }
          }
          return { rowspan, colspan: 1 }
        }
      }
    },
    confirmSearch() {
      // 用户点击确认按钮时执行的逻辑
      this.getData()
    },
    cancelDelete() {
      console.log('删除操作已取消')
    },
    // 导入文件
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = this.ABC[index]
      })
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, obj)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.gridData = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    // 导出表格
    exportTable() {
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = item.label
      })
      const data = [obj].concat(
        _.cloneDeep(
          this.gridData.map(item => {
            const objRow = {}
            this.setting.forEach(set => {
              objRow[set.keySave] = item[set.keySave]
            })
            return objRow
          })
        )
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `${this.title}（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    getSteelData() {
      post(this.urlSteel, {
        startDate: this.cDate2[0],
        endDate: this.cDate2[1]
      }).then(res => {
        let obj = []
        for (const item of res.data) {
          for (let key in item) {
            obj.push(key)
          }
        }
        this.steelTypeAll = obj
        //console.log(this.steelTypeAll, 'this.steelTypeAll')
      })
    },
    isCurrentTimeInAnyRange(timeRanges) {
      // 获取当前时间
      const currentTime = moment()

      // 遍历所有时间范围
      for (let range of timeRanges) {
        const start = moment(range.start)
        const end = moment(range.end)

        // 判断当前时间是否在任何一个范围内（包括边界）
        if (currentTime.isBetween(start, end, null, '[]')) {
          return true // 如果在范围内，立即返回 true
        }
      }

      // 如果遍历完所有范围都不在内，则返回 false
      return false
    },
    checkAnyTimeRange() {
      // 定义多个时间范围
      const timeRanges = [
        {
          start: moment()
            .hour(0)
            .minute(50)
            .second(0)
            .millisecond(0),
          end: moment()
            .hour(2)
            .minute(10)
            .second(0)
            .millisecond(0)
            .subtract(1, 'second')
        },
        {
          start: moment()
            .hour(8)
            .minute(30)
            .second(0)
            .millisecond(0),
          end: moment()
            .hour(10)
            .minute(10)
            .second(0)
            .millisecond(0)
            .subtract(1, 'second')
        },
        {
          start: moment()
            .hour(16)
            .minute(50)
            .second(0)
            .millisecond(0),
          end: moment()
            .hour(18)
            .minute(10)
            .second(0)
            .millisecond(0)
            .subtract(1, 'second')
        }
        // {
        //   start: moment()
        //     .hour(12)
        //     .minute(0)
        //     .second(0)
        //     .millisecond(0),
        //   end: moment()
        //     .hour(13)
        //     .minute(10)
        //     .second(0)
        //     .millisecond(0)
        //     .subtract(1, 'second')
        // }
        // ... 可以添加更多时间范围
      ]

      const isInAnyRange = this.isCurrentTimeInAnyRange(timeRanges)

      if (isInAnyRange) {
        console.log('当前时间在某个指定范围内')
        this.currentTime = false
      } else {
        console.log('当前时间不在任何指定范围内')
        this.currentTime = true
      }
    },

    // 获取数据
    getData() {
      this.loading = true
      post(
        this.urlList,
        Object.assign(
          {
            startDate: this.cDate2[0],
            endDate: this.cDate2[1],
            steelType: this.steelType
            // flag: this.flag
            // pageIndex: this.pagination.pageIndex,
            // pageSize: this.pagination.pageSize
          },
          this.params
        )
      ).then(res => {
        // this.pagination.total = res.data.totalElements
        this.loading = false
        this.showGridData = res.data.map(item => {
          const obj = {}
          this.setting.forEach(set => {
            if (set.children && set.children.length) {
              set.children.forEach(child => {
                obj[child.keySave] = item[child.keyQuery]
              })
            } else {
              obj[set.keySave] = item[set.keyQuery]
            }
          })
          return obj
        })
        this.gridData = _.cloneDeep(this.showGridData)
        this.$nextTick(() => {
          this.$emit('change', this.showGridData)
        })
      })
    },
    handleCurrentChange(newPage) {
      this.pagination.pageIndex = newPage
      this.getData()
    },
    handleSizeChange(newSize) {
      this.pagination.pageSize = newSize
      this.getData()
    },
    handleDateChange(value) {
      this.getData()
    },
    // 更新数据
    saveData() {
      this.loading = true
      // 数据信息
      const params = {
        setDate: this.cDate,
        data: this.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(this.urlSave, params).then(res => {
        //
        this.loading = false
        if (res.status == 1) {
          this.$message.success('保存成功！')
          this.dialogVisible = false
          this.getData()
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    // 导入日期数据
    importData(date) {
      post(this.urlList, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.gridData = res.data.content.map(item => {
          const obj = {}
          this.setting.forEach(set => {
            obj[set.keySave] = item[set.keyQuery]
          })
          return obj
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 执行导入
    importHistoryData() {
      this.importData(this.importDate)
      this.importDateVisible = false
    },
    // 下拉菜单指令
    handleProcessedCommand(command) {
      if (command === 'yesterday') {
        this.importData(
          this.$moment(this.cDate)
            .subtract(1, 'day')
            .format('yyyy-MM-DD')
        )
      } else {
        this.importDate = this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
        this.importDateVisible = true
      }
    },
    // 数据管理
    clearGridData() {
      this.gridData = []
    },
    addGridData() {
      this.gridData.push({})
    },
    delGridData(index) {
      this.gridData.splice(index, 1)
    },
    // 日期改变推送
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    // 计算需要合并的单元格
    formatSpanData(data) {
      this.mergeArr.forEach(keyName => {
        this.spanArr[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
    },
    // 生成带换行数据
    formatText(text, split) {
      if (!text) {
        return ''
      }
      if (split) text = text.split(split).join('\n')
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },
    // 计算高度
    calculate() {
      this.showTable &&
        this.heightAuto &&
        (this.maxHeight = this.$refs.table1.offsetHeight)
    },
    getSlabPanFei(slabFlagParm) {
      this.chartData = []
      post(SlabPanFei, {
        startDate: this.cDate2[0],
        endDate: this.cDate2[1],
        steelType: this.steelType2,
        slabFlag: slabFlagParm
      }).then(res => {
        switch (res.data.length) {
          case 1:
            this.chartData = [
              {
                value: res.data[0].slabWgt,
                name: res.data[0].slabType,
                percent: res.data[0].slabRatio
              }
            ]
            break
          case 2:
            this.chartData = [
              {
                value: res.data[0].slabWgt,
                name: res.data[0].slabType,
                percent: res.data[0].slabRatio
              },
              {
                value: res.data[1].slabWgt,
                name: res.data[1].slabType,
                percent: res.data[1].slabRatio
              }
            ]
            break
          case 3:
            this.chartData = [
              {
                value: res.data[0].slabWgt,
                name: res.data[0].slabType,
                percent: res.data[0].slabRatio
              },
              {
                value: res.data[1].slabWgt,
                name: res.data[1].slabType,
                percent: res.data[1].slabRatio
              },
              {
                value: res.data[2].slabWgt,
                name: res.data[2].slabType,
                percent: res.data[2].slabRatio
              }
            ]
            break
          case 4:
            this.chartData = [
              {
                value: res.data[0].slabWgt,
                name: res.data[0].slabType,
                percent: res.data[0].slabRatio
              },
              {
                value: res.data[1].slabWgt,
                name: res.data[1].slabType,
                percent: res.data[1].slabRatio
              },
              {
                value: res.data[2].slabWgt,
                name: res.data[2].slabType,
                percent: res.data[2].slabRatio
              },
              {
                value: res.data[3].slabWgt,
                name: res.data[3].slabType,
                percent: res.data[3].slabRatio
              }
            ]
            break
          case 5:
            this.chartData = [
              {
                value: res.data[0].slabWgt,
                name: res.data[0].slabType,
                percent: res.data[0].slabRatio
              },
              {
                value: res.data[1].slabWgt,
                name: res.data[1].slabType,
                percent: res.data[1].slabRatio
              },
              {
                value: res.data[2].slabWgt,
                name: res.data[2].slabType,
                percent: res.data[2].slabRatio
              },
              {
                value: res.data[3].slabWgt,
                name: res.data[3].slabType,
                percent: res.data[3].slabRatio
              },
              {
                value: res.data[4].slabWgt,
                name: res.data[4].slabType,
                percent: res.data[4].slabRatio
              }
            ]
            break
          case 6:
            this.chartData = [
              {
                value: res.data[0].slabWgt,
                name: res.data[0].slabType,
                percent: res.data[0].slabRatio
              },
              {
                value: res.data[1].slabWgt,
                name: res.data[1].slabType,
                percent: res.data[1].slabRatio
              },
              {
                value: res.data[2].slabWgt,
                name: res.data[2].slabType,
                percent: res.data[2].slabRatio
              },
              {
                value: res.data[3].slabWgt,
                name: res.data[3].slabType,
                percent: res.data[3].slabRatio
              },
              {
                value: res.data[4].slabWgt,
                name: res.data[4].slabType,
                percent: res.data[4].slabRatio
              },
              {
                value: res.data[5].slabWgt,
                name: res.data[5].slabType,
                percent: res.data[5].slabRatio
              }
            ]
            break
          case 7:
            this.chartData = [
              {
                value: res.data[0].slabWgt,
                name: res.data[0].slabType,
                percent: res.data[0].slabRatio
              },
              {
                value: res.data[1].slabWgt,
                name: res.data[1].slabType,
                percent: res.data[1].slabRatio
              },
              {
                value: res.data[2].slabWgt,
                name: res.data[2].slabType,
                percent: res.data[2].slabRatio
              },
              {
                value: res.data[3].slabWgt,
                name: res.data[3].slabType,
                percent: res.data[3].slabRatio
              },
              {
                value: res.data[4].slabWgt,
                name: res.data[4].slabType,
                percent: res.data[4].slabRatio
              },
              {
                value: res.data[5].slabWgt,
                name: res.data[5].slabType,
                percent: res.data[5].slabRatio
              },
              {
                value: res.data[6].slabWgt,
                name: res.data[6].slabType,
                percent: res.data[6].slabRatio
              }
            ]
            break
          case 8:
            this.chartData = [
              {
                value: res.data[0].slabWgt,
                name: res.data[0].slabType,
                percent: res.data[0].slabRatio
              },
              {
                value: res.data[1].slabWgt,
                name: res.data[1].slabType,
                percent: res.data[1].slabRatio
              },
              {
                value: res.data[2].slabWgt,
                name: res.data[2].slabType,
                percent: res.data[2].slabRatio
              },
              {
                value: res.data[3].slabWgt,
                name: res.data[3].slabType,
                percent: res.data[3].slabRatio
              },
              {
                value: res.data[4].slabWgt,
                name: res.data[4].slabType,
                percent: res.data[4].slabRatio
              },
              {
                value: res.data[5].slabWgt,
                name: res.data[5].slabType,
                percent: res.data[5].slabRatio
              },
              {
                value: res.data[6].slabWgt,
                name: res.data[6].slabType,
                percent: res.data[6].slabRatio
              },
              {
                value: res.data[7].slabWgt,
                name: res.data[7].slabType,
                percent: res.data[7].slabRatio
              }
            ]
            break
        }
        this.$nextTick(() => {
          // 确保对话框已经打开并且 DOM 已经更新
          this.initChart(this.chartData)
        })
      })
    },
    showChartDialog(row, $index) {
      this.dialogVisible2 = true
      this.rowData = row
      this.steelType2 = row.plgz
      if ($index == 'fpzl' || $index == 'fpl') {
        this.chartData = []
        this.titleDitail = '坯料判废影响' + row.plgz
        this.rowData.data1 = row.fpzl
        this.rowData.data2 = row.fpl
        this.rowData.data11 = '判废量'
        this.rowData.data12 = '判废率'

        this.getSlabPanFei('1')
      } else if ($index == 'tdzl' || $index == 'tdl') {
        this.chartData = []
        this.titleDitail = '坯料替代影响' + row.plgz
        this.rowData.data1 = row.tdzl
        this.rowData.data2 = row.tdl
        this.rowData.data11 = '替代量'
        this.rowData.data12 = '替代率'
        this.getSlabPanFei('2')
      } else if ($index == 'kcpzl' || $index == 'kcpbl') {
        this.chartData = []
        this.titleDitail = '库存坯' + row.plgz
        this.rowData.data1 = row.kcpzl
        this.rowData.data2 = row.kcpbl
        this.rowData.data11 = '重量'
        this.rowData.data12 = '比例'
        this.getSlabPanFei('3')
      }

      // 准备图表数据（这里仅作为示例，您需要根据实际情况来设置数据）

      // this.$nextTick(() => {
      //   // 确保对话框已经打开并且 DOM 已经更新
      //   this.initChart(chartData)
      // })
      // 初始化或更新图表
      // this.initChart(chartData)
    },
    initChart(data) {
      // 如果已经存在图表实例，则销毁它
      if (this.chartInstance) {
        this.chartInstance.dispose()
      }
      let interData = this.rowData

      // 在对话框的容器上初始化新的图表实例
      this.chartInstance = this.$echarts.init(document.getElementById('chart'))
      // let myChart = this.$echarts.init(document.getElementById(chartname))
      // 设置图表选项
      const options = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center',
          data: this.chartData.map((item, index) => {
            return {
              name: item.name,
              textStyle: {
                color: this.color[index]
              }
            }
          }),
          formatter: name => {
            let arr =
              '{name|' +
              name +
              '}\n{value|' +
              this.getNum(name).value +
              '}\n{percentage|' +
              this.getNum(name).percent +
              '}'
            return arr
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 18,
                align: 'left',
                lineHeight: 50,
                width: this.labelWidth,
                color: '#f1f1f1'
              },
              value: {
                fontSize: 20,
                align: 'left',
                width: 50
              },
              percentage: {
                fontSize: 16,
                align: 'right'
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '65%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center',
              formatter: function(params) {
                // console.log('params', params)
                return `${params.name}: ${params.value}`
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ],
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text:
                interData.data11 +
                ':' +
                interData.data1 +
                '\n' +
                interData.data12 +
                ':' +
                interData.data2,
              textAlign: 'center',
              textVerticalAlign: 'middle',
              fill: '#fff', // 文本颜色
              fontSize: 14, // 字体大小
              fontWeight: 'bold' // 字体粗细
            }
          }
        ]
      }
      // 使用选项来设置图表
      this.chartInstance.setOption(options)
      this.chartInstance.on('click', this.handleChartClick)
    },
    //获取二级表格数据
    findDetailsDateNoPage(slabFlag, slabType) {
      this.secondTitle = slabType
      post(
        findDetailsDateNoPage,
        Object.assign(
          {
            startDate: this.cDate2[0],
            endDate: this.cDate2[1],
            steelType: this.steelType2,
            slabFlag: slabFlag,
            slabType: slabType
          },
          this.params
        )
      ).then(res => {
        // this.pagination.total = res.data.totalElements
        this.secondTableData = res.data
      })
    },
    handleChartClick(params) {
      this.selectedData = params.data
      this.secondTablevisible = true
      let slabFlagParms = ''
      console.log('params', params)
      if (this.titleDitail.includes('判废')) {
        slabFlagParms = '1'
      } else if (this.titleDitail.includes('替代')) {
        slabFlagParms = '2'
      } else {
        slabFlagParms = '3'
      }
      this.findDetailsDateNoPage(slabFlagParms, params.name)
    },
    getNum(name) {
      const match = this.chartData.find(item => item.name === name)
      return match ? match : {}
    }
  }
}
</script>

<style scoped lang="less">
// 大屏按钮
.screen-btn {
  display: inline-block;
  min-width: 68px;
  height: 28px;
  padding: 0 5px;
  background: rgba(31, 198, 255, 0.3);
  border: 1px solid #1fc6ff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  color: #fff;
  &:hover {
    background: rgba(31, 198, 255, 0.6);
    border: 1px solid #1fc6ff;
  }
}
.scroll-wrapper {
  height: 100%;
  // overflow: auto;
}
/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}
/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
/deep/.el-input--small .el-input__inner {
  height: 32px;
  line-height: 32px;
  background-color: #093d4d;
  color: #fff;
}
/deep/.el-range-editor--small .el-range-input {
  font-size: 13px;
  color: #fff;
}
/deep/.el-table__body-wrapper::-webkit-scrollbar {
  width: 26px; /* 设置滚动条的宽度 */
  height: 26px; /* 如果需要垂直滚动条也变粗，可以设置这个属性 */
}
</style>
