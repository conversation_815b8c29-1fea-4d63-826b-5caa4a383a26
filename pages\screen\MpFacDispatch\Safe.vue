<template>
  <div class="bigBox">
    <el-row :gutter="32">
      <el-col :span="10">
        <screen-border title="安全检查情况">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(1)">
              <el-icon class="el-icon-edit-outline" />
              操作
            </span>
          </template>
          <el-table
            :data="securityChecks"
            height="calc(100vh - 250px)">
            <el-table-column
              type="index"
              label="序号"
              width="60" />
            <el-table-column
              prop="project"
              label="项目"
              align="center" />
            <el-table-column
              prop="checkSituation"
              label="检查情况"
              align="center" />
            <el-table-column
              prop="remark"
              label="备注"
              align="center" />
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="14">
        <screen-border title="施工项目安全检查情况">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(2)">
              <el-icon class="el-icon-edit-outline" />
              操作
            </span>
          </template>
          <el-table
            :data="projectSecurity"
            height="calc(100vh - 250px)"
            show-summary>
            <el-table-column
              type="index"
              label="序号"
              width="60" />
            <el-table-column
              prop="region"
              label="施工区域"
              align="center" />
            <el-table-column
              prop="project"
              label="施工项目"
              align="center" />
            <el-table-column
              prop="person"
              label="项目负责人"
              align="center" />
            <el-table-column
              prop="startTime"
              label="施工开始时间"
              align="center" />
            <el-table-column
              prop="endTime"
              label="施工结束时间"
              align="center" />
            <el-table-column
              prop="numberOfPeople"
              label="作业人数"
              align="center" />
            <el-table-column
              prop="checkSituation"
              label="检查情况"
              align="center" />
          </el-table>
        </screen-border>
      </el-col>
    </el-row>

    <!--弹框-->
    <el-dialog
      :visible.sync="dialogBox"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      @close="closeDialogBox">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span> -->
            <span
              v-show="title!='原因说明'"
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download" />
              下载
            </span>
            <span
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-printer" />
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <div>
        <el-table
          id="table"
          :data="formData"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60" />
          <el-table-column
            v-for="(item,index) in Header"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            align="center">
            <template v-slot="{ row }">
              <el-input v-model="row[item.prop]" />
              <span v-show="false">{{ row[item.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            width="100"
            label="操作">
            <template v-slot="{ row, $index }">
              <div class="btn">
                <el-button 
                  type="danger"
                  icon="el-icon-delete"
                  @click="delRow($index)"/>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="text-center">
          <span
            class="screen-btn"
            @click="addNewRow()">
            <el-icon class="el-icon-circle-plus-outline" />
            增加数据
          </span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import { saveAs } from 'file-saver'
import * as echarts from 'echarts'
import moment from 'moment'

import {
  PROJECTSECURITY,
  PROJECTSECURITYSAVE,
  SECURITYCHECKS,
  SECURITYCHECKSSAVE
} from '@/api/screen'

export default {
  name: 'Safe',
  components: {
    ScreenBorder
  },
  props: {
    selTime: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      //安全检查情况
      securityChecks: [],

      //施工项目安全检查情况
      projectSecurity: [],

      //弹框统一
      dialogBox: false,
      title: '',
      //弹框统一表字段
      Header: [],
      //弹框统一表数据
      formData: [],
      //上传Excel
      fileList: []
    }
  },
  watch: {
    selTime: function() {
      this.getSecurityChecks()
      this.getProjectSecurity()
    }
  },

  created() {
    this.getSecurityChecks()
    this.getProjectSecurity()
  },
  methods: {
    //安全检查情况
    async getSecurityChecks() {
      this.securityChecks = []
      let res = await post(SECURITYCHECKS, {
        setTime: this.selTime
      })
      // console.log('安全检查情况', res)

      if (res.data.length != 0) {
        this.securityChecks = res.data
      }
    },
    //施工项目安全检查情况
    async getProjectSecurity() {
      this.projectSecurity = []
      let res = await post(PROJECTSECURITY, {
        setTime: this.selTime
      })
      // console.log('施工项目安全检查情况', res)

      if (res.data.length != 0) {
        this.projectSecurity = res.data
      }
    },

    //弹框
    openView(nub) {
      this.dialogBox = true
      if (nub === 1) {
        this.title = '安全检查情况'
        this.Header = [
          {
            label: '项目',
            prop: 'project'
          },
          {
            label: '检查情况',
            prop: 'checkSituation'
          },
          {
            label: '备注',
            prop: 'remark'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.securityChecks))
      } else if (nub == 2) {
        this.title = '施工项目安全检查情况'
        this.Header = [
          {
            label: '施工区域',
            prop: 'region'
          },
          {
            label: '施工项目',
            prop: 'project'
          },
          {
            label: '项目负责人',
            prop: 'person'
          },
          {
            label: '施工开始时间',
            prop: 'startTime'
          },
          {
            label: '施工结束时间',
            prop: 'endTime'
          },
          {
            label: '作业人数',
            prop: 'numberOfPeople'
          },
          {
            label: '检查情况',
            prop: 'checkSituation'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.projectSecurity))
      }
    },

    //添加行
    addNewRow() {
      this.formData.push({})
    },

    //删除行
    delRow(index) {
      this.formData.splice(index, 1)
    },

    //下载模板
    DownloadExcel() {
      let data = [{}]
      this.Header.forEach(item => {
        data[0][item.prop] = item.label
      })

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `${this.title}.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          `${this.title}.xlsx`
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      let header = {}
      let letter = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      this.Header.forEach((item, index) => {
        header[item.prop] = letter[index]
      })

      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, header)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        console.log(list)

        list.forEach(item => {
          item.selTime = this.selTime
          this.formData.push(item)
        })
      })
    },

    //保存新增数据
    async saveData() {
      let res
      if (this.title == '安全检查情况') {
        res = await post(SECURITYCHECKSSAVE, {
          setTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '施工项目安全检查情况') {
        res = await post(PROJECTSECURITYSAVE, {
          setTime: this.selTime,
          data: this.formData
        })
      }
      // console.log('保存', res)
      if (res.status == 1) {
        this.$message.success('保存成功')
        if (this.title == '安全检查情况') {
          this.getSecurityChecks()
        } else if (this.title == '施工项目安全检查情况') {
          this.getProjectSecurity()
        }

        this.closeDialogBox()
      }
    },

    //关闭弹框
    closeDialogBox() {
      this.dialogBox = false
      this.title = ''
      this.Header = []
      this.formData = []
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  .EchartsBox {
    height: 380px;

    .setRadio {
      /deep/ .el-radio {
        color: white;
      }
    }
  }

  .border-wrapper {
    margin-bottom: 15px;
  }

  /deep/ .el-textarea__inner {
    background-color: #041a21;
    border: 1px solid #1fc6ff;
    color: white;
    font-size: 14px;
    height: calc(100vh - 670px);
  }
}

.btn {
  /deep/ .el-button {
    font-size: 15px;
    padding: 4px 15px;
    border-radius: 4px;
  }
}

/deep/ .el-table .el-table__cell {
  font-size: 19px;
}

/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}
</style>
