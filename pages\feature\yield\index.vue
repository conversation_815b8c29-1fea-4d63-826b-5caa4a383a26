<template>
  <div>
    <kpi-def/>
    <el-tabs
      v-model="activeName"
      type="border-card"
      @tab-click="handleClick">
      <el-tab-pane
        v-for="(item) in dataList"
        :key="item.id"
        :label="item.name"
        :name="item.id"/>
      <loading v-if="loading"/>
      <template
        v-else>
        <div
          v-if="dataList.length"
          class="operate-wrapper">
          <el-select
            v-model="rank"
            placeholder="显示级别">
            <el-option
              v-for="(item, index) in levelList"
              :key="index"
              :disabled="item.name"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-checkbox
            v-model="onlyWarning"
            label="仅预警"
            border
            size="small"/>
        </div>
        <Empty v-else/>
        <kpi-node
          :node="treeDate"
          :only-warning="onlyWarning"
          :rank="rank"/>
      </template>
    </el-tabs>
  </div>
</template>

<script>
import { findKpiDataTree } from '@/api/kpi'
import { post } from '@/lib/Util'
import KpiNode from '@/components/kpiTree/KpiNode'
import KpiDef from '@/components/KpiDef'
import { ENUM } from '@/lib/Constant'

export default {
  name: 'feature-yield',
  components: { KpiDef, KpiNode },
  data() {
    return {
      activeName: null,
      dataList: [],
      levelList: ENUM.levelList,
      onlyWarning: false,
      rank: 3,
      feature: 0,
      loading: true
    }
  },
  computed: {
    treeDate: function() {
      return this.dataList.length
        ? this.dataList.find(item => this.activeName === item.id)
        : {}
    }
  },
  watch: {
    $route: {
      handler(newVal, oldVal) {
        //判断newVal有没有值监听路由变化
        console.log(newVal)
        this.feature = newVal.query.feature
        this.loadData()
      },
      deep: true
    }
  },
  created() {
    this.feature = this.$route.query.feature
    this.loadData()
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    },
    async loadData() {
      this.onlyWarning = false
      this.rank = 3
      this.loading = true
      const data = await post(findKpiDataTree, { feature: this.feature })
      if (data.success) {
        this.dataList = data.data || []
        this.activeName = this.dataList.length ? this.dataList[0].id : null
      }
      this.loading = false
    }
  }
}
</script>

<style scoped lang="less">
.operate-wrapper {
  text-align: right;
  margin: 10px;
}
</style>
