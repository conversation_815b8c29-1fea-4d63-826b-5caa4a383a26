<template>
  <div
    :id="containerId"
    :style="{ height: '100%' }"/>
</template>

<script>
export default {
  name: 'stock-line-chart',
  props: {
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    lastMonthData: {
      type: String,
      default: ''
    },
    monthPlanData: {
      type: String,
      default: ''
    },
    chartData2: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return [
          '#2772F0',
          '#48fa4a',
          '#fac858',
          '#ee6666',
          '#4aead2',
          '#d66aee',
          '#c9f602',
          '#D45454'
        ]
      }
    },
    showLegend: {
      type: Boolean,
      default: false
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 46
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
        window.addEventListener('resize', this.resizeChart)
      }
      const options = {
        tooltip: {
          confine: true,
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#57617B'
            }
          }
        },
        color: this.color,
        grid: {
          top: this.showLegend ? '15%' : '5%',
          left: '2%',
          right: '2%',
          bottom: '5%',
          containLabel: true
        },
        legend: {
          show: this.showLegend,
          right: 10,
          itemHeight: 8, // 修改icon图形大小
          itemWidth: 25, // 修改icon图形大小
          lineStyle: {
            join: 'bevel'
          },
          textStyle: {
            color: '#9facd5',
            fontSize: 12
          },
          data: this.chartData.filter(item => item.name !== 'CAD')
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisTick: { show: false },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            },
            data: this.xData
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisTick: {
              show: false
            },
            min: value => {
              const maxLength = value.min
                .toString()
                .split('.')
                .pop().length
              return this.formatInt(value.min - 1000, maxLength - 1, false)
            },
            axisLine: {
              lineStyle: {
                color: '#57617B'
              }
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'right',
              formatter: value => {
                if (value >= 10000) {
                  return value / 10000 + 'w'
                }
                return value
              }
            },
            splitLine: {
              lineStyle: {
                color: '#2E4262'
              }
            }
          }
        ],
        series: this.chartData.map(item => {
          return {
            name: item.name,
            type: 'line',
            smooth: true,
            markLine: {
              //最大值和最小值
              data: [
                {
                  name: '阈值',
                  yAxis: this.lastMonthData,
                  label: {
                    show: 'true',
                    position: 'insideEndTop',
                    formatter: '上月库存：' + this.lastMonthData || 0,
                    color: '#FF9800',
                    textBorderWidth: 0
                  },
                  lineStyle: {
                    normal: {
                      width: 2,
                      color: '#FF9800'
                    }
                  }
                },
                {
                  name: '阈值',
                  yAxis: this.monthPlanData,
                  label: {
                    show: 'true',
                    position: 'insideEndTop',
                    formatter: '当月计划：' + this.monthPlanData || 0,
                    color: '#bd1c1c',
                    textBorderWidth: 0
                  },
                  lineStyle: {
                    normal: {
                      width: 2,
                      color: '#bd1c1c'
                    }
                  }
                }
              ]
            },
            lineStyle: {
              normal: {
                width: 2,
                opacity: !item.hidden ? 1 : 0
              }
            },
            symbol: 'emptyCircle',
            symbolSize: !item.hidden ? 6 : 0,
            legendHoverLink: !item.hidden,
            data: item.data
          }
        })
      }
      this.myChart.setOption(options)
    },
    getColor(item) {
      return !item.show
        ? 'transparent'
        : item.value < item.plan
          ? '#FF2855'
          : '#19BE6B'
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    /**
     * 将数字取整为10的倍数
     * @param {Number} num 需要取整的值
     * @param {Boolean} ceil 是否向上取整
     * @param {Number} prec 需要用0占位的数量
     */
    formatInt(num, prec = 2, ceil = true) {
      const len = String(num).length
      if (len <= prec) {
        return num
      }
      const mult = Math.pow(10, prec)
      return ceil ? Math.ceil(num / mult) * mult : Math.floor(num / mult) * mult
    },

    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
