<!--施工明细-->
<template>
  <div class="content">
    <div class="content-item">
      <screen-border title="施工明细">
        <template v-slot:headerRight>
          <el-row :gutter="20">
            <span
              v-command="'/screen/plateRollsSafe/edit'"
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              v-command="'/screen/plateRollsSafe/edit'"
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download"/>
              下载
            </span>
            <span
              v-command="'/screen/plateRollsSafe/edit'"
              class="screen-btn"
              @click="AddData">
              <el-icon class="el-icon-edit-outline"/>
              新增
            </span>
            <span
              v-command="'/screen/plateRollsSafe/edit'"
              class="screen-btn"
              @click="handDel">
              <el-icon class="el-icon-delete"/>
              删除
            </span>
            <span
              v-command="'/screen/plateRollsSafe/edit'"
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="ImportExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span>
          </el-row>
        </template>

        <el-table
          v-loading="loading"
          id="table"
          :data="CD_data"
          :row-class-name="rowClassName"
          border
          @selection-change="getSelectRow">
          <el-table-column
            type="selection"
            align="center"/>
          <el-table-column
            show-overflow-tooltip
            width="80"
            align="center"
            label="序号">
            <template v-slot="scope">
              <div>{{ scope.$index + 1 }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="单位"
            align="center"
            width="100">
            <template v-slot="scope">
              <div>{{ scope.row.deptName }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="项目名称"
            align="center"
            width="300">
            <template v-slot="scope">
              <div>{{ scope.row.projectName }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="区域"
            align="center"
            width="100">
            <template v-slot="scope">
              <div>{{ scope.row.area }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="高危等级"
            align="center"
            width="100">
            <template v-slot="scope">
              <div>{{ scope.row.hazardLevel }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="作业内容">
            <template v-slot="scope">
              <div>{{ scope.row.workContent }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="监护姓名"
            align="center"
            width="100">
            <template v-slot="scope">
              <div>{{ scope.row.guardianName }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="施工单位"
            align="center"
            width="200">
            <template v-slot="scope">
              <div>{{ scope.row.constructionUnit }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="施工人数"
            align="center"
            width="100">
            <template v-slot="scope">
              <div>{{ scope.row.constructionPersons }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="日期"
            align="center"
            width="130">
            <template v-slot="scope">
              <div>{{ scope.row.setDate }}</div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property=""
            width="150"
            label="操作">
            <template v-slot="scope">
              <span @click="Edit(scope.row)">编辑</span>
              <span @click="Del(scope.row)">删除</span>
            </template>
          </el-table-column>
        </el-table>
      </screen-border>
    </div>

    <!--新增修改-->
    <el-dialog
      :visible.sync="CD_view"
      :close-on-click-modal="false"
      width="60%"
      class="screen-dialog"
      @close="Close_CD_view">
      <template v-slot:title>
        <div class="custom-dialog-title">
          施工明细
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">单位</div>
          <el-select
            v-model="FormData.deptName"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in deptList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">项目名称</div>
          <el-input
            v-model="FormData.projectName"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">区域</div>
          <el-input
            v-model="FormData.area"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">高危等级</div>
          <el-select
            v-model="FormData.hazardLevel"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in hazardLevelList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">作业内容</div>
          <el-input
            v-model="FormData.workContent"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">监护姓名</div>
          <el-input
            v-model="FormData.guardianName"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">施工单位</div>
          <el-input
            v-model="FormData.constructionUnit"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">施工人数</div>
          <el-input
            v-model="FormData.constructionPersons"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="SubmitData(0)">
          确定
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { CD_INQUIRE, CD_NEWS, CD_DEL } from '@/api/screen'
import moment from 'moment'

export default {
  name: 'ProjectPage',
  components: { ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      loading: false,
      //数据
      CD_data: [],

      //弹框数据
      CD_view: false,
      FormData: {},

      //单位下拉
      deptList: [
        {
          text: '原料车间',
          value: '原料车间',
          type: 'YLCJ'
        },
        {
          text: '炼钢车间',
          value: '炼钢车间',
          type: 'LGCJ'
        },
        {
          text: '精炼车间',
          value: '精炼车间',
          type: 'JLCJ'
        },
        {
          text: '连铸车间',
          value: '连铸车间',
          type: 'LZCJ'
        },
        {
          text: '运行车间',
          value: '运行车间',
          type: 'YXCJ'
        },
        {
          text: '坯料车间',
          value: '坯料车间',
          type: 'PLCJ'
        },
        {
          text: '综合管理室',
          value: '综合管理室',
          type: 'ZHGLS'
        },
        {
          text: '设备管理室',
          value: '设备管理室',
          type: 'SBGLS'
        },
        {
          text: '品质室',
          value: '品质室',
          type: 'PZS'
        },
        {
          text: '生产管理室',
          value: '生产管理室',
          type: 'SCGLS'
        }
      ],
      //高危等级下拉
      hazardLevelList: [
        {
          text: '一级',
          value: '一级',
          type: 'A'
        },
        {
          text: '二级',
          value: '二级',
          type: 'B'
        },
        {
          text: '无',
          value: '无',
          type: 'C'
        }
      ],

      //多选数据
      rowData: [],

      //上传
      fileList: []
    }
  },
  watch: {
    selectDate: function() {
      this.getConstructionDetail()
    }
  },
  mounted() {
    this.getConstructionDetail()
  },
  methods: {
    //查询施工明细数据
    async getConstructionDetail() {
      this.loading = true
      let res = await post(CD_INQUIRE, {
        setDate: this.selectDate
      })
      // console.log('总数据', res)
      if (res.data) {
        this.loading = false
        this.CD_data = res.data
      }
    },

    //下载模板
    DownloadExcel() {
      const data = [
        {
          num: '序号',
          deptName: '单位',
          projectName: '项目名称',
          area: '区域',
          hazardLevel: '高危等级',
          workContent: '作业内容',
          guardianName: '监护姓名',
          constructionUnit: '施工单位',
          constructionPersons: '施工人数'
        }
      ]
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `施工明细模板.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          '施工明细.xlsx'
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //新增
    AddData() {
      this.CD_view = true
      this.FormData = {
        deptName: '',
        projectName: '',
        area: '',
        hazardLevel: '',
        workContent: '',
        guardianName: '',
        constructionUnit: '',
        constructionPersons: '',
        setDate: this.selectDate
      }
    },

    //新增/修改
    async SubmitData(num) {
      let res
      if (num == 0) {
        res = await post(CD_NEWS, [this.FormData])
      } else {
        res = await post(CD_NEWS, this.FormData)
      }

      // console.log('新增', res)
      if (res.status == 1) {
        this.$message.success('成功!')
        this.Close_CD_view()
        this.getConstructionDetail()
      }
    },

    //关闭新增弹框
    Close_CD_view() {
      this.CD_view = false
      this.FormData = {}
    },

    //多选
    getSelectRow(val) {
      this.rowData = val
    },

    // 批量删除
    handDel() {
      let IdData = []
      if (this.rowData.length != 0) {
        this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.rowData.forEach(item => {
            IdData.push({ id: item.id })
          })
          let res = await post(CD_DEL, IdData)
          // console.log('删除', res)
          if (res.status == 1) {
            this.$message.success(res.data)
            this.getConstructionDetail()
          }
        })
      } else {
        this.$message.warning('请勾选删除行!')
      }
    },

    //上传
    ImportExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          num: 'A',
          deptName: 'B',
          projectName: 'C',
          area: 'D',
          hazardLevel: 'E',
          workContent: 'F',
          guardianName: 'G',
          constructionUnit: 'H',
          constructionPersons: 'I'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })

        this.FormData = list
        this.SubmitData(1)
      })
    },

    //编辑
    Edit(row) {
      this.CD_view = true
      this.FormData = JSON.parse(JSON.stringify(row))
    },

    //删除
    Del(row) {
      this.rowData = [
        {
          id: row.id
        }
      ]
      this.handDel()
    },

    //改变行颜色
    rowClassName({ row, rowIndex }) {
      if (row.hazardLevel === 'C') {
        return 'class_red'
      } else if (row.hazardLevel === 'B') {
        return 'class_yellow'
      } else {
        return ''
      }
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}

/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}

.dialog-body {
  overflow: scroll;

  .dialog-cell {
    margin-bottom: 12px;

    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }

    .dialog-cell-input {
    }
  }
}
</style>
