<template>
  <div class="content">
    <div class="content-item">
      <screen-border title="炼钢日计划完成情况复盘">
        <template v-slot:headerRight>
          <span
            v-command="'/screen/morningMeeting/edit'"
            class="screen-btn"
            @click="unfinished.dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            操作
          </span>
        </template>
        <div
          ref="table1"
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            :data="unfinished.showGridData"
            :span-method="arraySpanMethod"
            :max-height="unfinished.maxHeight"
            class="font-table center-table"
            border>
            <el-table-column
              property="setTime"
              label="日期"
              width="90"/>
            <el-table-column
              property="dayIronConnection"
              label="接铁量"
              width="80"/>
            <el-table-column
              property="ironLoss"
              label="铁耗"
              width="75"/>
            <el-table-column
              property="furnaceProduction"
              label="炉产"
              width="75"/>
            <el-table-column
              property="abnormalSteelMaking"
              label="重点品种冶炼异常"
              align="left">
              <template v-slot="{ row }">
                <div
                  slot="content"
                  v-html="formatText(row.abnormalSteelMaking)"/>
              </template>
            </el-table-column>
            <el-table-column
              property="blankReviewResults"
              label="重点品种坯料评审结果"
              align="left"/>
            <el-table-column
              property="dayPlanFurnace"
              label="计划炉数"
              width="90"/>
            <el-table-column
              property="dayRealityFurnace"
              label="实际炉数"
              width="90"/>
            <el-table-column
              property="dayComplete"
              label="是否完成"
              width="90"/>
            <el-table-column
              property="classes"
              label="班次"
              width="80"/>
            <el-table-column
              property="classPlanFurnace"
              label="计划炉数"
              width="90"/>
            <el-table-column
              property="classRealityFurnace"
              label="实际炉数"
              width="90"/>
            <el-table-column
              property="classComplete"
              label="是否完成"
              width="90"/>
            <el-table-column
              property="underproduction"
              label="欠产情况"
              width="90"/>
            <el-table-column
              property="cause"
              label="原因分析"
              align="left"/>
          </el-table>
        </div>
      </screen-border>
    </div>
    <div class="content-hold"/>
    <div
      class="content-item">
      <custom-table
        :title="'轧钢日计划完成情况复盘'"
        :setting="steelMaking"
        :url-list="steelMakingUrl.list"
        :url-save="steelMakingUrl.save"
        :merge-set="mergeObj"
        :select-date="selectDate"/>
    </div>
    <div
      v-if="true"
      class="content-hold"/>
    <!--    <div
      v-if="true"
      class="content-item"
      style="height: 39%; flex: unset">
      <screen-border>
        <template v-slot:title>
          <div class="tabs-class">
            尖峰平谷电耗汇总 &emsp;
            <div
              v-for="(item) in tabList"
              :key="item.id"
              :class="{'tab-pane-active': item.id === activeTab}"
              class="tab-pane"
              @click="clickTabPane(item.id)">
              <div class="tab-pane-title-class">
                <div>{{ item.title }}</div>
                <div
                  v-if="item.id === activeTab"
                  class="tab-pane-img">
                  <img
                    class="tab-pane-img2"
                    src="@/assets/images/screen/tab-pane-active-line2.png"
                    alt="">
                  <img
                    class="tab-pane-img1"
                    src="@/assets/images/screen/tab-pane-active-line.png"
                    alt="">
                </div>
              </div>
            </div>
          </div>
        </template>
        <div
          ref="table3"
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            :data="elObj.showGridData"
            :max-height="elObj.maxHeight"
            class="font-table center-table"
            border>
            <el-table-column
              label="排名"
              type="index"
              width="70"/>
            <el-table-column
              property="factory"
              label="分厂"
              width="120">
              <template v-slot="{ row }">
                <span
                  style="text-decoration: underline; color: #e5f0f5; cursor: pointer"
                  @click="showEleChart(row)">
                  {{ getDict(row.factory, 'factoryList').label }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              property="average"
              label="综合单价">
              <template v-slot="{ row }">
                <span :class="{'color-red': row.average > 0.6656, 'color-green': row.average <= 0.6656}">
                  {{ row.average }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              label="尖">
              <el-table-column
                label="消耗量"
                property="consume3"/>
              <el-table-column
                property="pro3"
                label="占比">
                <template v-slot="{ row }">
                  <span :class="getClass(row.pro3,'尖')">
                    {{ row.pro3 }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                property="price3"
                label="成本"/>
            </el-table-column>
            <el-table-column
              label="峰">
              <el-table-column
                property="consume2"
                label="消耗量"/>
              <el-table-column
                property="pro2"
                label="占比">
                <template v-slot="{ row }">
                  <span :class="getClass(row.pro2,'峰')">
                    {{ row.pro2 }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                property="price2"
                label="成本"/>
            </el-table-column>
            <el-table-column
              label="平">
              <el-table-column
                property="consume1"
                label="消耗量"/>
              <el-table-column
                property="pro1"
                label="占比"/>
              <el-table-column
                property="price1"
                label="成本"/>
            </el-table-column>
            <el-table-column
              label="谷">
              <el-table-column
                property="consume0"
                label="消耗量"/>
              <el-table-column
                property="pro0"
                label="占比"/>
              <el-table-column
                property="price0"
                label="成本"/>
            </el-table-column>
          </el-table>
        </div>
      </screen-border>
    </div>-->
    <!--能源趋势图弹窗-->

    <el-dialog
      :visible.sync="elObj.dialogChartVisible"
      :width="'88%'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          {{ getDict(elObj.currentFactory, 'factoryList').label }} - 近30天尖峰平谷电耗趋势
        </div>
      </template>
      <div style="height: 350px">
        <line-chart
          :chart-data="elObj.chartData"
          :chart-data2="elObj.chartData2"
          :x-data="elObj.chartX"
          :show-legend="true" />
      </div>
    </el-dialog>
    <!--热处理详情-->
    <el-dialog
      :visible.sync="unfinished.dialogVisible"
      :width="'98%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="炼钢日计划完成情况复盘">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <template
              v-if="canEdit">
              <el-dropdown @command="handleProcessedCommand($event, 'importUnfinishedData')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportunfinished">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveUnfinished">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          炼钢日计划完成情况复盘
        </div>
      </template>
      <el-form :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :span-method="arraySpanMethod"
          :data="unfinished.gridData"
          border>
          <el-table-column
            property="setTime"
            label="日期"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.setTime" />
            </template>
          </el-table-column>
          <el-table-column
            property="dayIronConnection"
            label="接铁量"
            width="100">
            <template v-slot="{ row }">
              <el-input v-model="row.dayIronConnection" />
            </template>
          </el-table-column>
          <el-table-column
            property="ironLoss"
            label="铁耗"
            width="100">
            <template v-slot="{ row }">
              <el-input v-model="row.ironLoss" />
            </template>
          </el-table-column>
          <el-table-column
            property="furnaceProduction"
            label="炉产"
            width="100">
            <template v-slot="{ row }">
              <el-input v-model="row.furnaceProduction" />
            </template>
          </el-table-column>
          <el-table-column
            property="abnormalSteelMaking"
            label="重点品种冶炼异常">
            <template v-slot="{ row }">
              <el-input
                v-model="row.abnormalSteelMaking"
                :rows="8"
                type="textarea" />
            </template>
          </el-table-column>
          <el-table-column
            property="blankReviewResults"
            label="重点品种坯料评审结果">
            <template v-slot="{ row }">
              <el-input
                v-model="row.blankReviewResults"
                :rows="8"
                type="textarea" />
            </template>
          </el-table-column>
          <el-table-column
            property="dayPlanFurnace"
            label="计划炉数"
            width="100">
            <template v-slot="{ row }">
              <el-input v-model="row.dayPlanFurnace" />
            </template>
          </el-table-column>
          <el-table-column
            property="dayRealityFurnace"
            label="实际炉数"
            width="100">
            <template v-slot="{ row }">
              <el-input v-model="row.dayRealityFurnace" />
            </template>
          </el-table-column>
          <el-table-column
            property="dayComplete"
            label="是否完成"
            width="100">
            <template v-slot="{ row }">
              <el-input v-model="row.dayComplete" />
            </template>
          </el-table-column>
          <el-table-column
            property="classes"
            label="班次"
            width="100">
            <template v-slot="{ row }">
              <el-input v-model="row.classes" />
            </template>
          </el-table-column>
          <el-table-column
            property="classPlanFurnace"
            label="计划炉数"
            width="100">
            <template v-slot="{ row }">
              <el-input v-model="row.classPlanFurnace" />
            </template>
          </el-table-column>
          <el-table-column
            property="classRealityFurnace"
            label="实际炉数"
            width="100">
            <template v-slot="{ row }">
              <el-input v-model="row.classRealityFurnace" />
            </template>
          </el-table-column>
          <el-table-column
            property="classComplete"
            label="是否完成"
            width="100">
            <template v-slot="{ row }">
              <el-input v-model="row.classComplete" />
            </template>
          </el-table-column>
          <el-table-column
            property="underproduction"
            label="欠产情况"
            width="100">
            <template v-slot="{ row }">
              <el-input v-model="row.underproduction" />
            </template>
          </el-table-column>
          <el-table-column
            property="cause"
            label="原因分析">
            <template v-slot="{ row }">
              <el-input
                v-model="row.cause"
                type="textarea" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index, 'unfinished')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEdit"
          class="screen-btn"
          @click="addGridData('unfinished')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { batchUpdateResource } from '@/api/system'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import {
  findSteelmakingReplayByDate,
  saveSteelmakingReplay,
  steelRollingReplaySave,
  steelRollingReplayByDate,
  eleSplit,
  eleSplitForPlatform
} from '@/api/screen'
import * as _ from 'lodash'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/morningMeeting/component/custom-table'
import LineChart from '@/pages/screen/morningMeeting/component/line-chart'
import BaseMixins from '@/mixins/BaseMixins'
export default {
  name: 'replay',
  components: {
    LineChart,
    CustomTable,
    SingleBarsChart,
    SteelBarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins, BaseMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      unfinished: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      factoryList: [
        {
          label: '一炼钢',
          value: 'fs'
        },
        {
          label: '中厚板卷厂',
          value: 'mats'
        },
        {
          label: '宽厚板厂',
          value: 'ele'
        },
        {
          label: '中板厂',
          value: 'ms'
        },
        {
          label: '金石',
          value: 'gs'
        }
      ],
      elObj: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        dialogChartVisible: false,
        maxHeight: null,
        currentFactory: null,
        chartData: [],
        chartData2: [],
        chartX: []
      },
      steelMakingUrl: {
        save: steelRollingReplaySave,
        list: steelRollingReplayByDate
      },
      mergeObj: {
        '0-0': [3, 1],
        '1-0': false,
        '2-0': false
      },
      steelMaking: [
        {
          keyQuery: 'settime',
          keySave: 'settime',
          label: '时间',
          width: '130'
        },
        {
          keyQuery: 'plt',
          keySave: 'plt',
          label: '厂别',
          width: 100
        },
        {
          keyQuery: 'plan',
          keySave: 'plan',
          label: '计划',
          width: 110
        },
        {
          keyQuery: 'reality',
          keySave: 'reality',
          label: '实际',
          width: 110
        },
        {
          keyQuery: 'keyvarieties',
          keySave: 'keyVarieties',
          label: '重点品种'
        },
        {
          keyQuery: 'completed',
          keySave: 'completed',
          label: '是否完成',
          width: 100
        },
        {
          keyQuery: 'underbirthsituation',
          keySave: 'underBirthSituation',
          label: '超欠情况',
          width: 150
        },
        {
          keyQuery: 'stoptime',
          keySave: 'stopTime',
          label: '停时时间',
          width: 130
        },
        {
          keyQuery: 'reason',
          keySave: 'reason',
          label: '原因分析'
        }
      ],
      activeTab: '1',
      tabList: [
        {
          id: '1',
          title: '昨日'
        },
        {
          id: '2',
          title: '当月'
        }
      ]
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getUnfinished()
      this.getEle()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.$nextTick(() => {
      this.calculateHeight()
      window.addEventListener('resize', this.calculateHeight)
    })
  },
  methods: {
    calculateHeight() {
      this.unfinished.maxHeight = this.$refs.table1.offsetHeight
      this.$refs.table3 &&
        (this.elObj.maxHeight = this.$refs.table3.offsetHeight)
    },
    getClass(num, sy) {
      console.log(sy)
      const percent = Number(num.replace('%', ''))
      if (sy == '尖') {
        return percent > 12.5 ? 'color-red' : 'color-green'
      } else {
        return percent > 20.8333 ? 'color-red' : 'color-green'
      }
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          setTime: 'A',
          ironLoss: 'B',
          furnaceProduction: 'C',
          dayIronConnection: 'D',
          abnormalSteelMaking: 'E',
          blankReviewResults: 'F',
          dayPlanFurnace: 'G',
          dayRealityFurnace: 'H',
          dayComplete: 'I',
          classes: 'J',
          classPlanFurnace: 'K',
          classRealityFurnace: 'L',
          classComplete: 'M',
          underproduction: 'N',
          cause: 'O'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unfinished.gridData = sheet
          .filter(item => item.setTime !== '日期')
          .map(item => {
            item.setTime =
              typeof item.setTime === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setTime, 'MM月DD日')
                : item.setTime
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportunfinished() {
      const data = [
        {
          setTime: '日期',
          dayIronConnection: '接铁量',
          ironLoss: '铁耗',
          furnaceProduction: '炉产',
          abnormalSteelMaking: '重点品种冶炼异常',
          blankReviewResults: '重点品种坯料评审结果',
          dayPlanFurnace: '计划炉数',
          dayRealityFurnace: '实际炉数',
          dayComplete: '当日是否完成',
          classes: '班次',
          classPlanFurnace: '计划炉数',
          classRealityFurnace: '实际炉数',
          classComplete: '当班是否完成',
          underproduction: '欠产情况',
          cause: '原因分析'
        }
      ].concat(_.cloneDeep(this.unfinished.gridData))
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `炼钢日计划完成情况复盘（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },

    clickTabPane(item) {
      this.activeTab = item
      this.getEle()
    },
    getEle() {
      this.loading = true
      const params = {
        startTime:
          this.activeTab == 1
            ? this.prevDate
            : this.$moment(this.prevDate)
                .startOf('month')
                .format('YYYY-MM-DD'),
        endTime: this.cDate
      }
      post(eleSplit, params).then(res => {
        this.loading = false
        this.elObj.showGridData = res.data.listFpg_Pt
          .map(item => {
            item.average = res.data.listCompPrice[item.factory]
            return item
          })
          .sort((a, b) => a.average - b.average)
      })
    },
    getUnfinished() {
      post(findSteelmakingReplayByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.$emit('statusChange', !res.data.length)
        this.unfinished.showGridData = res.data.map(item => {
          return {
            setTime: item.settime,
            dayIronConnection: item.dayironconnection,
            ironLoss: item.ironloss,
            furnaceProduction: item.furnaceproduction,
            abnormalSteelMaking: item.abnormalsteelmaking,
            blankReviewResults: item.blankreviewresults,
            dayPlanFurnace: item.dayplanfurnace,
            dayRealityFurnace: item.dayrealityfurnace,
            dayComplete: item.daycomplete,
            classes: item.classes,
            classPlanFurnace: item.classplanfurnace,
            classRealityFurnace: item.classrealityfurnace,
            classComplete: item.classcomplete,
            underproduction: item.underproduction,
            cause: item.cause
          }
        })
        this.unfinished.gridData = lodash.cloneDeep(
          this.unfinished.showGridData
        )
      })
    },
    saveUnfinished() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.unfinished.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(saveSteelmakingReplay, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.unfinished.dialogVisible = false
          this.getUnfinished()
        }
      })
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
      if ([0, 1, 2, 3, 4, 5, 6, 7].includes(columnIndex)) {
        if (rowIndex === 0) {
          return [3, 1]
        } else {
          return [0, 0]
        }
      }
    },
    getMergeData(rowIndex, columnIndex) {
      const matchLeftTop = this.unfinished.gridMerge.find(
        item => item.s.c === columnIndex && item.s.r === rowIndex
      )
      if (matchLeftTop) {
        return [
          matchLeftTop.e.r - matchLeftTop.s.r + 1,
          matchLeftTop.e.c - matchLeftTop.s.c + 1
        ]
      }
      const merged = this.unfinished.gridMerge.find(item => {
        return (
          item.s.c < columnIndex &&
          columnIndex <= item.e.c &&
          item.s.r < rowIndex &&
          rowIndex <= item.e.r
        )
      })
      if (merged) {
        console.log(merged)
        return [0, 0]
      }
    },
    importUnfinishedData(date) {
      post(findSteelmakingReplayByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.unfinished.gridData = res.data.map(item => {
          return {
            setTime: item.settime,
            dayIronConnection: item.dayironconnection,
            ironLoss: item.ironloss,
            furnaceProduction: item.furnaceproduction,
            abnormalSteelMaking: item.abnormalsteelmaking,
            blankReviewResults: item.blankreviewresults,
            dayPlanFurnace: item.dayplanfurnace,
            dayRealityFurnace: item.dayrealityfurnace,
            dayComplete: item.daycomplete,
            classes: item.classes,
            classPlanFurnace: item.classplanfurnace,
            classRealityFurnace: item.classrealityfurnace,
            classComplete: item.classcomplete,
            underproduction: item.underproduction,
            cause: item.cause
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 显示能源图表
    showEleChart(row) {
      this.elObj.currentFactory = row.factory
      this.elObj.dialogChartVisible = true
      this.loading = true
      const params = {
        startTime: this.$moment(this.cDate)
          .subtract(30, 'day')
          .format('YYYY-MM-DD'),
        endTime: this.cDate
      }
      post(eleSplitForPlatform, params).then(res => {
        this.loading = false
        this.elObj.chartX = res.data.X
        this.elObj.chartData = [
          {
            name: '尖',
            data: res.data[row.factory].jian
          },
          {
            name: '峰',
            data: res.data[row.factory].feng
          },
          {
            name: '平',
            data: res.data[row.factory].ping
          },
          {
            name: '谷',
            data: res.data[row.factory].gu
          }
        ]
        this.elObj.chartData2 = [
          {
            name: '综合单价',
            data: res.data[row.factory].zonghe
          }
        ]
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 10px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.tabs-class {
  display: flex;
  flex-direction: row;
  .tab-pane {
    color: #ffffffbf;
    margin-right: 21px;
  }
  .tab-pane-active {
    color: #ffffff;
  }
  .tab-pane-title-class {
    display: flex;
    flex-direction: column;
    position: relative;
    cursor: pointer;
    .tab-pane-img {
      .tab-pane-img2 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
      .tab-pane-img1 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        margin-bottom: 7px;
      }
    }
  }
}

.scroll-wrapper {
  height: 100%;
}
/deep/ .color-red {
  color: #af0000;
}
/deep/ .color-green {
  color: #00af37;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
