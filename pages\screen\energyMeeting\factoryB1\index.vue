<template>
  <div class="content">
    <div class="content-item">

      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="4"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'炼钢工序能耗'">
            <div class="chart-wrapper">
              <div
                class="chart">
                <multi-bars-chart
                  :bar-width="26"
                  :finished-color="'#19BE6B'"
                  :unfinished-color="'#FF2855'"
                  :inverse="ecChart.inverse1"
                  :chart-data="ecChart.bar1"
                  :chart-data2="ecChart.bar2"
                  :x-data="ecChart.barX1"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="4"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'炼钢能动成本'">
            <div class="chart-wrapper">
              <div
                class="chart">
                <single-bars-chart
                  :unfinished-color="'#19BE6B'"
                  :finished-color="'#FF2855'"
                  :bar-width="40"
                  :unit="'元/t'"
                  :chart-data="costChart.bar1"
                  :x-data="['']"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="11"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'主要能介单耗'">
            <div class="chart-wrapper">
              <el-row
                :gutter="20"
                class="full-height chart-row">
                <el-col
                  v-for="(item, index) in chartList"
                  :key="index"
                  :span="3"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div class="chart-title">
                      {{ item.name }}
                    </div>
                    <div
                      class="chart">
                      <single-bars-chart
                        :finished-color="getFinishColorByName(item.name)"
                        :unfinished-color="getUnfinishColorByName(item.name)"
                        :bar-width="26"
                        :lebel-wrap="true"
                        :unit="item.unit || ''"
                        :chart-data="item.bar"
                        :x-data="['']"/>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="5"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'主要能效指标'">
            <div class="chart-wrapper">
              <div class="content-item">
                <pie-chart
                  :chart-data="pieChart.bar1"
                  :unit="'KWh'"
                  :title="'谷平峰利用率'"
                  :title-num="pieChart.total"
                  :label-width="25"
                  :vertical="false"/>
              </div>
              <div class="content-item">
                <polar-chart
                  :chart-data="pieChart.RATE"
                  :unit="'%'"
                  :title="'煤气回收率'"
                  :title-num="pieChart.RATE + '%'"
                  :label-width="22"
                  :vertical="false"/>
                <div class="polar-text">
                  平均燃烧系数&emsp;{{ pieChart.COEFF }} <br>
                  平均吸入系数&emsp;{{ pieChart.AIRCOE }} <br>
                  平均铁水比&emsp;&emsp;{{ pieChart.RATIO }} <br>
                </div>
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold" />

    <div class="content-item">

      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="4"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'炼钢工序能耗（月度）'">
            <div class="chart-wrapper">
              <div
                class="chart">
                <multi-bars-chart
                  :bar-width="26"
                  :finished-color="'#19BE6B'"
                  :unfinished-color="'#FF2855'"
                  :inverse="ecChart.inverse2"
                  :chart-data="ecChart.bar3"
                  :chart-data2="ecChart.bar4"
                  :x-data="ecChart.barX1"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="4"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'炼钢能动成本（月度）'">
            <div class="chart-wrapper">
              <div
                class="chart">
                <single-bars-chart
                  :unfinished-color="'#19BE6B'"
                  :finished-color="'#FF2855'"
                  :bar-width="40"
                  :unit="'元/t'"
                  :chart-data="costChart.bar3"
                  :x-data="['']"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="11"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'主要能介单耗（月度）'">
            <div class="chart-wrapper">
              <el-row
                :gutter="20"
                class="full-height chart-row" >
                <el-col
                  v-for="(item, index) in chartMonthList"
                  :key="index"
                  :span="3"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div class="chart-title">
                      {{ item.name }}
                    </div>
                    <div
                      class="chart">
                      <single-bars-chart
                        :finished-color="getFinishColorByName(item.name)"
                        :unfinished-color="getUnfinishColorByName(item.name)"
                        :bar-width="26"
                        :lebel-wrap="true"
                        :unit="item.unit || ''"
                        :chart-data="item.bar"
                        :x-data="['']"/>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="5"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'主要能效指标（月度）'">
            <div class="chart-wrapper">
              <div class="content-item">
                <pie-chart
                  :chart-data="pieMonthChart.bar1"
                  :unit="'KWh'"
                  :title="'谷平峰利用率'"
                  :title-num="pieMonthChart.total"
                  :label-width="24"
                  :vertical="false"/>
              </div>
              <div class="content-item">
                <polar-chart
                  :chart-data="pieMonthChart.RATE"
                  :unit="'%'"
                  :title="'煤气回收率'"
                  :title-num="pieMonthChart.RATE + '%'"
                  :label-width="22"
                  :vertical="false"/>
                <div class="polar-text">
                  平均燃烧系数&emsp;{{ pieMonthChart.COEFF }} <br>
                  平均吸入系数&emsp;{{ pieMonthChart.AIRCOE }} <br>
                  平均铁水比&emsp;&emsp;{{ pieMonthChart.RATIO }} <br>
                </div>
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/qualityMeeting/component/custom-table'
import SingleBarsChart from '@/pages/screen/energyMeeting/component/single-bars-chart'
import BarsChart from '@/pages/screen/qualityMeeting/component/bars-chart'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import MultiBarsChart from '@/pages/screen/energyMeeting/component/multi-bars-chart'
import PieChart from '@/pages/screen/energyMeeting/component/pie-chart'
import BarChart from '@/pages/screen/energyMeeting/component/bar-chart'
import {
  ecAndCostB1,
  ELEFGP,
  FSMONTHTOSUsingPOST,
  GASRECOVERY
} from '@/api/screenEnergy'
import PolarChart from '@/pages/screen/energyMeeting/component/polar-chart'

export default {
  name: 'FactoryB1',
  components: {
    PolarChart,
    BarChart,
    PieChart,
    MultiBarsChart,
    ScreenBorder,
    BarsChart,
    SingleBarsChart,
    CustomTable
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      ecChart: {
        barX1: [],
        bar1: [],
        bar2: [],
        inverse1: false, // 是否反转
        bar3: [],
        bar4: [],
        inverse2: false // 是否反转
      },
      costChart: {
        barX1: [],
        bar1: [],
        bar2: [],
        inverse1: false, // 是否反转
        bar3: [],
        bar4: [],
        inverse2: false // 是否反转
      },
      pieChart: {
        bar1: [],
        total: '',
        RATE: 0, // 煤气回收率：RATE
        COEFF: 0, // 燃烧系数：COEFF
        RATIO: 0, // 铁水比：RATIO
        AIRCOE: 0 // 空气吸入系数：AIRCOE
      },
      pieMonthChart: {
        bar1: [],
        total: '',
        RATE: 0, // 煤气回收率：RATE
        COEFF: 0, // 燃烧系数：COEFF
        RATIO: 0, // 铁水比：RATIO
        AIRCOE: 0 // 空气吸入系数：AIRCOE
      },
      barChart: {
        barX1: [1, 2, 3],
        bar1: [
          {
            name: 10,
            data: [23, 34, 25]
          }
        ]
      },
      chartList: [],
      chartMonthList: [],
      unitObj: {
        转炉煤气回收: 'GJ/t',
        转炉蒸汽回收: 'm³/t',
        氧气消耗: 'm³/t',
        LF精炼电耗: 'kWh/t',
        连铸水耗: 'm³/t',
        除尘电耗: 'kWh/t'
      }
    }
  },
  computed: {
    showTime: function() {
      return '（' + this.$moment(this.cDate).format('MM月DD日') + '）'
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.loadData()
    }
  },
  mounted() {
    this.cDate = this.selectDate
    this.loadData()
  },
  methods: {
    loadData() {
      this.getGSMONTHTOS()
    },
    getGSMONTHTOS() {
      // 主要能介单耗
      post(ecAndCostB1, { flag: '0', date: this.cDate }).then(res => {
        this.ecChart.barX1 = res.data.ec.map(item => item.name)
        this.ecChart.bar1 = res.data.ec
          .filter(item => item.name === '工序能耗')
          .map(item => {
            this.ecChart.inverse1 = item.value < 0
            return {
              value: Math.abs(item.value),
              plan: Math.abs(item.targetValue),
              finished: Math.abs(item.value) > Math.abs(item.targetValue),
              unit: '',
              show: true
            }
          })
        this.ecChart.bar2 = [
          {
            value: 0,
            plan: 0,
            unit: '',
            show: false
          }
        ].concat(
          res.data.ec.filter(item => item.name === '产量').map(item => {
            return {
              value: item.value,
              plan: item.targetValue,
              finished: item.value > item.targetValue,
              unit: '',
              show: true
            }
          })
        )
        this.costChart.bar1 = res.data.cost.map(item => {
          this.costChart.inverse1 = item.value < 0
          return {
            value: item.value,
            plan: item.targetValue,
            finished: item.value > item.targetValue,
            unit: '',
            show: true
          }
        })
      })
      post(ecAndCostB1, { flag: '1', date: this.cDate }).then(res => {
        this.ecChart.bar3 = res.data.ec
          .filter(item => item.name === '工序能耗')
          .map(item => {
            this.ecChart.inverse2 = item.value < 0
            return {
              value: Math.abs(item.value),
              plan: Math.abs(item.targetValue),
              finished: Math.abs(item.value) > Math.abs(item.targetValue),
              unit: '',
              show: true
            }
          })
        this.ecChart.bar4 = [
          {
            value: 0,
            plan: 0,
            unit: '',
            show: false
          }
        ].concat(
          res.data.ec.filter(item => item.name === '产量').map(item => {
            return {
              value: item.value,
              plan: item.targetValue,
              finished: item.value > item.targetValue,
              unit: '',
              show: true
            }
          })
        )
        this.costChart.bar3 = res.data.cost.map(item => {
          this.costChart.inverse1 = item.value < 0
          return {
            value: item.value,
            plan: item.targetValue,
            finished: item.value > item.targetValue,
            unit: '',
            show: true
          }
        })
      })
      // 主要能介单耗
      post(FSMONTHTOSUsingPOST, { flag: '0', date: this.cDate }).then(res => {
        this.chartList = res.data.map(item => {
          return {
            name: item.name,
            barX: [],
            unit: this.unitObj[item.name] || '',
            bar: [
              {
                show: true,
                value: item.value,
                plan: item.targetValue,
                finished: item.value >= item.targetValue
              }
            ]
          }
        })
      })
      // 主要能介单耗月度
      post(FSMONTHTOSUsingPOST, { flag: '1', date: this.cDate }).then(res => {
        this.chartMonthList = res.data.map(item => {
          return {
            name: item.name,
            barX: [],
            unit: this.unitObj[item.name] || '',
            bar: [
              {
                show: true,
                value: item.value,
                plan: item.targetValue,
                finished: item.value >= item.targetValue
              }
            ]
          }
        })
      })
      // 谷平峰利用率
      post(ELEFGP, { flag: '0', date: this.cDate, name: 'S_26' }).then(res => {
        this.pieChart.total = res.eleConsumptionFGP.eoeRateMap.eoeRate
        this.pieChart.bar1 = res.eleConsumptionFGP.data.map(item => {
          item.name = item.name.replace('电', '')
          item.value = item.value.toFixed(0)
          return item
        })
      })
      // 谷平峰利用率（月度）
      post(ELEFGP, { flag: '1', date: this.cDate, name: 'S_26' }).then(res => {
        this.pieMonthChart.total = res.eleConsumptionFGP.eoeRateMap.eoeRate
        this.pieMonthChart.bar1 = res.eleConsumptionFGP.data.map(item => {
          item.name = item.name.replace('电', '')
          item.value = item.value.toFixed(0)
          return item
        })
      })
      // 煤气回收率
      post(GASRECOVERY, { flag: '0', date: this.cDate }).then(res => {
        Object.assign(this.pieChart, res.resultMap)
      })
      post(GASRECOVERY, { flag: '1', date: this.cDate }).then(res => {
        Object.assign(this.pieMonthChart, res.resultMap)
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
    position: relative;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  overflow: auto;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart-title {
    font-style: normal;
    font-weight: 900;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
  .chart {
    flex: 1;
    overflow: hidden;
    position: relative;
  }
  .chart-row {
    display: flex;
    justify-content: space-between;
  }
  .polar-text {
    position: absolute;
    left: 55%;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    line-height: 28px;
    color: #ffffff;
  }
}
</style>
