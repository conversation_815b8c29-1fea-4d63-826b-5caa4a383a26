<template>
  <!--订单详情查询-->
  <div>
    <el-dialog
      :title="'订单详情'"
      :width="'1450px'"
      :close-on-click-modal="false"
      :visible.sync="orderDetailVisible"
      @close="close()">
      <div class="search-wrapper">
        <el-form
          ref="form"
          :label-width="'80px'"
          :model="searchForm"
          size="small"
          inline
          @keyup.enter.native="getOrders(true)"
        >
          <el-form-item
            prop="standardNo"
          >
            <el-input
              v-model="searchForm.otherSteelType"
              suffix-icon="el-icon-search"
              clearable
              placeholder="输入国标钢种号"
              style="width:120px"
              type="text"
            />
          </el-form-item>
          <el-form-item
            prop="standardNo"
          >
            <el-input
              v-model="searchForm.standardNo"
              suffix-icon="el-icon-search"
              clearable
              placeholder="输入标准号"
              style="width:120px"
              type="text"
            />
          </el-form-item>
          <el-form-item
            prop="standardNo"
            label="厚度区间"
          >
            <el-input-number
              v-model="searchForm.thicknessDown"
              :min="0"
              :max="searchForm.thicknessUp"
              :controls="false"
              style="width: 70px"/>
            —
            <el-input-number
              v-model="searchForm.thicknessUp"
              :min="searchForm.thicknessDown || 0"
              :max="100000"
              :controls="false"
              style="width: 70px"/>
          </el-form-item>
          <el-form-item
            prop="widthDown"
            label="宽度区间"
          >
            <el-input-number
              v-model="searchForm.widthDown"
              :min="0"
              :max="searchForm.widthUp"
              :controls="false"
              style="width: 70px"/>
            —
            <el-input-number
              v-model="searchForm.widthUp"
              :min="searchForm.widthDown || 0"
              :max="100000"
              :controls="false"
              style="width: 70px"/>
          </el-form-item>
          <el-form-item
            prop="standardNo"
            label="机时产量"
          >
            <el-input-number
              v-model="searchForm.machineOutputDown"
              :min="0"
              :max="searchForm.machineOutputUp"
              :controls="false"
              style="width: 70px"/>
            —
            <el-input-number
              v-model="searchForm.machineOutputUp"
              :min="searchForm.machineOutputDown || 0"
              :max="100000"
              :controls="false"
              style="width: 70px"/>
          </el-form-item>
          <el-form-item
            prop="matrFl"
            label="保性能"
          >
            <el-select
              v-model="searchForm.matrFl"
              placeholder="请选择"
              clearable
              style="width: 100px">
              <el-option
                :label="'是'"
                :value="'Y'"/>
              <el-option
                :label="'否'"
                :value="'N'"/>
            </el-select>
          </el-form-item>
          <el-form-item
            prop="dealResult"
            label="订单适配"
          >
            <el-select
              v-model="searchForm.dealResult"
              placeholder="请选择"
              clearable
              style="width: 120px">
              <el-option
                v-for="(item, index) in matchType"
                :key="index"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item
            prop="dealResult"
            label="是否可信"
          >
            <el-select
              v-model="searchForm.moReliable"
              placeholder="请选择"
              clearable
              style="width: 120px">
              <el-option
                v-for="(item, index) in moReliableList"
                :key="index"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item
            prop="dealResult"
            label=""
          >
            <el-checkbox 
              v-model="searchForm.modifiedOrdWgtFlag" 
              :true-label="true" 
              :false-label="''">已修改</el-checkbox>
          </el-form-item>
          <el-button
            icon="el-icon-search"
            type="primary"
            @click="getOrders"
          >搜索
          </el-button>
        </el-form>
      </div>
      <div
        class="search-wrapper"
        style="position: relative">
        <el-tabs
          v-model="orderState"
          @tab-click="handleTabClick">
          <el-tab-pane
            :name="'0'"
            label="正常订单"/>
          <el-tab-pane
            :name="'1'"
            label="已删除订单"/>
        </el-tabs>
        <div
          style="position: absolute; right: 0; top: 0;">
          <el-button
            v-if="orderState === '0' && canEdit"
            type="primary"
            @click="handleUpdate">批量更新订单重量</el-button>
          <el-button
            v-if="orderState === '0' && canEdit"
            type="primary"
            @click="handleRstore">一键恢复</el-button>
          <el-button
            v-if="orderState === '0' && canEdit"
            type="warning"
            @click="handleDelete">批量删除</el-button>
        </div>
      </div>
      <div class="table">
        <el-table
          :data="orderProductionData"
          :row-class-name="rowClass"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange">
          <el-table-column
            v-if="orderState === '0'"
            type="selection"
            width="45"/>
          <el-table-column
            prop="orderId"
            label="订单号"
            width="105"/>
          <el-table-column
            prop="orderItem"
            label="项次号"
            width="60"/>
          <el-table-column
            prop="custDelToDate"
            label="交货日期"
            width="75"/>
          <el-table-column
            prop="otherSteelType"
            label="国标钢种"
            width="75"/>
          <el-table-column
            prop="standardNo"
            label="标准号"
            width="110"/>
          <el-table-column
            prop="dealResult"
            label="保性能"
            width="60">
            <template v-slot="{ row }">
              <el-tag
                v-if="row.matrFl !== null"
                :type="'info'"
                disable-transitions>{{ getDict(row.matrFl, 'matrFlList').label }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="dealResult"
            label="是否可信"
            width="70">
            <template v-slot="{ row }">
              <el-tag
                v-if="row.moReliable !== null"
                :type="getDict(row.moReliable, 'moReliableList').tag"
                disable-transitions>{{ getDict(row.moReliable, 'moReliableList').label }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="thickness"
            label="订单厚度"
            width="70"/>
          <el-table-column
            prop="width"
            label="订单宽度"
            width="70"/>
          <el-table-column
            prop="dealResult"
            label="适配结果"
            width="88">
            <template v-slot="{ row }">
              <el-tag
                v-if="row.dealResult !== null"
                :type="getDict(row.dealResult, 'matchType').tag"
                disable-transitions>{{ getDict(row.dealResult, 'matchType').label }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="oriOrdWgt"
            label="原始订单重量"/>
          <el-table-column
            prop="unProdOrdWgt"
            label="未生产订单重量"/>
          <el-table-column
            prop="thkAdapt"
            label="厚度适配"
            width="70"/>
          <el-table-column
            prop="thkDifRate"
            label="厚度偏差率"
            width="85"/>
          <el-table-column
            prop="widAdapt"
            label="宽度适配"
            width="70"/>
          <el-table-column
            prop="widDifRate"
            label="宽度偏差率"
            width="85"/>
          <el-table-column
            prop="orderWeight"
            label="订单重量"
            width="100">
            <template v-slot="{ row }">
              <div style="white-space: nowrap">
                <el-input
                  v-model="row.orderWeight"
                  @change="userDefChange($event, row)"/>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="userDefMachineOutput"
            label="用户确认机时产量"
            width="170">
            <template v-slot="{ row }">
              <template
                v-if="orderState === '0'">
                <el-tooltip
                  class="item"
                  effect="dark"
                  placement="top">
                  <span
                    :class="{'red': !row.moReliable}"
                    class="point"/>
                  <div
                    slot="content"
                    v-html="'历史生产重量：' + (row.mergeSlabWgt || '-') + '<br/>历史生产次数：' + (row.mergeSlabNum || '-')"/>
                </el-tooltip>
                <span class="input-output">
                  <el-input
                    v-model="row.userDefMachineOutput"
                    @change="userDefChange($event, row)"
                  >
                    <el-button
                      slot="append"
                      icon="el-icon-search"
                      @click="orderMachineOutput(row)"/>
                  </el-input>
                </span>
              </template>
              <template v-else>
                {{ row.userDefMachineOutput }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            prop="productionTime"
            label="生产耗时"
            width="70"/>
          <el-table-column
            label="操作"
            width="60">
            <template v-slot="{ row }">
              <el-button
                v-if="orderState === '1'"
                type="text"
                size="mini"
                @click.native.stop="saveOrderFu($event, row)">
                恢复
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <br>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="orderProductionForm.page"
            :page-size="orderProductionForm.size"
            :page-sizes="[10, 20, 30, 40]"
            :total="orderProductionForm.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </el-dialog>
    <!--手持订单机时产量查询-->
    <el-dialog
      :width="'1400px'"
      :close-on-click-modal="false"
      :append-to-body="true"
      :visible.sync="orderMachineOutputVisible">
      <div
        v-if="orderMachineOutputVisible"
        class="search-wrapper" >
        <el-form
          v-if="factory !== 3"
          ref="form"
          :label-width="'80px'"
          :model="orderMachineOutputForm.searchForm"
          size="small"
          inline
          @submit.native.prevent="getOrderMachineOutput(true)"
        >
          <el-form-item
            prop="standardNo"
          >
            <el-input
              v-model="orderMachineOutputForm.searchForm.standardNo"
              suffix-icon="el-icon-search"
              clearable
              placeholder="请输入标准号"
              style="width:120px"
              type="text"
            />
          </el-form-item>
          <el-form-item
            prop="standardNo"
            label="厚度区间"
          >
            <el-input-number
              v-model="orderMachineOutputForm.searchForm.thicknessDown"
              :min="0"
              :max="orderMachineOutputForm.searchForm.thicknessUp"
              :controls="false"
              style="width: 80px"/>
            —
            <el-input-number
              v-model="orderMachineOutputForm.searchForm.thicknessUp"
              :min="orderMachineOutputForm.searchForm.thicknessDown || 0"
              :max="100000"
              :controls="false"
              style="width: 80px"/>
          </el-form-item>
          <el-form-item
            prop="standardNo"
            label="宽度区间"
          >
            <el-input-number
              v-model="orderMachineOutputForm.searchForm.widthDown"
              :min="0"
              :max="orderMachineOutputForm.searchForm.widthUp"
              :controls="false"
              style="width: 80px"/>
            —
            <el-input-number
              v-model="orderMachineOutputForm.searchForm.widthUp"
              :min="orderMachineOutputForm.searchForm.widthDown || 0"
              :max="100000"
              :controls="false"
              style="width: 80px"/>
          </el-form-item>
          <el-form-item
            prop="matrFl"
            label="保性能"
          >
            <el-select
              v-model="orderMachineOutputForm.searchForm.matrFl"
              placeholder="请选择"
              clearable
              style="width: 100px">
              <el-option
                :label="'是'"
                :value="'Y'"/>
              <el-option
                :label="'否'"
                :value="'N'"/>
            </el-select>
          </el-form-item>
          <el-form-item
            prop="factory"
            label="产线"
          >
            <el-select
              v-model="orderMachineOutputForm.searchForm.factory"
              placeholder="请选择"
              clearable
              style="width: 100px">
              <el-option
                v-for="(item, index) in factoryList"
                :key="index"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-button
            icon="el-icon-search"
            type="primary"
            @click="getOrderMachineOutput"
          >搜索
          </el-button>
        </el-form>
      </div>
      <div class="table">
        <el-table
          :data="orderMachineOutputForm.data"
          :row-class-name="rowClass"
          border
          style="width: 100%">
          <el-table-column
            type="index"
            label="序号"/>
          <el-table-column
            prop="steelType"
            label="产线">
            <template v-slot="{ row }">
              {{ getDict(row.factory, 'factoryList').label }}
            </template>
          </el-table-column>
          <el-table-column
            prop="standardNo"
            label="标准号"/>
          <el-table-column
            prop="thickness"
            label="订单厚度"
            width=""/>
          <el-table-column
            prop="width"
            label="订单宽度"/>
          <el-table-column
            prop="dealResult"
            label="保性能">
            <template v-slot="{ row }">
              <el-tag
                v-if="row.matrFl !== null"
                :type="getDict(row.matrFl, 'matrFlList').type"
                disable-transitions>{{ getDict(row.matrFl, 'matrFlList').label }}</el-tag>
            </template>
          </el-table-column>
          <template v-if="factory === 3">
            <el-table-column
              prop="slabThk"
              label="板坯厚度"/>
            <el-table-column
              prop="avgSlabLen"
              label="板坯平均长度"/>
            <el-table-column
              prop="avgMachineOutput"
              label="平均机时产量">
              <template v-slot="{ row }">
                <span
                  style="cursor: pointer"
                  @click="setMachineOutput(row.avgMachineOutput, row)">{{ row.avgMachineOutput }}</span>
              </template>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column
              prop="avgInMat"
              label="机时产量-历史均值">
              <template v-slot="{ row }">
                <span
                  style="cursor: pointer"
                  @click="setMachineOutput(row.avgInMat)">{{ row.avgInMat }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="bestInMat"
              label="机时产量-历史最佳">
              <template v-slot="{ row }">
                <span
                  style="cursor: pointer"
                  @click="setMachineOutput(row.bestInMat)">{{ row.bestInMat }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="bestInRolling3m"
              label="机时产量-滚动3个月最佳">
              <template v-slot="{ row }">
                <span
                  style="cursor: pointer"
                  @click="setMachineOutput(row.bestInRolling3m)">{{ row.bestInRolling3m }}</span>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <br>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="orderMachineOutputForm.page"
            :page-size="orderMachineOutputForm.size"
            :page-sizes="[10, 20, 30, 40]"
            :total="orderMachineOutputForm.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleMachineSizeChange"
            @current-change="handleMachineCurrentChange"
          />
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import {
  deleteOrders,
  findDetailedOrders,
  findMachineOutputByConditions,
  findOrders,
  findRollingAvgMachineoutput,
  saveOrder,
  updateOrder,
  updateOrders,
  userMachineOutputSave
} from '@/api/kpi'
import { ENUM } from '@/lib/Constant'

export default {
  name: 'orderDetail',
  // eslint-disable-next-line vue/require-prop-types
  props: ['factory', 'period', 'orderType', 'canEdit'],
  data: () => {
    return {
      searchForm: {},
      moReliableList: ENUM.moReliableList,
      matchType: ENUM.matchType,
      // 性能要求列表
      matrFlList: ENUM.matrFlList,
      orderProductionData: [],
      multipleSelection: [],
      orderDetailVisible: false,
      orderProductionForm: {
        newOrder: false,
        handleDateRange: null,
        page: 1,
        size: 10,
        total: 0
      },
      factoryList: ENUM.factoryListForecast,
      orderState: '0',
      orderMachineOutputVisible: false, // 机时产量查询
      orderMachineOutputForm: {
        searchForm: {
          standardNo: '',
          steelType: '',
          id: ''
        },
        data: [],
        page: 1,
        size: 10,
        total: 0,
        edit: {}
      }
    }
  },
  methods: {
    close() {
      this.searchForm = {}
      this.$emit('update', true)
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.orderProductionForm.size = val
      this.getOrders()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.orderProductionForm.page = val
      this.getOrders()
    },
    getOrders(reset = false) {
      if (reset) {
        this.orderProductionForm.page = 1
        this.orderProductionForm.size = 10
      }
      //orderType： 手持： 1， 手补：2， 中间品：3  历史所有： 0
      post(
        findDetailedOrders,
        Object.assign({}, this.searchForm, {
          period: this.period,
          factory: this.factory,
          orderType: this.orderType,
          orderState: this.orderState,
          page: this.orderProductionForm.page - 1,
          size: this.orderProductionForm.size
        })
      ).then(res => {
        this.orderProductionData = res.data.content || []
        this.orderProductionForm.total = res.data.totalElements
        // this.getMachineProduction(name)
      })
    },

    getDict(value, list) {
      const match = this[list].find(item => item.value == value)
      return match ? match : {}
    },
    // 更新订单
    userDefChange(e, row) {
      if (!row.orderWeight || !row.userDefMachineOutput) return
      row.orderState = 0
      post(updateOrder, {
        id: row.id,
        newWeight: row.orderWeight,
        newUserDefMachineOutput: row.userDefMachineOutput
      }).then(res => {
        if (res.success) {
          this.$message.success('保存订单成功')
          this.$emit('modify', res.data)
          this.getOrders()
        }
      })
    },
    // 更新订单
    saveOrderFu(e, row) {
      row.orderState = 0
      post(saveOrder, row).then(res => {
        this.$message.success('保存订单成功')
        this.getOrders()
      })
    },
    // 一键恢复
    handleRstore() {
      if (!this.multipleSelection.length)
        return this.$message.warning('请先选择订单！')
      this.$confirm('确认恢复所选订单重量?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          post(updateOrders, {
            ids: this.multipleSelection.map(item => item.id),
            orderWeight: null
          }).then(res => {
            if (res.success) {
              this.getOrders()
              this.$message.success('一键恢复成功')
            }
          })
        })
        .catch(() => {})
    },
    // 批量更新订单重量
    handleUpdate() {
      console.log(this.multipleSelection)
      if (!this.multipleSelection.length)
        return this.$message.warning('请先选择订单！')
      // /productionForecast/deleteOrders
      this.$prompt('请输入要修改的重量', '输入', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(({ value }) => {
          post(updateOrders, {
            ids: this.multipleSelection.map(item => item.id),
            orderWeight: value
          }).then(res => {
            if (res.success) {
              this.getOrders()
              this.$message.success('批量更新成功')
            }
          })
        })
        .catch(() => {})
    },
    // 批量删除
    handleDelete() {
      if (!this.multipleSelection.length)
        return this.$message.warning('请先选择订单！')
      // /productionForecast/deleteOrders
      post(deleteOrders, this.multipleSelection.map(item => item.id)).then(
        res => {
          if (res.success) {
            this.$message.success('批量删除成功')
            this.getOrders()
          } else {
            this.$message.warning('批量删除失败')
          }
        }
      )
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleTabClick() {
      //
      this.getOrders(true)
    },

    orderMachineOutput(row = {}) {
      this.orderMachineOutputForm.edit = row.id ? row : {}
      this.orderMachineOutputForm.searchForm.id = row.id
      this.orderMachineOutputForm.searchForm.standardNo = row.id
        ? row.standardNo
        : ''
      this.orderMachineOutputForm.searchForm.matrFl = row.id ? row.matrFl : ''
      this.orderMachineOutputForm.searchForm.factory = row.id ? row.factory : ''
      this.orderMachineOutputVisible = true
      this.getOrderMachineOutput(true)
    },

    getOrderMachineOutput(reset = false) {
      if (reset) {
        this.orderMachineOutputForm.page = 1
        this.orderMachineOutputForm.size = 10
      }
      post(
        this.factory === 3
          ? findRollingAvgMachineoutput
          : findMachineOutputByConditions,
        Object.assign({}, this.orderMachineOutputForm.searchForm, {
          page: this.orderMachineOutputForm.page - 1,
          size: this.orderMachineOutputForm.size
        })
      ).then(res => {
        this.orderMachineOutputForm.data = res.data.content || []
        this.orderMachineOutputForm.total = res.data.totalElements
        // this.getMachineProduction(name)
      })
    },
    handleMachineSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.orderMachineOutputForm.size = val
      this.getOrderMachineOutput()
    },
    handleMachineCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.orderMachineOutputForm.page = val
      this.getOrderMachineOutput()
    },

    // 快捷设置机时产量no
    setMachineOutput(num, row = {}) {
      if (!this.canEdit) return
      if (this.orderMachineOutputForm.edit.id) {
        this.orderMachineOutputForm.edit.userDefMachineOutput = num
        this.userDefChange({}, this.orderMachineOutputForm.edit)
        this.orderMachineOutputVisible = false
      }
      if (this.factory === 3) {
        this.saveUserMachineOutput(row)
      }
    },
    // 保存用户选择机时产量进入中间表
    saveUserMachineOutput(row) {
      post(userMachineOutputSave, row).then(res => {})
    },
    rowClass(row) {
      let classStr = ''
      if (row.row.manualPick && row.row.manualPick === true) {
        classStr += 'manual-pick'
      }
      if (row.row.modifiedOrdWgtFlag && row.row.modifiedOrdWgtFlag === true) {
        classStr += 'ordwgt-mod'
      }
      return classStr
    }
  }
}
</script>

<style scoped lang="less">
.point {
  display: inline-block;
  height: 22px;
  width: 22px;
  background: #b5e61d;
  border: 3px solid #28b349;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 5px;
  &.red {
    background: #ff7f27;
    border: 3px solid #ed1c24;
  }
}
.input-output {
  display: inline-block;
  width: 110px;
  vertical-align: middle;
}
/deep/ .manual-pick {
  background-color: #fff8cc;
}
/deep/ .ordwgt-mod {
  background-color: #c7eeff;
}
</style>
