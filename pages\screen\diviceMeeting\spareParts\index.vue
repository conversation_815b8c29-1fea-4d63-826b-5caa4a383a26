<template>
  <div class="content">
    <div class="content-item top">
      <screen-border title="库存">
        <el-row
          :gutter="32"
          class="full-height">
          <el-col
            :span="24"
            class="full-height">
            <div class="grid-content bg-purple full-height">
              <div class="chart-wrapper">
                <div
                  class="chart">
                  <bars-chart
                    :bar-width="25"
                    :unit="'万元'"
                    :chart-data="inventoryRight.bar1"
                    :x-data="inventoryRight.barX1"/>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </screen-border>
    </div>
    <div class="content-hold"/>
    <div class="content-item top">
      <screen-border :title="'费用细项'">
        <div class="chart-wrapper">
          <div
            class="chart">
            <!-- <bars-chart
              :bar-width="40"
              :unit="'万元'"
              :tooltipbg="true"
              :chart-data="cost.bar1"
              :x-data="cost.barX1"/>
            /> -->
            <FoldedColumnChart 
              :chart-data="cost2"
              :color="['#61a4e4','#ffa958','#f56c6c']"
              :bar-width="40"
              :unit="'元/吨'"
              :x-data="cost2X" />
          </div>
        </div>
      </screen-border>
    </div>
  </div>
</template>

<script>
import BarsChart from '@/pages/screen/diviceMeeting/component/bars-chart'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border.vue'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import FoldedColumnChart from '@/pages/screen/diviceMeeting/component/FoldedColumn-chart'
import {
  bigPurchaseSys,
  expenseDetail,
  FindSparePartsInventory,
  getFourAmount2,
  maintenanceCost,
  queryNotDealDangerInfo
} from '@/api/device'
import { post, get } from '@/lib/Util'
import * as _ from 'lodash'
import { math } from '@/lib/Math'
export default {
  name: 'spareParts',
  components: { BarsChart, ScreenBorder, FoldedColumnChart },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      inventoryLift: {
        bar1: [
          {
            name: '3年以内',
            data: [],
            barGap: '0'
          },
          {
            name: '3-5年',
            data: [],
            barGap: '0'
          },
          {
            name: '5年以上',
            data: [],
            barGap: '0'
          }
        ],
        barX1: ['板材事业部']
      },
      cost2X: [],
      cost2: [
        {
          name: '事业部考核',
          type: 'line',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        },
        {
          name: '实际单耗',
          type: 'line',
          yAxisIndex: 1,
          barGap: 0,
          smooth: true,
          data: []
        },
        {
          name: '生产',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        },
        {
          name: '维修',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        },
        {
          name: '轧辊',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        }
      ],
      inventoryRight: {
        bar1: [],
        barX1: [
          '板材事业部',
          '第一炼钢厂',
          '中厚板卷厂',
          '宽厚板厂',
          '中板厂',
          '金石材料厂',
          '金润智能工厂'
        ]
      },
      cost: {
        bar1: [],
        barX1: []
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.$nextTick(item => {
        this.getCostDetail()
        this.getCost2()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.getCost()
    this.getCostDetail()
    this.getCost2()
  },
  methods: {
    //费用
    getCost() {
      post(bigPurchaseSys).then(res => {
        // console.log(res)
        const data = res.backMsg
        const categories = {
          板材事业部机关: {
            '6个月以内': 0,
            '6个月~1年': 0,
            '1~2年': 0,
            '2~3年': 0,
            '3~4年': 0,
            '4~5年': 0,
            '5年以上': 0
          },
          第一炼钢厂: {
            '6个月以内': 0,
            '6个月~1年': 0,
            '1~2年': 0,
            '2~3年': 0,
            '3~4年': 0,
            '4~5年': 0,
            '5年以上': 0
          },
          中厚板卷厂: {
            '6个月以内': 0,
            '6个月~1年': 0,
            '1~2年': 0,
            '2~3年': 0,
            '3~4年': 0,
            '4~5年': 0,
            '5年以上': 0
          },
          宽厚板厂: {
            '6个月以内': 0,
            '6个月~1年': 0,
            '1~2年': 0,
            '2~3年': 0,
            '3~4年': 0,
            '4~5年': 0,
            '5年以上': 0
          },
          中板厂: {
            '6个月以内': 0,
            '6个月~1年': 0,
            '1~2年': 0,
            '2~3年': 0,
            '3~4年': 0,
            '4~5年': 0,
            '5年以上': 0
          },
          金石材料厂: {
            '6个月以内': 0,
            '6个月~1年': 0,
            '1~2年': 0,
            '2~3年': 0,
            '3~4年': 0,
            '4~5年': 0,
            '5年以上': 0
          },
          金润智能制造厂: {
            '6个月以内': 0,
            '6个月~1年': 0,
            '1~2年': 0,
            '2~3年': 0,
            '3~4年': 0,
            '4~5年': 0,
            '5年以上': 0
          }
        }

        for (let i = 0; i < data.length; i++) {
          const dept = data[i].TWOCOL
          const type = data[i].TYPE
          const amt = data[i].ENDAMT ? (data[i].ENDAMT / 10000).toFixed(1) : 0

          if (dept in categories && type in categories[dept]) {
            categories[dept][type] = amt
          }
        }
        Object.keys(categories['板材事业部机关']).forEach(type => {
          ;[
            '第一炼钢厂',
            '中厚板卷厂',
            '宽厚板厂',
            '中板厂',
            '金石材料厂',
            '金润智能制造厂'
          ].forEach(dept => {
            categories['板材事业部机关'][type] = math.add(
              categories['板材事业部机关'][type],
              Number(categories[dept][type])
            )
          })
        })
        // this.inventoryLift.bar1[0].data =
        //   categories['板材事业部机关']['三年以内']
        // this.inventoryLift.bar1[1].data = categories['板材事业部机关']['3~5年']
        // this.inventoryLift.bar1[2].data =
        //   categories['板材事业部机关']['5年以上']
        console.log(categories, categories['板材事业部机关'])
        this.inventoryRight.bar1 = [
          {
            name: '6个月以内',
            data: [
              categories['板材事业部机关']['6个月以内'] || 0,
              categories['第一炼钢厂']['6个月以内'] || 0,
              categories['中厚板卷厂']['6个月以内'] || 0,
              categories['宽厚板厂']['6个月以内'] || 0,
              categories['中板厂']['6个月以内'] || 0,
              categories['金石材料厂']['6个月以内'] || 0,
              categories['金润智能制造厂']['6个月以内'] || 0
            ],
            barGap: '0'
          },
          {
            name: '6个月~1年',
            data: [
              categories['板材事业部机关']['6个月~1年'] || 0,
              categories['第一炼钢厂']['6个月~1年'] || 0,
              categories['中厚板卷厂']['6个月~1年'] || 0,
              categories['宽厚板厂']['6个月~1年'] || 0,
              categories['中板厂']['6个月~1年'] || 0,
              categories['金石材料厂']['6个月~1年'] || 0,
              categories['金润智能制造厂']['6个月~1年'] || 0
            ],
            barGap: '0'
          },
          {
            name: '1~2年',
            data: [
              categories['板材事业部机关']['1~2年'] || 0,
              categories['第一炼钢厂']['1~2年'] || 0,
              categories['中厚板卷厂']['1~2年'] || 0,
              categories['宽厚板厂']['1~2年'] || 0,
              categories['中板厂']['1~2年'] || 0,
              categories['金石材料厂']['1~2年'] || 0,
              categories['金润智能制造厂']['1~2年'] || 0
            ],
            barGap: '0'
          },
          {
            name: '2~3年',
            data: [
              categories['板材事业部机关']['2~3年'] || 0,
              categories['第一炼钢厂']['2~3年'] || 0,
              categories['中厚板卷厂']['2~3年'] || 0,
              categories['宽厚板厂']['2~3年'] || 0,
              categories['中板厂']['2~3年'] || 0,
              categories['金石材料厂']['2~3年'] || 0,
              categories['金润智能制造厂']['2~3年'] || 0
            ],
            barGap: '0'
          },
          {
            name: '3~4年',
            data: [
              categories['板材事业部机关']['3~4年'] || 0,
              categories['第一炼钢厂']['3~4年'] || 0,
              categories['中厚板卷厂']['3~4年'] || 0,
              categories['宽厚板厂']['3~4年'] || 0,
              categories['中板厂']['3~4年'] || 0,
              categories['金石材料厂']['3~4年'] || 0,
              categories['金润智能制造厂']['3~4年'] || 0
            ],
            barGap: '0'
          },
          {
            name: '4~5年',
            data: [
              categories['板材事业部机关']['4~5年'] || 0,
              categories['第一炼钢厂']['4~5年'] || 0,
              categories['中厚板卷厂']['4~5年'] || 0,
              categories['宽厚板厂']['4~5年'] || 0,
              categories['中板厂']['4~5年'] || 0,
              categories['金石材料厂']['4~5年'] || 0,
              categories['金润智能制造厂']['4~5年'] || 0
            ],
            barGap: '0'
          },
          {
            name: '5年以上',
            data: [
              categories['板材事业部机关']['5年以上'] || 0,
              categories['第一炼钢厂']['5年以上'] || 0,
              categories['中厚板卷厂']['5年以上'] || 0,
              categories['宽厚板厂']['5年以上'] || 0,
              categories['中板厂']['5年以上'] || 0,
              categories['金石材料厂']['5年以上'] || 0,
              categories['金润智能制造厂']['5年以上'] || 0
            ],
            barGap: '0'
          }
        ]
        console.log(this.inventoryRight.bar1)
      })
    },
    getFactoryName(name) {
      switch (name) {
        case '中厚板卷厂':
          return '中厚板卷厂'
        case '金石材料厂':
          return '金石材料厂'
        case '金润厂':
          return '金润智能工厂'
        case '金润智能制造厂':
          return '金润智能工厂'
        case '第一炼钢厂':
          return '第一炼钢厂'
        case '中板厂':
          return '中板厂'
        case '宽厚板厂':
          return '宽厚板厂'
        case '板材事业部机关':
          return '板材事业部机关'
        case '事业部':
          return '事业部'
        default:
          return name
      }
    },
    getCost2() {
      const params = {
        yearMonth: this.$moment(this.cDate)
          .subtract(1, 'month')
          .format('yyyy-MM')
      }
      post(maintenanceCost, params).then(res => {
        if (res.data.length > 0) {
          this.cost2[0].data = []
          this.cost2[1].data = []
          //   this.cost2X = res.data.map(item =>
          //     this.getFactoryName(item.factoryNo)
          //   )
          res.data.forEach(item => {
            this.cost2[0].data.push(Number(item.divPlan).toFixed(3))
            this.cost2[1].data.push(Number(item.actualUnitConsIns).toFixed(3))
          })
        }
      })
    },

    getCostDetail() {
      post(expenseDetail, {
        yearBudget: this.$moment()
          .subtract(1, 'month')
          .startOf('month')
          .format('YYYY-MM')
      }).then(res => {
        this.cost2X = res.data.list.map(item =>
          this.getFactoryName(item.DEPTNAME)
        )
        this.cost2[2].data = []
        this.cost2[3].data = []
        this.cost2[4].data = []
        // this.cost2[2].bar1 = [
        //   {
        //     name: '生产',
        //     data: res.data.list.map(item => item.SJSC),
        //     barGap: '0'
        //   },
        //   {
        //     name: '维修',
        //     data: res.data.list.map(item => item.SJWX),
        //     barGap: '0'
        //   },
        //   {
        //     name: '轧辊',
        //     data: res.data.list.map(item => item.SJNC),
        //     barGap: '0'
        //   }
        // ]
        res.data.list.forEach(item => {
          this.cost2[2].data.push(Number(item.SJSC).toFixed(3))
          this.cost2[3].data.push(Number(item.SJWX).toFixed(3))
          this.cost2[4].data.push(Number(item.SJNC).toFixed(3))
        })
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    font-size: 0;
    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.green {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
