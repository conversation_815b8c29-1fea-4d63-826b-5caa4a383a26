<!--项目汇报-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border title="项目汇报">
                <template v-slot:headerRight>
                  <span
                    class="screen-btn"
                    @click="clickAddProject">
                    <el-icon class="el-icon-edit-outline"/>
                    新增
                  </span>
                </template>
                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="ProjectData.loading"
                    :data="ProjectData.showGridData"
                    border>
                    <el-table-column
                      show-overflow-tooltip
                      width="60"
                      label="序号">
                      <template slot-scope="scope">
                        <div>{{ scope.$index+1 }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="项目">
                      <template slot-scope="scope">
                        <div>{{ scope.row.item }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="itemclassList"
                      :filter-method="filterMethod"
                      property="itemClass"
                      label="类别">
                      <template slot-scope="scope">
                        <div>{{ scope.row.itemClass }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="上月进度">
                      <template slot-scope="scope">
                        <div>{{ scope.row.yesterdayDesc }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="本月进度">
                      <template slot-scope="scope">
                        <div>{{ scope.row.description }}</div>
                      </template>
                    </el-table-column>
                    <!-- <el-table-column
                      label="实际进度情况">
                      <template slot-scope="scope">
                        <div>{{ scope.row.description }}</div>
                      </template>
                    </el-table-column> -->
                    <el-table-column
                      label="计划日期"
                      width="130">
                      <template slot-scope="scope">
                        <div>{{ scope.row.dateTime }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="备注"
                      width="130">
                      <template slot-scope="scope">
                        <div>{{ scope.row.remarks }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property=""
                      width="150"
                      label="操作">
                      <template v-slot="scope">
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectItem(scope.row)">查看详情</span>
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectDeleteItem(scope.row)">删除</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--项目汇报新增修改-->
    <el-dialog
      v-loading="ProjectData.loading"
      :visible.sync="ProjectData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="项目汇报">
      <template v-slot:title>
        <div class="custom-dialog-title">
          项目汇报
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">项目</div>
          <el-input
            v-model="projectItem.item"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>

        <div class="dialog-cell">
          <div class="dialog-cell-title">项目进程</div>
          <el-steps
            :active="projectItem.detailList&&projectItem.detailList.length"
            style="margin-top: 5px;margin-left: 20px">
            <el-step
              v-for="item in projectItem.detailList"
              :key="item.id"
              :title="item.setDate"
              :description="item.description"/>
          </el-steps>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">类别</div>
          <!-- <el-input
            v-model="projectItem.itemClass"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
         
            class="dialog-cell-input"/> -->
          <el-select
            v-model="projectItem.itemClass"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in itemclassList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">昨日进度</div>
          <el-input
            v-model="projectItem.yesterdayDesc"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            disabled
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">今日计划</div>
          <el-input
            v-model="projectItem.description"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">计划日期</div>
          <el-date-picker
            v-model="projectItem.dateTime"
            :clearable="false"
            :size="'mini'"
            :value-format="'yyyy-MM-dd'"
            class="screen-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">备注</div>
          <el-input
            v-model="projectItem.remarks"
            :rows="8"
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="addProjectData()">
          确定
        </span>
      </div>
    </el-dialog>
    <!--项目汇报新增修改删除-->
    <!--    <el-dialog
      :visible.sync="ProjectData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="项目汇报">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('ProjectData')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importProjectData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportProjectData">
              导出
            </span>
            <span
              class="screen-btn"
              @click="saveProjectData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          项目汇报
        </div>
      </template>
      <el-form>
        <el-table
          v-loading="ProjectData.loading"
          :data="ProjectData.gridData"
          :height="'calc(100vh - 345px)'"
          border>
          <el-table-column
            property="itemName"
            label="项目">
            <template v-slot="{ row }">
              <el-input v-model="row.item" />
            </template>
          </el-table-column>
          <el-table-column
            property="content"
            label="昨日进度">
            <template v-slot="{ row }">
              <el-input v-model="row.process" />
            </template>
          </el-table-column>
          <el-table-column
            property="dealPlan"
            label="今日计划">
            <template v-slot="{ row }">
              <el-input v-model="row.todayPlan" />
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="备注">
            <template v-slot="{ row }">
              <el-input v-model="row.remarks" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                class="screen-btn"
                @click="delGridData($index, 'ProjectData')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          class="screen-btn"
          @click="addGridData('ProjectData')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>-->

    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="上月导入日期库存">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  firstMeetingProject1,
  firstMeetingProject2,
  firstMeetingProject3
} from '@/api/firstMeeting'
export default {
  name: 'ProjectPage',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      ProjectData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      projectItem: {},
      itemclassList: [
        {
          text: '工艺设备',
          value: '工艺设备'
          //  type: 'GYSB'
        },
        {
          text: '自动化',
          value: '自动化'
          //  type: 'ZDH'
        },
        {
          text: '节能降耗',
          value: '节能降耗'
          //  type: 'JNJH'
        },
        {
          text: '装备升级',
          value: '装备升级'
          //  type: 'ZBSJ'
        },
        {
          text: '安全提升',
          value: '安全提升'
          //  type: 'AQTS'
        }
      ]
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getProjectData()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    filterMethod(value, row, column) {
      const property = column['property']
      console.log('aaa', property)
      return row[property] === value
    },
    clickAddProject() {
      this.projectItem = {
        item: '',
        process: '',
        todayPlan: '',
        description: '',
        dateTime: this.cDate,
        modifyDetailList: [],
        detailList: [],
        remarks: '',
        setDate: this.cDate
      }
      this.ProjectData.dialogVisible = true
    },
    //隐患点击查看详情
    clickProjectItem(row) {
      this.projectItem = JSON.parse(JSON.stringify(row))
      this.ProjectData.dialogVisible = true
    },
    //隐点击查看详情
    clickProjectDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteProject(row.id)
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    //新增/修改
    addProjectData() {
      let modifyDetailList = this.projectItem.modifyDetailList
      // let detailList = this.projectItem.detailList
      let dateTime = this.projectItem.dateTime
      let description = this.projectItem.description
      if (description === null || description.length === 0) {
        this.$message.warning('请输入计划内容！')
        return
      }
      if (dateTime === null || dateTime.length === 0) {
        this.$message.warning('请选择计划日期！')
        return
      }
      const detailItem = modifyDetailList.find(
        item => item.setDate === dateTime
      )
      if (detailItem) {
        detailItem.description = this.projectItem.description
      } else {
        modifyDetailList.push({
          description: this.projectItem.description,
          setDate: this.projectItem.dateTime
        })
      }
      // const params = [this.hiddenItem]
      // 保存钢铁产量信息
      const params = {
        date: this.cDate,
        data: [this.projectItem]
      }
      this.ProjectData.loading = true
      post(firstMeetingProject2, params)
        .then(res => {
          if (res.success) {
            this.$notify.success('操作成功！')
            this.ProjectData.dialogVisible = false
            this.getProjectData()
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    //删除隐患
    deleteProject(id) {
      const params = [
        {
          id: id
        }
      ]
      post(firstMeetingProject3, params).then(res => {
        if (res.success) {
          this.$notify.success('删除成功！')
          this.getProjectData()
        }
      })
    },
    calculateHeight() {
      this.ProjectData.maxHeight = this.$refs.table1.offsetHeight
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          item: 'A',
          process: 'B',
          todayPlan: 'C',
          remarks: 'D'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.ProjectData.gridData = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    exportProjectData() {
      const data = [
        {
          item: '项目',
          process: '昨日进度',
          itemClass: '类别',
          todayPlan: '今日计划',
          remarks: '备注'
        }
      ].concat(
        _.cloneDeep(this.ProjectData.gridData).map(item => {
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `项目汇报（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    getProjectData() {
      this.ProjectData.loading = true
      post(firstMeetingProject1, {
        time: this.cDate
      })
        .then(res => {
          this.ProjectData.showGridData = res.data.map(item => {
            let detailList = item.modifyDetailList.filter(
              detailItem =>
                new Date(detailItem.setDate).getTime() <=
                new Date(this.cDate).getTime()
            )
            let dateTime = ''
            let description = item.process
            let yesterdayDesc = item.todayPlan
            if (detailList && detailList.length > 0) {
              description = detailList[detailList.length - 1].description
              dateTime = detailList[detailList.length - 1].setDate
            }
            if (detailList && detailList.length > 1) {
              yesterdayDesc = detailList[detailList.length - 2].description
            }
            return {
              id: item.id,
              item: item.item,
              process: item.process,
              todayPlan: item.todayPlan,
              itemClass: item.itemClass,
              description: description,
              yesterdayDesc: yesterdayDesc,
              dateTime: dateTime,
              remarks: item.remarks,
              modifyDetailList: item.modifyDetailList,
              detailList: detailList
            }
          })
          this.ProjectData.gridData = lodash.cloneDeep(
            this.ProjectData.showGridData
          )
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    saveProjectData() {
      this.ProjectData.loading = true
      // 保存钢铁产量信息
      const params = {
        date: this.cDate,
        data: this.ProjectData.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(firstMeetingProject2, params)
        .then(res => {
          if (res.success) {
            this.$message.success('保存成功！')
            this.ProjectData.dialogVisible = false
            this.getProjectData()
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    importProjectData(date) {
      this.ProjectData.loading = true
      post(firstMeetingProject1, {
        time: date
      })
        .then(res => {
          this.ProjectData.gridData = res.data.map(item => {
            return {
              item: item.item,
              process: item.process,
              todayPlan: item.todayPlan,
              remarks: item.remarks
            }
          })
          if (!res.data.length) {
            this.$message.warning('该日期无数据！')
          } else {
            this.$message.success('导入成功！')
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
