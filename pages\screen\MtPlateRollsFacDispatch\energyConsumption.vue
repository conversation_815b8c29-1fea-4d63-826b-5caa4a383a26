<template>
  <div class="container">
    <div class="chart-row">
      <div class="chart-box"> 
        <screen-border title="主线能耗趋势(单耗)">
          <div class="energy-filter">
            <label 
              v-for="(item, index) in energyTypes" 
              :key="index" 
              class="radio-item">
              <input 
                :value="item.value" 
                v-model="selectedEnergyType1" 
                type="radio" 
                @change="handleEnergyTypeChange(1)"
              >
              <span>{{ item.label }}</span>
            </label>
          </div>
          <line-single-chart 
            :chart-data="filteredLineChartData1" 
            :x-data="lineChartData.xData" 
            :show-legend="false"
            :color="['#FFDA35','#F45549', '#55C6D4', '#3391FF']"
            :height="300" 
            :show-label="true" 
            :bar-width="20" 
            :unit="getSelectedEnergyUnit(selectedEnergyType1)"
          />
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="主线日单耗">
          <div class="energy-cards">
            <div 
              v-for="(item, index) in energyTypes" 
              :key="index" 
              class="energy-card">
              <div class="energy-card-title">{{ item.label }}单耗</div>
              <div class="energy-card-unit">{{ item.unit }}</div>
              <div class="energy-card-value">{{ getMaxValueForType(item.value) }}</div>
            </div>
          </div>
          <bars-chart-group 
            :chart-data="barsData" 
            :height="240"
            :is-heat-treatment="false"
            @totalUpdated="handleTotalUpdate"/>
        </screen-border>
      </div>
    </div>

    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="热处理能耗趋势(单耗)">
          <div class="energy-filter">
            <label 
              v-for="(item, index) in energyTypes" 
              :key="index" 
              class="radio-item">
              <input 
                :value="item.value" 
                v-model="selectedEnergyType2" 
                type="radio" 
                @change="handleEnergyTypeChange(2)"
              >
              <span>{{ item.label }}</span>
            </label>
          </div>
          <line-single-chart 
            :chart-data="filteredLineChartData2" 
            :x-data="heatTreatmentLineChartData.xData" 
            :show-legend="false"
            :color="['#FFDA35','#F45549', '#55C6D4', '#3391FF']"
            :height="300" 
            :show-label="true" 
            :bar-width="20" 
            :unit="getSelectedEnergyUnit(selectedEnergyType2)"
          />
        </screen-border>
      </div>

      <div class="chart-box">
        <screen-border title="热处理单耗">
          <div class="energy-cards">
            <div 
              v-for="(item, index) in energyTypes" 
              :key="index" 
              class="energy-card">
              <div class="energy-card-title">{{ item.label }}单耗</div>
              <div class="energy-card-unit">{{ item.unit }}</div>
              <div class="energy-card-value">{{ getMaxValueForType2(item.value) }}</div>
            </div>
          </div>
          <bars-chart-group 
            :chart-data="barsData2" 
            :height="240"
            :is-heat-treatment="true" />
        </screen-border>
      </div>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/MtPlateRollsFacDispatch/component/screen-border.vue'
import LineSingleChart from '@/pages/screen/MtPlateRollsFacDispatch/component/line-single-chart.vue'
import BarsChart from '@/pages/screen/MtPlateRollsFacDispatch/component/bars-chart.vue'
import BarsChartGroup from '@/pages/screen/MtPlateRollsFacDispatch/component/bars-chart-group.vue'
import CustomProcessTable from '@/pages/screen/MtPlateRollsFacDispatch/component/custom-process-table.vue'

import {
  eleConsumptionCurveExportField,
  gasConsumptionCurveExportField,
  cpaConsumptionCurveExportField,
  watConsumptionCurveExportField,
  eleConsumptionFieldPage,
  gasConsumptionFieldPage,
  cpaConsumptionFieldPage,
  watConsumptionFieldPage
} from '@/api/screen'

import { post } from '@/lib/Util'
import moment from 'moment'

export default {
  name: 'TechnicalEconomicIndicators',
  components: {
    ScreenBorder,
    LineSingleChart,
    BarsChart,
    BarsChartGroup,
    CustomProcessTable
  },
  props: {
    selTime: {
      type: [String, Date],
      default: ''
    }
  },
  data() {
    return {
      rawMaterialPlanData: [],
      lineChartData: {
        xData: [],
        chartData: [
          {
            name: '电耗',
            value: 'electricity',
            data: []
          },
          {
            name: '煤气',
            value: 'gas',
            data: []
          },
          {
            name: '压缩空气',
            value: 'compressedAir',
            data: []
          },
          {
            name: '水',
            value: 'water',
            data: []
          }
        ]
      },
      heatTreatmentLineChartData: {
        xData: [],
        chartData: [
          {
            name: '电耗',
            value: 'electricity',
            data: []
          },
          {
            name: '煤气',
            value: 'gas',
            data: []
          },
          {
            name: '压缩空气',
            value: 'compressedAir',
            data: []
          },
          {
            name: '水',
            value: 'water',
            data: []
          }
        ]
      },
      energyTypes: [
        { label: '电', value: 'electricity', unit: 'kWh/t' },
        { label: '煤气', value: 'gas', unit: 'GJ/t' },
        { label: '压缩空气', value: 'compressedAir', unit: 'm³/t' },
        { label: '水', value: 'water', unit: 'm³/t' }
      ],
      selectedEnergyType1: 'electricity',
      selectedEnergyType2: 'electricity',
      barsData: [
        {
          title: '电单耗',
          unit: 'kWh/t',
          maxValue: 100,
          data: [
            { name: '大', value: null },
            { name: '中', value: null },
            { name: '小', value: null },
            { name: '综合', value: null }
          ]
        },
        {
          title: '煤气单耗',
          unit: 'GJ/t',
          maxValue: 100,
          data: [
            { name: '大', value: null },
            { name: '中', value: null },
            { name: '小', value: null },
            { name: '综合', value: null }
          ]
        },
        {
          title: '压缩空气单耗',
          unit: 'm³/t',
          maxValue: 100,
          data: [
            { name: '大', value: null },
            { name: '中', value: null },
            { name: '小', value: null },
            { name: '综合', value: null }
          ]
        },
        {
          title: '水单耗',
          unit: 'm³/t',
          maxValue: 100,
          data: [
            { name: '大', value: null },
            { name: '中', value: null },
            { name: '小', value: null },
            { name: '综合', value: null }
          ]
        }
      ],
      barsData2: [
        {
          title: '电单耗',
          unit: 'kWh/t',
          maxValue: 100,
          data: [
            { name: '大', value: null },
            { name: '中', value: null },
            { name: '小', value: null },
            { name: '综合', value: null }
          ]
        },
        {
          title: '煤气单耗',
          unit: 'GJ/t',
          maxValue: 100,
          data: [
            { name: '大', value: null },
            { name: '中', value: null },
            { name: '小', value: null },
            { name: '综合', value: null }
          ]
        },
        {
          title: '压缩空气单耗',
          unit: 'm³/t',
          maxValue: 100,
          data: [
            { name: '大', value: null },
            { name: '中', value: null },
            { name: '小', value: null },
            { name: '综合', value: null }
          ]
        },
        {
          title: '水单耗',
          unit: 'm³/t',
          maxValue: 100,
          data: [
            { name: '大', value: null },
            { name: '中', value: null },
            { name: '小', value: null },
            { name: '综合', value: null }
          ]
        }
      ]
    }
  },
  computed: {
    filteredLineChartData1() {
      const selectedType = this.selectedEnergyType1
      const selectedData = this.lineChartData.chartData.find(
        item => item.value === selectedType
      )
      return selectedData ? [selectedData] : []
    },
    filteredLineChartData2() {
      const selectedType = this.selectedEnergyType2
      const selectedData = this.heatTreatmentLineChartData.chartData.find(
        item => item.value === selectedType
      )
      return selectedData ? [selectedData] : []
    }
  },
  watch: {
    selTime: {
      handler() {
        this.fetchAllData()
        this.fetchFieldPageData()
        console.log(
          '%c selTime',
          'color:red;',
          moment(this.selTime).format('YYYY-MM-DD')
        )
      }
    }
  },
  created() {
    this.fetchAllData()
    this.fetchFieldPageData()
  },
  methods: {
    handleEnergyTypeChange(chartIndex) {
      console.log(
        `图表${chartIndex}能源类型变更为: ${
          chartIndex === 1 ? this.selectedEnergyType1 : this.selectedEnergyType2
        }`
      )

      const selectedType =
        chartIndex === 1 ? this.selectedEnergyType1 : this.selectedEnergyType2

      // 根据选择的能源类型调用相应的数据获取方法
      switch (selectedType) {
        case 'electricity':
          this.fetchEleConsumptionData()
          break
        case 'gas':
          this.fetchGasConsumptionData()
          break
        case 'compressedAir':
          this.fetchCpaConsumptionData()
          break
        case 'water':
          this.fetchWatConsumptionData()
          break
      }
    },
    getSelectedEnergyUnit(selectedType) {
      const energyType = this.energyTypes.find(
        item => item.value === selectedType
      )
      return energyType ? energyType.unit : ''
    },
    fetchAllData() {
      this.fetchEleConsumptionData()
      this.fetchGasConsumptionData()
      this.fetchCpaConsumptionData()
      this.fetchWatConsumptionData()
    },
    async fetchGasConsumptionData() {
      try {
        const res = await post(gasConsumptionCurveExportField, this.getParams())

        if (res && res.code === 0 && res.gasConsumptionStatisticsCurve) {
          const mainLineData = res.gasConsumptionStatisticsCurve.find(
            item => item.name === '中厚板卷厂总煤气'
          )

          const heatTreatmentData = res.gasConsumptionStatisticsCurve.find(
            item => item.name === '中厚板卷厂热处理煤气'
          )

          if (mainLineData && heatTreatmentData) {
            // 更新主线图表数据中的煤气数据
            const chartDataIndex = this.lineChartData.chartData.findIndex(
              item => item.value === 'gas'
            )
            if (chartDataIndex !== -1) {
              this.lineChartData.chartData[chartDataIndex].data =
                mainLineData.yZhou
              this.lineChartData.xData = mainLineData.xZhou
            }

            // 更新热处理图表数据中的煤气数据
            const heatChartDataIndex = this.heatTreatmentLineChartData.chartData.findIndex(
              item => item.value === 'gas'
            )
            if (heatChartDataIndex !== -1) {
              this.heatTreatmentLineChartData.chartData[
                heatChartDataIndex
              ].data = heatTreatmentData.yZhou
              this.heatTreatmentLineChartData.xData = heatTreatmentData.xZhou
            }

            // 更新主线日单耗中的煤气值
            if (mainLineData.yZhou.length > 0) {
              const latestIndex = mainLineData.yZhou.length - 1
              const latestGasValue = mainLineData.yZhou[latestIndex]

              const gasCard = this.barsData.find(
                item => item.title === '煤气单耗'
              )
              if (gasCard) {
                gasCard.data[3].value = latestGasValue
              }
            }

            // 更新热处理单耗中的煤气值
            if (heatTreatmentData.yZhou.length > 0) {
              const latestIndex = heatTreatmentData.yZhou.length - 1
              const latestHeatGasValue = heatTreatmentData.yZhou[latestIndex]

              const heatGasCard = this.barsData2.find(
                item => item.title === '煤气单耗'
              )
              if (heatGasCard) {
                heatGasCard.data[3].value = latestHeatGasValue
              }
            }
          }
        }

        this.updateMaxValues()
        return res
      } catch (error) {
        console.error('获取煤气能耗数据失败:', error)
        return null
      }
    },
    async fetchCpaConsumptionData() {
      try {
        const res = await post(cpaConsumptionCurveExportField, this.getParams())

        if (res && res.code === 0 && res.cpaConsumptionStatisticsCurve) {
          const mainLineData = res.cpaConsumptionStatisticsCurve.find(
            item => item.name === '中厚板卷总压缩空气'
          )

          const heatTreatmentData = res.cpaConsumptionStatisticsCurve.find(
            item => item.name === '热处理压缩空气'
          )

          if (mainLineData) {
            // 更新主线图表数据中的压缩空气数据
            const chartDataIndex = this.lineChartData.chartData.findIndex(
              item => item.value === 'compressedAir'
            )
            if (chartDataIndex !== -1) {
              this.lineChartData.chartData[chartDataIndex].data =
                mainLineData.yZhou
              this.lineChartData.xData = mainLineData.xZhou
            }

            // 更新主线日单耗中的压缩空气值
            if (mainLineData.yZhou.length > 0) {
              const latestIndex = mainLineData.yZhou.length - 1
              const latestCpaValue = mainLineData.yZhou[latestIndex]

              const cpaCard = this.barsData.find(
                item => item.title === '压缩空气单耗'
              )
              if (cpaCard) {
                cpaCard.data[3].value = latestCpaValue
              }
            }
          }

          if (heatTreatmentData) {
            // 更新热处理图表数据中的压缩空气数据
            const heatChartDataIndex = this.heatTreatmentLineChartData.chartData.findIndex(
              item => item.value === 'compressedAir'
            )
            if (heatChartDataIndex !== -1) {
              this.heatTreatmentLineChartData.chartData[
                heatChartDataIndex
              ].data = heatTreatmentData.yZhou
              this.heatTreatmentLineChartData.xData = heatTreatmentData.xZhou
            }

            // 更新热处理单耗中的压缩空气值
            if (heatTreatmentData.yZhou.length > 0) {
              const latestIndex = heatTreatmentData.yZhou.length - 1
              const latestHeatCpaValue = heatTreatmentData.yZhou[latestIndex]

              const heatCpaCard = this.barsData2.find(
                item => item.title === '压缩空气单耗'
              )
              if (heatCpaCard) {
                heatCpaCard.data[3].value = latestHeatCpaValue
              }
            }
          }
        }

        this.updateMaxValues()
        return res
      } catch (error) {
        console.error('获取压缩空气能耗数据失败:', error)
        return null
      }
    },
    async fetchWatConsumptionData() {
      try {
        const res = await post(watConsumptionCurveExportField, this.getParams())

        if (res && res.code === 0 && res.watConsumptionStatisticsCurve) {
          const mainLineData = res.watConsumptionStatisticsCurve.find(
            item => item.name === '中厚板卷厂总循环水'
          )

          const heatTreatmentData = res.watConsumptionStatisticsCurve.find(
            item => item.name === '热处理循环水'
          )

          if (mainLineData) {
            // 更新主线图表数据中的水数据
            const chartDataIndex = this.lineChartData.chartData.findIndex(
              item => item.value === 'water'
            )
            if (chartDataIndex !== -1) {
              this.lineChartData.chartData[chartDataIndex].data =
                mainLineData.yZhou
              this.lineChartData.xData = mainLineData.xZhou
            }

            // 更新主线日单耗中的水值
            if (mainLineData.yZhou.length > 0) {
              const latestIndex = mainLineData.yZhou.length - 1
              const latestWatValue = mainLineData.yZhou[latestIndex]

              const watCard = this.barsData.find(
                item => item.title === '水单耗'
              )
              if (watCard) {
                watCard.data[3].value = latestWatValue
              }
            }
          }

          if (heatTreatmentData) {
            // 更新热处理图表数据中的水数据
            const heatChartDataIndex = this.heatTreatmentLineChartData.chartData.findIndex(
              item => item.value === 'water'
            )
            if (heatChartDataIndex !== -1) {
              this.heatTreatmentLineChartData.chartData[
                heatChartDataIndex
              ].data = heatTreatmentData.yZhou
              this.heatTreatmentLineChartData.xData = heatTreatmentData.xZhou
            }

            // 更新热处理单耗中的水值
            if (heatTreatmentData.yZhou.length > 0) {
              const latestIndex = heatTreatmentData.yZhou.length - 1
              const latestHeatWatValue = heatTreatmentData.yZhou[latestIndex]

              const heatWatCard = this.barsData2.find(
                item => item.title === '水单耗'
              )
              if (heatWatCard) {
                heatWatCard.data[3].value = latestHeatWatValue
              }
            }
          }
        }

        this.updateMaxValues()
        return res
      } catch (error) {
        console.error('获取水能耗数据失败:', error)
        return null
      }
    },
    async fetchEleConsumptionData() {
      try {
        const res = await post(eleConsumptionCurveExportField, this.getParams())

        if (res && res.code === 0 && res.eleConsumptionStatisticsFieldCurve) {
          const mainLineData = res.eleConsumptionStatisticsFieldCurve.find(
            item => item.name === '中厚板卷厂总电'
          )

          const heatTreatmentData = res.eleConsumptionStatisticsFieldCurve.find(
            item => item.name === '中厚板卷厂热处理电耗'
          )

          if (mainLineData && heatTreatmentData) {
            // 更新主线电耗数据
            this.lineChartData.xData = mainLineData.xZhou
            const electricityIndex = this.lineChartData.chartData.findIndex(
              item => item.value === 'electricity'
            )
            if (electricityIndex !== -1) {
              this.lineChartData.chartData[electricityIndex].data =
                mainLineData.yZhou
            }

            // 更新热处理电耗数据
            this.heatTreatmentLineChartData.xData = heatTreatmentData.xZhou
            const heatElectricityIndex = this.heatTreatmentLineChartData.chartData.findIndex(
              item => item.value === 'electricity'
            )
            if (heatElectricityIndex !== -1) {
              this.heatTreatmentLineChartData.chartData[
                heatElectricityIndex
              ].data = heatTreatmentData.yZhou
            }

            // 更新主线日单耗中的电值
            if (mainLineData.yZhou.length > 0) {
              const latestIndex = mainLineData.yZhou.length - 1
              const latestElectricityValue = mainLineData.yZhou[latestIndex]

              const electricityCard = this.barsData.find(
                item => item.title === '电单耗'
              )
              if (electricityCard) {
                electricityCard.data[3].value = latestElectricityValue
              }
            }

            // 更新热处理单耗中的电值
            if (heatTreatmentData.yZhou.length > 0) {
              const latestIndex = heatTreatmentData.yZhou.length - 1
              const latestHeatValue = heatTreatmentData.yZhou[latestIndex]

              const heatElectricityCard = this.barsData2.find(
                item => item.title === '电单耗'
              )
              if (heatElectricityCard) {
                heatElectricityCard.data[3].value = latestHeatValue
              }
            }
          }
        }

        this.updateMaxValues()
        return res
      } catch (error) {
        console.error('获取能耗数据失败:', error)
        return null
      }
    },
    formatDate(date) {
      if (!date) return ''
      return moment(date).format('YYYY-MM-DD')
    },
    getParams() {
      const endDate = moment(this.selTime)
      const startDate = moment(endDate).startOf('month')

      return {
        startTime: startDate.format('YYYY-MM-DD HH:mm:ss'),
        endTime: endDate.format('YYYY-MM-DD HH:mm:ss'),
        medium: 1,
        team: '0,1,2,3',
        hDflag: '0',
        cTflag: '0'
      }
    },
    async fetchFieldPageData() {
      try {
        const baseParams = this.getParams()
        const { cTflag, ...filteredParams } = baseParams

        const requestParams = {
          ...filteredParams,
          startTime: moment(this.selTime).format('YYYY-MM-DD 00:00:00'),
          endTime: moment(this.selTime).format('YYYY-MM-DD 23:59:59'),
          page: 1,
          size: 10,
          orderBy: 'id_',
          rule: 'asc'
        }

        // 定义所有请求，并添加标识符以便于区分不同类型的数据
        const requestList = [
          {
            type: 'electricity',
            promise: post(eleConsumptionFieldPage, requestParams)
          },
          {
            type: 'gas',
            promise: post(gasConsumptionFieldPage, requestParams)
          },
          {
            type: 'compressedAir',
            promise: post(cpaConsumptionFieldPage, requestParams)
          },
          {
            type: 'water',
            promise: post(watConsumptionFieldPage, requestParams)
          }
        ]

        // 使用Promise.allSettled并行处理所有请求
        const results = await Promise.allSettled(
          requestList.map(item => item.promise)
        )

        // 处理每个请求的结果
        const processedResults = results.map((result, index) => {
          const requestType = requestList[index].type
          if (result.status === 'fulfilled') {
            console.log(`${requestType}数据请求成功:`, result.value)
            return { type: requestType, data: result.value, success: true }
          } else {
            console.error(`${requestType}数据请求失败:`, result.reason)
            return { type: requestType, error: result.reason, success: false }
          }
        })

        // 可以根据处理后的结果进行后续操作
        this.updateUIBasedOnResults(processedResults)

        return processedResults
      } catch (error) {
        console.error('初始化请求处理失败:', error)
        return []
      }
    },
    // 根据请求结果更新UI
    updateUIBasedOnResults(processedResults) {
      // 处理电能数据
      const electricityResult = processedResults.find(
        r => r.type === 'electricity' && r.success
      )
      if (
        electricityResult &&
        electricityResult.data &&
        electricityResult.data.code === 0
      ) {
        const eleData = electricityResult.data.eleConsumptionStatistics
        if (eleData && eleData.list && eleData.list.length > 1) {
          // 获取第二项数据（索引为1）
          const targetData = eleData.list[1]

          // 更新主线日单耗中电单耗的综合值
          if (targetData.S_40TOS) {
            const electricityCard = this.barsData.find(
              item => item.title === '电单耗'
            )
            if (electricityCard) {
              electricityCard.data[3].value = parseFloat(targetData.S_40TOS)
            }
          }

          // 更新热处理单耗中电单耗的综合值
          if (targetData.S_41TOS) {
            const heatElectricityCard = this.barsData2.find(
              item => item.title === '电单耗'
            )
            if (heatElectricityCard) {
              heatElectricityCard.data[3].value = parseFloat(targetData.S_41TOS)
            }
          }

          // 处理children数据
          if (targetData.children && Array.isArray(targetData.children)) {
            // 获取卡片引用
            const mainElectricityCard = this.barsData.find(
              item => item.title === '电单耗'
            )
            const heatElectricityCard = this.barsData2.find(
              item => item.title === '电单耗'
            )

            // 遍历children数组
            targetData.children.forEach(child => {
              if (child.classes) {
                // 根据classes属性确定对应的索引
                let index = -1
                if (child.classes === '大夜班') {
                  index = 0 // 大
                } else if (child.classes === '白班') {
                  index = 1 // 中
                } else if (child.classes === '小夜班') {
                  index = 2 // 小
                }

                // 如果找到对应的索引，更新对应的值
                if (index >= 0) {
                  // 更新主线日单耗
                  if (mainElectricityCard && child.S_40TOS) {
                    mainElectricityCard.data[index].value = parseFloat(
                      child.S_40TOS
                    )
                  }

                  // 更新热处理单耗
                  if (heatElectricityCard && child.S_41TOS) {
                    heatElectricityCard.data[index].value = parseFloat(
                      child.S_41TOS
                    )
                  }
                }
              }
            })
          }
        }
      }

      // 处理水耗数据
      const waterResult = processedResults.find(
        r => r.type === 'water' && r.success
      )
      if (waterResult && waterResult.data && waterResult.data.code === 0) {
        const watData = waterResult.data.watConsumptionStatistics
        if (watData && watData.list && watData.list.length > 1) {
          // 获取第二项数据（索引为1）
          const targetData = watData.list[1]

          // 更新主线日单耗中水单耗的综合值
          if (targetData.S_1643896454902714369TOS) {
            const waterCard = this.barsData.find(
              item => item.title === '水单耗'
            )
            if (waterCard) {
              waterCard.data[3].value = parseFloat(
                targetData.S_1643896454902714369TOS
              )
            }
          }

          // 更新热处理单耗中水单耗的综合值
          if (targetData.S_1643896843718889474TOS) {
            const heatWaterCard = this.barsData2.find(
              item => item.title === '水单耗'
            )
            if (heatWaterCard) {
              heatWaterCard.data[3].value = parseFloat(
                targetData.S_1643896843718889474TOS
              )
            }
          }

          // 处理children数据
          if (targetData.children && Array.isArray(targetData.children)) {
            // 获取卡片引用
            const mainWaterCard = this.barsData.find(
              item => item.title === '水单耗'
            )
            const heatWaterCard = this.barsData2.find(
              item => item.title === '水单耗'
            )

            // 遍历children数组
            targetData.children.forEach(child => {
              if (child.classes) {
                // 根据classes属性确定对应的索引
                let index = -1
                if (child.classes === '大夜班') {
                  index = 0 // 大
                } else if (child.classes === '白班') {
                  index = 1 // 中
                } else if (child.classes === '小夜班') {
                  index = 2 // 小
                }

                // 如果找到对应的索引，更新对应的值
                if (index >= 0) {
                  // 更新主线日单耗
                  if (mainWaterCard && child.S_1643896454902714369TOS) {
                    mainWaterCard.data[index].value = parseFloat(
                      child.S_1643896454902714369TOS
                    )
                  }

                  // 更新热处理单耗
                  if (heatWaterCard && child.S_1643896843718889474TOS) {
                    heatWaterCard.data[index].value = parseFloat(
                      child.S_1643896843718889474TOS
                    )
                  }
                }
              }
            })
          }
        }
      }

      // 处理煤气数据
      const gasResult = processedResults.find(
        r => r.type === 'gas' && r.success
      )
      if (gasResult && gasResult.data && gasResult.data.code === 0) {
        const gasData = gasResult.data.gasConsumptionStatistics
        if (gasData && gasData.list && gasData.list.length > 1) {
          // 获取第二项数据（索引为1）
          const targetData = gasData.list[1]

          // 更新主线日单耗中煤气单耗的综合值 (S_33TOS)
          if (targetData.S_33TOS) {
            const gasCard = this.barsData.find(
              item => item.title === '煤气单耗'
            )
            if (gasCard) {
              gasCard.data[3].value = parseFloat(targetData.S_33TOS)
            }
          }

          // 更新热处理单耗中煤气单耗的综合值 (S_34TOS)
          if (targetData.S_34TOS) {
            const heatGasCard = this.barsData2.find(
              item => item.title === '煤气单耗'
            )
            if (heatGasCard) {
              heatGasCard.data[3].value = parseFloat(targetData.S_34TOS)
            }
          }

          // 处理children数据
          if (targetData.children && Array.isArray(targetData.children)) {
            // 获取卡片引用
            const mainGasCard = this.barsData.find(
              item => item.title === '煤气单耗'
            )
            const heatGasCard = this.barsData2.find(
              item => item.title === '煤气单耗'
            )

            // 遍历children数组
            targetData.children.forEach(child => {
              if (child.classes) {
                // 根据classes属性确定对应的索引
                let index = -1
                if (child.classes === '大夜班') {
                  index = 0 // 大
                } else if (child.classes === '白班') {
                  index = 1 // 中
                } else if (child.classes === '小夜班') {
                  index = 2 // 小
                }

                // 如果找到对应的索引，更新对应的值
                if (index >= 0) {
                  // 更新主线日单耗 (S_33TOS)
                  if (mainGasCard && child.S_33TOS) {
                    mainGasCard.data[index].value = parseFloat(child.S_33TOS)
                  }

                  // 更新热处理单耗 (S_34TOS)
                  if (heatGasCard && child.S_34TOS) {
                    heatGasCard.data[index].value = parseFloat(child.S_34TOS)
                  }
                }
              }
            })
          }
        }
      }

      // 处理压缩空气数据
      const cpaResult = processedResults.find(
        r => r.type === 'compressedAir' && r.success
      )
      if (cpaResult && cpaResult.data && cpaResult.data.code === 0) {
        const cpaData = cpaResult.data.cpaConsumptionStatistics
        if (cpaData && cpaData.list && cpaData.list.length > 1) {
          // 获取第二项数据（索引为1）
          const targetData = cpaData.list[1]

          // 更新主线日单耗中压缩空气单耗的综合值
          if (targetData.S_1643917110486765569TOS) {
            const cpaCard = this.barsData.find(
              item => item.title === '压缩空气单耗'
            )
            if (cpaCard) {
              cpaCard.data[3].value = parseFloat(
                targetData.S_1643917110486765569TOS
              )
            }
          }

          // 更新热处理单耗中压缩空气单耗的综合值
          if (targetData.S_1643917373473820674TOS) {
            const heatCpaCard = this.barsData2.find(
              item => item.title === '压缩空气单耗'
            )
            if (heatCpaCard) {
              heatCpaCard.data[3].value = parseFloat(
                targetData.S_1643917373473820674TOS
              )
            }
          }

          // 处理children数据
          if (targetData.children && Array.isArray(targetData.children)) {
            // 获取卡片引用
            const mainCpaCard = this.barsData.find(
              item => item.title === '压缩空气单耗'
            )
            const heatCpaCard = this.barsData2.find(
              item => item.title === '压缩空气单耗'
            )

            // 遍历children数组
            targetData.children.forEach(child => {
              if (child.classes) {
                // 根据classes属性确定对应的索引
                let index = -1
                if (child.classes === '大夜班') {
                  index = 0 // 大
                } else if (child.classes === '白班') {
                  index = 1 // 中
                } else if (child.classes === '小夜班') {
                  index = 2 // 小
                }

                // 如果找到对应的索引，更新对应的值
                if (index >= 0) {
                  // 更新主线日单耗
                  if (mainCpaCard && child.S_1643917110486765569TOS) {
                    mainCpaCard.data[index].value = parseFloat(
                      child.S_1643917110486765569TOS
                    )
                  }

                  // 更新热处理单耗
                  if (heatCpaCard && child.S_1643917373473820674TOS) {
                    heatCpaCard.data[index].value = parseFloat(
                      child.S_1643917373473820674TOS
                    )
                  }
                }
              }
            })
          }
        }
      }

      // 在更新完数据后，调用更新最大值的方法
      this.updateMaxValues()
    },
    // 新增方法：更新所有图表的最大值
    updateMaxValues() {
      // 更新主线图表最大值
      this.barsData.forEach(item => {
        const values = item.data
          .map(d => d.value)
          .filter(v => v !== null && v !== undefined)
        if (values.length > 0) {
          const maxVal = Math.max(...values)
          // 设置最大值为数据最大值的1.2倍，并向上取整到10的倍数
          item.maxValue = Math.ceil((maxVal * 1.2) / 10) * 10
          // 确保最小值至少为100，除非所有数据都很小
          item.maxValue = Math.max(item.maxValue, maxVal < 50 ? 50 : 100)
        }
      })

      // 更新热处理图表最大值
      this.barsData2.forEach(item => {
        const values = item.data
          .map(d => d.value)
          .filter(v => v !== null && v !== undefined)
        if (values.length > 0) {
          const maxVal = Math.max(...values)
          // 设置最大值为数据最大值的1.2倍，并向上取整到10的倍数
          item.maxValue = Math.ceil((maxVal * 1.2) / 10) * 10
          // 确保最小值至少为100，除非所有数据都很小
          item.maxValue = Math.max(item.maxValue, maxVal < 50 ? 50 : 100)
        }
      })
    },
    getMaxValueForType(type) {
      const barsDataItem = this.barsData.find(item => {
        if (type === 'electricity' && item.title === '电单耗') return true
        if (type === 'gas' && item.title === '煤气单耗') return true
        if (type === 'compressedAir' && item.title === '压缩空气单耗')
          return true
        if (type === 'water' && item.title === '水单耗') return true
        return false
      })

      if (barsDataItem && barsDataItem.data) {
        const totalValue = barsDataItem.data.find(d => d.name === '综合')
        return totalValue ? Number(totalValue.value).toFixed(3) : '0.000'
      }
      return '0.000'
    },
    getMaxValueForType2(type) {
      const barsDataItem = this.barsData2.find(item => {
        if (type === 'electricity' && item.title === '电单耗') return true
        if (type === 'gas' && item.title === '煤气单耗') return true
        if (type === 'compressedAir' && item.title === '压缩空气单耗')
          return true
        if (type === 'water' && item.title === '水单耗') return true
        return false
      })

      if (barsDataItem && barsDataItem.data) {
        const totalValue = barsDataItem.data.find(d => d.name === '综合')
        return totalValue ? Number(totalValue.value).toFixed(3) : '0.000'
      }
      return '0.000'
    },
    handleTotalUpdate({ index, total }) {
      // 更新对应卡片的值
      if (this.energyTypes[index]) {
        const type = this.energyTypes[index].value
        const energyCard = this.barsData.find(item => {
          switch (type) {
            case 'electricity':
              return item.title === '电单耗'
            case 'gas':
              return item.title === '煤气单耗'
            case 'compressedAir':
              return item.title === '压缩空气单耗'
            case 'water':
              return item.title === '水单耗'
          }
        })
        if (energyCard) {
          // 更新卡片的综合值
          const totalItem = energyCard.data.find(d => d.name === '综合')
          if (totalItem) {
            totalItem.value = Number(total)
          }
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: hidden;

  .chart-row {
    margin-bottom: 10px;
  }

  .chart-row,
  .table-row {
    display: flex;
    gap: 10px;
    height: 50%;
    width: 100%;
  }

  .chart-box,
  .table-box {
    flex: 1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .energy-filter {
    display: flex;
    margin-bottom: 8px;
    padding: 5px 0 5px 30px;

    .radio-item {
      margin-right: 15px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #fff;
      cursor: pointer;

      input[type='radio'] {
        margin-right: 5px;
        accent-color: #1fc6ff;
      }
    }
  }

  .energy-cards {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    .energy-card {
      background-color: rgba(31, 198, 255, 0.1);
      border: 1px solid rgba(31, 198, 255, 0.3);
      border-radius: 4px;
      padding: 6px 10px;
      width: 23%;
      text-align: center;

      .energy-card-title {
        font-size: 14px;
        color: #fff;
        margin-bottom: 3px;
      }

      .energy-card-unit {
        font-size: 12px;
        color: #1fc6ff;
        margin-bottom: 2px;
      }

      .energy-card-value {
        font-size: 16px;
        font-weight: bold;
        color: #1fc6ff;
      }
    }
  }

  .chart-footer {
    margin: 0px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    padding: 5px 8px;
    background: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);
    width: 100%;
  }

  .chart-input {
    height: 100%;
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      height: 100%;
      padding: 0;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
        border-color: transparent !important;
      }
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      border-color: transparent !important;
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-content {
    padding: 10px;
    box-sizing: border-box;
  }
  .text-image-section {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .wrapper {
    display: flex;
    flex-flow: row wrap;

    .wrapper-box {
      display: flex;
      flex-flow: row wrap;
      width: 50%;
      transform: translateX(24px);

      .text-image-item {
        display: flex;
        align-items: center;
        width: 200px;
        height: 64px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #fff;
        margin: 20px 10px;

        img {
          width: 64px;
          height: 64px;
        }

        .text-image-item-wrapper {
          flex: 1;
          margin-left: 10px;

          .text-image-item-title {
            margin-bottom: 8px;
          }
        }
      }
    }

    .wrapper-box-chart {
      width: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;

      .wrapper-box-chart-item {
        width: 100%;
        height: 300px;
        position: absolute;
        top: 20px;
        right: 0;
      }
    }
  }
}
</style>
