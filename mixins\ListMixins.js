/**
 * 新增修改完成调用 modalFormOk方法 编辑弹框组件ref定义为modalForm
 * data中url定义 list为查询列表  delete为删除单条记录
 */
import { post } from '@/lib/Util'

export default {
  data() {
    return {
      loading: false,
      visibleEdit: false, // 编辑页面显示flag
      size: 'small', // medium / small / mini
      searchForm: {},
      page: {
        page: 1,
        size: 10,
        total: 0
      },
      tableData: [],
      selectedRowKeys: [], // table选中keys
      selectionRows: [] // table选中records
    }
  },
  // mixins: [BaseMixins],
  created() {
    this.handleSearch(true)
  },
  methods: {
    async handleSearch(reset = false) {
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.page = 1
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          page: this.page.page - 1,
          size: this.page.size
        })
      )
      this.tableData = data ? data.content : []
      this.page.page = data.pageable.pageNumber + 1
      this.page.size = data.pageable.pageSize
      this.page.total = data.totalElements
      this.afterHandleSearch(this.tableData)
      this.loading = false
    },
    handleReset() {
      this.searchForm = {}
      this.handleSearch(true)
    },
    changeTableSize(size) {
      console.log('改变尺寸')
      this.size = size
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.handleSearch()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.page = val
      this.handleSearch()
    },
    beforeHandleSearch() {
      // console.log('before load data')
    },
    afterHandleSearch() {
      // console.log('after load data')
    },
    handleDelete: function(data) {
      if (!this.url.delete) {
        this.$message('请设置url.delete属性!')
        return
      }
      this.$confirm('是否确认删除此数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除操作
        post(this.url.delete, { id: data.id }).then(res => {
          this.handleSearch()
          this.$message.success(res.data)
        })
      })
    },
    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
    },
    onClearSelected() {
      this.selectedRowKeys = []
      this.selectionRows = []
    },
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    handleAdd: function() {
      console.log('%c 上边node', 'color: red', this.nodeInfo)
      console.log('%c 上边data', 'color: red', this.dataInfo)
      this.$refs.modalForm.add(this.nodeInfo)
      this.$nextTick(() => {
        this.$refs.modalForm.visible = true
      })
    },
    handleToggleSearch() {
      this.toggleSearchStatus = !this.toggleSearchStatus
    }
  }
}
