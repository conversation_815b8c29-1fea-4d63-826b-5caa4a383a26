<!--施工明细-->
<template>
  <div class="bigBox">
    <screen-border title="重大风险">
      <template v-slot:headerRight>
        <div class="header">
          <div 
            v-show="!isNew" 
            class="searchCriteria">
            <el-date-picker
              v-model="TimeInterval"
              type="datetimerange"
              align="right"
              value-format="yyyy-MM-dd HH:mm:ss"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"/>
            <el-select 
              v-model="liability" 
              clearable 
              placeholder="请选择责任">
              <el-option
                v-for="item in liabilityOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
            <span
              class="screen-btn search"
              @click="getRiskData">
              查询
            </span>
          </div>
          <div class="btnBox">
            <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              v-show="isNew"
              class="screen-btn"
              @click="saveNewData">
              <el-icon class="el-icon-printer"/>
              保存
            </span>
            <span
              v-show="isNew"
              class="screen-btn"
              @click="back">
              <el-icon class="el-icon-d-arrow-left"/>
              返回
            </span>
            <span
              v-show="!isNew"
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              v-show="!isNew"
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download"/>
              下载
            </span>
            <span
              v-show="!isNew"
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span>
          </div>
        </div>
      </template>
      <el-table
        v-loading="loading"
        v-show="!isNew"
        id="table"
        :data="tableData"
        element-loading-text="加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        height="calc(100vh - 195px)"
        border>
        <el-table-column
          show-overflow-tooltip
          width="60"
          align="center"
          label="序号">
          <template v-slot="scope">
            <div>{{ scope.$index + 1 }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="责任"
          align="center"
          prop="liability"
          width="100"/>
        <el-table-column
          :show-overflow-tooltip="true"
          label="工作岗"
          prop="jobs"
          align="center"/>
        <el-table-column
          :show-overflow-tooltip="true"
          label="危险源"
          prop="dangerSource"
          align="center"/>
        <el-table-column
          :show-overflow-tooltip="true"
          label="工作任务或风险描述"
          prop="jobRisk"
          align="center"/>
        <el-table-column
          :show-overflow-tooltip="true"
          label="事故类别"
          prop="accidentCategory"
          align="center"/>
        <el-table-column
          label="RAR"
          prop="rar"
          align="center"/>
        <el-table-column
          label="安全风险等级"
          prop="riskLevel"
          align="center"
          width="150"/>
        <el-table-column
          label="时间"
          prop="time"
          align="center"
          width="130">
          <template v-slot="scope">
            {{ scope.row.time.split(' ')[0] }}
          </template>
        </el-table-column>
        <el-table-column
          label="厂部检查情况"
          prop="checkSituation"
          align="center"/>
        <el-table-column
          align="center"
          width="150"
          label="检查图片">
          <template v-slot="scope">
            <div class="actionBox">
              <div class="fileBox">
                <el-checkbox 
                  v-for="(item,index) in scope.row.checkPics" 
                  :key="index"
                  v-model="item.checked">
                  <span 
                    class="fileTitle"
                    @click.prevent="getImgFormat(item.img)">{{ item.name }}</span></el-checkbox>
              </div>
              <div>
                <el-upload
                  ref="upload"
                  :file-list="fileList1"
                  :multiple ="false"
                  :on-exceed="handleExceed"
                  :before-upload="beforeUpload"
                  :on-success="(response, file, fileList)=>handleSuccess(response, file, fileList, scope.row,'examine')"
                  :show-file-list="false"
                  action="">
                  <i class="el-icon-upload actionBtn"/>
                </el-upload>
                <i 
                  v-show="scope.row.checkPics.length!=0"
                  class="el-icon-delete actionBtn" 
                  @click="delExamineImg(scope.row)"/>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          width="150"
          label="整改图片">
          <template v-slot="scope">
            <div class="actionBox">
              <div class="fileBox">
                <el-checkbox 
                  v-for="(item,index) in scope.row.correctivePics" 
                  :key="index"
                  v-model="item.checked">
                  <span 
                    class="fileTitle"
                    @click.prevent="getImgFormat(item.img)">{{ item.name }}</span></el-checkbox>
              </div>
              <div>
                <el-upload
                  ref="upload"
                  :file-list="fileList2"
                  :multiple ="false"
                  :on-exceed="handleExceed"
                  :before-upload="beforeUpload"
                  :on-success="(response, file, fileList)=>handleSuccess(response, file, fileList, scope.row,'Rectification')"
                  :show-file-list="false"
                  action="">
                  <i class="el-icon-upload actionBtn"/>
                </el-upload>
                <i 
                  v-show="scope.row.correctivePics.length!=0"
                  class="el-icon-delete actionBtn" 
                  @click="delRectificationimg(scope.row)"/>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          property=""
          width="150"
          label="操作">
          <template v-slot="scope">
            <span
              style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
              @click="editRow(scope.row)">编辑</span>
            <span
              style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
              @click="DelRow(scope.row)">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 新增行 -->
      <el-table
        v-show="isNew"
        :data="newData"
        class="newCss"
        height="calc(100vh - 195px)"
        border>
        <el-table-column
          label="责任"
          width="150"
          prop="liability"
          align="center">
          <template v-slot="scope">
            <el-select 
              v-model="scope.row.liability" 
              clearable 
              placeholder="请选择">
              <el-option
                v-for="item in liabilityOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          label="工作岗"
          prop="jobs"
          width="220"
          align="center">
          <template v-slot="scope">
            <el-input
              :rows="4"
              v-model="scope.row.jobs"
              type="textarea"/>
          </template>
        </el-table-column>
        <el-table-column
          label="危险源"
          prop="dangerSource"
          width="220"
          align="center">
          <template v-slot="scope">
            <el-input
              :rows="4"
              v-model="scope.row.dangerSource"
              type="textarea"/>
          </template>
        </el-table-column>
        <el-table-column
          label="工作任务或风险描述"
          prop="jobRisk"
          width="220"
          align="center">
          <template v-slot="scope">
            <el-input
              :rows="4"
              v-model="scope.row.jobRisk"
              type="textarea"/>
          </template>
        </el-table-column>
        <el-table-column
          label="事故类别"
          prop="accidentCategory"
          width="220"
          align="center">
          <template v-slot="scope">
            <el-input
              :rows="4"
              v-model="scope.row.accidentCategory"
              type="textarea"/>
          </template>
        </el-table-column>
        <el-table-column
          label="RAR"
          prop="rar"
          align="center">
          <template v-slot="scope">
            <el-input v-model="scope.row.rar"/>
          </template>
        </el-table-column>
        <el-table-column
          label="安全风险等级"
          prop="riskLevel"
          align="center"
          width="140">
          <template v-slot="scope">
            <el-select 
              v-model="scope.row.riskLevel" 
              clearable 
              placeholder="请选择">
              <el-option
                v-for="item in riskLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          label="时间"
          prop="time"
          align="center"
          width="210">
          <template v-slot="scope">
            <el-date-picker
              v-model="scope.row.time"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              type="date"
              placeholder="选择日期时间"/>
          </template>
        </el-table-column>
        <el-table-column
          label="厂部检查情况"
          prop="checkSituation"
          width="220"
          align="center">
          <template v-slot="scope">
            <el-input
              :rows="4"
              v-model="scope.row.checkSituation"
              type="textarea"/>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          property=""
          width="100"
          label="操作">
          <template v-slot="scope">
            <el-button 
              type="danger"
              icon="el-icon-remove-outline" 
              size="mini"
              @click="delRow(scope.row)"/>
          </template>
        </el-table-column>
      </el-table>
    </screen-border>

    <!--修改-->
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="60%"
      top="5vh"
      class="screen-dialog"
      title="编辑">
      <template v-slot:title>
        <div class="custom-dialog-title">
          编辑
        </div>
      </template>
      <div class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">责任</div>
          <el-select
            v-model="reviseRow.liability"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in liabilityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">工作岗</div>
          <el-input
            :rows="3"
            v-model="reviseRow.jobs"
            type="textarea"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">危险源</div>
          <el-input
            :rows="3"
            v-model="reviseRow.dangerSource"
            type="textarea"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">工作任务或风险描述</div>
          <el-input
            :rows="3"
            v-model="reviseRow.jobRisk"
            type="textarea"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">事故类别</div>
          <el-input
            :rows="3"
            v-model="reviseRow.accidentCategory"
            type="textarea"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">RAR</div>
          <el-input v-model="reviseRow.rar"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">安全风险等级</div>
          <el-select 
            v-model="reviseRow.riskLevel" 
            clearable 
            placeholder="请选择">
            <el-option
              v-for="item in riskLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">时间</div>
          <el-date-picker
            v-model="reviseRow.time"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            type="date"
            placeholder="选择日期时间"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">厂部检查情况</div>
          <el-input
            :rows="3"
            v-model="reviseRow.checkSituation"
            type="textarea"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="sumbit">
          确定
        </span>
      </div>
    </el-dialog>
    <!-- 图片显示 -->
    <el-image 
      v-show="false"
      ref="previewImg"
      :preview-src-list="srcList"
      src=""/>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { saveAs } from 'file-saver'
import {
  RISKALL,
  RISKADDS,
  RISKDEL,
  RISKUPDATA,
  RISKUPLOADBEFORE,
  RISKUPLOADAFTER
} from '@/api/screenC2'
import moment from 'moment'

export default {
  name: 'SignificantRisks',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      //时间区间
      TimeInterval: ['', ''],
      liability: '',
      //责任下拉
      liabilityOptions: [
        {
          value: '原料区域',
          label: '原料区域'
        },
        {
          value: '炼钢区域',
          label: '炼钢区域'
        },
        {
          value: '精炼区域',
          label: '精炼区域'
        },
        {
          value: '连铸区域',
          label: '连铸区域'
        }
      ],

      tableData: [],

      //新增行
      newData: [],
      //风险等级下拉
      riskLevelOptions: [
        {
          value: '一般风险',
          label: '一般风险'
        },
        {
          value: '较大风险',
          label: '较大风险'
        },
        {
          value: '重大风险',
          label: '重大风险'
        }
      ],
      isNew: false,
      rowNum: 0,

      //编辑弹框
      dialogVisible: false,
      reviseRow: {},

      //上传Excel
      fileList: [],
      //table中图片上传
      fileList1: [],
      fileList2: [],
      uploadFileList: [],

      srcList: [],
      loading: false
    }
  },
  watch: {},

  mounted() {
    this.getRiskData()
  },
  methods: {
    //获取重大风险数据
    async getRiskData() {
      this.loading = true
      if (this.TimeInterval == '' || this.TimeInterval == null) {
        this.TimeInterval = ['', '']
      }
      let res = await post(RISKALL, {
        startTime: this.TimeInterval[0],
        endTime: this.TimeInterval[1],
        liability: this.liability
      })

      console.log('重大风险', res)
      if (res) {
        this.loading = false
      }
      if (res.data && res.data.length != 0) {
        res.data.forEach(item => {
          if (item.beforeImgName) {
            item.checkPics = [
              {
                name: item.beforeImgName,
                img: item.beforeImgUrl.replace(
                  'http://172.25.63.72:9123/',
                  '/minoApi/'
                ),
                checked: false
              }
            ]
          } else {
            item.checkPics = []
          }
          if (item.afterImgName) {
            item.correctivePics = [
              {
                name: item.afterImgName,
                img: item.afterImgUrl.replace(
                  'http://172.25.63.72:9123/',
                  '/minoApi/'
                ),
                checked: false
              }
            ]
          } else {
            item.correctivePics = []
          }
        })
        this.tableData = res.data
      }
    },
    //添加行
    addNewRow() {
      this.isNew = true
      this.newData.push({
        rowNum: (this.rowNum += 1),
        liability: '',
        jobs: '',
        dangerSource: '',
        jobRisk: '',
        accidentCategory: '',
        rar: '',
        riskLevel: '',
        time: '',
        checkSituation: '',
        setDate: this.selectDate
      })
    },

    //删除新增行
    delRow(row) {
      this.newData.forEach((item, index) => {
        if (row.rowNum == item.rowNum) {
          this.newData.splice(index, 1)
        }
      })
    },

    //保存新增数据
    async saveNewData() {
      this.newData.forEach(item => {
        if (item.rowNum) {
          delete item.rowNum
        }
      })

      let res = await post(RISKADDS, this.newData)

      if (res.status == 1) {
        //清空搜索条件
        this.TimeInterval = ['', '']
        this.liability = ''

        this.getRiskData()
        this.back()
        this.$message.success(res.data)
      } else {
        this.$message.success('新增失败!')
      }
    },
    //返回
    back() {
      this.isNew = false
      this.newData = []
      this.rowNum = 0
    },
    //下载模板
    DownloadExcel() {
      const data = [
        {
          liability: '责任',
          jobs: '工作岗',
          dangerSource: '危险源',
          jobRisk: '工作任务或风险描述',
          accidentCategory: '事故类别',
          rar: 'RAR',
          riskLevel: '安全风险等级',
          time: '时间',
          checkSituation: '厂部检查情况'
        }
      ]
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `重大风险模板.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          '重大风险.xlsx'
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          liability: 'A',
          jobs: 'B',
          dangerSource: 'C',
          jobRisk: 'D',
          accidentCategory: 'E',
          rar: 'F',
          riskLevel: 'G',
          time: 'H',
          checkSituation: 'I'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        list.forEach(item => {
          item.setDate = this.selectDate
        })
        this.newData = list
        setTimeout(() => {
          this.saveNewData()
        }, 500)
      })
    },

    //编辑
    editRow(row) {
      this.dialogVisible = true
      this.reviseRow = {}
      this.reviseRow = JSON.parse(JSON.stringify(row))
    },
    //编辑确定
    async sumbit() {
      let res = await post(RISKUPDATA, this.reviseRow)
      if (res.status == 1) {
        this.dialogVisible = false
        this.getRiskData()
        this.$message.success('编辑成功!')
      }
    },
    //删除
    DelRow(row) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await post(RISKDEL, {
          id: row.id
        })
        if (res.status == 1) {
          this.getRiskData()
          this.$message.success('删除成功!')
        }
      })
    },

    //图片查看
    async getImgFormat(val) {
      this.$refs['previewImg'].showViewer = true
      this.srcList = []
      this.srcList.push(val)
    },

    //删除检查图片
    delExamineImg(row) {
      let bol = row.checkPics.some(item => item.checked)
      if (!bol) {
        this.$message.warning('没有选中的图片,请选择!')
        return
      }
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        row.beforeImgName = ''
        row.beforeImgUrl = ''

        let res = await post(RISKUPDATA, row)
        if (res.status == 1) {
          this.getRiskData()
          this.$message.success('删除成功!')
        }
      })
    },
    //删除整改图片
    delRectificationimg(row) {
      let bol = row.correctivePics.some(item => item.checked)
      if (!bol) {
        this.$message.warning('没有选中的图片,请选择!')
        return
      }
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        row.afterImgName = ''
        row.afterImgUrl = ''

        let res = await post(RISKUPDATA, row)
        if (res.status == 1) {
          this.getRiskData()
          this.$message.success('删除成功!')
        }
      })
    },

    //上传成功后的回调
    async handleSuccess(response, file, fileList, row, val) {
      this.loading = true

      let formData = new FormData()
      let fileData = {
        id: row.id
      }

      const blob = new Blob([JSON.stringify(fileData)], {
        type: 'application/json'
      })
      formData.append('file', file.raw)
      formData.append('EquipmentTree', blob)

      let res = null
      if (val == 'examine') {
        res = await post(RISKUPLOADBEFORE, formData)
      } else {
        res = await post(RISKUPLOADAFTER, formData)
      }
      if (res.includes('http')) {
        this.loading = false
        this.$message.success('上传成功')
        this.getRiskData()
      }
    },

    //超出文件个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      })
      return
    },
    //上传文件之前
    beforeUpload(file) {
      if (file.type != '' || file.type != null || file.type != undefined) {
        //截取文件的后缀，判断文件类型
        const FileExt = file.name.replace(/.+\./, '').toLowerCase()
        //计算文件的大小
        const isLt5M = file.size / 1024 / 1024 < 500 //这里做文件大小限制
        let fileType = ['pdf', 'png', 'jpg'] //设置文件类型
        // 如果大于50M
        if (!isLt5M) {
          this.$message('上传文件大小不能超过 500MB!')
          return false
        }
        //如果文件类型不在允许上传的范围内
        if (fileType.includes(FileExt)) {
          return true
        } else {
          this.$message.error('上传文件格式不正确!')
          return false
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  color: #fff;
  height: calc(100vh - 128px);
  .header {
    display: flex;
    .searchCriteria {
      display: flex;
      align-items: center;
      /deep/.el-input {
        width: 180px;
        margin: 0 5px;
        font-size: 14px;
      }
      /deep/.el-input__inner {
        background: rgba(31, 198, 255, 0.3);
        color: white;
      }
      /deep/.el-date-editor .el-range-separator {
        width: 10%;
      }
      /deep/.el-date-editor .el-range-input,
      /deep/.el-date-editor .el-range-separator {
        color: white;
      }
      /deep/.el-range-editor--small .el-range-separator {
        line-height: 28px;
      }

      .search {
        height: 32px !important;
        line-height: 32px;
        margin-right: 30px;
        border: 1px solid white;
      }
    }
  }
  .newCss {
    /deep/.el-input__inner,
    /deep/.el-textarea__inner {
      background: rgba(31, 198, 255, 0.2);
      color: #fff;
      border-color: rgba(31, 198, 255, 0.6);
    }
    /deep/.el-date-editor.el-input {
      width: 184px;
      .el-input__suffix {
        top: 1px;
      }
    }
    /deep/.el-input__prefix {
      top: 1px;
    }
    /deep/.el-button {
      border-radius: 4px !important;
      font-weight: 700;
      font-size: 20px;
      padding: 4px 20px !important;
    }
  }
  .dialog-body {
    overflow: scroll;
    .dialog-cell {
      margin-bottom: 12px;

      .dialog-cell-title {
        font-size: 16px;
        font-weight: bolder;
        color: #ffffff;
        line-height: 24px;
        margin-bottom: 8px;
      }

      .dialog-cell-title::before {
        content: '1';
        color: #ffffff;
        background: #ffffff;
        width: 8px;
        height: 100%;
        margin-right: 4px;
      }
      /deep/.el-input {
        width: 220px;
      }
    }
  }
  .actionBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .fileBox {
      width: 102px;
      /deep/.el-checkbox {
        margin-right: 0;
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        .el-checkbox__label {
          padding-left: 6px;
          height: 19px;
        }
        .el-checkbox__input {
          top: 2px;
        }
      }
      .fileTitle {
        display: inline-block;
        width: 80px;
        height: 19px;
        border: 2px solid #0b2a34;
        color: white;
        text-align: left;
        cursor: pointer;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //溢出不换行
      }
    }
    .actionBtn {
      font-size: 20px;
      cursor: pointer;
      margin: 4px 0;
    }
  }
}
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
</style>
