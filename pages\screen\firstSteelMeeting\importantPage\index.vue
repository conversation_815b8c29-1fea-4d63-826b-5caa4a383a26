<!--重要事项跟踪-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border-multi>
                <template v-slot:title>
                  <div class="tabs-class">
                    <div
                      v-for="(item, index) in tabList"
                      :key="item.id"
                      :class="{'tab-pane-active': item.active}"
                      class="tab-pane"
                      @click="clickTabPane(item, index)">
                      <div class="tab-pane-title-class">
                        <div>{{ item.title }}</div>
                        <div
                          v-if="item.active"
                          class="tab-pane-img">
                          <img
                            class="tab-pane-img2"
                            src="@/assets/images/screen/tab-pane-active-line2.png"
                            alt="">
                          <img
                            class="tab-pane-img1"
                            src="@/assets/images/screen/tab-pane-active-line.png"
                            alt="">
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-slot:headerRight>
                  <span
                    class="screen-btn"
                    @click="clickAddImportant">
                    <el-icon class="el-icon-edit-outline"/>
                    新增
                  </span>
                </template>

                <div>
                  <div
                    v-show="active === 0"
                    ref="table1"
                    class="scroll-wrapper">
                    <el-table
                      v-loading="ImportantData.loading"
                      :data="ImportantData.showGridData"
                      border>
                      <el-table-column
                        show-overflow-tooltip
                        width="60"
                        label="序号">
                        <template v-slot="scope">
                          <div>{{ scope.$index+1 }}</div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="事项">
                        <template v-slot="scope">
                          <div>{{ scope.row.item }}</div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="描述">
                        <template v-slot="scope">
                          <div>{{ scope.row.content }}</div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        show-overflow-tooltip
                        width="120"
                        label="时间">
                        <template v-slot="scope">
                          <div>{{ scope.row.time }}</div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        show-overflow-tooltip
                        width="140"
                        label="备注">
                        <template v-slot="scope">
                          <div>{{ scope.row.remarks }}</div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        align="center"
                        property=""
                        width="150"
                        label="操作">
                        <template v-slot="scope">
                          <span
                            style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                            @click="clickImportantItem(scope.row)">查看详情</span>
                          <span
                            style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                            @click="clickImportantDeleteItem(scope.row)">删除</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                  <div
                    v-show="active===1"
                    class="scroll-wrapper">
                    <div class="content-div">
                      <div class="form">
                        <div class="form-left">
                          访问统计
                        </div>
                        <div class="form-right">
                          <el-tooltip
                            effect="dark"
                            content="刷新"
                            placement="top-center">
                            <i
                              title="刷新"
                              class="refresh el-icon-refresh"
                              @click="getData"/>
                          </el-tooltip>
                          <i
                            title="排序"
                            class="refresh el-icon-sort"
                            @click="sortChart"/>
                          <el-radio-group v-model="mode">
                            <el-radio-button label="yesterday">昨日</el-radio-button>
                            <el-radio-button label="today">今日</el-radio-button>
                            <el-radio-button label="month">当月</el-radio-button>
                            <el-radio-button label="all">全部</el-radio-button>
                            <el-radio-button label="custom">自定义</el-radio-button>
                          </el-radio-group>
                          <span
                            v-if="mode === 'custom'"
                            class="custom">
                            {{ formData.loginTimeStart }} - {{ formData.loginTimeEnd }}
                            <el-button
                              type="text"
                              @click="customVisible = true">
                              更改
                            </el-button>
                          </span>
                        </div>
                      </div>
                      <div
                        id="trend-chart"
                        class="chart"/>
                      <el-dialog
                        :visible.sync="customVisible"
                        :title="'自定义时间选择'"
                        :width="'380px'">
                        <el-form
                          v-if="customVisible"
                          ref="customForm"
                          :model="formData"
                          label-width="110px"
                          size="medium"
                          @keyup.enter.native="handelCustom"
                        >
                          <el-form-item
                            :label="'开始时间：'"
                            :rules="[
                              {
                                required: true,
                                message: '请选择时间',
                                trigger: 'change'
                              }
                            ]"
                            prop="loginTimeStart"
                          >
                            <el-date-picker
                              v-model="formData.loginTimeStart"
                              :type="exportMode"
                              :value-format="valueFormat"
                              :placeholder="'选择时间'" />
                          </el-form-item>
                          <el-form-item
                            :label="'结束时间：'"
                            :rules="[
                              {
                                required: true,
                                message: '请选择时间',
                                trigger: 'change'
                              }
                            ]"
                            prop="loginTimeEnd"
                          >
                            <el-date-picker
                              v-model="formData.loginTimeEnd"
                              :type="exportMode"
                              :value-format="valueFormat"
                              :placeholder="'选择时间'" />
                          </el-form-item>
                        </el-form>
                        <div slot="footer">
                          <el-button
                            :loading="loading"
                            type="primary"
                            @click="handelCustom"
                          >确定
                          </el-button>
                        </div>
                      </el-dialog>
                    </div>
                  </div>
                </div>

              </screen-border-multi>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--重要事项跟踪新增修改删除-->
    <el-dialog
      :visible.sync="ImportantData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="重要事项跟踪">
      <template v-slot:title>
        <div class="custom-dialog-title">
          重要事项跟踪
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">事项</div>
          <el-input
            v-model="importantItem.item"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">描述</div>
          <el-input
            v-model="importantItem.content"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">时间</div>
          <el-date-picker
            v-model="importantItem.time"
            :clearable="false"
            :size="'mini'"
            :value-format="'yyyy-MM-dd'"
            class="screen-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">备注</div>
          <el-input
            v-model="importantItem.remarks"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="addImportantData()">
          确定
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  firstMeetingImportant1,
  firstMeetingImportant2,
  firstMeetingImportant3,
  getFirstSteelMakingAccess
} from '@/api/firstMeeting'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
export default {
  name: 'ImportantPage',
  components: {
    ScreenBorderMulti,
    SingleBarsChart,
    SteelBarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      active: 0,
      tabList: [
        {
          id: '1',
          type: 'A',
          active: true,
          title: '重要事项跟踪'
        },
        {
          id: '2',
          type: 'B',
          active: false,
          title: '访问统计'
        }
      ],
      cDate: '',
      ImportantData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      importantItem: {},
      chart: null,
      mode: 'yesterday',
      type: 'user',
      org: 'org-1',
      serviceList: [],
      showData: [],
      sort: 'desc',
      visible: false,
      formData: {
        loginTimeStart: '',
        loginTimeEnd: '',
        week: null
      },
      loading: false,
      exportMode: 'date', // 导出模式
      customVisible: false,
      customMode: 'date' // 自定义模式
    }
  },
  computed: {
    valueFormat: function() {
      let format = ''
      switch (this.exportMode) {
        case 'year':
          format = 'yyyy'
          break
        case 'month':
          format = 'yyyy-MM'
          break
        case 'date':
          format = 'yyyy-MM-dd'
          break
        default:
          format = 'yyyy-MM-dd'
      }
      return format
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.$nextTick(_ => {
        this.getImportantData()
        this.drawChart()
        this.getData()
      })
    },
    active: function() {
      if (this.active === 0) {
        this.getImportantData()
      } else {
        this.drawChart()
        this.getData()
      }
    },
    mode(value) {
      if (value === 'custom') {
        this.customVisible = true
      } else {
        this.getData()
      }
    },
    type(value) {
      this.getData()
    },
    org(value) {
      this.getData()
    },
    exportMode() {
      let format = ''
      switch (this.exportMode) {
        case 'date':
          format = 'YYYY-MM-DD'
          break
        case 'month':
          format = 'YYYY-MM'
          break
        case 'year':
          format = 'YYYY'
          break
        default:
          format = 'YYYY-MM-DD'
          break
      }
      this.formData.loginTimeStart = this.$moment(
        this.formData.loginTimeStart
      ).format(format)
      this.formData.loginTimeEnd = this.$moment(
        this.formData.loginTimeEnd
      ).format(format)
    }
  },
  created() {
    this.cDate = this.selectDate
    this.formData = {
      loginTimeStart: this.$moment().format('YYYY-MM-DD'),
      loginTimeEnd: this.$moment().format('YYYY-MM-DD'),
      week: null
    }
  },
  mounted() {
    // this.calculateHeight()
    // window.addEventListener('resize', this.calculateHeight)
    // this.drawChart()
    // this.getData()
  },
  methods: {
    // 排序
    async sortChart() {
      //
      this.showData = _.sortBy(this.showData, 'num')
      if (this.sort === 'desc') {
        this.showData.reverse()
        this.sort = 'asc'
      } else {
        this.sort = 'desc'
      }
      this.updateChart()
    },
    // 获取参数
    async getData() {
      let date = ''
      let start = ''
      let end = ''
      switch (this.mode) {
        case 'yesterday':
          date = this.$moment(
            new Date().getTime() - 24 * 60 * 60 * 1000
          ).format('YYYY-MM-DD')
          start = date
          end = date
          await this.getTrendData(start, end)
          break
        case 'today':
          date = this.$moment().format('YYYY-MM-DD')
          start = date
          end = date
          await this.getTrendData(start, end)
          break
        case 'month':
          date = this.$moment().format('YYYY-MM')
          start = date + '-01'
          end = this.$moment().format('YYYY-MM-DD')
          await this.getTrendData(start, end)
          break
        case 'all':
          date = this.$moment().format('YYYY')
          start = date + '-01-01'
          end = this.$moment().format('YYYY-MM-DD')
          await this.getTrendData(start, end)
          break
        case 'custom':
          await this.getTrendData(
            this.formData.loginTimeStart,
            this.formData.loginTimeEnd
          )
          break
        default:
          break
      }
    },
    // 请求接口
    async getTrendData(start, end) {
      this.chart.showLoading()
      const { data, success } =
        (await post(getFirstSteelMakingAccess, {
          loginTimeStart: start,
          loginTimeEnd: end
        })) || []
      if (!success) return this.chart.hideLoading()
      this.sort = 'desc'
      this.showData = data
      this.updateChart()
    },
    // 自定义确认
    handelCustom() {
      this.customVisible = false
      this.getTrendData(
        this.formData.loginTimeStart,
        this.formData.loginTimeEnd
      )
    },
    // 更新图表
    updateChart() {
      this.chart.setOption({
        xAxis: {
          type: 'category',
          data: this.showData.map(item => item.userName)
        },
        series: [
          {
            data: this.showData.map(item => {
              item.value = item.accessNum
              return item
            }),
            type: 'bar'
          }
        ],
        dataZoom: [
          {
            show: true,
            start: 0,
            end: 100
          }
        ]
      })
      this.chart.hideLoading()
    },
    // 绘制图表
    async drawChart() {
      this.chart = this.$echarts.init(
        document.getElementById('trend-chart'),
        'light'
      )
      this.chart.setOption({
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          name: '访问量'
        },
        dataZoom: [
          {
            show: true,
            start: 0,
            end: 100
          }
        ],
        tooltip: {
          trigger: 'axis',
          formatter: params => {
            console.log(params)
            return (
              params[0].data.userName +
              '<br>' +
              (params[0].data.orgAllName
                ? `单位：${params[0].data.orgAllName}</br>`
                : ``) +
              '访问次数：' +
              params[0].data.accessNum
            )
          },
          axisPointer: {
            type: 'shadow',
            label: {
              show: true
            }
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '50',
          containLabel: true
        },
        series: [
          {
            data: [],
            type: 'bar',
            label: {
              show: true,
              position: 'top',
              valueAnimation: true
            }
          }
        ]
      })

      // 监听窗口改动
      window.onresize = () => {
        this.chart.resize()
      }
    },
    // tab切换
    clickTabPane(item, index) {
      this.tabList.forEach(item => {
        item.active = false
      })
      item.active = true
      this.active = index
    },
    clickAddImportant() {
      this.importantItem = {
        item: '',
        content: '',
        remarks: '',
        time: '',
        setDate: this.cDate
      }
      this.ImportantData.dialogVisible = true
    },
    //隐患点击查看详情
    clickImportantItem(row) {
      this.importantItem = JSON.parse(JSON.stringify(row))
      this.ImportantData.dialogVisible = true
    },
    //隐点击查看详情
    clickImportantDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteImportant(row.id)
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    //新增/修改
    addImportantData() {
      if (
        this.importantItem.time == null ||
        this.importantItem.time.length === 0
      ) {
        this.$message.warning('请选择日期！')
        return
      }
      // 保存钢铁产量信息
      const params = {
        date: this.cDate,
        data: [this.importantItem]
      }
      post(firstMeetingImportant2, params).then(res => {
        if (res.success) {
          this.ImportantData.dialogVisible = false
          this.getImportantData()
        }
      })
    },
    //删除隐患
    deleteImportant(id) {
      const params = [
        {
          id: id
        }
      ]
      post(firstMeetingImportant3, params).then(res => {
        if (res.success) {
          this.$notify.success('删除成功！')
          this.getImportantData()
        }
      })
    },
    calculateHeight() {
      this.ImportantData.maxHeight = this.$refs.table1.offsetHeight
    },
    //查询数据
    getImportantData() {
      this.ImportantData.loading = true
      post(firstMeetingImportant1, {
        time: this.cDate
      })
        .then(res => {
          this.ImportantData.showGridData = res.data.map(item => {
            return {
              id: item.id,
              item: item.item,
              content: item.content,
              remarks: item.remarks,
              time: item.time
            }
          })
          this.ImportantData.gridData = lodash.cloneDeep(
            this.ImportantData.showGridData
          )
        })
        .finally(_ => {
          this.ImportantData.loading = false
        })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
.tabs-class {
  display: flex;
  flex-direction: row;
  .tab-pane {
    color: #ffffffbf;
    margin-right: 21px;
  }
  .tab-pane-active {
    color: #ffffff;
  }
  .tab-pane-title-class {
    display: flex;
    flex-direction: column;
    position: relative;
    .tab-pane-img {
      .tab-pane-img2 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
      .tab-pane-img1 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        margin-bottom: 7px;
      }
    }
  }
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}

/deep/ .el-tabs__header {
  margin-bottom: 0;
}
.content-div {
  background: #fff;
  width: 100%;
  height: 100%;
}
.form {
  padding: 25px 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .refresh {
    font-size: 24px;
    vertical-align: middle;
    margin-right: 10px;
    cursor: pointer;
  }
  .form-left {
    position: relative;
    color: black;
    font-weight: bold;
    font-size: 20px;
    .org-radio {
      position: absolute;
      right: 0;
      top: 115%;
      z-index: 9;
    }
  }
  .form-right {
    position: relative;
    .custom {
      position: absolute;
      top: 100%;
      right: 0;
      z-index: 9;
    }
  }
}
.chart {
  width: calc(100vw - 90px);
  height: calc(100vh - 260px);
}

/deep/ .el-radio {
  margin-right: 10px;
}
</style>
