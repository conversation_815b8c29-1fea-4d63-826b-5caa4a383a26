<template>
  <div class="content">
    <div class="content-item top">
      <custom-table
        :title="'大夜班计划完成情况'"
        :setting="tableObj1.setting"
        :url-list="tableObj1.url.list"
        :url-save="tableObj1.url.save"
        :select-date="selectDate"
        :table-class="'big-table'">
        <template v-slot:headerRight>
          <span
            class="screen-btn"
            @click="noteDialogVisible1 = true">
            情况说明
          </span>
          <span
            v-command="'/screen/B1ProductionMeeting/edit'"
            class="screen-btn"
            @click="noteDialogEdit1 = true">
            <el-icon class="el-icon-edit-outline"/>
            编辑情况说明
          </span>
        </template>
      </custom-table>

      <!--备注 1-->
      <el-dialog
        :visible.sync="noteDialogVisible1"
        :width="'1000px'"
        :close-on-click-modal= "false"
        :append-to-body="false"
        class="screen-dialog">
        <template v-slot:title>
          <div class="custom-dialog-title">
            大夜班计划完成情况说明
          </div>
        </template>
        <p class="note-text">{{ note1 }}</p>
      </el-dialog>
      <!--备注 1-->
      <el-dialog
        :visible.sync="noteDialogEdit1"
        :width="'1000px'"
        :close-on-click-modal= "false"
        :append-to-body="false"
        class="screen-dialog">
        <template v-slot:title>
          <div class="custom-dialog-title">
            大夜班计划完成情况说明编辑
          </div>
        </template>
        <el-form
          label-width="120px"
          class="demo-form-inline">
          <el-form-item label="情况说明">
            <el-input
              v-model="note1Edit"
              :rows="8"
              type="textarea"/>
          </el-form-item>
          <div class="text-center">
            <el-button
              type="primary"
              @click="saveParams(note1Edit, 'B1GraveyardShiftPlan')">确定
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <custom-table
        :title="'大夜班转炉生产情况'"
        :setting="tableObj2.setting"
        :url-list="tableObj2.url.list"
        :url-save="tableObj2.url.save"
        :select-date="selectDate"
        :table-class="'big-table'">
        <template v-slot:headerRight>
          <span
            class="screen-btn"
            @click="noteDialogVisible2 = true">
            情况说明
          </span>
          <span
            v-command="'/screen/B1ProductionMeeting/edit'"
            class="screen-btn"
            @click="noteDialogEdit2 = true">
            <el-icon class="el-icon-edit-outline"/>
            编辑情况说明
          </span>
        </template>
      </custom-table>
      <!--备注 2-->
      <el-dialog
        :visible.sync="noteDialogVisible2"
        :width="'1000px'"
        :close-on-click-modal= "false"
        :append-to-body="false"
        class="screen-dialog">
        <template v-slot:title>
          <div class="custom-dialog-title">
            大夜班转炉生产情况说明
          </div>
        </template>
        <p class="note-text">{{ note2 }}</p>
      </el-dialog>
      <!--备注 1-->
      <el-dialog
        :visible.sync="noteDialogEdit2"
        :width="'1000px'"
        :close-on-click-modal= "false"
        :append-to-body="false"
        class="screen-dialog">
        <template v-slot:title>
          <div class="custom-dialog-title">
            大夜班转炉生产情况说明编辑
          </div>
        </template>
        <el-form
          label-width="120px"
          class="demo-form-inline">
          <el-form-item label="情况说明">
            <el-input
              v-model="note2Edit"
              :rows="8"
              type="textarea"/>
          </el-form-item>
          <div class="text-center">
            <el-button
              type="primary"
              @click="saveParams(note2Edit, 'B1GraveyardShiftConverter')">确定
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/B1ProductionMeeting/component/custom-table'
import {
  accidentConditionFind,
  accidentConditionSave,
  converterNightFind,
  converterNightSave,
  emergencyShutdownFind,
  emergencyShutdownSave,
  planCompletionNightFind,
  planCompletionNightSave
} from '@/api/screenB1Production'
import { post } from '@/lib/Util'
import {
  findBoardParameterByDateAndPara,
  saveBoardParameter
} from '@/api/screen'
export default {
  name: 'GraveyardShift',
  components: { CustomTable, SingleBarsChart },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      noteDialogVisible1: false,
      noteDialogEdit1: false,
      note1: '',
      note1Edit: '',
      noteDialogVisible2: false,
      noteDialogEdit2: false,
      note2: '',
      note2Edit: '',
      tableObj1: {
        url: {
          save: planCompletionNightSave,
          list: planCompletionNightFind
        },
        setting: [
          {
            keyQuery: 'planfurnacenumtotal',
            keySave: 'planFurnaceNumTotal',
            label: '当班计划总炉数'
          },
          {
            keyQuery: 'realityfurnacenumtotal',
            keySave: 'realityFurnaceNumTotal',
            label: '当班实际总炉数'
          },
          {
            keyQuery: 'prcline',
            keySave: 'prcLine',
            label: '铸机号'
          },
          {
            keyQuery: 'realityfurnacenum',
            keySave: 'realityFurnaceNum',
            label: '实际炉数'
          },
          {
            keyQuery: 'stlgrd',
            keySave: 'stlGrd',
            label: '钢种'
          },
          {
            keyQuery: 'reason',
            keySave: 'reason',
            class: 'red',
            label: '原因'
          }
        ]
      },
      tableObj2: {
        url: {
          save: converterNightSave,
          list: converterNightFind
        },
        setting: [
          {
            keyQuery: 'planfurnacenumtotal',
            keySave: 'planFurnaceNumTotal',
            label: '当班计划总炉数'
          },
          {
            keyQuery: 'furnacenumber',
            keySave: 'furnaceNumber',
            label: '炉座号'
          },
          {
            keyQuery: 'realityfurnacenum',
            keySave: 'realityFurnaceNum',
            label: '实际炉数'
          },
          {
            keyQuery: 'changeskateboard',
            keySave: 'changeSkateboard',
            label: '换滑板次'
          },
          {
            keyQuery: 'padiron',
            keySave: 'padIron',
            label: '垫生铁次数'
          },
          {
            keyQuery: 'padding',
            keySave: 'padding',
            label: '垫补次数'
          },
          {
            keyQuery: 'gunning',
            keySave: 'gunning',
            label: '喷补次数'
          },
          {
            keyQuery: 'tappinghole',
            keySave: 'tappingHole',
            label: '出钢口'
          }
        ]
      }
    }
  },
  computed: {
    selectedMonth: function() {
      return this.$moment(this.selectDate).format('YYYY-MM')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    }
  },
  created() {
    this.cDate = this.selectDate
    this.getParamData()
  },
  methods: {
    saveParams(text, name) {
      const params = {
        data: [
          {
            parameter: name,
            content: text,
            setDate: this.cDate
          }
        ]
      }
      post(saveBoardParameter, params).then(res => {
        this.loading = false
        if (res.status === 1) {
          this.noteDialogEdit1 = false
          this.noteDialogEdit2 = false
          this.getParamData()
          this.$message.success('保存成功！')
        }
      })
    },
    async getParamData() {
      post(findBoardParameterByDateAndPara, {
        setDate: this.cDate
      }).then(res => {
        this.note1 = this.note1Edit = this.getParam(
          'B1GraveyardShiftPlan',
          res.data
        )
        this.note2 = this.note2Edit = this.getParam(
          'B1GraveyardShiftConverter',
          res.data
        )
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.note-text {
  font-size: 20px;
  color: #fff;
  line-height: 1.5;
  text-indent: 2em;
}
</style>
