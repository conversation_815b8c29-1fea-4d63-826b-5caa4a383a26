<template>
  <div class="container">
    <screen-border title="补焊数据分析">
      <div class="radio-container">
        <div class="radio-group-wrapper">
          <div class="radio-selector">
            <el-radio
              v-model="selectedDateType"
              label="daily"
              @change="handleDateChange('daily')">每日
            </el-radio>
            <el-radio
              v-model="selectedDateType"
              label="custom"
              @change="handleDateChange('custom')">自定义
            </el-radio>
          </div>
          <!-- <el-button 
            :class="['custom-radio-btn', selectedDateType === 'daily' ? 'active' : '']" 
            @click="selectedDateType = 'daily'; handleDateChange('daily')">
            每日
          </el-button>
          <el-button 
            :class="['custom-radio-btn', selectedDateType === 'custom' ? 'active' : '']" 
            @click="selectedDateType = 'custom'; handleDateChange('custom')">
            自定义
          </el-button> -->
        </div>
        
        <div 
          v-if="selectedDateType === 'custom'" 
          class="date-picker-wrapper">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            class="date-picker"
            popper-class="date-picker-dropdown"
            @change="handleCustomDateChange"
          />
        </div>
      </div>
      <div 
        v-show="!noData" 
        ref="chartContainer"
        class="chart-container"/>
      <div 
        v-show="noData" 
        class="no-data-container">
        <i class="el-icon-data-analysis"/>
        <p>暂无数据</p>
      </div>
    </screen-border>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { weldingDataAnalysisFindAllDate } from '@/api/screenC2'
import ScreenBorder from '@/pages/screen/C2Meeting/component/screen-border.vue'
import { post } from '@/lib/Util'

export default {
  name: 'WeldingManagement',
  components: {
    ScreenBorder
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null,
      chartData: {
        oneData: [], // 3-22数据
        twoData: [], // 3-23数据
        threeData: [] // 3-24数据
      },
      noData: false,
      selectedDateType: 'daily', // 默认选择每日
      dateRange: null, // 日期范围
      startDate: '', // 开始日期
      endDate: '' // 结束日期
    }
  },
  watch: {
    selectDate: {
      handler(newVal) {
        if (newVal) {
          this.selectedDateType = 'daily'
          this.fetchData(newVal)
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.fetchData(this.selectDate)

      // 窗口大小变化时重绘图表
      window.addEventListener('resize', this.handleResize)
    })
  },
  beforeDestroy() {
    // 清除事件监听和销毁图表
    window.removeEventListener('resize', this.handleResize)
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    // 处理日期类型切换
    handleDateChange(value) {
      if (value === 'daily') {
        this.selectedDateType = 'daily'
        // 每日模式下使用传入的selectDate
        this.fetchData(this.selectDate)
      } else if (value === 'custom') {
        this.selectedDateType = 'custom'
        // 如果已经有日期范围，则使用它
        if (this.dateRange) {
          this.handleCustomDateChange(this.dateRange)
        } else {
          // 当没有日期范围时，显示无数据状态
          this.noData = true
          if (this.chart) {
            this.renderChart(null, null, null)
          }
        }
      }
    },

    // 处理自定义日期变化
    handleCustomDateChange(dateRange) {
      if (dateRange === null) {
        this.handleDateClear()
      } else if (dateRange && dateRange.length === 2) {
        this.startDate = dateRange[0]
        this.endDate = dateRange[1]
        this.fetchDataCustom()
      }
    },

    // 获取自定义日期范围数据
    fetchDataCustom() {
      if (!this.startDate || !this.endDate) return

      post(weldingDataAnalysisFindAllDate, {
        type: 'custom',
        startDate: this.startDate,
        endDate: this.endDate
      })
        .then(res => {
          if (res && res.data && res.data.length > 0) {
            this.noData = false
            this.renderCustomChart(res.data)
          } else {
            this.noData = true
          }
        })
        .catch(error => {
          console.error('获取焊接数据失败', error)
          this.noData = true
        })
    },

    // 渲染自定义日期范围数据的图表
    renderCustomChart(data) {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartContainer)
      }

      // 提取X轴数据（责任单位）
      const xAxisData = data.map(item => item.ZRDW)

      // 提取Y轴数据（数量）
      const seriesData = data.map(item => item.ZADW_COUNT)

      const option = {
        grid: {
          left: '5%',
          right: '5%',
          top: '10%',
          bottom: '5%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(0, 240, 255, 0.1)'
            }
          },
          formatter: function(params) {
            const param = params[0]
            return (
              param.name +
              '<br/>' +
              '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#00BFFF;"></span>' +
              '数量: ' +
              param.value
            )
          },
          backgroundColor: 'rgba(0, 15, 40, 0.85)',
          borderColor: 'rgba(0, 240, 255, 0.8)',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 12
          }
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.5)'
            }
          },
          axisLabel: {
            color: '#fff',
            interval: 0,
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '块',
          nameTextStyle: {
            color: '#fff'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.5)'
            }
          },
          axisLabel: {
            color: '#fff'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        series: [
          {
            type: 'bar',
            data: seriesData,
            barWidth: '20%', // 单柱模式下宽度可以更大
            itemStyle: {
              // 添加科技感渐变
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00BFFF' },
                { offset: 1, color: '#1A8CFF' }
              ]),
              borderRadius: [3, 3, 0, 0], // 柱状图顶部圆角
              borderColor: 'rgba(0, 240, 255, 0.5)',
              borderWidth: 1,
              shadowColor: 'rgba(0, 240, 255, 0.5)',
              shadowBlur: 10
            },
            label: {
              show: true,
              position: 'top',
              color: '#fff'
            }
          }
        ]
      }

      this.chart.setOption(option, true)
    },

    // 窗口大小变化时重绘图表的方法
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },

    // 获取数据的方法
    fetchData(date) {
      if (!date) return

      // 调用API获取数据
      post(weldingDataAnalysisFindAllDate, {
        type: 'daily',
        date: this.selectDate
      })
        .then(res => {
          this.processResponseData(res)
        })
        .catch(error => {
          console.error('获取焊接数据失败', error)
          this.noData = true
          this.chartData = { oneData: [], twoData: [], threeData: [] }
        })
    },

    // 处理API响应数据
    processResponseData(res) {
      const hasOneData =
        res && res.oneData && res.oneData.data && res.oneData.data.length
      const hasTwoData =
        res && res.twoData && res.twoData.data && res.twoData.data.length
      const hasThreeData =
        res && res.threeData && res.threeData.data && res.threeData.data.length

      if (hasOneData || hasTwoData || hasThreeData) {
        // 处理三天的数据
        this.chartData = {
          oneData: res.oneData && res.oneData.data ? res.oneData.data : [],
          twoData: res.twoData && res.twoData.data ? res.twoData.data : [],
          threeData:
            res.threeData && res.threeData.data ? res.threeData.data : []
        }
        this.noData = false

        // 获取日期，避免使用可选链式语法
        const oneDate =
          res.oneData && res.oneData.date ? res.oneData.date : null
        const twoDate =
          res.twoData && res.twoData.date ? res.twoData.date : null
        const threeDate =
          res.threeData && res.threeData.date ? res.threeData.date : null
        this.renderChart(oneDate, twoDate, threeDate)
      } else {
        this.noData = true
        this.chartData = { oneData: [], twoData: [], threeData: [] }
      }
    },

    // 渲染图表
    renderChart(oneDate, twoDate, threeDate) {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartContainer)
      }

      // 格式化日期的函数: 20250310 => 2025-03-10
      const formatDate = dateStr => {
        if (!dateStr || dateStr.length !== 8) return dateStr
        return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(
          6,
          8
        )}`
      }

      // 获取所有的责任单位（ZRDW）
      const allUnits = new Set()

      // 收集所有出现过的责任单位
      this.chartData.oneData.forEach(item => allUnits.add(item.ZRDW))
      this.chartData.twoData.forEach(item => allUnits.add(item.ZRDW))
      this.chartData.threeData.forEach(item => allUnits.add(item.ZRDW))

      const xAxisData = Array.from(allUnits)

      // 为每个日期准备数据，将0值替换为'-'以便不显示柱子
      const getSeriesData = (data, units) => {
        return units.map(unit => {
          const item = data.find(d => d.ZRDW === unit)
          // 当找不到数据或数值为0时返回'-'，这样柱子就不会显示
          return item && item.ZADW_COUNT ? item.ZADW_COUNT : '-'
        })
      }

      // 使用后端返回的日期格式，并格式化为YYYY-MM-DD
      const dateLabel1 = formatDate(oneDate) || ''
      const dateLabel2 = formatDate(twoDate) || ''
      const dateLabel3 = formatDate(threeDate) || ''

      // 使用格式化后的日期标签
      const legendData = [dateLabel1, dateLabel2, dateLabel3]

      const oneSeriesData = getSeriesData(this.chartData.oneData, xAxisData)
      const twoSeriesData = getSeriesData(this.chartData.twoData, xAxisData)
      const threeSeriesData = getSeriesData(this.chartData.threeData, xAxisData)

      const option = {
        grid: {
          left: '5%',
          right: '5%',
          top: '20%',
          bottom: '5%',
          containLabel: true
        },
        legend: {
          show: this.selectedDateType === 'daily',
          data: legendData,
          textStyle: {
            color: '#fff'
          },
          right: 10,
          top: 10
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(0, 240, 255, 0.1)'
            }
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>'

            // 为每个系列定义固定颜色
            const seriesColors = {
              [legendData[0]]: '#00BFFF', // 第一天数据颜色
              [legendData[1]]: '#FFCC00', // 第二天数据颜色
              [legendData[2]]: '#7CFF00' // 第三天数据颜色
            }

            params.forEach(param => {
              const valueDisplay = param.value === '-' ? '-' : param.value
              // 使用预定义的固定颜色而不是param.color
              const color = seriesColors[param.seriesName] || '#ffffff'
              const marker = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`
              result +=
                marker + param.seriesName + ': ' + valueDisplay + '<br/>'
            })

            return result
          },
          backgroundColor: 'rgba(0, 15, 40, 0.85)',
          borderColor: 'rgba(0, 240, 255, 0.8)',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 12
          }
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.5)'
            }
          },
          axisLabel: {
            color: '#fff',
            interval: 0,
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '块',
          nameTextStyle: {
            color: '#fff'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.5)'
            }
          },
          axisLabel: {
            color: '#fff'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        series: [
          {
            name: legendData[0],
            type: 'bar',
            data: oneSeriesData,
            barWidth: '12%', // 减小柱宽
            barGap: '30%', // 增加柱间距
            itemStyle: {
              // 添加科技感渐变
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00BFFF' },
                { offset: 1, color: '#1A8CFF' }
              ]),
              borderRadius: [3, 3, 0, 0], // 柱状图顶部圆角
              borderColor: 'rgba(0, 240, 255, 0.5)',
              borderWidth: 1,
              shadowColor: 'rgba(0, 240, 255, 0.5)',
              shadowBlur: 10
            },
            label: {
              show: true,
              position: 'top',
              color: '#fff',
              formatter: function(params) {
                return params.value === '-' ? '' : params.value
              }
            }
          },
          {
            name: legendData[1],
            type: 'bar',
            data: twoSeriesData,
            barWidth: '12%', // 减小柱宽
            itemStyle: {
              // 添加科技感渐变
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#FFCC00' },
                { offset: 1, color: '#FF9A00' }
              ]),
              borderRadius: [3, 3, 0, 0], // 柱状图顶部圆角
              borderColor: 'rgba(255, 154, 0, 0.5)',
              borderWidth: 1,
              shadowColor: 'rgba(255, 154, 0, 0.5)',
              shadowBlur: 10
            },
            label: {
              show: true,
              position: 'top',
              color: '#fff',
              formatter: function(params) {
                return params.value === '-' ? '' : params.value
              }
            }
          },
          {
            name: legendData[2],
            type: 'bar',
            data: threeSeriesData,
            barWidth: '12%', // 减小柱宽
            itemStyle: {
              // 添加科技感渐变
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#7CFF00' },
                { offset: 1, color: '#4CD964' }
              ]),
              borderRadius: [3, 3, 0, 0], // 柱状图顶部圆角
              borderColor: 'rgba(76, 217, 100, 0.5)',
              borderWidth: 1,
              shadowColor: 'rgba(76, 217, 100, 0.5)',
              shadowBlur: 10
            },
            label: {
              show: true,
              position: 'top',
              color: '#fff',
              formatter: function(params) {
                return params.value === '-' ? '' : params.value
              }
            }
          }
        ]
      }

      this.chart.setOption(option)
    },

    // 处理日期清空事件
    handleDateClear() {
      this.dateRange = null
      this.startDate = ''
      this.endDate = ''
      this.noData = true
      // 重新渲染图表以隐藏legend
      if (this.chart) {
        this.renderChart(null, null, null)
      }
    }
  }
}
</script>

<style lang="less">
/* 日期选择器下拉面板科技风格 */
.date-picker-dropdown {
  background-color: rgba(4, 29, 53, 0.95);
  border: 1px solid rgba(0, 242, 254, 0.7);
  box-shadow: 0 0 15px rgba(0, 242, 254, 0.3);

  /* 标题和头部样式 */
  .el-date-range-picker__header {
    margin: 8px;
    color: #fff;
  }

  /* 日期表格样式 */
  .el-picker-panel__content {
    color: #fff;

    .el-date-table {
      th {
        color: rgba(0, 242, 254, 0.8);
        font-weight: normal;
        border-bottom: 1px solid rgba(0, 242, 254, 0.3);
      }

      td {
        &.available:hover {
          background-color: rgba(0, 242, 254, 0.2);
        }

        &.in-range div {
          background-color: rgba(0, 242, 254, 0.15);
          color: #fff;
        }

        &.end-date div,
        &.start-date div {
          background-color: rgba(0, 242, 254, 0.8);
          color: #000;
        }

        &.today span {
          color: #1fc6ff;
        }

        &.disabled div {
          background-color: transparent;
          color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  /* 按钮样式 */
  .el-picker-panel__footer {
    background-color: rgba(4, 29, 53, 0.8);
    border-top: 1px solid rgba(0, 242, 254, 0.3);

    .el-button {
      background: transparent;
      border: 1px solid rgba(0, 242, 254, 0.5);
      color: #fff;

      &:hover {
        background-color: rgba(0, 242, 254, 0.2);
        border-color: rgba(0, 242, 254, 0.8);
      }

      &--default {
        margin-right: 10px;
      }

      &--primary {
        background-color: rgba(0, 242, 254, 0.3);
        border-color: rgba(0, 242, 254, 0.7);

        &:hover {
          background-color: rgba(0, 242, 254, 0.5);
        }
      }
    }
  }

  /* 箭头样式 */
  .el-date-range-picker__header .el-icon-arrow-left,
  .el-date-range-picker__header .el-icon-arrow-right,
  .el-date-range-picker__header .el-icon-d-arrow-left,
  .el-date-range-picker__header .el-icon-d-arrow-right {
    color: rgba(0, 242, 254, 0.8);

    &:hover {
      color: rgba(0, 242, 254, 1);
    }
  }

  .el-date-table td.end-date span,
  .el-date-table td.start-date span {
    background-color: rgba(0, 242, 254, 0.6);
    color: #fff;
    font-weight: bold;
  }
}
</style>

<style scoped lang="less">
.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  color: #fff;
}

.title {
  font-size: 24px;
  margin-bottom: 20px;
  text-shadow: 0 0 10px rgba(0, 242, 254, 0.5);
}

.chart-container {
  width: 100%;
  height: calc(100% - 60px);
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 242, 254, 0.2);
  overflow: hidden;
}

.no-data-container {
  width: 100%;
  height: calc(100% - 60px);
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 242, 254, 0.2);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 20, 40, 0.3);

  i {
    font-size: 60px;
    color: rgba(0, 242, 254, 0.6);
    margin-bottom: 20px;
  }

  p {
    font-size: 20px;
    color: #fff;
  }
}

.radio-container {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  justify-content: space-between;
  width: 100%;
}

.radio-group-wrapper {
  display: flex;
}

.custom-radio-btn {
  margin-right: 10px;
  background: transparent;
  border: 1px solid rgba(0, 242, 254, 0.5);
  color: #fff;
  padding: 8px 15px;
  transition: all 0.3s;

  &.active {
    background-color: #1751a6; // 蓝色背景，根据图片调整
    color: #fff;
    border-color: #1751a6;
  }

  &:hover {
    background-color: rgba(23, 81, 166, 0.7);
    border-color: rgba(0, 242, 254, 0.8);
  }
}

.date-picker-wrapper {
  display: block;
}

.date-picker {
  width: 350px;
  margin-left: 15px;
}

.radio-selector {
  display: flex;
  margin: 10px;

  .el-radio {
    margin-right: 20px;
    color: #fff;

    /deep/ .el-radio__label {
      color: #fff;
      font-size: 16px;
    }

    /deep/ .el-radio__input.is-checked .el-radio__inner {
      border-color: #1fc6ff;
      background: #1fc6ff;
    }

    /deep/ .el-radio__input.is-checked + .el-radio__label {
      color: #1fc6ff;
    }

    /deep/ .el-radio__inner {
      background-color: transparent;
      border: 1px solid #fff;
    }
  }
}

/* 优化日期选择器内部文本样式 */
/deep/ .el-date-editor {
  background-color: rgba(4, 29, 53, 0.7);
  border: 1px solid rgba(0, 242, 254, 0.5);

  .el-range-input {
    background-color: transparent;
    color: #fff;

    &::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }

    &:focus {
      border: none !important;
    }
  }

  .el-range-separator,
  .el-input__icon {
    color: rgba(0, 242, 254, 0.8);
  }

  /* 调整输入框间距和对齐方式 */
  .el-range__icon {
    transform: translateY(3px);
    color: rgba(0, 242, 254, 0.8);
  }

  .el-range-separator {
    padding: 0 5px;
    transform: translate(-10px, 5px);
  }

  .el-input__inner {
    vertical-align: middle;
  }

  /* 调整输入框高度和内边距 */
  &.el-input__inner,
  .el-range-input {
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
  }

  /* 针对开始日期和结束日期文本的特定样式 */
  .el-range__close-icon {
    color: rgba(0, 242, 254, 0.8);

    &:hover {
      color: rgba(0, 242, 254, 1);
    }
  }
}

/* 确保开始日期和结束日期的placeholder文本颜色与控件保持一致 */
/deep/ .el-date-editor input::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
/deep/ .el-date-editor input::-moz-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
/deep/ .el-date-editor input:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* 优化日期选择器弹出面板的输入框样式 */
/deep/ .el-picker-panel {
  .el-date-range-picker__editors-wrap {
    input {
      background-color: rgba(4, 29, 53, 0.8);
      border-color: rgba(0, 242, 254, 0.5);
      color: #fff;

      &:hover,
      &:focus {
        border-color: rgba(0, 242, 254, 0.8);
      }

      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }

  .el-date-range-picker__time-header {
    border-bottom-color: rgba(0, 242, 254, 0.3);
  }
}
</style>
