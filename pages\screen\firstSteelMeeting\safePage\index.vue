<!--设备检查-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border-multi :title="'设备检查'">
                <template v-slot:headerRight>
                  <el-row>
                    <span>已完成：</span>
                    <span style="margin-right: 10px;">{{ monthData.finish }}</span>
                    <span>整改中：</span>
                    <span style="margin-right: 10px;">{{ monthData.ongoing }}</span>
                    <span
                      v-if="monthData.ongoingSet.length!==0"
                      style="margin-right: 10px;font-size: 14px">{{ monthData.ongoingSet }}</span>
                    <span>未完成：</span>
                    <span style="margin-right: 10px;">{{ monthData.unFinish }}</span>
                    <span
                      v-if="monthData.unFinishSet.length!==0"
                      style="margin-right: 10px;font-size: 14px">{{ monthData.unFinishSet }}</span>
                    <span
                      class="screen-btn"
                      @click="clickAddProject">
                      <el-icon class="el-icon-edit-outline"/>
                      新增
                    </span>
                    <span
                      v-command="'/first/steel/meeting/safe/delete'"
                      class="screen-btn"
                      @click="handleDelete">
                      <el-icon class="el-icon-delete"/>
                      删除
                    </span>
                    <!-- <span
                      v-command="'/first/steel/meeting/safe/delete'"
                      class="screen-btn"> -->
                    <!-- <el-icon class="el-icon-download"/> -->
                    <el-upload
                      v-command="'/first/steel/meeting/safe/delete'"
                      ref="upload"
                      :show-file-list="false"
                      :on-change="handlePreview"
                      :auto-upload="false"
                      :action="''"
                      style="display: inline-block">
                      <span
                        class="screen-btn">
                        <el-icon class="el-icon-edit-outline"/>
                        导入
                      </span>
                    </el-upload>
                    <!-- </span> -->
                    <span
                      v-command="'/first/steel/meeting/safe/delete'"
                      class="screen-btn"
                      @click="ExportExcel">
                      <el-icon class="el-icon-download"/>
                      导出
                    </span>
                  </el-row>
                </template>

                <el-table
                  id="table"
                  ref="table1"
                  :data="ProjectData.showGridData">
                  <el-table-column :label="'已完成:'+monthData.finish+' '+'整改中:'+monthData.ongoing+' '+'['+monthData.ongoingSet+']'+' '+'未完成:'+monthData.unFinish+' '+'['+monthData.unFinishSet+']'">
                    <el-table-column
                      show-overflow-tooltip
                      width="70"
                      label="序号">
                      <template v-slot="scope">
                        <div>{{ scope.$index+1 }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="workshopList"
                      :filter-method="filterMethod"
                      property="workshop"
                      label="车间"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.workshop }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="问题类别"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.dangerType }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="检查问题">
                      <template v-slot="scope">
                        <div>{{ scope.row.description }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="workshopList"
                      :filter-method="filterMethod"
                      property="modifyWorkshop"
                      label="整改车间"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.modifyWorkshop }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="整改负责人"
                      width="120">
                      <template v-slot="scope">
                        <div>{{ scope.row.modifyHead }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="计划完成时间"
                      width="120">
                      <template v-slot="scope">
                        <div>{{ scope.row.planDate }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="statusList"
                      :filter-method="filterMethod"
                      property="modifyExecution"
                      label="完成情况"
                      width="120">
                      <template v-slot="scope">
                        <div>{{ scope.row.modifyExecution }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="考核金额"
                      width="100">
                      <template v-slot="scope">
                        <div>{{ scope.row.amount }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="日期"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.setDate }}</div>
                      </template>
                    </el-table-column>
                  </el-table-column>
                </el-table>

                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="ProjectData.loading"
                    :data="ProjectData.showGridData"
                    :row-class-name="rowClassName"
                    border
                    @selection-change="handleSelectionChange">
                    <el-table-column
                      type="selection"
                      align="center"/>
                    <el-table-column
                      show-overflow-tooltip
                      width="70"
                      label="序号">
                      <template v-slot="scope">
                        <div>{{ scope.$index+1 }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="workshopList"
                      :filter-method="filterMethod"
                      property="workshop"
                      label="车间"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.workshop }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="问题类别"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.dangerType }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="检查问题">
                      <template v-slot="scope">
                        <div>{{ scope.row.description }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="workshopList"
                      :filter-method="filterMethod"
                      property="modifyWorkshop"
                      label="整改车间"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.modifyWorkshop }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="整改负责人"
                      width="120">
                      <template v-slot="scope">
                        <div>{{ scope.row.modifyHead }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="计划完成时间"
                      width="140">
                      <template v-slot="scope">
                        <div>{{ scope.row.planDate }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="statusList"
                      :filter-method="filterMethod"
                      property="modifyExecution"
                      label="完成情况"
                      width="120">
                      <template v-slot="scope">
                        <div>{{ scope.row.modifyExecution }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="考核金额"
                      width="100">
                      <template v-slot="scope">
                        <div>{{ scope.row.amount }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="日期"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.setDate }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property=""
                      width="150"
                      label="操作">
                      <template v-slot="scope">
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectItemWatch(scope.row, scope.$index)">查看</span>
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectUpdateItem(scope.row)">修改</span>
                        <span
                          v-command="'/first/steel/meeting/safe/delete'"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectDeleteItem(scope.row)">删除</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border-multi>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--考核通报新增修改-->
    <el-dialog
      v-loading="ProjectData.loading"
      :visible.sync="ProjectData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="设备检查"
      @close="dialogClose">
      <template v-slot:title>
        <div class="custom-dialog-title">
          设备检查
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">车间</div>
          <el-select
            v-model="projectItem.workshop"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in workshopList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">问题类别</div>
          <el-input
            v-model="projectItem.dangerType"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
            <!--          <el-select
            v-model="projectItem.dangerType"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in dangerTypeList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>-->
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">检查问题</div>
          <el-input
            v-model="projectItem.description"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改车间</div>
          <el-select
            v-model="projectItem.modifyWorkshop"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in workshopList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改负责人</div>
          <el-input
            v-model="projectItem.modifyHead"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">完成情况</div>
          <el-select
            v-model="projectItem.modifyExecution"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">计划完成时间</div>
          <el-date-picker
            v-model="projectItem.planDate"
            :clearable="false"
            :size="'mini'"
            :value-format="'yyyy-MM-dd'"
            class="screen-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改前图片上传</div>
          <el-upload
            :before-remove="beforeRemove"
            :before-upload="beforeUpload"
            :limit="4"
            :on-exceed="handleExceed"
            :file-list="fileList"
            :multiple="false"
            action=""
            accept="image/*"
            class="upload-demo">
            <el-button
              size="small"
              type="primary">点击上传</el-button>
            <div
              slot="tip"
              class="el-upload__tip">请选择图片</div>
          </el-upload>
          <input
            readonly
            class="el-input__inner paste-div"
            placeholder="点击此处，ctrl+v粘贴截图"
            @paste="handlePaste">
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改后图片上传</div>
          <el-upload
            :before-remove="beforeRemove2"
            :before-upload="beforeUpload2"
            :limit="4"
            :on-exceed="handleExceed2"
            :file-list="fileList2"
            :multiple="false"
            action=""
            accept="image/*"
            class="upload-demo">
            <el-button
              size="small"
              type="primary">点击上传</el-button>
            <div
              slot="tip"
              class="el-upload__tip">请选择图片</div>
          </el-upload>
          <input
            readonly
            class="el-input__inner paste-div"
            placeholder="点击此处，ctrl+v粘贴截图"
            @paste="handlePaste2">
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">金额考核</div>
          <el-input
            v-model="projectItem.amount"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="clickAddProjectData()">
          确定
        </span>
      </div>
    </el-dialog>
    <!--查看-->
    <el-dialog
      :visible.sync="dialogVisibleWatch"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="设备检查">
      <template v-slot:title>
        <div class="custom-dialog-title">
          设备检查
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <el-row>
          <el-col :span="12">
            <div class="dialog-cell">
              <div class="dialog-cell-title">整改前图片</div>
              <el-empty
                v-if="previewSrcList.length===0"
                description="暂无图片"/>
              <div v-else>
                <div
                  v-for="(item, index) in previewSrcList"
                  :key="item"
                  style="display: inline">
                  <el-image
                    :src="item"
                    :preview-src-list="previewSrcList"
                    :initial-index="index"
                    fit="scale-down"
                    style="height: 250px">
                    <div
                      slot="placeholder"
                      class="image-slot">
                      加载中<span class="dot">...</span>
                    </div>
                  </el-image>
                </div>
              </div>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">整改后图片</div>
              <el-empty
                v-if="previewSrcList2.length===0"
                description="暂无图片"/>
              <div v-else>
                <div
                  v-for="(item, index) in previewSrcList2"
                  :key="item"
                  style="display: inline">
                  <el-image
                    :src="item"
                    :preview-src-list="previewSrcList2"
                    :initial-index="index"
                    fit="scale-down"
                    style="height: 250px">
                    <div
                      slot="placeholder"
                      class="image-slot">
                      加载中<span class="dot">...</span>
                    </div>
                  </el-image>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="dialog-cell">
              <div class="dialog-cell-title">车间</div>
              <el-input
                v-model="projectItem.workshop"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">问题类别</div>
              <el-input
                v-model="projectItem.dangerType"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">检查问题</div>
              <el-input
                v-model="projectItem.description"
                :rows="3"
                type="textarea"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">整改车间</div>
              <el-input
                v-model="projectItem.modifyWorkshop"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">整改负责人</div>
              <el-input
                v-model="projectItem.modifyHead"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">计划完成时间</div>
              <el-input
                v-model="projectItem.planDate"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">完成情况</div>
              <el-input
                v-model="projectItem.modifyExecution"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">金额考核</div>
              <el-input
                v-model="projectItem.amount"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">修改人</div>
              <el-input
                v-model="projectItem.updateUser"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">修改时间</div>
              <el-input
                v-model="projectItem.updateDate"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
          </el-col>
        </el-row>
      </div>
      <div
        style="margin-top: 10px;margin-bottom: 10px"
        class="text-center">
        <el-row style="float: right;">
          <span
            class="screen-btn"
            @click="clickPre()">
            &lt;上一条
          </span>
          <span
            class="screen-btn"
            @click="clickNext()">
            下一条&gt;
          </span>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { saveAs } from 'file-saver'
import {
  minio_upload,
  oneBulletinBoard_deleteEquipCheck,
  oneBulletinBoard_getEquipCheck,
  oneBulletinBoard_getEquipCheckCount,
  oneBulletinBoard_saveEquipCheck,
  Export_Excel,
  oneBulletinBoardImportExcel
} from '@/api/firstMeeting'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi.vue'
export default {
  name: 'ProjectPage',
  components: {
    ScreenBorderMulti,
    SingleBarsChart,
    SteelBarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      ProjectData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      projectIndex: 0,
      userNo: localStorage.getItem('userId'),
      userName: '',
      projectItem: {},
      dangerTypeList: [
        {
          text: '行为规范',
          value: '行为规范'
        },
        {
          text: '高处作业',
          value: '高处作业'
        },
        {
          text: '安全标识',
          value: '安全标识'
        },
        {
          text: '焊机使用',
          value: '焊机使用'
        },
        {
          text: '安全手续',
          value: '安全手续'
        },
        {
          text: '能量源挂牌上锁',
          value: '能量源挂牌上锁'
        },
        {
          text: '安全用电',
          value: '安全用电'
        },
        {
          text: '消防管理',
          value: '消防管理'
        },
        {
          text: '安全防护',
          value: '安全防护'
        },
        {
          text: '基础管理',
          value: '基础管理'
        },
        {
          text: '加门上锁',
          value: '加门上锁'
        },
        {
          text: '设备本质',
          value: '设备本质'
        },
        {
          text: '特殊工种',
          value: '特殊工种'
        },
        {
          text: '十大禁令',
          value: '十大禁令'
        },
        {
          text: '相关方管理',
          value: '相关方管理'
        },
        {
          text: '物体打击',
          value: '物体打击'
        },
        {
          text: '车辆伤害',
          value: '车辆伤害'
        },
        {
          text: '机械伤害',
          value: '机械伤害'
        },
        {
          text: '起重伤害',
          value: '起重伤害'
        },
        {
          text: '触电',
          value: '触电'
        },
        {
          text: '淹溺',
          value: '淹溺'
        },
        {
          text: '灼烫',
          value: '灼烫'
        },
        {
          text: '火灾',
          value: '火灾'
        },
        {
          text: '高处坠落',
          value: '高处坠落'
        },
        {
          text: '坍塌',
          value: '坍塌'
        },
        {
          text: '其他爆炸',
          value: '其他爆炸'
        },
        {
          text: '中毒和窒息',
          value: '中毒和窒息'
        },
        {
          text: '其他伤害',
          value: '其他伤害'
        },
        {
          text: '四不准—作业项目未申报，不作业',
          value: '四不准—作业项目未申报，不作业'
        },
        {
          text: '四不准—作业无方案、sop，不作业',
          value: '四不准—作业无方案、sop，不作业'
        },
        {
          text:
            '四不准—施工单位负责人（区域负责人、项目管理人员）不在现场，不作业',
          value:
            '四不准—施工单位负责人（区域负责人、项目管理人员）不在现场，不作业'
        },
        {
          text: '四不准—无安全监护人员，不作业',
          value: '四不准—无安全监护人员，不作业'
        },
        {
          text: '五必查—动火、高处、吊运、用电、能量源挂牌上锁',
          value: '五必查—动火、高处、吊运、用电、能量源挂牌上锁'
        },
        {
          text: '六必须—检修作业必须有方案',
          value: '六必须—检修作业必须有方案'
        },
        {
          text: '六必须—监护人员必须到位',
          value: '六必须—监护人员必须到位'
        },
        {
          text: '六必须—危险源与措施必须交底清楚',
          value: '六必须—危险源与措施必须交底清楚'
        },
        {
          text: '六必须—作业方案必须严格执行',
          value: '六必须—作业方案必须严格执行'
        },
        {
          text: '六必须—能量源上锁挂牌必须执行到位',
          value: '六必须—能量源上锁挂牌必须执行到位'
        },
        {
          text: '六必须—非生产作业必须先办理手续再作业',
          value: '六必须—非生产作业必须先办理手续再作业'
        }
      ],
      workshopList: [
        {
          text: '原料车间',
          value: '原料车间',
          type: 'YLCJ'
        },
        {
          text: '炼钢车间',
          value: '炼钢车间',
          type: 'LGCJ'
        },
        {
          text: '精炼车间',
          value: '精炼车间',
          type: 'JLCJ'
        },
        {
          text: '连铸车间',
          value: '连铸车间',
          type: 'LZCJ'
        },
        {
          text: '运行车间',
          value: '运行车间',
          type: 'YXCJ'
        },
        {
          text: '坯料车间',
          value: '坯料车间',
          type: 'PLCJ'
        },
        {
          text: '综合管理室',
          value: '综合管理室',
          type: 'ZHGLS'
        },
        {
          text: '设备管理室',
          value: '设备管理室',
          type: 'SBGLS'
        },
        {
          text: '品质室',
          value: '品质室',
          type: 'PZS'
        },
        {
          text: '生产管理室',
          value: '生产管理室',
          type: 'SCGLS'
        }
      ],
      workshopMap: {
        YLCJ: '原料车间',
        LGCJ: '炼钢车间',
        JLCJ: '精炼车间',
        LZCJ: '连铸车间',
        YXCJ: '运行车间',
        PLCJ: '坯料车间',
        ZHGLS: '综合管理室',
        SBGLS: '设备管理室',
        PZS: '品质室',
        SCGLS: '生产管理室'
      },
      statusList: [
        {
          text: '未完成',
          value: '未完成'
        },
        {
          text: '整改中',
          value: '整改中'
        },
        {
          text: '已完成',
          value: '已完成'
        }
      ],
      fileList: [],
      fileList2: [],
      fileUrl: '',
      dialogVisibleWatch: false,
      multipleSelection: [],
      previewSrcList: [],
      previewSrcList2: [],
      uploadFileList: [], //上传
      uploadFileList2: [], //上传
      pasteList: [],
      clickRow: '',
      monthData: {
        finish: '',
        ongoing: '',
        ongoingSet: [],
        unFinish: '',
        unFinishSet: []
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getProjectData()
      this.getMonthData()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    filterMethod(value, row, column) {
      const property = column['property']
      return row[property] === value
    },
    rowClassName({ row, rowIndex }) {
      if (
        row.modifyExecution === null ||
        row.modifyExecution === '未完成' ||
        row.modifyExecution === ''
      ) {
        return 'class_red'
      } else if (row.modifyExecution === '整改中') {
        return 'class_yellow'
      } else {
        return ''
      }
      // if (
      //   (row.modifyExecution === null ||
      //     row.modifyExecution === '整改中' ||
      //     row.modifyExecution === '') &&
      //   row.fileList.length === 0
      // ) {
      //   if (
      //     new Date().getTime() - new Date(row.setDate).getTime() >
      //     24 * 3600 * 1000
      //   ) {
      //     return 'class_red'
      //   } else {
      //     return 'class_yellow'
      //   }
      // } else {
      //   return ''
      // }
    },
    // 批量删除
    handleDelete() {
      if (!this.multipleSelection.length)
        return this.$message.warning('请先选择数据！')
      // /productionForecast/deleteOrders
      let list = []
      this.multipleSelection.forEach(item => list.push({ id: item.id }))
      console.log('删除列表：', list)
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteProject(list)
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    clickPre() {
      if (this.projectIndex > 0) {
        this.projectIndex--
        this.projectItem = JSON.parse(
          JSON.stringify(this.ProjectData.showGridData[this.projectIndex])
        )
        let list = []
        this.ProjectData.showGridData[this.projectIndex].fileList.forEach(
          item => {
            list.push(item.fileUrl)
          }
        )
        this.previewSrcList = list
        let list2 = []
        this.ProjectData.showGridData[this.projectIndex].fileList2.forEach(
          item => {
            list.push(item.fileUrl)
          }
        )
        this.previewSrcList2 = list2
      } else {
        this.$message.warning('已经到第一条啦！')
      }
    },
    clickNext() {
      if (this.projectIndex < this.ProjectData.showGridData.length - 1) {
        this.projectIndex++
        this.projectItem = JSON.parse(
          JSON.stringify(this.ProjectData.showGridData[this.projectIndex])
        )
        let list = []
        this.ProjectData.showGridData[this.projectIndex].fileList.forEach(
          item => {
            list.push(item.fileUrl)
          }
        )
        this.previewSrcList = list
        let list2 = []
        this.ProjectData.showGridData[this.projectIndex].fileList2.forEach(
          item => {
            list.push(item.fileUrl)
          }
        )
        this.previewSrcList2 = list2
      } else {
        this.$message.warning('已经到最后一条啦！')
      }
    },
    beforeUpload(file) {
      // console.log('beforeUpload', file)
      this.uploadFileList.push({
        fileUrl: '',
        fileName: file.name,
        file: file
      })
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 4 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },
    async beforeRemove(file, fileList) {
      const isDel = await this.$confirm(`确定移除 ${file.name}？`)
      if (isDel) {
        // this.projectItem.fileUrl = ''
        if (file.url) {
          // //网络
          const index = this.projectItem.fileList.findIndex(
            item => item.url === file.url
          )
          this.projectItem.fileList.splice(index, 1)
        } else {
          const index = this.uploadFileList.findIndex(
            item => item.file === file.file
          )
          this.uploadFileList.splice(index, 1)
        }
      }
      return isDel
    },
    beforeUpload2(file) {
      console.log('beforeUpload', file)
      this.uploadFileList2 = []
      this.uploadFileList2.push({
        fileUrl: '',
        fileName: file.name,
        file: file
      })
      // console.log('this.uploadFileList2', this.uploadFileList2)
      // console.log('this.fileList2', this.fileList2)
      // this.fileList2 = this.uniqueJsonArray(this.fileList2)
    },
    handleExceed2(files, fileList) {
      this.$message.warning(
        `当前限制选择 4 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },
    uniqueJsonArray(arr) {
      const uniqueArray = []
      const seen = new Set()
      arr.forEach(item => {
        const itemStr = JSON.stringify(item)
        if (!seen.has(itemStr)) {
          seen.add(itemStr)
          uniqueArray.push(item)
        }
      })
      return uniqueArray
    },
    async beforeRemove2(file, fileList) {
      console.log('aaa', file)

      const isDel = await this.$confirm(`确定移除 ${file.name}？`)
      if (isDel) {
        // this.projectItem.fileUrl = ''
        if (file.url) {
          // //网络
          const index = this.projectItem.fileList2.findIndex(
            item => item.url === file.url
          )
          console.log('aaa', this.projectItem.fileList2)
          console.log('bbb', index)
          console.log('ccc', this.projectItem.fileList2.splice(index, 1))
          this.projectItem.fileList2.splice(index, 1)
        } else {
          const index = this.uploadFileList2.findIndex(
            item => item.file === file.file
          )
          this.uploadFileList2.splice(index, 1)
        }
      }
      return isDel
    },
    //点击新增
    clickAddProject() {
      this.projectItem = {
        workshop: '',
        dangerType: '',
        description: '',
        amount: '',
        modifyWorkshop: '',
        modifyHead: '',
        modifyExecution: '',
        planDate: '',
        updateUser: this.userNo,
        // setDate: '',
        fileUrl: '',
        fileList: [],
        fileList2: []
      }
      this.fileList = []
      this.fileList2 = []
      this.uploadFileList = []
      this.uploadFileList2 = []
      this.ProjectData.dialogVisible = true
    },
    //点击修改
    clickProjectUpdateItem(row) {
      let fileList = []
      let fileList2 = []
      this.clickRow = row
      row.fileList.forEach(item => {
        fileList.push({
          name: item.fileName,
          url: item.fileUrl,
          id: item.id,
          parentId: item.parentId,
          type: item.type
        })
      })
      row.fileList2.forEach(item => {
        fileList2.push({
          name: item.fileName,
          url: item.fileUrl,
          id: item.id,
          parentId: item.parentId,
          type: item.type
        })
      })
      this.fileList = fileList
      this.fileList2 = fileList2
      this.projectItem = JSON.parse(JSON.stringify(row))
      this.ProjectData.dialogVisible = true
    },
    dialogClose() {
      this.fileList = []
      this.fileList2 = []
      this.uploadFileList = []
      this.uploadFileList2 = []
    },
    clickProjectItemWatch(row, index) {
      this.projectIndex = index
      this.projectItem = JSON.parse(JSON.stringify(row))
      let list = []
      row.fileList.forEach(item => {
        list.push(item.fileUrl)
      })
      this.previewSrcList = list
      let list2 = []
      row.fileList2.forEach(item => {
        list2.push(item.fileUrl)
      })
      this.previewSrcList2 = list2
      this.dialogVisibleWatch = true
    },
    //点击删除
    clickProjectDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteProject([{ id: row.id }])
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    //确认新增/修改
    async clickAddProjectData() {
      //新图片上传
      if (this.uploadFileList.length > 0) {
        for (let i = 0; i < this.uploadFileList.length; i++) {
          let fileItem = this.uploadFileList[i]
          const loading = this.$loading({
            lock: true,
            text: `正在上传整改前第${i + 1}张图片`,
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          let formData = new FormData()
          formData.append('file', fileItem.file)
          // formData.append('userNo', this.userNo)
          const res = await post(minio_upload, formData)
          loading.close()
          let index = res.indexOf('?')
          if (index !== -1) {
            fileItem.fileUrl = res.substring(0, index)
          } else {
            fileItem.fileUrl = res
          }
        }
      }
      // console.log('(this.uploadFileList2', this.uploadFileList2)
      if (this.uploadFileList2.length > 0) {
        for (let i = 0; i < this.uploadFileList2.length; i++) {
          let fileItem = this.uploadFileList2[i]
          const loading = this.$loading({
            lock: true,
            text: `正在上传整改后第${i + 1}张图片`,
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          let formData = new FormData()
          formData.append('file', fileItem.file)
          // formData.append('userNo', this.userNo)
          const res = await post(minio_upload, formData)
          loading.close()
          let index = res.indexOf('?')
          if (index !== -1) {
            fileItem.fileUrl = res.substring(0, index)
          } else {
            fileItem.fileUrl = res
          }
        }
      }
      //新增的上传图片
      let fileList = []
      this.uploadFileList.forEach(item => {
        if (item.fileUrl) {
          fileList.push({
            type: 'A',
            fileUrl: item.fileUrl,
            fileName: item.fileName,
            parentId: this.projectItem.id
          })
        }
      })
      this.uploadFileList2.forEach(item => {
        if (item.fileUrl) {
          fileList.push({
            type: 'B',
            fileUrl: item.fileUrl,
            fileName: item.fileName,
            parentId: this.projectItem.id
          })
        }
      })
      //原来的fileList
      this.projectItem.fileList.forEach(item => {
        fileList.push(item)
      })
      this.projectItem.fileList2.forEach(item => {
        fileList.push(item)
      })
      //合并后的fileList
      this.projectItem.fileList = fileList
      console.log('新增/修改：', this.projectItem.fileList, this.uploadFileList)
      this.addProjectData()
    },
    //  onOpen() {
    //    this.$nextTick(() => {
    //      this.$refs.paste.addEventListener('paste', e => {
    //        //  console.dir(e.clipboardData.files)
    //        let file = null
    //        const Afiles = (e.clipboardData || window.clipboardData).files
    //        const files = Array.from(new Set(Afiles))
    //        console.log('pasteList', files)
    //        if (files && files.length) {
    //          for (let i = 0; i < files.length; i++) {
    //            if (files[i].type.indexOf('image') !== -1) {
    //              // 如果是image类型存为file
    //              //  file = {
    //              //    raw: files[i],
    //              //    src: imgUrl
    //              //  }
    //              file = files[i]
    //              //  this.fileList2.push(file)
    //              this.beforeUpload2(files[i])
    //              break
    //            }
    //          }
    //        }
    //        if (file) {
    //          // this.pasteList.push(file)
    //          // this.fileList2.push(file)
    //          // this.beforeUpload2(file)
    //          // console.log('file222', file)
    //          this.$message.success('图片粘贴成功')
    //          let ctrlFile = e.clipboardData || window.clipboardData
    //          ctrlFile.files = []
    //        } else {
    //          this.$message.warning('未识别到图片')
    //        }
    //      })
    //    })
    //  },
    handlePaste2(event) {
      const clipboardData = event.clipboardData || window.clipboardData
      if (!clipboardData) return

      // 检查剪贴板中的数据是否有图片
      const items = clipboardData.items
      if (!items) return
      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        if (item.kind === 'file' && item.type.startsWith('image/')) {
          const file = item.getAsFile()
          console.log('file', file)

          if (file) {
            this.$message.success('图片粘贴成功')
            this.beforeUpload2(file)
            // this.clickAddProjectData()
            this.fileList2.push(file)
            console.log('shunxu', this.fileList2)
          } else {
            this.$message.warning('未识别到图片')
          }
        }
      }
    },
    handlePaste(event) {
      const clipboardData = event.clipboardData || window.clipboardData
      if (!clipboardData) return

      // 检查剪贴板中的数据是否有图片
      const items = clipboardData.items
      if (!items) return
      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        if (item.kind === 'file' && item.type.startsWith('image/')) {
          const file = item.getAsFile()
          console.log('file', file)

          if (file) {
            this.$message.success('图片粘贴成功')
            this.beforeUpload(file)
            // this.clickAddProjectData()
            this.fileList.push(file)
          } else {
            this.$message.warning('未识别到图片')
          }
        }
      }
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          //  index: 'A',
          workshop: 'B',
          dangerType: 'C',
          description: 'D',
          modifyWorkshop: 'E',
          modifyHead: 'F',
          planDate: 'G',
          modifyExecution: 'H',
          amount: 'I',
          setDate: 'J'
        })

        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) {
          this.$message.error('未找到sheet1，请检查！')
          return
        }
        sheet.shift()

        // 表格信息
        const datas = sheet.filter(item => {
          // 过滤掉空行
          return item && (item.workshop || item.description || item.modifyHead)
        })

        if (datas.length === 0) {
          this.$message.error('Excel文件中没有有效数据，请检查！')
          return
        }

        this.$message.success('解析成功！')

        // 构建符合接口要求的数据格式
        const params = datas.map(item => {
          return {
            workshop: item.workshop || '',
            dangerType: item.dangerType || '',
            description: item.description || '',
            amount: item.amount || '',
            modifyWorkshop: item.modifyWorkshop || '',
            modifyHead: item.modifyHead || '',
            modifyExecution: item.modifyExecution || '',
            planDate: item.planDate || '',
            updateUser: this.userNo || '',
            fileUrl: '',
            fileList: [],
            fileList2: []
          }
        })

        this.ProjectData.loading = true

        // 直接调用保存接口
        post(oneBulletinBoard_saveEquipCheck, params)
          .then(res => {
            if (res.success) {
              this.$notify.success('导入成功！')
              this.getProjectData()
            } else {
              this.$message.error('导入失败：' + (res.message || '未知错误'))
            }
          })
          .catch(err => {
            this.$message.error('导入失败：' + (err.message || '未知错误'))
          })
          .finally(_ => {
            this.ProjectData.loading = false
            this.fileList = []
            this.uploadFileList = []
          })
      })
    },
    //  uploadImage(file) {
    //    // 创建FormData对象用于构建表单数据集
    //    const formData = new FormData();
    //    formData.append('image', file);

    //    // 使用你选择的HTTP库发送请求
    //    // 这里以axios为例
    //    axios.post('/upload/path', formData, {
    //      headers: {
    //        'Content-Type': 'multipart/form-data'
    //      }
    //    })
    //    .then(response => {
    //      // 处理响应
    //      console.log(response.data);
    //    })
    //    .catch(error => {
    //      // 处理错误
    //      console.error(error);
    //    });
    //  },
    //新增/修改
    addProjectData() {
      console.log('projectItem:', this.projectItem)
      const params = [this.projectItem]
      this.ProjectData.loading = true
      console.log('params', params)

      post(oneBulletinBoard_saveEquipCheck, params)
        .then(res => {
          if (res.success) {
            this.$notify.success('操作成功！')
            this.ProjectData.dialogVisible = false
            this.getProjectData()
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
          this.fileList = []
          this.uploadFileList = []
        })
    },
    //删除
    deleteProject(list) {
      post(oneBulletinBoard_deleteEquipCheck, list).then(res => {
        if (res.success) {
          this.$notify.success('删除成功！')
          this.getProjectData()
        }
      })
    },
    calculateHeight() {
      this.ProjectData.maxHeight = this.$refs.table1.offsetHeight
    },
    getProjectData() {
      this.ProjectData.loading = true
      post(oneBulletinBoard_getEquipCheck, {
        setDate: this.cDate
      })
        .then(res => {
          this.ProjectData.showGridData = res.data.map(item => {
            let fileList = []
            let fileList2 = []
            item.fileList.forEach(fileItem => {
              if (fileItem.type === 'B') {
                fileList2.push(fileItem)
              } else {
                fileList.push(fileItem)
              }
            })
            return {
              id: item.id,
              workshop: item.workshop,
              dangerType: item.dangerType,
              description: item.description,
              fileUrl: item.fileUrl,
              planDate: item.planDate,
              setDate: item.setDate,
              amount: item.amount,
              modifyWorkshop: item.modifyWorkshop,
              modifyHead: item.modifyHead,
              modifyExecution: item.modifyExecution,
              updateUser: item.updateUser,
              updateDate: item.updateDate,
              fileList: fileList,
              fileList2: fileList2
            }
          })
          this.ProjectData.gridData = lodash.cloneDeep(
            this.ProjectData.showGridData
          )
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    getMonthData() {
      post(oneBulletinBoard_getEquipCheckCount, {
        // setDate: this.cDate.substring(0, 7)
      }).then(res => {
        if (res && res.success) {
          this.monthData = res.data
        }
      })
    },

    //导出
    async ExportExcel() {
      // post(Export_Excel, {}).then(res => {
      //   let data = res
      //   if (!data) {
      //     return
      //   }
      //   const url = window.URL.createObjectURL(new Blob([data]))
      //   let link = document.createElement('a')

      //   link.style.display = 'none'
      //   link.href = url
      //   link.setAttribute('download', '设备检查列表' + '.xls')
      //   document.body.appendChild(link)
      //   link.click()
      //   document.body.removeChild(link) //下载完成移除元素
      //   window.URL.revokeObjectURL(url) //释放掉blob对象
      // })
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          '设备检查列表.xlsx'
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
/deep/ .el-table__body tr:hover > td {
  background-color: rgba(245, 247, 250, 0.1) !important;
  //background-color: transparent !important;
}

/deep/ .el-table__body tr.current-row > td {
  background-color: rgba(245, 247, 250, 0.1) !important;
  //background-color: transparent !important;
}
/deep/ .el-table .class_red {
  background: #fd0000;
  color: black;
}
/deep/ .el-table .class_yellow {
  background: #fdfd00;
  color: black;
}
/deep/ .el-table .class_orange {
  background: #f99f04;
  color: black;
}

.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    /* 保留dialog-cell-input选择器以便将来可能的样式扩展 */
    .dialog-cell-input {
      width: 100%;
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
#table {
  display: none;
}
</style>
