<!--安全检查-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border-multi :title="'安全检查'">
                <template v-slot:headerRight>
                  <el-row>
                    <span>已完成：</span>
                    <span style="margin-right: 10px;">{{ monthData.finish }}</span>
                    <span>整改中：</span>
                    <span style="margin-right: 10px;">{{ monthData.ongoing }}</span>
                    <span
                      v-if="monthData.ongoingSet.length!==0"
                      style="margin-right: 10px;font-size: 14px">{{ monthData.ongoingSet }}</span>
                    <span>未完成：</span>
                    <span style="margin-right: 10px;">{{ monthData.unFinish }}</span>
                    <span
                      v-if="monthData.unFinishSet.length!==0"
                      style="margin-right: 10px;font-size: 14px">{{ monthData.unFinishSet }}</span>
                    <span
                      v-command="'/first/steel/safe/delete'"
                      class="screen-btn"
                      @click="clickAddProject">
                      <el-icon class="el-icon-edit-outline"/>
                      新增
                    </span>
                    <span
                      v-command="'/first/steel/safe/delete'"
                      class="screen-btn"
                      @click="handleDelete">
                      <el-icon class="el-icon-delete"/>
                      删除
                    </span>
                  </el-row>

                </template>
                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="ProjectData.loading"
                    :data="ProjectData.showGridData"
                    :row-class-name="rowClassName"
                    border
                    @selection-change="handleSelectionChange">
                    <el-table-column
                      type="selection"
                      align="center"/>
                    <el-table-column
                      show-overflow-tooltip
                      width="70"
                      label="序号">
                      <template v-slot="scope">
                        <div>{{ scope.$index+1 }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="workshopList"
                      :filter-method="filterMethod"
                      property="workshop"
                      label="车间"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.workshop }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="隐患类别"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.dangerType }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="隐患描述">
                      <template v-slot="scope">
                        <div>{{ scope.row.description }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="workshopList"
                      :filter-method="filterMethod"
                      property="modifyWorkshop"
                      label="整改车间"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.modifyWorkshop }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="整改负责人"
                      width="120">
                      <template v-slot="scope">
                        <div>{{ scope.row.modifyHead }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="statusList"
                      :filter-method="filterMethod"
                      property="modifyExecution"
                      label="完成情况"
                      width="120">
                      <template v-slot="scope">
                        <div>{{ scope.row.modifyExecution }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="考核金额"
                      width="100">
                      <template v-slot="scope">
                        <div>{{ scope.row.amount }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="日期"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.setDate }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property=""
                      width="150"
                      label="操作">
                      <template v-slot="scope">
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectItemWatch(scope.row, scope.$index)">查看</span>
                        <span
                          v-command="'/first/steel/safe/delete'"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectUpdateItem(scope.row)">修改</span>
                        <span
                          v-command="'/first/steel/safe/delete'"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectDeleteItem(scope.row)">删除</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border-multi>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--考核通报新增修改-->
    <el-dialog
      v-loading="ProjectData.loading"
      :visible.sync="ProjectData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="安全检查"
      @close="dialogClose">
      <template v-slot:title>
        <div class="custom-dialog-title">
          安全检查
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">车间</div>
          <el-select
            v-model="projectItem.workshop"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in workshopList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">隐患类别</div>
          <el-select
            v-model="projectItem.dangerType"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in dangerTypeList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">隐患描述</div>
          <el-input
            v-model="projectItem.description"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改车间</div>
          <el-select
            v-model="projectItem.modifyWorkshop"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in workshopList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改负责人</div>
          <el-input
            v-model="projectItem.modifyHead"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">完成情况</div>
          <el-select
            v-model="projectItem.modifyExecution"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改前图片上传</div>
          <el-upload
            :before-remove="beforeRemove"
            :before-upload="beforeUpload"
            :limit="4"
            :on-exceed="handleExceed"
            :file-list="fileList"
            :multiple="false"
            action=""
            accept="image/*"
            class="upload-demo">
            <el-button
              size="small"
              type="primary">点击上传</el-button>
            <div
              slot="tip"
              class="el-upload__tip">请选择图片</div>
          </el-upload>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改后图片上传</div>
          <el-upload
            :before-remove="beforeRemove2"
            :before-upload="beforeUpload2"
            :limit="4"
            :on-exceed="handleExceed2"
            :file-list="fileList2"
            :multiple="false"
            action=""
            accept="image/*"
            class="upload-demo">
            <el-button
              size="small"
              type="primary">点击上传</el-button>
            <div
              slot="tip"
              class="el-upload__tip">请选择图片</div>
          </el-upload>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">金额考核</div>
          <el-input
            v-model="projectItem.amount"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="clickAddProjectData()">
          确定
        </span>
      </div>
    </el-dialog>
    <!--查看-->
    <el-dialog
      :visible.sync="dialogVisibleWatch"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="安全检查">
      <template v-slot:title>
        <div class="custom-dialog-title">
          安全检查
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <el-row>
          <el-col :span="12">
            <div class="dialog-cell">
              <div class="dialog-cell-title">整改前图片</div>
              <el-empty
                v-if="previewSrcList.length===0"
                description="暂无图片"/>
              <div v-else>
                <div
                  v-for="(item, index) in previewSrcList"
                  :key="item"
                  style="display: inline">
                  <el-image
                    :src="item"
                    :preview-src-list="previewSrcList"
                    :initial-index="index"
                    fit="scale-down"
                    style="height: 250px">
                    <div
                      slot="placeholder"
                      class="image-slot">
                      加载中<span class="dot">...</span>
                    </div>
                  </el-image>
                </div>
              </div>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">整改后图片</div>
              <el-empty
                v-if="previewSrcList2.length===0"
                description="暂无图片"/>
              <div v-else>
                <div
                  v-for="(item, index) in previewSrcList2"
                  :key="item"
                  style="display: inline">
                  <el-image
                    :src="item"
                    :preview-src-list="previewSrcList2"
                    :initial-index="index"
                    fit="scale-down"
                    style="height: 250px">
                    <div
                      slot="placeholder"
                      class="image-slot">
                      加载中<span class="dot">...</span>
                    </div>
                  </el-image>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="dialog-cell">
              <div class="dialog-cell-title">车间</div>
              <el-input
                v-model="projectItem.workshop"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">隐患类别</div>
              <el-input
                v-model="projectItem.dangerType"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">隐患描述</div>
              <el-input
                v-model="projectItem.description"
                :rows="3"
                type="textarea"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">整改车间</div>
              <el-input
                v-model="projectItem.modifyWorkshop"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">整改负责人</div>
              <el-input
                v-model="projectItem.modifyHead"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">完成情况</div>
              <el-input
                v-model="projectItem.modifyExecution"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">金额考核</div>
              <el-input
                v-model="projectItem.amount"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">修改人</div>
              <el-input
                v-model="projectItem.updateUser"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">修改时间</div>
              <el-input
                v-model="projectItem.updateDate"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
          </el-col>
        </el-row>
      </div>
      <div
        style="margin-top: 10px;margin-bottom: 10px"
        class="text-center">
        <el-row style="float: right;">
          <span
            class="screen-btn"
            @click="clickPre()">
            &lt;上一条
          </span>
          <span
            class="screen-btn"
            @click="clickNext()">
            下一条&gt;
          </span>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  minio_upload,
  oneBulletinBoard_deleteSecurityCheck,
  oneBulletinBoard_getSecurityCheck,
  oneBulletinBoard_getSecurityCheckCount,
  oneBulletinBoard_getSupervisionMattersCount,
  oneBulletinBoard_saveSecurityCheck
} from '@/api/firstMeeting'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi.vue'
export default {
  name: 'ProjectPage',
  components: {
    ScreenBorderMulti,
    SingleBarsChart,
    SteelBarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      ProjectData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      projectIndex: 0,
      userNo: localStorage.getItem('userId'),
      userName: '',
      projectItem: {},
      dangerTypeList: [
        {
          text: '行为规范',
          value: '行为规范'
        },
        {
          text: '高处作业',
          value: '高处作业'
        },
        {
          text: '安全标识',
          value: '安全标识'
        },
        {
          text: '焊机使用',
          value: '焊机使用'
        },
        {
          text: '安全手续',
          value: '安全手续'
        },
        {
          text: '能量源挂牌上锁',
          value: '能量源挂牌上锁'
        },
        {
          text: '安全用电',
          value: '安全用电'
        },
        {
          text: '消防管理',
          value: '消防管理'
        },
        {
          text: '安全防护',
          value: '安全防护'
        },
        {
          text: '基础管理',
          value: '基础管理'
        },
        {
          text: '加门上锁',
          value: '加门上锁'
        },
        {
          text: '设备本质',
          value: '设备本质'
        },
        {
          text: '特殊工种',
          value: '特殊工种'
        },
        {
          text: '十大禁令',
          value: '十大禁令'
        },
        {
          text: '相关方管理',
          value: '相关方管理'
        },
        {
          text: '物体打击',
          value: '物体打击'
        },
        {
          text: '车辆伤害',
          value: '车辆伤害'
        },
        {
          text: '机械伤害',
          value: '机械伤害'
        },
        {
          text: '起重伤害',
          value: '起重伤害'
        },
        {
          text: '触电',
          value: '触电'
        },
        {
          text: '淹溺',
          value: '淹溺'
        },
        {
          text: '灼烫',
          value: '灼烫'
        },
        {
          text: '火灾',
          value: '火灾'
        },
        {
          text: '高处坠落',
          value: '高处坠落'
        },
        {
          text: '坍塌',
          value: '坍塌'
        },
        {
          text: '其他爆炸',
          value: '其他爆炸'
        },
        {
          text: '中毒和窒息',
          value: '中毒和窒息'
        },
        {
          text: '其他伤害',
          value: '其他伤害'
        },
        {
          text: '四不准—作业项目未申报，不作业',
          value: '四不准—作业项目未申报，不作业'
        },
        {
          text: '四不准—作业无方案、sop，不作业',
          value: '四不准—作业无方案、sop，不作业'
        },
        {
          text:
            '四不准—施工单位负责人（区域负责人、项目管理人员）不在现场，不作业',
          value:
            '四不准—施工单位负责人（区域负责人、项目管理人员）不在现场，不作业'
        },
        {
          text: '四不准—无安全监护人员，不作业',
          value: '四不准—无安全监护人员，不作业'
        },
        {
          text: '五必查—动火、高处、吊运、用电、能量源挂牌上锁',
          value: '五必查—动火、高处、吊运、用电、能量源挂牌上锁'
        },
        {
          text: '六必须—检修作业必须有方案',
          value: '六必须—检修作业必须有方案'
        },
        {
          text: '六必须—监护人员必须到位',
          value: '六必须—监护人员必须到位'
        },
        {
          text: '六必须—危险源与措施必须交底清楚',
          value: '六必须—危险源与措施必须交底清楚'
        },
        {
          text: '六必须—作业方案必须严格执行',
          value: '六必须—作业方案必须严格执行'
        },
        {
          text: '六必须—能量源上锁挂牌必须执行到位',
          value: '六必须—能量源上锁挂牌必须执行到位'
        },
        {
          text: '六必须—非生产作业必须先办理手续再作业',
          value: '六必须—非生产作业必须先办理手续再作业'
        }
      ],
      workshopList: [
        {
          text: '原料车间',
          value: '原料车间',
          type: 'YLCJ'
        },
        {
          text: '炼钢车间',
          value: '炼钢车间',
          type: 'LGCJ'
        },
        {
          text: '精炼车间',
          value: '精炼车间',
          type: 'JLCJ'
        },
        {
          text: '连铸车间',
          value: '连铸车间',
          type: 'LZCJ'
        },
        {
          text: '运行车间',
          value: '运行车间',
          type: 'YXCJ'
        },
        {
          text: '坯料车间',
          value: '坯料车间',
          type: 'PLCJ'
        },
        {
          text: '综合管理室',
          value: '综合管理室',
          type: 'ZHGLS'
        },
        {
          text: '设备管理室',
          value: '设备管理室',
          type: 'SBGLS'
        },
        {
          text: '品质室',
          value: '品质室',
          type: 'PZS'
        },
        {
          text: '生产管理室',
          value: '生产管理室',
          type: 'SCGLS'
        }
      ],
      workshopMap: {
        YLCJ: '原料车间',
        LGCJ: '炼钢车间',
        JLCJ: '精炼车间',
        LZCJ: '连铸车间',
        YXCJ: '运行车间',
        PLCJ: '坯料车间',
        ZHGLS: '综合管理室',
        SBGLS: '设备管理室',
        PZS: '品质室',
        SCGLS: '生产管理室'
      },
      statusList: [
        {
          text: '未完成',
          value: '未完成'
        },
        {
          text: '整改中',
          value: '整改中'
        },
        {
          text: '已完成',
          value: '已完成'
        }
      ],
      fileList: [],
      fileList2: [],
      fileUrl: '',
      dialogVisibleWatch: false,
      multipleSelection: [],
      previewSrcList: [],
      previewSrcList2: [],
      uploadFileList: [], //上传
      uploadFileList2: [], //上传
      monthData: {
        finish: '',
        ongoing: '',
        ongoingSet: [],
        unFinish: '',
        unFinishSet: []
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getProjectData()
      this.getMonthData()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    filterMethod(value, row, column) {
      const property = column['property']
      return row[property] === value
    },
    rowClassName({ row, rowIndex }) {
      if (
        row.modifyExecution === null ||
        row.modifyExecution === '未完成' ||
        row.modifyExecution === ''
      ) {
        return 'class_red'
      } else if (row.modifyExecution === '整改中') {
        return 'class_yellow'
      } else {
        return ''
      }
      // if (
      //   (row.modifyExecution === null ||
      //     row.modifyExecution === '整改中' ||
      //     row.modifyExecution === '') &&
      //   row.fileList.length === 0
      // ) {
      //   if (
      //     new Date().getTime() - new Date(row.setDate).getTime() >
      //     24 * 3600 * 1000
      //   ) {
      //     return 'class_red'
      //   } else {
      //     return 'class_yellow'
      //   }
      // } else {
      //   return ''
      // }
    },
    // 批量删除
    handleDelete() {
      if (!this.multipleSelection.length)
        return this.$message.warning('请先选择数据！')
      // /productionForecast/deleteOrders
      let list = []
      this.multipleSelection.forEach(item => list.push({ id: item.id }))
      console.log('删除列表：', list)
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteProject(list)
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    clickPre() {
      if (this.projectIndex > 0) {
        this.projectIndex--
        this.projectItem = JSON.parse(
          JSON.stringify(this.ProjectData.showGridData[this.projectIndex])
        )
        let list = []
        this.ProjectData.showGridData[this.projectIndex].fileList.forEach(
          item => {
            list.push(item.fileUrl)
          }
        )
        this.previewSrcList = list
        let list2 = []
        this.ProjectData.showGridData[this.projectIndex].fileList2.forEach(
          item => {
            list.push(item.fileUrl)
          }
        )
        this.previewSrcList2 = list2
      } else {
        this.$message.warning('已经到第一条啦！')
      }
    },
    clickNext() {
      if (this.projectIndex < this.ProjectData.showGridData.length - 1) {
        this.projectIndex++
        this.projectItem = JSON.parse(
          JSON.stringify(this.ProjectData.showGridData[this.projectIndex])
        )
        let list = []
        this.ProjectData.showGridData[this.projectIndex].fileList.forEach(
          item => {
            list.push(item.fileUrl)
          }
        )
        this.previewSrcList = list
        let list2 = []
        this.ProjectData.showGridData[this.projectIndex].fileList2.forEach(
          item => {
            list.push(item.fileUrl)
          }
        )
        this.previewSrcList2 = list2
      } else {
        this.$message.warning('已经到最后一条啦！')
      }
    },
    beforeUpload(file) {
      // console.log('beforeUpload', file)
      this.uploadFileList.push({
        fileUrl: '',
        fileName: file.name,
        file: file
      })
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 4 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },
    async beforeRemove(file, fileList) {
      const isDel = await this.$confirm(`确定移除 ${file.name}？`)
      if (isDel) {
        // this.projectItem.fileUrl = ''
        if (file.url) {
          // //网络
          const index = this.projectItem.fileList.findIndex(
            item => item.url === file.url
          )
          this.projectItem.fileList.splice(index, 1)
        } else {
          const index = this.uploadFileList.findIndex(
            item => item.file === file.file
          )
          this.uploadFileList.splice(index, 1)
        }
      }
      return isDel
    },
    beforeUpload2(file) {
      // console.log('beforeUpload', file)
      this.uploadFileList2.push({
        fileUrl: '',
        fileName: file.name,
        file: file
      })
    },
    handleExceed2(files, fileList) {
      this.$message.warning(
        `当前限制选择 4 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },
    async beforeRemove2(file, fileList) {
      const isDel = await this.$confirm(`确定移除 ${file.name}？`)
      if (isDel) {
        // this.projectItem.fileUrl = ''
        if (file.url) {
          // //网络
          const index = this.projectItem.fileList2.findIndex(
            item => item.url === file.url
          )
          this.projectItem.fileList2.splice(index, 1)
        } else {
          const index = this.uploadFileList2.findIndex(
            item => item.file === file.file
          )
          this.uploadFileList2.splice(index, 1)
        }
      }
      return isDel
    },
    //点击新增
    clickAddProject() {
      this.projectItem = {
        workshop: '',
        dangerType: '',
        description: '',
        amount: '',
        modifyWorkshop: '',
        modifyHead: '',
        modifyExecution: '',
        updateUser: this.userNo,
        // setDate: '',
        fileUrl: '',
        fileList: [],
        fileList2: []
      }
      this.fileList = []
      this.fileList2 = []
      this.uploadFileList = []
      this.uploadFileList2 = []
      this.ProjectData.dialogVisible = true
    },
    //点击修改
    clickProjectUpdateItem(row) {
      let fileList = []
      let fileList2 = []
      row.fileList.forEach(item => {
        fileList.push({
          name: item.fileName,
          url: item.fileUrl,
          id: item.id,
          parentId: item.parentId,
          type: item.type
        })
      })
      row.fileList2.forEach(item => {
        fileList2.push({
          name: item.fileName,
          url: item.fileUrl,
          id: item.id,
          parentId: item.parentId,
          type: item.type
        })
      })
      this.fileList = fileList
      this.fileList2 = fileList2
      this.projectItem = JSON.parse(JSON.stringify(row))
      this.ProjectData.dialogVisible = true
    },
    dialogClose() {
      this.fileList = []
      this.fileList2 = []
      this.uploadFileList = []
      this.uploadFileList2 = []
    },
    clickProjectItemWatch(row, index) {
      this.projectIndex = index
      this.projectItem = JSON.parse(JSON.stringify(row))
      let list = []
      row.fileList.forEach(item => {
        list.push(item.fileUrl)
      })
      this.previewSrcList = list
      let list2 = []
      row.fileList2.forEach(item => {
        list2.push(item.fileUrl)
      })
      this.previewSrcList2 = list2
      this.dialogVisibleWatch = true
    },
    //点击删除
    clickProjectDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteProject([{ id: row.id }])
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    //确认新增/修改
    async clickAddProjectData() {
      //新图片上传
      if (this.uploadFileList.length > 0) {
        for (let i = 0; i < this.uploadFileList.length; i++) {
          let fileItem = this.uploadFileList[i]
          const loading = this.$loading({
            lock: true,
            text: `正在上传整改前第${i + 1}张图片`,
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          let formData = new FormData()
          formData.append('file', fileItem.file)
          // formData.append('userNo', this.userNo)
          const res = await post(minio_upload, formData)
          loading.close()
          let index = res.indexOf('?')
          if (index !== -1) {
            fileItem.fileUrl = res.substring(0, index)
          } else {
            fileItem.fileUrl = res
          }
        }
      }
      if (this.uploadFileList2.length > 0) {
        for (let i = 0; i < this.uploadFileList2.length; i++) {
          let fileItem = this.uploadFileList2[i]
          const loading = this.$loading({
            lock: true,
            text: `正在上传整改后第${i + 1}张图片`,
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          let formData = new FormData()
          formData.append('file', fileItem.file)
          // formData.append('userNo', this.userNo)
          const res = await post(minio_upload, formData)
          loading.close()
          let index = res.indexOf('?')
          if (index !== -1) {
            fileItem.fileUrl = res.substring(0, index)
          } else {
            fileItem.fileUrl = res
          }
        }
      }
      //新增的上传图片
      let fileList = []
      this.uploadFileList.forEach(item => {
        if (item.fileUrl) {
          fileList.push({
            type: 'A',
            fileUrl: item.fileUrl,
            fileName: item.fileName,
            parentId: this.projectItem.id
          })
        }
      })
      this.uploadFileList2.forEach(item => {
        if (item.fileUrl) {
          fileList.push({
            type: 'B',
            fileUrl: item.fileUrl,
            fileName: item.fileName,
            parentId: this.projectItem.id
          })
        }
      })
      //原来的fileList
      this.projectItem.fileList.forEach(item => {
        fileList.push(item)
      })
      this.projectItem.fileList2.forEach(item => {
        fileList.push(item)
      })
      //合并后的fileList
      this.projectItem.fileList = fileList
      console.log('新增/修改：', this.projectItem.fileList, this.uploadFileList)
      this.addProjectData()
    },
    //新增/修改
    addProjectData() {
      console.log('projectItem:', this.projectItem)
      const params = [this.projectItem]
      this.ProjectData.loading = true
      post(oneBulletinBoard_saveSecurityCheck, params)
        .then(res => {
          if (res.success) {
            this.$notify.success('操作成功！')
            this.ProjectData.dialogVisible = false
            this.getProjectData()
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
          this.fileList = []
          this.uploadFileList = []
        })
    },
    //删除
    deleteProject(list) {
      post(oneBulletinBoard_deleteSecurityCheck, list).then(res => {
        if (res.success) {
          this.$notify.success('删除成功！')
          this.getProjectData()
        }
      })
    },
    calculateHeight() {
      this.ProjectData.maxHeight = this.$refs.table1.offsetHeight
    },
    getProjectData() {
      this.ProjectData.loading = true
      post(oneBulletinBoard_getSecurityCheck, {
        setDate: this.cDate
      })
        .then(res => {
          this.ProjectData.showGridData = res.data.map(item => {
            let fileList = []
            let fileList2 = []
            item.fileList.forEach(fileItem => {
              if (fileItem.type === 'B') {
                fileList2.push(fileItem)
              } else {
                fileList.push(fileItem)
              }
            })
            return {
              id: item.id,
              workshop: item.workshop,
              dangerType: item.dangerType,
              description: item.description,
              fileUrl: item.fileUrl,
              setDate: item.setDate,
              amount: item.amount,
              modifyWorkshop: item.modifyWorkshop,
              modifyHead: item.modifyHead,
              modifyExecution: item.modifyExecution,
              updateUser: item.updateUser,
              updateDate: item.updateDate,
              fileList: fileList,
              fileList2: fileList2
            }
          })
          this.ProjectData.gridData = lodash.cloneDeep(
            this.ProjectData.showGridData
          )
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    getMonthData() {
      post(oneBulletinBoard_getSecurityCheckCount, {
        // setDate: this.cDate.substring(0, 7)
      }).then(res => {
        if (res && res.success) {
          this.monthData = res.data
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
/deep/ .el-table__body tr:hover > td {
  background-color: rgba(245, 247, 250, 0.1) !important;
  //background-color: transparent !important;
}

/deep/ .el-table__body tr.current-row > td {
  background-color: rgba(245, 247, 250, 0.1) !important;
  //background-color: transparent !important;
}
/deep/ .el-table .class_red {
  background: #fd0000;
  color: black;
}
/deep/ .el-table .class_yellow {
  background: #fdfd00;
  color: black;
}
/deep/ .el-table .class_orange {
  background: #f99f04;
  color: black;
}

.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
