<template>
  <div class="pie-factor-analysis">
    <div 
      ref="pieChart" 
      class="pie-chart"/>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'PieFactorAnalysis',
  props: {
    detailData: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    detailData: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.$nextTick(() => {
            this.renderChart()
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.resizeChart)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChart)
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.pieChart)
      this.renderChart()
    },
    renderChart() {
      if (!this.chart || !this.detailData || this.detailData.length === 0)
        return

      // 处理数据为饼图格式
      const pieData = this.detailData.map(item => ({
        name: item.slabType || '',
        value: parseFloat(item.slabWgt) || 0,
        ratio: item.slabRatio || '0%'
      }))

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: params => {
            const { name, value, data } = params
            return `${name}<br/>数量：${value.toFixed(2)} 吨<br/>占比：${
              data.ratio
            }`
          },
          backgroundColor: 'rgba(8, 47, 60, 0.8)',
          borderColor: '#1fc6ff',
          borderWidth: 1,
          textStyle: {
            color: '#fff'
          },
          extraCssText:
            'box-shadow: 0 0 10px rgba(31, 198, 255, 0.5); border-radius: 4px;'
        },
        legend: {
          orient: 'horizontal',
          bottom: '0%',
          textStyle: {
            color: '#fff'
          },
          itemGap: 20,
          padding: [0, 10, 0, 10]
        },
        series: [
          {
            name: this.title,
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#041a21',
              borderWidth: 2
            },
            label: {
              show: true,
              formatter: '{b}: {c} 吨 ({d}%)',
              color: '#fff'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: pieData
          }
        ],
        color: [
          '#0071F5', // 蓝色
          '#F7C900', // 黄色
          '#3DB842', // 绿色
          '#EF1E0E', // 红色
          '#9D60FF', // 紫色
          '#FF7F00', // 橙色
          '#00CED1', // 青色
          '#FF69B4', // 粉色
          '#32CD32', // 酸橙绿
          '#4169E1', // 皇家蓝
          '#FF4500', // 橙红色
          '#8A2BE2', // 紫罗兰
          '#20B2AA', // 海绿色
          '#FF8C00', // 深橙色
          '#BA55D3', // 兰花紫
          '#00FA9A', // 春绿色
          '#4682B4', // 钢青色
          '#FF1493', // 深粉色
          '#00BFFF', // 深天蓝
          '#FFD700' // 金色
        ]
      }

      this.chart.setOption(option)
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style scoped lang="less">
.pie-factor-analysis {
  width: 100%;
  height: 400px;

  .pie-chart {
    width: 100%;
    height: 100%;
  }
}
</style>
