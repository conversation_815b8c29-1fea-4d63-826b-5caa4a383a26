<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="4"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'炼钢工序能耗'">
            <div class="chart-wrapper">
              <div class="chart-title">
                一炼钢
              </div>
              <div
                class="chart">
                <multi-bars-chart
                  :finished-color="'#19BE6B'"
                  :unfinished-color="'#FF2855'"
                  :bar-width="24"
                  :chart-data="ecChart.bar11"
                  :chart-data2="ecChart.bar12"
                  :x-data="ecChart.barX1"
                  :inverse="ecChart.inverse1"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'轧钢工序能耗'">
            <div class="chart-wrapper">
              <el-row
                :gutter="20"
                class="full-height">
                <el-col
                  :span="8"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div class="chart-title">
                      中厚板卷厂
                    </div>
                    <div
                      class="chart">
                      <multi-bars-chart
                        :bar-width="24"
                        :chart-data="ecChart.bar71"
                        :chart-data2="ecChart.bar72"
                        :x-data="ecChart.barX1"/>
                    </div>
                  </div>
                </el-col>
                <el-col
                  :span="8"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div class="chart-title">
                      宽厚板厂
                    </div>
                    <div
                      class="chart">
                      <multi-bars-chart
                        :bar-width="24"
                        :chart-data="ecChart.bar31"
                        :chart-data2="ecChart.bar32"
                        :x-data="ecChart.barX1"/>
                    </div>
                  </div>
                </el-col>
                <el-col
                  :span="8"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div class="chart-title">
                      中板厂
                    </div>
                    <div
                      class="chart">
                      <multi-bars-chart
                        :bar-width="24"
                        :chart-data="ecChart.bar51"
                        :chart-data2="ecChart.bar52"
                        :x-data="ecChart.barX1"/>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="4"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'金石工序能耗'">
            <div class="chart-wrapper">
              <div class="chart-title">
                金石石灰
              </div>
              <div
                class="chart">
                <multi-bars-chart
                  :bar-width="24"
                  :chart-data="ecChart.bar91"
                  :chart-data2="ecChart.bar92"
                  :x-data="ecChart.barX1"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="4"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'炼钢真空钢比例'">
            <div class="chart-wrapper">
              <div
                class="chart">
                <pie-chart
                  :chart-data="pieChart.bar1"
                  :unit="'%'"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="4"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'加热炉热装率'">
            <div class="chart-wrapper">
              <div
                class="chart">
                <bar-chart
                  :show-legend="false"
                  :chart-data="barChart.bar1"
                  :x-data="barChart.barX1"
                  :unit="'%'"/>
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold" />

    <div class="content-item">

      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="4"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'炼钢工序能耗（月度）'">
            <div class="chart-wrapper">
              <div class="chart-title">
                一炼钢
              </div>
              <div
                class="chart">
                <multi-bars-chart
                  :finished-color="'#19BE6B'"
                  :unfinished-color="'#FF2855'"
                  :bar-width="24"
                  :chart-data="ecChart.bar21"
                  :chart-data2="ecChart.bar22"
                  :x-data="ecChart.barX1"
                  :inverse="ecChart.inverse2"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'轧钢工序能耗（月度）'">
            <div class="chart-wrapper">
              <el-row
                :gutter="20"
                class="full-height">
                <el-col
                  :span="8"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div class="chart-title">
                      中厚板卷厂
                    </div>
                    <div
                      class="chart">
                      <multi-bars-chart
                        :bar-width="24"
                        :chart-data="ecChart.bar81"
                        :chart-data2="ecChart.bar82"
                        :x-data="ecChart.barX1"/>
                    </div>
                  </div>
                </el-col>
                <el-col
                  :span="8"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div class="chart-title">
                      宽厚板厂
                    </div>
                    <div
                      class="chart">
                      <multi-bars-chart
                        :bar-width="24"
                        :chart-data="ecChart.bar41"
                        :chart-data2="ecChart.bar42"
                        :x-data="ecChart.barX1"/>
                    </div>
                  </div>
                </el-col>
                <el-col
                  :span="8"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div class="chart-title">
                      中板厂
                    </div>
                    <div
                      class="chart">
                      <multi-bars-chart
                        :bar-width="24"
                        :chart-data="ecChart.bar61"
                        :chart-data2="ecChart.bar62"
                        :x-data="ecChart.barX1"/>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="4"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'金石工序能耗（月度）'">
            <div class="chart-wrapper">
              <div class="chart-title">
                金石石灰
              </div>
              <div
                class="chart">
                <multi-bars-chart
                  :bar-width="24"
                  :chart-data="ecChart.bar101"
                  :chart-data2="ecChart.bar102"
                  :x-data="ecChart.barX1"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="4"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'炼钢真空钢比例（月度）'">
            <div class="chart-wrapper">
              <div
                class="chart">
                <pie-chart
                  :chart-data="pieChart.bar2"
                  :unit="'%'"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="4"
          class="full-height">
          <screen-border
            :header-class="'text-center'"
            :title="'加热炉热装率（月度）'">
            <div class="chart-wrapper">
              <div
                class="chart">
                <bar-chart
                  :show-legend="false"
                  :chart-data="barMonthChart.bar1"
                  :x-data="barMonthChart.barX1"
                  :unit="'%'"/>
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/qualityMeeting/component/custom-table'
import SingleBarsChart from '@/pages/screen/energyMeeting/component/single-bars-chart'
import BarsChart from '@/pages/screen/qualityMeeting/component/bars-chart'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import { math } from '@/lib/Math'
import * as _ from 'lodash'
import MultiBarsChart from '@/pages/screen/energyMeeting/component/multi-bars-chart'
import PieChart from '@/pages/screen/energyMeeting/component/pie-chart'
import BarChart from '@/pages/screen/energyMeeting/component/bar-chart'
import {
  ecAndCostB1,
  ecAndCostC1,
  ecAndCostC2,
  ecAndCostC3,
  ecAndCostGs,
  HOTTRANSFERDAY,
  HOTTRANSFERMONTHU,
  LGVACUUMTOS
} from '@/api/screenEnergy'
export default {
  name: 'Division',
  components: {
    BarChart,
    PieChart,
    MultiBarsChart,
    ScreenBorder,
    BarsChart,
    SingleBarsChart,
    CustomTable
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      pieChart: {
        bar1: [],
        bar2: []
      },
      barChart: {
        barX1: [],
        bar1: []
      },
      barMonthChart: {
        barX1: [],
        bar1: []
      },

      ecChart: {
        barX1: [],
        bar11: [],
        bar12: [],
        inverse1: false, // 是否反转
        bar21: [],
        bar22: [],
        inverse2: false, // 是否反转
        bar31: [],
        bar32: [],
        bar41: [],
        bar42: [],
        bar51: [],
        bar52: [],
        bar61: [],
        bar62: [],
        bar71: [],
        bar72: [],
        bar81: [],
        bar82: [],
        bar91: [],
        bar92: [],
        bar101: [],
        bar102: []
      }
    }
  },
  computed: {
    showTime: function() {
      return '（' + this.$moment(this.cDate).format('MM月DD日') + '）'
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.loadData()
    }
  },
  mounted() {
    this.cDate = this.selectDate
    this.loadData()
  },
  methods: {
    loadData() {
      // 工序能耗
      post(ecAndCostB1, { flag: '0', date: this.cDate }).then(res => {
        this.ecChart.barX1 = res.data.ec.map(item => item.name)
        this.ecChart.bar11 = res.data.ec
          .filter(item => item.name === '工序能耗')
          .map(item => {
            this.ecChart.inverse1 = item.value < 0
            return {
              value: Math.abs(item.value),
              plan: Math.abs(item.targetValue),
              finished: Math.abs(item.value) > Math.abs(item.targetValue),
              unit: '',
              show: true
            }
          })
        this.ecChart.bar12 = [
          {
            value: 0,
            plan: 0,
            unit: '',
            show: false
          }
        ].concat(
          res.data.ec.filter(item => item.name === '产量').map(item => {
            return {
              value: item.value,
              plan: item.targetValue,
              finished: item.value > item.targetValue,
              unit: '',
              show: true
            }
          })
        )
      })
      post(ecAndCostB1, { flag: '1', date: this.cDate }).then(res => {
        this.ecChart.bar21 = res.data.ec
          .filter(item => item.name === '工序能耗')
          .map(item => {
            this.ecChart.inverse2 = item.value < 0
            return {
              value: Math.abs(item.value),
              plan: Math.abs(item.targetValue),
              finished: Math.abs(item.value) > Math.abs(item.targetValue),
              unit: '',
              show: true
            }
          })
        this.ecChart.bar22 = [
          {
            value: 0,
            plan: 0,
            unit: '',
            show: false
          }
        ].concat(
          res.data.ec.filter(item => item.name === '产量').map(item => {
            return {
              value: item.value,
              plan: item.targetValue,
              finished: item.value > item.targetValue,
              unit: '',
              show: true
            }
          })
        )
      })
      post(ecAndCostC1, { flag: '0', date: this.cDate }).then(res => {
        this.ecChart.barX1 = res.data.ec.map(item => item.name)
        this.ecChart.bar71 = res.data.ec
          .filter(item => item.name === '工序能耗')
          .map(item => {
            this.ecChart.inverse1 = item.value < 0
            return {
              value: Math.abs(item.value),
              plan: Math.abs(item.targetValue),
              finished: Math.abs(item.value) > Math.abs(item.targetValue),
              unit: '',
              show: true
            }
          })
        this.ecChart.bar72 = [
          {
            value: 0,
            plan: 0,
            unit: '',
            show: false
          }
        ].concat(
          res.data.ec.filter(item => item.name === '产量').map(item => {
            return {
              value: item.value,
              plan: item.targetValue,
              finished: item.value > item.targetValue,
              unit: '',
              show: true
            }
          })
        )
      })
      post(ecAndCostC1, { flag: '1', date: this.cDate }).then(res => {
        this.ecChart.bar81 = res.data.ec
          .filter(item => item.name === '工序能耗')
          .map(item => {
            this.ecChart.inverse2 = item.value < 0
            return {
              value: Math.abs(item.value),
              plan: Math.abs(item.targetValue),
              finished: Math.abs(item.value) > Math.abs(item.targetValue),
              unit: '',
              show: true
            }
          })
        this.ecChart.bar82 = [
          {
            value: 0,
            plan: 0,
            unit: '',
            show: false
          }
        ].concat(
          res.data.ec.filter(item => item.name === '产量').map(item => {
            return {
              value: item.value,
              plan: item.targetValue,
              finished: item.value > item.targetValue,
              unit: '',
              show: true
            }
          })
        )
      })
      post(ecAndCostC2, { flag: '0', date: this.cDate }).then(res => {
        this.ecChart.barX1 = res.data.ec.map(item => item.name)
        this.ecChart.bar31 = res.data.ec
          .filter(item => item.name === '工序能耗')
          .map(item => {
            this.ecChart.inverse1 = item.value < 0
            return {
              value: Math.abs(item.value),
              plan: Math.abs(item.targetValue),
              finished: Math.abs(item.value) > Math.abs(item.targetValue),
              unit: '',
              show: true
            }
          })
        this.ecChart.bar32 = [
          {
            value: 0,
            plan: 0,
            unit: '',
            show: false
          }
        ].concat(
          res.data.ec.filter(item => item.name === '产量').map(item => {
            return {
              value: item.value,
              plan: item.targetValue,
              finished: item.value > item.targetValue,
              unit: '',
              show: true
            }
          })
        )
      })
      post(ecAndCostC2, { flag: '1', date: this.cDate }).then(res => {
        this.ecChart.bar41 = res.data.ec
          .filter(item => item.name === '工序能耗')
          .map(item => {
            this.ecChart.inverse2 = item.value < 0
            return {
              value: Math.abs(item.value),
              plan: Math.abs(item.targetValue),
              finished: Math.abs(item.value) > Math.abs(item.targetValue),
              unit: '',
              show: true
            }
          })
        this.ecChart.bar42 = [
          {
            value: 0,
            plan: 0,
            unit: '',
            show: false
          }
        ].concat(
          res.data.ec.filter(item => item.name === '产量').map(item => {
            return {
              value: item.value,
              plan: item.targetValue,
              finished: item.value > item.targetValue,
              unit: '',
              show: true
            }
          })
        )
      })
      post(ecAndCostC3, { flag: '0', date: this.cDate }).then(res => {
        this.ecChart.barX1 = res.data.ec.map(item => item.name)
        this.ecChart.bar51 = res.data.ec
          .filter(item => item.name === '工序能耗')
          .map(item => {
            this.ecChart.inverse1 = item.value < 0
            return {
              value: Math.abs(item.value),
              plan: Math.abs(item.targetValue),
              finished: Math.abs(item.value) > Math.abs(item.targetValue),
              unit: '',
              show: true
            }
          })
        this.ecChart.bar52 = [
          {
            value: 0,
            plan: 0,
            unit: '',
            show: false
          }
        ].concat(
          res.data.ec.filter(item => item.name === '产量').map(item => {
            return {
              value: item.value,
              plan: item.targetValue,
              finished: item.value > item.targetValue,
              unit: '',
              show: true
            }
          })
        )
      })
      post(ecAndCostC3, { flag: '1', date: this.cDate }).then(res => {
        this.ecChart.bar61 = res.data.ec
          .filter(item => item.name === '工序能耗')
          .map(item => {
            this.ecChart.inverse2 = item.value < 0
            return {
              value: Math.abs(item.value),
              plan: Math.abs(item.targetValue),
              finished: Math.abs(item.value) > Math.abs(item.targetValue),
              unit: '',
              show: true
            }
          })
        this.ecChart.bar62 = [
          {
            value: 0,
            plan: 0,
            unit: '',
            show: false
          }
        ].concat(
          res.data.ec.filter(item => item.name === '产量').map(item => {
            return {
              value: item.value,
              plan: item.targetValue,
              finished: item.value > item.targetValue,
              unit: '',
              show: true
            }
          })
        )
      })
      post(ecAndCostGs, { flag: '0', date: this.cDate }).then(res => {
        this.ecChart.barX1 = res.data.ec.map(item => item.name)
        this.ecChart.bar91 = res.data.ec
          .filter(item => item.name === '工序能耗')
          .map(item => {
            this.ecChart.inverse1 = item.value < 0
            return {
              value: Math.abs(item.value),
              plan: Math.abs(item.targetValue),
              finished: Math.abs(item.value) > Math.abs(item.targetValue),
              unit: '',
              show: true
            }
          })
        this.ecChart.bar92 = [
          {
            value: 0,
            plan: 0,
            unit: '',
            show: false
          }
        ].concat(
          res.data.ec.filter(item => item.name === '产量').map(item => {
            return {
              value: item.value,
              plan: item.targetValue,
              finished: item.value > item.targetValue,
              unit: '',
              show: true
            }
          })
        )
      })
      post(ecAndCostGs, { flag: '1', date: this.cDate }).then(res => {
        this.ecChart.bar101 = res.data.ec
          .filter(item => item.name === '工序能耗')
          .map(item => {
            this.ecChart.inverse2 = item.value < 0
            return {
              value: Math.abs(item.value),
              plan: Math.abs(item.targetValue),
              finished: Math.abs(item.value) > Math.abs(item.targetValue),
              unit: '',
              show: true
            }
          })
        this.ecChart.bar102 = [
          {
            value: 0,
            plan: 0,
            unit: '',
            show: false
          }
        ].concat(
          res.data.ec.filter(item => item.name === '产量').map(item => {
            return {
              value: item.value,
              plan: item.targetValue,
              finished: item.value > item.targetValue,
              unit: '',
              show: true
            }
          })
        )
      })
      post(LGVACUUMTOS, { flag: '0', date: this.cDate }).then(res => {
        this.pieChart.bar1 = res.data.map(item => {
          item.value = math.multiply(item.value, 100)
          return item
        })
      })
      post(LGVACUUMTOS, { flag: '1', date: this.cDate }).then(res => {
        this.pieChart.bar2 = res.data.map(item => {
          item.value = math.multiply(item.value, 100)
          return item
        })
      })
      post(HOTTRANSFERDAY, { date: this.cDate }).then(res => {
        this.barChart.barX1 = res.data.map(item => item.name)
        this.barChart.bar1 = [
          {
            name: '加热炉热装率',
            data: res.data.map(item => math.multiply(item.value, 100))
          }
        ]
      })
      post(HOTTRANSFERMONTHU, { date: this.cDate }).then(res => {
        this.barMonthChart.barX1 = res.data.map(item => item.name)
        this.barMonthChart.bar1 = [
          {
            name: '加热炉热装率',
            data: res.data.map(item => math.multiply(item.value, 100))
          }
        ]
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  overflow: auto;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart-title {
    font-style: normal;
    font-weight: 900;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
