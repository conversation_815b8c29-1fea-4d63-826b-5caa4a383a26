<template>
  <div class="node">
    <el-popover
      :open-delay="200"
      :disabled="!showChart"
      placement="right"
      width="400"
      trigger="hover"
      @show="getRule">
      <div>
        <el-button @click="diagramTreeVisible = true">问题追溯</el-button>
      </div>
      <el-table :data="gridData">
        <el-table-column
          property="ruleName"
          label="规则名"/>
        <el-table-column
          min-width="60"
          property="targetValue"
          label="目标值"/>
        <el-table-column
          min-width="60"
          property="resultValue"
          label="实际值"/>
        <el-table-column
          width="80"
          property="ruleStatus"
          label="预警状态">
          <template
            v-slot="{row}"
          >
            <el-tag
              :type="row.ruleStatus ? 'danger' : 'success'"
              disable-transitions
            >{{ row.ruleStatus ? '预警中' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div
        slot="reference"
        :class="{
          'warning': node.warningStatus,
          'trend': node.trendWarningStatus
        }"
        class="node-inner"
        @click="showChartPop">
        <div class="node-describe">
          {{ node.name }}<br>
          <span class="result">
            <template v-if="node.coreRid !== 0">
              <span
                v-if="!editing"
                @click="editNum">
                {{ getCoreResultValue(node, numName, unitName) }} <small>{{ node[unitName] }}</small>
              </span>
              <span
                v-else
                @click="editNum">
                <el-input
                  ref="input"
                  v-model="node[numName]"
                  style="width: 100px"
                  @blur="editing = false"/>
                <small>{{ node[unitName] }}</small>
              </span>
            </template>
          </span>
        </div>
        <el-icon
          v-if="node.children && node.children.length"
          :class="node.hiddenChildren ? 'el-icon-caret-right' : 'el-icon-caret-left'"
          :title="node.hiddenChildren ? '展开' : '收起'"
          class="node-arrow"
          @click.native.prevent.stop="changeStatus"/>
      </div>
    </el-popover>
    <node-chart
      v-if="chartVisible"
      ref="nodeChart"
      v-model="chartVisible"
      :node="node"
      append-to-body/>
    <el-dialog
      v-if="diagramTreeVisible"
      :title="'指标追溯 - ' + node.name"
      :width="'1000px'"
      :top="'50px'"
      :visible.sync="diagramTreeVisible"
      v-bind="$attrs"
      v-on="$listeners">
      <div style="height: 70vh;">
        <kpi-diagram :node-id="node.kid" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCoreResultValue, post } from '@/lib/Util'
import { findResultValueOfMonthAndDay, findRulesOfKpi } from '@/api/kpi'
import NodeChart from '@/components/kpiTree/NodeChart'
import KpiTree from '@/pages/kpi/diagram/component/kpiTree'
import KpiDiagram from '@/components/kpiDiagram/diagram'

export default {
  name: 'Node',
  components: { KpiTree, NodeChart, KpiDiagram },
  props: {
    node: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {}
    },
    showChart: {
      type: Boolean,
      default: true
    },
    numName: {
      type: String,
      default: 'coreResultValue'
    },
    unitName: {
      type: String,
      default: 'unit'
    },
    numEditable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      gridData: [],
      loading: false,
      chartVisible: false,
      diagramTreeVisible: false,
      editing: false
    }
  },
  mounted() {
    this.loading = true
  },
  methods: {
    getRule() {
      if (!this.showChart) return
      if (this.gridData.length) return
      post(findRulesOfKpi, { kid: this.node.kid })
        .then(res => {
          this.gridData = res.data
          this.loading = false
        })
        .catch(e => {
          this.loading = false
        })
    },
    getCoreResultValue(item, a, b) {
      return getCoreResultValue(item, a, b)
    },
    changeStatus() {
      this.$emit('changeStatus')
    },
    showChartPop() {
      this.$emit('handleClick', this.node)
      if (!this.showChart) return
      this.chartVisible = true
    },
    editNum() {
      console.log(1)
      if (!this.numEditable) return
      this.editing = true
      this.$nextTick(() => {
        this.$refs['input'].focus()
      })
    }
  }
}
</script>

<style scoped lang="less">
.node-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .node-describe {
    margin-right: 15px;
    small {
      color: #666;
      font-size: 10px;
    }
  }
  .node-arrow {
    cursor: pointer;
  }
  span {
    display: block;
    color: #5e93ed;
    font-weight: 600;
    font-size: 20px;
    line-height: 28px;
    height: 28px;
  }
  &.trend {
    border-color: #ffa958;
    background: #fff6ee;
    span {
      color: #ffa958;
    }
  }
  &.warning {
    border-color: #f56c6c;
    background: #fef0f0;
    span {
      color: #f56c6c;
    }
  }
}
.node {
  &:focus {
    outline: none;
  }
}
</style>
