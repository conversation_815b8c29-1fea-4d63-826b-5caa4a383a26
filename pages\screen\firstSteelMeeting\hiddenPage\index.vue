<!--隐患-->
<template>
  <div class="content">
    <div
      class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <screen-border-multi :title="'隐患'">
            <template v-slot:headerRight>
              <span
                class="screen-btn"
                @click="clickAddHidden">
                <el-icon class="el-icon-circle-plus-outline"/>
                新增
              </span>
              <!--              <span
                class="screen-btn"
                @click="exportHiddenData">
                <el-icon class="el-icon-download"/>
                导出
              </span>-->
            </template>
            <template v-slot:default>
              <div
                ref="table1"
                class="chart-wrapper">
                <el-table
                  v-loading="HiddenData.loading"
                  :data="HiddenData.showGridData"
                  height="450"
                  border>
                  <el-table-column
                    label="序号"
                    width="50">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.$index + 1 }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="车间"
                    width="100">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.row.area }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="产线"
                    width="100">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.row.productionLine }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    width="400"
                    label="隐患描述">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.row.description }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    width="400"
                    label="隐患原因">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.row.reason }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    width="100"
                    label="隐患等级">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.row.hiddenLevel==='A'?'常规':scope.row.hiddenLevel==='B'?'一般':scope.row.hiddenLevel==='C'?'重大':'' }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    width="300"
                    label="应对措施">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.row.measure }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    width="300"
                    label="整改计划">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.row.rectificationPlan }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    width="130"
                    label="整改时间">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.row.rectificationDate }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    width="100"
                    label="是否完成">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.row.isFinish==='Y'?'是':'否' }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    width="130"
                    label="完成时间">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.row.finishTime }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    width="130"
                    label="创建时间">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.row.createTime }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    width="200"
                    label="备注">
                    <template v-slot="scope">
                      <div :style="{color: scope.row.color}">{{ scope.row.remarks }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    property=""
                    width="150"
                    label="操作">
                    <template v-slot="scope">
                      <span
                        style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                        @click="clickHiddenItem(scope.row)">查看详情</span>
                      <span
                        style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                        @click="clickHiddenDeleteItem(scope.row)">删除</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>

          </screen-border-multi>
        </el-col>
      </el-row>
    </div>
    <!--隐患新增和修改-->
    <el-dialog
      :visible.sync="HiddenData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="隐患详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          隐患详情
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">车间</div>
          <el-input
            v-model="hiddenItem.area"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">产线</div>
          <el-input
            v-model="hiddenItem.productionLine"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
            <!--          <el-select
            v-model="hiddenItem.productionLine"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in factoryGradeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"/>
          </el-select>-->
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">隐患描述</div>
          <el-input
            v-model="hiddenItem.description"
            :rows="3"
            clearable
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">隐患原因</div>
          <el-input
            v-model="hiddenItem.reason"
            :rows="3"
            clearable
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">隐患等级</div>
          <el-select
            v-model="hiddenItem.hiddenLevel"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in hiddenLevelList"
              :key="item.id"
              :label="item.name"
              :value="item.id"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">应对措施</div>
          <el-input
            v-model="hiddenItem.measure"
            :rows="8"
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改计划</div>
          <el-input
            v-model="hiddenItem.rectificationPlan"
            :rows="8"
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改时间</div>
          <el-date-picker
            v-model="hiddenItem.rectificationDate"
            :clearable="false"
            :size="'mini'"
            :value-format="'yyyy-MM-dd'"
            class="screen-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">是否完成</div>
          <el-select
            v-model="hiddenItem.isFinish"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in isFinishList"
              :key="item.id"
              :label="item.name"
              :value="item.id"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">备注 </div>
          <el-input
            v-model="hiddenItem.remarks"
            :rows="8"
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="addHiddenData()">
          确定
        </span>
      </div>
    </el-dialog>
    <!--隐患新增修改删除-->
    <!--    <el-dialog
      :visible.sync="HiddenData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="隐患">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('HiddenData')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importHiddenData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportHiddenData">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveHiddenData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          隐患
        </div>
      </template>
      <el-form :disabled="!canEdit">
        <el-table
          v-loading="HiddenData.loading"
          :data="HiddenData.gridData"
          :height="'calc(100vh - 345px)'"
          border>
          <el-table-column
            property="item"
            label="区域">
            <template v-slot="{ row }">
              <el-input v-model="row.area" />
            </template>
          </el-table-column>
          <el-table-column
            property="content"
            label="产线">
            <template v-slot="{ row }">
              <el-select
                v-model="row.productionLine"
                class="screen-input"
                placeholder="请选择">
                <el-option
                  v-for="item in factoryGradeList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"/>
              </el-select>
              &lt;!&ndash;              <el-input v-model="row.content" />&ndash;&gt;
            </template>
          </el-table-column>
          <el-table-column
            property="remarks"
            label="隐患描述">
            <template v-slot="{ row }">
              <el-input v-model="row.description" />
            </template>
          </el-table-column>
          <el-table-column
            property="remarks"
            label="应对措施">
            <template v-slot="{ row }">
              <el-input v-model="row.measure" />
            </template>
          </el-table-column>
          <el-table-column
            property="remarks"
            label="整改计划">
            <template v-slot="{ row }">
              <el-input v-model="row.rectificationPlan" />
            </template>
          </el-table-column>
          <el-table-column
            property="time"
            label="整改时间">
            <template v-slot="{ row }">
              <el-date-picker
                v-model="row.rectificationDate"
                :clearable="false"
                :size="'mini'"
                :value-format="'yyyy-MM-dd'"
                class="screen-input"/>
            </template>
          </el-table-column>
          <el-table-column
            property="remarks"
            label="备注">
            <template v-slot="{ row }">
              <el-input v-model="row.remarks" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index, 'HiddenData')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEdit"
          class="screen-btn"
          @click="addGridData('HiddenData')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>-->

    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="上月导入日期库存">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import FirstSteelChart from '@/pages/screen/firstSteelMeeting/component/first-steel-chart'
import FirstSteelPie from '@/pages/screen/firstSteelMeeting/component/first-steel-pie.vue'
import { post } from '@/lib/Util'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import {
  firstMeetingHidden1,
  firstMeetingHidden2,
  firstMeetingHidden3
} from '@/api/firstMeeting'
import lodash from 'lodash'
export default {
  name: 'fuelPage',
  components: {
    ScreenBorder,
    ScreenBorderMulti,
    FirstSteelChart,
    FirstSteelPie
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      hiddenLoading: false,
      hiddenList: [],
      //产线类别
      factoryGradeList: [
        {
          id: '主要产线',
          name: '主要产线'
        },
        {
          id: '重要产线',
          name: '重要产线'
        },
        {
          id: '辅助产线',
          name: '辅助产线'
        }
      ],
      //隐患等级
      hiddenLevelList: [
        {
          id: 'A',
          name: '常规'
        },
        {
          id: 'B',
          name: '一般'
        },
        {
          id: 'C',
          name: '重大'
        }
      ],
      //是否完成
      isFinishList: [
        {
          id: 'N',
          name: '否'
        },
        {
          id: 'Y',
          name: '是'
        }
      ],
      cDate: '',
      HiddenData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      hiddenItem: {}
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.$nextTick(item => {
        this.init()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    // this.init()
  },
  methods: {
    clickAddHidden() {
      this.hiddenItem = {
        area: '',
        productionLine: '',
        description: '',
        reason: '',
        hiddenLevel: 'A',
        measure: '',
        rectificationPlan: '',
        rectificationDate: '',
        isFinish: 'N',
        remarks: '',
        setDate: this.cDate
      }
      this.HiddenData.dialogVisible = true
    },
    //隐患点击查看详情
    clickHiddenItem(row) {
      this.hiddenItem = JSON.parse(JSON.stringify(row))
      this.HiddenData.dialogVisible = true
    },
    //隐点击查看详情
    clickHiddenDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteHidden(row.id)
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    //新增/修改
    addHiddenData() {
      // const params = [this.hiddenItem]
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: [this.hiddenItem]
      }
      post(firstMeetingHidden2, params).then(res => {
        if (res.success) {
          this.HiddenData.dialogVisible = false
          this.getHiddenData()
        }
      })
    },
    //删除隐患
    deleteHidden(id) {
      const params = [
        {
          id: id
        }
      ]
      post(firstMeetingHidden3, params).then(res => {
        if (res.success) {
          this.$notify.success('删除成功！')
          this.getHiddenData()
        }
      })
    },
    init() {
      this.getHiddenData()
    },
    //导入昨天数据或者选择的某一天导入数据
    importHiddenData(date) {
      post(firstMeetingHidden1, {
        setDate: date
      }).then(res => {
        //
        this.HiddenData.loading = false
        this.HiddenData.gridData = res.data.map(item => {
          return {
            area: item.area,
            productionLine: item.productionLine,
            description: item.description,
            hiddenLevel: item.hiddenLevel,
            measure: item.measure,
            rectificationPlan: item.rectificationPlan,
            rectificationDate: item.rectificationDate,
            isFinish: item.isFinish,
            finishTime: item.finishTime,
            createTime: item.createTime,
            remarks: item.remarks,
            setDate: item.setDate
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    //导入
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          area: 'A',
          productionLine: 'B',
          description: 'C',
          measure: 'D',
          rectificationPlan: 'E',
          rectificationDate: 'F',
          isFinish: 'G',
          finishTime: 'H',
          remarks: 'I'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.HiddenData.gridData = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    //导出
    exportHiddenData() {
      const data = [
        {
          area: '车间',
          productionLine: '产线',
          description: '隐患描述',
          hiddenLevel: '隐患等级',
          measure: '应对措施',
          rectificationPlan: '整改计划',
          rectificationDate: '整改时间',
          isFinish: '是否完成',
          finishTime: '完成时间',
          remarks: '备注'
        }
      ].concat(
        _.cloneDeep(this.HiddenData.gridData).map(item => {
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `隐患（${this.cDate}晨会）.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },
    //隐患查询
    getHiddenData() {
      const params = {
        setDate: this.cDate
      }
      this.HiddenData.loading = true
      post(firstMeetingHidden1, params)
        .then(res => {
          if (res.success) {
            this.HiddenData.showGridData = res.data.map(item => {
              return {
                id: item.id,
                area: item.area,
                productionLine: item.productionLine,
                description: item.description,
                reason: item.reason,
                hiddenLevel: item.hiddenLevel,
                measure: item.measure,
                rectificationPlan: item.rectificationPlan,
                rectificationDate: item.rectificationDate,
                isFinish: item.isFinish,
                isFinishName: item.isFinish === 'Y' ? '是' : '否',
                finishTime: item.finishTime,
                createTime: item.createTime,
                remarks: item.remarks,
                setDate: item.setDate,
                color:
                  item.isFinish === 'Y'
                    ? 'green'
                    : item.hiddenLevel === 'B'
                      ? 'yellow'
                      : item.hiddenLevel === 'C'
                        ? 'red'
                        : 'white'
              }
            })
            let order = ['red', 'yellow', 'white', 'green']
            this.HiddenData.showGridData.sort(function(a, b) {
              return order.indexOf(a.color) - order.indexOf(b.color)
            })
            console.log(
              ' this.HiddenData.showGridData',
              this.HiddenData.showGridData
            )

            this.HiddenData.gridData = lodash.cloneDeep(
              this.HiddenData.showGridData
            )
          }
        })
        .finally(_ => {
          this.HiddenData.loading = false
        })
    },
    //保存数据
    saveHiddenData() {
      this.HiddenData.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.HiddenData.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(firstMeetingHidden2, params)
        .then(res => {
          if (res.success) {
            this.$message.success('保存成功！')
            this.HiddenData.dialogVisible = false
            this.getHiddenData()
          }
        })
        .finally(_ => {
          this.HiddenData.loading = false
        })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}

.tabs-class {
  display: flex;
  flex-direction: row;
  .tab-pane {
    color: #ffffffbf;
    margin-right: 21px;
  }
  .tab-pane-active {
    color: #ffffff;
  }
  .tab-pane-title-class {
    display: flex;
    flex-direction: column;
    position: relative;
    .tab-pane-img {
      .tab-pane-img2 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
      .tab-pane-img1 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        margin-bottom: 7px;
      }
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }

  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  overflow: auto;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
