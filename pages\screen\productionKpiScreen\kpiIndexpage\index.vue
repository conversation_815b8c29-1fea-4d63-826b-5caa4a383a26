<template>
  <div class="content">
    <div
      class="content-hold slick">
      <div class="kpi-list">
        <div 
          v-for="(item, index) in kpiList"
          :key="index"
          class="item">
          <img
            :src="item.src"
            alt=""
          >
          <div class="kpi-text">
            <div class="name">
              {{ item.name }}
            </div>
            <div class="num">
              {{ item.num }}
              <small class="unit">
                {{ item.unit }}
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <el-row
                :gutter="32"
                class="full-height">
                <el-col
                  :span="12"
                  class="full-height">
                  <screen-border title="钢产量（月度）">
                    <div class="chart-wrapper">
                      <div
                        class="cards">
                        <div class="card">
                          <span class="name">累计产量</span>
                          <span class="num"><em>{{ steelMonth.output }}</em> t</span>
                        </div>
                        <div class="card">
                          <span class="name">超欠</span>
                          <span class="num">
                            <em :class="{'red': steelMonth.targetSchedule < 0, 'green': steelMonth.targetSchedule > 0}">
                              {{ steelMonth.targetSchedule }}
                            </em>
                            t</span>
                        </div>
                        <div class="card">
                          <span class="name">超欠进度</span>
                          <span class="num"><em>{{ steelMonth.percent }}</em> %</span>
                        </div>
                      </div>
                      <div style="flex: 1">
                        <single-bars-chart
                          :show-legend="false"
                          :bar-width="20"
                          :chart-data="steelMonth.bar1"
                          :x-data="['一炼钢吨位']"/>
                      </div>
                    </div>
                  </screen-border>
                </el-col>
                <el-col
                  :span="12"
                  class="full-height">
                  <screen-border title="热轧产量（月度）">
                    <div class="chart-wrapper">
                      <div
                        class="cards">
                        <div class="card">
                          <span class="name">累计产量</span>
                          <span class="num"><em>{{ rollMonth.output }}</em> t</span>
                        </div>
                        <div class="card">
                          <span class="name">超欠</span>
                          <span class="num"><em :class="{'red': rollMonth.targetSchedule < 0, 'green': rollMonth.targetSchedule >= 0}">{{ rollMonth.targetSchedule }}</em> t</span>
                        </div>
                        <div class="card">
                          <span class="name">超欠进度</span>
                          <span class="num"><em>{{ rollMonth.percent }}</em> %</span>
                        </div>
                      </div>
                      <div style="height: 85%">
                        <single-bars-chart
                          :show-legend="false"
                          :bar-width="20"
                          :chart-data="rollMonth.bar1"
                          :x-data="['板卷厂', '宽厚板厂', '中板厂']"/>
                      </div>
                    </div>
                  </screen-border>
                </el-col>
              </el-row>
            </div>
            <div class="content-hold"/>
            <div class="content-item">
              <screen-border :title="'坯库存 ' + searchTime.format('yyyy年MM月')">
                <template v-slot:headerRight/>
                <div class="chart-wrapper">
                  <div
                    class="chart">
                    <stock-line-chart
                      :last-month-data="stock1.blankLastMonth || '0'"
                      :month-plan-data="stock1.blankMonthPlan || '0'"
                      :show-legend="true"
                      :chart-data="stock1.bar1"
                      :x-data="stock1.barX"/>
                  </div>
                </div>
              </screen-border>
            </div>
            <div class="content-hold"/>
            <div class="content-item">
              <screen-border title="三个厂订单">
                <template v-slot:headerRight/>
                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="loading"
                    :data="factoryOrder.showGridData"
                    :span-method="arraySpanMethod"
                    :max-height="factoryOrder.maxHeight"
                    :row-class-name="tableTotalClass"
                    border>
                    <el-table-column
                      align="center"
                      property="thFactoryOrder"
                      label="三个厂订单"/>
                    <el-table-column
                      align="center"
                      property=""
                      label="等待生产">
                      <el-table-column
                        align="center"
                        property="waitSteelmaking"
                        label="待炼钢"/>
                      <el-table-column
                        align="center"
                        property="waitSteelrolling"
                        label="待轧钢"/>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property="trackingContent"
                      label="暂缓生产">
                      <el-table-column
                        align="center"
                        property="postponeSteelmaking"
                        label="待炼钢"/>
                      <el-table-column
                        align="center"
                        property="postponeSteelrolling"
                        label="待轧钢"/>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property="unsoldOrder"
                      label="未抛单"/>
                    <el-table-column
                      align="center"
                      property="thFactoryOrdTotal"
                      label="合计"/>
                    <el-table-column
                      align="center"
                      property="ordeNotch"
                      label="完成计划的订单缺口"/>
                    <el-table-column
                      align="center"
                      property=""
                      label="当日确认订单">
                      <el-table-column
                        align="center"
                        property="vacuumSteelConfirm"
                        label="真空钢"/>
                      <el-table-column
                        align="center"
                        property="commonSteelConfirm"
                        label="普通钢"/>
                      <el-table-column
                        align="center"
                        property="confirmTotal"
                        label="合计"/>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border>
            </div>
          </div>
        </el-col>
        <el-col
          :span="6"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <custom-table
                :title="'综判产量'"
                :setting="tableObj"
                :url-list="tableUrl.list"
                :url-save="tableUrl.save"
                :params="{type: 'A'}"
                :select-date="selectDate"
                :show-table="false"
                @change="getChartZp">
                <template v-slot:content>
                  <div class="chart-wrapper">
                    <div
                      class="cards">
                      <div class="card">
                        <span class="name">累计产量</span>
                        <span class="num"><em>{{ zpMonth.output }}</em> t</span>
                      </div>
                      <div class="card">
                        <span class="name">超欠</span>
                        <span class="num"><em :class="{'red': zpMonth.targetSchedule < 0, 'green': zpMonth.targetSchedule >= 0}">{{ zpMonth.targetSchedule }}</em> t</span>
                      </div>
                      <div class="card">
                        <span class="name">超欠进度</span>
                        <span class="num"><em>{{ zpMonth.percent }}</em> %</span>
                      </div>
                    </div>
                    <div style="height: 85%">
                      <single-bars-chart
                        :show-legend="false"
                        :bar-width="20"
                        :chart-data="zpMonth.bar1"
                        :x-data="['板卷厂', '宽厚板厂', '中板厂']"/>
                    </div>
                  </div>
                </template>
              </custom-table>
            </div>
            <div class="content-hold"/>
            <div class="content-item">
              <screen-border title="热处理总产量（月度）">
                <div class="chart-wrapper">
                  <div
                    class="cards">
                    <div class="card">
                      <span class="name">累计产量</span>
                      <span class="num"><em>{{ heatMonth.output }}</em> t</span>
                    </div>
                    <div class="card">
                      <span class="name">超欠</span>
                      <span class="num"><em :class="{'red': heatMonth.targetSchedule < 0, 'green': heatMonth.targetSchedule >= 0}">{{ heatMonth.targetSchedule }}</em> t</span>
                    </div>
                    <div class="card">
                      <span class="name">超欠进度</span>
                      <span class="num"><em>{{ heatMonth.percent }}</em> %</span>
                    </div>
                  </div>
                  <div style="height: 85%">
                    <single-bars-chart
                      :show-legend="false"
                      :bar-width="20"
                      :chart-data="heatMonth.bar1"
                      :x-data="['板卷厂', '宽厚板厂', '中板厂']"/>
                  </div>
                </div>
              </screen-border>
            </div>
            <div class="content-hold"/>
            <div class="content-item">
              <screen-border title="合同兑现率（月度）">
                <div class="chart-wrapper">
                  <div
                    class="cards">
                    <div class="card">
                      <span class="name">总计</span>
                      <span class="num"><em>{{ other.bar1Total || '-' }}</em>%</span>
                    </div>
                  </div>
                  <div style="height: 85%">
                    <bars-chart
                      :show-legend="false"
                      :bar-width="20"
                      :chart-data="other.bar1"
                      :x-data="['板卷厂', '宽厚板厂', '中板厂']"
                      unit="%"/>
                  </div>
                </div>
              </screen-border>
            </div>
          </div>
        </el-col>
        <el-col
          :span="6"
          class="full-height">
  
          <div class="content">
            <div class="content-item">
              <custom-table
                :title="'在制品统计（月度）'"
                :setting="tableObj"
                :url-list="tableUrl.list"
                :url-save="tableUrl.save"
                :params="{type: 'B'}"
                :select-date="selectDate"
                :show-table="false"
                @change="getChartZzp">
                <template v-slot:content>
                  <div class="chart-wrapper">
                    <div
                      class="cards">
                      <div class="card">
                        <span class="name">累计产量</span>
                        <span class="num"><em>{{ zzpMonth.output }}</em> t</span>
                      </div>
                      <div class="card">
                        <span class="name">超欠</span>
                        <span class="num"><em :class="{'red': zzpMonth.targetSchedule < 0, 'green': zzpMonth.targetSchedule >= 0}">{{ zzpMonth.targetSchedule }}</em> t</span>
                      </div>
                      <div class="card">
                        <span class="name">超欠进度</span>
                        <span class="num"><em>{{ zzpMonth.percent }}</em> %</span>
                      </div>
                    </div>
                    <div style="height: 85%">
                      <single-bars-chart
                        :show-legend="false"
                        :bar-width="20"
                        :chart-data="zzpMonth.bar1"
                        :x-data="['板卷厂', '宽厚板厂', '中板厂']"/>
                    </div>
                  </div>
                </template>
              </custom-table>
            </div>
            <div class="content-hold"/>
            <div class="content-item">
              <screen-border title="综合非计划（月度）">
                <div class="chart-wrapper">
                  <div
                    class="cards">
                    <div class="card">
                      <span class="name">总计</span>
                      <span class="num"><em>{{ other.bar2Total || '-' }}</em>%</span>
                    </div>
                  </div>
                  <div style="height: 85%">
                    <bars-chart
                      :show-legend="false"
                      :bar-width="20"
                      :chart-data="other.bar2"
                      :x-data="['板卷厂', '宽厚板厂', '中板厂']"
                      unit="%"/>
                  </div>
                </div>
              </screen-border>
            </div>
            <div class="content-hold"/>
            <div class="content-item">
              <screen-border title="250℃以上热装热送率">
                <div class="chart-wrapper">
                  <div
                    class="cards">
                    <div class="card">
                      <span class="name">总计</span>
                      <span class="num"><em>{{ other.bar3Total || '-' }}</em>%</span>
                    </div>
                  </div>
                  <div style="height: 85%">
                    <bars-chart
                      :show-legend="false"
                      :bar-width="20"
                      :chart-data="other.bar3"
                      :x-data="['板卷厂', '宽厚板厂', '中板厂']"
                      unit="%"/>
                  </div>
                </div>
              </screen-border>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--产量-->
    <el-dialog
      :visible.sync="steelYesterday.dialogVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="产量详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="SyncData(steelOutputTask)">
              手动同步数据
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearShowGridData('steelYesterday')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <template v-if="canEdit">
              <el-dropdown @command="handleProcessedCommand($event, 'importSteelData')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportSteel">
              <el-icon class="el-icon-export"/>
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveSteel">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          产量详情
        </div>
      </template>
      <el-form
        v-loading="syncLoading" 
        :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="steelYesterday.showGridData"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="45"/>
          <el-table-column
            property="plt"
            label="产线"
            width="100">
            <template v-slot="{ row }">
              <el-input
                v-model="row.plt" />
            </template>
          </el-table-column>
          <el-table-column
            property="unit"
            label="单位">
            <template v-slot="{ row }">
              <el-select
                :popper-append-to-body="false"
                v-model="row.unit">
                <el-option
                  v-for="(item, index) in unitList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            property="plan"
            label="计划">
            <template v-slot="{ row }">
              <el-input
                v-model="row.plan" />
            </template>
          </el-table-column>
          <el-table-column
            property="reality"
            label="实绩">
            <template v-slot="{ row }">
              <el-input
                v-model="row.reality"
                :class="{'input-green': row.unit !== '炉数'}" />
            </template>
          </el-table-column>
          <el-table-column
            property="complete"
            label="是否完成">
            <template v-slot="{ row }">
              <el-select
                :popper-append-to-body="false"
                v-model="row.complete">
                <el-option
                  v-for="(item, index) in finishList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            property="unfinishedCause"
            label="未完成原因">
            <template v-slot="{ row }">
              <el-input v-model="row.unfinishedCause" />
            </template>
          </el-table-column>
          <el-table-column
            property="responsibleUnit"
            label="责任单位">
            <template v-slot="{ row }">
              <el-input v-model="row.responsibleUnit" />
            </template>
          </el-table-column>
          <el-table-column
            property="cumulativeOutput"
            label="月计划产量">
            <template v-slot="{ row }">
              <el-input
                v-model="row.monplan"
                :class="{'input-green': row.unit !== '炉数'}" />
            </template>
          </el-table-column>
          <el-table-column
            property="targetProduction"
            label="月目标产量">
            <template v-slot="{ row }">
              <el-input
                v-model="row.targetProduction"
                :class="{'input-green': row.unit !== '炉数'}" />
            </template>
          </el-table-column>
          <el-table-column
            property="cumulativeOutput"
            label="累计产量">
            <template v-slot="{ row }">
              <el-input
                v-model="row.cumulativeOutput"
                :class="{'input-green': row.unit !== '炉数'}" />
            </template>
          </el-table-column>
          <el-table-column
            property="mtcPfdTime"
            label="已执行检修时间（h）">
            <template v-slot="{ row }">
              <el-input
                v-model="row.mtcPfdTime" />
            </template>
          </el-table-column>
          <el-table-column
            property="rmMtTime"
            label="剩余检修时间（h）">
            <template v-slot="{ row }">
              <el-input
                v-model="row.rmMtTime" />
            </template>
          </el-table-column>
          <el-table-column
            property="targetSchedule"
            label="超欠目标进度(%)">
            <template v-slot="{ row }">
              <el-input v-model="row.targetSchedule" />
            </template>
          </el-table-column>
          <el-table-column
            property="targetSchedule"
            label="日需均产">
            <template v-slot="{ row }">
              <el-input v-model="row.avgDailyProduction" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delShowGridData($index, 'steelYesterday')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
        <div class="text-center">
          <span
            v-if="canEdit"
            class="screen-btn"
            @click="addShowGridData('steelYesterday')">
            <el-icon class="el-icon-circle-plus-outline"/>
            增加数据
          </span>
        </div>
        <br>
        <el-form-item label="喜报:">
          <el-input
            v-model="steelYesterday.noticeEdit"
            type="textarea"
            placeholder="输入喜报"/>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
  
  <script>
import ScreenBorder from '@/pages/screen/productionKpiScreen/component/screen-border'
import SingleBarsChart from '@/pages/screen/productionKpiScreen/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import BarsChart from '@/pages/screen/productionKpiScreen/component/bars-chart'
import StockLineChart from '@/pages/screen/morningMeeting/component/stock-line-chart'
import CustomTable from '@/pages/screen/productionKpiScreen/component/custom-table'
import { post } from '@/lib/Util'
import * as _ from 'lodash'
import {
  findBlankDetail,
  findBlankStock,
  findBlankStockPltZj,
  findBoardParameterByDateAndPara,
  findHeatTreatmentYieldByDate,
  findKeyIndic,
  findKeyResultByDate,
  findSteelOutputByDate,
  findThreeFactoryOrderByDate,
  saveBoardParameter,
  saveKeyResul,
  saveSteelOutput,
  steelOutputTask
} from '@/api/screen'
import { math } from '@/lib/Math'
import moment from 'moment'
import lodash from 'lodash'
export default {
  name: 'KpiIndePage',
  components: {
    CustomTable,
    BarsChart,
    StockLineChart,
    SingleBarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      steelOutputTask: steelOutputTask,
      loading: false,
      steelYesterday: {
        bar1: [
          {
            value: 0,
            plan: 0,
            unit: '吨'
          }
        ],
        bar2: [
          {
            value: 0,
            plan: 0,
            unit: '炉'
          },
          {
            value: 0,
            plan: 0,
            unit: '炉'
          }
        ],
        failReason: '',
        notice: '',
        noticeEdit: '',
        editType: '',
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      steelMonth: {
        bar1: [
          {
            value: 0,
            plan: 0,
            unit: '吨'
          }
        ],
        bar2: [
          {
            value: 0,
            plan: 0,
            unit: '炉'
          },
          {
            value: 0,
            plan: 0,
            unit: '炉'
          }
        ],
        output: 0,
        targetSchedule: 0,
        percent: 0
      },
      rollMonth: {
        bar1: [],
        failReason: '',
        output: 0,
        targetSchedule: 0,
        percent: 0
      },
      heatMonth: {
        bar1: [],
        barX: [],
        gridData: [],
        failReason: '',
        output: 0,
        targetSchedule: 0,
        percent: 0
      },
      unitList: ['吨位', '炉数'],
      finishList: ['是', '否'],
      stock1: {
        bar1: [],
        barX: [],
        gridData: [],
        dialogVisible: false,
        detailVisible: false,
        blankMonthPlan: '',
        blankLastMonth: ''
      },
      kpiList: [
        {
          name: '钢产量',
          num: '-',
          unit: '吨',
          src: require('../../../../assets/images/screen/kpi/icon1.png')
        },
        {
          name: '热轧总产量',
          num: '-',
          unit: '吨',
          src: require('../../../../assets/images/screen/kpi/icon2.png')
        },
        {
          name: '综判总产量',
          num: '-',
          unit: '吨',
          src: require('../../../../assets/images/screen/kpi/icon1.png')
        },
        {
          name: '热处理炉总产量',
          num: '-',
          unit: '吨',
          src: require('../../../../assets/images/screen/kpi/icon3.png')
        },
        {
          name: '在制品',
          num: '-',
          unit: '吨',
          src: require('../../../../assets/images/screen/kpi/icon6.png')
        },
        {
          name: '综合非计划',
          num: '-',
          unit: '%',
          red: true,
          src: require('../../../../assets/images/screen/kpi/icon5.png')
        },
        {
          name: '热装热送率',
          num: '-',
          unit: '%',
          src: require('../../../../assets/images/screen/kpi/icon4.png')
        },
        {
          name: '合同兑现率',
          num: '-',
          unit: '%',
          src: require('../../../../assets/images/screen/kpi/icon7.png')
        }
      ],
      factoryOrder: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      other: {
        bar1: [],
        bar1Total: null,
        bar2: [],
        bar2Total: null,
        bar3: [],
        bar3Total: null
      },
      tableUrl: {
        save: saveKeyResul,
        list: findKeyResultByDate
      },
      tableObj: [
        {
          keyQuery: 'plt',
          keySave: 'plt',
          label: '轧钢厂'
        },
        {
          keyQuery: 'unit',
          keySave: 'unit',
          label: '单位'
        },
        {
          keyQuery: 'cumulativeOutput',
          keySave: 'cumulativeOutput',
          label: '累计产量'
        },
        {
          keyQuery: 'targetProduction',
          keySave: 'targetProduction',
          label: '目标'
        },
        {
          keyQuery: 'targetSchedule',
          keySave: 'targetSchedule',
          label: '超欠目标进度'
        },
        {
          keyQuery: 'avgDailyProduction',
          keySave: 'avgDailyProduction',
          label: '日需均产'
        },
        {
          keyQuery: 'type',
          keySave: 'type',
          label: '类型', //  A 综判 B 在制品
          show: false
        }
      ],
      zzpMonth: {
        bar1: [],
        failReason: '',
        output: 0,
        targetSchedule: 0,
        percent: 0
      },
      zpMonth: {
        bar1: [],
        failReason: '',
        output: 0,
        targetSchedule: 0,
        percent: 0
      }
    }
  },
  computed: {
    searchTime: function() {
      return moment(this.cDate).subtract(2, 'day')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.$nextTick(() => {
        this.loadData()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.loadData()
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    async loadData() {
      this.$nextTick(() => {
        this.getSteal()
        this.getZZP()
        this.getHeat()
        this.getOther()
        this.getfactoryOrder()
      })
    },
    handlePreview(file) {
      try {
        LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
          data = LAY_EXCEL.filterImportData(data, {
            num: 'A',
            plt: 'B',
            unit: 'C',
            plan: 'D',
            reality: 'E',
            complete: 'F',
            unfinishedCause: 'G',
            responsibleUnit: 'H',
            monplan: 'I',
            targetProduction: 'J',
            cumulativeOutput: 'K',
            mtcPfdTime: 'L',
            rmMtTime: 'M',
            targetSchedule: 'N',
            avgdailyproduction: 'O'
          })
          // 去除第一行
          const sheet = data[0].Sheet1 || data[0].sheet1
          sheet.shift()
          // 表格信息
          this.steelYesterday.showGridData = sheet.map(item => {
            item.unit = item.unit.trim()
            item.targetSchedule = item.targetSchedule.toString()
            item.type = this.steelYesterday.editType
            if (item.targetSchedule.includes('%')) {
              item.targetSchedule = item.targetSchedule.replace('%', '')
            } else {
              item.targetSchedule = Number(
                math.multiply(Number(item.targetSchedule), 100).toFixed(2)
              )
            }
            return item
          })
          this.$message.success('解析成功！')
        })
      } catch (e) {
        this.$message.warning('解析失败！')
      }
    },
    handleSteel(type) {
      this.steelYesterday.editType = type
      this.steelYesterday.showGridData = this.steelYesterday.gridData.filter(
        item => item.type === type
      )
      this.steelYesterday.dialogVisible = true
    },
    getSteal() {
      this.steelYesterday.gridData = []
      // this.clearViewData()
      post(findSteelOutputByDate, {
        setDate: this.cDate
      }).then(res => {
        this.loading = false
        this.clearViewData()
        this.steelYesterday.gridData = res.data.map(item => {
          return {
            num: item.num,
            plt: item.plt,
            unit: item.unit,
            plan: item.plan,
            reality: item.reality,
            complete: item.complete,
            unfinishedCause: item.unfinishedcause,
            responsibleUnit: item.responsibleunit,
            monplan: item.monplan,
            targetProduction: item.targetproduction,
            cumulativeOutput: item.cumulativeoutput,
            mtcPfdTime: item.mtcpfdtime,
            rmMtTime: item.rmmttime,
            targetSchedule: Number(item.targetschedule || 0).toFixed(2),
            avgDailyProduction: item.avgdailyproduction,
            type: item.type
          }
        })
        this.steelYesterday.editType &&
          (this.steelYesterday.showGridData = this.steelYesterday.gridData.filter(
            item => item.type === this.steelYesterday.editType
          ))
        if (!res.data.length) return
        this.formatViewData()
      })
    },
    clearViewData() {
      this.steelMonth = {
        bar1: [
          {
            value: 0,
            plan: 0,
            unit: '吨'
          }
        ],
        bar2: [
          {
            value: 0,
            plan: 0,
            unit: '炉'
          },
          {
            value: 0,
            plan: 0,
            unit: '炉'
          }
        ],
        output: 0,
        targetSchedule: 0,
        percent: 0
      }
      this.rollMonth = {
        bar1: [],
        failReason: '',
        output: 0,
        targetSchedule: 0,
        percent: 0
      }
    },
    formatViewData() {
      try {
        const b11 = this.steelYesterday.gridData.find(
          item => item.plt.trim() === 'B1' && item.unit.trim() === '吨位'
        )
        const b12 = this.steelYesterday.gridData.find(
          item => item.plt.trim() === 'B1' && item.unit.trim() === '炉数'
        )
        b11 &&
          Object.assign(this.steelMonth, {
            bar1: [
              {
                value: b11.cumulativeOutput,
                plan: b11.targetProduction || b11.monplan,
                finished: b11.targetSchedule >= 0,
                unit: '吨',
                totalText: b11.targetProduction ? '目标' : '计划',
                targetSchedule: b11.targetSchedule ? b11.targetSchedule : '',
                schedule: b11.targetSchedule
                  ? math.divide(
                      math.multiply(
                        b11.targetSchedule || 0,
                        b11.targetProduction || b11.monplan
                      ),
                      100
                    )
                  : '',
                avgDaily: b11.avgDailyProduction ? b11.avgDailyProduction : ''
              }
            ],
            targetSchedule: math.divide(
              math.multiply(
                b11.targetSchedule || 0,
                b11.targetProduction || b11.monplan
              ),
              100
            ),
            output: b11.cumulativeOutput,
            percent: b11.targetSchedule
          })

        this.setTopData('钢产量', b11.cumulativeOutput)
      } catch (e) {}

      try {
        const C1 = this.steelYesterday.gridData.find(
          item => item.plt.trim() === 'C1'
        )
        const C2 = this.steelYesterday.gridData.find(
          item => item.plt.trim() === 'C2'
        )
        const C3 = this.steelYesterday.gridData.find(
          item => item.plt.trim() === 'C3'
        )
        Object.assign(this.rollMonth, {
          bar1: [
            {
              value: C1 ? C1.cumulativeOutput : 0,
              plan: C1 ? C1.targetProduction || C1.monplan : 0,
              finished: C1.targetSchedule >= 0,
              totalText: C1.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C1.targetSchedule ? C1.targetSchedule : '',
              schedule: C1.targetSchedule
                ? math.divide(
                    math.multiply(
                      C1.targetSchedule || 0,
                      C1.targetProduction || C1.monplan
                    ),
                    100
                  )
                : '',
              avgDaily: C1.avgDailyProduction ? C1.avgDailyProduction : ''
            },
            {
              value: C2 ? C2.cumulativeOutput : 0,
              plan: C2 ? C2.targetProduction || C2.monplan : 0,
              finished: C2.targetSchedule >= 0,
              totalText: C2.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C2.targetSchedule ? C2.targetSchedule : '',
              schedule: C2.targetSchedule
                ? math.divide(
                    math.multiply(
                      C2.targetSchedule || 0,
                      C2.targetProduction || C2.monplan
                    ),
                    100
                  )
                : '',
              avgDaily: C2.avgDailyProduction ? C2.avgDailyProduction : ''
            },
            {
              value: C3 ? C3.cumulativeOutput : 0,
              plan: C3 ? C3.targetProduction || C3.monplan : 0,
              finished: C3.targetSchedule >= 0,
              totalText: C3.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C3.targetSchedule ? C3.targetSchedule : '',
              schedule: C3.targetSchedule
                ? math.divide(
                    math.multiply(
                      C3.targetSchedule || 0,
                      C3.targetProduction || C3.monplan
                    ),
                    100
                  )
                : '',
              avgDaily: C3.avgDailyProduction ? C3.avgDailyProduction : ''
            }
          ],
          targetSchedule: math.add(
            math.multiply(
              C1.targetSchedule,
              C1.targetProduction || C1.monplan
            ) / 100,
            math.multiply(
              C2.targetSchedule,
              C2.targetProduction || C2.monplan
            ) / 100,
            math.multiply(
              C3.targetSchedule,
              C3.targetProduction || C3.monplan
            ) / 100
          ),
          output: math.add(
            C1.cumulativeOutput,
            C2.cumulativeOutput,
            C3.cumulativeOutput
          )
        })

        this.setTopData(
          '热轧总产量',
          math.add(
            C1.cumulativeOutput,
            C2.cumulativeOutput,
            C3.cumulativeOutput
          )
        )
        // 计算进度
        this.rollMonth.percent = math.multiply(
          math
            .divide(
              this.rollMonth.targetSchedule,
              math.add(
                C1.targetProduction || C1.monplan,
                C2.targetProduction || C2.monplan,
                C3.targetProduction || C3.monplan
              )
            )
            .toFixed(4),
          100
        )
      } catch (e) {}
    },
    async saveSteel() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        type: this.steelYesterday.editType,
        data: this.steelYesterday.showGridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(saveSteelOutput, params).then(res => {
        //
        if (res.status === 1) {
          this.getSteal()
        } else {
          this.$message.warning('保存失败！')
        }
        const params2 = {
          data: [
            {
              parameter: 'outputNotice',
              content: this.steelYesterday.noticeEdit,
              setDate: this.cDate
            }
          ]
        }
        post(saveBoardParameter, params2).then(res => {
          this.loading = false
          if (res.status === 1) {
            this.steelYesterday.dialogVisible = false
            this.$message.success('保存成功！')
          }
        })
      })
    },
    exportSteel() {
      const data = [
        {
          num: '序号',
          plt: '产线',
          unit: '单位',
          plan: '计划',
          reality: '实际',
          complete: '是否完成',
          unfinishedCause: '未完成原因',
          responsibleUnit: '责任单位',
          monplan: '月计划',
          targetProduction: '月目标',
          cumulativeOutput: '累计产量',
          mtcPfdTime: '已执行检修时间',
          rmMtTime: '剩余检修时间',
          targetSchedule: '超欠目标进度',
          avgDailyProduction: '日需均产'
        }
      ].concat(
        _.cloneDeep(this.steelYesterday.showGridData).map(item => {
          item.targetSchedule = item.targetSchedule + '%'
          delete item.type
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `钢产量详情（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    importSteelData(date) {
      post(findSteelOutputByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.steelYesterday.showGridData = res.data
          .filter(item => item.type === this.steelYesterday.editType)
          .map(item => {
            return {
              num: item.num,
              plt: item.plt,
              unit: item.unit,
              plan: item.plan,
              reality: item.reality,
              complete: item.complete,
              unfinishedCause: item.unfinishedcause,
              responsibleUnit: item.responsibleunit,
              monplan: item.monplan,
              targetProduction: item.targetproduction,
              cumulativeOutput: item.cumulativeoutput,
              mtcPfdTime: item.mtcpfdtime,
              rmMtTime: item.rmmttime,
              targetSchedule: Number(item.targetschedule || 0).toFixed(2),
              avgDailyProduction: item.avgdailyproduction,
              type: item.type
            }
          })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 数据管理
    clearShowGridData(name) {
      this[name].showGridData = []
    },
    addShowGridData(name) {
      this[name].showGridData.push({})
    },
    delShowGridData(index, name) {
      this[name].showGridData.splice(index, 1)
    },
    async getZZP() {
      const monthStart = moment(this.cDate).format('D') == 2
      // 参数
      const parameters = await post(findBoardParameterByDateAndPara, {
        setDate: this.searchTime.format('yyyy-MM')
      })
      this.stock1.blankMonthPlan = this.getParam(
        'blankMonthPlan',
        parameters.data
      )
      this.stock1.blankLastMonth = this.getParam(
        'blankLastMonth',
        parameters.data
      )
      // 坯料
      const zrStock = await post(findBlankStock, {
        date: this.searchTime.format('yyyyMM')
      })
      const zrStock2 = monthStart
        ? await post(findBlankStock, {
            date: this.$moment(this.cDate).format('yyyyMM')
          })
        : { data: [] }
      const stockZj = await post(findBlankStockPltZj, {
        date: this.searchTime.format('yyyyMM')
      })
      const stockZj2 = monthStart
        ? await post(findBlankStockPltZj, {
            date: this.$moment(this.cDate).format('yyyyMM')
          })
        : { data: [] }
      this.stock1.barX = zrStock.data
        .concat(zrStock2.data)
        .filter(item => item.PLT === 'C1')
        .map(item => item.PLCK_DATE.substr(6, 2))
      this.stock1.bar1 = [
        {
          name: '总计',
          data: zrStock.data
            .concat(zrStock2.data)
            .filter(item => item.PLT === 'C1')
            .map(item => {
              const match = stockZj.data
                .concat(stockZj2.data)
                .find(zj => zj.PLCK_DATE === item.PLCK_DATE)
              return match ? parseInt(match['总计']) : 0
            })
        },
        {
          name: 'C1',
          data: zrStock.data
            .concat(zrStock2.data)
            .filter(item => item.PLT === 'C1')
            .map(item => parseInt(item.ZJ))
        },
        {
          name: 'C2',
          data: zrStock.data
            .concat(zrStock2.data)
            .filter(item => item.PLT === 'C2')
            .map(item => parseInt(item.ZJ))
        },
        {
          name: 'C3',
          data: zrStock.data
            .concat(zrStock2.data)
            .filter(item => item.PLT === 'C3')
            .map(item => parseInt(item.ZJ))
        },
        {
          name: 'CAD',
          data: zrStock.data
            .concat(zrStock2.data)
            .filter(item => item.PLT === 'C1')
            .map(item => {
              const match = stockZj.data
                .concat(stockZj2.data)
                .find(zj => zj.PLCK_DATE === item.PLCK_DATE)
              return match ? parseInt(match['CAD']) : 0
            })
        },
        {
          name: 'CAC',
          data: zrStock.data
            .concat(zrStock2.data)
            .filter(item => item.PLT === 'C1')
            .map(item => {
              const match = stockZj.data
                .concat(stockZj2.data)
                .find(zj => zj.PLCK_DATE === item.PLCK_DATE)
              return match ? parseInt(match['CAC']) : 0
            })
        }
      ]
    },
    getHeat() {
      post(findHeatTreatmentYieldByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.heatMonth.gridData = res.data.map(item => {
          return {
            hearthnumber: item.hearthnumber,
            plan: item.plan,
            reality: item.reality,
            loadingdelaytime: item.loadingdelaytime,
            complete: item.complete,
            unfinishedcause: item.unfinishedcause,
            homeworkdays: item.homeworkdays,
            plannedmonthlyoutput: item.plannedmonthlyoutput,
            cumulativeoutput: item.cumulativeoutput,
            targetschedule: Number(item.targetschedule).toFixed(2),
            avgDailyProduction: item.avgdailyproduction
          }
        })
        if (!res.data.length) {
          this.heatMonth = {
            bar1: [],
            barX: [],
            gridData: [],
            failReason: '',
            output: 0,
            targetSchedule: 0,
            percent: 0
          }
          return
        }
        const merge = res.data.find(item => item.hearthnumber === '合计')
        const list = []
        const C1List = res.data.filter(item =>
          ['1#炉', '2#炉'].includes(item.hearthnumber)
        )
        list.push(this.handleHeat(C1List))
        const C2List = res.data.filter(item =>
          ['5#炉', '6#炉'].includes(item.hearthnumber)
        )
        list.push(this.handleHeat(C2List))
        const C3List = res.data.filter(item =>
          ['3#炉', '4#炉'].includes(item.hearthnumber)
        )
        list.push(this.handleHeat(C3List))

        Object.assign(this.heatMonth, {
          bar1: list,
          output: merge ? merge.cumulativeoutput : 0,
          targetSchedule: merge
            ? math
                .multiply(
                  Number(merge.plannedmonthlyoutput || 0),
                  Number(math.divide(merge.targetschedule, 100) || 0)
                )
                .toFixed(2)
            : 0,
          percent: merge ? Number(merge.targetschedule).toFixed(2) : 0
        })
        this.setTopData('热处理炉总产量', merge ? merge.cumulativeoutput : 0)
      })
    },
    // 处理数据合并
    handleHeat(list) {
      const obj = {
        value: _.sumBy(list, 'cumulativeoutput'),
        plan: _.sumBy(list, 'plannedmonthlyoutput'),
        unit: '吨'
      }
      // 超欠进度 （（累计产量/计划产量）-（当前天数/当月总天数））*100
      const days = this.getResentMonth(this.prevDate)
      const currentDays = this.$moment(days.endTime).diff(
        this.$moment(days.startTime).subtract(1, 'days'),
        'days'
      )
      const monthDays = this.$moment(days.endMonthTime).diff(
        this.$moment(days.startTime).subtract(1, 'days'),
        'days'
      )
      obj.targetSchedule =
        (obj.value / obj.plan - currentDays / monthDays).toFixed(4) * 100
      // console.log(days, currentDays, monthDays, obj)
      obj.finished = obj.targetschedule > 0
      obj.schedule = obj.targetschedule
        ? math.divide(math.multiply(obj.targetschedule || 0, obj.plan), 100)
        : ''
      return obj
    },
    // 设置顶部数据
    setTopData(name, num) {
      const index = this.kpiList.findIndex(item => item.name === name)
      index &&
        this.kpiList.splice(
          index,
          1,
          Object.assign({}, this.kpiList[index], { num })
        )

      if (name == '钢产量') {
        this.kpiList[0].num = num
      }
    },
    getOther() {
      post(findKeyIndic, {
        setDate: this.$moment(this.prevDate).format('YYYYMM'),
        time: this.cDate
      }).then(res => {
        this.other.bar1 = [
          {
            name: '合同兑现率',
            data: [
              Number(res['合同兑现率板卷厂'] * 100).toFixed(2),
              Number(res['合同兑现率宽厚板厂'] * 100).toFixed(2),
              Number(res['合同兑现率中板厂'] * 100).toFixed(2)
            ]
          }
        ]
        this.other.bar1Total = Number(res['合同兑现率事业部'] * 100).toFixed(2)
        this.setTopData('合同兑现率', this.other.bar1Total)
        this.other.bar2 = [
          {
            name: '综合非计划',
            data: [
              Number(res['综合非计划板卷厂'] * 100).toFixed(2),
              Number(res['综合非计划宽厚板厂'] * 100).toFixed(2),
              Number(res['综合非计划中板厂'] * 100).toFixed(2)
            ]
          }
        ]
        this.other.bar2Total = Number(res['综合非计划事业部'] * 100).toFixed(2)
        this.setTopData('综合非计划', this.other.bar2Total)
        this.other.bar3 = [
          {
            name: '热装热送率',
            data: [
              Number(res['热装热送率板卷厂'] * 100).toFixed(2),
              Number(res['热装热送率宽厚板厂'] * 100).toFixed(2),
              Number(res['热装热送率中板厂'] * 100).toFixed(2)
            ]
          }
        ]
        this.other.bar3Total = Number(res['热装热送率事业部'] * 100).toFixed(2)
        this.setTopData('热装热送率', this.other.bar3Total)
      })
    },
    getfactoryOrder() {
      post(findThreeFactoryOrderByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.factoryOrder.showGridData = res.data.map(item => {
          return {
            thFactoryOrder: item.thfactoryorder,
            waitSteelmaking: item.waitsteelmaking,
            waitSteelrolling: item.waitsteelrolling,
            postponeSteelmaking: item.postponesteelmaking,
            postponeSteelrolling: item.postponesteelrolling,
            unsoldOrder: item.unsoldorder,
            thFactoryOrdTotal: item.thfactoryordtotal,
            ordeNotch: item.ordenotch,
            vacuumSteelConfirm: item.vacuumsteelconfirm,
            commonSteelConfirm: item.commonsteelconfirm,
            confirmTotal: item.confirmtotal
          }
        })
        this.factoryOrder.gridData = lodash.cloneDeep(
          this.factoryOrder.showGridData
        )
      })
    },
    tableTotalClass(row) {
      // console.log(row)
      if (row.row.thFactoryOrder && row.row.thFactoryOrder.trim() === '总计') {
        return 'table-total'
      }
      return ''
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
      if (row.thFactoryOrder === '总计') {
        if (columnIndex === 1) {
          return [1, 2]
        } else if (columnIndex === 3) {
          return [1, 3]
        } else if (columnIndex === 6) {
          return [1, 1]
        } else if (columnIndex < 6 && columnIndex > 1) {
          return [0, 0]
        } else {
          return [1, 1]
        }
      }
      if (row.thFactoryOrder === '备注') {
        if (columnIndex === 1) {
          return [1, 10]
        }
        if (columnIndex < 11 && columnIndex > 1) {
          return [0, 0]
        } else {
          return [1, 1]
        }
      }
    },
    calculateHeight() {
      this.factoryOrder.maxHeight = this.$refs.table1.offsetHeight
    },
    // 在制品统计 （月度）
    getChartZzp(data) {
      try {
        const C1 = data.find(item => item.plt.trim() === 'C1')
        const C2 = data.find(item => item.plt.trim() === 'C2')
        const C3 = data.find(item => item.plt.trim() === 'C3')
        Object.assign(this.zzpMonth, {
          bar1: [
            {
              value: C1 ? C1.cumulativeOutput : 0,
              plan: C1 ? C1.targetProduction : 0,
              finished: C1.targetSchedule >= 0,
              totalText: C1.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C1.targetSchedule ? C1.targetSchedule : '',
              schedule: C1.targetSchedule
                ? math.divide(
                    math.multiply(C1.targetSchedule || 0, C1.targetProduction),
                    100
                  )
                : '',
              avgDaily: C1.avgDailyProduction ? C1.avgDailyProduction : ''
            },
            {
              value: C2 ? C2.cumulativeOutput : 0,
              plan: C2 ? C2.targetProduction : 0,
              finished: C2.targetSchedule >= 0,
              totalText: C2.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C2.targetSchedule ? C2.targetSchedule : '',
              schedule: C2.targetSchedule
                ? math.divide(
                    math.multiply(C2.targetSchedule || 0, C2.targetProduction),
                    100
                  )
                : '',
              avgDaily: C2.avgDailyProduction ? C2.avgDailyProduction : ''
            },
            {
              value: C3 ? C3.cumulativeOutput : 0,
              plan: C3 ? C3.targetProduction : 0,
              finished: C3.targetSchedule >= 0,
              totalText: C3.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C3.targetSchedule ? C3.targetSchedule : '',
              schedule: C3.targetSchedule
                ? math.divide(
                    math.multiply(C3.targetSchedule || 0, C3.targetProduction),
                    100
                  )
                : '',
              avgDaily: C3.avgDailyProduction ? C3.avgDailyProduction : ''
            }
          ],
          targetSchedule: math.add(
            math.multiply(C1.targetSchedule, C1.targetProduction) / 100,
            math.multiply(C2.targetSchedule, C2.targetProduction) / 100,
            math.multiply(C3.targetSchedule, C3.targetProduction) / 100
          ),
          output: math.add(
            C1.cumulativeOutput,
            C2.cumulativeOutput,
            C3.cumulativeOutput
          )
        })
        this.setTopData(
          '在制品',
          math
            .add(C1.cumulativeOutput, C2.cumulativeOutput, C3.cumulativeOutput)
            .toFixed(0)
        )
        // 计算进度
        this.rollMonth.percent = math.multiply(
          math
            .divide(
              this.rollMonth.targetSchedule,
              math.add(
                C1.targetProduction,
                C2.targetProduction,
                C3.targetProduction
              )
            )
            .toFixed(4),
          100
        )
      } catch (e) {}
    },
    // 宗判 （月度）
    getChartZp(data) {
      try {
        const C1 = data.find(item => item.plt.trim() === 'C1')
        const C2 = data.find(item => item.plt.trim() === 'C2')
        const C3 = data.find(item => item.plt.trim() === 'C3')
        Object.assign(this.zpMonth, {
          bar1: [
            {
              value: C1 ? C1.cumulativeOutput : 0,
              plan: C1 ? C1.targetProduction : 0,
              finished: C1.targetSchedule >= 0,
              totalText: C1.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C1.targetSchedule ? C1.targetSchedule : '',
              schedule: C1.targetSchedule
                ? math.divide(
                    math.multiply(C1.targetSchedule || 0, C1.targetProduction),
                    100
                  )
                : '',
              avgDaily: C1.avgDailyProduction ? C1.avgDailyProduction : ''
            },
            {
              value: C2 ? C2.cumulativeOutput : 0,
              plan: C2 ? C2.targetProduction : 0,
              finished: C2.targetSchedule >= 0,
              totalText: C2.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C2.targetSchedule ? C2.targetSchedule : '',
              schedule: C2.targetSchedule
                ? math.divide(
                    math.multiply(C2.targetSchedule || 0, C2.targetProduction),
                    100
                  )
                : '',
              avgDaily: C2.avgDailyProduction ? C2.avgDailyProduction : ''
            },
            {
              value: C3 ? C3.cumulativeOutput : 0,
              plan: C3 ? C3.targetProduction : 0,
              finished: C3.targetSchedule >= 0,
              totalText: C3.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C3.targetSchedule ? C3.targetSchedule : '',
              schedule: C3.targetSchedule
                ? math.divide(
                    math.multiply(C3.targetSchedule || 0, C3.targetProduction),
                    100
                  )
                : '',
              avgDaily: C3.avgDailyProduction ? C3.avgDailyProduction : ''
            }
          ],
          targetSchedule: math.add(
            math.multiply(C1.targetSchedule, C1.targetProduction) / 100,
            math.multiply(C2.targetSchedule, C2.targetProduction) / 100,
            math.multiply(C3.targetSchedule, C3.targetProduction) / 100
          ),
          output: math.add(
            C1.cumulativeOutput,
            C2.cumulativeOutput,
            C3.cumulativeOutput
          )
        })

        this.setTopData(
          '综判总产量',
          math
            .add(C1.cumulativeOutput, C2.cumulativeOutput, C3.cumulativeOutput)
            .toFixed(0)
        )
        // 计算进度
        this.rollMonth.percent = math.multiply(
          math
            .divide(
              this.rollMonth.targetSchedule,
              math.add(
                C1.targetProduction,
                C2.targetProduction,
                C3.targetProduction
              )
            )
            .toFixed(4),
          100
        )
      } catch (e) {}
    }
  }
}
</script>
  
  <style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 20px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .slick {
    height: 65px;
    position: relative;
    //top: -15px;
    margin-bottom: 15px;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    width: 90px;
    line-height: 24px;
    font-size: 16px;
    white-space: nowrap;
    color: #ffffff;
  }
  span:last-child {
    flex: 1;
    overflow: auto;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    font-size: 0;
    margin-right: 10px;
    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.green {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.scroll-wrapper {
  height: 100%;
}
.kpi-list {
  font-size: 0;
  .item {
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12.5%;
    img {
      width: 48px;
      margin-right: 10px;
    }
    .name {
      font-size: 18px;
      font-weight: 700;
      line-height: 18px;
      margin-bottom: 10px;
      letter-spacing: 0px;
      text-align: left;
    }
    .num {
      font-size: 32px;
      font-weight: 700;
      line-height: 32px;
      letter-spacing: 0px;
      text-align: left;
    }
    .unit {
      font-size: 20px;
      font-weight: 350;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
    }
  }
}
</style>
