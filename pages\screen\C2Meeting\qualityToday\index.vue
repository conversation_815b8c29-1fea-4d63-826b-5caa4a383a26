<template>
  <div class="content">
    <div class="content-item">
      <screen-border-multi>
        <template v-slot:title>
          <div class="tabs-class">
            <div
              v-for="(item) in tabList"
              :key="item.id"
              :class="{'tab-pane-active': active === item.id}"
              class="tab-pane"
              @click="active = item.id">
              <div class="tab-pane-title-class">
                <div>{{ item.title }}</div>
                <div
                  v-if="active === item.id"
                  class="tab-pane-img">
                  <img
                    class="tab-pane-img2"
                    src="@/assets/images/screen/tab-pane-active-line2.png"
                    alt="">
                  <img
                    class="tab-pane-img1"
                    src="@/assets/images/screen/tab-pane-active-line.png"
                    alt="">
                </div>
              </div>
            </div>
          </div>
        </template>
        <custom-table-noheader7
          v-if="active === '1'"
          :title="'实物累计待判'"
          :key="'productYes1'"
          :setting="tableObj1.setting"
          :url-list="tableObj1.url.list"
          :url-save="tableObj1.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader3
          v-if="active === '2'"
          :key="'productYes2'"
          :title="'主线判定非计划'"
          :setting="tableObj2.setting"
          :url-list="tableObj2.url.list"
          :url-save="tableObj2.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '3'"
          :key="'productYes3'"
          :title="'离线判定非计划'"
          :setting="tableObj3.setting"
          :url-list="tableObj3.url.list"
          :url-save="tableObj3.url.save"
          :select-date="selectDate"/>
        <!-- <custom-table-noheader8
          v-if="active === '4'"
          :key="'productYes4'"
          :title="'原始非计划'"
          :setting="tableObj4.setting"
          :url-list="tableObj4.url.list"
          :url-save="tableObj4.url.save"
          :select-date="selectDate"/> -->
        <custom-table-noheader2
          v-if="active === '5'"
          :key="'productYes5'"
          :title="'探伤合格率'"
          :setting="tableObj5.setting"
          :url-list="tableObj5.url.list"
          :url-save="tableObj5.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '6'"
          :title="'材判废'"
          :key="'productYes6'"
          :setting="tableObj6.setting"
          :url-list="tableObj6.url.list"
          :url-save="tableObj6.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader21
          v-if="active === '9'"
          :title="'坯判废'"
          :key="'productYes9'"
          :setting="tableObj9.setting"
          :url-list="tableObj9.url.list"
          :url-save="tableObj9.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '7'"
          :title="'重点钢种改判'"
          :key="'productYes7'"
          :setting="tableObj7.setting"
          :url-list="tableObj7.url.list"
          :url-save="tableObj7.url.save"
          :select-date="selectDate"/>
        <quality-typical-photos
          v-if="active === '8'"
          :title="'质量典型照片'"
          :key="'productYes8'"
          :select-date="selectDate"/>
        <template
          v-if="active === '7'">
          <el-table
            v-loading="loading"
            :data="deviceSetting.dataList"
            :size="'medium'"
            class="center-table font-big-table"
            border>
            <template
              v-for="(item, index) in deviceSetting.setting">
              <template v-if="item.show !== false">
                <el-table-column
                  v-if="item.children"
                  :key="index"
                  :width="item.width || ''"
                  :property="item.keySave"
                  :label="item.label"
                  :align="item.align">
                  <template
                    v-for="(cItem, cIndex) in item.children">
                    <template v-if="item.inputType === 'textarea'">
                      <el-table-column
                        :key="cIndex"
                        :width="cItem.width || ''"
                        :property="cItem.keySave"
                        :label="cItem.label"
                        :align="cItem.align">
                        <template v-slot="{ row }">
                          <div
                            slot="content"
                            v-html="formatText(row[cItem.keySave], cItem.split)"
                          />
                        </template>
                      </el-table-column>
                    </template>
                    <template v-else>
                      <el-table-column
                        :key="cIndex"
                        :width="cItem.width || ''"
                        :property="cItem.keySave"
                        :label="cItem.label"
                        :align="cItem.align"/>
                    </template>
                  </template>
                </el-table-column>
                <template v-else>
                  <el-table-column
                    v-if="item.type === 'index'"
                    :key="index"
                    :label="item.label"
                    type="index"
                    width="100"
                  />
                  <template v-else>
                    <template v-if="item.inputType === 'textarea'">
                      <el-table-column
                        :key="index"
                        :width="item.width || ''"
                        :property="item.keySave"
                        :label="item.label"
                        :align="item.align">
                        <template v-slot="{ row }">
                          <div
                            slot="content"
                            v-html="formatText(row[item.keySave], item.split)"
                          />
                        </template>
                      </el-table-column>
                    </template>
                    <template v-else>
                      <el-table-column
                        :key="index"
                        :width="item.width || ''"
                        :property="item.keySave"
                        :label="item.label"
                        :align="item.align"/>
                    </template>
                  </template>
                </template>
              </template>
            </template>
          </el-table>
          <br>
          <el-table
            v-loading="loading"
            :data="deviceSetting.dataList2"
            :size="'medium'"
            class="center-table font-big-table"
            border>
            <template
              v-for="(item, index) in deviceSetting.setting2">
              <template v-if="item.show !== false">
                <el-table-column
                  v-if="item.children"
                  :key="index"
                  :width="item.width || ''"
                  :property="item.keySave"
                  :label="item.label"
                  :align="item.align">
                  <template
                    v-for="(cItem, cIndex) in item.children">
                    <template v-if="item.inputType === 'textarea'">
                      <el-table-column
                        :key="cIndex"
                        :width="cItem.width || ''"
                        :property="cItem.keySave"
                        :label="cItem.label"
                        :align="cItem.align">
                        <template v-slot="{ row }">
                          <div
                            slot="content"
                            v-html="formatText(row[cItem.keySave], cItem.split)"
                          />
                        </template>
                      </el-table-column>
                    </template>
                    <template v-else>
                      <el-table-column
                        :key="cIndex"
                        :width="cItem.width || ''"
                        :property="cItem.keySave"
                        :label="cItem.label"
                        :align="cItem.align"/>
                    </template>
                  </template>
                </el-table-column>
                <template v-else>
                  <el-table-column
                    v-if="item.type === 'index'"
                    :key="index"
                    :label="item.label"
                    type="index"
                    width="100"
                  />
                  <template v-else>
                    <template v-if="item.inputType === 'textarea'">
                      <el-table-column
                        :key="index"
                        :width="item.width || ''"
                        :property="item.keySave"
                        :label="item.label"
                        :align="item.align">
                        <template v-slot="{ row }">
                          <div
                            slot="content"
                            v-html="formatText(row[item.keySave], item.split)"
                          />
                        </template>
                      </el-table-column>
                    </template>
                    <template v-else>
                      <el-table-column
                        :key="index"
                        :width="item.width || ''"
                        :property="item.keySave"
                        :label="item.label"
                        :align="item.align"/>
                    </template>
                  </template>
                </template>
              </template>
            </template>
          </el-table>
        </template>
      </screen-border-multi>
    </div>
  </div>
</template>

 <script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import {
  qmsQualitySupervisionQuery,
  qmsQualitySupervisionSave
} from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/C2Meeting/component/custom-table'
import {
  cutUnplannedRateFind,
  cutUnplannedRateSave,
  findEquipmentOperation,
  finishingShearingFind,
  finishingShearingSave,
  hotRollingSituationFind,
  hotRollingSituationSave,
  productionSituationDayFind,
  productionSituationDaySave,
  PSCDayFind,
  PSCDaySave,
  findInspectionPassRateByDateAndPlt,
  findAllBySetDate,
  saveAll,
  mainLineFindAllBySetDate,
  mainLinesaveAll,
  QualityJudgeDayFindAllBySetDate,
  QualityJudgeDaysaveAll,
  findBlankWastesAllByDate,
  keySteelChangeFindAllBySetDate,
  keySteelChangesaveAll,
  QualityOriginalNonPlanFindAllBySetDate,
  QualityOriginalNonPlansaveAll
} from '@/api/screenC2'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import CustomTableNoheader from '@/pages/screen/C2Meeting/component/custom-table-noheader'
import CustomTableNoheader2 from '@/pages/screen/C2Meeting/component/custom-table-noheader2'
import CustomTableNoheader3 from '@/pages/screen/C2Meeting/component/custom-table-noheader3'
import CustomTableNoheader7 from '@/pages/screen/C2Meeting/component/custom-table-noheader7'
import CustomTableNoheader8 from '@/pages/screen/C2Meeting/component/custom-table-noheader8'
import QualityTypicalPhotos from '@/pages/screen/C2Meeting/component/quality-typical-photos'
import CustomTableNoheader21 from '@/pages/screen/C2Meeting/component/custom-table-noheader21'
import { post } from '@/lib/Util'
import { expenseDetail } from '@/api/device'
import { forEach } from 'mathjs'
export default {
  name: 'productYest',
  components: {
    CustomTableNoheader,
    CustomTableNoheader21,
    CustomTableNoheader2,
    CustomTableNoheader3,
    CustomTableNoheader7,
    CustomTableNoheader8,
    ScreenBorderMulti,
    CustomTable,
    SingleBarsChart,
    QualityTypicalPhotos
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      active: '1',
      loading: false,
      tabList: [
        {
          id: '1',
          active: true,
          title: '实物累计待判'
        },
        {
          id: '2',
          active: false,
          title: '判定非计划'
        },
        //   {
        //     id: '3',
        //     active: false,
        //     title: '离线判定非计划'
        //   },
        // {
        //   id: '4',
        //   active: false,
        //   title: '原始非计划'
        // },
        {
          id: '5',
          active: false,
          title: '探伤合格率'
        },
        {
          id: '6',
          active: false,
          title: '材判废'
        },
        {
          id: '9',
          active: false,
          title: '坯判废'
        },
        {
          id: '7',
          active: false,
          title: '重点钢种改判'
        },
        {
          id: '8',
          active: false,
          title: '质量典型照片'
        }
      ],
      tableObj1: {
        url: {
          save: saveAll,
          list: findAllBySetDate
        },
        setting: [
          //  {
          //    keyQuery: 'index',
          //    keySave: 'index',
          //    label: '序号',
          //    width: '60'
          //  },
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'name',
            keySave: 'name',
            label: '缺陷名称',
            width: '130'
          },
          {
            keyQuery: 'weight',
            keySave: 'weight',
            label: '吨位',
            width: '280'
          },
          {
            keyQuery: 'grade',
            keySave: 'grade',
            label: '钢种'
          },
          {
            keyQuery: 'specifications',
            keySave: 'specifications',
            label: '规格'
          },
          {
            keyQuery: 'content',
            keySave: 'content',
            label: '生产原因'
          },
          {
            keyQuery: 'prediction',
            keySave: 'prediction',
            label: '预判'
          },
          {
            keyQuery: 'setDate',
            keySave: 'setDate',
            label: '晨会日期'
          }
        ]
      },
      tableObj2: {
        url: {
          save: mainLinesaveAll,
          list: mainLineFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'company',
            keySave: 'company',
            label: '责任单位'
          },
          {
            keyQuery: 'rate',
            keySave: 'rate',
            label: '非计划指标'
          },
          {
            keyQuery: 'todayRate',
            keySave: 'todayRate',
            label: '当日指标'
          },
          //  {
          //    keyQuery: 'totalWeight',
          //    keySave: 'totalWeight',
          //    label: '总重量'
          //  },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '缺陷'
          },
          {
            keyQuery: 'weight',
            keySave: 'weight',
            type: 'textarea',
            label: '重量'
          },
          {
            keyQuery: 'grade',
            keySave: 'grade',
            type: 'textarea',
            label: '钢种'
          },
          {
            keyQuery: 'specifications',
            keySave: 'specifications',
            type: 'textarea',
            label: '厚度'
          }
        ]
      },
      tableObj3: {
        url: {
          save: cutUnplannedRateSave,
          list: cutUnplannedRateFind
        },
        setting: [
          {
            keyQuery: 'project',
            keySave: 'project',
            label: '项目'
          },
          {
            keyQuery: 'situationday',
            keySave: 'situationDay',
            label: '当日情况'
          },
          {
            keyQuery: 'accumulatemonth',
            keySave: 'accumulateMonth',
            label: '本月累计'
          }
        ]
      },
      tableObj4: {
        url: {
          save: QualityOriginalNonPlansaveAll,
          list: QualityOriginalNonPlanFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'REASON',
            keySave: 'REASON',
            label: '余材原因'
          },
          {
            keyQuery: 'RESPONSIBLE_DEPARTMENT',
            keySave: 'RESPONSIBLE_DEPARTMENT',
            label: '责任单位'
          },
          {
            keyQuery: 'KS',
            keySave: 'KS',
            label: '块数'
          },
          {
            keyQuery: 'WEIGHT_TON',
            keySave: 'WEIGHT_TON',
            label: '吨位'
          }
        ]
      },
      tableObj5: {
        url: {
          save: PSCDaySave,
          list: findInspectionPassRateByDateAndPlt
        },
        setting: [
          {
            keyQuery: 'targetValue',
            keySave: 'targetValue',
            label: '目标(%)'
          },
          {
            keyQuery: 'value',
            keySave: 'value',
            label: '当日合格率(%)'
          },
          {
            keyQuery: 'monthData',
            keySave: 'monthData',
            label: '当月合格率(%)'
          },
          {
            keyQuery: 'setDate',
            keySave: 'setDate',
            label: '日期'
          }
        ]
      },
      tableObj6: {
        url: {
          save: QualityJudgeDaysaveAll,
          list: QualityJudgeDayFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'plateno',
            keySave: 'plateno',
            label: '钢板号'
          },
          {
            keyQuery: 'standard',
            keySave: 'standard',
            label: '轧制标准',
            width: '220'
          },
          {
            keyQuery: 'thk',
            keySave: 'thk',
            label: '厚度'
          },
          {
            keyQuery: 'wid',
            keySave: 'wid',
            label: '宽度'
          },
          {
            keyQuery: 'len',
            keySave: 'len',
            label: '长度'
          },
          {
            keyQuery: 'wgt',
            keySave: 'wgt',
            label: '重量'
          },
          {
            keyQuery: 'prodgrd',
            keySave: 'prodgrd',
            label: '表面等级'
          },
          {
            keyQuery: 'flaw',
            keySave: 'flaw',
            label: '改判缺陷'
          },
          {
            keyQuery: 'company',
            keySave: 'company',
            label: '改判缺陷责任单位'
          }
        ]
      },
      tableObj9: {
        url: {
          save: '',
          list: findBlankWastesAllByDate
        },
        setting: [
          {
            keyQuery: 'scrap_date',
            keySave: 'scrap_date',
            label: '发生时间'
          },
          {
            keyQuery: 'mat_no',
            keySave: 'mat_no',
            label: '废钢号'
          },
          {
            keyQuery: 'scrap_wgt',
            keySave: 'scrap_wgt',
            label: '废钢重量'
          },
          {
            keyQuery: 'cd_short_name',
            keySave: 'cd_short_name',
            label: '原因'
          },
          {
            keyQuery: 'cd_name',
            keySave: 'cd_name',
            label: '原因描述'
          },
          {
            keyQuery: 'thk',
            keySave: 'thk',
            label: '板坯厚度'
          },
          {
            keyQuery: 'wid',
            keySave: 'wid',
            label: '板坯宽度'
          },
          {
            keyQuery: 'len',
            keySave: 'len',
            label: '板坯长度'
          },
          {
            keyQuery: 'wgt',
            keySave: 'wgt',
            label: '重量'
          },
          {
            keyQuery: 'steel_grd_detail',
            keySave: 'steel_grd_detail',
            label: '板坯钢种'
          }
        ]
      },
      tableObj7: {
        url: {
          save: keySteelChangesaveAll,
          list: keySteelChangeFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'grade',
            keySave: 'grade',
            label: '钢种'
          },
          {
            keyQuery: 'type',
            keySave: 'type',
            label: '产量'
          },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '改判缺陷'
          },
          {
            keyQuery: 'company',
            keySave: 'company',
            label: '责任单位'
          },
          {
            keyQuery: 'weight',
            keySave: 'weight',
            label: '改判吨位'
          },
          {
            keyQuery: 'setDate',
            keySave: 'setDate',
            label: '晨会日期'
          }
        ]
      },
      deviceSetting: {
        url: {
          list: keySteelChangeFindAllBySetDate
        },
        dataList: [],
        dataList2: [],
        setting: [
          {
            keyQuery: 'grade',
            keySave: 'grade',
            label: '钢种'
          },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '改判缺陷'
          },
          {
            keyQuery: 'company',
            keySave: 'company',
            label: '责任单位'
          },
          {
            keyQuery: 'weight',
            keySave: 'weight',
            label: '吨位'
          },
          //  {
          //    keyQuery: 'type',
          //    keySave: 'type',
          //    label: '1.产量 2.改判量'
          //  },
          {
            keyQuery: 'setDate',
            keySave: 'setDate',
            label: '晨会日期'
          }
        ],
        setting2: [
          {
            keyQuery: 'grade',
            keySave: 'grade',
            label: '钢种'
          },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '产量'
          },
          {
            keyQuery: 'weighe',
            keySave: 'weight',
            label: '改判量'
          },
          {
            keyQuery: 'weight',
            keySave: 'weight',
            label: '改判占比'
          },
          //  {
          //    keyQuery: 'type',
          //    keySave: 'type',
          //    label: '1.产量 2.改判量'
          //  },
          {
            keyQuery: 'setDate',
            keySave: 'setDate',
            label: '晨会日期'
          }
        ]
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.loadData()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.loadData()
  },
  methods: {
    loadData() {
      post(this.deviceSetting.url.list, {
        setDate: this.$moment(this.cDate).format('YYYY-MM-DD')
        //   endTime: this.$moment(this.cDate)
        //     .subtract(1, 'day')
        //     .format('YYYYMMDD')
      }).then(res => {
        //    res.data.forEach()
        //   this.deviceSetting.dataList = res.data.map(item => {
        //     item.type
        //   })

        //   for (const item in res.data) {
        //     console.log('aa', item)
        //     if (item.type == 1) {
        //       this.dataList.push(item)
        //     } else if (item.type == 2) {
        //       this.dataList2.push(item)
        //     }
        //   }
        var groupedData = {}
        // 遍历数据数组
        let adata = []
        let bdata = []
        res.data.forEach(function(item) {
          if (item.type == 1) {
            adata.push(item)
          }
          if (item.type == 2) {
            bdata.push(item)
          }
          //  var category = item.type

          //  // 如果分组对象中不存在该类别，则创建一个新的数组存储该类别下的项
          //  if (!groupedData[category]) {
          //    groupedData[category] = []
          //  }

          //  // 将项添加到相应的类别数组中
          //  groupedData[category].push(item)
        })
        this.deviceSetting.dataList = adata
        this.deviceSetting.dataList2 = bdata
        console.log('adata', adata)
        console.log('bb', bdata)
      })
    }
  }
}
</script>

 <style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
