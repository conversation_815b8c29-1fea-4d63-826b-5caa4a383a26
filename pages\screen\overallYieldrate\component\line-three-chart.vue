<template>
  <div
    :id="containerId"
    :style="{ height: typeof height === 'number' ? `${height}px` : '100%' }"/>
</template>

<script>
export default {
  name: 'line-chart',
  props: {
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    monthAvgData: {
      type: Number,
      default: 0
    },
    historyBestData: {
      type: Number,
      default: 0
    },
    lastMonthData: {
      type: String,
      default: ''
    },
    monthPlanData: {
      type: String,
      default: ''
    },
    chartData2: {
      type: Array,
      default: () => {
        return []
      }
    },
    chartData3: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return [
          '#1e90ff',
          '#f7c900',
          '#3DB842',
          '#51DF81',
          '#2772F0',
          '#35cff1'
        ]
      }
    },
    showLegend: {
      type: Boolean,
      default: false
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 46
    },
    unit: {
      type: String,
      default: '%'
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    },
    monthAvgData: {
      handler: function() {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    },
    historyBestData: {
      handler: function() {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    },
    xData: {
      handler: function() {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
        window.addEventListener('resize', this.resizeChart)
      }

      // 格式化X轴显示，如果是YYYYMM格式则转为YYYY-MM格式
      const formattedXData = this.xData.map(item => {
        if (item && item.length === 6) {
          const year = item.substring(0, 4)
          const month = item.substring(4, 6)
          return `${year}-${month}`
        }
        return item
      })

      // 图表配置项
      const options = {
        tooltip: {
          confine: true,
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#57617B'
            }
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            params.forEach(item => {
              // 为不同系列设置不同的点颜色
              let dotColor = item.color
              // 柱状图系列使用固定颜色而非渐变色
              if (item.seriesType === 'bar') {
                dotColor = '#1ad6ff' // 使用与柱状图顶部颜色相近的固定色
              }

              result += `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${dotColor};"></span>`

              // 检查值是否为null或undefined，如果是则显示"-"
              const valueText =
                item.value === null || item.value === undefined
                  ? '-'
                  : `${item.value}%`
              result += `${item.seriesName}: ${valueText}<br/>`
            })
            return result
          }
        },
        color: this.color,
        legend: {
          show: this.showLegend,
          data: ['月收得率', '历史平均', '历史最好'],
          right: 10,
          top: 0,
          itemHeight: 8,
          itemWidth: 25,
          itemGap: 20,
          textStyle: {
            color: '#fff',
            fontSize: 12
          }
        },
        grid: {
          top: '15%',
          left: '3%',
          right: '3%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true,
            axisTick: { show: false },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              rotate: this.labelRotate
            },
            data: formattedXData
          }
        ],
        yAxis: [
          {
            name: this.unit,
            min: function(value) {
              return value.min - 10
            },
            nameTextStyle: {
              color: '#ddd',
              align: 'right'
            },
            type: 'value',
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#57617B'
              }
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              formatter: function(value) {
                return value.toFixed(0) // 将数值格式化为保留2位小数
              }
            },
            splitLine: {
              lineStyle: {
                color: '#2E4262'
              }
            }
          }
        ],
        series: [
          // 月收得率曲线
          ...this.chartData.map(item => {
            return {
              name: item.name,
              type: 'bar',
              barWidth: this.barWidth,
              label: {
                show: true,
                color: '#fff',
                position: 'top',
                fontSize: 12
              },
              itemStyle: {
                normal: {
                  // 添加渐变效果
                  color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#1ad6ff' }, // 顶部颜色
                    { offset: 1, color: '#1e90ff' } // 底部颜色
                  ]),
                  // 添加边框和圆角
                  borderRadius: [4, 4, 0, 0],
                  borderColor: '#1fc6ff',
                  borderWidth: 1,
                  // 添加阴影效果
                  shadowColor: 'rgba(31, 198, 255, 0.5)',
                  shadowBlur: 5
                },
                // 鼠标悬停效果
                emphasis: {
                  color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#5edeff' },
                    { offset: 1, color: '#59abff' }
                  ])
                }
              },
              // 保持原有配置
              data: item.data
            }
          }),
          // 历史平均标线
          {
            name: '历史平均',
            type: 'line',
            symbol: 'none',
            itemStyle: {
              normal: {
                color: '#F7C900',
                width: 1
              }
            },
            lineStyle: {
              type: 'dashed',
              width: 1
            },
            markLine: {
              symbol: ['none', 'none'],
              label: {
                normal: {
                  show: false
                }
              },
              lineStyle: {
                type: 'dashed',
                color: '#F7C900'
              },
              data: [
                {
                  yAxis: this.monthAvgData,
                  name: '历史平均'
                }
              ]
            },
            data: new Array(this.xData.length).fill(this.monthAvgData)
          },
          // 历史最好标线
          {
            name: '历史最好',
            type: 'line',
            symbol: 'none',
            itemStyle: {
              normal: {
                color: '#3DB842',
                width: 1
              }
            },
            lineStyle: {
              type: 'dashed',
              width: 1
            },
            markLine: {
              symbol: ['none', 'none'],
              label: {
                normal: {
                  show: false
                }
              },
              lineStyle: {
                type: 'dashed',
                color: '#3DB842'
              },
              data: [
                {
                  yAxis: this.historyBestData,
                  name: '历史最好'
                }
              ]
            },
            data: new Array(this.xData.length).fill(this.historyBestData)
          }
        ]
      }

      this.myChart.setOption(options)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
