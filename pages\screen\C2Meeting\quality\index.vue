<template>
  <div class="content">
    <div class="content-item">
      <screen-border :title="'原钢种工序一次合格率'">
        <template v-slot:headerRight>
          <span
            v-command="'/screen/C2Meeting/coordinate'"
            class="screen-btn"
            @click="pilotPlan1.dialogDefect = true">
            缺陷维护
          </span>
          <span
            v-command="'/screen/C2Meeting/coordinate'"
            class="screen-btn"
            @click="pilotPlan1.dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            操作
          </span>
        </template>
        <div 
          ref="table1" 
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            ref="tableShow"
            :data="pilotPlan1.showGridData"
            :span-method="handleObjectSpan"
            :max-height="pilotPlan1.maxHeight"
            :row-class-name="tableRowClassName"
            :size="'medium'"
            class="center-table font-big-table"
            border>
            <el-table-column
              property="defect"
              label="缺陷"
              width="260"/>
            <el-table-column
              :formatter="formatWeight"
              property="weight"
              label="重量"
              width="200"/>
            <el-table-column
              property="millStdspec"
              width="600"
              label="主要钢种"/>
            <el-table-column
              property="specifications"
              label="规格"
              width="240"/>
            <el-table-column
              property="rescuingOutcome"
              label="挽救结果"/>
            <el-table-column
              :label="'备注'"
              property="remark"/>
          </el-table>
          <!-- <div 
            style="font-size: 20px; margin-top: 20px">
            {{ notes }}
          </div> -->
          <div class="remark wgt">
            <div class="wgt_total">合计：</div>
            <el-input 
              v-model="editedwgt" 
              :rows="5" 
              :autosize="{ minRows: 1, maxRows: 3 }"
              :disabled="!editing"
              style="width: 110px;"
              class="custom-input"
              type="textarea"/>
            <div class="wgt_unit">吨</div>
            <span
              v-if="!editing"
              class="screen-btn" 
              @click="startEditing">操作
            </span>
            <span
              v-if="editing" 
              class="screen-btn"
              @click="saveRemark">保存
            </span>
          </div>
          <div class="remark">
            <el-input 
              v-model="editedRemark" 
              :rows="5" 
              :disabled="!editing"
              :autosize="{ minRows: 1, maxRows: 3 }"
              class="custom-input"
              type="textarea"/>
            
          </div>
        </div>
      </screen-border>
    </div>
    <!--操作弹窗-->
    <el-dialog
      :visible.sync="pilotPlan1.dialogVisible"
      :width="'95%'"
      :top="'50px'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="$refs.tableEdit.clearFilter()">
              清除筛选
            </span> -->
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM'"
              type="month"
              @change="changeDate"/>
              <!-- <template>
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
            </template> -->
              <!-- <span
              class="screen-btn"
              @click="exportpilotPlan">
              导出
            </span> -->
          </div>
          原钢种工序一次合格率
        </div>
      </template>
      <el-form>
        <el-table
          v-loading="loading"
          ref="tableEdit"
          :data="pilotPlan1.showGridData"
          :max-height="tableMaxHeight"
          class="center-table"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60"/>
          <el-table-column
            property="defect"
            label="缺陷">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.defect"
                :rows="4"
                type="textarea"/>
              <template v-else>
                {{ row.defect }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="weight"
            label="重量">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.weight"/>
              <template v-else>
                {{ row.weight }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="millStdspec"
            label="主要钢种"
            width="450">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.millStdspec"/>
              <template v-else>
                {{ row.millStdspec }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            property="specifications"
            label="规格"
            width="120">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.specifications"/>
              <template v-else>
                {{ row.specifications }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            :label="'挽救结果'"
            property="rescuingOutcome">
            <template v-slot="{ row, $index }">
              <el-radio-group 
                v-if="$index === editIndex" 
                v-model="row.rescuingOutcome">
                <el-radio label="合格">合格</el-radio>
                <el-radio label="不合格">不合格</el-radio>
              </el-radio-group>
              <template v-else>
                {{ row.rescuingOutcome }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'备注'"
            property="remark">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.remark"/>
              <template v-else>
                {{ row.remark }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'操作'"
            fixed="right"
            property="proofResult">
            <template v-slot="{ row, $index}">
              <!-- 部分编辑-->
              <el-button
                v-if="$index !== editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="editItem(pilotPlan1.gridData, $index, 1)">编辑</el-button>
              <el-button
                v-if="$index === editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="savepilotPlanItem(row)">保存</el-button>
              <el-button
                class="screen-btn edit-btn"
                type="text"
                @click="deleteItem(row, $index)">删除</el-button>
            </template>
          </el-table-column>

        </el-table>
      </el-form>
      <div class="text-center">
        <span
          class="screen-btn"
          style="margin-top: 10px"
          @click="addGridData('pilotPlan1');editIndex = pilotPlan1.gridData.length - 1">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--缺陷维护弹窗-->
    <el-dialog
      :visible.sync="pilotPlan1.dialogDefect"
      :width="'95%'"
      :top="'50px'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div 
          class="custom-dialog-title" 
          style="display:flex;justify-content: space-between;">
          <el-form :model="searchInfo">
            <el-row style="display:flex;">
              <el-form-item >
                大类：
                <el-input
                  v-model="searchInfo.category"
                  placeholder="请输入内容"
                  class="contentInput"
                />
              </el-form-item>
              <el-form-item >
                缺陷：
                <el-input
                  v-model="searchInfo.defect"
                  placeholder="请输入内容"
                  class="contentInput"
                />
              </el-form-item>
              <el-form-item >
                优先级：
                <el-input
                  v-model.number="searchInfo.priority"
                  placeholder="请输入内容"
                  class="contentInput"
                />
              </el-form-item>
              <el-form-item >
                <el-button
                  round
                  type="primary"
                  size="small"
                  icon="el-icon-search"
                  class="margin-left"
                  @click="handleSearchClick"
                >查询
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="$refs.tableEdit.clearFilter()">
              清除筛选
            </span> -->
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM'"
              type="month"
              @change="changeDate"/>
              <!-- <template>
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
            </template> -->
              <!-- <span
              class="screen-btn"
              @click="exportpilotPlan">
              导出
            </span> -->
          </div>
        </div>
      </template>
      <el-form>
        <el-table
          v-loading="loading"
          ref="tableEdit"
          :data="pilotPlan1.DefectData"
          :max-height="tableMaxHeight"
          class="center-table"
          border>
          <!-- <el-table-column
            type="index"
            label="序号"
            width="60"/> -->
          <el-table-column
            property="category"
            label="大类">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editDefect"
                v-model="row.category"
                :rows="4"
                type="textarea"/>
              <template v-else>
                {{ row.category }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="defect"
            label="缺陷">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editDefect"
                v-model="row.defect"/>
              <template v-else>
                {{ row.defect }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="priority"
            label="优先级"
            width="450">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editDefect"
                v-model.number="row.priority"/>
              <template v-else>
                {{ row.priority }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'操作'"
            fixed="right"
            property="proofResult">
            <template v-slot="{ row, $index}">
              <!-- 部分编辑-->
              <el-button
                v-if="$index !== editDefect"
                class="screen-btn edit-btn"
                type="text"
                @click="editItems(pilotPlan1.gridData, $index, 1)">编辑</el-button>
              <el-button
                v-if="$index === editDefect"
                class="screen-btn edit-btn"
                type="text"
                @click="saveDefect(row)">保存</el-button>
              <el-button
                class="screen-btn edit-btn"
                type="text"
                @click="deleteDefect(row, $index)">删除</el-button>
            </template>
          </el-table-column>

        </el-table>
      </el-form>
      <div class="text-center">
        <span
          class="screen-btn"
          style="margin-top: 10px"
          @click="addDecfectData('pilotPlan1');editDefect = pilotPlan1.gridData.length - 1">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
      <el-pagination
        :current-page="page.page"
        :page-size="page.size"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM'"
            type="month"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { qmsQualitySystemSaveNew, qmsQualitySystem } from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  checklistBySetDate,
  checklistDelete,
  checklistSave,
  findCountDeptBySetDate,
  findCountPltBySetDate,
  findDpPltBySetDate,
  progressReportingDeleteAllById,
  progressReportingFindAllBySetDate,
  progressReportingSave
} from '@/api/screenTechnolagy'
import moment from 'moment'
import { math } from '@/lib/Math'
import TextDisplay from '@/pages/screen/technologyMeeting/component/text-display'
import { findOneUserByUserNo } from '@/api/system'
import { mapState } from 'vuex'
import {
  qualityDel,
  qualityFind,
  qualitySave,
  defrctFind,
  defrctSave,
  defrctDel
} from '@/api/screenC2'

export default {
  name: 'coordinateB1',
  components: { TextDisplay, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      editIndex: null,
      editDefect: null,
      editPartIndex: null, //部分编辑
      tableMaxHeight: null,
      isEditing: false,
      searchInfo: {
        category: '',
        defect: '',
        priority: ''
      },
      wgt: [],
      editedwgt: '', // 用于编辑的备注
      remark: [],
      editedRemark: '', // 用于编辑的备注
      editing: false, // 是否正在编辑
      page: {
        page: 1,
        size: 10,
        total: 0
      },
      pilotPlan1: {
        gridData: [],
        DefectData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false, //操作弹窗
        dialogDefect: false, //缺陷维护弹窗
        maxHeight: null
      },
      pivotTable: {
        table1: [],
        table2: [],
        table3: [],
        dialogVisible: false,
        maxHeight: null
      },
      pltList: [
        { value: '第一炼钢厂', text: '第一炼钢厂' },
        { value: '宽厚板厂', text: '宽厚板厂' },
        { value: '中厚板卷厂', text: '中厚板卷厂' },
        { value: '中板厂', text: '中板厂' }
      ],
      departmentList: [
        '工艺研究室',
        '调质钢研发室',
        '结构船板研发室',
        '低温容器研发室',
        '能源用钢研发室'
      ],
      varietyList: ['工艺抽查', '工装备件'],
      conclusionList: ['符合', '不符合'],
      factoryList: [
        { code: 'X73', name: '第一炼钢厂' },
        { code: 'X38', name: '宽厚板厂' },
        { code: 'X32', name: '中厚板卷厂' },
        { code: 'X66', name: '中板厂' }
      ],
      isFactoryUser: false
    }
  },
  computed: {
    ...mapState('menu', ['pageButtonPower']),
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'month')
        .format('yyyy-MM')
    },
    levelList: function() {
      if (
        this.editIndex !== null &&
        this.pilotPlan1.gridData[this.editIndex] &&
        this.pilotPlan1.gridData[this.editIndex].inspectionContent ===
          '工装备件'
      ) {
        return [
          '一般1级',
          '一般2级',
          '一般3级',
          '重要1级',
          '重要2级',
          '重要3级'
        ]
      }
      return []
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.loadData()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    console.log(this.$route, this.$router)
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.calculate()
    this.getpilotPlans()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    loadData() {
      this.getpilotPlan()
    },
    // 判定挽救结果（合格不合格）
    tableRowClassName({ row, rowIndex }) {
      console.log(row)
      if (row.rescuingOutcome === '不合格') {
        return 'row-red-background' // 设置背景为红色
      }
      // 如果条件都不符合，则不添加任何自定义类
      return ''
    },
    // 表格下方编辑功能
    startEditing() {
      this.editing = true
    },
    saveRemark() {
      this.editing = false
      this.remark[0].remark = this.editedRemark
      this.remark[0].weight = this.editedwgt
      const params = {
        setDate: this.cDate,
        data: this.remark
      }
      post(qualitySave, params).then(res => {
        //
        this.loading = false
        if (res && res !== -1) {
          this.$message.success('保存成功！')
          this.getpilotPlan()
          this.editIndex = null
          this.editPartIndex = null
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    // 重量字段保留三位小数
    formatWeight(row, column, cellValue) {
      return parseFloat(cellValue).toFixed(2)
    },
    // 获取数据
    async getpilotPlan() {
      post(qualityFind, {
        setDate: this.cDate,
        output: '2'
      }).then(res => {
        this.loading = false
        this['pilotPlan1'].showGridData = res.data
      })
      post(qualityFind, {
        setDate: this.cDate,
        output: '1'
      }).then(res => {
        this.remark = res.data
        if (res.data.length != 0) {
          this.editedRemark = res.data[0].remark ? res.data[0].remark : ''
          this.editedwgt = res.data[0].weight
            ? Number(res.data[0].weight).toFixed(2)
            : ''
        }
      })
    },
    // 保存
    savepilotPlanItem(row) {
      let err = 0
      let arr = []
      arr.forEach(item => {
        if (row[item] === '' || row[item] === null) {
          console.log(item)
          err++
        }
      })
      if (err > 0) {
        return this.$message.warning('请补全信息！')
      }
      this.savepilotPlan(row)
    },
    // 保存
    savepilotPlan(items) {
      let arry = []
      arry.push(items)
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: arry
      }
      post(qualitySave, params).then(res => {
        //
        this.loading = false
        if (res && res !== -1) {
          this.$message.success('保存成功！')
          this.getpilotPlan()
          this.editIndex = null
          this.editPartIndex = null
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    // 删除
    deleteItem(data) {
      let arry = []
      arry.push(data)
      this.$confirm(`是否确认删除这条记录?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        post(qualityDel, {
          data: arry
        }).then(res => {
          this.loading = false
          this.$message.success('删除成功！')
          this.getpilotPlan()
        })
      })
    },
    // 获取数据(缺陷维护)
    async getpilotPlans() {
      post(defrctFind, {
        page: this.page.page,
        size: this.page.size,
        defect: this.searchInfo.defect,
        category: this.searchInfo.category,
        priority: this.searchInfo.priority
      }).then(res => {
        this.loading = false
        this['pilotPlan1'].DefectData = res.content
        this.page.total = res.totalElements
      })
    },
    // 查询
    handleSearchClick() {
      this.getpilotPlans()
    },
    // 保存（缺陷维护）
    saveDefect(items) {
      let arry = []
      arry.push(items)
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: arry
      }
      post(defrctSave, params).then(res => {
        //
        this.loading = false
        if (res && res !== -1) {
          this.$message.success('保存成功！')
          this.getpilotPlans()
          this.editDefect = null
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    // 删除（缺陷维护）
    deleteDefect(data) {
      let arry = []
      arry.push(data)
      this.$confirm(`是否确认删除这条记录?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        post(defrctDel, {
          data: arry
        }).then(res => {
          this.loading = false
          this.$message.success('删除成功！')
          this.getpilotPlans()
        })
      })
    },
    handleSizeChange(val) {
      this.page.size = val
      this.getpilotPlans()
    },
    handleCurrentChange(val) {
      this.page.page = val
      this.getpilotPlans()
    },
    editItem(data, index, type) {
      this.editIndex = index
    },
    editItems(data, index, type) {
      this.editDefect = index
    },
    editPartItem(data, index, type) {
      this.editPartIndex = index
    },
    addGridData() {
      this.pilotPlan1.showGridData.push({})
      this.editIndex = 1
    },
    addDecfectData() {
      this.pilotPlan1.DefectData.push({})
    },
    calculate() {
      this.pilotPlan1.maxHeight = this.$refs.table1.offsetHeight - 100
      this.tableMaxHeight = document.body.clientHeight - 240
    },
    async findOneUserByUserNo() {
      this.userNo = localStorage.getItem('userId')
      const user = await post(findOneUserByUserNo, {
        userNo: this.userNo
      })
      return new Promise(resolve => resolve(user.data))
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          index: 'A',
          backlog: 'B',
          unit: 'C',
          proposedTime: 'D',
          completionTime: 'E',
          completionStatus: 'F',
          salesConfirmation: 'G',
          closingTime: 'H'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        const datas = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
        this.savepilotPlan(datas)
      })
    },
    exportpilotPlan() {
      const data = [
        {
          index: '序号',
          backlog: '待办事项内容',
          unit: '责任单位',
          proposedTime: '提出时间',
          completionTime: '完成时间',
          completionStatus: '完成情况',
          salesConfirmation: '销项确认',
          closingTime: '销项时间'
        }
      ].concat(
        _.cloneDeep(this.pilotPlan1.gridData).map((item, index) => {
          delete item.id
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `工艺技术绩效评价表（${this.cDate}）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {
              '!merges': LAY_EXCEL.makeMergeConfig([])
            }
          }
        }
      )
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.edit-btn {
  margin: 0 3px;
  &:first-child {
    margin-bottom: 5px;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.dialog-item-tit {
  font-size: 18px;
  color: #dcdcdc;
  margin-bottom: 8px;
  margin-top: 15px;
}
.contentInput {
  margin-right: 40px; /* 设置右外边距 */
  width: 140px;
}
/* 使用 white-space 设置换行 */
.el-input textarea {
  white-space: pre-wrap;
}

::v-deep .remark .el-input--small {
  font-size: 18px; /* 设置合适的字体大小 */
}
::v-deep .el-textarea.is-disabled .el-textarea__inner {
  color: #fff;
}
::v-deep .el-textarea.is-disabled .el-textarea__inner {
  background-color: rgba(31, 198, 255, -0.8);
  border: rgba(31, 198, 255, 0.2);
}
.remark {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(31, 198, 255, 0.2);
  border-bottom: 1px rgba(31, 198, 255, 0.2) solid;
}
.wgt_total {
  width: 200px;
}
.wgt_unit {
  margin-left: 10px;
  width: 70%;
}
::v-deep .el-table .row-red-background {
  color: red !important; /* 红色背景 */
}
</style>
