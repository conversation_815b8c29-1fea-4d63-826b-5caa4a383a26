<template>
  <div class="bigBox">
    <el-row :gutter="32">
      <el-col :span="12">
        <screen-border title="炼钢计划">
          <template v-slot:headerRight>
            <!-- <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(1)">
              <el-icon class="el-icon-edit-outline" />
              操作
            </span> -->
          </template>
          <el-table
            :data="listData1"
            height="calc(100vh - 200px)">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="PLC"
              label="机号"
              width="60"
              align="center"/>
            <el-table-column
              prop="CLASSES"
              label="班次"
              width="80"
              align="center"/>
            <el-table-column
              prop="CLASSPLANFURNACENUM"
              label="计划炉数"
              width="100"
              align="center"/>
            <el-table-column
              prop="STEELTYPESECTION"
              label="钢种及断面"
              align="center"/>
            <el-table-column
              prop="FURNACEGROUPS"
              label="每组炉数"
              width="100"
              align="center"/>
            <el-table-column
              prop="USE"
              label="用途"
              align="center"/>
            <el-table-column
              prop="CASTINGTIMEREMARKS"
              label="浇铸时间"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="生产计划">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="SaveProductionPlan">
              <el-icon class="el-icon-printer" />
              保存
            </span>
          </template>
          <div class="descriptionCss">
            <div class="PlanCss">
              <div><i/><span>停时计划</span></div>
              <el-descriptions 
                :column="2" 
                border>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    工艺换辊
                  </template>
                  <el-input v-model="listData2.rollChange"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    工艺换刀片
                  </template>
                  <el-input v-model="listData2.changeBlade"/>
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="PlanCss">
              <div><i/><span>生产计划</span></div>
              <el-descriptions 
                :column="3" 
                border>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    生产计划
                  </template>
                  <el-input v-model="listData2.productionPlan"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    规格计划
                  </template>
                  <el-input v-model="listData2.formatPlan"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    紧急合同
                  </template>
                  <el-input v-model="listData2.emergencyContract"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    合同跟踪计划
                  </template>
                  <el-input v-model="listData2.trackingPlan"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    待判版上线计划
                  </template>
                  <el-input v-model="listData2.linePlan"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    在线探伤堆冷板
                  </template>
                  <el-input v-model="listData2.coldPlateStack"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    可用材料
                  </template>
                  <el-input v-model="listData2.available"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    切割计划
                  </template>
                  <el-input v-model="listData2.slicingPlan"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    拉坯计划
                  </template>
                  <el-input v-model="listData2.throwPlan"/>
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="PlanCss">
              <div><i/><span>质量计划</span></div>
              <el-descriptions 
                :column="2" 
                border>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    坯料质量计划
                  </template>
                  <el-input v-model="listData2.blankQualityPlan"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    品种及质量计划
                  </template>
                  <el-input v-model="listData2.varietyQualityPlan"/>
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="PlanCss">
              <div><i/><span>装炉计划</span></div>
              <el-descriptions 
                :column="2" 
                border>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    钢种或订单号
                  </template>
                  <el-input v-model="listData2.feedPlanOrder1"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    质量要求
                  </template>
                  <el-input v-model="listData2.feedPlanRequire1"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    钢种或订单号
                  </template>
                  <el-input v-model="listData2.feedPlanOrder2"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    质量要求
                  </template>
                  <el-input v-model="listData2.feedPlanRequire2"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    钢种或订单号
                  </template>
                  <el-input v-model="listData2.feedPlanOrder3"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    质量要求
                  </template>
                  <el-input v-model="listData2.feedPlanRequire3"/>
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="PlanCss">
              <div><i/><span>在炉计划</span></div>
              <el-descriptions 
                :column="2" 
                border>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    钢种或订单号
                  </template>
                  <el-input v-model="listData2.inFurnacePlanOrder1"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    质量要求
                  </template>
                  <el-input v-model="listData2.inFurnacePlanRequire1"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    钢种或订单号
                  </template>
                  <el-input v-model="listData2.inFurnacePlanOrder2"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    质量要求
                  </template>
                  <el-input v-model="listData2.inFurnacePlanRequire2"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    钢种或订单号
                  </template>
                  <el-input v-model="listData2.inFurnacePlanOrder3"/>
                </el-descriptions-item>
                <el-descriptions-item :span="1">
                  <template slot="label">
                    质量要求
                  </template>
                  <el-input v-model="listData2.inFurnacePlanRequire3"/>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </screen-border>
      </el-col>
    </el-row>

    <!--弹框-->
    <el-dialog
      :visible.sync="dialogBox"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      @close="closeDialogBox">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span> -->
            <span
              v-show="title!='原因说明'"
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download" />
              下载
            </span>
            <span
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-printer" />
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <div>
        <el-table
          id="table"
          :data="formData"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60" />
          <el-table-column
            v-for="(item,index) in Header"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            align="center">
            <template v-slot="{ row }">
              <el-input v-model="row[item.prop]" />
              <span v-show="false">{{ row[item.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            width="100"
            label="操作">
            <template v-slot="{ row, $index }">
              <div class="btn">
                <el-button 
                  type="danger"
                  icon="el-icon-delete"
                  @click="delRow($index)"/>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="text-center">
          <span
            class="screen-btn"
            @click="addNewRow()">
            <el-icon class="el-icon-circle-plus-outline" />
            增加数据
          </span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import { saveAs } from 'file-saver'
import * as echarts from 'echarts'
import moment from 'moment'

import { PLAN_DATA1, PLAN_SAVE1, PLAN_DATA2, PLAN_SAVE2 } from '@/api/screen'

export default {
  name: 'Plan',
  components: {
    ScreenBorder
  },
  props: {
    selTime: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      //炼钢计划
      listData1: [],

      //生产计划
      listData2: {
        rollChange: '',
        changeBlade: '',
        productionPlan: '',
        formatPlan: '',
        emergencyContract: '',
        trackingPlan: '',
        linePlan: '',
        coldPlateStack: '',
        available: '',
        slicingPlan: '',
        throwPlan: '',
        blankQualityPlan: '',
        varietyQualityPlan: '',
        feedPlanOrder1: '',
        feedPlanOrder2: '',
        feedPlanOrder3: '',
        feedPlanRequire1: '',
        feedPlanRequire2: '',
        feedPlanRequire3: '',
        inFurnacePlanOrder1: '',
        inFurnacePlanOrder2: '',
        inFurnacePlanOrder3: '',
        inFurnacePlanRequire1: '',
        inFurnacePlanRequire2: '',
        inFurnacePlanRequire3: ''
      },

      //弹框统一
      dialogBox: false,
      title: '',
      //弹框统一表字段
      Header: [],
      //弹框统一表数据
      formData: [],
      //上传Excel
      fileList: []
    }
  },
  watch: {
    selTime: function() {
      this.getListData1()
      this.getListData2()
    }
  },

  created() {
    this.getListData1()
    this.getListData2()
  },
  methods: {
    //获取炼钢计划
    async getListData1() {
      let res = await post(PLAN_DATA1, {
        setTime: this.selTime
      })
      // console.log('获取炼钢计划', res)
      if (res.data) {
        this.listData1 = res.data
      }
    },

    //生产计划
    async getListData2() {
      let res = await post(PLAN_DATA2, {
        setTime: this.selTime
      })
      // console.log('生产计划', res)

      if (res.data) {
        Object.keys(this.listData2).forEach(item => {
          this.listData2[item] = res.data[0][item] ? res.data[0][item] : ''
        })
      }
    },

    //生产计划保存
    async SaveProductionPlan() {
      let res = await post(PLAN_SAVE2, {
        setTime: this.selTime,
        data: [this.listData2]
      })

      //console.log('保存', res)

      if (res.status == 1) {
        this.$message.success('保存成功')
        this.getListData2()
      }
    },

    //弹框
    openView(nub) {
      this.dialogBox = true
      if (nub === 1) {
        this.title = '炼钢计划'
        this.Header = [
          {
            label: '机号',
            prop: 'PLC'
          },
          {
            label: '班次',
            prop: 'CLASSES'
          },
          {
            label: '计划炉数',
            prop: 'CLASSPLANFURNACENUM'
          },
          {
            label: '钢种及断面',
            prop: 'STEELTYPESECTION'
          },
          {
            label: '每组炉数',
            prop: 'FURNACEGROUPS'
          },
          {
            label: '用途',
            prop: 'USE'
          },
          {
            label: '浇铸时间',
            prop: 'CASTINGTIMEREMARKS'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData1))
      }
    },

    //添加行
    addNewRow() {
      this.formData.push({})
    },

    //删除行
    delRow(index) {
      this.formData.splice(index, 1)
    },

    //下载模板
    DownloadExcel() {
      let data = [{}]
      this.Header.forEach(item => {
        data[0][item.prop] = item.label
      })

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `${this.title}.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          `${this.title}.xlsx`
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      let header = {}
      let letter = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      this.Header.forEach((item, index) => {
        header[item.prop] = letter[index]
      })

      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, header)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        console.log(list)

        list.forEach(item => {
          item.selTime = this.selTime
          this.formData.push(item)
        })
      })
    },

    //保存新增数据
    async saveData() {
      let res
      if (this.title == '炼钢计划') {
        res = await post(PLAN_SAVE1, {
          setTime: this.selTime,
          data: this.formData
        })
      }
      // console.log('保存', res)
      if (res.status == 1) {
        this.$message.success('保存成功')
        if (this.title == '炼钢计划') {
          this.getListData1()
        }

        this.closeDialogBox()
      }
    },

    //关闭弹框
    closeDialogBox() {
      this.dialogBox = false
      this.title = ''
      this.Header = []
      this.formData = []
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  .EchartsBox {
    height: 380px;

    .setRadio {
      /deep/ .el-radio {
        color: white;
      }
    }
  }

  .border-wrapper {
    margin-bottom: 15px;
  }

  /deep/ .el-textarea__inner {
    background-color: #041a21;
    border: 1px solid #1fc6ff;
    color: white;
    font-size: 14px;
    height: calc(100vh - 670px);
  }
}

//描述列表样式
.descriptionCss {
  .PlanCss {
    > div {
      color: #def0ff;
      margin-bottom: 10px;
      i {
        display: inline-block;
        width: 6px;
        height: 15px;
        background: #def0ff;
        position: relative;
        top: 2px;
        margin-right: 8px;
      }
    }
  }
  height: calc(100vh - 200px);
  overflow: auto;
  /deep/ .el-descriptions--small {
    font-size: 16px;
  }

  /deep/.el-descriptions-item__cell {
    border: 2px solid #0d4f6c;
  }

  /deep/.el-descriptions__body {
    color: #def0ff;
    background: transparent;
  }
  /deep/.el-descriptions-item__label.is-bordered-label {
    color: #def0ff;
    background: transparent;
    // background: rgba(255, 255, 255, 0.1);
  }
  /deep/.el-input__inner {
    background-color: #041a21;
    border: 1px solid #98c0cd;
    color: #def0ff;
    height: 29px;
    line-height: 29px;
  }
}

.btn {
  /deep/ .el-button {
    font-size: 15px;
    padding: 4px 15px;
    border-radius: 4px;
  }
}

/deep/ .el-table .el-table__cell {
  font-size: 19px;
}

/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}
</style>
