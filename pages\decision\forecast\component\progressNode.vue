<template>
  <div class="progress-box">
    <div class="relative">
      <div class="name">{{ name }}</div>
      <div class="progress-border">
        <em class="num-min">0</em>
        <em class="num-max">{{ total }}</em>
        <div
          :style="{'left': targetPercent + '%'}"
          class="target-line">
          <span>目标值</span>
          <em>{{ progressData.targetValue ? progressData.targetValue.toFixed(accuracy) : 0 }}</em>
        </div>
        <el-progress
          :text-inside="true"
          :stroke-width="20"
          :percentage="resultPercent"
          :stroke-linecap="'square'"
          :color="getColor"
          :format="getText"/>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'progress-node',
  props: {
    name: {
      default: '',
      type: String
    },
    percent: {
      default: 0,
      type: Number
    },
    text: {
      default: '',
      type: String
    },
    warning: {
      default: '',
      type: String
    },
    progressData: {
      default: function() {
        return {}
      },
      type: Object
    },
    accuracy: {
      default: null,
      type: Number || null
    }
  },
  computed: {
    getColor: function() {
      if (this.progressData.warningStatus) {
        return '#ffb243'
      }
      if (this.progressData.trendStatus) {
        return '#ffb243'
      }
      return '#00b0f0'
    },
    total: function() {
      if (this.progressData.targetValue === undefined) return 0
      if (this.progressData.resultValue <= this.progressData.targetValue) {
        return this.formatInt(this.progressData.targetValue * 1.2, 1, true)
      } else {
        return this.formatInt(this.progressData.resultValue * 1.2, 1, true)
      }
    },
    targetPercent: function() {
      if (!this.total) return 0
      return Number(
        ((this.progressData.targetValue / this.total) * 100).toFixed(1)
      )
    },
    resultPercent: function() {
      if (!this.total) return 0
      return Number(
        ((this.progressData.resultValue / this.total) * 100).toFixed(1)
      )
    }
  },
  methods: {
    getText(percent) {
      return this.accuracy
        ? this.progressData.resultValue
          ? this.progressData.resultValue.toFixed(this.accuracy)
          : 0
        : this.progressData.resultValue
    },
    /**
     * 将数字取整为10的倍数
     * @param {Number} num 需要取整的值
     * @param {Boolean} ceil 是否向上取整
     * @param {Number} prec 需要用0占位的数量
     */
    formatInt(num, prec = 2, ceil = true) {
      const len = String(num).length
      if (len <= prec) {
        return num
      }

      const mult = Math.pow(10, prec)
      return ceil ? Math.ceil(num / mult) * mult : Math.floor(num / mult) * mult
    }
  }
}
</script>

<style scoped lang="less">
.progress-box {
  position: relative;
  padding-left: 10px;
  margin-bottom: 30px;
  .name {
    margin-bottom: 20px;
    font-size: 18px;
    line-height: 1;
    font-weight: bold;
    color: #6a74a5;
  }
  .num-min {
    position: absolute;
    top: 130%;
    left: 0;
    font-size: 14px;
    font-weight: bold;
    color: #6a74a5;
    transform: translateX(-60%);
  }
  .num-max {
    position: absolute;
    top: 130%;
    right: 0;
    font-size: 14px;
    font-weight: bold;
    color: #6a74a5;
    transform: translateX(60%);
  }
}
.progress-border {
  position: relative;
  border: 4px solid #e5e9f2;
  padding: 2px 1px;
  margin: 0 20px;
  .target-line {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #e5e9f2;
    z-index: 10;
    span {
      position: absolute;
      white-space: nowrap;
      font-size: 12px;
      bottom: 135%;
      left: 50%;
      transform: translateX(-50%);
    }
    em {
      position: absolute;
      white-space: nowrap;
      font-size: 14px;
      font-weight: bold;
      color: #6a74a5;
      top: 130%;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
/deep/ .el-progress-bar__outer {
  border-radius: unset;
  background-color: #fff;
}
/deep/ .el-progress-bar__inner {
  border-radius: unset;
}
</style>
