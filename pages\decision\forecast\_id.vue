<template>
  <div>
    <div class="def-main">
      <div
        :class="{
          'warning': 0,
          'trend': 0
        }"
        :title="'目标产量'"
        class="item">
        <img
          :src="require('../../../assets/kpi_icon/odometer.svg')"
          alt="">
        <div class="node-describe">
          <div class="tit">目标产量</div>
          <span>{{ topModel.targetValue ? topModel.targetValue.toFixed(0) : '-' }}<em>吨</em></span>
        </div>
      </div>
      <div
        :class="{
          'warning': 0,
          'trend': 0
        }"
        :title="'本月实时产量'"
        class="item">
        <img
          :src="require('../../../assets/kpi_icon/odometer.svg')"
          alt="">
        <div class="node-describe">
          <div class="tit">本月实时产量</div>
          <span>{{ topModel.realMonthWgt ? topModel.realMonthWgt.toFixed(0) : '-' }}<em>吨</em></span>
        </div>
      </div>
      <div
        :class="{
          'warning': 0,
          'trend': 0
        }"
        :title="leaveWgtText"
        class="item">
        <img
          :src="require('../../../assets/kpi_icon/box.svg')"
          alt="">
        <div class="node-describe">
          <div class="tit">{{ leaveWgtText }}</div>
          <span>{{ leaveWgt }}<em>吨</em></span>
        </div>
      </div>
      <div
        :class="{
          'warning': 0,
          'trend': 0
        }"
        :title="'滚动预测产量'"
        class="item">
        <img
          :src="require('../../../assets/kpi_icon/data-line.svg')"
          alt="">
        <div class="node-describe">
          <div class="tit">滚动预测产量</div>
          <span>{{ rollForecastProductWeight ? rollForecastProductWeight.toFixed(0) : '-' }}<em>吨</em></span>
        </div>
      </div>
      <div
        :class="{
          'warning': 0,
          'trend': 0
        }"
        :title="'设备作业率'"
        class="item">
        <img
          :src="require('../../../assets/kpi_icon/pie-chart.svg')"
          alt="">
        <div class="node-describe">
          <div class="tit">设备作业率</div>
          <span>{{ deviceWorkRate || '-' }}<em>%</em></span>
        </div>
      </div>
    </div>
    <div class="content shadow-light">
      <div class="card-title">
        <div class="date">{{ dateStr }}</div>
        生产周期
        <el-tag size="medium">
          {{ startStr }}
        </el-tag>
        至
        <el-tag size="medium">
          {{ endStr }}
        </el-tag>
        共计
        <el-tag size="medium">
          {{ days }} 天
        </el-tag>
        <el-tag size="medium">
          {{ times }} 班次
        </el-tag>
        <el-tag size="medium">
          {{ hours }} 小时
        </el-tag>
      </div>
      <div class="progress-wrapper">
        <el-row
          :gutter="20">
          <el-col
            :span="13"
            style="border-right: 3px solid #eee">
            <div class="card-sub-title">生产时间概况</div>
            <el-row
              :gutter="16">
              <el-col :span="8">
                <div style="padding-top: 100%;position: relative;">
                  <div style="position: absolute;top: 0;left: 0;width: 100%;height: 100%;">
                    <chart-pie
                      :chart-data="[
                        { value: leaveCanUse, name: '剩余可生产时间' },
                        { value: productTimeUsed, name: '已生产时间' }
                    ]"/>
                    <div class="text">
                      <span class="leave"><em>{{ leaveCanUse }}</em>小时</span>
                      <span class="name">剩余可生产时间</span><br>
                      <span class="compare">月可生产时数：{{ monthCanUse || '-' }} 小时</span><br>
                      <span class="description">已生产：{{ productTimeUsed || '-' }}小时</span>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <progress-circle
                  :name="'设备总停时'"
                  :sub-name="'（当前/目标）'"
                  :accuracy="1"
                  :progress-data="getProgressData(1)"/>
              </el-col>
              <el-col :span="8">
                <progress-line
                  :name="'检修时间'"
                  :progress-data="getProgressData(2)"/>
                <progress-line
                  :name="'换辊时间:'"
                  :progress-data="getProgressData(3)"/>
                <progress-line
                  :name="'故障停时'"
                  :progress-data="getProgressData(4)"/>
                <progress-line
                  :name="'品种试验'"
                  :progress-data="getProgressData(5)"/>
                <progress-line
                  :name="'其他停时'"
                  :progress-data="getProgressData(6)"/>
              </el-col>
            </el-row>
            <br>
            <el-row
              :gutter="16">
              <el-col
                :span="8"
                class="col-1-5 custom-time">
                <p>剩余检修时间</p>
                <div>
                  <el-input
                    v-model="forecastObj.userRemainReconditionTime"
                    :controls="false"
                    type="number" />
                </div>
              </el-col>
              <el-col
                :span="8"
                class="col-1-5 custom-time">
                <p>剩余换辊时间</p>
                <div>
                  <el-input
                    v-model="forecastObj.userRemainRollChangeTime"
                    :controls="false"
                    type="number" />
                </div>
              </el-col>
              <el-col
                :span="8"
                class="col-1-5 custom-time">
                <p>剩余故障停时</p>
                <div>
                  <el-input
                    v-model="forecastObj.userRemainRepairTime"
                    :controls="false"
                    type="number" />
                </div>
              </el-col>
              <el-col
                :span="8"
                class="col-1-5 custom-time">
                <p>剩余品种试验</p>
                <div>
                  <el-input
                    v-model="forecastObj.userRemainResearchTime"
                    :controls="false"
                    type="number" />
                </div>
              </el-col>
              <el-col
                :span="8"
                class="col-1-5 custom-time">
                <p>剩余其他停时</p>
                <div>
                  <el-input
                    v-model="forecastObj.userRemainOtherBlockTime"
                    :controls="false"
                    type="number" />
                </div>
              </el-col>
            </el-row>
            <br>
            <div class="text-center">
              <el-button
                type="primary"
                @click="saveForecast" >提交</el-button>
            </div>
          </el-col>
          <el-col :span="11">
            <el-row
              :gutter="20">
              <el-col
                :span="12"
                style="border-right: 3px solid #eee">
                <div class="card-sub-title">已生产订单概况</div>
                <progress-node
                  :name="'平均厚度'"
                  :progress-data="getProgressData(8)"
                  class="progress"/>
                <progress-node
                  :name="'平均宽度'"
                  :progress-data="getProgressData(9)"
                  class="progress"/>
                <progress-node
                  :name="'坯料单重'"
                  :accuracy="1"
                  :progress-data="getProgressData(10)"/>
              </el-col>
              <el-col :span="12">
                <div class="card-sub-title">机时产量概况</div>
                <progress-node
                  :name="'当前机时产量'"
                  :progress-data="getProgressData(7)"
                  class="progress"/>
                <div class="product-advice">
                  <div class="name">日需机时产量</div>
                  <div class="num">
                    <el-icon
                      :class="getAdviceClass"
                      class="animate-bounce-down1"/>
                    <span>
                      <em>{{ adviceMachineOutput }}</em>
                      t/h
                    </span>
                  </div>
                </div>
                <div class="product-advice">
                  <div class="name">日需均产</div>
                  <div class="num">
                    <el-icon
                      :class="getAdviceClass"
                      class="animate-bounce-down1"/>
                    <span>
                      <em>{{ adviceDayOutput }}</em>
                      t
                    </span>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
    </div>
    <div>
      <div class="forecast-order shadow-light">
        <el-row :gutter="0">
          <el-col :span="24">
            <el-alert
              v-if="orderAdapting"
              :title="'正在预测'"
              :closable="false"
              :description="loadingText"
              class="forecast-alert"
              type="warning"
              show-icon/>
            <div
              v-loading="orderAdapting"
              :element-loading-text="loadingText"
              class="order-wrapper">
              <div class="list-header">
                <div style="width: 120px">
                  手持订单数据
                </div>
                <div class="right inp-name">
                  <el-form inline>
                    <el-form-item :label="'订单时间'">
                      <el-date-picker
                        v-model="orderProductionForm.handleDateRange"
                        :clearable="false"
                        :picker-options="pickerRangeOptions"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        style="width: 220px"/>
                    </el-form-item>
                    <el-form-item :label="'预测规则'">
                      <el-select
                        v-model="orderProductionRule"
                        placeholder="请选择"
                        style="width: 120px">
                        <el-option
                          v-for="item in rules"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"/>
                      </el-select>
                    </el-form-item>
                    <el-form-item label-width="0">
                      <el-button
                        type="warning"
                        @click="orderAdapt">开始预测</el-button>
                      <el-button
                        type="primary"
                        @click="orderMachineOutput()"
                      >机时产量查询
                      </el-button>
                      <el-button
                        type="primary"
                        @click="orderDetail()"
                      >订单详情
                      </el-button>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
              <order-detail
                ref="orderDetail"
                :factory="factory"
                :period="period + '-R'"
                :order-type="1"
                :can-edit="true"
                etail
                @update="loadData()"/>
              <el-dialog
                :width="'500px'"
                :close-on-click-modal="false"
                :visible.sync="importVisible">
                <el-form>
                  <el-form-item :label="'订单时间'">
                    <el-date-picker
                      v-model="orderProductionForm.handleDateRange"
                      :clearable="false"
                      :picker-options="pickerRangeOptions"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"/>
                  </el-form-item>
                  <el-form-item :label="'推算规则'">
                    <el-select
                      v-model="orderProductionRule"
                      placeholder="请选择">
                      <el-option
                        v-for="item in rules"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                  </el-form-item>
                </el-form>
                <div
                  slot="footer"
                  class="text-center">
                  <el-button
                    type="primary"
                    @click="orderAdapt()"
                  >确定
                  </el-button>
                </div>
              </el-dialog>
              <div class="description-box">
                <el-descriptions
                  :column="5"
                  :size="'medium'"
                  border>
                  <el-descriptions-item
                    label="订单总重量">
                    <span>{{ forecastObj.orderWeight }}吨</span>
                  </el-descriptions-item>
                  <el-descriptions-item
                    :span="2"
                    label="订单开始时间">
                    <span>{{ forecastObj.orderStartTime || 0 }}</span>
                  </el-descriptions-item>
                  <el-descriptions-item
                    :span="2"
                    label="订单结束时间">
                    <span>{{ forecastObj.orderEndTime || 0 }}</span>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="已匹配总重量">
                    <span>{{ forecastObj.orderAdaptedWeight || 0 }}吨</span>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="可信总重量">
                    <span>{{ forecastObj.orderReliableWgt || 0 }}吨</span>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="平均机时产量">
                    <span>{{ forecastObj.orderReliableMachineOutput || 0 }}吨/小时</span>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="平均宽度">
                    <span>{{ forecastObj.orderAvgWidth || 0 }} mm</span>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="平均厚度">
                    <span>{{ forecastObj.orderAvgThickness || 0 }} mm</span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              <div class="description-box">
                <el-form
                  ref="form1"
                  :label-width="'80px'"
                  :model="orderProductionForm.searchForm"
                  size="small"
                  inline
                  @keyup.enter.native="getOrders(1, 'orderProduction')"
                >
                  <el-form-item
                    prop="standardNo"
                  >
                    <el-select
                      v-model="orderProductionForm.searchForm.otherSteelTypes"
                      placeholder="请选择国标钢种号"
                      clearable
                      multiple
                      filterable
                      style="width: 180px">
                      <el-option
                        v-for="(item, index) in steelTypeList"
                        :key="index"
                        :label="item.name"
                        :value="item.value"/>
                    </el-select>
                    <!--                    <el-autocomplete-->
                    <!--                      v-model="orderProductionForm.searchForm.otherSteelType"-->
                    <!--                      :fetch-suggestions="steelTypeCp"-->
                    <!--                      :popper-append-to-body="false"-->
                    <!--                      clearable-->
                    <!--                      placeholder="输入国标钢种号"-->
                    <!--                      style="width:180px"-->
                    <!--                      type="text">-->
                    <!--                      <template slot-scope="{ item }">-->
                    <!--                        <span>{{ item.name }}</span>-->
                    <!--                      </template>-->
                    <!--                    </el-autocomplete>-->

                  </el-form-item>

                  <el-form-item
                    prop="standardNo"
                    label="厚度区间"
                  >
                    <el-input-number
                      v-model="orderProductionForm.searchForm.thicknessDown"
                      :min="0"
                      :max="orderProductionForm.searchForm.thicknessUp"
                      :controls="false"
                      style="width: 70px"/>
                    —
                    <el-input-number
                      v-model="orderProductionForm.searchForm.thicknessUp"
                      :min="orderProductionForm.searchForm.thicknessDown || 0"
                      :max="100000"
                      :controls="false"
                      style="width: 70px"/>
                  </el-form-item>
                  <el-form-item
                    prop="widthDown"
                    label="宽度区间"
                  >
                    <el-input-number
                      v-model="orderProductionForm.searchForm.widthDown"
                      :min="0"
                      :max="orderProductionForm.searchForm.widthUp"
                      :controls="false"
                      style="width: 70px"/>
                    —
                    <el-input-number
                      v-model="orderProductionForm.searchForm.widthUp"
                      :min="orderProductionForm.searchForm.widthDown || 0"
                      :max="100000"
                      :controls="false"
                      style="width: 70px"/>
                  </el-form-item>
                  <el-form-item
                    prop="standardNo"
                    label="机时产量"
                  >
                    <el-input-number
                      v-model="orderProductionForm.searchForm.machineOutputDown"
                      :min="0"
                      :max="orderProductionForm.searchForm.machineOutputUp"
                      :controls="false"
                      style="width: 70px"/>
                    —
                    <el-input-number
                      v-model="orderProductionForm.searchForm.machineOutputUp"
                      :min="orderProductionForm.searchForm.machineOutputDown || 0"
                      :max="100000"
                      :controls="false"
                      style="width: 70px"/>
                  </el-form-item>
                  <el-form-item
                    prop="matrFl"
                    label="保性能"
                  >
                    <el-select
                      v-model="orderProductionForm.searchForm.matrFl"
                      placeholder="请选择"
                      clearable
                      style="width: 100px">
                      <el-option
                        :label="'是'"
                        :value="'Y'"/>
                      <el-option
                        :label="'否'"
                        :value="'N'"/>
                    </el-select>
                  </el-form-item>
                  <el-button
                    icon="el-icon-search"
                    type="primary"
                    @click="getOrders(1, 'orderProduction')"
                  >查询
                  </el-button>
                  <el-button
                    @click="exportOrders(1, 'orderProduction')"
                  >导出
                  </el-button>
                </el-form>
              </div>
              <div class="table">
                <el-table
                  :data="orderProductionData"
                  border
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    label="序号"/>
                  <el-table-column
                    prop="otherSteelType"
                    label="国标钢种"/>
                  <el-table-column
                    prop="dealResult"
                    label="保性能"
                    width="80">
                    <template v-slot="{ row }">
                      <el-tag
                        v-if="row.matrFl !== null"
                        :type="getDict(row.matrFl, 'matrFlList').type"
                        disable-transitions>{{ getDict(row.matrFl, 'matrFlList').label }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="thickness"
                    label="订单厚度"/>
                  <el-table-column
                    prop="width"
                    label="订单宽度"/>
                  <el-table-column
                    prop="dealResult"
                    label="适配结果"
                    width="120px">
                    <template v-slot="{ row }">
                      <el-tag
                        v-if="row.dealResult !== null"
                        :type="getDict(row.dealResult, 'matchType').tag"
                        disable-transitions>{{ getDict(row.dealResult, 'matchType').label }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="oriOrdWgt"
                    label="原始订单重量"/>
                  <el-table-column
                    prop="unProdOrdWgt"
                    label="未生产订单重量"/>
                  <el-table-column
                    prop="wgt"
                    label="订单重量">
                    <template v-slot="{ row }">
                      <div style="white-space: nowrap">
                        {{ row.orderWeight }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-if="factory === 3"
                    prop="avgSteelsingleWgt"
                    label="平均钢板单重">
                    <template v-slot="{ row }">
                      <div style="white-space: nowrap">
                        {{ row.avgSteelSingleWgt }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="avgMachineBlocks"
                    label="平均机时块数">
                    <template v-slot="{ row }">
                      <div style="white-space: nowrap">
                        {{ row.avgMachineBlocks }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="userDefMachineOutput"
                    label="用户确认机时产量">
                    <template v-slot="{ row }">
                      <div style="white-space: nowrap">
                        {{ row.userDefMachineOutput }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="productionTime"
                    label="生产耗时"/>
                  <el-table-column
                    label="操作"
                    width="80">
                    <template v-slot="{ row }">
                      <el-button
                        slot="append"
                        type="text"
                        @click="handleEditOrder(row)">修改</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <br>
                <el-row
                  align="middle"
                  class="table-pagination"
                  justify="end"
                  type="flex"
                >
                  <el-pagination
                    :current-page="orderProductionForm.page"
                    :page-size="orderProductionForm.size"
                    :page-sizes="[10, 20, 30, 40]"
                    :total="orderProductionForm.total"
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </el-row>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="0">
          <el-col :span="24">
            <div class="order-wrapper">
              <div class="list-header">
                订单手动补齐（仅预测）
                <div class="right inp-name">
                  <el-button
                    type="primary"
                    @click="orderDetailManual()"
                  >查看详情
                  </el-button>
                  <el-button
                    type="success"
                    @click="showAddManual">添加订单</el-button>
                </div>
              </div>
              <order-detail
                ref="orderDetailManual"
                :factory="factory"
                :period="period + '-R'"
                :order-type="2"
                :can-edit="true"
                @update="loadData()"/>
              <!--手补订单添加-->
              <el-dialog
                :width="'1400px'"
                :visible.sync="manualMachineOutputVisible"
                @close="loadData">
                <div
                  v-if="manualMachineOutputVisible"
                  class="search-wrapper" >
                  <el-form
                    ref="form"
                    :label-width="'80px'"
                    :model="orderMachineOutputForm.searchForm"
                    size="small"
                    inline
                    @submit.native.prevent="getOrderMachineOutput(true)"
                  >
                    <el-form-item
                      prop="standardNo"
                    >
                      <el-input
                        v-model="orderMachineOutputForm.searchForm.standardNo"
                        suffix-icon="el-icon-search"
                        clearable
                        placeholder="请输入标准号"
                        style="width:120px"
                        type="text"
                      />
                    </el-form-item>
                    <el-form-item
                      prop="standardNo"
                      label="厚度区间"
                    >
                      <el-input-number
                        v-model="orderMachineOutputForm.searchForm.thicknessDown"
                        :min="0"
                        :max="orderMachineOutputForm.searchForm.thicknessUp"
                        :controls="false"
                        style="width: 80px"/>
                      —
                      <el-input-number
                        v-model="orderMachineOutputForm.searchForm.thicknessUp"
                        :min="orderMachineOutputForm.searchForm.thicknessDown || 0"
                        :max="100000"
                        :controls="false"
                        style="width: 80px"/>
                    </el-form-item>
                    <el-form-item
                      prop="standardNo"
                      label="宽度区间"
                    >
                      <el-input-number
                        v-model="orderMachineOutputForm.searchForm.widthDown"
                        :min="0"
                        :max="orderMachineOutputForm.searchForm.widthUp"
                        :controls="false"
                        style="width: 80px"/>
                      —
                      <el-input-number
                        v-model="orderMachineOutputForm.searchForm.widthUp"
                        :min="orderMachineOutputForm.searchForm.widthDown || 0"
                        :max="100000"
                        :controls="false"
                        style="width: 80px"/>
                    </el-form-item>
                    <el-form-item
                      prop="matrFl"
                      label="保性能"
                    >
                      <el-select
                        v-model="orderMachineOutputForm.searchForm.matrFl"
                        placeholder="请选择"
                        clearable
                        style="width: 100px">
                        <el-option
                          :label="'是'"
                          :value="'1'"/>
                        <el-option
                          :label="'否'"
                          :value="'0'"/>
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      prop="matrFl"
                      label-width="80"
                      label="机时产量推荐"
                    >
                      <el-select
                        v-model="manualProductionRule"
                        placeholder="请选择"
                        clearable
                        style="width: 120px">
                        <el-option
                          v-for="item in rules"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"/>
                      </el-select>
                    </el-form-item>
                    <el-button
                      icon="el-icon-search"
                      type="primary"
                      @click="getOrderMachineOutput"
                    >搜索
                    </el-button>
                  </el-form>
                </div>
                <div class="table">
                  <el-table
                    :data="orderMachineOutputForm.data"
                    border
                    style="width: 100%">
                    <el-table-column
                      type="index"
                      label="序号"/>
                    <el-table-column
                      prop="standardNo"
                      label="标准号"/>
                    <el-table-column
                      prop="thickness"
                      label="订单厚度"/>
                    <el-table-column
                      prop="width"
                      label="订单宽度"/>
                    <el-table-column
                      prop="dealResult"
                      label="保性能">
                      <template v-slot="{ row }">
                        <el-tag
                          v-if="row.matrFl !== null"
                          :type="getDict(row.matrFl, 'matrFlList').type"
                          disable-transitions>{{ getDict(row.matrFl, 'matrFlList').label }}</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="avgInMat"
                      label="机时产量-推荐值">
                      <template v-slot="{ row }">
                        <span>{{ row[getDict(manualProductionRule, 'rules').dict] }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="avgInMat"
                      label="订单重量">
                      <template v-slot="{ row }">
                        <el-input
                          v-model="row.orderWeight"/>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="avgInMat"
                      label="操作">
                      <template v-slot="{ row }">
                        <el-button
                          type="text"
                          size="mini"
                          @click.native.stop="addManual($event, row)">
                          添加
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <br>
                  <el-row
                    align="middle"
                    class="table-pagination"
                    justify="end"
                    type="flex"
                  >
                    <el-pagination
                      :current-page="orderMachineOutputForm.page"
                      :page-size="orderMachineOutputForm.size"
                      :page-sizes="[10, 20, 30, 40]"
                      :total="orderMachineOutputForm.total"
                      background
                      layout="total, sizes, prev, pager, next, jumper"
                      @size-change="handleMachineSizeChange"
                      @current-change="handleMachineCurrentChange"
                    />
                  </el-row>
                </div>
              </el-dialog>
              <div class="table">
                <el-table
                  :data="manualProductionData"
                  border
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    label="序号"/>
                  <el-table-column
                    prop="standardNo"
                    label="标准号"/>
                  <el-table-column
                    prop="dealResult"
                    label="保性能"
                    width="80">
                    <template v-slot="{ row }">
                      <el-tag
                        v-if="row.matrFl !== null"
                        :type="getDict(row.matrFl, 'matrFlList').type"
                        disable-transitions>{{ getDict(row.matrFl, 'matrFlList').label }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="thickness"
                    label="订单厚度"/>
                  <el-table-column
                    prop="width"
                    label="订单宽度"/>
                  <el-table-column
                    prop="orderWeight"
                    label="订单重量"
                    width="100px"/>
                  <el-table-column
                    prop="userDefMachineOutput"
                    label="用户确认机时产量">
                    <template v-slot="{ row }">
                      <div style="white-space: nowrap">
                        {{ row.userDefMachineOutput }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="productionTime"
                    label="生产耗时"/>
                </el-table>
                <br>
                <el-row
                  align="middle"
                  class="table-pagination"
                  justify="end"
                  type="flex"
                >
                  <el-pagination
                    :current-page="manualProductionForm.page"
                    :page-size="manualProductionForm.size"
                    :page-sizes="[10, 20, 30, 40]"
                    :total="manualProductionForm.total"
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleManualSizeChange"
                    @current-change="handleManualCurrentChange"
                  />
                </el-row>
              </div>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="order-wrapper">
              <div class="list-header">
                剩余生产时间产量预测
              </div>
              <el-form
                :inline="true"
                class="demo-form-inline">
                <el-form-item
                  :class="{'warning': remainHours < 0}"
                  :label="'剩余时数'">
                  <el-input
                    v-model="remainHours"
                    readonly
                    placeholder="请输入内容">
                    <template slot="append">小时</template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  :label="'历史机时产量'">
                  <el-autocomplete
                    v-model="forecastObj.historyMachineProductionWgt"
                    :fetch-suggestions="historyMachineProductionWgtList"
                    :popper-append-to-body="false"
                    popper-class="forecast-inp"
                    placeholder="请输入内容"
                    style="width: 220px">
                    <template slot="append">吨/小时</template>
                    <template slot-scope="{ item }">
                      <span>{{ item.name }}</span>
                    </template>
                  </el-autocomplete>
                </el-form-item>
                <el-form-item
                  :label="'预估产量'">
                  <el-input
                    v-model="remainHoursWeight"
                    readonly
                    placeholder="请输入内容">
                    <template slot="append">吨</template>
                  </el-input>
                </el-form-item>
              </el-form>
            </div>
          </el-col>
        </el-row>
        <div class="btn-wrapper">
          <el-button
            type="primary"
            @click="saveForecast" >提交</el-button>
        </div>

      </div>
    </div>
  </div>
</template>

percent<script>
import {
  findForecast,
  findMachineOutputByConditions,
  findMachineOutputDatasById,
  findOrders,
  findOtherSteelTypes,
  findWarningDataByConditions,
  getEvalOverview,
  getRollForecastData,
  machineOutputAdapt,
  orderAdapt,
  saveForecast,
  saveOrder
} from '@/api/kpi'
import { post } from '@/lib/Util'
import KpiNode from '@/components/kpiTree/KpiNode'
import KpiDef from '@/components/KpiDef'
import { ENUM } from '@/lib/Constant'
import ProgressNode from '@/pages/decision/forecast/component/progressNode'
import ProgressCircle from '@/pages/decision/forecast/component/progressCircle'
import ProgressLine from '@/pages/decision/forecast/component/progressLine'
import OrderDetail from '@/pages/kpiForecast/forecast/component/orderDetail'
import ChartBars from '@/pages/decision/forecast/component/chart-bars'
import ChartPie from '@/pages/decision/forecast/component/chart-pie'
import { math } from '@/lib/Math'
import * as _ from 'lodash'
import moment from 'moment'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import baseMixins from '@/mixins/BaseMixins'

export default {
  name: 'feature-factory',
  components: {
    ChartPie,
    ChartBars,
    OrderDetail,
    ProgressLine,
    ProgressCircle,
    ProgressNode,
    KpiDef,
    KpiNode
  },
  mixins: [baseMixins],
  data() {
    return {
      activeName: '0',
      dataList: [],
      factory: null,
      loading: true,
      icon: require('../../../assets/kpi_icon/currency-rmb-circle.svg'),
      period: '',
      fac2kpi: {
        3: {
          1: 1332,
          2: 1333,
          3: 1334,
          4: 1335,
          5: 1336,
          6: 1337,
          7: 1349,
          8: 1345,
          9: 1341,
          10: 216,
          real: 12
        },
        1: {
          1: 1320,
          2: 1321,
          3: 1322,
          4: 1323,
          5: 1324,
          6: 1325,
          7: 1347,
          8: 1343,
          9: 1339,
          10: 214,
          real: 2
        },
        2: {
          1: 1326,
          2: 1327,
          3: 1328,
          4: 1329,
          5: 1330,
          6: 1331,
          7: 1348,
          8: 1344,
          9: 1340,
          10: 215,
          real: 7
        }
      },
      forecastObj: {
        factory: '',
        period: '',
        conditionStopsStatus: false,
        researchStopsStatus: false,
        otherStopsStatus: false,
        orderStatus: false,
        manualOrderStatus: false,
        effectiveTimeToProduce: null, // 有效生产时间
        spendTimeOnOrder: null, // 手持订单生产耗时
        equipmentOperationRate: null, // 设备作业率预测
        productionForecast: null, // 产量预测
        reconditionTime: null, // 检修时间
        rollChangeTime: null, // 换辊时间
        repairTime: null, // 故障停时
        researchTime: null, // 品种试验停时
        otherBlockingTime: null, // 其他停时
        orderProductionTime: null, // 手持订单预估生产用时
        orderWeight: null, // 手持订单预估生产重量
        orderProductionType: null, // 中间品机时产量协作方式
        midProductionPrdTime: null, // 中间品预估生产用时
        midProductionWeight: null, // 中间品预估生产重量
        midProductionType: null, // 手持订单机时产量协作方式
        manualProductionTime: null, // 手补订单预估生产用时
        manualWeight: null, // 手补订单预估生产重量
        manualProductionType: null, // 手补订单机时产量协作方式
        remainHours: null, // 剩余时数
        historyProductionType: null, // 历史机时产量协作方式
        historyMachineProductionWgt: null, // 历史机时产量
        remainHoursWeight: null, // 剩余时数预估产量
        orderAdaptedWeight: null,
        orderReliableWgt: null,
        orderReliableTime: null,
        orderReliableMachineOutput: null,
        userRemainOtherBlockTime: null,
        userRemainReconditionTime: null,
        userRemainRepairTime: null,
        userRemainResearchTime: null,
        userRemainRollChangeTime: null
      },
      orderAdapting: false,
      orderAdaptTime: 0,
      importVisible: false,
      orderProductionForm: {
        searchForm: {},
        newOrder: false,
        handleDateRange: null,
        page: 1,
        size: 10,
        total: 0
      },
      orderProductionData: [],
      manualProductionData: [],
      manualProductionForm: {
        searchForm: {
          standardNo: '',
          steelType: ''
        },
        data: [],
        page: 1,
        size: 10,
        total: 0,
        edit: {}
      },
      orderProductionRule: 0,
      manualProductionRule: 0,
      rules: ENUM.cooperation,
      matchType: ENUM.matchType,
      // 性能要求列表
      matrFlList: ENUM.matrFlList,
      manualMachineOutputVisible: false,
      orderMachineOutputVisible: false, // 机时产量查询
      orderMachineOutputForm: {
        searchForm: {
          standardNo: '',
          steelType: ''
        },
        data: [],
        page: 1,
        size: 10,
        total: 0,
        edit: {}
      },
      loadingText: '订单预测中，本次预测约需要2分钟,请耐心等待...',
      // 厂代号映射关系
      CFM_MILL_PLT: {
        1: 'C1',
        2: 'C2',
        3: 'C3'
      },
      topModel: {
        monthTarget: 0, // 本月目标值,
        targetValue: 0, // 本月目标值,
        planWgt: 0, // 滚动预测产量
        realMonthWgt: 0 // 本月实时产量
      },
      productTime: {
        used: null, // 已生产时间
        leave: null // 剩余可生产
      },
      historyMachineProductionWgtList: null,
      steelTypeList: [] // 钢种列表
    }
  },
  // 已生产时间、本月实时产量、当前应生产量:调用轧制实绩数据接口
  // 刺余可用生产时间=总时数-设备总停时目标值-5个分目标超目标值的时间-已生产时间
  // 建议后期排产订单机时产量值= (本月目标产量-本月实时产量)/剩余可用生产时间
  // 目标值: productionForecast/getRollForecastData --data: machineOutputTargetValue
  computed: {
    leaveWgtText: function() {
      const month = this.getProductMonth(this.dateStr)
      return (
        moment(month.curDate).format('MM月DD') +
        '~' +
        moment(month.endDate).format('MM月DD日') +
        '预测产量'
      )
    },
    // 判断是否每月第一天
    isFirstDay: function() {
      return moment().format('DD') === '01'
    },
    leaveWgt: function() {
      return this.isFirstDay
        ? '0'
        : math
            .subtract(
              this.rollForecastProductWeight,
              this.topModel.realMonthWgt
            )
            .toFixed(0)
    },
    days: function() {
      return this.$moment(this.period, 'YYYY-MM').daysInMonth()
    },
    hours: function() {
      return this.days * 24
    },
    times: function() {
      return this.days * 3
    },
    dateStr: function() {
      return this.$moment()
        .subtract(1, 'day')
        .format('yyyy-MM-DD')
    },
    startStr: function() {
      return this.$moment(this.period)
        .startOf('month')
        .format('yyyy-MM-DD')
    },
    endStr: function() {
      return this.$moment(this.period)
        .endOf('month')
        .format('yyyy-MM-DD')
    },
    steelTypeFilter: function() {
      return this.steelTypeList.filter(
        item =>
          !this.orderProductionForm.searchForm.otherSteelType ||
          item.value.indexOf(
            this.orderProductionForm.searchForm.otherSteelType
          ) !== -1
      )
    },
    // 日期选择
    pickerRangeOptions: function() {
      return {
        onPick: ({ maxDate, minDate }) => {
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.selectDate = minDate.getTime()
          if (maxDate) {
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            this.selectDate = ''
          }
        },
        disabledDate: time => {
          if (this.selectDate !== '') {
            const one = 150 * 24 * 3600 * 1000
            const minTime = this.selectDate - one
            const maxTime = this.selectDate + one
            return time.getTime() < minTime || time.getTime() > maxTime
          }
        }
      }
    },
    // 滚动预测产量
    // 滚动预测产量=本月实时产量+手持订单机时产量"剩余可用生产时间+历史订单预测产量
    rollForecastProductWeight: function() {
      return this.isFirstDay
        ? this.topModel.realMonthWgt
        : math.add(
            Number((this.topModel.realMonthWgt || 0).toFixed(0)),
            this.forecastObj.orderReliableWgt || 0,
            this.forecastObj.manualWeight || 0,
            this.remainHoursWeight || 0
          )
    },
    // 已生产时间
    productTimeUsed: function() {
      return math
        .subtract(
          24 *
            this.$moment()
              .subtract(1, 'day')
              .format('DD'),
          this.getProgressData(1).resultValue || 0
        )
        .toFixed(2)
    },
    // 设备作业率
    deviceWorkRate: function() {
      return (
        math.divide(
          this.productTimeUsed,
          24 *
            this.$moment()
              .subtract(1, 'day')
              .format('DD')
        ) * 100
      ).toFixed(2)
    },
    // 月可生产时间
    monthCanUse: function() {
      return this.hours - (this.getProgressData(1).targetValue || 0)
    },
    // 剩余可生产时间
    leaveCanUse: function() {
      return this.isFirstDay
        ? 0
        : Number(
            math
              .subtract(
                this.hours,
                this.forecastObj.userRemainReconditionTime || 0,
                this.forecastObj.userRemainRollChangeTime || 0,
                this.forecastObj.userRemainRepairTime || 0,
                this.forecastObj.userRemainResearchTime || 0,
                this.forecastObj.userRemainOtherBlockTime || 0,
                24 * (this.$moment().format('DD') - 1)
              )
              .toFixed(2)
          )
    },
    // 建议机时产量
    adviceMachineOutput: function() {
      if (
        (!this.topModel.targetValue && !this.topModel.realMonthWgt) ||
        this.isFirstDay
      )
        return '-'
      return (
        (this.topModel.targetValue - this.topModel.realMonthWgt) /
        this.leaveCanUse
      ).toFixed(1)
    },
    // 建议日产产量
    adviceDayOutput: function() {
      const leaveDays = Number(this.days - (this.$moment().format('DD') - 1))
      console.log(leaveDays, this.getProgressData(2))
      return this.isFirstDay
        ? '-'
        : math
            .divide(
              math.subtract(
                this.topModel.targetValue,
                this.topModel.realMonthWgt
              ),
              math.subtract(
                leaveDays,
                (this.getProgressData(2).targetValue -
                  this.getProgressData(2).resultValue) /
                  24
              )
            )
            .toFixed(0)
    },
    // 手持订单预测机时产量
    avgMachineOutput() {
      return this.forecastObj.orderReliableWgt &&
        this.forecastObj.orderReliableTime
        ? math
            .divide(
              this.forecastObj.orderReliableWgt || 0,
              this.forecastObj.orderReliableTime || 0
            )
            .toFixed(2)
        : 0
    },
    getAdviceClass() {
      return this.adviceMachineOutput <= this.getProgressData(7).targetValue
        ? 'el-icon-caret-bottom red'
        : 'el-icon-caret-top green'
    },
    effectiveTimeToProduce: function() {
      let num = this.hours
      if (this.forecastObj.conditionStopsStatus) {
        num = _.subtract(num, Number(this.forecastObj.repairTime))
        num = _.subtract(num, Number(this.forecastObj.rollChangeTime))
        num = _.subtract(num, Number(this.forecastObj.reconditionTime))
      }
      if (this.forecastObj.researchStopsStatus) {
        num = _.subtract(num, Number(this.forecastObj.researchTime))
      }
      if (this.forecastObj.otherStopsStatus) {
        num = _.subtract(num, Number(this.forecastObj.otherBlockingTime))
      }
      return num
    },
    // 历史预测-剩余时长
    remainHours: function() {
      let num = this.leaveCanUse
      if (this.forecastObj.orderStatus) {
        num = math.subtract(num, this.forecastObj.orderReliableTime)
      }
      if (this.forecastObj.manualOrderStatus) {
        num = math.subtract(num, this.forecastObj.manualProductionTime)
      }
      return num.toFixed(2)
    },

    // 历史-预估产量
    remainHoursWeight: function() {
      return math
        .multiply(
          this.remainHours,
          this.forecastObj.historyMachineProductionWgt || 0
        )
        .toFixed(2)
    }
  },
  watch: {},
  created() {
    this.orderProductionForm.handleDateRange = [
      this.$moment()
        .subtract(2, 'month')
        .format('yyyy-MM-DD'),
      this.$moment()
        .subtract(-5, 'month')
        .format('yyyy-MM-DD')
    ]
    this.period = this.$moment()
      .subtract(1, 'day')
      .format('yyyy-MM')
    this.factory = parseInt(this.$route.params.id)
    this.loadData()
  },
  methods: {
    getProgressData(key) {
      if (!this.fac2kpi[this.factory]) return {}
      if (!this.fac2kpi[this.factory][key]) return {}
      const match = this.dataList.find(
        item => item.kid === this.fac2kpi[this.factory][key] && item.rule === 0
      )
      if (!match) return {}
      return match ? match : {}
    },
    async loadData() {
      this.getOrders(1, 'orderProduction')
      this.getOrders(2, 'manualProduction')
      // 获取预测数据
      post(findForecast, {
        period: this.period + '-R',
        factory: this.factory
      }).then(res => {
        if (res.data) {
          Object.assign(this.forecastObj, res.data)
          this.orderProductionRule = this.rules.find(
            item => item.value == res.data.orderProductionType
          ).value
          this.forecastObj.orderStatus = true
          this.forecastObj.manualOrderStatus = true
        } else {
          this.orderProductionForm.newOrder = true
        }
      })

      // 历史机时产量
      post(findMachineOutputDatasById, {
        id: '0'
      }).then(res => {
        const list = res.data
          ? [
              { name: '近1年最好值', value: String(res.data.bestInMat) },
              { name: '近1年最差值', value: String(res.data.worstInMat) },
              { name: '近1年平均值', value: String(res.data.avgInMat) },
              { name: '近1年中位数', value: String(res.data.midInMat) },
              { name: '近3月平均值', value: String(res.data.avgInLast3m) },
              {
                name: '滚动3月最佳值',
                value: String(res.data.bestInRolling3m)
              },
              { name: '本月均值', value: String(res.data.avgInThisMonth) },
              { name: '上月均值', value: String(res.data.avgInLastMonth) }
            ]
          : []
        this.historyMachineProductionWgtList = (str, cb) => {
          cb(list)
        }
      })
      // 获取其他钢种
      post(findOtherSteelTypes, {
        period: this.period,
        factory: this.factory
      }).then(res => {
        this.steelTypeList = res.data
          ? res.data.map(item => {
              return {
                name: item,
                value: item
              }
            })
          : []
      })
      // 获取kpi数据
      const data = await post(findWarningDataByConditions, {
        factory: this.factory,
        period: this.period
      })
      if (data.success) {
        if (!data.data) return
        this.dataList = data.data || []
        data.data.forEach(kpi => {
          kpi.percent = Number(
            ((kpi.resultValue / kpi.targetValue) * 100).toFixed(1)
          )
          // 获取预警状态
          const warningMatch = this.dataList.find(
            item => item.kid === kpi.kid && item.ruleSign === 0
          )
          kpi.warningStatus = warningMatch ? warningMatch.ruleStatus : false
          const trendMatch = data.data.find(
            item => item.kid === kpi.kid && item.ruleSign === 0
          )
          kpi.trendStatus = trendMatch ? trendMatch.ruleStatus : false
        })
        const monthReal = this.dataList.find(
          item =>
            item.kid === this.fac2kpi[this.factory].real &&
            item.isTargetMark === true
        )
        this.topModel.realMonthWgt = monthReal ? monthReal.resultValue : 0

        /* Object.assign(this.forecastObj, {
          userRemainReconditionTime: this.judgeNum(
            math.subtract(
              this.getProgressData(2).targetValue,
              this.getProgressData(2).resultValue
            )
          ),
          userRemainRollChangeTime: this.judgeNum(
            math.subtract(
              this.getProgressData(3).targetValue,
              this.getProgressData(3).resultValue
            )
          ),
          userRemainRepairTime: this.judgeNum(
            math.subtract(
              this.getProgressData(4).targetValue,
              this.getProgressData(4).resultValue
            )
          ),
          userRemainResearchTime: this.judgeNum(
            math.subtract(
              this.getProgressData(5).targetValue,
              this.getProgressData(5).resultValue
            )
          ),
          userRemainOtherBlockTime: this.judgeNum(
            math.subtract(
              this.getProgressData(6).targetValue,
              this.getProgressData(6).resultValue
            )
          )
        })*/
      }
      this.loading = false
      // 数据概览
      post(getEvalOverview, {
        period: this.period,
        factory: this.factory,
        millDate: this.$moment()
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
      }).then(res => {
        Object.assign(this.topModel, res.data)
      })
    },
    judgeNum(num) {
      return num >= 0 ? num : 0
    },
    steelTypeCp(str, cb) {
      return cb(this.steelTypeFilter)
    },
    getDict(value, list) {
      const match = this[list].find(item => item.value === value)
      return match ? match : {}
    },
    orderMachineOutput() {
      this.$refs.orderDetail.orderMachineOutputVisible = true
      this.$refs.orderDetail.getOrderMachineOutput(true)
    },
    // 显示订单详情
    orderDetail() {
      this.$refs.orderDetail.orderDetailVisible = true
      this.$refs.orderDetail.getOrders()
    },
    // 导出订单
    exportOrders(orderType, name) {
      post(
        findOrders,
        Object.assign(
          {
            period: this.period + '-R',
            factory: this.factory,
            orderType: orderType,
            page: 0,
            size: 10000,
            orderState: 0
          },
          this[name + 'Form'].searchForm
        )
      ).then(res => {
        // this[name + 'Data'] = res.data.content || []
        this.formatExcel(res.data.content)
        // this.getMachineProduction(name)
      })
    },
    // 生成Excel
    formatExcel(list) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      const data = [
        {
          num: '序号',
          otherSteelType: '国标钢种',
          matrFl: '保性能',
          thickness: '订单厚度',
          width: '订单宽度',
          dealResult: '适配结果',
          oriOrdWgt: '原始订单重量',
          unProdOrdWgt: '未生产订单重量',
          wgt: '订单重量',
          avgSteelsingleWgt: '平均钢板单重',
          avgMachineBlocks: '平均机时块数',
          userDefMachineOutput: '用户确认机时产量',
          productionTime: '生产耗时'
        }
      ].concat(
        _.cloneDeep(list).map((item, index) => {
          return {
            num: index + 1,
            otherSteelType: item.otherSteelType,
            matrFl: this.getDict(item.matrFl, 'matrFlList').label,
            thickness: item.thickness,
            width: item.width,
            dealResult: this.getDict(item.dealResult, 'matchType').label,
            oriOrdWgt: item.oriOrdWgt,
            unProdOrdWgt: item.unProdOrdWgt,
            wgt: item.wgt,
            avgSteelsingleWgt: item.avgSteelsingleWgt,
            avgMachineBlocks: item.avgMachineBlocks,
            userDefMachineOutput: item.userDefMachineOutput,
            productionTime: item.productionTime
          }
        })
      )
      const len = list.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `手持订单.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },
    getOrders(orderType, name) {
      //orderType： 手持： 1， 手补：2， 中间品：3  历史所有： 0
      post(
        findOrders,
        Object.assign(
          {
            period: this.period + '-R',
            factory: this.factory,
            orderType: orderType,
            page: this[name + 'Form'].page - 1,
            size: this[name + 'Form'].size,
            orderState: 0
          },
          this[name + 'Form'].searchForm
        )
      ).then(res => {
        this[name + 'Data'] = res.data.content || []
        this[name + 'Form'].total = res.data.totalElements
        // this.getMachineProduction(name)
      })
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.orderProductionForm.size = val
      this.getOrders(1, 'orderProduction')
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.orderProductionForm.page = val
      this.getOrders(1, 'orderProduction')
    },
    handleManualSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.orderProductionForm.size = val
      this.getOrders(2, 'manualProduction')
    },
    handleManualCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.orderProductionForm.page = val
      this.getOrders(2, 'manualProduction')
    },
    handleMachineSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.orderMachineOutputForm.size = val
      this.getOrderMachineOutput()
    },
    handleMachineCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.orderMachineOutputForm.page = val
      this.getOrderMachineOutput()
    },
    // 导入订单
    async orderAdapt() {
      // 导入订单
      if (1) {
        return await this.$confirm(
          '当前周期已有订单数据，此操作将会覆盖所有订单数据, 是否继续?',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.orderAdaptConfirm()
          })
          .catch(() => {
            this.importVisible = false
          })
      } else {
        this.orderAdaptConfirm()
      }
    },
    // 导入订单动作
    orderAdaptConfirm() {
      this.orderAdapting = true
      this.importVisible = false
      post(machineOutputAdapt, {
        period: this.period + '-R',
        factory: this.factory,
        orderType: 1,
        userDef: this.orderProductionRule,
        newOrder: this.orderProductionForm.newOrder,
        startTime: this.$moment(
          this.orderProductionForm.handleDateRange[0]
        ).format('yyyy-MM-DD'),
        endTime: this.$moment(
          this.orderProductionForm.handleDateRange[1]
        ).format('yyyy-MM-DD'),
        orderState: 0
      }).then(res => {
        this.orderAdapting = false
        if (res.success) {
          this.orderProductionForm.newOrder = false
          this.loadData()
        }
      })
    },
    handleEditOrder(row) {
      this.$refs.orderDetail.searchForm.steelType = row.steelType
      this.$refs.orderDetail.searchForm.thicknessDown = row.thickness
      this.$refs.orderDetail.searchForm.thicknessUp = row.thickness
      this.$refs.orderDetail.searchForm.widthUp = row.width
      this.$refs.orderDetail.searchForm.widthDown = row.width
      this.$refs.orderDetail.searchForm.matrFl = row.matrFl
      this.$refs.orderDetail.orderDetailVisible = true
      this.$refs.orderDetail.getOrders()
    },
    // 手补显示订单详情
    orderDetailManual() {
      this.$refs.orderDetailManual.orderDetailVisible = true
      this.$refs.orderDetailManual.getOrders()
    },
    // 手补订单添加显示
    showAddManual() {
      this.manualMachineOutputVisible = true
      this.getOrderMachineOutput(true)
    },
    getOrderMachineOutput(reset = false) {
      if (reset) {
        this.orderMachineOutputForm.page = 1
        this.orderMachineOutputForm.size = 10
      }
      post(
        findMachineOutputByConditions,
        Object.assign({}, this.orderMachineOutputForm.searchForm, {
          page: this.orderMachineOutputForm.page - 1,
          size: this.orderMachineOutputForm.size
        })
      ).then(res => {
        this.orderMachineOutputForm.data = res.data.content || []
        this.orderMachineOutputForm.total = res.data.totalElements
        // this.getMachineProduction(name)
      })
    },
    // 添加手补订单
    addManual(e, row) {
      if (!row.orderWeight) return this.$message.warning('请输入订单重量')
      const data = Object.assign({}, row)
      data.userDefMachineOutput =
        row[this.getDict(this.manualProductionRule, 'rules').dict]
      post(
        saveOrder,
        Object.assign(data, {
          period: this.period + '-R',
          factory: this.factory,
          orderType: 2,
          orderState: 0,
          productionTime: (
            data.orderWeight / data.userDefMachineOutput
          ).toFixed(2)
        })
      ).then(res => {
        this.getOrders(2, 'manualProduction')
        this.$message.success('添加订单成功!')
      })
    },

    // 保存预测
    saveForecast(type, name) {
      //
      const params = this.forecastObj
      Object.assign(params, {
        effectiveTimeToProduce: this.effectiveTimeToProduce,
        spendTimeOnOrder: this.spendTimeOnOrder,
        equipmentOperationRate: this.equipmentOperationRate,
        remainHours: this.remainHours,
        remainHoursWeight: this.remainHoursWeight,
        productionForecast: this.rollForecastProductWeight
      })

      post(saveForecast, params).then(res => {
        if (res.success) {
          this.$message.success('保存成功')
        } else {
          this.$message.warning('保存失败')
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.def-main {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  .item {
    width: 19%;
    display: flex;
    justify-content: space-between;
    color: #fff;
    background: #5e93ed;
    padding: 12px 15px;
    border-radius: 6px;
    box-shadow: 0 0 10px rgba(34, 35, 35, 0.5);
    gap: 4px;
    span {
      display: block;
      font-size: 24px;
      line-height: 38px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      em {
        font-size: 14px;
      }
    }
    img {
      margin-right: 10px;
      width: 20%;
    }
    .tit {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    &.trend {
      background: #ffa958;
    }
    &.warning {
      background: #f56c6c;
    }
    .node-describe {
      //margin-right: 15px;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
      span {
        display: block;
        min-height: 40px;
      }
    }
    .node-arrow {
      cursor: pointer;
    }
  }
}
.content {
  padding: 24px;
  margin-top: 20px;
  margin-bottom: 20px;
  background: #fff;
  .card-title {
    font-size: 20px;
    margin-bottom: 20px;
    line-height: 28px;
    font-weight: bold;
    color: #3a3f63;
    .date {
      float: right;
      font-size: 26px;
      color: #666;
      font-weight: 500;
    }
  }
  .card-sub-title {
    position: relative;
    padding-left: 15px;
    font-weight: 900;
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 20px;
    color: #3a3f63;
    &:before {
      content: '';
      position: absolute;
      top: 4px;
      bottom: 4px;
      width: 8px;
      left: 0;
      background: #3a3f63;
    }
  }
  .progress-wrapper {
    margin-top: 20px;
  }
  .text {
    position: absolute;
    white-space: nowrap;
    min-width: 140px;
    top: 50%;
    left: 20px;
    right: 20px;
    transform: translateY(-50%);
    text-align: center;
    line-height: 22px;
    border-radius: 40px;
    .leave {
      display: block;
      margin-bottom: 6%;
      font-weight: bold;
      font-size: 18px;
      line-height: 1;
      color: #999;
      em {
        font-size: 36px;
        color: #19be6b;
      }
    }
    .name {
      font-weight: 600;
      font-size: 18px;
      line-height: 1;
      color: #6a74a5;
    }
    .compare {
      font-size: 12px;
      line-height: 20px;
      text-align: center;
      color: #4458fe;
      font-weight: bold;
    }
    .description {
      font-size: 12px;
      line-height: 14px;
      text-align: center;
      color: #6a74a5;
    }
  }

  .progress {
    margin-bottom: 20%;
  }
  .product-advice {
    margin-bottom: 20%;
    .name {
      padding-left: 10px;
      margin-bottom: 10px;
      font-size: 18px;
      line-height: 1;
      font-weight: bold;
      color: #6a74a5;
    }
    .num {
      color: #6a74a5;
      font-weight: bold;
      text-align: center;
      margin-top: 20px;
      em {
        color: #00b0f0;
        font-size: 26px;
      }
    }
  }
}
.box-custom {
  .el-col-4 {
    width: 20%;
  }
}

.forecast-order {
  margin-bottom: 20px;
  padding: 24px;
  background: #fff;
  .inp-name {
  }
  .name {
    margin-bottom: 20px;
  }
  .list-header {
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .order-wrapper {
    //padding: 0 25px;
    margin-bottom: 20px;
  }
  .description-box {
    margin-bottom: 20px;
  }
  .btn-wrapper {
    padding: 25px;
    text-align: center;
  }
}
.custom-time {
  p {
    font-size: 14px;
    line-height: 24px;
    color: #6a74a5;
    margin-bottom: 5px;
  }
  /deep/ .el-input-number .el-input__inner {
    text-align: left;
  }
}
.footer_btn {
  text-align: center;
  line-height: 47px;
}
@keyframes bounce-down {
  25% {
    transform: translateY(-3px);
  }
  50% {
    transform: translateY(0);
  }
  75% {
    transform: translateY(3px);
  }
  100% {
    transform: translateY(0);
  }
}
.animate-bounce-down {
  -webkit-animation: bounce-down 1s linear infinite;
  animation: bounce-down 1s linear infinite;
}

@keyframes bounce-down1 {
  25% {
    transform: translateY(-3px);
  }
  50%,
  100% {
    transform: translateY(0);
  }
  75% {
    transform: translateY(3px);
  }
}
/deep/ .col-1-5 {
  width: 20%;
}
.animate-bounce-down1 {
  display: none;
  -webkit-animation: bounce-down 1s linear infinite;
  animation: bounce-down 1s linear infinite;
}
</style>
