<template>
  <div 
    :id="containerId" 
    style="height: 100%"/>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  props: {
    chartData: {
      type: Number,
      default: 0
    },
    color: {
      type: String,
      default: '#19be6b'
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    vertical: {
      type: Boolean,
      default: true
    },
    unit: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    titleNum: {
      type: String,
      default: ''
    },
    titleText: {
      type: String,
      default: ''
    },
    labelWidth: {
      type: Number,
      default: 80
    },
    total: {
      type: Number,
      default: 0
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
        window.addEventListener('resize', this.resizeChart)
      }
      const options = {
        title: {
          show: !!this.title,
          text: '{tit|' + this.title + '}',
          subtext:
            '{stit|' + this.titleNum + '}\n{text|' + this.titleText + '}',
          left: '50%',
          top: '34%',
          textAlign: 'center',
          padding: 0,
          textStyle: {
            fontSize: 23,
            rich: {
              tit: {
                borderColor: 'transparent',
                borderWidth: 4,
                borderRadius: 4,
                color: '#fff',
                fontSize: 16
              }
            }
          },
          subtextStyle: {
            rich: {
              stit: {
                borderColor: 'transparent',
                fontSize: 30,
                lineHeight: 20,
                fontWeight: 600,
                color: this.color
              },
              text: {
                borderColor: 'transparent',
                fontSize: 16,
                borderWidth: 15,
                lineHeight: 40,
                fontWeight: 600,
                color: '#fff'
              }
            }
          }
        },
        tooltip: {
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        polar: {
          radius: ['58%', '86%'],
          center: ['50%', '50%']
        },
        angleAxis: {
          max: 100,
          show: false
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '',
            type: 'bar',
            roundCap: false,
            showBackground: true,
            backgroundStyle: {
              color: '#2e4262'
            },
            data: [this.chartData],
            coordinateSystem: 'polar',
            itemStyle: {
              normal: {
                color: this.color,
                borderRadius: 0
              }
            }
          }
        ]
      }
      this.myChart.setOption(options)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    getNum(name) {
      const match = this.chartData.find(item => item.name === name)
      return match ? match : {}
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style scoped>
</style>
