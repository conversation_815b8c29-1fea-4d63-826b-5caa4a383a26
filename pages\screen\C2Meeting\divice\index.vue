<template>
  <div class="content">
    <el-row
      :gutter="32"
      class="full-height">
      <screen-border-multi>
        <template v-slot:title>
          <div class="header">
            <div class="tabBox">
              <div
                v-for="(item, index) in tabList"
                :key="index"
                :style="{color:item.active?'#FFFFFF':''}"
                class="tab"
                @click="selTab(item, index)">
                <div class="tab_block">
                  <div>{{ item.title }}</div>
                  <div
                    v-if="item.active"
                    class="tab_img">
                    <img
                      class="tab_img2"
                      src="@/assets/images/screen/tab-pane-active-line2.png"
                      alt="">
                    <img
                      class="tab_img1"
                      src="@/assets/images/screen/tab-pane-active-line.png"
                      alt="">
                  </div>
                </div>
              </div>
            </div>
            <span
              v-command="'/screen/C2Meeting/edit'"
              v-if="active==0"
              class="screen-btn"
              @click="dataObj.dialogVisible = true">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
            <span
              v-command="'/screen/C2Meeting/edit'"
              v-else
              class="screen-btn"
              @click="openView">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </div>
        </template>
        <template v-slot:default>
          <div
            v-if="active===0"
            class="img-box">
            <img
              :src="baseURL + dataObj.src"
              alt="">
          </div>
          <div v-else-if="active===1">
            <el-table
              :data="drivingFailure"
              :max-height="600">
              <el-table-column
                type="index"
                label="序号"
                width="60"/>
              <el-table-column
                prop="dateConfig"
                label="日期"
                align="center"/>
              <el-table-column
                prop="classes"
                label="班次"
                align="center"/>
              <el-table-column
                prop="faultContent"
                label="行车故障内容"
                align="center"/>
              <el-table-column
                prop="faultTime"
                label="故障时间"
                align="center"/>
              <el-table-column
                prop="unit"
                label="责任单位"
                align="center"/>
              <el-table-column
                prop="remark"
                label="备注"
                align="center"/>
            </el-table>
          </div>
        </template>
      </screen-border-multi>
    </el-row>


    <!-- 切割机设备利用率弹框 -->
    <el-dialog
      :visible.sync="dataObj.dialogVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="图片选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="图片">
          <template v-if="dataObj.picture">
            <ul class="el-upload-list el-upload-list--picture-card">
              <li
                class="el-upload-list__item is-ready">
                <img-view
                  :id="dataObj.picture"
                  deleteable
                  @img-delete="handleImgDelete($event)"
                />
              </li>
            </ul>
          </template>
          <el-upload
            v-else
            ref="upload"
            :auto-upload="false"
            :on-change="handleChange"
            :show-file-list="false"
            multiple
            list-type="picture-card"
            action="#"
            style="display: inline">
            <i class="el-icon-plus"/>
          </el-upload>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>

    <!--行车故障弹框-->
    <el-dialog
      :visible.sync="dialogBox"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      @close="closeDialogBox">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span> -->
            <span
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span>
            <span
              v-show="title!='原因说明'"
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download" />
              下载
            </span>
            <el-button
              :loading="buttonLoading"
              class="screen-btn"
              @click="saveData"
            >
              <el-icon class="el-icon-printer" />
              保存
            </el-button>
          </div>
          {{ title }}
        </div>
      </template>
      <div>
        <el-table
          id="table"
          :data="formData"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60" />
          <el-table-column
            v-for="(item,index) in Header"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            align="center">
            <template v-slot="{ row }">
              <el-input v-model="row[item.prop]" />
              <span v-show="false">{{ row[item.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            width="100"
            label="操作">
            <template v-slot="{ row, $index }">
              <span
                class="screen-btn"
                @click="delRow($index)">
                <el-icon class="el-icon-delete" />
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
        <div class="text-center">
          <span
            class="screen-btn"
            @click="addNewRow()">
            <el-icon class="el-icon-circle-plus-outline" />
            增加数据
          </span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import {
  findBoardParameterByDateAndPara,
  qmsQualitySupervisionQuery,
  qmsQualitySupervisionSave,
  saveBoardParameter,
  DRIVINGFAILURE_DATA,
  DRIVINGFAILURE_SAVE
} from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/C2Meeting/component/custom-table'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ImgView from '@/components/ImgView'
import { post } from '@/lib/Util'
import { deleteFileByIds, downloadFileById, uploadFile } from '@/api/system'
export default {
  name: 'diviceC1',
  components: {
    ImgView,
    ScreenBorder,
    CustomTable,
    SingleBarsChart,
    ScreenBorderMulti
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      buttonLoading: false,
      active: 0,
      tabList: [
        {
          active: true,
          title: '切割机设备利用率'
        },
        {
          active: false,
          title: '行车故障'
        }
      ],

      cDate: '',
      baseURL: 'http://172.25.63.67:9800/' + downloadFileById,
      dataObj: {
        dialogVisible: false,
        src: '',
        picture: ''
      },

      //行车故障
      drivingFailure: [],

      dialogBox: false,
      title: '',
      Header: [],
      formData: [],
      fileList: [] // 添加fileList属性
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.getPicture()
      this.getDrivingFailureData()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.getPicture()
    this.getDrivingFailureData()
  },
  methods: {
    //tab切换
    selTab(item, index) {
      this.tabList.forEach(item => {
        item.active = false
      })
      item.active = true
      this.active = index
      if (this.active == 0) {
        this.getPicture()
      } else {
        this.getDrivingFailureData()
      }
    },

    // 获取当月是否锁定
    getPicture() {
      post(findBoardParameterByDateAndPara, {
        setDate: this.cDate
      }).then(res => {
        this.dataObj.src = this.dataObj.picture = this.getParam(
          'C1DivicePicture',
          res.data
        )
      })
    },

    async handleChange(file, fileList, index) {
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) {
        return this.$message.error('上传头像图片大小不能超过 5MB!')
      }
      const formData = new FormData()
      formData.append('files', file.raw)
      post(uploadFile, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        if (res.success) {
          this.$message.success('图片上传成功！')
          this.savePictureData(res.data[0].id)
        } else {
          this.$message.warning('图片上传失败！')
          this.loading = false
        }
      })
    },
    async handleImgDelete(file) {
      const del = await post(deleteFileByIds, { ids: [file.id] })
      if (del.success) {
        this.savePictureData('')
      }
    },
    savePictureData(id) {
      const params = {
        data: [
          {
            parameter: 'C1DivicePicture',
            content: id,
            setDate: this.cDate
          }
        ]
      }
      post(saveBoardParameter, params).then(res => {
        this.loading = false
        if (res.status === 1) {
          this.$message.success('数据保存成功')
          this.dataObj.picture = this.dataObj.src = id
        }
      })
    },

    //获取行车故障数据
    async getDrivingFailureData() {
      this.drivingFailure = []
      let res = await post(DRIVINGFAILURE_DATA, {
        setTime: this.cDate
      })
      // console.log('行车故障', res)

      if (res.data.length != 0) {
        this.drivingFailure = res.data
      }
    },

    //弹框
    openView() {
      this.dialogBox = true
      this.title = '行车故障'
      this.Header = [
        {
          label: '日期',
          prop: 'dateConfig'
        },
        {
          label: '班次',
          prop: 'classes'
        },
        {
          label: '行车故障内容',
          prop: 'faultContent'
        },
        {
          label: '故障时间',
          prop: 'faultTime'
        },
        {
          label: '责任单位',
          prop: 'unit'
        },
        {
          label: '备注',
          prop: 'remark'
        }
      ]
      this.formData = JSON.parse(JSON.stringify(this.drivingFailure))
    },

    //添加行
    addNewRow() {
      this.formData.push({})
    },

    //删除行
    delRow(index) {
      this.formData.splice(index, 1)
    },

    //下载模板
    DownloadExcel() {
      let data = [{}]
      this.Header.forEach(item => {
        data[0][item.prop] = item.label
      })

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `${this.title}.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          `${this.title}.xlsx`
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      let header = {}
      let letter = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      this.Header.forEach((item, index) => {
        header[item.prop] = letter[index]
      })

      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, header)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        console.log(list)

        list.forEach(item => {
          item.cDate = this.cDate
          this.formData.push(item)
        })
      })
    },

    //保存新增数据
    async saveData() {
      this.buttonLoading = true
      let res = await post(DRIVINGFAILURE_SAVE, {
        setTime: this.cDate,
        data: this.formData
      })
      // console.log('保存', res)
      if (res.status == 1) {
        this.$message.success('保存成功')
        this.getDrivingFailureData()
        this.closeDialogBox()
      }
      this.buttonLoading = false
    },

    //关闭弹框
    closeDialogBox() {
      this.dialogBox = false
      this.title = ''
      this.Header = []
      this.formData = []
    }
  }
}
</script>

<style scoped lang="less">
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 10px;
  .tabBox {
    display: flex;
    .tab {
      color: #ffffffbf;
      margin-right: 20px;
    }
    .tab_block {
      display: flex;
      flex-direction: column;
      position: relative;
      .tab_img {
        .tab_img2 {
          width: 100%;
          position: absolute;
          bottom: 0;
          left: 0;
        }
        .tab_img1 {
          width: 100%;
          position: absolute;
          bottom: 0;
          left: 0;
          margin-bottom: 7px;
        }
      }
    }
  }
  .border-content {
    height: 380px;
  }
}
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.img-box {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    height: 100%;
    width: 100%;
    object-fit: contain;
  }
}
</style>
