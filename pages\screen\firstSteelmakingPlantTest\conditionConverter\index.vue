<template>
  <div class="content">
    <div class="content-item">
      <screen-border :title="'转炉炉况'">
        <template v-slot:headerRight>
          <span
            v-command="'/screen/firstSteelmakingPlant/edit'"
            class="screen-btn"
            @click="showDialogVisible"
          >
            <el-icon class="el-icon-edit-outline" />
            操作
          </span>
        </template>
        <div 
          ref="tableHeight" 
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            key="zl"
            :data="unfinished.showGridData"
            :max-height="tableHeight"
            class="font-table center-table"
            border
          >
            <el-table-column 
              property="NAME" 
              width="120px" />
            <el-table-column 
              label="1号转炉" 
              property="A_LIST" >
              <template v-slot="{ row }">
                <template v-if="row.NAME=='测量情况'||row.NAME=='考核情况'">
                  <ul class="el-upload-list el-upload-list--picture-card">
                    <li
                      v-for="(item, index) in getPictureList(row.A_LIST)"
                      :key="index"
                      class="el-upload-list__item is-ready"
                    >
                      <img-view
                        :key="item"
                        :src="'http://*************:9084/orgApi2/boardUploadfiles/'+item"
                        :is-id="false"
                        :img-width="'95%'"
                        :deleteable="false"
                      />
                    </li>
                  </ul>
                </template>
                <template v-else>
                  {{ row.A_LIST }}
                </template>
              </template>
            </el-table-column>
            <el-table-column 
              label="2号转炉" 
              property="B_LIST" >
              <template v-slot="{ row }">
                <template v-if="row.NAME=='测量情况'||row.NAME=='考核情况'">
                  <ul class="el-upload-list el-upload-list--picture-card">
                    <li
                      v-for="(item, index) in getPictureList(row.B_LIST)"
                      :key="index"
                      class="el-upload-list__item is-ready"
                    >
                      <img-view
                        :key="item"
                        :src="'http://*************:9084/orgApi2/boardUploadfiles/'+item"
                        :is-id="false"
                        :img-width="'95%'"
                        :deleteable="false"
                      />
                    </li>
                  </ul>
                </template>
                <template v-else>
                  {{ row.B_LIST }}
                </template>
              </template>
            </el-table-column>
            <el-table-column 
              label="3号转炉" 
              property="C_LIST" >
              <template v-slot="{ row }">
                <template v-if="row.NAME=='测量情况'||row.NAME=='考核情况'">
                  <ul class="el-upload-list el-upload-list--picture-card">
                    <li
                      v-for="(item, index) in getPictureList(row.C_LIST)"
                      :key="index"
                      class="el-upload-list__item is-ready"
                    >
                      <img-view
                        :key="item"
                        :src="'http://*************:9084/orgApi2/boardUploadfiles/'+item"
                        :is-id="false"
                        :img-width="'95%'"
                        :deleteable="false"
                      />
                    </li>
                  </ul>
                </template>
                <template v-else>
                  {{ row.C_LIST }}
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </screen-border>
    </div>
    <!--转炉炉况-->
    <el-dialog
      :visible.sync="unfinished.dialogVisible"
      :width="'90%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="转炉炉况"
    >
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"
            />
            <span
              v-loading="loading"
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveUnfinished"
            >
              <el-icon class="el-icon-document-checked" />
              保存
            </span>
          </div>
          转炉炉况
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-row
          :style="{ height: 'calc(100vh - 355px)' }"
          :gutter="24"
          class="dialog-body"
        >
          <el-col :span="8">
            <div class="dialog-cell">
              <div class="dialog-cell-title">1号转炉测量情况</div>
              <template>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in unfinished.updataImgurl.ImgView1
                    .showMeasureUrl"
                    :key="index"
                    class="el-upload-list__item is-ready"
                  >
                    <img-view
                      :key="item"
                      :src="'http://*************:9084/orgApi2/boardUploadfiles/'+item"
                      :is-id="false"
                      :img-width="'95%'"
                      :deleteable="canEditQuality"
                      @img-delete="
                        handleImgDeleteAfter(
                          $event,
                          'ImgView1',
                          'showMeasureUrl'
                        )
                      "
                    />
                  </li>
                </ul>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in unfinished.updataImgurl.ImgView1
                    .showMeasure"
                    :key="index"
                    class="el-upload-list__item is-ready"
                  >
                    <img-view
                      :key="item"
                      :src="item"
                      :is-id="false"
                      :img-width="'95%'"
                      :deleteable="canEditQuality"
                      @img-delete="
                        handleImgDelete($event, 'ImgView1', 'measure')
                      "
                    />
                  </li>
                </ul>
                <el-upload
                  v-if="canEditQuality"
                  ref="upload"
                  :auto-upload="false"
                  :show-file-list="false"
                  :http-request="httpRequest"
                  :on-change="
                    (file, fileList) =>
                      handleChange(file, fileList, 'ImgView1', 'measure')
                  "
                  multiple
                  list-type="picture-card"
                  action="#"
                  style="display: inline"
                >
                  <i class="el-icon-plus" />
                </el-upload>
              </template>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">1号转炉后大面</div>
              <el-input
                v-model="unfinished.gridData.stove1.A_BOFCD"
                :disabled="!canEditQuality"
                :placeholder="'请输入后大面'"
              />
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">1号转炉耳轴</div>
              <el-input
                v-model="unfinished.gridData.stove1.B_BOFCD"
                :disabled="!canEditQuality"
                :placeholder="'请输入耳轴'"
              />
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">1号转炉炉底</div>
              <el-input
                v-model="unfinished.gridData.stove1.C_BOFCD"
                :disabled="!canEditQuality"
                :placeholder="'请输入炉底'"
              />
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">1号转炉补炉情况</div>
              <el-input
                v-model="unfinished.gridData.stove1.D_BOFCD"
                :disabled="!canEditQuality"
                :placeholder="'请输入补炉情况'"
              />
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">1号转炉考核情况</div>
              <template>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in unfinished.updataImgurl.ImgView1
                    .showExamineUrl"
                    :key="index"
                    class="el-upload-list__item is-ready"
                  >
                    <img-view
                      :key="item"
                      :src="'http://*************:9084/orgApi2/boardUploadfiles/'+item"
                      :is-id="false"
                      :img-width="'95%'"
                      :deleteable="canEditQuality"
                      @img-delete="
                        handleImgDeleteAfter(
                          $event,
                          'ImgView1',
                          'showExamineUrl'
                        )
                      "
                    />
                  </li>
                </ul>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in unfinished.updataImgurl.ImgView1
                    .showExamine"
                    :key="index"
                    class="el-upload-list__item is-ready"
                  >
                    <img-view
                      :key="item"
                      :src="item"
                      :is-id="false"
                      :img-width="'95%'"
                      :deleteable="canEditQuality"
                      @img-delete="
                        handleImgDelete($event, 'ImgView1', 'examine')
                      "
                    />
                  </li>
                </ul>
                <el-upload
                  v-if="canEditQuality"
                  ref="upload"
                  :auto-upload="false"
                  :show-file-list="false"
                  :http-request="httpRequest"
                  :on-change="
                    (file, fileList) =>
                      handleChange(file, fileList, 'ImgView1', 'examine')
                  "
                  multiple
                  list-type="picture-card"
                  action="#"
                  style="display: inline"
                >
                  <i class="el-icon-plus" />
                </el-upload>
              </template>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="dialog-cell">
              <div class="dialog-cell-title">2号转炉测量情况</div>
              <template>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in unfinished.updataImgurl.ImgView2
                    .showMeasureUrl"
                    :key="index"
                    class="el-upload-list__item is-ready"
                  >
                    <img-view
                      :key="item"
                      :src="'http://*************:9084/orgApi2/boardUploadfiles/'+item"
                      :is-id="false"
                      :img-width="'95%'"
                      :deleteable="canEditQuality"
                      @img-delete="
                        handleImgDeleteAfter(
                          $event,
                          'ImgView2',
                          'showMeasureUrl'
                        )
                      "
                    />
                  </li>
                </ul>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in unfinished.updataImgurl.ImgView2
                    .showMeasure"
                    :key="index"
                    class="el-upload-list__item is-ready"
                  >
                    <img-view
                      :key="item"
                      :src="item"
                      :is-id="false"
                      :img-width="'95%'"
                      :deleteable="canEditQuality"
                      @img-delete="
                        handleImgDelete($event, 'ImgView2', 'measure')
                      "
                    />
                  </li>
                </ul>
                <el-upload
                  v-if="canEditQuality"
                  ref="upload"
                  :auto-upload="false"
                  :show-file-list="false"
                  :http-request="httpRequest"
                  :on-change="
                    (file, fileList) =>
                      handleChange(file, fileList, 'ImgView2', 'measure')
                  "
                  multiple
                  list-type="picture-card"
                  action="#"
                  style="display: inline"
                >
                  <i class="el-icon-plus" />
                </el-upload>
              </template>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">2号转炉后大面</div>
              <el-input
                v-model="unfinished.gridData.stove2.A_BOFCD"
                :disabled="!canEditQuality"
                :placeholder="'请输入后大面'"
              />
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">2号转炉耳轴</div>
              <el-input
                v-model="unfinished.gridData.stove2.B_BOFCD"
                :disabled="!canEditQuality"
                :placeholder="'请输入耳轴'"
              />
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">2号转炉炉底</div>
              <el-input
                v-model="unfinished.gridData.stove2.C_BOFCD"
                :disabled="!canEditQuality"
                :placeholder="'请输入炉底'"
              />
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">2号转炉补炉情况</div>
              <el-input
                v-model="unfinished.gridData.stove2.D_BOFCD"
                :disabled="!canEditQuality"
                :placeholder="'请输入补炉情况'"
              />
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">2号转炉考核情况</div>
              <template>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in unfinished.updataImgurl.ImgView2
                    .showExamineUrl"
                    :key="index"
                    class="el-upload-list__item is-ready"
                  >
                    <img-view
                      :key="item"
                      :src="'http://*************:9084/orgApi2/boardUploadfiles/'+item"
                      :is-id="false"
                      :img-width="'95%'"
                      :deleteable="canEditQuality"
                      @img-delete="
                        handleImgDeleteAfter(
                          $event,
                          'ImgView2',
                          'showExamineUrl'
                        )
                      "
                    />
                  </li>
                </ul>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in unfinished.updataImgurl.ImgView2
                    .showExamine"
                    :key="index"
                    class="el-upload-list__item is-ready"
                  >
                    <img-view
                      :key="item"
                      :src="item"
                      :is-id="false"
                      :img-width="'95%'"
                      :deleteable="canEditQuality"
                      @img-delete="
                        handleImgDelete($event, 'ImgView2', 'examine')
                      "
                    />
                  </li>
                </ul>
                <el-upload
                  v-if="canEditQuality"
                  ref="upload"
                  :auto-upload="false"
                  :show-file-list="false"
                  :http-request="httpRequest"
                  :on-change="
                    (file, fileList) =>
                      handleChange(file, fileList, 'ImgView2', 'examine')
                  "
                  multiple
                  list-type="picture-card"
                  action="#"
                  style="display: inline"
                >
                  <i class="el-icon-plus" />
                </el-upload>
              </template>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="dialog-cell">
              <div class="dialog-cell-title">3号转炉测量情况</div>
              <template>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in unfinished.updataImgurl.ImgView3
                    .showMeasureUrl"
                    :key="index"
                    class="el-upload-list__item is-ready"
                  >
                    <img-view
                      :key="item"
                      :src="'http://*************:9084/orgApi2/boardUploadfiles/'+item"
                      :is-id="false"
                      :img-width="'95%'"
                      :deleteable="canEditQuality"
                      @img-delete="
                        handleImgDeleteAfter(
                          $event,
                          'ImgView3',
                          'showMeasureUrl'
                        )
                      "
                    />
                  </li>
                </ul>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in unfinished.updataImgurl.ImgView3
                    .showMeasure"
                    :key="index"
                    class="el-upload-list__item is-ready"
                  >
                    <img-view
                      :key="item"
                      :src="item"
                      :is-id="false"
                      :img-width="'95%'"
                      :deleteable="canEditQuality"
                      @img-delete="
                        handleImgDelete($event, 'ImgView3', 'measure')
                      "
                    />
                  </li>
                </ul>
                <el-upload
                  v-if="canEditQuality"
                  ref="upload"
                  :auto-upload="false"
                  :show-file-list="false"
                  :http-request="httpRequest"
                  :on-change="
                    (file, fileList) =>
                      handleChange(file, fileList, 'ImgView3', 'measure')
                  "
                  multiple
                  list-type="picture-card"
                  action="#"
                  style="display: inline"
                >
                  <i class="el-icon-plus" />
                </el-upload>
              </template>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">3号转炉后大面</div>
              <el-input
                v-model="unfinished.gridData.stove3.A_BOFCD"
                :disabled="!canEditQuality"
                :placeholder="'请输入后大面'"
              />
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">3号转炉耳轴</div>
              <el-input
                v-model="unfinished.gridData.stove3.B_BOFCD"
                :disabled="!canEditQuality"
                :placeholder="'请输入耳轴'"
              />
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">3号转炉炉底</div>
              <el-input
                v-model="unfinished.gridData.stove3.C_BOFCD"
                :disabled="!canEditQuality"
                :placeholder="'请输入炉底'"
              />
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">3号转炉补炉情况</div>
              <el-input
                v-model="unfinished.gridData.stove3.D_BOFCD"
                :disabled="!canEditQuality"
                :placeholder="'请输入补炉情况'"
              />
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">3号转炉考核情况</div>
              <template>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in unfinished.updataImgurl.ImgView3
                    .showExamineUrl"
                    :key="index"
                    class="el-upload-list__item is-ready"
                  >
                    <img-view
                      :key="item"
                      :src="'http://*************:9084/orgApi2/boardUploadfiles/'+item"
                      :is-id="false"
                      :img-width="'95%'"
                      :deleteable="canEditQuality"
                      @img-delete="
                        handleImgDeleteAfter(
                          $event,
                          'ImgView3',
                          'showExamineUrl'
                        )
                      "
                    />
                  </li>
                </ul>
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in unfinished.updataImgurl.ImgView3
                    .showExamine"
                    :key="index"
                    class="el-upload-list__item is-ready"
                  >
                    <img-view
                      :key="item"
                      :src="item"
                      :is-id="false"
                      :img-width="'95%'"
                      :deleteable="canEditQuality"
                      @img-delete="
                        handleImgDelete($event, 'ImgView3', 'examine')
                      "
                    />
                  </li>
                </ul>
                <el-upload
                  v-if="canEditQuality"
                  ref="upload"
                  :auto-upload="false"
                  :show-file-list="false"
                  :http-request="httpRequest"
                  :on-change="
                    (file, fileList) =>
                      handleChange(file, fileList, 'ImgView3', 'examine')
                  "
                  multiple
                  list-type="picture-card"
                  action="#"
                  style="display: inline"
                >
                  <i class="el-icon-plus" />
                </el-upload>
              </template>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择"
    >
      <template v-slot:title>
        <div class="custom-dialog-title">导入日期选择</div>
      </template>
      <el-form 
        label-width="120px" 
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"
          />
        </el-form-item>
        <div class="text-center">
          <el-button 
            type="primary" 
            @click="importHistoryData()"
          >确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
// 数据混入
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import * as _ from 'lodash'
// 接口查询
import { post } from '@/lib/Util'
// 文件上传、删除、数据查询接口
import { firstMorningMeeting } from '@/api/screen'
// 图片预览组件
import ImgView from '@/components/ImgView.vue'
// 边框组件
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
// 接口结构
const {
  fileUpdata,
  fileDelete,
  bofcdDataUpdate,
  bofcdDataInit
} = firstMorningMeeting

export default {
  name: 'conditionConverter',
  components: { ImgView, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      editIndex: 0,
      tableHeight: null,
      unfinished: {
        gridData: {
          stove1: {},
          stove2: {},
          stove3: {}
        },
        details: [],
        showGridData: [
          {
            NAME: '测量情况'
          },
          {
            NAME: '后大面'
          },
          {
            NAME: '耳轴'
          },
          {
            NAME: '炉底'
          },
          {
            NAME: '补炉情况'
          },
          {
            NAME: '考核情况'
          }
        ],
        updataImgurl: {
          // 1炉
          ImgView1: {
            // 测量展示图片
            showMeasure: [],
            showMeasureUrl: [],
            // 测量删除文件
            delMeasure: [],
            // 测量上传文件
            measure: [],
            showExamine: [],
            showExamineUrl: [],
            delExamine: [],
            examine: []
          },
          ImgView2: {
            // 测量展示图片
            showMeasure: [],
            showMeasureUrl: [],
            // 测量删除文件
            delMeasure: [],
            // 测量上传文件
            measure: [],
            showExamine: [],
            showExamineUrl: [],
            delExamine: [],
            examine: []
          },
          ImgView3: {
            // 测量展示图片
            showMeasure: [],
            showMeasureUrl: [],
            // 测量删除文件
            delMeasure: [],
            // 测量上传文件
            measure: [],
            showExamine: [],
            showExamineUrl: [],
            delExamine: [],
            examine: []
          }
        },
        dialogVisible: false
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    // 监听时间改变查询
    cDate: function() {
      this.BofcdDataInits()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    // 初始化高度
    // this.tableHeight = this.$refs.tableHeight.offsetHeight
  },
  methods: {
    // 判断是不是json，是转
    getPictureList(data) {
      if (typeof data == 'string' && data.length) {
        try {
          return data.split(',')
        } catch (e) {
          return []
        }
      }
      return []
    },
    // 数据查询
    BofcdDataInits() {
      this.loading = true
      const showName = [
        'E_BOFCD',
        'A_BOFCD',
        'B_BOFCD',
        'C_BOFCD',
        'D_BOFCD',
        'F_BOFCD'
      ]
      const showData = [
        {
          NAME: '测量情况'
        },
        {
          NAME: '后大面'
        },
        {
          NAME: '耳轴'
        },
        {
          NAME: '炉底'
        },
        {
          NAME: '补炉情况'
        },
        {
          NAME: '考核情况'
        }
      ]
      post(bofcdDataInit, {
        startTime: this.cDate,
        endTime: this.cDate
      }).then(res => {
        this.loading = false
        if (res.code === 200) {
          const values = _.orderBy(res.data, ['FLAG'], ['asc'])
          this.unfinished.details[0] = _.cloneDeep(
            values[0] == undefined ? {} : values[0]
          )
          this.unfinished.details[1] = _.cloneDeep(
            values[1] == undefined ? {} : values[1]
          )
          this.unfinished.details[2] = _.cloneDeep(
            values[2] == undefined ? {} : values[2]
          )
          _.forEach(showName, (item, index) => {
            _.forEach(['A_LIST', 'B_LIST', 'C_LIST'], (item2, index2) => {
              showData[index][item2] =
                values[index2] != undefined
                  ? values[index2][item] != undefined
                    ? values[index2][item]
                    : ''
                  : ''
            })
          })
          this.unfinished.showGridData = showData
          this.showDialogVisible(this.unfinished.dialogVisible)
        }
      })
    },
    // 展示编辑框、数据清空
    showDialogVisible(bool = true) {
      this.unfinished.updataImgurl = {
        // 1炉
        ImgView1: {
          // 测量展示图片
          showMeasure: [],
          showMeasureUrl: [],
          // 测量删除文件
          delMeasure: [],
          // 测量上传文件
          measure: [],
          showExamine: [],
          showExamineUrl: [],
          delExamine: [],
          examine: []
        },
        ImgView2: {
          // 测量展示图片
          showMeasure: [],
          showMeasureUrl: [],
          // 测量删除文件
          delMeasure: [],
          // 测量上传文件
          measure: [],
          showExamine: [],
          showExamineUrl: [],
          delExamine: [],
          examine: []
        },
        ImgView3: {
          // 测量展示图片
          showMeasure: [],
          showMeasureUrl: [],
          // 测量删除文件
          delMeasure: [],
          // 测量上传文件
          measure: [],
          showExamine: [],
          showExamineUrl: [],
          delExamine: [],
          examine: []
        }
      }
      this.showDialogVisibleUpdata(bool)
    },
    // 存储数据
    showDialogVisibleUpdata(bool) {
      this.unfinished.gridData.stove1 = _.cloneDeep(this.unfinished.details[0])
      this.unfinished.gridData.stove2 = _.cloneDeep(this.unfinished.details[1])
      this.unfinished.gridData.stove3 = _.cloneDeep(this.unfinished.details[2])
      console.log(this.unfinished.details)
      this.unfinished.updataImgurl.ImgView1.showMeasureUrl =
        this.unfinished.details[0].E_BOFCD == undefined ||
        this.unfinished.details[0].E_BOFCD == ''
          ? []
          : _.cloneDeep(this.unfinished.details[0].E_BOFCD).split(',')

      this.unfinished.updataImgurl.ImgView1.showExamineUrl =
        this.unfinished.details[0].F_BOFCD == undefined ||
        this.unfinished.details[0].F_BOFCD == ''
          ? []
          : _.cloneDeep(this.unfinished.details[0].F_BOFCD).split(',')
      this.unfinished.updataImgurl.ImgView2.showMeasureUrl =
        this.unfinished.details[1].E_BOFCD == undefined ||
        this.unfinished.details[1].E_BOFCD == ''
          ? []
          : _.cloneDeep(this.unfinished.details[1].E_BOFCD).split(',')
      this.unfinished.updataImgurl.ImgView2.showExamineUrl =
        this.unfinished.details[1].F_BOFCD == undefined ||
        this.unfinished.details[1].F_BOFCD == ''
          ? []
          : _.cloneDeep(this.unfinished.details[1].F_BOFCD).split(',')
      this.unfinished.updataImgurl.ImgView3.showMeasureUrl =
        this.unfinished.details[2].E_BOFCD == undefined ||
        this.unfinished.details[2].E_BOFCD == ''
          ? []
          : _.cloneDeep(this.unfinished.details[2].E_BOFCD).split(',')
      this.unfinished.updataImgurl.ImgView3.showExamineUrl =
        this.unfinished.details[2].F_BOFCD == undefined ||
        this.unfinished.details[2].F_BOFCD == ''
          ? []
          : _.cloneDeep(this.unfinished.details[2].F_BOFCD).split(',')
      this.unfinished.dialogVisible = bool
    },
    // 本地图片上传预览
    async handleChange(file, _, name, pushName) {
      let imgName = ''
      if (pushName === 'measure') {
        imgName = 'showMeasure'
      } else if (pushName === 'examine') {
        imgName = 'showExamine'
      }
      this.unfinished.updataImgurl[name][imgName].push(file.url)
      this.unfinished.updataImgurl[name][pushName].push(file.raw)
    },
    // 上传前图片删除
    async handleImgDelete(file, name, delName) {
      let imgName = ''
      if (delName === 'measure') {
        imgName = 'showMeasure'
      } else if (delName === 'examine') {
        imgName = 'showExamine'
      }
      let delIndex = -1
      delIndex = this.unfinished.updataImgurl[name][imgName].indexOf(file.url)
      if (delIndex > -1) {
        this.unfinished.updataImgurl[name][imgName].splice(delIndex, 1)
        this.unfinished.updataImgurl[name][delName].splice(delIndex, 1)
      }
    },
    // 上传后图片删除
    async handleImgDeleteAfter(file, name, delName) {
      console.log(file, name, delName)
      let delIndex = -1
      delIndex = this.unfinished.updataImgurl[name][delName].indexOf(
        file.url.replace(
          'http://*************:9084/orgApi2/boardUploadfiles/',
          ''
        )
      )
      if (delIndex > -1) {
        this.unfinished.updataImgurl[name][delName].splice(delIndex, 1)
        this.unfinished.gridData[
          name.includes(1) ? 'stove1' : name.includes(2) ? 'stove2' : 'stove3'
        ][
          delName.includes('MeasureUrl') ? 'E_BOFCD' : 'F_BOFCD'
        ] = this.unfinished.updataImgurl[name][delName].toString()
        this.unfinished.updataImgurl[name][
          delName.replace('show', 'del').replace('Url', '')
        ].push(
          file.url.replace(
            'http://*************:9084/orgApi2/boardUploadfiles/',
            ''
          )
        )
      }
    },
    // 去除图片上传默认事件
    httpRequest(params) {},
    // 删除图片
    delImgs(delImg) {
      this.$message.info('正在删除图片请勿操作！！！')
      if (delImg.length) {
        // 图片删除
        post(fileDelete, delImg)
          .then(res => {
            if (res.code === 200) {
              this.saveData()
            } else {
              this.$message.error('图片删除失败！')
            }
          })
          .catch(err => {
            this.$message.error('图片删除失败！')
          })
      }
    },
    // 保存更新数据
    saveBofcdDataUpdates(data) {
      const dataValues = []
      Object.keys(data).forEach(item => {
        const value = _.cloneDeep(data[item])
        value.FLAG = item.replace('stove', '')
        value.PROD_DATE = this.cDate
        dataValues.push(value)
      })
      post(bofcdDataUpdate, dataValues)
        .then(res => {
          if (res.code == 200) {
            this.unfinished.dialogVisible = false
            this.BofcdDataInits()
            this.$message.success('保存成功！')
          } else {
            this.$message.error('保存失败！')
          }
          console.log(res)
        })
        .catch(err => {
          this.$message.error('保存失败！')
        })
    },
    // 上传图片
    uploadImg(fd, data) {
      this.$message.info('图片上传中...')
      // 将表单数据转成对象
      const ImageData = {
        ImgView1Measure:
          data.stove1.E_BOFCD == undefined || data.stove1.E_BOFCD == ''
            ? []
            : data.stove1.E_BOFCD.split(','),
        ImgView1Examine:
          data.stove1.F_BOFCD == undefined || data.stove1.F_BOFCD == ''
            ? []
            : data.stove1.F_BOFCD.split(','),
        ImgView2Measure:
          data.stove2.E_BOFCD == undefined || data.stove2.E_BOFCD == ''
            ? []
            : data.stove2.E_BOFCD.split(','),
        ImgView2Examine:
          data.stove2.F_BOFCD == undefined || data.stove2.F_BOFCD == ''
            ? []
            : data.stove2.F_BOFCD.split(','),
        ImgView3Measure:
          data.stove3.E_BOFCD == undefined || data.stove3.E_BOFCD == ''
            ? []
            : data.stove3.E_BOFCD.split(','),
        ImgView3Examine:
          data.stove3.F_BOFCD == undefined || data.stove3.F_BOFCD == ''
            ? []
            : data.stove3.F_BOFCD.split(',')
      }

      post(fileUpdata, fd)
        .then(res => {
          if (res.code == 200) {
            _.forEach(Object.keys(ImageData), key => {
              _.forEach(res.data, item => {
                if (item.data.includes(key)) {
                  ImageData[key].push(item.data)
                }
              })
            })
            this.$message.info('图片上传成功，开始保存数据！,请勿关闭！')
            data.stove1.E_BOFCD = ImageData.ImgView1Measure.toString()
            data.stove1.F_BOFCD = ImageData.ImgView1Examine.toString()
            data.stove2.E_BOFCD = ImageData.ImgView2Measure.toString()
            data.stove2.F_BOFCD = ImageData.ImgView2Examine.toString()
            data.stove3.E_BOFCD = ImageData.ImgView3Measure.toString()
            data.stove3.F_BOFCD = ImageData.ImgView3Examine.toString()
            this.saveBofcdDataUpdates(data)
          } else {
            this.$message.error('图片上传失败')
          }
        })
        .catch(err => {
          this.$message.error('图片上传失败')
        })
    },
    // 保存数据判断有没有图片
    saveData() {
      this.loading = true
      const fd = new FormData()
      const fdData = []
      _.forEach(['ImgView1', 'ImgView2', 'ImgView3'], item => {
        this.unfinished.updataImgurl[item].measure.forEach(file => {
          if (file) {
            fd.append(item + 'Measure', file)
            fdData.push(item + 'Measure')
          }
        })
        this.unfinished.updataImgurl[item].examine.forEach(file => {
          if (file) {
            fd.append(item + 'Examine', file)
            fdData.push(item + 'Examine')
          }
        })
      })
      // 创建传入后台数据
      const data = {}
      Object.keys(this.unfinished.gridData).forEach(item => {
        const value = _.cloneDeep(this.unfinished.gridData[item])
        value.FLAG = item.replace('stove', '')
        value.PROD_DATE = this.cDate
        data[item] = value
      })
      if (fdData.length) {
        this.uploadImg(fd, data)
      } else {
        this.saveBofcdDataUpdates(data)
      }
    },
    // 数据处理
    saveUnfinished() {
      let delImg = []
      _.forEach(['ImgView1', 'ImgView2', 'ImgView3'], item => {
        this.unfinished.updataImgurl[item].delMeasure.forEach(url => {
          if (url) {
            delImg.push(url)
          }
        })
        this.unfinished.updataImgurl[item].delExamine.forEach(url => {
          if (url) {
            delImg.push(url)
          }
        })
      })
      if (delImg.length) {
        this.delImgs(delImg)
      } else {
        this.saveData()
      }
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
  }
}
.screen-input {
  /deep/ .el-textarea__inner,
  /deep/ .el-input__inner {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
    padding-right: 5px;
  }
  /deep/ .el-input__prefix {
    color: #fff;
  }
  /deep/ .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    background: rgba(31, 198, 255, 0.3);
    border-color: rgba(31, 198, 255, 0.6);
    //border: 1px solid #1fc6ff;
  }
  /deep/ .el-radio-button--mini .el-radio-button__inner {
    background: #d8edff;
  }
}
</style>
