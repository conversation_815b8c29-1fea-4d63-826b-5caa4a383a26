<template>
  <div class="content">
    <div
      class="content-item"
      style="height: 40vh;">
      <screen-border title="原钢种收得率综合分析">
        <template v-slot:headerRight>
          <!-- <template v-if="steelmakingShow">
            <slot name="topRight"/>
          </template> -->
          <template>
            <!-- <span
              class="screen-btn"
              @click="FindYieldRateSteel()">
              <el-icon class="el-icon-search"/>
              查找
            </span> -->
            <button
              v-if="currentTime"
              class="screen-btn"
              @click="FindYieldRateSteel()">
              <el-icon class="el-icon-search"/>
              查找
            </button>
            <el-popconfirm
              v-else
              title="该时间段数据不稳定，请确认是否查找"
              @confirm="confirmSearch()"
              @cancel="cancelDelete()"
            >
              <el-button
                slot="reference"
                class="screen-btn">  <el-icon class="el-icon-search"/>查找</el-button>
            </el-popconfirm>
          </template>
          <el-select
            v-model="steelType"
            style="width:200px;margin-left: 5px;"
            filterable
            clearable
            multiple
            collapse-tags
            placeholder="请选择钢种">
            <el-option
              v-for="item in steelTypeAll"
              :key="item"
              :label="item"
              :value="item"/>
          </el-select>
        </template>
        <div class="chart-wrapper">
          <div
            v-loading="loading"
            style="flex: 1" >
            <bars-chart
              :unit="'%'"
              :last-month-data="stock1.blankLastMonth || '0'"
              :month-plan-data="stock1.blankMonthPlan || '0'"
              :show-legend="true"
              :bar-width="45"
              :chart-data="stock1.bar1"
              :x-data="stock1.barX"
              @selected="getDetectionDetailed($event)"/>
          </div>
        </div>
      </screen-border>
    </div>
    <div class="content-hold"/>
    <div
      class="content-item"
      style="height: 50vh"> 
      <custom-table8
        :title="'坯料' + subTimeTitle"
        :flag="flag.toString()"
        :setting="tableObj1.setting"
        :url-list="tableObj1.url.list"
        :url-save="tableObj1.url.save"
        :url-steel="tableObj1.url.steel"
        :select-date="selectDate"
        :table-class="'big-table'"
        :subtitle-show="true"
        @subtitle="showSubtitle"
        @steelTypeChange="handleSteelTypeChange"/>
    </div>
    <div class="content-hold"/>
    <div style="background-color: #041a21;">
      <div
        class="content-item"
        style="height: 50vh;"> 
        <custom-table
          :title="'钢板-原钢种' + subTimeTitle"
          :is-overall-title="false"
          :flag="flag.toString()"
          :setting="tableObj2.setting"
          :url-list="tableObj2.url.list"
          :url-save="tableObj2.url.save"
          :url-steel="tableObj2.url.steel"
          :select-date="selectDate"
          :steel-type-material="steelTypeMaterial"
          :subtitle-show="true"
          :table-class="'big-table'"
          @subtitle="showSubtitle"
        />
      </div>
      <div class="content-hold"/>
      <div
        class="content-item"
        style="height: 50vh;"
      > <custom-table
        :title="'钢板-综合' + subTimeTitle"
        :is-overall-title="true"
        :flag="flag.toString()"
        :setting="tableObj3.setting"
        :max-height="'100%'"
        :url-list="tableObj3.url.list"
        :url-save="tableObj3.url.save"
        :url-steel="tableObj3.url.steel"
        :select-date="selectDate"
        :steel-type-material="steelTypeMaterial"
        :table-class="'big-table'"
        :subtitle-show="true"
        @subtitle="showSubtitle"
      />
      </div>
    </div>
  </div>
</template>

 <script>
import ScreenBorder from '@/pages/screen/productionKpiScreen/component/screen-border'
import SingleBarsChart from '@/pages/screen/productionKpiScreen/component/single-bars-chart'
import SingleBarsChart1 from '@/pages/screen/productionKpiScreen/component/single-bars-chart1'
import SingleBarsChart2 from '@/pages/screen/productionKpiScreen/component/single-bars-chart2'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import BarsChart from '@/pages/screen/overallYieldrate/component/bars-chart'
import BarsChart2 from '@/pages/screen/productionKpiScreen/component/bars-chart2'
import StockLineChart from '@/pages/screen/morningMeeting/component/stock-line-chart'
import CustomTable from '@/pages/screen/overallYieldrate/component/custom-table5'
import CustomTable8 from '@/pages/screen/overallYieldrate/component/custom-table8'
import { post } from '@/lib/Util'
import {
  FindSteelGrade,
  FindSlabFullProcess,
  FindQltFullProcessy,
  FindQltFullProcessz
} from '@/api/screen'
import * as _ from 'lodash'
import {
  findBlankStock,
  findBlankStockPltZj,
  steelOutputTask,
  findSteelOutputByDate1,
  FindYieldRateSteel,
  FindYieldRateSteel1,
  FindYieldRateSteel2
} from '@/api/screen'
import { math } from '@/lib/Math'
import moment from 'moment'
import lodash from 'lodash'
export default {
  name: 'Output',
  components: {
    CustomTable,
    BarsChart,
    BarsChart2,
    CustomTable8,
    StockLineChart,
    SingleBarsChart,
    ScreenBorder,
    SingleBarsChart1,
    SingleBarsChart2
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      subTimeTitle: '',
      cDate: '',
      loading: true,
      currentTime: false,
      steelOutputTask: steelOutputTask,
      cDate2: [
        moment(new Date().getTime() - 7 * 24 * 1000 * 60 * 60).format(
          'YYYY-MM-DD'
        ),
        moment(new Date().getTime()).format('YYYY-MM-DD')
      ],
      steelType: [],
      steelTypeMaterial: '',
      steelTypeAll: '',
      flag: '4',
      stock1: {
        bar1: [],
        barX: [],
        gridData: [],
        dialogVisible: false,
        detailVisible: false,
        blankMonthPlan: '',
        blankLastMonth: ''
      },
      tableObj1: {
        url: {
          save: '',
          list: FindSlabFullProcess,
          steel: FindSteelGrade
        },
        setting: [
          // {
          //   keyQuery: 'index',
          //   keySave: 'index',
          //   label: '序号',
          //   type: 'index',
          //   fixed: true
          // },
          {
            keyQuery: 'plgz',
            keySave: 'plgz',
            label: '坯料钢种',
            width: 150
          },
          {
            keyQuery: 'gzhsdl',
            keySave: 'gzhsdl',
            label: '钢综合收得率',
            sort: true
          },
          {
            keyQuery: 'gygzsdl',
            keySave: 'gygzsdl',
            label: '钢原钢种收得率',
            sort: true
          },
          {
            keyQuery: 'plzscl',
            keySave: 'plzscl',
            label: '坯料总生产量',
            sort: true
          },
          {
            keyQuery: 'tdzl',
            keySave: 'tdzl',
            label: '替代重量',
            sort: true
          },
          {
            keyQuery: 'fpzl',
            keySave: 'fpzl',
            label: '废品重量',
            sort: true
          },
          {
            keyQuery: 'tdl',
            keySave: 'tdl',
            label: '替代率',
            sort: true
          },
          {
            keyQuery: 'fpl',
            keySave: 'fpl',
            label: '废品率',
            sort: true
          },
          {
            keyQuery: 'twpzl',
            keySave: 'twpzl',
            label: '其中：头尾坯重量',
            width: 200,
            sort: true
          },
          {
            keyQuery: 'twpbl',
            keySave: 'twpbl',
            label: '头尾坯比例',
            sort: true
          },
          {
            keyQuery: 'kcpzl',
            keySave: 'kcpzl',
            label: '库存坯重量',
            sort: true
          }
          // {
          //   keyQuery: 'endTime',
          //   keySave: 'endTime',
          //   label: '数据结束时间'
          // },
          // {
          //   keyQuery: 'startTime',
          //   keySave: 'startTime',
          //   label: '数据开始时间'
          // }
        ]
      },
      tableObj2: {
        url: {
          save: '',
          list: FindQltFullProcessy,
          steel: FindSteelGrade
        },
        setting: [
          // {
          //   keyQuery: 'index',
          //   keySave: 'index',
          //   label: '序号',
          //   type: 'index',
          //   fixed: true
          // },
          {
            keyQuery: 'plgz',
            keySave: 'plgz',
            label: '坯料钢种',
            width: 160
          },
          {
            keyQuery: 'qlcygzzpsdl',
            keySave: 'qlcygzzpsdl',
            label: '全流程原钢种正品收得率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'qlcygzzpsdlcxh',
            keySave: 'qlcygzzpsdlcxh',
            label: '全流程原钢种正品收得率（除现货）',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'cygzsdl',
            keySave: 'cygzsdl',
            label: '材原钢种收得率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzplrlzl',
            keySave: 'ygzplrlzl',
            label: '原钢种坯料入炉重量',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzplrlv',
            keySave: 'ygzplrlv',
            label: '原钢种坯料入炉率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzplsjgbzl',
            keySave: 'ygzplsjgbzl',
            label: '原钢种坯料实绩钢板重量',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzplsjgbgpxyl',
            keySave: 'ygzplsjgbgpxyl',
            label: '原钢种坯料实绩钢板改判协议量',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzplsjgbxhl',
            keySave: 'ygzplsjgbxhl',
            label: '原钢种坯料实绩钢板现货量',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzplzl',
            keySave: 'ygzplzl',
            label: '原钢种坯料重量',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzsjgbzlhpfl',
            keySave: 'ygzsjgbzlhpfl',
            label: '原钢种实绩钢板重量（含判废量）',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzsjgbzlbhpfl',
            keySave: 'ygzsjgbzlbhpfl',
            label: '原钢种实绩钢板重量（不含判废量）',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzpfl',
            keySave: 'ygzpfl',
            label: '原钢种判废量',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzpflv',
            keySave: 'ygzpflv',
            label: '原钢种废品率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzsjccl',
            keySave: 'ygzsjccl',
            label: '原钢种实际成材率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzgpxyl',
            keySave: 'ygzgpxyl',
            label: '原钢种改判协议量',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzgpxylv',
            keySave: 'ygzgpxylv',
            label: '原钢种改判协议率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'gzlv',
            keySave: 'gzlv',
            label: '改轧率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'cygzzpl',
            keySave: 'cygzzpl',
            label: '材原钢种正品率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'ygzzpxhl',
            keySave: 'ygzzpxhl',
            label: '原钢种正品现货量',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'cygzzpxhl',
            keySave: 'cygzzpxhl',
            label: '材原钢种正品现货率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'cygzzplvcxh',
            keySave: 'cygzzplvcxh',
            label: '材原钢种正品率（除现货）',
            sort: true,
            width: 160
          }
        ]
      },
      tableObj3: {
        url: {
          save: '',
          list: FindQltFullProcessz,
          steel: FindSteelGrade
        },
        setting: [
          // {
          //   keyQuery: 'index',
          //   keySave: 'index',
          //   label: '序号',
          //   type: 'index',
          //   fixed: true
          // },
          {
            keyQuery: 'plgz',
            keySave: 'plgz',
            label: '坯料钢种',
            width: 160
          },
          {
            keyQuery: 'qlczpsdl',
            keySave: 'qlczpsdl',
            label: '全流程正品收得率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'qlczpsdlcxh',
            keySave: 'qlczpsdlcxh',
            label: '全流程正品收得率（除现货）',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'plrlzl',
            keySave: 'plrlzl',
            label: '坯料入炉重量',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'plrlv',
            keySave: 'plrlv',
            label: '坯料入炉率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'sjgbzlhpfl',
            keySave: 'sjgbzlhpfl',
            label: '实绩钢板重量（含判废量）',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'sjgbzlbhpfl',
            keySave: 'sjgbzlbhpfl',
            label: '实绩钢板重量（不含判废量）',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'pfl',
            keySave: 'pfl',
            label: '判废量',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'fpl',
            keySave: 'fpl',
            label: '废品率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'sjccl',
            keySave: 'sjccl',
            label: '成材率-综合',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'gpxyl',
            keySave: 'gpxyl',
            label: '改判协议量',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'gpxylv',
            keySave: 'gpxylv',
            label: '改判协议率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'czpl',
            keySave: 'czpl',
            label: '材正品率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'zpxhl',
            keySave: 'zpxhl',
            label: '正品现货量',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'zpxhlv',
            keySave: 'zpxhlv',
            label: '正品现货率',
            sort: true,
            width: 160
          },
          {
            keyQuery: 'czplcxh',
            keySave: 'czplcxh',
            label: '材正品率（除现货）',
            sort: true,
            width: 160
          },

          {
            keyQuery: 'dpl',
            keySave: 'dpl',
            label: '待判量',
            sort: true,
            width: 160
          }
        ]
      }
    }
  },
  computed: {
    searchTime: function() {
      return moment(this.cDate).subtract(2, 'day')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.$nextTick(() => {
        this.loadData()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.loadData()
    this.getSteelData()
    this.checkAnyTimeRange()
    // this.FindYieldRateSteel()
    //  this.calculateHeight()
    //  window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    handleSteelTypeChange(type) {
      this.steelTypeMaterial = type
      console.log('%c type', 'color:red;', type)
    },
    async loadData() {
      this.$nextTick(() => {
        this.FindYieldRateSteel()
        // this.getZZP()
      })
    },
    isCurrentTimeInAnyRange(timeRanges) {
      // 获取当前时间
      const currentTime = moment()

      // 遍历所有时间范围
      for (let range of timeRanges) {
        const start = moment(range.start)
        const end = moment(range.end)

        // 判断当前时间是否在任何一个范围内（包括边界）
        if (currentTime.isBetween(start, end, null, '[]')) {
          return true // 如果在范围内，立即返回 true
        }
      }

      // 如果遍历完所有范围都不在内，则返回 false
      return false
    },
    checkAnyTimeRange() {
      // 定义多个时间范围
      const timeRanges = [
        {
          start: moment()
            .hour(0)
            .minute(50)
            .second(0)
            .millisecond(0),
          end: moment()
            .hour(2)
            .minute(10)
            .second(0)
            .millisecond(0)
            .subtract(1, 'second')
        },
        {
          start: moment()
            .hour(8)
            .minute(30)
            .second(0)
            .millisecond(0),
          end: moment()
            .hour(10)
            .minute(10)
            .second(0)
            .millisecond(0)
            .subtract(1, 'second')
        },
        {
          start: moment()
            .hour(16)
            .minute(50)
            .second(0)
            .millisecond(0),
          end: moment()
            .hour(18)
            .minute(10)
            .second(0)
            .millisecond(0)
            .subtract(1, 'second')
        }
        // {
        //   start: moment()
        //     .hour(12)
        //     .minute(0)
        //     .second(0)
        //     .millisecond(0),
        //   end: moment()
        //     .hour(13)
        //     .minute(10)
        //     .second(0)
        //     .millisecond(0)
        //     .subtract(1, 'second')
        // }
        // ... 可以添加更多时间范围
      ]

      const isInAnyRange = this.isCurrentTimeInAnyRange(timeRanges)

      if (isInAnyRange) {
        console.log('当前时间在某个指定范围内')
        this.currentTime = false
      } else {
        console.log('当前时间不在任何指定范围内')
        this.currentTime = true
      }
    },
    confirmSearch() {
      // 用户点击确认按钮时执行的逻辑
      this.FindYieldRateSteel()
    },
    cancelDelete() {
      console.log('删除操作已取消')
    },
    parsePercentageToNumber(percentageStr) {
      // 移除百分号
      let numberStr = percentageStr.replace('%', '')

      // 转换为数值
      let number = parseFloat(numberStr)

      // （可选）转换为小数（即比例值）
      // 如果你想要得到的是 0.5 而不是 50，则执行这一步
      let decimalNumber = number

      // 返回你需要的值，可以是原始数值或小数
      // return number; // 如果你只需要原始数值（50）
      return decimalNumber // 如果你需要小数（0.5）
    },
    getSteelData() {
      post(FindSteelGrade, {
        startDate: this.cDate2[0],
        endDate: this.cDate2[1]
      }).then(res => {
        let obj = []
        for (const item of res.data) {
          let data = item
          for (let key in item) {
            obj.push(key)
          }
        }
        this.steelTypeAll = obj
      })
    },
    async FindYieldRateSteel() {
      this.stock1.barX = []
      this.stock1.bar1 = [
        {
          name: '钢原钢种收得率',
          data: []
        },

        {
          name: '钢板综合收得率',
          data: []
        },
        {
          name: '钢板原钢种收得率',
          data: []
        }
      ]
      this.loading = true
      const parameters = await post(FindYieldRateSteel, {
        // setDate: this.searchTime.format('yyyy-MM')
        steelType: this.steelType
      })
      // const parameters1 = await post(FindYieldRateSteel1, {
      //   // setDate: this.searchTime.format('yyyy-MM')
      //   steelType: this.steelType
      // })
      // const parameters2 = await post(FindYieldRateSteel2, {
      //   // setDate: this.searchTime.format('yyyy-MM')
      //   steelType: this.steelType
      // })
      this.stock1.barX = parameters.data.map(item => item.nowDate)
      if (parameters.data) {
        this.loading = false
      }
      // console.log('aaaaa', this.stock1.barX)
      this.stock1.bar1 = [
        {
          name: '钢原钢种收得率',
          data: parameters.data.map(item =>
            this.parsePercentageToNumber(item.rate)
          )
        },

        {
          name: '材原钢种收得率',
          data: parameters.data.map(
            item => this.parsePercentageToNumber(item.zrate)
            // parseInt(item.zrate).replace('%', '')
          )
        },
        {
          name: '全流程原钢种收得率',
          data: parameters.data.map(
            item => this.parsePercentageToNumber(item.yrate)
            // parseInt().replace('%', '')
          )
        }
      ]
    },
    // 获取点击信息
    getDetectionDetailed(data) {
      this.flag = data.fromActionPayload.dataIndexInside + 1
    },
    showSubtitle(data) {
      this.subTimeTitle = data.startTime
        ? '(' + data.startTime + ' ~ ' + data.endTime + ')'
        : ''
    }
    // async getZZP() {
    //   const monthStart = moment(this.cDate).format('D') == 2
    //   // 参数
    //   const parameters = await post(findSteelOutputByDate1, {
    //     // setDate: this.searchTime.format('yyyy-MM')
    //     setDate: this.cDate
    //   })
    //   this.stock1.blankMonthPlan = this.getParam(
    //     'blankMonthPlan',
    //     parameters.data
    //   )
    //   this.stock1.blankLastMonth = this.getParam(
    //     'blankLastMonth',
    //     parameters.data
    //   )
    //   // 坯料
    //   const zrStock = await post(findBlankStock, {
    //     date: this.searchTime.format('yyyyMM')
    //   })
    //   const zrStock2 = monthStart
    //     ? await post(findBlankStock, {
    //         date: this.$moment(this.cDate).format('yyyyMM')
    //       })
    //     : { data: [] }
    //   const stockZj = await post(findBlankStockPltZj, {
    //     date: this.searchTime.format('yyyyMM')
    //   })
    //   const stockZj2 = monthStart
    //     ? await post(findBlankStockPltZj, {
    //         date: this.$moment(this.cDate).format('yyyyMM')
    //       })
    //     : { data: [] }
    //   this.stock1.barX = parameters.data.map(item => item.setdate.substr(5, 6))
    //   console.log('bbbb', this.stock1.barX)
    //   this.stock1.bar1 = [
    //     {
    //       name: '计划',
    //       data: parameters.data.map(item => parseInt(item.targetproduction))
    //     },
    //     {
    //       name: '实际',
    //       data: parameters.data.map(item => parseInt(item.cumulativeoutput))
    //     }
    //   ]
    // }
  }
}
</script>

 <style scoped lang="less">
// /deep/.screen-wrapper {
//   position: relative;
//   display: flex;
//   height: 200vh;
//   width: 100vw;
// }
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  // display: flex;
  flex-direction: column;
  .content-hold {
    height: 20px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .slick {
    height: 65px;
    position: relative;
    //top: -15px;
    margin-bottom: 15px;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    width: 90px;
    line-height: 24px;
    font-size: 16px;
    white-space: nowrap;
    color: #ffffff;
  }
  span:last-child {
    flex: 1;
    overflow: auto;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: left;
    font-size: 0;
    margin-left: 10px;
    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.green {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.scroll-wrapper {
  height: 100%;
}
/deep/.el-input--small .el-input__inner {
  height: 32px;
  line-height: 32px;
  background-color: #093d4d;
  color: #fff;
}
/deep/.el-table.el-table--border {
  border-width: 0px;
  border-color: #081f27;
}
.kpi-list {
  font-size: 0;
  .item {
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12.5%;
    img {
      width: 48px;
      margin-right: 10px;
    }
    .name {
      font-size: 18px;
      font-weight: 700;
      line-height: 18px;
      margin-bottom: 10px;
      letter-spacing: 0px;
      text-align: left;
    }
    .num {
      font-size: 32px;
      font-weight: 700;
      line-height: 32px;
      letter-spacing: 0px;
      text-align: left;
    }
    .unit {
      font-size: 20px;
      font-weight: 350;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
    }
  }
}
</style>
