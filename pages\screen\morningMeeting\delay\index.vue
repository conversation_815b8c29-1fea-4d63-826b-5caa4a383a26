<template>
  <div class="content">
    <div class="content-item">
      <screen-border title="延误停时分析">
        <div
          ref="table1"
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            :data="unfinished.showGridData"
            :max-height="unfinished.maxHeight"
            :row-class-name="getRowClass"
            class="font-table center-table"
            border>
            <el-table-column
              property="生产厂"
              label="厂别"
              width="120"/>
            <el-table-column
              property="产线"
              label="产线区域"/>
            <el-table-column
              property="责任部门"
              label="责任部门"/>
            <el-table-column
              property="专业"
              label="专业"
              width="80"/>
            <el-table-column
              property="延误类别"
              label="延误类别"/>
            <el-table-column
              property="停机时长"
              label="停机时长"
              width="100"/>
            <el-table-column
              property="停机开始时间"
              label="停机开始时间"
              width="180"/>
            <el-table-column
              property="停机结束时间"
              label="停机结束时间"
              width="180"/>
            <el-table-column
              property="班别"
              label="停机班别"
              width="100"/>
            <el-table-column
              property="备注"
              label="备注"/>
            <el-table-column
              property="状态"
              label="状态"
              width="80"/>
          </el-table>
        </div>
      </screen-border>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <screen-border title="延误停时汇总">
        <div
          ref="table2"
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            :data="delay.showGridData"
            :span-method="handleObjectSpanMethod"
            :max-height="delay.maxHeight"
            class="font-table center-table"
            border>
            <el-table-column
              property="timing"
              label="时间"/>
            <el-table-column
              property="typeName"
              label="类别"/>
            <el-table-column
              property="planName"
              label="性质"
            />
            <el-table-column
              label="总计">
              <el-table-column
                property="totalNum"
                label="次数">
                <template v-slot="{ row }">
                  <template v-if="row.timing === '昨天' && row.typeName === '生产'">
                    <a 
                      class="show-more-btn" 
                      @click="showDetail(row.planName)">{{ row.totalNum }}</a>
                  </template>
                  <template v-else>
                    {{ row.totalNum }}
                  </template>
                </template>
              </el-table-column>
              <el-table-column
                property="totalHour"
                label="时长"/>
            </el-table-column>
            <el-table-column
              label="B1">
              <el-table-column
                property="B1Num"
                label="次数"/>
              <el-table-column
                property="B1Hour"
                label="时长"/>
            </el-table-column>
            <el-table-column
              label="C1">
              <el-table-column
                property="C1Num"
                label="次数"/>
              <el-table-column
                property="C1Hour"
                label="时长"/>
            </el-table-column>
            <el-table-column
              label="C2">
              <el-table-column
                property="C2Num"
                label="次数"/>
              <el-table-column
                property="C2Hour"
                label="时长"/>
            </el-table-column>
            <el-table-column
              label="C3">
              <el-table-column
                property="C3Num"
                label="次数"/>
              <el-table-column
                property="C3Hour"
                label="时长"/>
            </el-table-column>
          </el-table>
        </div>
      </screen-border>
    </div>
    <!--热处理详情-->
    <el-dialog
      :visible.sync="delay.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          昨日{{ delay.detailFlag }}详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="detailTable"
        :row-class-name="getRowClass"
        class="center-table"
        border>
        <el-table-column
          property="生产厂"
          label="厂别"/>
        <el-table-column
          property="产线"
          label="产线区域"/>
        <el-table-column
          property="责任部门"
          label="责任部门"/>
        <el-table-column
          property="专业"
          label="专业"
          width="50"/>
        <el-table-column
          property="延误类别"
          label="延误类别"/>
        <el-table-column
          property="停机时长"
          label="停机时长"
          width="60"/>
        <el-table-column
          property="停机开始时间"
          label="停机开始时间"
          width="150"/>
        <el-table-column
          property="停机结束时间"
          label="停机结束时间"
          width="150"/>
        <el-table-column
          property="班别"
          label="停机班别"/>
        <el-table-column
          property="备注"
          label="备注"/>
        <el-table-column
          property="状态"
          label="状态"/>
      </el-table>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { batchUpdateResource } from '@/api/system'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import {
  findDelayDataToBC,
  findDelayNumAndHourToBC,
  findSteelmakingReplayByDate,
  saveSteelmakingReplay
} from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { math } from '@/lib/Math'
export default {
  name: 'delay',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      unfinished: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      delay: {
        gridData: [],
        detailFlag: '非计划',
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      unitList: ['吨数', '炉数'],
      spanArr: {},
      position: 0
    }
  },
  computed: {
    detailTable: function() {
      return this.unfinished.showGridData.filter(
        item => item['是否计划'] === this.delay.detailFlag
      )
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getUnfinished()
      this.getDelay()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
  },
  methods: {
    calculateHeight() {
      this.unfinished.maxHeight = this.$refs.table1.offsetHeight
      this.delay.maxHeight = this.$refs.table2.offsetHeight
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          setTime: 'A',
          dayPlanFurnace: 'B',
          dayRealityFurnace: 'C',
          dayComplete: 'D',
          classes: 'E',
          classPlanFurnace: 'F',
          classRealityFurnace: 'G',
          classComplete: 'H',
          underproduction: 'I',
          cause: 'J'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unfinished.gridData = sheet
          .filter(item => item.setTime !== '日期')
          .map(item => {
            item.setTime = item.setTime
              ? LAY_EXCEL.dateCodeFormat(item.setTime, 'MM月DD日')
              : ''
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportunfinished() {
      const data = [
        {
          setTime: '日期',
          dayPlanFurnace: '当日计划炉数',
          dayRealityFurnace: '当日实际炉数',
          dayComplete: '当日是否完成',
          classes: '班次',
          classPlanFurnace: '当班计划炉数',
          classRealityFurnace: '当班实际炉数',
          classComplete: '当班是否完成',
          underproduction: '欠产情况',
          cause: '原因分析'
        }
      ].concat(_.cloneDeep(this.unfinished.gridData))
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `炼钢日计划完成情况复盘（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    getUnfinished() {
      post(findDelayDataToBC, {
        startDate: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD'),
        endDate: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD'),
        current: 1,
        size: 500
      }).then(res => {
        //
        this.loading = false
        this.$emit('statusChange', !res.data.length)
        const data = res.data.map(item => {
          const timeStr = item['停机开始时间'].substring(11, 19)
          console.log(timeStr)
          if (timeStr < '08:00:00') {
            item['班别'] = '大夜班'
          } else if (timeStr < '16:00:00') {
            item['班别'] = '白班'
          } else {
            item['班别'] = '小夜班'
          }
          item.plan = item['是否计划'] === '非计划' ? 0 : 1
          item.fac = null
          switch (item['生产厂']) {
            case '第一炼钢厂':
              item.fac = 'B1'
              break
            case '中厚板卷厂':
              item.fac = 'C1'
              break
            case '宽厚板厂':
              item.fac = 'C2'
              break
            case '中板厂':
              item.fac = 'C3'
              break
            default:
              item.fac = 'C3'
          }
          return item
        })
        this.unfinished.showGridData = _.sortBy(
          data,
          ['plan', 'fac'],
          ['asc', 'desc']
        )
        this.unfinished.gridData = lodash.cloneDeep(
          this.unfinished.showGridData
        )
      })
    },
    async getDelay() {
      const today = await post(findDelayNumAndHourToBC, {
        startDate: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD'),
        endDate: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD'),
        current: 1,
        size: 500
      })
      const dayData = this.formatData(today.data, '昨天')
      const month = await post(findDelayNumAndHourToBC, {
        startDate: this.$moment(this.cDate)
          .startOf('month')
          .format('yyyy-MM-DD'),
        endDate: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD'),
        current: 1,
        size: 500
      })
      const monthData = this.formatData(month.data, '当月')
      this.getSpanData(dayData.concat(monthData))
      this.delay.showGridData = dayData.concat(monthData)
    },
    formatData(data, timing) {
      const B1 = this.matchData(data, '*********')
      const C1 = this.matchData(data, 'X32000000')
      const C2 = this.matchData(data, 'X38000000')
      const C3 = this.matchData(data, 'X66000000')
      const arr = { B1, C1, C2, C3 }

      /*isPlanEquipNum：设备计划次数
      isPlanEquipHour：设备计划时长
      notPlanEquipNum：设备非计划次数
      notPlanEquipHour：设备非计划时长
      isPlanProductNum：生产计划次数
      isPlanProductHour：生产计划时长
      notPlanProductNum：生产非计划次数
      notPlanProductHour：生产非计划时长*/
      const keys = [
        {
          keyName: 'isPlanProduct',
          name: '计划',
          type: '生产'
        },
        {
          keyName: 'notPlanProduct',
          name: '非计划',
          type: '生产'
        },
        {
          keyName: 'isPlanEquip',
          name: '计划',
          type: '设备'
        },
        {
          keyName: 'notPlanEquip',
          name: '非计划',
          type: '设备'
        }
      ]
      const result = keys.map(item => {
        const obj = {}
        Object.keys(arr).forEach(fac => {
          obj[fac + 'Num'] = arr[fac][item.keyName + 'Num']
          obj[fac + 'Hour'] = arr[fac][item.keyName + 'Hour']
          obj['planName'] = item.name
          obj['typeName'] = item.type
          obj['timing'] = timing
        })
        let totalNum = 0
        let totalHour = 0
        Object.keys(arr).forEach(fac => {
          totalNum = math.add(totalNum, obj[fac + 'Num'])
          totalHour = math.add(totalHour, obj[fac + 'Hour'])
        })
        obj.totalNum = totalNum
        obj.totalHour = totalHour
        return obj
      })
      return result
    },
    // 计算需要合并的单元格
    getSpanData(data) {
      ;['typeName', 'timing'].forEach(keyName => {
        this.spanArr[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
    },
    matchData(data, org) {
      //factoryName: "第一炼钢厂"
      // factoryNo: "*********"
      // isPlanEquipHour: 301.26
      // isPlanEquipNum: 61
      // isPlanProductHour: 182.81
      // isPlanProductNum: 230
      // notPlanEquipHour: 1.25
      // notPlanEquipNum: 1
      // notPlanProductHour: 37.62
      // notPlanProductNum: 26
      const match = data.find(item => item.factoryNo === org)
      return match ? match : {}
    },
    showDetail(flag) {
      this.delay.detailFlag = flag
      this.delay.dialogVisible = true
    },
    // 合并单元格
    handleObjectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 需要合并的列
      // [0, 1, 2].includes(columnIndex ), 表示合并前三列
      if (['typeName', 'timing'].includes(column.property)) {
        const _row = this.spanArr[column.property][rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getRowClass(row) {
      console.log(row)
      if (row.row['是否计划'] === '非计划') {
        return 'red-row'
      } else {
        return ''
      }
    },
    saveUnfinished() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.unfinished.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(saveSteelmakingReplay, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.unfinished.dialogVisible = false
          this.getUnfinished()
        }
      })
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
      if ([0, 1, 2, 3].includes(columnIndex)) {
        if (rowIndex === 0) {
          return [3, 1]
        } else {
          return [0, 0]
        }
      }
    },
    getMergeData(rowIndex, columnIndex) {
      const matchLeftTop = this.unfinished.gridMerge.find(
        item => item.s.c === columnIndex && item.s.r === rowIndex
      )
      if (matchLeftTop) {
        return [
          matchLeftTop.e.r - matchLeftTop.s.r + 1,
          matchLeftTop.e.c - matchLeftTop.s.c + 1
        ]
      }
      const merged = this.unfinished.gridMerge.find(item => {
        return (
          item.s.c < columnIndex &&
          columnIndex <= item.e.c &&
          item.s.r < rowIndex &&
          rowIndex <= item.e.r
        )
      })
      if (merged) {
        console.log(merged)
        return [0, 0]
      }
    },
    importUnfinishedData(date) {
      post(findSteelmakingReplayByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.unfinished.gridData = res.data.map(item => {
          return {
            setTime: item.settime,
            dayPlanFurnace: item.dayplanfurnace,
            dayRealityFurnace: item.dayrealityfurnace,
            dayComplete: item.daycomplete,
            classes: item.classes,
            classPlanFurnace: item.classplanfurnace,
            classRealityFurnace: item.classrealityfurnace,
            classComplete: item.classcomplete,
            underproduction: item.underproduction,
            cause: item.cause
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.show-more-btn {
  border-bottom: 1px solid #fff;
  cursor: pointer;
}
</style>
