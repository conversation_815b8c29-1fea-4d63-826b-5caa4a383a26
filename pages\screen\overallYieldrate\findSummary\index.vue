<template>
  <div class="content">
    <div class="content-item">

      <custom-table4
        :title="'汇总表'"
        :setting="tableObj1.setting"
        :url-list="tableObj1.url.list"
        :url-save="tableObj1.url.save"
        :url-steel="tableObj1.url.steel"
        :select-date="selectDate"
        :table-class="'big-table'"/>
    </div>
  </div>
</template>

<script>
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import * as _ from 'lodash'
import {
  FullProcessYieldFindSummaryDate,
  FindSteelGrade,
  TripleOrderTrackSave
} from '@/api/screen'
import moment from 'moment'
import CustomTable4 from '@/pages/screen/overallYieldrate/component/custom-table4'

export default {
  name: 'findSummary',
  components: { CustomTable4 },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      editIndex: null,
      tableObj1: {
        url: {
          save: '',
          list: FullProcessYieldFindSummaryDate,
          steel: FindSteelGrade
        },
        setting: [
          {
            keyQuery: 'startDate',
            keySave: 'startDate',
            label: '开始时间',
            width: 120,
            fixed: true
          },
          {
            keyQuery: 'endDate',
            keySave: 'endDate',
            label: '结束时间',
            width: 120,
            fixed: true
          },
          {
            keyQuery: 'plgz',
            keySave: 'plgz',
            label: '钢种说明',
            width: 150,
            fixed: true
          },
          {
            keyQuery: 'cygzsdl',
            keySave: 'cygzsdl',
            label: '材原钢种收得率',
            width: 150
          },
          {
            keyQuery: 'gygzsdl',
            keySave: 'gygzsdl',
            label: '钢原钢种收得率',
            width: 150
          },
          {
            keyQuery: 'gzhsdl',
            keySave: 'gzhsdl',
            label: '钢综合收得率',
            width: 150
          },
          {
            keyQuery: 'qlczpsdl',
            keySave: 'qlczpsdl',
            label: '全流程正品收得率',
            width: 160
          },
          {
            keyQuery: 'qlczpsdlcxh',
            keySave: 'qlczpsdlcxh',
            label: '全流程正品收得率（除现货）',
            width: 180
          },
          {
            keyQuery: 'qlcygzzpsdlcxh',
            keySave: 'qlcygzzpsdlcxh',
            label: '全流程原钢种正品收得率（除现货）',
            width: 200
          },
          {
            keyQuery: 'plzscl',
            keySave: 'plzscl',
            label: '坯料总生产量',
            width: 150
          },
          {
            keyQuery: 'twpzl',
            keySave: 'twpzl',
            label: '头尾坯重量',
            width: 150
          },
          {
            keyQuery: 'twpbl',
            keySave: 'twpbl',
            label: '头尾坯比例',
            width: 150
          },
          {
            keyQuery: 'tdzl',
            keySave: 'tdzl',
            label: '替代重量',
            width: 150
          },
          {
            keyQuery: 'fpzl',
            keySave: 'fpzl',
            label: '废品重量',
            width: 150
          },
          {
            keyQuery: 'tdl',
            keySave: 'tdl',
            label: '替代率',
            width: 150
          },
          {
            keyQuery: 'fpl',
            keySave: 'fpl',
            label: '废品率',
            width: 150
          },
          {
            keyQuery: 'kcpzl',
            keySave: 'kcpzl',
            label: '库存坯重量',
            width: 150
          },
          {
            keyQuery: 'ygzplrlzl',
            keySave: 'ygzplrlzl',
            label: '原钢种坯料入炉重量',
            width: 150
          },
          {
            keyQuery: 'ygzplrll',
            keySave: 'ygzplrll',
            label: '原钢种坯料入炉率',
            width: 150
          },
          {
            keyQuery: 'ygzplsjgbzl',
            keySave: 'ygzplsjgbzl',
            label: '原钢种坯料实绩钢板重量',
            width: 150
          },
          {
            keyQuery: 'ygzplsjgbgpxyl',
            keySave: 'ygzplsjgbgpxyl',
            label: '原钢种坯料实绩钢板改判协议量',
            width: 150
          },
          {
            keyQuery: 'ygzplsjgbxhl',
            keySave: 'ygzplsjgbxhl',
            label: '原钢种坯料实绩钢板现货量',
            width: 150
          },
          {
            keyQuery: 'ygzplzl',
            keySave: 'ygzplzl',
            label: '原钢种坯料重量',
            width: 150
          },
          {
            keyQuery: 'ygzsjgbzlhpfl',
            keySave: 'ygzsjgbzlhpfl',
            label: '原钢种实绩钢板重量（含判废量）',
            width: 150
          },
          {
            keyQuery: 'ygzsjgbzlbhpfl',
            keySave: 'ygzsjgbzlbhpfl',
            label: '原钢种实绩钢板重量（不含判废量）',
            width: 150
          },
          {
            keyQuery: 'ygzpfl',
            keySave: 'ygzpfl',
            label: '原钢种判废量',
            width: 150
          },
          {
            keyQuery: 'ygzfpl',
            keySave: 'ygzfpl',
            label: '原钢种废品率',
            width: 150
          },
          {
            keyQuery: 'ygzsjccl',
            keySave: 'ygzsjccl',
            label: '原钢种实际成材率',
            width: 150
          },
          {
            keyQuery: 'ygzgpxyl',
            keySave: 'ygzgpxyl',
            label: '原钢种改判协议量',
            width: 150
          },
          {
            keyQuery: 'ygzgpxylv',
            keySave: 'ygzgpxylv',
            label: '原钢种改判协议率',
            width: 150
          },
          {
            keyQuery: 'cygzzpl',
            keySave: 'cygzzpl',
            label: '材原钢种正品率',
            width: 150
          },
          {
            keyQuery: 'cygzzpxhl',
            keySave: 'cygzzpxhl',
            label: '材原钢种正品现货量',
            width: 150
          },
          {
            keyQuery: 'cygzzpxhl',
            keySave: 'cygzzpxhl',
            label: '材原钢种正品现货率',
            width: 150
          },
          {
            keyQuery: 'cygzzplcxh',
            keySave: 'cygzzplcxh',
            label: '材原钢种正品率（除现货）',
            width: 150
          },
          {
            keyQuery: 'plrlzl',
            keySave: 'plrlzl',
            label: '坯料入炉重量',
            width: 150
          },
          {
            keyQuery: 'plrll',
            keySave: 'plrll',
            label: '坯料入炉率',
            width: 150
          },
          {
            keyQuery: 'sjgbzlhpfl',
            keySave: 'sjgbzlhpfl',
            label: '实绩钢板重量（含判废量）',
            width: 150
          },
          {
            keyQuery: 'sjgbzlbhpfl',
            keySave: 'sjgbzlbhpfl',
            label: '实绩钢板重量（不含判废量）',
            width: 150
          },
          {
            keyQuery: 'pfl',
            keySave: 'pfl',
            label: '判废量',
            width: 100
          },
          {
            keyQuery: 'fplv',
            keySave: 'fplv',
            label: '废品率'
          },
          {
            keyQuery: 'sjccl',
            keySave: 'sjccl',
            label: '实际成材率'
          },
          {
            keyQuery: 'gpxyl',
            keySave: 'gpxyl',
            label: '改判协议量'
          },
          {
            keyQuery: 'gpxylv',
            keySave: 'gpxylv',
            label: '改判协议率'
          },
          {
            keyQuery: 'czpl',
            keySave: 'czpl',
            label: '材正品率',
            width: 100
          },
          {
            keyQuery: 'zpxhl',
            keySave: 'zpxhl',
            label: '正品现货量'
          },
          {
            keyQuery: 'zpxhlv',
            keySave: 'zpxhlv',
            label: '正品现货率'
          },
          {
            keyQuery: 'czplcxh',
            keySave: 'czplcxh',
            label: '材正品率(除现货)',
            width: 100
          },
          {
            keyQuery: 'dpl',
            keySave: 'dpl',
            label: '待判量'
          }
        ]
      }
    }
  },
  computed: {
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'month')
        .format('yyyy-MM')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      // this.getpilotPlan()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    calculate() {
      // this.pilotPlan1.maxHeight = this.$refs.table1.offsetHeight
    },
    totalClass(row) {
      if (row.row.serialNumber && row.row.serialNumber.trim() === '合计') {
        return 'table-total'
      }
      return ''
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.edit-btn {
  margin: 0 3px;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.dialog-item-tit {
  font-size: 18px;
  color: #dcdcdc;
  margin-bottom: 8px;
  margin-top: 15px;
}
</style>
