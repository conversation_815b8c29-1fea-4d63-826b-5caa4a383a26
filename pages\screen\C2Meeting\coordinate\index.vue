<template>
  <div class="content">
    <div   
      class="content-item" >
      <screen-border-multi>
        <template v-slot:title>
          <div class="tabs-class">
            <div
              v-for="(item) in tabList"
              :key="item.id"
              :class="{'tab-pane-active': activeFllow === item.id}"
              class="tab-pane"
              @click="activeFllow = item.id">
              <div class="tab-pane-title-class">
                <div>{{ item.title }}</div>
                <div
                  v-if="activeFllow === item.id"
                  class="tab-pane-img">
                  <img
                    class="tab-pane-img2"
                    src="@/assets/images/screen/tab-pane-active-line2.png"
                    alt="">
                  <img
                    class="tab-pane-img1"
                    src="@/assets/images/screen/tab-pane-active-line.png"
                    alt="">
                </div>
              </div>
            </div>
          </div>
        </template>
     
        <screen-border 
          v-if="activeFllow === '1'">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/C2Meeting/coordinate'"
              class="screen-btn"
              @click="pilotPlan1.dialogVisible = true">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <div 
            ref="table1" 
            class="scroll-wrapper">
            <el-table
              v-loading="loading"
              ref="tableShow"
              :data="pilotPlan1.showGridData"
              :span-method="handleObjectSpan"
              :max-height="pilotPlan1.maxHeight"
              :row-class-name="totalClass"
              :size="'medium'"
              class="center-table font-big-table"
              border>
              <el-table-column
                type="index"
                label="序号"
                width="60"/>
              <el-table-column
                property="backlog"
                label="待办事项内容"/>
              <el-table-column
                property="unit"
                label="责任单位"/>
              <el-table-column
                property="proposedTime"
                label="提出时间"
                width="120"/>
              <el-table-column
                v-if="!isFactoryUser"
                property="completionTime"
                label="完成时间"
                width="120"/>
              <el-table-column
                v-if="!isFactoryUser"
                :label="'完成情况'"
                property="completionStatus"/>
              <el-table-column
                :label="'销项确认'"
                align="left"
                property="salesConfirmation"/>
              <el-table-column
                :label="'销项时间'"
                align="left"
                property="closingTime"/>
              <el-table-column
                :label="'反馈人'"
                align="left"
                property="feedbackPerson"/>
            </el-table>
          
          </div>
        </screen-border>
       
       
      
      
        <screen-border 
          v-if="activeFllow === '2'">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/C2Meeting/coordinate'"
              class="screen-btn"
              @click="pilotPlan2.dialogVisible = true">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <div 
            ref="table1" 
            class="scroll-wrapper">
            <el-table
              v-loading="loading2"
              ref="tableShow"
              :data="pilotPlan2.showGridData"
              :span-method="handleObjectSpan"
              :max-height="pilotPlan2.maxHeight"
              :row-class-name="totalClass"
              :size="'medium'"
              class="center-table font-big-table"
              border>
              <el-table-column
                type="index"
                label="序号"
                width="60"/>
              <el-table-column
                property="BACKLOG"
                label="待办事项内容"/>
              <el-table-column
                property="UNIT"
                label="责任单位"/>
              <el-table-column
                property="PROPOSEDTIME"
                label="提出时间"
                width="120"/>
              <el-table-column
                v-if="!isFactoryUser"
                property="COMPLETIONTIME"
                label="完成时间"
                width="120"/>
              <el-table-column
                v-if="!isFactoryUser"
                :label="'完成情况'"
                property="COMPLETIONSTATUS"/>
              <el-table-column
                :label="'销项确认'"
                align="left"
                property="SALESCONFIRMATION"/>
              <el-table-column
                :label="'销项时间'"
                align="left"
                property="CLOSINGTIME"/>
              <el-table-column
                :label="'反馈人'"
                align="left"
                property="FEEDBACKPERSON"/>
            </el-table>
          
          </div>
        </screen-border>
        <screen-border 
          v-if="activeFllow === '3'">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/C2Meeting/coordinate'"
              class="screen-btn"
              @click="pilotPlan3.dialogVisible = true">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <div 
            ref="table1" 
            class="scroll-wrapper">
            <el-table
              v-loading="loading3"
              ref="tableShow"
              :data="pilotPlan3.showGridData"
              :span-method="handleObjectSpan"
              :max-height="pilotPlan3.maxHeight"
              :row-class-name="totalClass"
              :size="'medium'"
              class="center-table font-big-table"
              border>
              <el-table-column
                type="index"
                label="序号"
                width="60"/>
              <el-table-column
                property="backlog"
                label="待办事项内容"/>
              <el-table-column
                property="unit"
                label="责任单位"/>
              <el-table-column
                property="proposedTime"
                label="提出时间"
                width="120"/>
              <el-table-column
                v-if="!isFactoryUser"
                property="completionTime"
                label="完成时间"
                width="120"/>
              <el-table-column
                v-if="!isFactoryUser"
                :label="'完成情况'"
                property="completionStatus"/>
              <el-table-column
                :label="'销项确认'"
                align="left"
                property="salesConfirmation"/>
              <el-table-column
                :label="'销项时间'"
                align="left"
                property="closingTime"/>
              <el-table-column
                :label="'反馈人'"
                align="left"
                property="feedbackPerson"/>
            </el-table>
          
          </div>
        </screen-border>
        <screen-border 
          v-if="activeFllow === '4'">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/C2Meeting/coordinate'"
              class="screen-btn"
              @click="pilotPlan4.dialogVisible = true">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <div 
            ref="table1" 
            class="scroll-wrapper">
            <el-table
              v-loading="loading4"
              ref="tableShow"
              :data="pilotPlan4.showGridData"
              :span-method="handleObjectSpan"
              :row-class-name="totalClass"
              :size="'medium'"
              :max-height="pilotPlan3.maxHeight"
              class="center-table font-big-table"
              border>
              <el-table-column
                type="index"
                label="序号"
                width="60"/>
              <el-table-column
                property="planType"
                label="类别"/>
              <el-table-column
                v-if="!isFactoryUser"
                property="supervisionContent"
                label="督办内容"
                width="120"/>
             
              <el-table-column
                property="measures"
                label="措施"
                width="500"/>
              <el-table-column
                property="endData"
                label="计划完成时间"/>
              <el-table-column
                :label="'责任人'"
                align="left"
                property="projectManager"/>
              <el-table-column
                :label="'赋能单位'"
                align="left"
                property="supportUnit"/>
              <el-table-column
                v-if="!isFactoryUser"
                :label="monthData+'月7日完成情况'"
                :width="170"
                property="status7Day"/>
              <el-table-column
                :label="monthData+'月17日完成情况'"
                :width="180"
                align="left"
                property="status17Day"/>
              <el-table-column
                :label="monthData+'月27日完成情况'"
                :width="170"
                align="left"
                property="status27Day"/>
             
            </el-table>
          
          </div>
        </screen-border>
    </screen-border-multi></div>

    <!--工艺绩效评价-->
    <el-dialog
      :visible.sync="pilotPlan1.dialogVisible"
      :width="'95%'"
      :top="'50px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="$refs.tableEdit.clearFilter()">
              清除筛选
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              type="date"
              @change="changeDate"/>
            <template>
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
            </template>
            <span
              class="screen-btn"
              @click="exportpilotPlan">
              导出
            </span>
          </div>
          跟踪事项
        </div>
      </template>
      <el-form>
        <el-table
          v-loading="loading"
          ref="tableEdit"
          :data="pilotPlan1.gridData"
          :max-height="tableMaxHeight"
          class="center-table"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60"/>
          <el-table-column
            property="backlog"
            label="待办事项内容">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.backlog"
                :rows="4"
                type="textarea"/>
              <template v-else>
                {{ row.backlog }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="unit"
            label="责任单位">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.unit"/>
              <template v-else>
                {{ row.unit }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="proposedTime"
            label="提出时间"
            width="120">
            <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.proposedTime"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>
                {{ row.proposedTime }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            property="completionTime"
            label="完成时间"
            width="120">
            <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.completionTime"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>
                {{ row.completionTime }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            :label="'完成情况'"
            property="completionStatus">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.completionStatus"/>
              <template v-else>
                {{ row.completionStatus }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'销项确认'"
            align="left"
            property="salesConfirmation">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.salesConfirmation"/>
              <template v-else>
                {{ row.salesConfirmation }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'销项时间'"
            align="left"
            property="closingTime">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.closingTime"/>
              <template v-else>
                {{ row.closingTime }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'反馈人'"
            align="left"
            property="feedbackPerson">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.feedbackPerson"/>
              <template v-else>
                {{ row.feedbackPerson }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'操作'"
            fixed="right"
            property="proofResult">
            <template v-slot="{ row, $index}">
              <!-- 部分编辑-->
              <el-button
                v-if="$index !== editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="editItem(pilotPlan1.gridData, $index, 1)">编辑</el-button>
              <el-button
                v-if="$index === editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="savepilotPlanItem(row)">保存</el-button>
              <el-button
                class="screen-btn edit-btn"
                type="text"
                @click="deleteItem(row, $index)">删除</el-button>
            </template>
          </el-table-column>
  
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          class="screen-btn"
          style="margin-top: 10px"
          @click="addGridData('pilotPlan1');editIndex = pilotPlan1.gridData.length - 1">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="pilotPlan2.dialogVisible"
      :width="'95%'"
      :top="'50px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="$refs.tableEdit.clearFilter()">
              清除筛选
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              type="date"
              @change="changeDate"/>
            <template>
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview2"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
            </template>
            <span
              class="screen-btn"
              @click="exportpilotPlan2">
              导出
            </span>
          </div>
          持续跟踪事项
        </div>
      </template>
      <el-form>
        <el-table
          v-loading="loading2"
          ref="tableEdit"
          :data="pilotPlan2.gridData"
          :max-height="tableMaxHeight"
          class="center-table"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60"/>
          <el-table-column
            property="BACKLOG"
            label="待办事项内容">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.BACKLOG"
                :rows="4"
                type="textarea"/>
              <template v-else>
                {{ row.BACKLOG }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="UNIT"
            label="责任单位">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.UNIT"/>
              <template v-else>
                {{ row.UNIT }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="PROPOSEDTIME"
            label="提出时间"
            width="120">
            <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.PROPOSEDTIME"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>
                {{ row.PROPOSEDTIME }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            property="COMPLETIONTIME"
            label="完成时间"
            width="120">
            <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.COMPLETIONTIME"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>
                {{ row.COMPLETIONTIME }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            :label="'完成情况'"
            property="COMPLETIONSTATUS">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.COMPLETIONSTATUS"/>
              <template v-else>
                {{ row.COMPLETIONSTATUS }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'销项确认'"
            align="left"
            property="SALESCONFIRMATION">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.SALESCONFIRMATION"/>
              <template v-else>
                {{ row.SALESCONFIRMATION }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'销项时间'"
            align="left"
            property="CLOSINGTIME">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.CLOSINGTIME"/>
              <template v-else>
                {{ row.CLOSINGTIME }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'反馈人'"
            align="left"
            property="FEEDBACKPERSON">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.FEEDBACKPERSON"/>
              <template v-else>
                {{ row.FEEDBACKPERSON }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'操作'"
            fixed="right"
            property="proofResult">
            <template v-slot="{ row, $index}">
              <!-- 部分编辑-->
              <el-button
                v-if="$index !== editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="editItem(pilotPlan2.gridData, $index, 1)">编辑</el-button>
              <el-button
                v-if="$index === editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="savepilotPlanItem2(row)">保存</el-button>
              <el-button
                class="screen-btn edit-btn"
                type="text"
                @click="deleteItem2(row, $index)">删除</el-button>
            </template>
          </el-table-column>
  
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          class="screen-btn"
          style="margin-top: 10px"
          @click="addGridData('pilotPlan2');editIndex = pilotPlan2.gridData.length - 1">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="pilotPlan3.dialogVisible"
      :width="'95%'"
      :top="'50px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="$refs.tableEdit.clearFilter()">
              清除筛选
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              type="date"
              @change="changeDate"/>
            <template>
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview3"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
            </template>
            <span
              class="screen-btn"
              @click="exportpilotPlan3">
              导出
            </span>
          </div>
          月度跟踪事项
        </div>
      </template>
      <el-form>
        <el-table
          v-loading="loading3"
          ref="tableEdit"
          :data="pilotPlan3.gridData"
          :max-height="tableMaxHeight"
          class="center-table"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60"/>
          <el-table-column
            property="backlog"
            label="待办事项内容">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.backlog"
                :rows="4"
                type="textarea"/>
              <template v-else>
                {{ row.backlog }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="unit"
            label="责任单位">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.unit"/>
              <template v-else>
                {{ row.unit }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="proposedTime"
            label="提出时间"
            width="120">
            <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.proposedTime"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>
                {{ row.proposedTime }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            property="completionTime"
            label="完成时间"
            width="120">
            <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.completionTime"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>
                {{ row.completionTime }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            :label="'完成情况'"
            property="completionStatus">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.completionStatus"/>
              <template v-else>
                {{ row.completionStatus }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'销项确认'"
            align="left"
            property="salesConfirmation">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.salesConfirmation"/>
              <template v-else>
                {{ row.salesConfirmation }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'销项时间'"
            align="left"
            property="closingTime">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.closingTime"/>
              <template v-else>
                {{ row.closingTime }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'反馈人'"
            align="left"
            property="feedbackPerson">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.feedbackPerson"/>
              <template v-else>
                {{ row.feedbackPerson }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'操作'"
            fixed="right"
            property="proofResult">
            <template v-slot="{ row, $index}">
              <!-- 部分编辑-->
              <el-button
                v-if="$index !== editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="editItem(pilotPlan3.gridData, $index, 1)">编辑</el-button>
              <el-button
                v-if="$index === editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="savepilotPlanItem3(row)">保存</el-button>
              <el-button
                class="screen-btn edit-btn"
                type="text"
                @click="deleteItem3(row, $index)">删除</el-button>
            </template>
          </el-table-column>
  
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          class="screen-btn"
          style="margin-top: 10px"
          @click="addGridData('pilotPlan3');editIndex = pilotPlan3.gridData.length - 1">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="pilotPlan4.dialogVisible"
      :width="'95%'"
      :top="'50px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="$refs.tableEdit.clearFilter()">
              清除筛选
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              type="date"
              @change="changeDate"/>
            <template>
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview4"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
            </template>
            <span
              class="screen-btn"
              @click="exportpilotPlan4">
              导出
            </span>
          </div>
          成本攻关挂牌计划跟踪表
        </div>
      </template>
      <el-form>
        <el-table
          v-loading="loading4"
          ref="tableEdit"
          :data="pilotPlan4.gridData"
          :max-height="tableMaxHeight"
          class="center-table"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60"/>
          <el-table-column
            property="planType"
            label="类别">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.planType"
                :rows="4"
                type="textarea"/>
              <template v-else>
                {{ row.planType }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="supervisionContent"
            label="督办内容">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.supervisionContent"/>
              <template v-else>
                {{ row.supervisionContent }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="measures"
            label="措施">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.measures"/>
              <template v-else>
                {{ row.measures }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="endData"
            label=" 计划完成时间"
            width="120">
            <template v-slot="{ row, $index }">
              <!-- <el-date-picker
                v-if="$index === editIndex"
                v-model="row.endData"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>
                {{ row.endData }}
              </template> -->
              <el-input
                v-if="$index === editIndex"
                v-model="row.endData"
                :rows="4"
                type="textarea"/>
              <template v-else>
                {{ row.endData }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'责任人'"
            align="left"
            property="projectManager">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.projectManager"/>
              <template v-else>
                {{ row.projectManager }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'赋能单位'"
            align="left"
            property="supportUnit">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.supportUnit"/>
              <template v-else>
                {{ row.supportUnit }}
              </template>
            </template>
          </el-table-column>
          
          <el-table-column
            v-if="!isFactoryUser"
            :label="monthData+'月7日完成情况'"
            property="status7Day">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.status7Day"/>
              <template v-else>
                {{ row.status7Day }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            :label="monthData+'月17日完成情况'"
            property="status17Day">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.status17Day"/>
              <template v-else>
                {{ row.status17Day }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            :label="monthData+'月27日完成情况'"
            property="status27Day">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.status27Day"/>
              <template v-else>
                {{ row.status27Day }}
              </template>
            </template>
          </el-table-column>
       
          <el-table-column
            :label="'操作'"
            fixed="right"
            property="proofResult">
            <template v-slot="{ row, $index}">
              <!-- 部分编辑-->
              <el-button
                v-if="$index !== editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="editItem(pilotPlan4.gridData, $index, 1)">编辑</el-button>
              <el-button
                v-if="$index === editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="savepilotPlanItem4(row)">保存</el-button>
              <el-button
                class="screen-btn edit-btn"
                type="text"
                @click="deleteItem4(row, $index)">删除</el-button>
            </template>
          </el-table-column>
  
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          class="screen-btn"
          style="margin-top: 10px"
          @click="addGridData('pilotPlan4');editIndex = pilotPlan4.gridData.length - 1">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM'"
            type="month"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
  
  <script>
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { qmsQualitySystemSaveNew, qmsQualitySystem } from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  checklistBySetDate,
  checklistDelete,
  checklistSave,
  findCountDeptBySetDate,
  findCountPltBySetDate,
  findDpPltBySetDate,
  progressReportingDeleteAllById,
  progressReportingFindAllBySetDate,
  progressReportingSave
} from '@/api/screenTechnolagy'
import moment from 'moment'
import { math } from '@/lib/Math'
import TextDisplay from '@/pages/screen/technologyMeeting/component/text-display'
import { findOneUserByUserNo } from '@/api/system'
import { mapState } from 'vuex'
import {
  trackingMattersDelete,
  trackingMattersFind,
  trackingMattersSave,
  findRemarkBySetDate,
  updateRemarkBySetDate,
  deleteById,
  deleteById3,
  findRemarkBySetDateMonth,
  updateRemarkBySetDateMonth,
  CostPRPlanData,
  CostPRPlanSave
} from '@/api/screenC2'

export default {
  name: 'coordinateB1',
  components: { TextDisplay, ScreenBorder, ScreenBorderMulti },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      active: '1',
      activeFllow: '1',
      loading2: false,
      loading3: false,
      loading4: false,
      editIndex: null,
      editPartIndex: null, //部分编辑
      tableMaxHeight: null,
      updateFllowAbled: true,
      tabList: [
        {
          id: '1',
          activeFllow: true,
          title: '跟踪事项'
        },
        {
          id: '2',
          activeFllow: false,
          title: '持续跟踪事项'
        },
        {
          id: '3',
          activeFllow: false,
          title: '月度跟踪事项'
        },
        {
          id: '4',
          activeFllow: false,
          title: '成本攻关挂牌计划跟踪表'
        }
      ],
      pilotPlan1: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      pilotPlan2: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      pilotPlan3: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      pilotPlan4: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      form: {
        desc: ''
      },
      pivotTable: {
        table1: [],
        table2: [],
        table3: [],
        dialogVisible: false,
        maxHeight: null
      },
      pltList: [
        { value: '第一炼钢厂', text: '第一炼钢厂' },
        { value: '宽厚板厂', text: '宽厚板厂' },
        { value: '中厚板卷厂', text: '中厚板卷厂' },
        { value: '中板厂', text: '中板厂' }
      ],
      departmentList: [
        '工艺研究室',
        '调质钢研发室',
        '结构船板研发室',
        '低温容器研发室',
        '能源用钢研发室'
      ],
      varietyList: ['工艺抽查', '工装备件'],
      conclusionList: ['符合', '不符合'],
      factoryList: [
        { code: 'X73', name: '第一炼钢厂' },
        { code: 'X38', name: '宽厚板厂' },
        { code: 'X32', name: '中厚板卷厂' },
        { code: 'X66', name: '中板厂' }
      ],
      isFactoryUser: false
    }
  },
  computed: {
    ...mapState('menu', ['pageButtonPower']),
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'month')
        .format('yyyy-MM')
    },
    levelList: function() {
      if (
        this.editIndex !== null &&
        this.pilotPlan1.gridData[this.editIndex] &&
        this.pilotPlan1.gridData[this.editIndex].inspectionContent ===
          '工装备件'
      ) {
        return [
          '一般1级',
          '一般2级',
          '一般3级',
          '重要1级',
          '重要2级',
          '重要3级'
        ]
      }
      return []
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.loadData()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
    this.loadData()
  },
  methods: {
    loadData() {
      this.getpilotPlan()
      this.getpilotPlan2()
      this.getpilotPlan3()
      this.getpilotPlan4()
      this.monthData = moment(this.cDate).month() + 1
    },
    filterConclusion(value, row) {
      return row['conclusion'] === value
    },
    filterPlt(value, row) {
      return row['plt'] === value
    },
    async findOneUserByUserNo() {
      this.userNo = localStorage.getItem('userId')
      const user = await post(findOneUserByUserNo, {
        userNo: this.userNo
      })
      return new Promise(resolve => resolve(user.data))
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          index: 'A',
          backlog: 'B',
          unit: 'C',
          proposedTime: 'D',
          completionTime: 'E',
          completionStatus: 'F',
          salesConfirmation: 'G',
          closingTime: 'H',
          feedbackPerson: 'I'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        const datas = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
        this.savepilotPlan(datas)
      })
    },
    handlePreview2(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          index: 'A',
          BACKLOG: 'B',
          UNIT: 'C',
          PROPOSEDTIME: 'D',
          COMPLETIONTIME: 'E',
          COMPLETIONSTATUS: 'F',
          SALESCONFIRMATION: 'G',
          CLOSINGTIME: 'H',
          FEEDBACKPERSON: 'I'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        const datas = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
        this.savepilotPlan2(datas)
      })
    },
    handlePreview3(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          index: 'A',
          backlog: 'B',
          unit: 'C',
          proposedTime: 'D',
          completionTime: 'E',
          completionStatus: 'F',
          salesConfirmation: 'G',
          closingTime: 'H',
          feedbackPerson: 'I'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        const datas = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
        this.savepilotPlan3(datas)
      })
    },
    handlePreview4(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          index: 'A',
          planType: 'B',
          supervisionContent: 'C',
          measures: 'D',
          endData: 'E',
          projectManager: 'F',
          supportUnit: 'G',
          status7Day: 'H',
          status17Day: 'I',
          status27Day: 'J'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        const datas = sheet.map(item => {
          return item
        })
        datas.forEach(item => this.pilotPlan4.gridData.push(item))
        console.log('datas', this.pilotPlan4.gridData)
        this.$message.success('解析成功！')
        // this.pilotPlan4.gridData.
        this.savepilotPlan4()
      })
    },
    exportpilotPlan() {
      const data = [
        {
          index: '序号',
          backlog: '待办事项内容',
          unit: '责任单位',
          proposedTime: '提出时间',
          completionTime: '完成时间',
          completionStatus: '完成情况',
          salesConfirmation: '销项确认',
          closingTime: '销项时间',
          feedbackPerson: '反馈人'
        }
      ].concat(
        _.cloneDeep(this.pilotPlan1.gridData).map((item, index) => {
          delete item.id
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `跟踪事项${this.cDate}）.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {
            '!merges': LAY_EXCEL.makeMergeConfig([])
          }
        }
      })
    },
    exportpilotPlan2() {
      const data = [
        {
          index: '序号',
          BACKLOG: '待办事项内容',
          UNIT: '责任单位',
          PROPOSEDTIME: '提出时间',
          COMPLETIONTIME: '完成时间',
          COMPLETIONSTATUS: '完成情况',
          SALESCONFIRMATION: '销项确认',
          CLOSINGTIME: '销项时间',
          FEEDBACKPERSON: '反馈人'
        }
      ].concat(
        _.cloneDeep(this.pilotPlan2.gridData).map((item, index) => {
          delete item.ID
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `持续跟踪事项${this.cDate}）.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {
            '!merges': LAY_EXCEL.makeMergeConfig([])
          }
        }
      })
    },
    exportpilotPlan3() {
      const data = [
        {
          index: '序号',
          backlog: '待办事项内容',
          unit: '责任单位',
          proposedTime: '提出时间',
          completionTime: '完成时间',
          completionStatus: '完成情况',
          salesConfirmation: '销项确认',
          closingTime: '销项时间',
          feedbackPerson: '反馈人'
        }
      ].concat(
        _.cloneDeep(this.pilotPlan3.gridData).map((item, index) => {
          delete item.id
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `月度跟踪事项${this.cDate}）.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {
            '!merges': LAY_EXCEL.makeMergeConfig([])
          }
        }
      })
    },
    exportpilotPlan4() {
      const data = [
        {
          // index: '序号',
          // planType: '类别',
          // supervisionContent: '督办内容',
          // measures: '措施',
          // endData: '计划完成时间',
          // projectManager: '责任人',
          // status7Day: '每月7日完成情况',
          // status17Day: '每月17日完成情况',
          // status27Day: '每月27日完成情况'
          index: '序号',
          planType: '类别',
          supervisionContent: '督办内容',
          measures: '措施',
          endData: '计划完成时间',
          projectManager: '责任人',
          supportUnit: '赋能单位',
          status7Day: '每月7日完成情况',
          status17Day: '每月17日完成情况',
          status27Day: '每月27日完成情况'
        }
      ].concat(
        _.cloneDeep(this.pilotPlan4.gridData).map((item, index) => {
          delete item.id
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `成本攻关挂牌计划跟踪表${this.cDate}）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {
              '!merges': LAY_EXCEL.makeMergeConfig([])
            }
          }
        }
      )
    },
    // 获取数据
    async getpilotPlan() {
      post(trackingMattersFind, {
        setDate: this.cDate
      }).then(res => {
        this.loading = false
        this.$emit('statusChange', !res.data.length)
        let data = res.data.map((item, index) => {
          return {
            index: index + 1,
            backlog: item.backlog,
            unit: item.unit,
            proposedTime: item.proposedtime,
            completionTime: item.completiontime,
            completionStatus: item.completionstatus,
            salesConfirmation: item.salesconfirmation,
            closingTime: item.closingtime,
            feedbackPerson: item.feedbackperson,
            id: item.id
          }
        })
        //   console.log('data11', data)
        this.pilotPlan1.showGridData = res.data
        this['pilotPlan1'].gridData = lodash.cloneDeep(data)
        this['pilotPlan1'].showGridData = data
        this.formatSpanData(this['pilotPlan1'].showGridData)
      })
    },
    // 获取数据
    async getpilotPlan2() {
      post(findRemarkBySetDate, {}).then(res => {
        this.loading2 = false
        this.$emit('statusChange', !res.data.length)
        let data = res.data.map((item, index) => {
          return {
            index: index + 1,
            BACKLOG: item.BACKLOG,
            UNIT: item.UNIT,
            PROPOSEDTIME: item.PROPOSEDTIME,
            COMPLETIONTIME: item.COMPLETIONTIME,
            COMPLETIONSTATUS: item.COMPLETIONSTATUS,
            SALESCONFIRMATION: item.SALESCONFIRMATION,
            CLOSINGTIME: item.CLOSINGTIME,
            FEEDBACKPERSON: item.FEEDBACKPERSON,
            ID: item.ID
          }
        })
        this['pilotPlan2'].gridData = lodash.cloneDeep(data)
        this['pilotPlan2'].showGridData = data
        this.formatSpanData(this['pilotPlan2'].showGridData)
      })
    },
    async getpilotPlan3() {
      post(findRemarkBySetDateMonth, { setDate: this.cDate }).then(res => {
        this.loading3 = false
        this.$emit('statusChange', !res.data.length)
        let data = res.data.map((item, index) => {
          return {
            index: index + 1,
            backlog: item.backlog,
            unit: item.unit,
            proposedTime: item.proposedtime,
            completionTime: item.completiontime,
            completionStatus: item.completionstatus,
            salesConfirmation: item.salesconfirmation,
            closingTime: item.closingtime,
            feedbackPerson: item.feedbackperson,
            id: item.id
          }
        })
        this.pilotPlan3.showGridData = res.data
        this['pilotPlan3'].gridData = lodash.cloneDeep(data)
        this['pilotPlan3'].showGridData = data
        this.formatSpanData(this['pilotPlan3'].showGridData)
      })
    },
    async getpilotPlan4() {
      post(CostPRPlanData, { setDate: this.cDate }).then(res => {
        this.loading4 = false
        this.$emit('statusChange', !res.data.length)
        let data = res.data.map((item, index) => {
          return {
            index: index + 1,
            planType: item.planType,
            endData: item.endData,
            measures: item.measures,
            supervisionContent: item.supervisionContent,
            supportUnit: item.supportUnit,
            status7Day: item.status7Day,
            status17Day: item.status17Day,
            status27Day: item.status27Day,
            projectManager: item.projectManager,
            id: item.id
          }
        })
        this.pilotPlan4.showGridData = res.data
        this['pilotPlan4'].gridData = lodash.cloneDeep(data)
        this['pilotPlan4'].showGridData = data
        this.formatSpanData(this['pilotPlan4'].showGridData)
      })
    },
    savepilotPlanItem(row) {
      let err = 0
      let arr = []
      arr.forEach(item => {
        if (row[item] === '' || row[item] === null) {
          console.log(item)
          err++
        }
      })
      if (err > 0) {
        return this.$message.warning('请补全信息！')
      }
      this.savepilotPlan([row])
    },
    savepilotPlanItem2(row) {
      let err = 0
      let arr = []
      arr.forEach(item => {
        if (row[item] === '' || row[item] === null) {
          console.log(item)
          err++
        }
      })
      if (err > 0) {
        return this.$message.warning('请补全信息！')
      }
      this.savepilotPlan2([row])
    },
    savepilotPlanItem3(row) {
      let err = 0
      let arr = []
      arr.forEach(item => {
        if (row[item] === '' || row[item] === null) {
          console.log(item)
          err++
        }
      })
      if (err > 0) {
        return this.$message.warning('请补全信息！')
      }
      this.savepilotPlan3([row])
    },
    savepilotPlanItem4(row) {
      let err = 0
      let arr = []
      arr.forEach(item => {
        if (row[item] === '' || row[item] === null) {
          console.log(item)
          err++
        }
      })
      if (err > 0) {
        return this.$message.warning('请补全信息！')
      }
      this.savepilotPlan4([row])
    },
    savepilotPlan(items) {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: items.map(item => {
          Object.assign(item, { setDate: this.cDate })
          return item
        })
      }
      post(trackingMattersSave, params).then(res => {
        //
        this.loading = false
        if (res && res !== -1) {
          this.$message.success('保存成功！')
          this.getpilotPlan()
          this.editIndex = null
          this.editPartIndex = null
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    savepilotPlan2(items) {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        //   setDate: this.cDate,
        data: items.map(item => {
          Object.assign(item, { setDate: this.cDate })
          return item
        })
      }
      post(updateRemarkBySetDate, params).then(res => {
        //
        this.loading = false
        if (res && res !== -1) {
          this.$message.success('保存成功！')
          this.getpilotPlan2()
          this.editIndex = null
          this.editPartIndex = null
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    savepilotPlan3(items) {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: items.map(item => {
          Object.assign(item, { setDate: this.cDate })
          return item
        })
      }
      post(updateRemarkBySetDateMonth, params).then(res => {
        //
        this.loading = false
        if (res && res !== -1) {
          this.$message.success('保存成功！')
          this.getpilotPlan3()
          this.editIndex = null
          this.editPartIndex = null
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    savepilotPlan4(items) {
      this.loading4 = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.pilotPlan4.gridData
        // data: items.map(item => {
        //   Object.assign(item, { setDate: this.cDate })
        //   return item
        // })
      }
      post(CostPRPlanSave, params).then(res => {
        //
        this.loading = false
        if (res && res !== -1) {
          this.$message.success('保存成功！')
          this.getpilotPlan4()
          this.editIndex = null
          this.editPartIndex = null
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    editItem(data, index, type) {
      this.editIndex = index
    },
    updateFllow() {
      this.updateFllowAbled = false
    },
    editPartItem(data, index, type) {
      this.editPartIndex = index
    },
    deleteItem(data) {
      this.$confirm(`是否确认删除这条记录?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        post(trackingMattersDelete, {
          id: data.id
        }).then(res => {
          this.loading = false
          this.$message.success('删除成功！')
          this.getpilotPlan()
        })
      })
    },
    deleteItem2(data) {
      this.$confirm(`是否确认删除这条记录?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        //   console.log('id', data)

        post(deleteById, {
          id: data.ID
        }).then(res => {
          this.loading = false
          this.$message.success('删除成功！')
          this.getpilotPlan2()
        })
      })
    },
    deleteItem3(data) {
      this.$confirm(`是否确认删除这条记录?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading3 = true
        console.log('id', data)

        post(deleteById3, {
          id: data.id
        }).then(res => {
          this.loading3 = false
          this.$message.success('删除成功！')
          this.getpilotPlan3()
        })
      })
    },
    deleteItem4(data) {
      this.$confirm(`是否确认删除这条记录?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        //   console.log('id', data)

        this.pilotPlan4.gridData.splice(data.index - 1, 1)
        this.savepilotPlanItem4(data)
      })
    },
    conclusionChange($event, data) {
      if ($event === '符合') {
        console.log(data)
        data[this.editIndex].deductPoints = 0
        data[this.editIndex].rftDeadline = null
        data[this.editIndex].deductPointsDisabled = true
      } else {
        data[this.editIndex].deductPointsDisabled = false
      }
    },
    getPivot() {
      post(findCountPltBySetDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.pivotTable.table1 = res
      })
      post(findCountDeptBySetDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.pivotTable.table2 = res
      })
      post(findDpPltBySetDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.pivotTable.table3 = res.map(item => {
          item.DF = 100 - item.ZJ
          return item
        })
      })
    },
    calculate() {
      this.pilotPlan1.maxHeight = this.$refs.table1.offsetHeight
      this.pilotPlan2.maxHeight = this.$refs.table1.offsetHeight
      this.pilotPlan3.maxHeight = this.$refs.table1.offsetHeight
      this.pilotPlan4.maxHeight = this.$refs.table1.offsetHeight
      this.tableMaxHeight = document.body.clientHeight - 240
    },
    totalClass(row) {
      if (row.row.serialNumber && row.row.serialNumber.trim() === '合计') {
        return 'table-total'
      }
      return ''
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总计'
          return
        }
        console.log(column)
        if (![1, 2, 3].includes(index)) return (sums[index] = '')
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          sums[index] = ''
        }
      })
      return sums
    }
  }
}
</script>
  
  <style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
  .content-item2 {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.edit-btn {
  margin: 0 3px;
  &:first-child {
    margin-bottom: 5px;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.dialog-item-tit {
  font-size: 18px;
  color: #dcdcdc;
  margin-bottom: 8px;
  margin-top: 15px;
}
/deep/ .el-textarea__inner {
  background-color: #041a21;
  color: #dcdcdc;
  border: #041a21;
}
/deep/ .el-textarea.is-disabled .el-textarea__inner {
  background-color: #072530;
}
</style>
