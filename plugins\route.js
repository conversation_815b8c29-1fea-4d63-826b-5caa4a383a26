// 路由守卫
import { post } from '@/lib/Util'
import { resourceListNoPage } from '@/api/system'
import {
  filterUrl,
  funcUrlDel,
  generateTree,
  initialOpenedList,
  openNewPage
} from '@/lib/Menu'

const sleep = function timeout(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}
export default ({ app }) => {
  app.router.afterEach((to, from) => {
    console.log(to)
    openNewPage(app, to.name, to.path, to.fullPath, to.params, to.query)
    window.scrollTo(0, 0)
  })
  app.router.beforeEach(async (to, from, next) => {
    // console.log(to)
    const { query } = to
    // 判断资源来源是否为后端重定向
    // 获取url参数，如果有token信息，直接塞入localstorage
    if (query.org && query.org === 'redirect') {
      !!query.token && localStorage.setItem('token', query.token)
      !!query.userId && localStorage.setItem('userId', query.userId)
    }
    // 判断是否显示头部
    if (query.showHeader !== undefined) {
      app.store.commit('menu/showHeader', parseInt(query.showHeader))
    }
    // 判断系统是否已经记录token
    const token = localStorage.getItem('token')
    if (!token) {
      // 没有token,记录当前页面Url
      // console.warn('没有token，开始重定向')
      // 直接跳转到统一认证登录页
      // 重定向搭到后端登录请求
      const userName = localStorage.getItem('userId'),
        href = window.location.href,
        redirectUrl = `http://172.25.63.126:9701/login/ssoLogin?userId=${userName}&url=${href}`
      window.location.href = redirectUrl
      next()
    } else {
      next()
      // 删除页面token参数
      if (query.org && query.org === 'redirect') {
        funcUrlDel()
      }
      // 用户菜单没有加载时直接请求用户权限菜单
      if (
        // windows 对象上的系统初始化标志位
        !window.SYSTEM_MENU_LOADED
      ) {
        // 系统菜单已加载动作已执行
        window.SYSTEM_MENU_LOADED = 1
        await sleep(0)
        // 系统初始化时，设置头部显示
        app.store.commit('menu/showHeader', true)
        // 判断是否显示头部
        query.showHeader !== undefined &&
          app.store.commit('menu/showHeader', !!parseInt(query.showHeader))
        // console.log('重新请求数据')
        const data = await post(resourceListNoPage, {
          userNo: localStorage.getItem('userId'),
          serviceName: 'kpi'
        })
        if (data.success) {
          const menu = data.data.filter(item => item.type === 'menu')
          const button = data.data
            .filter(item => item.type === 'button')
            .map(item => item.url)
          const menuTree = generateTree(menu)
          // 缓存菜单、按钮权限数据
          app.store.commit(
            'menu/setAllMenus',
            menu.map(item => {
              const obj = Object.assign({}, item)
              delete obj.imgIconResource
              return obj
            })
          )
          app.store.commit('menu/setUserMenuList', menuTree)
          app.store.commit('menu/setPageButtonPower', button)
        }
        // 初始化标签固定页
        initialOpenedList(app)
      }
      // 判断用户访问的路由权限是否存在
      if (
        app.store.state.menu.allMenus &&
        !app.store.state.menu.allMenus.find(
          item =>
            item.url === to.path ||
            item.url === decodeURIComponent(filterUrl(to.fullPath))
        ) &&
        to.path !== '/'
      ) {
        await app.router.push('/')
      }
    }
  })
}
