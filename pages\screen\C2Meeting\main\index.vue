<template>
  <div class="content">
    <div class="content-item">
      <screen-border-multi>
        <template v-slot:title>
          <div class="tabs-class">
            <div
              v-for="(item) in tabList"
              :key="item.id"
              :class="{'tab-pane-active': item.id === active}"
              class="tab-pane"
              @click="active = item.id">
              <div class="tab-pane-title-class">
                <div>{{ item.title }}</div>
                <div
                  v-if="item.id === active"
                  class="tab-pane-img">
                  <img
                    class="tab-pane-img2"
                    src="@/assets/images/screen/tab-pane-active-line2.png"
                    alt="">
                  <img
                    class="tab-pane-img1"
                    src="@/assets/images/screen/tab-pane-active-line.png"
                    alt="">
                </div>
              </div>
            </div>
          </div>
        </template>
        <custom-table-noheader19
          v-if="active === '1'"
          :key="'plan1'"
          :title="'当月计划和产量进度'"
          :setting="tableObj1.setting"
          :merge-set="tableObj1.mergeObj"
          :url-list="tableObj1.url.list"
          :url-save="tableObj1.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '2'"
          :key="'plan2'"
          :title="'待处理情况'"
          :setting="tableObj2.setting"
          :url-list="tableObj2.url.list"
          :url-save="tableObj2.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '3'"
          :key="'plan3'"
          :title="'当月累计停时汇报'"
          :setting="tableObj4.setting"
          :url-list="tableObj4.url.list"
          :url-save="tableObj4.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '4'"
          :key="'plan4'"
          :title="'本月累计隐性停时汇总'"
          :setting="tableObj5.setting"
          :url-list="tableObj5.url.list"
          :url-save="tableObj5.url.save"
          :select-date="selectDate"/>
      </screen-border-multi>
    </div>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import {
  findSubjectData,
  qmsQualitySupervisionQuery,
  qmsQualitySupervisionSave,
  WidePlateRate
} from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/C2Meeting/component/custom-table'
import {
  downtimeMonthFind,
  downtimeMonthSave,
  hiddenDowntimeMonthFind,
  hiddenDowntimeMonthSave,
  pendingSituationFind,
  pendingSituationSave,
  planProductionMonthFind,
  planProductionMonthSave
} from '@/api/screenC2'
import ScreenBorderMulti from '@/pages/screen/C2Meeting/component/screen-border-multi'
import CustomTableNoheader from '@/pages/screen/C2Meeting/component/custom-table-noheader'
import CustomTableNoheader19 from '@/pages/screen/C2Meeting/component/custom-table-noheader19'
import { post } from '@/lib/Util'
import { math } from '@/lib/Math'
export default {
  name: 'C1Main',
  components: {
    CustomTableNoheader,
    CustomTableNoheader19,
    ScreenBorderMulti,
    CustomTable,
    SingleBarsChart
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      tabList: [
        {
          id: '1',
          active: true,
          title: '当月计划和产量进度'
        },
        {
          id: '2',
          active: false,
          title: '待处理情况'
        },
        {
          id: '3',
          active: false,
          title: '当月累计停时汇报'
        },
        {
          id: '4',
          active: false,
          title: '本月累计隐性停时汇总'
        }
      ],
      active: '1',
      steelRate: '',
      tableObj1: {
        url: {
          save: planProductionMonthSave,
          list: planProductionMonthFind
        },
        mergeObj: {
          //  '0-3': [2, 1],
          //  '3-0': [1, 2],
          //  '1-3': false,
          //  '3-1': false
        },
        setting: [
          {
            keyQuery: 'classification',
            keySave: 'classification',
            label: ''
          },
          {
            keyQuery: 'plannedproduction',
            keySave: 'plannedProduction',
            label: '计划产量\n(t)'
          },
          {
            keyQuery: 'cumulativeproduction',
            keySave: 'cumulativeProduction',
            label: '累计产量\n(t)'
          },
          {
            keyQuery: 'scheduling',
            keySave: 'scheduling',
            label: '时间进度\n(%)'
          },
          {
            keyQuery: 'productionprogress',
            keySave: 'productionProgress',
            label: '产量进度\n(%)'
          },
          {
            keyQuery: 'overprogress',
            keySave: 'overProgress',
            label: '超欠进度\n(%)'
          },
          {
            keyQuery: 'avgdailyproduction',
            keySave: 'avgDailyProduction',
            label: '日需均产\n(t)'
          },
          {
            keyQuery: 'rmmttime',
            keySave: 'rmmttime',
            label: '剩余检修时间'
          },
          {
            keyQuery: 'mtcpfdtime',
            keySave: 'mtcpfdtime',
            label: '已执行检修时间'
          }
        ]
      },
      tableObj2: {
        url: {
          save: pendingSituationSave,
          list: pendingSituationFind
        },
        setting: [
          {
            keyQuery: 'classification',
            keySave: 'classification',
            label: ''
          },
          {
            keyQuery: 'plan',
            keySave: 'plan',
            label: '计划'
          },
          {
            keyQuery: 'piece',
            keySave: 'piece',
            label: '数量(块)'
          },
          {
            keyQuery: 'weight',
            keySave: 'weight',
            label: '重量\n（吨）'
          },
          {
            keyQuery: 'reason',
            keySave: 'reason',
            label: '未完成计划原因'
          }
        ]
      },
      tableObj4: {
        url: {
          save: downtimeMonthSave,
          list: downtimeMonthFind
        },
        setting: [
          {
            keyQuery: 'unit',
            keySave: 'unit',
            label: '单位'
          },
          {
            keyQuery: 'mechanicalreality',
            keySave: 'mechanicalReality',
            label: '机械'
          },
          {
            keyQuery: 'electricalreality',
            keySave: 'electricalReality',
            label: '电气'
          },
          {
            keyQuery: 'operationalreality',
            keySave: 'operationalReality',
            label: '操作'
          },
          {
            keyQuery: 'totalreality',
            keySave: 'totalReality',
            label: '合计'
          },
          // {
          //   label: '机械',
          //   children: [
          //     {
          //       keyQuery: 'mechanicalplan',
          //       keySave: 'mechanicalPlan',
          //       label: '计划'
          //     },
          //     {
          //       keyQuery: 'mechanicalreality',
          //       keySave: 'mechanicalReality',
          //       label: '实际'
          //     }
          //   ]
          // },
          // {
          //   label: '电气',
          //   children: [
          //     {
          //       keyQuery: 'electricalplan',
          //       keySave: 'electricalPlan',
          //       label: '计划'
          //     },
          //     {
          //       keyQuery: 'electricalreality',
          //       keySave: 'electricalReality',
          //       label: '实际'
          //     }
          //   ]
          // },
          // {
          //   label: '操作',
          //   children: [
          //     {
          //       keyQuery: 'operationalplan',
          //       keySave: 'operationalPlan',
          //       label: '计划'
          //     },
          //     {
          //       keyQuery: 'operationalreality',
          //       keySave: 'operationalReality',
          //       label: '实际'
          //     }
          //   ]
          // },
          // {
          //   label: '合计',
          //   children: [
          //     {
          //       keyQuery: 'totalplan',
          //       keySave: 'totalPlan',
          //       label: '计划'
          //     },
          //     {
          //       keyQuery: 'totalreality',
          //       keySave: 'totalReality',
          //       label: '实际'
          //     }
          //   ]
          // },
          {
            keyQuery: 'shakedowntest',
            keySave: 'shakedownTest',
            label: '调式'
          },
          {
            keyQuery: 'external',
            keySave: 'external',
            label: '外部'
          }
        ]
      },
      tableObj5: {
        url: {
          save: hiddenDowntimeMonthSave,
          list: hiddenDowntimeMonthFind
        },
        setting: [
          {
            keyQuery: 'unit',
            keySave: 'unit',
            label: '单位'
          },
          {
            keyQuery: 'mechanicalreality',
            keySave: 'mechanicalReality',
            label: '机械'
          },
          {
            keyQuery: 'electricalreality',
            keySave: 'electricalReality',
            label: '电气'
          },
          {
            keyQuery: 'operationalreality',
            keySave: 'operationalReality',
            label: '操作'
          },
          {
            keyQuery: 'totalreality',
            keySave: 'totalReality',
            label: '合计'
          },
          // {
          //   label: '机械',
          //   children: [
          //     {
          //       keyQuery: 'mechanicalplan',
          //       keySave: 'mechanicalPlan',
          //       label: '计划'
          //     },
          //     {
          //       keyQuery: 'mechanicalreality',
          //       keySave: 'mechanicalReality',
          //       label: '实际'
          //     }
          //   ]
          // },
          // {
          //   label: '电气',
          //   children: [
          //     {
          //       keyQuery: 'electricalplan',
          //       keySave: 'electricalPlan',
          //       label: '计划'
          //     },
          //     {
          //       keyQuery: 'electricalreality',
          //       keySave: 'electricalReality',
          //       label: '实际'
          //     }
          //   ]
          // },
          // {
          //   label: '操作',
          //   children: [
          //     {
          //       keyQuery: 'operationalplan',
          //       keySave: 'operationalPlan',
          //       label: '计划'
          //     },
          //     {
          //       keyQuery: 'operationalreality',
          //       keySave: 'operationalReality',
          //       label: '实际'
          //     }
          //   ]
          // },
          // {
          //   label: '合计',
          //   children: [
          //     {
          //       keyQuery: 'totalplan',
          //       keySave: 'totalPlan',
          //       label: '计划'
          //     },
          //     {
          //       keyQuery: 'totalreality',
          //       keySave: 'totalReality',
          //       label: '实际'
          //     }
          //   ]
          // },
          {
            keyQuery: 'shakedowntest',
            keySave: 'shakedownTest',
            label: '调式'
          },
          {
            keyQuery: 'external',
            keySave: 'external',
            label: '外部'
          }
        ]
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.loadData()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.loadData()
  },
  methods: {
    loadData() {
      this.getSteelRate()
    },
    // 材原钢种一次合格率
    getSteelRate() {
      post(WidePlateRate, {
        startTime: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('YYYYMMDD'),
        endTime: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('YYYYMMDD')
      }).then(res => {
        this.steelRate = res.data[0]['一次合格率']
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
