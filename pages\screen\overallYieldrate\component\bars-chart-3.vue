<template>
  <div style="height: 100%">
    <div
      :id="containerId"
      :style="{ height: '100%' }" />
    <!-- 炼钢分析表 -->
    <el-dialog
      :visible.sync="visible1"
      class="screen-dialog"
      width="80%">
      <template v-slot:title>
        <div class="custom-dialog-title">
          炼钢分析表
        </div>
      </template>
      <div
        ref="table1"
        class="scroll-wrapper">
        <el-table
          v-loading="loading1"
          :data="data1"
          class="font-table center-table"
          border>
          <el-table-column
            property="plgz"
            label="坯料钢种" />
          <el-table-column
            property="plzscl"
            label="坯料生产量"
          />
          <el-table-column
            property="plygzsdl"
            label="坯料原钢种收得量"
          />
          <el-table-column
            property="gygzsdl"
            label="坯料原钢种收得率" />
          <el-table-column label="坯料判废影响">
            <el-table-column
              property="fpzl"
              label="判废量"
            />
            <el-table-column
              property="fpl"
              label="判废率"
            />
          </el-table-column>
          <el-table-column label="坯料替代影响">
            <el-table-column
              property="tdzl"
              label="替代量"
            />
            <el-table-column
              property="tdl"
              label="替代率"
            />
          </el-table-column>
          <el-table-column label="库存坯">
            <el-table-column
              property="kcpzl"
              label="重量"
            />
            <el-table-column
              property="kcpbl"
              label="比例"
            />
          </el-table-column>
          <el-table-column label="其中:头尾坯">
            <el-table-column
              property="twpzl"
              label="重量"
            />
            <el-table-column
              property="twpbl"
              label="比例"
            />
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!-- 轧钢分析表 -->
    <el-dialog
      :visible.sync="visible2"
      class="screen-dialog"
      width="80%">
      <template v-slot:title>
        <div class="custom-dialog-title">
          轧钢分析表
        </div>
      </template>
      <div
        ref="table1"
        class="scroll-wrapper">
        <el-table
          v-loading="loading2"
          :data="data2"
          class="font-table center-table"
          border>
          <el-table-column
            property="plgz"
            label="坯料钢种 "
          />
          <el-table-column
            property="ygzplrlzl"
            label="原钢种坯料入炉重量"
          />
          <el-table-column
            property="ygzplsjgbzl"
            label="原钢种坯料实绩钢板重量"
          />
          <el-table-column
            property="ygzsjgbzlbhpfl"
            label="原钢种实绩钢板重量(不含判废量)" />
          <el-table-column
            property="ygzplzl"
            label="原钢种坯料重量" />
          <el-table-column
            property="cygzsdlv"
            label="材原钢种收得率" />
          <el-table-column label="材轧制影响">
            <el-table-column
              property="sjccl"
              label="实际成材率"
            />
            <el-table-column
              property="ygzsjccl"
              label="原钢种实际成材率"
            />
            <el-table-column
              property="zzqsl"
              label="轧制切损量"
            />
            <el-table-column
              property="zzqslzb"
              label="轧制切损量占比"
            />
          </el-table-column>
          <el-table-column label="判废影响">
            <el-table-column
              property="ygzpfl"
              label="判废量"
            />
            <el-table-column
              property="ygzpflv"
              label="判废率"
            />
          </el-table-column>
          <el-table-column label="改判、协议影响">
            <el-table-column
              property="ygzgpxyl"
              label="改判协议量"
            />
            <el-table-column
              property="ygzgpxylv"
              label="改判率"
            />
          </el-table-column>
          <el-table-column label="改轧影响">
            <el-table-column
              property="gzl"
              label="非原钢种正品量-含待判"
            />
            <el-table-column
              property="gzlv"
              label="改轧率"
            />
          </el-table-column>
          <el-table-column label="其中：正品现货影响">
            <el-table-column
              property="ygzzpxhl"
              label="判现量"
            />
            <el-table-column
              property="cygzzpxhl"
              label="正品现货率"
            />
          </el-table-column>
          <el-table-column label="其中：待判材影响">
            <el-table-column
              property="dpl"
              label="待判量"
            />
            <el-table-column
              property="dplbl"
              label="待判率"
            />
          </el-table-column>
        </el-table>
      </div>

    </el-dialog>
  </div>
</template>
<script>
import { AnalysisSlabByOrd, AnalysisSlabQltByOrd } from '@/api/screen'
import { post } from '@/lib/Util'

export default {
  name: 'bars-chart',
  props: {
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    echartData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 0
    },
    unit: {
      type: String,
      default: ''
    },
    scrollable: {
      type: Boolean,
      default: false
    },
    scrollStart: {
      type: Number,
      default: 0
    },
    scrollEnd: {
      type: Number,
      default: 100
    }
    // max: {
    //   type: Number,
    //   default: 100
    // }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null,
      visible1: false,
      loading1: false,
      data1: [],
      visible2: false,
      loading2: false,
      data2: []
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
    window.addEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
      }
      const options = {
        grid: {
          top: '40'
        },
        tooltip: {
          confine: true,
          show: this.showToolbox,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: function(params) {
            return (
              params[0].axisValue +
              '<br/>' +
              params
                .map(
                  item =>
                    `<div style="display: flex; justify-content: space-between"><span>
<span style="display: inline-block; width: 8px; height: 8px; vertical-align: middle; border-radius: 50%;background: ${
                      item.color
                    }"></span>
                    ${item.data.name || item.seriesName}
</span> <span> &emsp;
                    ${item.value}%</span></div>
                   `
                )
                .join('')
            )
          }
        },
        dataZoom: this.scrollable
          ? [
              {
                type: 'slider',
                show: true,
                xAxisIndex: [0],
                start: this.scrollStart,
                end: this.scrollEnd,
                height: 15,
                bottom: 5,
                borderColor: 'transparent',
                backgroundColor: 'rgba(47, 69, 84, 0.3)',
                dataBackground: {
                  lineStyle: { color: '#1fc6ff' },
                  areaStyle: { color: 'rgba(31, 198, 255, 0.2)' }
                },
                fillerColor: 'rgba(31, 198, 255, 0.2)',
                handleStyle: {
                  color: '#1fc6ff'
                },
                textStyle: {
                  color: '#ddd'
                }
              }
            ]
          : [],
        color: this.color,
        legend: {
          show: this.showLegend,
          align: 'left',
          top: '10px',
          right: 2,
          padding: [5, 10],
          icon: 'circle',
          textStyle: {
            color: '#C9E1FDF2',
            fontSize: 12
          },
          itemHeight: 10,
          itemWidth: 10,
          itemGap: 10,
          itemStyle: {
            borderWidth: 0,
            padding: 0
          }
        },
        grid: {
          top: '15%',
          left: '2%',
          right: '50',
          bottom: this.scrollable ? '20px' : '1%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: this.xData,
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              interval: 0,
              rotate: this.labelRotate || 0
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            }
          }
        ],
        yAxis: [
          {
            name: this.unit,
            nameTextStyle: {
              color: '#ddd',
              nameLocation: 'center'
            },
            type: 'value',
            minInterval: 0.5,
            axisLine: {
              show: false
            },
            axisLabel: {
              color: '#ddd',
              fontSize: 12,
              align: 'right'
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        series: this.chartData.map(item => {
          return {
            name: item.name,
            type: 'bar',
            barGap: '40%',
            barWidth: this.barWidth || 60 / this.chartData.length + '%',
            barMaxWidth: this.barWidth || 12,
            showBackground: this.barBackground,
            backgroundStyle: {
              color: 'rgba(232, 236, 239, 0.3)'
            },
            markPoint: {
              symbolSize: 5
            },
            label: {
              show: this.showLabel,
              color: '#fff',
              position: 'top',
              fontSize: 12,
              formatter: function(params) {
                return params.value + ' %'
              },
              offset: [0, 2]
            },
            data: item.data
          }
        })
      }
      this.myChart.setOption(options)
      this.myChart.on('click', this.handleBarClick)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    },
    handleBarClick(params) {
      console.log('%c params', 'color: red', params)
      // console.log('%c echartData', 'color: red', this.echartData)
      let steelTypes = []
      if (params.name === '综合') {
        this.echartData.xgrade.forEach(item => {
          if (item === '综合') {
            return
          }
          steelTypes.push(item)
        })
      } else {
        steelTypes.push(params.name)
      }
      steelTypes = steelTypes.filter(
        (item, index) => steelTypes.indexOf(item) === index
      )
      let ordNos = this.echartData.orderNo
      ordNos = ordNos.filter((item, index) => ordNos.indexOf(item) === index)
      const data = {
        orderNo: ordNos,
        steelType: steelTypes
      }
      // console.log('%c data', 'color: red', data)
      if (params.seriesIndex === 1) {
        this.getData1(data)
      } else if (params.seriesIndex === 2) {
        this.getData2(data)
      }
    },
    getData1(data) {
      this.visible1 = true
      this.loading1 = true
      post(AnalysisSlabByOrd, data).then(res => {
        this.data1 = res.data
        this.loading1 = false
      })
    },
    getData2(data) {
      this.visible2 = true
      this.loading2 = true
      post(AnalysisSlabQltByOrd, data).then(res => {
        this.data2 = res.data
        this.loading2 = false
      })
    }
  }
}
</script>

<style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;

  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;

    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }

    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
