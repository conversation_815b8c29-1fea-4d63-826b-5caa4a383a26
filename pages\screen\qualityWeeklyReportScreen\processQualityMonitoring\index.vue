<template>
  <div class="container">
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="高附加值钢板脱单情况(敬请期待)">
          <!-- 多行文本输入框 -->
          <el-input
            v-model="highValueInput"
            :rows="2"
            type="textarea"
            class="chart-input"
            resize="none"
          />
          <custom-table 
            ref="highValueSteelRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'highValueSteel'"
            :title="'高附加值钢板脱单情况'" 
            :setting="tableObj.setting" 
            :url-list="tableObj.url.list" 
            :url-save="tableObj.url.save"
            :select-date="selectDate" 
            :dialog-width="'50%'"
          />
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="头尾坯探伤情况(敬请期待)">
          <!-- 多行文本输入框 -->
          <el-input
            v-model="headTailFlawInput"
            :rows="2"
            type="textarea"
            class="chart-input"
            resize="none"
          />
          <custom-table 
            ref="headTailFlawRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'headTailFlaw'"
            :title="'头尾坯探伤情况'" 
            :setting="tableObj2.setting" 
            :url-list="tableObj2.url.list"
            :url-save="tableObj2.url.save" 
            :select-date="selectDate"
            :dialog-width="'80%'"
          />
        </screen-border>
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="每周瓢曲板发生情况(敬请期待)">
          <!-- 多行文本输入框 -->
          <el-input
            v-model="weeklyBuckleInput"
            :rows="2"
            type="textarea"
            class="chart-input"
            resize="none"
          />
          <custom-table 
            ref="weeklyBuckleRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'weeklyBuckle'"
            :title="'每周瓢曲板发生情况'" 
            :setting="tableObj3.setting" 
            :url-list="tableObj3.url.list" 
            :url-save="tableObj3.url.save"
            :select-date="selectDate" 
            :dialog-width="'50%'"
          />
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="关键设备使用情况(敬请期待)">
          <custom-table 
            ref="keyEquipmentRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'keyEquipment'"
            :title="'关键设备使用情况'" 
            :setting="tableObj4.setting" 
            :url-list="tableObj4.url.list"
            :url-save="tableObj4.url.save" 
            :select-date="selectDate" 
            :dialog-width="'62%'"
          />
        </screen-border>
      </div>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityWeeklyReportScreen/components/screen-border.vue'
import CustomTable from '@/pages/screen/qualityWeeklyReportScreen/components/custom-table.vue'
import {
  workshopAssessmentFindAllDate,
  workshopAssessmentSaveAll,
  workshopQualityPerformanceFindAllDate,
  workshopQualityPerformanceSaveAll,
  stakeholderAssessmentFindAllDate,
  stakeholderAssessmentSaveAll,
  offlineFlawDetectionFindAllDate,
  offlineFlawDetectionSaveAll
} from '@/api/screen'

export default {
  name: 'ProcessQualityMonitoring',
  components: {
    CustomTable,
    ScreenBorder
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      highValueInput: '',
      headTailFlawInput: '',
      weeklyBuckleInput: '',
      tableObj: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'materialNo',
            keySave: 'materialNo',
            label: '物料号',
            width: '285'
          },
          {
            keyQuery: 'standardNo',
            keySave: 'standardNo',
            label: '订单标准',
            width: '250'
          },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '缺陷',
            width: '250'
          },
          {
            keyQuery: 'unit',
            keySave: 'unit',
            label: '责任单位',
            width: '250'
          },
          {
            keyQuery: 'thickness',
            keySave: 'thickness',
            label: '厚度',
            width: '250'
          },
          {
            keyQuery: 'width',
            keySave: 'width',
            label: '宽度',
            width: '250'
          },
          {
            keyQuery: 'length',
            keySave: 'length',
            label: '长度',
            width: '250'
          },
          {
            keyQuery: 'weight',
            keySave: 'weight',
            label: '重量',
            width: '250'
          },
          {
            keyQuery: 'assessment',
            keySave: 'assessment',
            label: '考核',
            width: '250'
          }
        ],
        url: {
          list: workshopAssessmentFindAllDate,
          save: workshopAssessmentSaveAll
        }
      },
      tableObj2: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          // {
          //   keyQuery: 'materialNo',
          //   keySave: 'materialNo',
          //   label: '物料号',
          //   width: '200'
          // },
          {
            keyQuery: 'online',
            keySave: 'online',
            label: '在线',
            width: '200'
          },
          {
            keyQuery: 'offline',
            keySave: 'offline',
            label: '不合格',
            width: '200'
          },
          {
            keyQuery: 'qualified',
            keySave: 'qualified',
            label: '合格',
            width: '200'
          },
          {
            keyQuery: 'countTotal',
            keySave: 'countTotal',
            label: '总计',
            width: '200'
          },
          {
            keyQuery: 'qualifiedRate',
            keySave: 'qualifiedRate',
            label: '合格率',
            width: '200'
          }
        ],
        url: {
          list: workshopQualityPerformanceFindAllDate,
          save: workshopQualityPerformanceSaveAll
        }
      },
      tableObj3: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'date',
            keySave: 'date',
            label: '日期',
            width: '250'
          },
          {
            keyQuery: 'indicatorExclusion',
            keySave: 'indicatorExclusion',
            label: '指标剔除',
            width: '250'
          },
          {
            keyQuery: 'weeklyBuckle',
            keySave: 'weeklyBuckle',
            label: '剪切瓢曲',
            width: '285'
          },
          {
            keyQuery: 'rollingBuckle',
            keySave: 'rollingBuckle',
            label: '轧制瓢曲',
            width: '285'
          },
          {
            keyQuery: 'countTotal',
            keySave: 'countTotal',
            label: '总计',
            width: '285'
          }
        ],
        url: {
          list: stakeholderAssessmentFindAllDate,
          save: stakeholderAssessmentSaveAll
        }
      },
      tableObj4: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'workshop',
            keySave: 'workshop',
            label: '车间',
            width: '100'
          },
          {
            keyQuery: 'equipmentName',
            keySave: 'equipmentName',
            label: '设备名称',
            width: '175'
          },
          {
            keyQuery: 'checkContentAndStandard',
            keySave: 'checkContentAndStandard',
            label: '检查内容和标准',
            width: '170'
          },
          {
            keyQuery: 'equipmentUsage',
            keySave: 'equipmentUsage',
            label: '设备使用情况',
            width: '170'
          },
          {
            keyQuery: 'remark',
            keySave: 'remark',
            label: '备注',
            width: '170'
          }
        ],
        url: {
          list: offlineFlawDetectionFindAllDate,
          save: offlineFlawDetectionSaveAll
        }
      }
    }
  },
  methods: {}
}
</script>

<style scoped lang="less">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: hidden;

  .chart-row {
    margin-bottom: 10px;
    height: 50%;
    flex-direction: column;
  }

  .chart-row,
  .table-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 10px;
    width: 100%;
  }

  .chart-box,
  .table-box {
    width: 50%;
    flex: 1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .chart-footer {
    margin: 0px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    padding: 5px 8px;
    background: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);
    width: 100%;
  }

  .chart-input {
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      border: 1px solid rgba(31, 198, 255, 0.3);
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      padding: 10px;
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
    }
  }

  /deep/ .el-table {
    background-color: transparent;
    width: 100% !important;
    table-layout: fixed;

    &::before {
      display: none;
    }

    th {
      background-color: rgba(31, 198, 255, 0.3);
      color: #fff;
      border-color: #1fc6ff;
    }

    td {
      background-color: transparent;
      color: #fff;
      border-color: #2e4262;
    }

    tr {
      background-color: transparent;
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-content {
    padding: 10px;
    box-sizing: border-box;
  }
}
</style>
