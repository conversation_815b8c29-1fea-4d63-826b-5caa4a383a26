<!--督办事项-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border title="督办事项">
                <template v-slot:headerRight>
                  <el-row>
                    <span>已完成：</span>
                    <span style="margin-right: 10px;">{{ monthData.finish }}</span>
                    <span>进行中：</span>
                    <span style="margin-right: 10px;">{{ monthData.ongoing }}</span>
                    <span>未完成：</span>
                    <span style="margin-right: 10px;">{{ monthData.unFinish }}</span>
                    <span
                      class="screen-btn"
                      @click="clickAddProject">
                      <el-icon class="el-icon-edit-outline"/>
                      新增
                    </span>
                  </el-row>

                </template>
                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="ProjectData.loading"
                    :data="ProjectData.showGridData"
                    :row-class-name="rowClassName"
                    border>
                    <el-table-column
                      show-overflow-tooltip
                      width="60"
                      align="center"
                      label="序号">
                      <template slot-scope="scope">
                        <div>{{ scope.$index+1 }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="日期"
                      align="center"
                      width="130">
                      <template slot-scope="scope">
                        <div>{{ scope.row.setDate }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="[{text: '安全', value: '安全'}, {text: '环保', value: '环保'},{text: '生产', value: '生产'}, {text: '成本', value: '成本'},{text: '质量', value: '质量'}, {text: '设备', value: '设备'}, {text: '综合', value: '综合'}]"
                      :filter-method="filterMethod"
                      label="类别"
                      align="center"
                      property="category"
                      width="80">
                      <template slot-scope="scope">
                        <div>{{ scope.row.category }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="下发部门"
                      align="center"
                      width="100">
                      <template slot-scope="scope">
                        <div>{{ scope.row.department }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="晨会内容">
                      <template slot-scope="scope">
                        <div>{{ scope.row.meetingContent }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="责任单位"
                      align="center"
                      width="160">
                      <template slot-scope="scope">
                        <div>{{ scope.row.responsibilityUnit }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="牵头人"
                      align="center"
                      width="100">
                      <template slot-scope="scope">
                        <div>{{ scope.row.leader }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="[{text: '未完成', value: '未完成'}, {text: '短期进行中', value: '短期进行中'},{text: '长期进行中', value: '长期进行中'}, {text: '已完成', value: '已完成'}]"
                      :filter-method="filterMethod"
                      property="execution"
                      label="完成情况"
                      align="center"
                      width="120">
                      <template slot-scope="scope">
                        <div>{{ scope.row.execution }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="情况说明">
                      <template slot-scope="scope">
                        <div>{{ scope.row.description }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="完成时间"
                      align="center"
                      width="130">
                      <template slot-scope="scope">
                        <div>{{ scope.row.scheduleTime }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property=""
                      width="150"
                      label="操作">
                      <template v-slot="scope">
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectItem(scope.row)">编辑</span>
                        <span
                          v-command="'/first/steel/complex/delete'"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectDeleteItem(scope.row)">删除</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--督办事项新增修改-->
    <el-dialog
      v-loading="ProjectData.loading"
      :visible.sync="ProjectData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="督办事项">
      <template v-slot:title>
        <div class="custom-dialog-title">
          督办事项
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">日期</div>
          <el-date-picker
            v-model="projectItem.setDate"
            :clearable="false"
            :size="'mini'"
            :value-format="'yyyy-MM-dd'"
            class="screen-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">类别</div>
          <!--          <el-input
            v-model="projectItem.category"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>-->
          <el-select
            v-model="projectItem.category"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in categoryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">下发部门</div>
          <el-select
            v-model="projectItem.department"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in deptList"
              :key="item.id"
              :label="item.name"
              :value="item.id"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">晨会内容</div>
          <el-input
            v-model="projectItem.meetingContent"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">责任单位</div>
          <el-input
            v-model="projectItem.responsibilityUnit"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">牵头人</div>
          <el-input
            v-model="projectItem.leader"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">情况说明</div>
          <el-input
            v-model="projectItem.description"
            :rows="10"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <!--        <div class="dialog-cell">
          <div class="dialog-cell-title">情况说明</div>
          <el-table
            :data="projectItem.supervisionMattersRecordList"
            border>
            <el-table-column
              align="center"
              label="序号"
              width="60">
              <template v-slot="{ row, $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property="finishTime"
              width="200"
              label="完成时间">
              <template v-slot="{ row }">
                <el-date-picker
                  v-model="row.finishTime"
                  :clearable="false"
                  :size="'mini'"
                  :value-format="'yyyy-MM-dd'"
                  style="width: 160px"
                  class="screen-input"/>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property="description"
              label="情况说明">
              <template v-slot="{ row }">
                <el-input v-model="row.description" />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property=""
              width="120"
              label="操作">
              <template slot-scope="scope">
                <span
                  class="screen-btn"
                  @click="delTroubleDetailData(scope.row, $index)">
                  <el-icon class="el-icon-delete"/>
                  删除
                </span>
              </template>
            </el-table-column>
          </el-table>
          <div class="text-center">
            <span
              class="screen-btn"
              @click="addTroubleDetailData()">
              <el-icon class="el-icon-circle-plus-outline"/>
              增加数据
            </span>
          </div>
        </div>-->
        <div class="dialog-cell">
          <div class="dialog-cell-title">完成情况</div>
          <el-select
            v-model="projectItem.execution"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in statusList"
              :key="item.id"
              :label="item.name"
              :value="item.id"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">完成时间</div>
          <el-date-picker
            v-model="projectItem.scheduleTime"
            :clearable="false"
            :size="'mini'"
            :value-format="'yyyy-MM-dd'"
            class="screen-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="addProjectData()">
          确定
        </span>
      </div>
    </el-dialog>

    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="上月导入日期库存">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  oneBulletinBoard_deleteSupervisionMatters,
  oneBulletinBoard_deleteSupervisionRecord,
  oneBulletinBoard_getSupervisionMatters,
  oneBulletinBoard_saveSupervisionMatters,
  oneBulletinBoard_getSupervisionMattersCount
} from '@/api/firstMeeting'
export default {
  name: 'ProjectPage',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      ProjectData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      projectItem: {},
      deptList: [
        {
          id: '公司',
          name: '公司'
        },
        {
          id: '事业部',
          name: '事业部'
        },
        {
          id: '厂级',
          name: '厂级'
        }
      ],
      statusList: [
        {
          id: '未完成',
          name: '未完成'
        },
        {
          id: '短期进行中',
          name: '短期进行中'
        },
        {
          id: '长期进行中',
          name: '长期进行中'
        },
        {
          id: '已完成',
          name: '已完成'
        }
      ],
      categoryList: [
        {
          id: '安全',
          name: '安全'
        },
        {
          id: '环保',
          name: '环保'
        },
        {
          id: '生产',
          name: '生产'
        },
        {
          id: '成本',
          name: '成本'
        },
        {
          id: '质量',
          name: '质量'
        },
        {
          id: '设备',
          name: '设备'
        },
        {
          id: '综合',
          name: '综合'
        }
      ],
      monthData: {
        finish: '',
        ongoing: '',
        unFinish: ''
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getProjectData()
      this.getMonthData()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    filterMethod(value, row, column) {
      const property = column['property']
      return row[property] === value
    },
    rowClassName({ row, rowIndex }) {
      if (row.execution === '未完成') {
        return 'class_red'
      } else if (row.execution === '短期进行中' || row.execution === '进行中') {
        return 'class_yellow'
      } else if (row.execution === '长期进行中') {
        return 'class_orange'
      } else {
        return ''
      }
    },
    //督办点击删除
    clickAddProject() {
      this.projectItem = {
        category: '',
        department: '',
        meetingContent: '',
        scheduleTime: '',
        responsibilityUnit: '',
        leader: '',
        execution: '',
        description: '',
        supervisionMattersRecordList: [],
        setDate: this.cDate
      }
      this.ProjectData.dialogVisible = true
    },
    //督办点击查看详情
    clickProjectItem(row) {
      this.projectItem = JSON.parse(JSON.stringify(row))
      this.ProjectData.dialogVisible = true
    },
    //督办点击删除
    clickProjectDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteProject(row.id)
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    //增加督办明细
    addTroubleDetailData() {
      this.projectItem.supervisionMattersRecordList.push({})
    },
    //删除督办明细
    delTroubleDetailData(row, index) {
      if (row.id) {
        this.deleteTroubleDetail(row.id)
      }
      this.projectItem.supervisionMattersRecordList.splice(index, 1)
    },
    //新增/修改
    addProjectData() {
      // let supervisionMattersRecordList = this.projectItem
      //   .supervisionMattersRecordList
      let setDate = this.projectItem.setDate
      let department = this.projectItem.department
      let scheduleTime = this.projectItem.scheduleTime
      let meetingContent = this.projectItem.meetingContent
      let execution = this.projectItem.execution
      if (setDate === null || setDate.length === 0) {
        this.$message.warning('请选择日期！')
        return
      }
      if (department === null || department.length === 0) {
        this.$message.warning('请选择下发部门！')
        return
      }
      if (meetingContent === null || meetingContent.length === 0) {
        this.$message.warning('请输入晨会内容！')
        return
      }
      // if (scheduleTime === null || scheduleTime.length === 0) {
      //   this.$message.warning('请选择安排时间！')
      //   return
      // }
      // if (execution === null || execution.length === 0) {
      //   this.$message.warning('请选择完成情况！')
      //   return
      // }
      const params = [this.projectItem]
      this.ProjectData.loading = true
      post(oneBulletinBoard_saveSupervisionMatters, params)
        .then(res => {
          if (res.success) {
            this.$notify.success('操作成功！')
            this.ProjectData.dialogVisible = false
            this.getProjectData()
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    //删除
    deleteProject(id) {
      const params = [
        {
          id: id
        }
      ]
      post(oneBulletinBoard_deleteSupervisionMatters, params).then(res => {
        if (res.success) {
          this.$notify.success('删除成功！')
          this.getProjectData()
        }
      })
    },
    //删除督办明细
    deleteTroubleDetail(id) {
      const params = [
        {
          id: id
        }
      ]
      post(oneBulletinBoard_deleteSupervisionRecord, params).then(res => {
        if (res.success) {
          this.$message.success('删除成功！')
        }
      })
    },
    calculateHeight() {
      this.ProjectData.maxHeight = this.$refs.table1.offsetHeight
    },
    getProjectData() {
      this.ProjectData.loading = true
      post(oneBulletinBoard_getSupervisionMatters, {
        setDate: this.cDate
      })
        .then(res => {
          if (res && res.success) {
            // this.$message.success('查询成功！')
            this.ProjectData.showGridData = res.data.map(item => {
              let detailList = item.supervisionMattersRecordList
              let description = item.description
              if (description === null || description.length === 0) {
                if (detailList && detailList.length > 0) {
                  description = detailList[detailList.length - 1].description
                }
              }
              return {
                id: item.id,
                setDate: item.setDate,
                category: item.category,
                department: item.department,
                meetingContent: item.meetingContent,
                scheduleTime: item.scheduleTime,
                responsibilityUnit: item.responsibilityUnit,
                leader: item.leader,
                execution:
                  item.execution === '未开始' ? '未完成' : item.execution,
                description: description,
                supervisionMattersRecordList: item.supervisionMattersRecordList
              }
            })
            this.ProjectData.gridData = lodash.cloneDeep(
              this.ProjectData.showGridData
            )
          } else {
            this.$message.error(res.message)
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    getMonthData() {
      post(oneBulletinBoard_getSupervisionMattersCount, {
        setDate: this.cDate.substring(0, 7)
      }).then(res => {
        if (res && res.success) {
          this.monthData = res.data
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
/deep/ .el-table .class_red {
  background: #fd0000;
  color: black;
}
/deep/ .el-table .class_yellow {
  background: #fdfd00;
  color: black;
}
/deep/ .el-table .class_orange {
  background: #f99f04;
  color: black;
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
