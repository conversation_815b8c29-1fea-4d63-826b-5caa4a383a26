<template>
  <div class="content">
    <div class="formTab">
      <el-form 
        v-if="tabModal=='维护统计表'"
        :inline="true" 
        :model="formInline">
        <el-form-item label="炉号">
          <el-input 
            v-model="formInline.heatNO" 
            class="screen-input"
            placeholder="炉号" />
        </el-form-item>
        <el-form-item 
          :rules="[
            { required: true},
          ]" 
          label="开始日期">
          <el-date-picker
            v-model="formInline.startTime"
            class="screen-input"
            type="datetime"
            placeholder="开始日期"
          />
        </el-form-item>
        <el-form-item 
          :rules="[
            { required: true},
          ]"
          label="结束日期" >
          <el-date-picker
            v-model="formInline.endTime"
            class="screen-input"
            type="datetime"
            placeholder="开始日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button 
            v-loading="reportLoading"
            type="primary"
            @click="reportPost(formInline)">查询</el-button>
        </el-form-item>
      </el-form>
      <el-form 
        v-else-if="tabModal=='CAD日报'"
        :inline="true" 
        :model="formWorkshop">
        <el-form-item label="炉号">
          <el-input 
            v-model="formWorkshop.heatNO" 
            class="screen-input"
            placeholder="炉号" />
        </el-form-item>
        <el-form-item 
          :rules="[
            { required: true},
          ]" 
          label="开始日期">
          <el-date-picker
            v-model="formWorkshop.startTime"
            class="screen-input"
            type="datetime"
            placeholder="开始日期"
          />
        </el-form-item>
        <el-form-item 
          :rules="[
            { required: true},
          ]"
          label="结束日期" >
          <el-date-picker
            v-model="formWorkshop.endTime"
            class="screen-input"
            type="datetime"
            placeholder="开始日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button 
            v-loading="reportLoading"
            type="primary"
            @click="workshopPost(formWorkshop)">查询</el-button>
        </el-form-item>
      </el-form>
      <el-form 
        v-else-if="tabModal=='CAD日报(简报)'"
        :inline="true" 
        :model="formWorkshop2">
        <el-form-item label="炉号">
          <el-input 
            v-model="formWorkshop2.heatNO" 
            class="screen-input"
            placeholder="炉号" />
        </el-form-item>
        <el-form-item 
          :rules="[
            { required: true},
          ]" 
          label="开始日期">
          <el-date-picker
            v-model="formWorkshop2.startTime"
            class="screen-input"
            type="datetime"
            placeholder="开始日期"
          />
        </el-form-item>
        <el-form-item 
          :rules="[
            { required: true},
          ]"
          label="结束日期" >
          <el-date-picker
            v-model="formWorkshop2.endTime"
            class="screen-input"
            type="datetime"
            placeholder="开始日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button 
            v-loading="reportLoading"
            type="primary"
            @click="workshopPost2(formWorkshop2)">查询</el-button>
        </el-form-item>
      </el-form>
      <div v-else/>
      <el-radio-group 
        v-model="tabModal"
        style="margin-bottom: 18px;"
        class="screen-input">
        <el-radio-button
          class="screen-input" 
          label="CAD分析"/>
        <el-radio-button
          class="screen-input" 
          label="CAD日报(简报)"/>
        <el-radio-button
          class="screen-input" 
          label="CAD日报"/>
        <el-radio-button 
          label="维护统计表" 
          class="screen-input"/>
      </el-radio-group>
    </div>
    <div 
      v-if="tabModal=='维护统计表'" 
      class="content-item">
      <el-row 
        :gutter="32" 
        class="full-height">
        <el-col 
          :span="24" 
          class="full-height">
          <screen-border title="CAD">
            <template v-slot:headerRight >
              <el-button 
                type="primary"
                size="small"
                @click="exportunfinished"
              >
                导出
              </el-button>
              <el-button 
                type="primary"
                size="small"
                @click="reportShow2"
              >
                维护
              </el-button>
            </template>
            <div 
              ref="table1"
              class="scroll-wrapper" 
            >
              <el-table
                v-loading="reportLoading"
                :data="reportData2"
                :key="'reportData2'"
                :height="maxHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  :filter-method="filterHandler"
                  :filters="filterHandlerData(reportData,'cadName')"
                  property="cadName"
                  width="140"
                  fixed="left"
                  label="CAD锁定详称"/>
                <el-table-column
                  property="reasonComment"
                  width="140"
                  fixed="left"
                  label="原因说明"/>
                <el-table-column
                  property="heatNo"
                  width="100"
                  fixed="left"
                  label="炉号"/>
                <el-table-column
                  property="steelGrade"
                  width="125"
                  fixed="left"
                  label="录入钢种"/>
                <el-table-column
                  property="inTime"
                  width="210"
                  fixed="left"
                  label="锁定时间"/>
                <el-table-column
                  :filter-method="filterHandler"
                  :filters="filterHandlerData(reportData,'machNo')"
                  property="machNo"
                  width="140"
                  fixed="left"
                  label="连铸机号"/>
                <el-table-column
                  property="Thk"
                  width="140"
                  label="厚度"/>
                <el-table-column
                  property="weight"
                  width="140"
                  label="重量">
                  <template #default="scope">
                    {{ (scope.row.weight*1).toFixed(1) }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="prodDate"
                  width="210"
                  label="生产时间"/>
                <el-table-column
                  property="unlockedW"
                  width="140"
                  label="已解锁量（吨）">
                  <template #default="scope">
                    {{ (scope.row.unlockedW*1).toFixed(1) }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="lockedW"
                  width="140"
                  label="未解锁量（吨）">
                  <template #default="scope">
                    {{ (scope.row.lockedW*1).toFixed(1) }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="shiftNo"
                  width="85"
                  label="班别"/>
                <el-table-column
                  property="realReason"
                  width="140"
                  label="真正原因"/>
                <el-table-column
                  property="dutyUnit"
                  width="140"
                  label="责任单位"/>
                <el-table-column
                  property="description"
                  width="140"
                  label="情况说明"/>
                <el-table-column
                  property="measures"
                  width="140"
                  label="整改措施"/>
                <el-table-column
                  property="dispose"
                  width="140"
                  label="坯料处置情况"/>
                <el-table-column
                  width="140"
                  label="操作">
                  <template slot-scope="scope">
                    <el-button 
                      type="primary"
                      size="small"
                      @click="reportShow(scope.row)"
                    >
                      维护
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                :current-page="report.page"
                :page-size="report.size"
                :page-sizes="[40, 60, 80]"
                :total="report.total"
                background
                layout="total, sizes, prev, pager, next, jumper"
                class="pagination"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div 
      v-else-if="tabModal=='CAD日报'" 
      class="content-item">
      <el-row 
        :gutter="32" 
        class="full-height">
        <el-col 
          :span="24" 
          class="full-height">
          <screen-border title="CAD">
            <template v-slot:headerRight />
            <div 
              ref="table2" 
              class="scroll-wrapper"
            >
              <el-table
                v-loading="workshopLoading"
                :data="workshopData"
                :key="'workshopData2'"
                :span-method="objectSpanMethod"
                :height="maxHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  property="DutyUnit"
                  width="140"
                  fixed
                  label="责任单位"/>
                <el-table-column
                  property="TotalWgt"
                  width="115"
                  fixed
                  label="总量（吨）"/>
                <el-table-column
                  property="Reason"
                  width="140"
                  fixed
                  label="真正原因"/>
                <el-table-column
                  property="PartWgt"
                  width="115"
                  fixed
                  label="重量（吨）">
                  <template #default="scope">
                    {{ (scope.row.PartWgt*1).toFixed(1) }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="heatNo"
                  width="100"
                  fixed
                  label="炉号"/>
                <el-table-column
                  property="steelGrade"
                  width="125"
                  fixed
                  label="录入钢种"/>
                <el-table-column
                  property="inTime"
                  width="210"
                  label="锁定时间"/>
                <el-table-column
                  property="machNo"
                  width="120"
                  label="连铸机号"/>
                <el-table-column
                  property="Thk"
                  width="115"
                  label="厚度"/>
                <el-table-column
                  property="weight"
                  width="115"
                  label="重量(吨)">
                  <template #default="scope">
                    {{ (scope.row.weight*1).toFixed(1) }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="prodDate"
                  width="210"
                  label="生产时间"/>
                <el-table-column
                  property="unlockedW"
                  width="140"
                  label="已解锁量（吨）">
                  <template #default="scope">
                    {{ (scope.row.unlockedW*1).toFixed(1) }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="lockedW"
                  width="140"
                  label="未解锁量（吨）">
                  <template #default="scope">
                    {{ (scope.row.lockedW*1).toFixed(1) }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="description"
                  width="140"
                  label="情况说明"/>
                <el-table-column
                  property="measures"
                  width="140"
                  label="整改措施"/>
                <el-table-column
                  property="dispose"
                  width="140"
                  label="坯料处置情况"/>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div 
      v-else-if="tabModal=='CAD日报(简报)'"
      class="content-item">
      <el-row 
        :gutter="32" 
        class="full-height">
        <el-col 
          :span="24" 
          class="full-height">
          <screen-border title="CAD">
            <template v-slot:headerRight />
            <div 
              ref="table3" 
              class="scroll-wrapper"
            >
              <el-table
                v-loading="workshopLoading2"
                :data="workshopData2"
                :key="'workshopData22'"
                :span-method="objectSpanMethod2"
                :height="maxHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  property="DutyUnit"
                  width="140"
                  fixed
                  label="责任单位"/>
                <el-table-column
                  property="TotalWgt"
                  width="115"
                  fixed
                  label="总量（吨）"/>
                <el-table-column
                  property="Reason"
                  width="150"
                  fixed
                  label="真正原因"/>
                <el-table-column
                  property="PartWgt"
                  width="150"
                  fixed
                  label="锁定量（吨）">
                  <template #default="scope">
                    {{ (scope.row.PartWgt*1).toFixed(1) }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="UnlockedW"
                  width="160"
                  fixed
                  label="解锁量（吨）">
                  <template #default="scope">
                    {{ (scope.row.UnlockedW*1).toFixed(1) }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="LockedW"
                  width="170"
                  fixed
                  label="待解锁量（吨）">
                  <template #default="scope">
                    {{ (scope.row.LockedW*1).toFixed(1) }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="ReasonComment"
                  width="720"
                  label="情况说明"/>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <template
      v-else>
      <div 
        ref="tbHit"
        class="content-item">
        <el-row 
          :gutter="32" 
          class="full-height">
          <el-col 
            :span="24" 
            class="full-height">
            <screen-border title="月统计">
              <template v-slot:headerRight />
              <div 
                class="scroll-wrapper" 
              >
                <p-line-chart
                  :show-legend="true"
                  :chart-data="[
                    {
                      name:'总量（吨）',
                      data:cadMonthData.map(item=>{
                        return (item.TotalWgt*1).toFixed(1)
                      })
                    },
                  ]"
                  :pos-show="true"
                  :boundary-gap="true"
                  :x-data="cadMonthData.map(item=>{
                    return item.Day
                })"/>
              </div>
            </screen-border>
          </el-col>
        </el-row>
      </div>
      <div class="content-hold"/>
      <div 
        class="content-item">
        <el-row 
          :gutter="32" 
          class="full-height">
          <el-col 
            :span="12" 
            class="full-height">
            <screen-border title="班组">
              <template v-slot:headerRight />
              <bars-chart
                :bar-width="50"
                :unit="'吨'"
                :color="[
                  '#2196f3',
                ]"
                :pos-size="24"
                :chart-data="original.bar1"
                :x-data="original.barX1"
                @selected="getIncidenceRate($event)"/>
            </screen-border>
          </el-col>
          <el-col 
            :span="12" 
            class="full-height">
            <screen-border title="车间">
              <template v-slot:headerRight />
              <bars-chart
                :bar-width="50"
                :unit="'吨'"
                :color="[
                  '#2196f3',
                ]"
                :pos-size="24"
                :chart-data="original2.bar1"
                :x-data="original2.barX1"
                @selected="getIncidenceRate2($event)"/>
            </screen-border>
          </el-col>
        </el-row>
      </div>
    </template>

    <el-dialog
      :visible.sync="reportUpDataShow"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="人工维护">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box"/>
          人工维护
        </div>
      </template>
      <div
        :style="{height: '520px'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">炉号</div>
          <el-input
            :rows="3"
            v-model="reportUpData.heatNo"
            disabled
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">CAD锁定详称</div>
          <el-input
            :rows="3"
            v-model="reportUpData.cadName"
            disabled
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">真正原因</div>
          <el-input
            :rows="3"
            v-model="reportUpData.realReason"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">责任单位</div>
          <el-input
            :rows="3"
            v-model="reportUpData.dutyUnit"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">情况说明</div>
          <el-input
            :rows="3"
            v-model="reportUpData.description"
            clearable
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改措施</div>
          <el-input
            :rows="3"
            v-model="reportUpData.measures"
            clearable
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">坯料处置情况</div>
          <el-input
            :rows="3"
            v-model="reportUpData.dispose"
            clearable
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          v-command="'/screen/firstSteelmakingPlant/edit'"
          class="screen-btn"
          @click="reportDataUpPost(reportUpData)"
        >
          确定
        </span>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="showData.show"
      :width="'1200px'"
      :close-on-click-modal="false"
      :title="showData.title"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box"/>
          {{ showData.title }}
        </div>
      </template>
      <div
        :style="{height: '520px'}"
        class="dialog-body">
        <div style="display: flex;justify-content: space-between;flex-wrap: wrap;">

          <div 
            v-for="item in showData.data" 
            :key="item.CadName" 
            class="dialog-cell" 
            style="width: 30%;display: inline-block;">
            <div class="dialog-cell-title">{{ item.CadName }}</div>
            <el-input
              :rows="3"
              v-model="item.Wgt"
              disabled
              clearable
              class="dialog-cell-input"/>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="cad.show"
      :width="'80%'"
      :close-on-click-modal="false"
      :title="'CAD维护(复选框为是否修改项)'"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="savevariety2">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          CAD维护(复选框为是否修改项)
        </div>
      </template>
      <div style="height: 500px;overflow-y: auto;">
        <div 
          class="scroll-wrapper" 
        >
          <el-table
            v-loading="reportLoading"
            :data="cad.finish"
            :key="'cad.reportData2'"
            :height="500"
            class="font-table center-table"
            border>
            <el-table-column 
              fixed
              property="selection"
              width="55" >
              <template
                #default="{ row }">
                <input 
                  v-model="row.selection"
                  type="checkbox" >
              </template>
            </el-table-column>
            <el-table-column
              :filter-method="filterHandler"
              :filters="filterHandlerData(reportData,'machNo')"
              property="cadName"
              fixed
              width="140"
              label="CAD锁定详称"/>
            <el-table-column
              fixed
              property="reasonComment"
              width="140"
              label="原因说明"/>
            <el-table-column
              property="heatNo"
              fixed
              width="115"
              label="炉号"/>
            <el-table-column
              :filter-method="filterHandler"
              :filters="filterHandlerData(reportData,'machNo')"
              fixed
              property="machNo"
              width="120"
              label="连铸机号"/>
              
            <el-table-column
              property="steelGrade"
              fixed
              width="115"
              label="录入钢种"/>
            <el-table-column
              property="realReason"
              width="140"
              label="真正原因">
              <template
                v-slot="{ row }">
                <el-input
                  v-model="row.realReason"
                  :rows="4"
                  type="textarea"/>
              </template>
            </el-table-column>
            <el-table-column
              property="dutyUnit"
              width="140"
              label="责任单位">
              <template
                v-slot="{ row }">
                <el-input
                  v-model="row.dutyUnit"
                  :rows="4"
                  type="textarea"/>
              </template>
            </el-table-column>
            <el-table-column
              property="description"
              width="140"
              label="情况说明">
              <template
                v-slot="{ row }">
                <el-input
                  v-model="row.description"
                  :rows="4"
                  type="textarea"/>
              </template>
            </el-table-column>
            <el-table-column
              property="measures"
              width="140"
              label="整改措施">
              <template
                v-slot="{ row }">
                <el-input
                  v-model="row.measures"
                  :rows="4"
                  type="textarea"/>
              </template>
            </el-table-column>
            <el-table-column
              property="dispose"
              width="155"
              label="坯料处置情况">
              <template
                v-slot="{ row }">
                <el-input
                  v-model="row.dispose"
                  :rows="4"
                  type="textarea"/>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 数据混入
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
// 边框组件
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { firstMorningMeeting } from '@/api/screen'
import PLineChart from '../component/p-line-chart.vue'
import BarsChart from '@/pages/screen/firstSteelmakingPlantTest/component/bars-chart'
import { post, get } from '@/lib/Util'
import * as lodash from 'lodash'
export default {
  name: 'CAD',
  components: {
    ScreenBorder,
    PLineChart,
    BarsChart
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      tabModal: 'CAD分析',
      // 维护统计表查询条件
      formInline: {
        heatNO: '',
        startTime: '',
        endTime: ''
      },
      reportData: [],
      reportData2: [],
      report: {
        page: 1,
        size: 40,
        total: 0
      },
      reportUpDataShow: false,
      reportUpData: {
        heatNo: '',
        cadName: '',
        realReason: '',
        dutyUnit: '',
        description: '',
        measures: '',
        dispose: ''
      },
      reportLoading: false,
      // CAD日报查询条件
      formWorkshop: {
        heatNO: '',
        startTime: '',
        endTime: ''
      },
      formWorkshop2: {
        heatNO: '',
        startTime: '',
        endTime: ''
      },
      workshopData: [],
      workshopData2: [],
      workshopCount: [],
      workshopCount2: [],
      workshopCount2_1: [],
      workshop: {
        page: 1,
        size: 40,
        total: 0
      },
      workshopLoading: false,
      workshopLoading2: false,
      maxHeight: null,
      cadMonthData: [],
      original: {
        bar1: [
          {
            name: '重量',
            data: [],
            barGap: '0.5'
          }
        ],
        barX1: [],
        cadGroupReportItems: []
      },
      original2: {
        bar1: [
          {
            name: '重量',
            data: [],
            barGap: '0.5'
          }
        ],
        barX1: [],
        cadGroupReportItems: []
      },
      showData: {
        show: false,
        data: [],
        title: ''
      },
      cad: {
        finish: [],
        show: false
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.formInline.startTime =
        this.$moment(this.cDate)
          .add(-1, 'days')
          .format('yyyy-MM-DD') + ' 16:00:00'
      this.formInline.endTime =
        this.$moment(this.cDate)
          .add(2, 'days')
          .format('yyyy-MM-DD') + ' 00:00:00'
      this.formWorkshop = lodash.cloneDeep({
        ...this.formInline,
        endTime: this.$moment(this.cDate).format('yyyy-MM-DD') + ' 16:00:00'
      })
      this.formWorkshop2 = lodash.cloneDeep({
        ...this.formInline,
        endTime: this.$moment(this.cDate).format('yyyy-MM-DD') + ' 16:00:00'
      })
      this.reportPost(this.formInline)
      this.workshopPost(this.formWorkshop)
      this.workshopPost2(this.formWorkshop2)
      this.postCharts(this.selectDate + ' 00:00:00')
    },
    tabModal: function() {
      this.heightChange()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.formInline.startTime =
      this.$moment(this.cDate)
        .add(-1, 'days')
        .format('yyyy-MM-DD') + ' 16:00:00'
    this.formInline.endTime =
      this.$moment(this.cDate)
        .add(2, 'days')
        .format('yyyy-MM-DD') + ' 00:00:00'
    this.formWorkshop = lodash.cloneDeep({
      ...this.formInline,
      endTime: this.$moment(this.cDate).format('yyyy-MM-DD') + ' 16:00:00'
    })
    this.formWorkshop2 = lodash.cloneDeep({
      ...this.formInline,
      endTime: this.$moment(this.cDate).format('yyyy-MM-DD') + ' 16:00:00'
    })
    this.reportPost(this.formInline)
    this.workshopPost(this.formWorkshop)
    this.workshopPost2(this.formWorkshop2)
    this.postCharts(this.selectDate + ' 00:00:00')
  },
  mounted() {
    this.heightChange()
  },
  methods: {
    reportPost(data) {
      if (
        data.startTime === '' ||
        data.endTime === '' ||
        data.startTime === null ||
        data.endTime === undefined ||
        data.startTime === undefined ||
        data.endTime === null
      ) {
        return this.$message.error('时间不能为空')
      }
      this.reportLoading = true
      post(firstMorningMeeting.report, {
        ...data,
        startTime: this.$moment(data.startTime).format('yyyy-MM-DD HH:mm:ss'),
        endTime: this.$moment(data.endTime).format('yyyy-MM-DD HH:mm:ss')
      })
        .then(res => {
          this.reportLoading = false
          this.reportData = lodash.cloneDeep(res)
          this.report.total = res.length
          this.fetchData()
        })
        .catch(err => {
          this.reportLoading = false
          this.reportData = lodash.cloneDeep([])
          this.report.total = 0
          this.fetchData()
        })
    },
    workshopPost(data) {
      if (
        data.startTime === '' ||
        data.endTime === '' ||
        data.startTime === null ||
        data.endTime === undefined ||
        data.startTime === undefined ||
        data.endTime === null
      ) {
        return this.$message.error('时间不能为空')
      }
      this.workshopLoading = true
      post(firstMorningMeeting.statistics, {
        ...data,
        startTime: this.$moment(data.startTime).format('yyyy-MM-DD HH:mm:ss'),
        endTime: this.$moment(data.endTime).format('yyyy-MM-DD HH:mm:ss')
      })
        .then(res => {
          this.workshopLoading = false
          const data = []
          this.workshopCount = lodash.cloneDeep([])
          this.workshopCount2 = lodash.cloneDeep([])
          res.forEach(item => {
            item.ReportItems.forEach((its, index) => {
              its.Statiscs.forEach((its2, index2) => {
                if (index2 === 0 && index === 0) {
                  let count = 0
                  // 合并前两列
                  item.ReportItems.forEach(its3 => {
                    count += its3.Statiscs.length
                  })
                  this.workshopCount.push(count)
                } else {
                  this.workshopCount.push(0)
                }
                if (index2 === 0) {
                  this.workshopCount2.push(its.Statiscs.length)
                } else {
                  this.workshopCount2.push(0)
                }
                data.push({
                  DutyUnit: item.DutyUnit,
                  TotalWgt: item.TotalWgt,
                  PartWgt: its.PartWgt,
                  Reason: its.Reason,
                  ...its2
                })
              })
            })
          })
          this.workshopData = lodash.cloneDeep(data)
        })
        .catch(err => {
          this.workshopLoading = false
          this.workshopData = lodash.cloneDeep([])
        })
    },
    workshopPost2(data) {
      if (
        data.startTime === '' ||
        data.endTime === '' ||
        data.startTime === null ||
        data.endTime === undefined ||
        data.startTime === undefined ||
        data.endTime === null
      ) {
        return this.$message.error('时间不能为空')
      }
      this.workshopLoading2 = true
      post(firstMorningMeeting.statisticsNew, {
        ...data,
        startTime: this.$moment(data.startTime).format('yyyy-MM-DD HH:mm:ss'),
        endTime: this.$moment(data.endTime).format('yyyy-MM-DD HH:mm:ss')
      })
        .then(res => {
          this.workshopLoading2 = false
          const data = []
          this.workshopCount2_1 = lodash.cloneDeep([])
          this.workshopCount2_2 = lodash.cloneDeep([])
          let TotalWgt = 0
          let PartWgt = 0
          let UnlockedW = 0
          let LockedW = 0
          res.forEach(item => {
            TotalWgt += item.TotalWgt
            item.ReportItems.forEach((its, index) => {
              if (index === 0) {
                let count = item.ReportItems.length
                this.workshopCount2_1.push(count)
              } else {
                this.workshopCount2_1.push(0)
              }
              PartWgt += its.PartWgt
              UnlockedW += its.UnlockedW
              LockedW += its.LockedW
              data.push({
                DutyUnit: item.DutyUnit,
                TotalWgt: item.TotalWgt,
                ...its
              })
            })
          })
          this.workshopCount2_1.push(1)
          this.workshopData2 = lodash.cloneDeep(
            data.concat({
              DutyUnit: '合计',
              TotalWgt,
              PartWgt,
              UnlockedW,
              LockedW
            })
          )
        })
        .catch(err => {
          this.workshopLoading2 = false
          this.workshopData2 = lodash.cloneDeep([])
        })
    },
    handleSizeChange(val) {
      this.report.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.report.page = val
      this.fetchData()
    },
    fetchData() {
      const start = (this.report.page - 1) * this.report.size
      const end = start + this.report.size
      this.reportData2 = this.reportData.slice(start, end)
      this.report.total = this.reportData.length
    },
    reportShow(row) {
      this.reportUpDataShow = true
      this.reportUpData = lodash.cloneDeep({
        heatNo: '',
        cadName: '',
        realReason: '',
        dutyUnit: '',
        description: '',
        measures: '',
        dispose: ''
      })
      const upData = [
        'heatNo',
        'cadName',
        'realReason',
        'dutyUnit',
        'description',
        'measures',
        'dispose'
      ]
      upData.forEach(item => {
        this.reportUpData[item] = row[item]
      })
    },
    reportShow2() {
      this.cad.show = true
      this.cad.finish = lodash.cloneDeep(this.reportData2)
      this.cad.finish.forEach(item => {
        item.realReason =
          item.realReason != undefined &&
          item.realReason != null &&
          item.realReason.length != 0 &&
          item.cadName != '其他连铸生产异常' &&
          item.cadName != '连铸生产异常' &&
          item.cadName != '修磨保留'
            ? item.realReason
            : item.cadName
        item.selection = false
        return item
      })
    },
    savevariety2() {
      this.reportLoading = true
      this.cad.show = false
      post(
        firstMorningMeeting.reportUpDatas,
        this.cad.finish.filter(item => item.selection)
      )
        .then(res => {
          this.reportLoading = false
          this.$message.success('更新成功')
          this.reportPost(this.formInline)
        })
        .catch(err => {
          this.reportLoading = false
          this.$message.error('更新失败')
        })
    },
    reportDataUpPost(data) {
      this.reportLoading = true
      this.reportUpDataShow = false
      post(firstMorningMeeting.reportUpData, data)
        .then(res => {
          this.reportLoading = false
          this.$message.success('更新成功')
          this.reportPost(this.formInline)
        })
        .catch(err => {
          this.reportLoading = false
          this.$message.error('更新失败')
        })
    },
    postCharts(data) {
      get(firstMorningMeeting.cadMonth(data))
        .then(res => {
          this.cadMonthData = lodash.cloneDeep(res)
        })
        .catch(err => {
          this.cadMonthData = lodash.cloneDeep([])
        })
      get(firstMorningMeeting.cadGroup(data))
        .then(res => {
          this.original.bar1[0].data = lodash.cloneDeep(
            res.map(item => Number(item.TotalWgt).toFixed(1))
          )
          this.original.barX1 = lodash.cloneDeep(res.map(item => item.Group))
          this.original.cadGroupReportItems = lodash.cloneDeep(
            res.map(item => item.cadGroupReportItems)
          )
        })
        .catch(err => {
          this.original.bar1[0].data = lodash.cloneDeep([])
          this.original.barX1 = lodash.cloneDeep(['甲', '乙', '丙', '丁'])
          this.original.cadGroupReportItems = lodash.cloneDeep([])
        })
      get(firstMorningMeeting.cadWorkshop(data))
        .then(res => {
          this.original2.bar1[0].data = lodash.cloneDeep(
            res.map(item => Number(item.TotalWgt).toFixed(1))
          )
          this.original2.barX1 = lodash.cloneDeep(
            res.map(item => item.Workshop)
          )
          this.original2.cadGroupReportItems = lodash.cloneDeep(
            res.map(item => item.cadGroupReportItems)
          )
        })
        .catch(err => {
          this.original2.bar1[0].data = lodash.cloneDeep([])
          this.original2.barX1 = lodash.cloneDeep([])
          this.original2.cadGroupReportItems = lodash.cloneDeep([])
        })
    },
    getIncidenceRate(data) {
      let team = this.original.cadGroupReportItems[
        data.fromActionPayload.dataIndexInside
      ].map(item => {
        return {
          Wgt: Number(item.Wgt).toFixed(1),
          CadName: item.CadName
        }
      })
      this.showData = {
        show: true,
        data: team,
        title:
          '班组-' + this.original.barX1[data.fromActionPayload.dataIndexInside]
      }
    },
    getIncidenceRate2(data) {
      let team = this.original2.cadGroupReportItems[
        data.fromActionPayload.dataIndexInside
      ].map(item => {
        return {
          Wgt: Number(item.Wgt).toFixed(1),
          CadName: item.CadName
        }
      })
      this.showData = {
        show: true,
        data: team,
        title:
          '车间-' + this.original2.barX1[data.fromActionPayload.dataIndexInside]
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0 || columnIndex == 1) {
        return {
          rowspan: this.workshopCount[rowIndex],
          colspan: 1
        }
      } else if (columnIndex == 2 || columnIndex == 3) {
        return {
          rowspan: this.workshopCount2[rowIndex],
          colspan: 1
        }
      }
    },
    objectSpanMethod2({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0 || columnIndex == 1) {
        return {
          rowspan: this.workshopCount2_1[rowIndex],
          colspan: 1
        }
      }
    },
    filterHandler(value, row, column) {
      const property = column['property']
      return row[property] === value
    },
    filterHandlerData(data, row) {
      const its = data.map(item => item[row])
      const newArr = Array.from(new Set(its))
      return newArr.map(item => ({ text: item, value: item }))
    },
    exportunfinished() {
      const data = [
        {
          cadName: 'CAD锁定详称',
          reasonComment: '原因说明',
          heatNo: '炉号',
          steelGrade: '录入钢种',
          inTime: '锁定时间',
          machNo: '连铸机号',
          Thk: '厚度',
          weight: '重量',
          prodDate: '生产时间',
          unlockedW: '已解锁量（吨）',
          lockedW: '未解锁量（吨）',
          shiftNo: '班别',
          realReason: '真正原因',
          dutyUnit: '责任单位',
          description: '情况说明',
          measures: '整改措施',
          dispose: '坯料处置情况'
        }
      ].concat(
        _.map(_.cloneDeep(this.reportData), item => {
          let datas = {}
          _.forEach(
            [
              'cadName',
              'reasonComment',
              'heatNo',
              'steelGrade',
              'inTime',
              'machNo',
              'Thk',
              'weight',
              'prodDate',
              'unlockedW',
              'lockedW',
              'shiftNo',
              'realReason',
              'dutyUnit',
              'description',
              'measures',
              'dispose'
            ],
            items => {
              datas[items] = item[items]
            }
          )
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `维护统计表（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 高度数据
    heightChange() {
      if (this.$refs.tbHit != undefined) {
        this.maxHeight = this.$refs.tbHit.offsetHeight * 2 - 90
      }
      if (this.$refs.table1 != undefined) {
        this.maxHeight = this.$refs.table1.offsetHeight - 45
      }
      if (this.$refs.table2 != undefined) {
        this.maxHeight = this.$refs.table2.offsetHeight - 45
      }
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .formTab {
    display: flex;
    justify-content: space-between;
    /deep/.el-form-item__label {
      color: #fff;
    }
  }
  .pagination {
    color: #fff;
    margin-top: 10px;
    /deep/.el-pagination__total,
    /deep/.el-pagination__sizes,
    /deep/.el-pagination__jump {
      color: #fff;
    }
  }
  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
    position: relative;

    .operate-box {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}

.operate-box {
  position: absolute;
  right: 20px;
  top: 10px;
  z-index: 5;
}

.dialog-body {
  overflow: scroll;

  .dialog-cell {
    margin-bottom: 12px;

    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
  }
}
.screen-input {
  /deep/ .el-textarea__inner,
  /deep/ .el-input__inner {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
    padding-right: 5px;
  }
  /deep/ .el-input__prefix {
    color: #fff;
  }
  /deep/ .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    background: rgba(31, 198, 255, 0.3);
    border-color: rgba(31, 198, 255, 0.6);
    //border: 1px solid #1fc6ff;
  }
  /deep/ .el-radio-button--mini .el-radio-button__inner {
    background: #d8edff;
  }
}
/deep/ tr.el-table__row {
  background: #041a21 !important;
}
/deep/ tr.hover-row {
  background: #041a21 !important;
}
/deep/ .el-table__body tr.hover-row > td.el-table__cell {
  background: rgb(22, 79, 96) !important;
}
/deep/ .el-table th.el-table__cell {
  background: #093d4d !important;
}
/deep/ .el-table__fixed {
  height: calc(100% - 10px) !important;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
  height: 10px !important;
}
</style>
