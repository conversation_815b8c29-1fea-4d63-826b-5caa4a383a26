<template>
  <div class="container">
    <screen-border title="尺寸抽查记录">
      <template v-slot:headerRight>
        <span 
          v-command="'/screen/qualityDailyReportScreen/edit'"
          class="screen-btn"
          @click="handleDialogOperationVisible()"
        >
          <el-icon class="el-icon-edit-outline"/>
          操作
        </span>
      </template>
      <div class="table-container">
        <!-- 第一个表格 -->
        <custom-table
          ref="dimensionSpotCheckRef" 
          :show-table="true"
          :show-edit="false"
          :key="'dimensionSpotCheck'"
          :title="'尺寸抽查记录'"
          :setting="tableObj.setting"
          :url-list="tableObj.url.list"
          :url-save="tableObj.url.save"
          :select-date="selectDate"
          @close="tableVisible.dimensionSpotCheck = false"
        />

      </div>
      <template v-slot:bottom>
        <div class="chart-footer">
          <el-input
            v-model="processAppealDesc"
            :rows="2"
            type="textarea"
            class="chart-input"
            resize="none"
          />
        </div>
      </template>
    </screen-border>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityDailyReportScreen/components/screen-border.vue'
import CustomTable from '@/pages/screen/qualityDailyReportScreen/components/custom-table.vue'
import { dimensionSpotCheck, dimensionSpotCheckSave } from '@/api/screen'

export default {
  name: 'DimensionSpotCheck',
  components: {
    ScreenBorder,
    CustomTable
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      processAppealDesc: '',
      tableObj: {
        url: {
          list: dimensionSpotCheck,
          save: dimensionSpotCheckSave
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'steelPlateNumber',
            keySave: 'steelPlateNumber',
            label: '铜板号',
            width: '350'
          },
          {
            keyQuery: 'avgSameBoardDiff',
            keySave: 'avgSameBoardDiff',
            label: '平均值项：同板差',
            width: '300'
          },
          {
            keyQuery: 'avgThicknessMargin',
            keySave: 'avgThicknessMargin',
            label: '平均值项：厚度余量',
            width: '280'
          },
          {
            keyQuery: 'avgLengthMargin',
            keySave: 'avgLengthMargin',
            label: '平均值项：长度余量',
            width: '280'
          },
          {
            keyQuery: 'avgWidthMargin',
            keySave: 'avgWidthMargin',
            label: '平均值项：宽度余量',
            width: '280'
          },
          {
            keyQuery: 'remark',
            keySave: 'remark',
            label: '备注',
            width: '300'
          }
        ]
      }
    }
  },
  methods: {
    handleDialogOperationVisible() {
      this.$refs.dimensionSpotCheckRef.openDialog()
    }
  }
}
</script>

<style scoped lang="less">
.container {
  width: 100%;
  height: calc(100vh - 150px);
  display: flex;
  flex-direction: column;

  .table-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    gap: 10px;

    .el-table {
      width: 45%;
    }
  }

  .chart-footer {
    margin-top: 0px;
    color: #fff;
    font-size: 12px;
    line-height: 61px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 5px 8px;
    background: #041a21;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);

    .chart-input {
      height: 100%;
      width: 100%;

      /deep/ .el-textarea__inner {
        background: transparent;
        border: none;
        color: #fff;
        font-size: 12px;
        line-height: 20px;
        height: 100%;
        padding: 0;

        &:focus {
          outline: none !important;
          box-shadow: none !important;
          border: none !important;
          border-color: transparent !important;
        }
      }

      /deep/ .el-textarea.is-focused .el-textarea__inner {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
        border-color: transparent !important;
      }
    }
  }
}
</style>
