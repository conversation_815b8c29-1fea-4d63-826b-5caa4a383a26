<template>
  <div 
    :id="containerId" 
    style="height: 100%"/>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return [
          '#0C75FF',
          '#FF7D00',
          '#00B42A',
          '#91cc75',
          '#fac858',
          '#ee6666',
          '#73c0de',
          '#3ba272',
          '#fc8452',
          '#9a60b4',
          '#ea7ccc'
        ]
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    unit: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    labelWidth: {
      type: Number,
      default: 160
    },
    total: {
      type: Number,
      default: 0
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  methods: {
    initChart() {
      let that = this
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
        this.myChart.on('mouseover', 'series', params => {
          this.showCenterText(params)
        })
        this.myChart.on('mouseout', 'series', () => {
          this.hideCenterText()
        })
        this.myChart.on('click', params => {
          this.$emit('click-item', {
            name: params.name,
            value: params.value,
            dataIndex: params.dataIndex
          })
        })
        window.addEventListener('resize', this.resizeChart)
      }
      const options = {
        title: {
          show: !!this.title,
          text: '{tit|' + this.title + '}',
          left: 'center',
          top: '5%',
          textAlign: 'center',
          padding: 0,
          textStyle: {
            fontSize: 23,
            rich: {
              tit: {
                borderColor: '#F0F0F0',
                borderWidth: 12,
                borderRadius: 4,
                fontWeight: 600,
                fontSize: 18
              }
            }
          }
        },
        tooltip: {
          show: true,
          trigger: 'item',
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: function(params) {
            return `<div>
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${
                params.color
              }; border-radius: 50%; margin-right: 5px;"></span>
              <span style="font-weight: bold; margin: 0">${(params.data &&
                params.data.name) ||
                ''}</span>
              <p style="margin: 5px 0 0">${params.name}：${
              params.value
            }${that.unit || ''}</p>
              <p style="margin: 5px 0 0">${params.name}占比：${(params.data &&
              params.data.rate) ||
              ''}</p>
            </div>`
          }
        },
        legend: {
          top: '0%',
          left: 'center',
          icon: 'rect',
          borderRadius: 0,
          itemWidth: 16,
          itemHeight: 8,
          itemGap: 10,
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        color: this.color,
        series: [
          {
            type: 'pie',
            selectedMode: 'single',
            radius: ['50%', '75%'],
            center: ['50%', '50%'],
            label: {
              show: true,
              position: 'center',
              formatter: () => '',
              rich: {
                rate: {
                  fontSize: 20,
                  color: '#fff',
                  fontWeight: 'bold'
                }
              }
            },
            labelLine: {
              show: false
            },
            data: this.chartData
          }
        ]
      }
      this.myChart.setOption(options)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    getNum(name) {
      const match = this.chartData.find(item => item.name === name)
      return match ? match : {}
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    },
    showCenterText(params) {
      const option = this.myChart.getOption()
      option.series[0].label.formatter = () =>
        `{rate|${(params.data && params.data.rate) || ''}}`
      this.myChart.setOption(option)
    },
    hideCenterText() {
      const option = this.myChart.getOption()
      option.series[0].label.formatter = () => ''
      this.myChart.setOption(option)
    }
  }
}
</script>

<style scoped>
</style>
