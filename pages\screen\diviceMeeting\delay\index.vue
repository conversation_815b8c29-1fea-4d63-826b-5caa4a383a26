<template>
  <div class="content">
    <div class="content-item top">
      <screen-border :title="'停时统计（近7天）'">
        <stoppingTime-chart
          :bar-width="30"
          :unit="'个'"
          :get-zr="true"
          :chart-data="statistics.bar1"
          :x-data="statistics.barX1"
          @child="hideHandlerChild"/>
      </screen-border>
    </div>
    <div class="content-hold"/>
    <div class="content-item top">
      <screen-border :title="'停时详情（近7天）'">
        <el-table
          :data="details"
          height="450"
        >
          <el-table-column
            type="index"
            label="序号"
            width="80"/>
          <el-table-column
            prop="产线"
            label="产线"
            width="200"/>
          <el-table-column
            prop="停机结束时间"
            label="结束时间"
            width="150"/>
          <el-table-column
            prop="停机时长"
            label="停机时长"
            width="150"/>
          <el-table-column
            prop="是否计划"
            label="是否计划"
            width="150">
            <template slot-scope="scope">
              <div v-if="scope.row.是否计划=='计划'">是</div>
              <div 
                v-else 
                class="planColor">否</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="备注"
            label="停机原因"/>
        </el-table>
      </screen-border>
    </div>
  </div>
</template>

<script>
import BarsChart from '@/pages/screen/diviceMeeting/component/bars-chart'
import StoppingTimeChart from '@/pages/screen/diviceMeeting/component/stoppingTime-chart'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border.vue'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { post } from '@/lib/Util'
import { findDelayNumAndHourToBC, findDelayDataToBCC } from '@/api/device'
import * as _ from 'lodash'
export default {
  name: 'delay',
  components: { BarsChart, ScreenBorder, StoppingTimeChart },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      statistics: {
        bar1: [
          {
            name: '总停时',
            type: 'bar',
            emphasis: {
              focus: 'series'
            },
            data: []
          },
          {
            name: '设备计划停时',
            type: 'bar',
            stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: []
          },
          {
            name: '设备非计划停时',
            type: 'bar',
            stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: []
          }
        ],
        barX1: [
          '第一炼钢厂',
          '中厚板卷厂',
          '宽厚板厂',
          '中板厂',
          '金石材料厂',
          '金润智能工厂'
        ]
      },
      details: [],
      factoryNo: '',
      StopTimeDetailsList: []
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.$nextTick(item => {
        this.getStoppingTime()
        this.StopTimeDetails()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.getStoppingTime()
    this.StopTimeDetails()
  },
  methods: {
    //停时统计
    getStoppingTime() {
      const params = {
        beginDate: this.$moment(this.cDate)
          .subtract(80, 'day')
          .format('yyyy-MM-DD'),
        endDate: this.$moment(this.cDate).format('yyyy-MM-DD')
      }
      post(findDelayNumAndHourToBC, params).then(res => {
        const order = [
          '第一炼钢厂',
          '中厚板卷厂',
          '宽厚板厂',
          '中板厂',
          '金石材料厂',
          '金润智能制造厂'
        ]
        const sortedData = res.data.sort((a, b) => {
          const aIndex = order.indexOf(a.factoryName)
          const bIndex = order.indexOf(b.factoryName)
          return aIndex - bIndex
        })
        const sumArray = sortedData.map(
          obj =>
            obj.isPlanEquipHour +
            obj.notPlanEquipHour +
            obj.isPlanProductHour +
            obj.notPlanProductHour
        )
        this.statistics.bar1[0].data = sumArray
        const isPlanEquipHour = sortedData.map(obj => obj.isPlanEquipHour)
        const notPlanEquipHour = sortedData.map(obj => obj.notPlanEquipHour)
        this.statistics.bar1[1].data = isPlanEquipHour
        this.statistics.bar1[2].data = notPlanEquipHour
      })
    },
    //停时详情
    StopTimeDetails() {
      const params = {
        beginDate: this.$moment(this.cDate)
          .subtract(7, 'day')
          .format('yyyy-MM-DD'),
        endDate: this.$moment(this.cDate).format('yyyy-MM-DD'),
        current: 1,
        size: 500
      }
      post(findDelayDataToBCC, params).then(res => {
        // console.log('停时详情', res)
        this.StopTimeDetailsList = res.data
        this.details = res.data
        // console.log(this.StopTimeDetailsList)
      })
    },
    //点击柱状图
    hideHandlerChild(msg) {
      const company = msg.month
      const filteredData = this.StopTimeDetailsList.filter(
        obj => obj['生产厂'] === company
      )
      this.details = filteredData
    }
  }
}
</script>

<style scoped lang="less">
.planColor {
  color: #ff2855;
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    font-size: 0;
    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.green {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
