<template>
  <div class="content">
    <div class="content-item">
      <screen-border :title="titleNamber!=4?(titleNamber+'号转炉炉况'):'会议纪要'">
        <template v-slot:headerRight>
          <el-radio-group
            v-model="titleNamber"
            size="mini" >
            <el-radio-button
              v-for="item in [{name:'1号转炉',value:1},{name:'2号转炉',value:2},{name:'3号转炉',value:3},{name:'会议纪要',value:4}]"
              :key="item.value"
              :label="item.value">
              {{ item.name }}
            </el-radio-button>
          </el-radio-group>
          <span
            v-command="'/screen/firstSteelmakingPlant/edit'"
            class="screen-btn"
            @click="titleNamber!=4?(unfinished.dialogVisible = true):(meetingMinutes.dialogVisible = true)">
            <el-icon class="el-icon-edit-outline"/>
            操作
          </span>
        </template>
        <div
          ref="tableHeight"
          class="scroll-wrapper">
          <template
            v-if="titleNamber!=4">
            <el-table
              v-loading="loading"
              key="zl"
              :data="unfinished.showGridData"
              :span-method="spanMethod"
              :header-cell-style="headerCell"
              :max-height="tableHeight"
              class="font-table center-table"
              border>
              <el-table-column
                :label="titleNamber!=4?(titleNamber+'号转炉炉况'):'会议纪要'">
                <el-table-column
                  width="160"
                  property="k1"/>
                <el-table-column
                  property="k2"
                  label="k2">
                  <template v-slot="{ row }">
                    <template v-if="row.pic">
                      <ul class="el-upload-list el-upload-list--picture-card">
                        <li
                          v-for="(item, index) in getPictureList(row.k2)"
                          :key="index"
                          class="el-upload-list__item is-ready">
                          <img-view
                            :key="item"
                            :id="item"
                            :img-width="'95%'"
                            :deleteable="false"
                          />
                        </li>
                      </ul>
                    </template>
                    <template v-else>
                      {{ row.k2 }}
                    </template>
                  </template>
                </el-table-column>
                <el-table-column
                  property="k3"
                  label="k3"/>
                <el-table-column
                  property="k4"/>
                <el-table-column
                  property="k5"/>
                <el-table-column
                  width="150"
                  property="k6"/>
                <el-table-column
                  property="k7"/>
                <el-table-column
                  property="k8"/>
                <el-table-column
                  property="k9"/>
              </el-table-column>
            </el-table>
          </template>
          <template
            v-else>
            <el-table
              v-loading="loading"
              key="hyjy"
              :data="meetingMinutes.showGridData"
              :span-method="handleObjectSpan"
              :max-height="tableHeight"
              :format-span-data="meetingMinutes.showGridData"
              class="font-table center-table"
              border>
              <el-table-column
                label="名称"
                width="180"
                property="A_LIST"/>
              <el-table-column
                label="内容"
                property="B_LIST"/>
              <el-table-column
                label="完成情况"
                width="280"
                property="C_LIST"/>
            </el-table>
          </template>
        </div>
      </screen-border>
    </div>
    <!--热处理详情-->
    <el-dialog
      :visible.sync="unfinished.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="转炉炉况">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData(unfinished.gridData)">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importUnfinishedData')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveUnfinished">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          {{ titleNamber }}号转炉炉况
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="unfinished.gridData"
          :header-cell-style="headerCell"
          :span-method="spanMethod"
          border>
          <el-table-column
            :label="titleNamber!=4?(titleNamber+'号转炉炉况'):'会议纪要'">
            <el-table-column
              width="160"
              property="k1"/>
            <el-table-column
              property="k2"
              label="k2">
              <template v-slot="{ row }">
                <template v-if="row.matName">
                  {{ row.k2 }}
                </template>
                <template v-else-if="row.kpiIn">
                  {{ row.k2 }}
                </template>
                <template v-else-if="row.pic">
                  <ul class="el-upload-list el-upload-list--picture-card">
                    <li
                      v-for="(item, index) in getPictureList(row.k2)"
                      :key="index"
                      class="el-upload-list__item is-ready">
                      <img-view
                        :key="item"
                        :id="item"
                        :img-width="'95%'"
                        :deleteable="canEditQuality"
                        @img-delete="handleImgDelete($event, row.index)"
                      />
                    </li>
                  </ul>
                  <el-upload
                    v-if="canEditQuality"
                    ref="upload"
                    :auto-upload="false"
                    :show-file-list="false"
                    :http-request="httpRequest"
                    :on-change="handleChange"
                    multiple
                    list-type="picture-card"
                    action="#"
                    style="display: inline"
                    @click.native="editIndex = row.index">
                    <i class="el-icon-plus"/>
                  </el-upload>
                </template>
                <template v-else>
                  <el-input v-model="row.k2"/>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              property="k3"
              label="k3">
              <template v-slot="{ row }">
                <template v-if="row.matName">
                  {{ row.k3 }}
                </template>
                <template v-else-if="row.kpiIn">
                  {{ row.k3 }}
                </template>
                <template v-else>
                  <el-input v-model="row.k3"/>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              property="k4">
              <template v-slot="{ row }">
                <template v-if="row.matName">
                  {{ row.k4 }}
                </template>
                <template v-else-if="row.kpiIn">
                  {{ row.k4 }}
                </template>
                <template v-else>
                  <el-input v-model="row.k4"/>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              property="k5">
              <template v-slot="{ row }">
                <template v-if="row.matName">
                  {{ row.k5 }}
                </template>
                <template v-else-if="row.kpiIn">
                  {{ row.k5 }}
                </template>
                <template v-else>
                  <el-input v-model="row.k5"/>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              width="150"
              property="k6">
              <template v-slot="{ row }">
                <template v-if="row.matName">
                  {{ row.k6 }}
                </template>
                <template v-else-if="row.kpiIn">
                  {{ row.k6 }}
                </template>
                <template v-else>
                  <el-input v-model="row.k6"/>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              property="k7">
              <template v-slot="{ row }">
                <template v-if="row.matIn">
                  铁水装入量（吨）
                </template>
                <template v-else-if="row.matName">
                  {{ row.k7 }}
                </template>
                <template v-else-if="row.kpiIn">
                  {{ row.k7 }}
                </template>
                <template v-else>
                  <el-input v-model="row.k7"/>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              property="k8">
              <template v-slot="{ row }">
                <template v-if="row.matName">
                  {{ row.k8 }}
                </template>
                <template v-else-if="row.kpiIn">
                  {{ row.k8 }}
                </template>
                <template v-else>
                  <el-input v-model="row.k8"/>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              property="k9">
              <template v-slot="{ row }">
                <template v-if="row.matName">
                  {{ row.k9 }}
                </template>
                <template v-else-if="row.kpiIn">
                  {{ row.k9 }}
                </template>
                <template v-else>
                  <el-input v-model="row.k9"/>
                </template>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="meetingMinutes.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="会议纪要">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData(meetingMinutes.gridData)">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importMeetingMinutes')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreviewMeetingMinutes"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveMeetingMinutes">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          会议纪要
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="meetingMinutes.gridData"
          border>
          <el-table-column
            label="序号"
            width="180"
            property="SORT_NUM">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            label="名称"
            width="180"
            property="A_LIST">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            label="内容"
            property="B_LIST">
            <template v-slot="{ row }">
              <el-input v-model="row.B_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            label="完成情况"
            property="C_LIST">
            <template v-slot="{ row }">
              <el-input v-model="row.C_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template
              v-slot="{ row, $index }"
              v-if="canEditQuality">
              <span
                class="screen-btn"
                @click="delGridData($index, 'meetingMinutes')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('meetingMinutes')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import lodash from 'lodash'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import { firstMorningMeeting } from '@/api/screen'
import ImgView from '@/components/ImgView.vue'
import { deleteFileByIds, uploadFile } from '@/api/system'

export default {
  name: 'conditionConverter',
  components: { ImgView, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      titleNamber: 1,
      editIndex: 0,
      tableHeight: null,
      unfinished: {
        initializedData: [
          {
            k2: 'A_LIST',
            k9: 'H_LIST'
          },
          {},
          {
            k2: 'A_LIST',
            k3: 'B_LIST',
            k4: 'C_LIST',
            k5: 'D_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST',
            k9: 'H_LIST'
          },
          {
            k2: 'A_LIST',
            k3: 'B_LIST',
            k4: 'C_LIST',
            k5: 'D_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST',
            k9: 'H_LIST'
          },
          {},
          {
            k2: 'A_LIST',
            k3: 'B_LIST',
            k4: 'C_LIST',
            k5: 'D_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST',
            k9: 'H_LIST'
          },
          {
            k2: 'A_LIST'
          },
          {
            k2: 'A_LIST'
          },
          {
            k2: 'A_LIST'
          },
          {
            k2: 'A_LIST'
          }
        ],
        initializedData2: [
          {
            k1: '炉况趋势评估',
            matIn: true,
            k7: '铁水装入量（吨）'
          },
          {
            k1: '炉况厚度（mm） ',
            matIn: false,
            matName: true,
            k2: '炉况位置',
            k3: '前大面',
            k4: '后大面',
            k5: '炉底',
            k6: '东侧耳轴',
            k7: '西侧耳轴',
            k8: '前45°',
            k9: '后45°'
          },
          {
            matIn: false
          },
          {
            matIn: false
          },
          {
            k1: '生产指标统计',
            matIn: false,
            kpiIn: true,
            k2: '生产炉数',
            k3: '低碳或低磷钢炉数',
            k4: '一倒命中率',
            k5: '普钢氧含量',
            k6: '低碳或低磷钢氧含量',
            k7: '垫补炉次数',
            k8: '喷补次数',
            k9: '补生铁次数'
          },
          {
            k1: '生产指标统计',
            matIn: false
          },
          {
            k1: '异常情况说明',
            matIn: false
          },
          {
            k1: '备注',
            matIn: false
          },
          {
            k1: '测量情况',
            matIn: false,
            pic: true,
            index: 8
          },
          {
            k1: '测量情况',
            matIn: false,
            pic: true,
            index: 9
          }
        ],
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      meetingMinutes: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.unfinished.initializedData2[8].k1 =
        this.$moment(this.cDate).format('DD号') + '测量情况'
      this.unfinished.initializedData2[9].k1 =
        this.$moment(this.cDate)
          .subtract(1, 'days')
          .format('DD号') + '测量情况'
      // 初始化数据
      this.getUnfinished({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: this.titleNamber + '号转炉'
      })
      this.getMeetingMinutes({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '炉况会议纪要'
      })
    },
    titleNamber: function(val) {
      if (val == 4) {
        this.getMeetingMinutes({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '炉况会议纪要'
        })
      } else {
        this.getUnfinished({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: val + '号转炉'
        })
      }
    }
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['A_LIST', 'B_LIST', 'C_LIST']
  },
  mounted() {
    this.tableHeight = this.$refs.tableHeight.offsetHeight
  },
  methods: {
    // 导入数据
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          B: 'B',
          C: 'C',
          D: 'D',
          E: 'E',
          F: 'F',
          G: 'G',
          H: 'H',
          I: 'I',
          J: 'J'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        console.log(sheet)
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        this.unfinished.gridData = lodash.map(
          this.unfinished.gridData,
          (item, index) => {
            if (
              index == 2 ||
              index == 3 ||
              index == 5 ||
              index == 6 ||
              index == 7 ||
              index == 0
            ) {
              return {
                ...item,
                k2: sheet[index].C,
                k3: sheet[index].D,
                k4: sheet[index].E,
                k5: sheet[index].F,
                k6: sheet[index].G,
                k7: sheet[index].H,
                k8: sheet[index].I,
                k9: sheet[index].J
              }
            } else {
              return item
            }
          }
        )
        this.$message.success('解析成功！')
      })
    },
    // 删除数据
    clearGridData(value) {
      this.unfinished.gridData = lodash.map(value, (item, index) => {
        if (index == 0) {
          let obj = {}
          Object.keys(item)
            .filter(items => items != 'k2' && items != 'k9')
            .forEach(items => {
              obj[items] = item[items]
            })
          return obj
        } else if (
          index == 2 ||
          index == 3 ||
          index == 5 ||
          index == 6 ||
          index == 7
        ) {
          let obj = {}
          Object.keys(item)
            .filter(
              items =>
                items != 'k2' &&
                items != 'k3' &&
                items != 'k4' &&
                items != 'k5' &&
                items != 'k6' &&
                items != 'k7' &&
                items != 'k8' &&
                items != 'k9'
            )
            .forEach(items => {
              obj[items] = item[items]
            })
          return obj
        } else {
          return item
        }
      })
    },
    // 获取数据
    getUnfinished(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let saveData = this.unfinished.initializedData
        let datas = lodash.map(
          this.unfinished.initializedData2,
          (item, index) => {
            let resData = lodash.filter(
              res.data,
              item => item.SORT_NUM - 1 === index
            )
            if (resData.length) {
              let objData = {}
              lodash.forEach(Object.keys(saveData[index]), item => {
                objData[item] = resData[0][saveData[index][item]]
              })
              return {
                ...objData,
                ...item
              }
            } else {
              return item
            }
          }
        )
        this.unfinished.gridData = _.cloneDeep(datas)
        this.unfinished.showGridData = _.cloneDeep(datas)
      })
    },
    saveUnfinished() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: this.titleNamber + '号转炉',
        data: lodash.map(this.unfinished.gridData, (item, index) => {
          let objData = {}
          objData.PROD_DATE = this.$moment(this.cDate).format('yyyyMMDD')
          objData.FLAG = this.titleNamber + '号转炉'
          objData.SORT_NUM = index + 1
          lodash.forEach(
            Object.keys(this.unfinished.initializedData[index]),
            items => {
              objData[this.unfinished.initializedData[index][items]] =
                item[items]
            }
          )
          return objData
        })
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        console.log(res)
        this.unfinished.dialogVisible = false
        this.getUnfinished({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: this.titleNamber + '号转炉'
        })
        this.loading = false
      })
    },
    getPictureList(data) {
      return data ? JSON.parse(data) : []
    },
    importUnfinishedData(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        FLAG: this.titleNamber + '号转炉',
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD')
      }).then(res => {
        this.loading = false
        let saveData = this.unfinished.initializedData
        let datas = lodash.map(
          this.unfinished.initializedData2,
          (item, index) => {
            let resData = lodash.filter(
              res.data,
              item => item.SORT_NUM - 1 === index
            )
            if (resData.length) {
              let objData = {}
              lodash.forEach(Object.keys(saveData[index]), item => {
                objData[item] = resData[0][saveData[index][item]]
              })
              return {
                ...objData,
                ...item
              }
            } else {
              return item
            }
          }
        )
        datas[8].k2 = `[]`
        datas[9].k2 = `[]`
        this.unfinished.gridData = _.cloneDeep(datas)
        this.$message.success('导入成功！')
      })
    },
    headerCell({ row }) {
      if (row.length != 1) {
        return 'display: none'
      }
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex === 1) {
          return {
            rowspan: 3,
            colspan: 1
          }
        }
        if (rowIndex === 4) {
          return {
            rowspan: 2,
            colspan: 1
          }
        }
        if (rowIndex === 2 || rowIndex === 3 || rowIndex === 5) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      if (columnIndex === 1) {
        // 合并第6,7,8,9行数据
        if (
          rowIndex === 7 ||
          rowIndex === 6 ||
          rowIndex === 8 ||
          rowIndex === 9
        ) {
          return {
            rowspan: 1,
            colspan: 8
          }
        }
        // 合并第一行数据
        if (rowIndex === 0) {
          return {
            rowspan: 1,
            colspan: 5
          }
        }
      }
      // 合并第一行数据
      if (
        columnIndex === 2 ||
        columnIndex === 3 ||
        columnIndex === 4 ||
        columnIndex === 5
      ) {
        if (rowIndex === 0) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      // 合并第6,7,8,9行数据
      if (
        columnIndex === 2 ||
        columnIndex === 3 ||
        columnIndex === 4 ||
        columnIndex === 5 ||
        columnIndex === 6 ||
        columnIndex === 7 ||
        columnIndex === 8 ||
        columnIndex === 9
      ) {
        if (
          rowIndex === 6 ||
          rowIndex === 7 ||
          rowIndex === 8 ||
          rowIndex === 9
        ) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      if (columnIndex === 6) {
        // 合并第一行数据
        if (rowIndex === 0) {
          return {
            rowspan: 1,
            colspan: 2
          }
        }
      }
      if (columnIndex === 7) {
        // 合并第一行数据
        if (rowIndex === 0) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    async handleChange(file, fileList, index) {
      console.log(file, fileList)
      const formData = new FormData()
      formData.append('files', file.raw)
      console.log(this.unfinished.gridData[this.editIndex], this.editIndex)
      post(uploadFile, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        if (res.success) {
          this.$message.success('图片上传成功！')
          const obj = this.unfinished.gridData[this.editIndex]
          console.log(obj)
          const pictures = obj.k2 != undefined ? JSON.parse(obj.k2) : []
          pictures.push(res.data[0].id)
          this.unfinished.gridData.splice(
            this.editIndex,
            1,
            Object.assign({}, obj, {
              k2: JSON.stringify(pictures)
            })
          )
          this.unfinished.gridData[this.editIndex].k2 = JSON.stringify(pictures)
          console.log(this.unfinished.gridData)
        } else {
          this.$message.warning('图片上传失败！')
          this.loading = false
        }
      })
    },
    httpRequest(params) {},
    async handleImgDelete(file, index) {
      const obj = this.unfinished.gridData[index]
      const del = await post(deleteFileByIds, { ids: [file.id] })
      if (del.success) {
        const obj = this.unfinished.gridData[index]
        this.unfinished.gridData.splice(
          index,
          1,
          Object.assign({}, obj, {
            k2: JSON.stringify(
              (obj.k2 != undefined ? JSON.parse(obj.k2) : []).filter(
                item => item !== file.id
              )
            )
          })
        )
      }
    },
    //会议纪要
    getMeetingMinutes(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        this.meetingMinutes.showGridData = lodash.cloneDeep(res.data)
        this.meetingMinutes.gridData = lodash.cloneDeep(res.data)
        this.formatSpanData(lodash.cloneDeep(res.data))
      })
    },
    saveMeetingMinutes() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '炉况会议纪要',
        data: lodash.map(
          lodash.sortBy(this.meetingMinutes.gridData, item => item.SORT_NUM),
          (item, index) => {
            item.PROD_DATE = this.$moment(this.cDate).format('yyyyMMDD')
            item.FLAG = '炉况会议纪要'
            item.SORT_NUM = index + 1
            return item
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.meetingMinutes.dialogVisible = false
        this.getMeetingMinutes({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '炉况会议纪要'
        })
        this.loading = false
      })
    },
    importMeetingMinutes(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        FLAG: '炉况会议纪要',
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD')
      }).then(res => {
        this.meetingMinutes.gridData = lodash.cloneDeep(res.data)
        this.$message.success('导入成功')
      })
    },
    handlePreviewMeetingMinutes(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          B_LIST: 'D',
          C_LIST: 'E'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.meetingMinutes.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.screen-input {
  /deep/ .el-textarea__inner,
  /deep/ .el-input__inner {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
    padding-right: 5px;
  }
  /deep/ .el-input__prefix {
    color: #fff;
  }
  /deep/ .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    background: rgba(31, 198, 255, 0.3);
    border-color: rgba(31, 198, 255, 0.6);
    //border: 1px solid #1fc6ff;
  }
  /deep/ .el-radio-button--mini .el-radio-button__inner {
    background: #d8edff;
  }
}
</style>
