<!--考核通报-->
<template>
  <div class="content">
    <div class="content-item">
      <screen-border-multi :title="'考核通报'">
        <template v-slot:headerRight>
          <span
            v-command="'/screen/plateRollsSafe/edit'"
            class="screen-btn"
            @click="AddData">
            <el-icon class="el-icon-upload2"/>
            上传
          </span>
        </template>
        <el-table
          v-loading="loading"
          :data="AN_data"
          border>
          <el-table-column
            show-overflow-tooltip
            width="70"
            label="序号">
            <template slot-scope="scope">
              <div>{{ scope.$index+1 }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="文件名">
            <template slot-scope="scope">
              <div
                style="text-decoration: underline;cursor: pointer;"
                @click="watchFile(scope.row)">{{ scope.row.fileName }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="上传日期"
            width="130">
            <template slot-scope="scope">
              <div>{{ scope.row.setDate }}</div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property=""
            width="150"
            label="操作">
            <template v-slot="scope">
              <span @click="DownloadFile(scope.row)">下载</span>
              <span @click="Del(scope.row)">删除</span>
            </template>
          </el-table-column>
        </el-table>
      </screen-border-multi>
    </div>
    <!--考核通报上传弹框-->
    <el-dialog
      :visible.sync="AN_view"
      :close-on-click-modal="false"
      width="60%"
      class="screen-dialog"
      @close="Close_AN_view">
      <template v-slot:title>
        <div class="custom-dialog-title">
          考核通报
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">文件</div>
          <el-upload
            :before-remove="beforeRemove"
            :before-upload="beforeUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
            :multiple="false"
            action=""
            class="upload-demo">
            <el-button
              size="small"
              type="primary">点击上传</el-button>
            <div
              slot="tip"
              class="el-upload__tip">请选择文件</div>
          </el-upload>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          v-loading="AN_loading"
          class="screen-btn"
          @click="SubmitData">
          确定
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { get, post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { minio_upload, AN_INQUIRE, AN_NEWS, AN_DEL } from '@/api/screen'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi.vue'
export default {
  name: 'ProjectPage',
  components: {
    ScreenBorderMulti
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      loading: false,
      //总数居
      AN_data: [],

      //上传
      AN_loading: false,
      AN_view: false,
      FormData: [],

      fileData: null,
      fileList: []
    }
  },
  watch: {
    selectDate: function() {
      this.getAssessmentNotification()
    }
  },
  mounted() {
    this.getAssessmentNotification()
  },
  methods: {
    //查询
    async getAssessmentNotification() {
      this.loading = true
      let res = await post(AN_INQUIRE, {
        setDate: this.selectDate
      })
      // console.log('查询数据', res)
      if (res) {
        this.loading = false
        this.AN_data = res.data
      }
    },

    //上传
    AddData() {
      this.AN_view = true
      this.FormData = {
        fileName: '',
        fileUrl: ''
      }
    },

    //提交
    async SubmitData() {
      this.AN_loading = true
      if (this.fileData) {
        let formData = new FormData()
        formData.append('file', this.fileData)

        let res = await post(minio_upload, formData)
        if (res) {
          let index = res.indexOf('?')
          if (index !== -1) {
            this.FormData.fileUrl = res.substring(0, index)
          } else {
            this.FormData.fileUrl = res
          }
          //清空文件
          this.fileList = []

          //上传文件数据给后台
          if (this.FormData.fileUrl != '') {
            let res1 = await post(AN_NEWS, [this.FormData])
            // console.log('数据上传', res1)
            if (res1.status == 1) {
              this.AN_loading = false
              this.Close_AN_view()
              this.getAssessmentNotification()
              this.$message.success('上传成功!')
            }
          }
        }
      } else {
        this.$message.warning('请选择文件！')
      }
    },

    //关闭上传弹框
    Close_AN_view() {
      this.AN_view = false
      this.FormData = {}
    },

    //上传文件之前
    beforeUpload(file) {
      this.fileData = file
      this.FormData.fileName = file.name
    },

    //删除文件之前
    beforeRemove(file, fileList) {
      const isDel = this.$confirm(`确定移除 ${file.name}?`)
      if (isDel) {
        this.fileData = null
      }
      return isDel
    },

    //超出文件上传数量
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },

    //查看文件
    async watchFile(row) {
      let format = row.fileUrl.substring(row.fileUrl.lastIndexOf('.') + 1)

      let type = ''
      if (format === 'pdf') {
        type = 'application/pdf'
      } else if (format === 'png') {
        type = 'image/png'
      } else if (format === 'jpeg') {
        type = 'image/jpeg'
      }

      //预览
      if (type != '') {
        let data = await get(row.fileUrl)
        if (!data) {
          return
        }
        const url = window.URL.createObjectURL(new Blob([data], { type: type }))
        window.open(url)
      } else {
        this.$message.warning('此文件不可在线浏览,请下载!')
      }
    },

    //下载文件
    DownloadFile(row) {
      window.open(row.fileUrl)
    },

    //删除
    async Del(row) {
      let res = await post(AN_DEL, [{ id: row.id }])
      if (res.status == 1) {
        this.getAssessmentNotification()
        this.$message.success('删除成功!')
      }
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
</style>
