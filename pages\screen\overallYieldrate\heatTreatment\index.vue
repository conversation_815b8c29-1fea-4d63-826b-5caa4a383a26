<template>
  <div class="content container">
    <div class="content-item">
      <div style="width:100%;text-align: right;margin-bottom: 10px">
        <span
          class="screen-btn"
          @click="edit()">
          <el-icon class="el-icon-edit-outline" />
          订单配置
        </span>
        <el-button
          :loading="refreshLoading"
          class="screen-btn"
          icon="el-icon-refresh"
          @click="debouncedRefresh"
        >
          刷新
        </el-button>
      </div>
      <screen-border header-class="hide-header">
        <el-row
          :gutter="32"
          style="height: 40vh;margin-bottom: 45vh;"
          class="full-height">
          <el-col
            :span="12"
            class="full-height">
            <div class="content">
              <div class="content-item">
                <screen-border :title="ecahrtAll[0].orderName">
                  <div class="chart-wrapper">
                    <div
                      v-loading="loading"
                      style="height: 100%">
                      <bars-chart-3
                        :unit="'%'"
                        :show-legend="true"
                        :bar-width="20"
                        :label-rotate="rotate"
                        :chart-data="ecahrtAll[0].yrate"
                        :x-data="ecahrtAll[0].xgrade"
                        :echart-data="ecahrtAll[0]"
                        :scrollable="true"
                        :scroll-start="0"
                        :scroll-end="90"
                        style="height: 100%"
                      />
                    </div>
                  </div>
                </screen-border>
              </div>
              <div class="content-hold" />
              <div class="content-item">
                <screen-border :title="ecahrtAll[1].orderName">
                  <div class="chart-wrapper">
                    <div
                      v-loading="loading"
                      style="height: 100%">
                      <bars-chart-3
                        :unit="'%'"
                        :show-legend="true"
                        :bar-width="20"
                        :label-rotate="rotate"
                        :chart-data="ecahrtAll[1].yrate"
                        :x-data="ecahrtAll[1].xgrade"
                        :echart-data="ecahrtAll[1]"
                        :scrollable="true"
                        :scroll-start="0"
                        :scroll-end="90"
                      />
                    </div>
                  </div>
                </screen-border>
              </div>
            </div>
          </el-col>
          <el-col
            :span="12"
            class="full-height">
            <div class="content">
              <div class="content-item">
                <screen-border :title="ecahrtAll[2].orderName">
                  <div class="chart-wrapper">
                    <div
                      v-loading="loading"
                      style="height: 100%">
                      <bars-chart-3
                        :unit="'%'"
                        :show-legend="true"
                        :bar-width="20"
                        :label-rotate="rotate"
                        :chart-data="ecahrtAll[2].yrate"
                        :x-data="ecahrtAll[2].xgrade"
                        :echart-data="ecahrtAll[2]"
                        :scrollable="true"
                        :scroll-start="0"
                        :scroll-end="90"
                      />
                    </div>
                  </div>
                </screen-border>
              </div>
              <div class="content-hold" />
              <div class="content-item">
                <screen-border :title="ecahrtAll[3].orderName">
                  <div class="chart-wrapper">
                    <div
                      v-loading="loading"
                      style="height: 100%">
                      <bars-chart-3
                        :unit="'%'"
                        :show-legend="true"
                        :bar-width="20"
                        :label-rotate="rotate"
                        :chart-data="ecahrtAll[3].yrate"
                        :x-data="ecahrtAll[3].xgrade"
                        :echart-data="ecahrtAll[3]"
                        :scrollable="true"
                        :scroll-start="0"
                        :scroll-end="90"
                      />
                    </div>
                  </div>
                </screen-border>
              </div>
            </div>
          </el-col>
          <!-- <el-col
            :span="12"
            class="full-height">
            <TreeMapChart
              :show-legend="false"
              :bar-width="50"
              :chart-data="heatMonth2.bar1"
            :x-data="['板卷厂', '1#热处理炉', '2#热处理炉']"/></el-col> -->
        </el-row>
        <el-row
          :gutter="32"
          style="height: 40vh;"
          class="full-height">
          <el-col
            :span="12"
            class="full-height">
            <div class="content">
              <div class="content-item">
                <screen-border :title="ecahrtAll[4].orderName">
                  <div class="chart-wrapper">
                    <div
                      v-loading="loading"
                      style="height: 100%">
                      <bars-chart-3
                        :unit="'%'"
                        :show-legend="true"
                        :bar-width="20"
                        :label-rotate="rotate"
                        :chart-data="ecahrtAll[4].yrate"
                        :x-data="ecahrtAll[4].xgrade"
                        :echart-data="ecahrtAll[4]"
                        :scrollable="true"
                        :scroll-start="0"
                        :scroll-end="90"
                      />
                    </div>
                  </div>
                </screen-border>
              </div>
              <div class="content-hold" />
              <div class="content-item">
                <screen-border :title="ecahrtAll[5].orderName">
                  <div class="chart-wrapper">
                    <div
                      v-loading="loading"
                      style="height: 100%">
                      <bars-chart-3
                        :unit="'%'"
                        :show-legend="true"
                        :bar-width="20"
                        :label-rotate="rotate"
                        :chart-data="ecahrtAll[5].yrate"
                        :x-data="ecahrtAll[5].xgrade"
                        :echart-data="ecahrtAll[5]"
                        :scrollable="true"
                        :scroll-start="0"
                        :scroll-end="90"
                      />
                    </div>
                  </div>
                </screen-border>
              </div>
            </div>
          </el-col>
          <el-col
            :span="12"
            class="full-height">
            <div class="content">
              <div class="content-item">
                <screen-border :title="ecahrtAll[6].orderName">
                  <div class="chart-wrapper">
                    <div
                      v-loading="loading"
                      style="height: 100%">
                      <bars-chart-3
                        :unit="'%'"
                        :show-legend="true"
                        :bar-width="20"
                        :label-rotate="rotate"
                        :chart-data="ecahrtAll[6].yrate"
                        :x-data="ecahrtAll[6].xgrade"
                        :echart-data="ecahrtAll[6]"
                        :scrollable="true"
                        :scroll-start="0"
                        :scroll-end="50"
                      />
                    </div>
                  </div>
                </screen-border>
              </div>
              <div class="content-hold" />
              <div class="content-item">
                <screen-border :title="ecahrtAll[7].orderName">
                  <div class="chart-wrapper">
                    <div
                      v-loading="loading"
                      style="height: 100%">
                      <bars-chart-3
                        :unit="'%'"
                        :show-legend="true"
                        :bar-width="20"
                        :label-rotate="rotate"
                        :chart-data="ecahrtAll[7].yrate"
                        :x-data="ecahrtAll[7].xgrade"
                        :echart-data="ecahrtAll[7]"
                        :scrollable="true"
                        :scroll-start="0"
                        :scroll-end="90"
                      />
                    </div>
                  </div>
                </screen-border>
              </div>
            </div>
          </el-col>
          <!-- <el-col
            :span="12"
            class="full-height">
            <TreeMapChart
              :show-legend="false"
              :bar-width="50"
              :chart-data="heatMonth2.bar1"
            :x-data="['板卷厂', '1#热处理炉', '2#热处理炉']"/></el-col> -->
        </el-row>
      </screen-border>
      <!--弹框-->
      <el-dialog
        :visible.sync="dialogFormVisible"
        class="screen-dialog">
        <template v-slot:title>
          <div class="custom-dialog-title"> {{ title }}</div>
        </template>
        <el-form :model="form">
          <el-form-item
            :label-width="formLabelWidth"
            label="订单1名称">
            <el-input
              v-model="form.orderName1"
              autocomplete="off" />
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单1订单号">
            <el-select
              v-model="form.orderNo1"
              :loading="loading"
              :remote-method="remoteSearch"
              :loading-text="loadingText"
              multiple
              filterable
              remote
              reserve-keyword
              style="width: 100%;"
              placeholder="请选择单号">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单2名称">
            <el-input
              v-model="form.orderName2"
              autocomplete="off" />
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单2订单号">
            <el-select
              v-model="form.orderNo2"
              :loading="loading"
              :remote-method="remoteSearch"
              :loading-text="loadingText"
              multiple
              filterable
              remote
              reserve-keyword
              style="width: 100%;"
              placeholder="请选择单号">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单3名称">
            <el-input
              v-model="form.orderName3"
              autocomplete="off" />
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单3订单号">
            <el-select
              v-model="form.orderNo3"
              :loading="loading"
              :remote-method="remoteSearch"
              :loading-text="loadingText"
              multiple
              filterable
              remote
              reserve-keyword
              style="width: 100%;"
              placeholder="请选择单号">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单4名称">
            <el-input
              v-model="form.orderName4"
              autocomplete="off" />
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单4订单号">
            <el-select
              v-model="form.orderNo4"
              :loading="loading"
              :remote-method="remoteSearch"
              :loading-text="loadingText"
              multiple
              filterable
              remote
              reserve-keyword
              style="width: 100%;"
              placeholder="请选择单号">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单5名称">
            <el-input
              v-model="form.orderName5"
              autocomplete="off" />
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单5订单号">
            <el-select
              v-model="form.orderNo5"
              :loading="loading"
              :remote-method="remoteSearch"
              :loading-text="loadingText"
              multiple
              filterable
              remote
              reserve-keyword
              style="width: 100%;"
              placeholder="请选择单号">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单6名称">
            <el-input
              v-model="form.orderName6"
              autocomplete="off" />
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单6订单号">
            <el-select
              v-model="form.orderNo6"
              :loading="loading"
              :remote-method="remoteSearch"
              :loading-text="loadingText"
              multiple
              filterable
              remote
              reserve-keyword
              style="width: 100%;"
              placeholder="请选择单号">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单7名称">
            <el-input
              v-model="form.orderName7"
              autocomplete="off" />
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单7订单号">
            <el-select
              v-model="form.orderNo7"
              :loading="loading"
              :remote-method="remoteSearch"
              :loading-text="loadingText"
              multiple
              filterable
              remote
              reserve-keyword
              style="width: 100%;"
              placeholder="请选择单号">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单8名称">
            <el-input
              v-model="form.orderName8"
              autocomplete="off" />
          </el-form-item>
          <el-form-item
            :label-width="formLabelWidth"
            label="订单8订单号">
            <el-select
              v-model="form.orderNo8"
              :loading="loading"
              :remote-method="remoteSearch"
              :loading-text="loadingText"
              multiple
              filterable
              remote
              reserve-keyword
              style="width: 100%;"
              placeholder="请选择单号">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
        <div
          slot="footer"
          class="text-center">
          <!-- <el-button @click="dialogFormVisible = false">取 消</el-button> -->
          <span
            v-show="title!='原因说明'"
            class="screen-btn"
            @click="saveOrder()">
            <el-icon class="el-icon-check" />
            确定
          </span>
          <span
            v-show="title!='原因说明'"
            class="screen-btn"
            @click="dialogFormVisible = false">
            <el-icon class="el-icon-close" />
            取消
          </span>
          <!-- <el-button
            type="primary"
            @click="dialogFormVisible = false">确 定</el-button> -->
        </div>
      </el-dialog>

    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/overallYieldrate/component/screen-border'
import SingleBarsChart from '@/pages/screen/overallYieldrate/component/single-bars-chart'
import TreeMapChart from '@/pages/screen/overallYieldrate/component/treeMap-chart'
import SingleBarsChart2 from '@/pages/screen/overallYieldrate/component/single-bars-chart2'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import BarsChart3 from '@/pages/screen/overallYieldrate/component/bars-chart-3'
import StockLineChart from '@/pages/screen/morningMeeting/component/stock-line-chart'
import CustomTable from '@/pages/screen/overallYieldrate/component/custom-table'
import { post } from '@/lib/Util'
import * as _ from 'lodash'
import {
  FullProcessYieldSave,
  FullProcessYieldFindAll,
  FindOrderYieldRateSteel,
  findHeatTreatmentYieldByDate,
  FindSteelOrder,
  steelOutputTask,
  ScheduledTasks2
} from '@/api/screen'
import { math } from '@/lib/Math'
import moment from 'moment'
import lodash from 'lodash'

export default {
  name: 'Output',
  components: {
    CustomTable,
    BarsChart3,
    StockLineChart,
    SingleBarsChart,
    SingleBarsChart2,
    ScreenBorder,
    TreeMapChart
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      rotate: 5,
      steelOutputTask: steelOutputTask,
      title: '收得率展示方式',
      options: [],
      order_All: [],
      loading: true, // 加载状态
      loadingText: '加载中...', // 加载时的提示文本
      dialogFormVisible: false,
      form: {
        orderName1: '',
        orderNo1: [],
        orderName2: '',
        orderNo2: [],
        orderName3: '',
        orderNo3: [],
        orderName4: '',
        orderNo4: [],

        orderName5: '',
        orderNo5: [],
        orderName6: '',
        orderNo6: [],
        orderName7: '',
        orderNo7: [],
        orderName8: '',
        orderNo8: []
      },
      formLabelWidth: '120px',
      ecahrtAll: [
        {
          orderName: '',
          orderNo: [],
          xgrade: [],
          yrate: [
            { name: '全流程原钢种正品收得率(除现货)', data: [] },
            { name: '钢原钢种收得率', data: [] },
            { name: '材原钢种收得率', data: [] }
          ]
        },
        {
          orderName: '',
          orderNo: [],
          xgrade: [],
          yrate: [
            { name: '全流程原钢种正品收得率（除现货)', data: [] },
            { name: '钢原钢种收得率', data: [] },
            { name: '材原钢种收得率', data: [] }
          ]
        },
        {
          orderName: '',
          orderNo: [],
          xgrade: [],
          yrate: [
            { name: '全流程原钢种正品收得率（除现货)', data: [] },
            { name: '钢原钢种收得率', data: [] },
            { name: '材原钢种收得率', data: [] }
          ]
        },
        {
          orderName: '',
          orderNo: [],
          xgrade: [],
          yrate: [
            { name: '全流程原钢种正品收得率（除现货)', data: [] },
            { name: '钢原钢种收得率', data: [] },
            { name: '材原钢种收得率', data: [] }
          ]
        },
        {
          orderName: '',
          orderNo: [],
          xgrade: [],
          yrate: [
            { name: '全流程原钢种正品收得率（除现货)', data: [] },
            { name: '钢原钢种收得率', data: [] },
            { name: '材原钢种收得率', data: [] }
          ]
        },
        {
          orderName: '',
          xgrade: [],
          yrate: [
            { name: '全流程原钢种正品收得率（除现货)', data: [] },
            { name: '钢原钢种收得率', data: [] },
            { name: '材原钢种收得率', data: [] }
          ]
        },
        {
          orderName: '',
          orderNo: [],
          xgrade: [],
          yrate: [
            { name: '全流程原钢种正品收得率（除现货)', data: [] },
            { name: '钢原钢种收得率', data: [] },
            { name: '材原钢种收得率', data: [] }
          ]
        },
        {
          orderName: '',
          orderNo: [],
          xgrade: [],
          yrate: [
            { name: '全流程原钢种正品收得率（除现货)', data: [] },
            { name: '钢原钢种收得率', data: [] },
            { name: '材原钢种收得率', data: [] }
          ]
        }
      ],
      refreshLoading: false
    }
  },
  computed: {
    searchTime: function() {
      return moment(this.cDate).subtract(2, 'day')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.$nextTick(() => {
        this.loadData()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
    this.debouncedRefresh = lodash.debounce(this.reflesh, 300)
  },
  mounted() {
    this.loadData()
    this.FullProcessYieldFindAll()
    //  this.calculateHeight()
    //  window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    reflesh() {
      this.refreshLoading = true
      this.loading = true
      post(ScheduledTasks2, {})
        .then(res => {
          console.log('obj', res)
          this.loading = false
          this.refreshLoading = false
        })
        .catch(err => {
          this.loading = false
          this.refreshLoading = false
        })
    },
    async loadData() {
      this.$nextTick(() => {
        //   this.getSteal()
        //   this.getZZP()
        this.getOrderData()
        this.FindOrderYieldRateSteel()
        //   this.getfactoryOrder()
      })
    },
    edit() {
      this.dialogFormVisible = true
      this.FullProcessYieldFindAll()
    },
    FullProcessYieldFindAll() {
      post(FullProcessYieldFindAll, {}).then(res => {
        this.form.orderName1 = res.data[0].order_name
        this.form.orderName2 = res.data[1].order_name
        this.form.orderName3 = res.data[2].order_name
        this.form.orderName4 = res.data[3].order_name
        this.form.orderName5 = res.data[4].order_name
        this.form.orderName6 = res.data[5].order_name
        this.form.orderName7 = res.data[6].order_name
        this.form.orderName8 = res.data[7].order_name
        this.form.orderNo1 = JSON.parse(res.data[0].order_no)
        this.form.orderNo2 = JSON.parse(res.data[1].order_no)
        this.form.orderNo3 = JSON.parse(res.data[2].order_no)
        this.form.orderNo4 = JSON.parse(res.data[3].order_no)
        this.form.orderNo5 = JSON.parse(res.data[4].order_no)
        this.form.orderNo6 = JSON.parse(res.data[5].order_no)
        this.form.orderNo7 = JSON.parse(res.data[6].order_no)
        this.form.orderNo8 = JSON.parse(res.data[7].order_no)
      })
    },
    getOrderData() {
      post(FindSteelOrder, {}).then(res => {
        let obj = []
        for (const item of res.data) {
          let data = item
          for (let key in item) {
            obj.push(key)
          }
        }
        const transformedArray = obj.map(item => ({
          value: item,
          label: item
        }))
        console.log('obj', transformedArray)
        this.order_All = transformedArray
      })
    },
    remoteSearch(query) {
      if (query !== '') {
        this.loading = true
        // 这里可以调用后端API进行搜索，但为了示例，我们使用前端过滤
        const results = this.order_All.filter(option =>
          option.label.toLowerCase().includes(query.toLowerCase())
        )
        // 模拟异步操作，使用setTimeout
        setTimeout(() => {
          this.options = results
          this.loading = false
        }, 1000) // 假设搜索耗时1秒
      } else {
        this.options = [] // 清空选项列表
      }
    },
    async FindOrderYieldRateSteel() {
      const res = await post(FindOrderYieldRateSteel)
      // this.title = res.data.map(item => item.orderName)
      // res.data.map(item => {
      //   if (item.orderName) {
      //   }
      // })

      if (res.data) {
        this.loading = false
        // this.ecahrtAll = res.data
        // this.ecahrtAll[0].yrate[0].data = [1, 2, 3, 4, 5]
        // this.ecahrtAll[1].yrate[0].data = [1, 2, 3, 4, 5]
        // this.ecahrtAll[2].yrate[0].data = [1, 2, 3, 4, 5]
        // this.ecahrtAll[3].yrate[0].data = [1, 2, 3, 4, 5]
        // console.log('aaaaa', this.stringsToNumbers(this.ecahrtAll[0].yrate))
        let index = 0
        res.data.forEach(item => {
          this.ecahrtAll[index].yrate[0].data = this.stringsToNumbers(
            item.yrate
          )
          this.ecahrtAll[index].yrate[1].data = this.stringsToNumbers(
            item.grate
          )
          this.ecahrtAll[index].yrate[2].data = this.stringsToNumbers(
            item.crate
          )
          this.ecahrtAll[index].orderName = item.orderName
          let orderNo = item.oderNo
          this.ecahrtAll[index].orderNo = orderNo.slice(1, -1).split(/\s*,\s*/)
          this.ecahrtAll[index].xgrade = item.xgrade
          for (let i = 0; i < item.xgrade.length; i++) {
            if (
              this.ecahrtAll[index].yrate[0].data[i] === 0 &&
              this.ecahrtAll[index].yrate[1].data[i] === 0 &&
              this.ecahrtAll[index].yrate[2].data[i] === 0
            ) {
              this.ecahrtAll[index].yrate[0].data.splice(i, 1)
              this.ecahrtAll[index].yrate[1].data.splice(i, 1)
              this.ecahrtAll[index].yrate[2].data.splice(i, 1)
              this.ecahrtAll[index].xgrade.splice(i, 1)
            }
          }
          index++
        })
      }
    },
    async saveOrder() {
      this.dialogFormVisible = false
      let param = {
        orderName1: this.form.orderName1,
        orderNo1: JSON.stringify(this.form.orderNo1),
        orderName2: this.form.orderName2,
        orderNo2: JSON.stringify(this.form.orderNo2),
        orderName3: this.form.orderName3,
        orderNo3: JSON.stringify(this.form.orderNo3),
        orderName4: this.form.orderName4,
        orderNo4: JSON.stringify(this.form.orderNo4),
        orderName5: this.form.orderName5,
        orderNo5: JSON.stringify(this.form.orderNo5),
        orderName6: this.form.orderName6,
        orderNo6: JSON.stringify(this.form.orderNo6),
        orderName7: this.form.orderName7,
        orderNo7: JSON.stringify(this.form.orderNo7),
        orderName8: this.form.orderName8,
        orderNo8: JSON.stringify(this.form.orderNo8)
      }
      const res = await post(FullProcessYieldSave, {
        ...param
      })
    },
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    // 数据管理
    clearShowGridData(name) {
      this[name].showGridData = []
    },
    addShowGridData(name) {
      this[name].showGridData.push({})
    },
    delShowGridData(index, name) {
      this[name].showGridData.splice(index, 1)
    },
    stringsToNumbers(percentArray) {
      // 使用 map 方法遍历数组，并转换每个元素
      return percentArray.map(function(percentStr) {
        // 去掉百分号，并将剩余的字符串转换为浮点数
        // 注意：这里使用了 parseFloat 而不是 Number，因为 Number("0.5%") 会返回 NaN
        // 而 parseFloat 可以正确处理小数点后的字符串，只要我们先去掉百分号
        let numberStr = percentStr.replace(/%/g, '') // 使用正则表达式确保去掉所有百分号
        return parseFloat(numberStr) // 将百分比转换为小数
      })
    }
    // getHeat() {
    //   post(findHeatTreatmentYieldByDate, {
    //     setDate: this.cDate
    //   }).then(res => {
    //     //
    //     this.loading = false
    //     this.heatMonth.gridData = res.data.map(item => {
    //       return {
    //         hearthnumber: item.hearthnumber,
    //         plan: item.plan,
    //         reality: item.reality,
    //         loadingdelaytime: item.loadingdelaytime,
    //         complete: item.complete,
    //         unfinishedcause: item.unfinishedcause,
    //         homeworkdays: item.homeworkdays,
    //         plannedmonthlyoutput: item.plannedmonthlyoutput,
    //         cumulativeoutput: item.cumulativeoutput,
    //         targetschedule: Number(item.targetschedule).toFixed(2),
    //         avgDailyProduction: item.avgdailyproduction
    //       }
    //     })
    //     if (!res.data.length) {
    //       this.heatMonth = {
    //         bar1: [],
    //         barX: [],
    //         gridData: [],
    //         failReason: '',
    //         output: 0,
    //         targetSchedule: 0,
    //         percent: 0
    //       }
    //       return
    //     }
    //     const merge = res.data.find(item => item.hearthnumber === '合计')
    //     // const merge2 = res.data.find(item => item.hearthnumber === '合计')
    //     // const merge3 = res.data.find(item => item.hearthnumber === '合计')
    //     // const merge4 = res.data.find(item => item.hearthnumber === '合计')
    //     const list = []
    //     const C1List = res.data.filter(item =>
    //       ['1#炉', '2#炉'].includes(item.hearthnumber)
    //     )

    //     list.push(this.handleHeat(C1List))
    //     const C2List = res.data.filter(item =>
    //       ['3#炉', '4#炉'].includes(item.hearthnumber)
    //     )
    //     list.push(this.handleHeat(C2List))
    //     const C3List = res.data.filter(item =>
    //       ['5#炉', '6#炉'].includes(item.hearthnumber)
    //     )
    //     list.push(this.handleHeat(C3List))
    //     // console.log('C1List', this.handleHeat(C1List))
    //     // console.log('C2List', C2List)
    //     // // console.log('C3List', C3List)
    //     console.log('C333List', list)
    //     Object.assign(this.heatMonth, {
    //       bar1: list,
    //       output: merge ? merge.cumulativeoutput : 0,
    //       targetSchedule: merge
    //         ? math
    //             .multiply(
    //               Number(merge.plannedmonthlyoutput || 0),
    //               Number(math.divide(merge.targetschedule, 100) || 0)
    //             )
    //             .toFixed(2)
    //         : 0,
    //       percent: merge ? Number(merge.targetschedule).toFixed(2) : 0
    //     })
    //     Object.assign(this.heatMonth4, {
    //       bar1: [
    //         this.handleHeat(C2List),
    //         this.handleHeat([C2List[0]]),
    //         this.handleHeat([C2List[1]])
    //       ],
    //       output: this.handleHeat(C2List) ? this.handleHeat(C2List).value : 0,
    //       targetSchedule: this.handleHeat(C2List)
    //         ? math
    //             .multiply(
    //               Number(this.handleHeat(C2List).plan || 0),
    //               Number(
    //                 math.divide(this.handleHeat(C2List).targetSchedule, 100) ||
    //                   0
    //               )
    //             )
    //             .toFixed(2)
    //         : 0,
    //       percent: this.handleHeat(C2List)
    //         ? Number(this.handleHeat(C2List).targetSchedule).toFixed(2)
    //         : 0
    //     })
    //     Object.assign(this.heatMonth3, {
    //       bar1: [
    //         this.handleHeat(C3List),
    //         this.handleHeat([C3List[0]]),
    //         this.handleHeat([C3List[1]])
    //       ],
    //       output: this.handleHeat(C3List) ? this.handleHeat(C3List).value : 0,
    //       targetSchedule: this.handleHeat(C3List)
    //         ? math
    //             .multiply(
    //               Number(this.handleHeat(C3List).plan || 0),
    //               Number(
    //                 math.divide(this.handleHeat(C3List).targetSchedule, 100) ||
    //                   0
    //               )
    //             )
    //             .toFixed(2)
    //         : 0,
    //       percent: this.handleHeat(C3List)
    //         ? Number(this.handleHeat(C3List).targetSchedule).toFixed(2)
    //         : 0
    //     })
    //     Object.assign(this.heatMonth2, {
    //       bar1: [
    //         this.handleHeat(C1List),
    //         this.handleHeat([C1List[0]]),
    //         this.handleHeat([C1List[1]])
    //       ],
    //       output: this.handleHeat(C1List) ? this.handleHeat(C1List).value : 0,
    //       targetSchedule: this.handleHeat(C1List)
    //         ? math
    //             .multiply(
    //               Number(this.handleHeat(C1List).plan || 0),
    //               Number(
    //                 math.divide(this.handleHeat(C1List).targetSchedule, 100) ||
    //                   0
    //               )
    //             )
    //             .toFixed(2)
    //         : 0,
    //       percent: this.handleHeat(C1List)
    //         ? Number(this.handleHeat(C1List).targetSchedule).toFixed(2)
    //         : 0
    //     })
    //     //   this.setTopData('热处理炉总产量', merge ? merge.cumulativeoutput : 0)
    //   })
    // },
    // 处理数据合并
    // handleHeat(list) {
    //   const obj = {
    //     value: _.sumBy(list, 'cumulativeoutput'),
    //     plan: _.sumBy(list, 'plannedmonthlyoutput'),
    //     unit: '吨'
    //   }
    //   // 超欠进度 （（累计产量/计划产量）-（当前天数/当月总天数））*100
    //   const days = this.getResentMonth(this.prevDate)
    //   const currentDays = this.$moment(days.endTime).diff(
    //     this.$moment(days.startTime).subtract(1, 'days'),
    //     'days'
    //   )
    //   const monthDays = this.$moment(days.endMonthTime).diff(
    //     this.$moment(days.startTime).subtract(1, 'days'),
    //     'days'
    //   )
    //   obj.targetSchedule =
    //     (obj.value / obj.plan - currentDays / monthDays).toFixed(4) * 100
    //   // console.log(days, currentDays, monthDays, obj)
    //   if (obj.targetSchedule > 0) {
    //     obj.finished = true
    //     // console.log('aaaaa', obj.finished)
    //   } else {
    //     obj.finished = false
    //   }
    //   // obj.finished = obj.targetschedule > 0
    //   // console.log('obj', obj)
    //   obj.schedule = obj.targetschedule
    //     ? math.divide(math.multiply(obj.targetschedule || 0, obj.plan), 100)
    //     : ''
    //   return obj
    // }
  }
}
</script>

<style lang="less" scoped>
// 添加这个样式来隐藏最外层的border-head
/deep/.hide-header {
  display: none !important;
}
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: auto;
  overflow-x: hidden;
}

/deep/ .content {
  height: 200%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 20px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }

  .slick {
    height: 65px;
    position: relative;
    //top: -15px;
    margin-bottom: 15px;
  }
}

// 保留原有样式
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    width: 90px;
    line-height: 24px;
    font-size: 16px;
    white-space: nowrap;
    color: #ffffff;
  }

  span:last-child {
    flex: 1;
    overflow: auto;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;
    font-size: 0;
    margin-right: 10px;

    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.green {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}

.scroll-wrapper {
  height: 100%;
}

.kpi-list {
  font-size: 0;

  .item {
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12.5%;

    img {
      width: 48px;
      margin-right: 10px;
    }

    .name {
      font-size: 18px;
      font-weight: 700;
      line-height: 18px;
      margin-bottom: 10px;
      letter-spacing: 0px;
      text-align: left;
    }

    .num {
      font-size: 32px;
      font-weight: 700;
      line-height: 32px;
      letter-spacing: 0px;
      text-align: left;
    }

    .unit {
      font-size: 20px;
      font-weight: 350;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
    }
  }
}
</style>
