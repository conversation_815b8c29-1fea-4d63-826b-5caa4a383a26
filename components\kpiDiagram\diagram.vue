<template>

  <div
    class="page-content"
    style="height: 100%;position: relative">
    <div
      id="diagram"
      style="height: 100%;"
      @contextmenu.prevent.stop/>
    <div style="position: absolute; top: 0; right: 0; background: #fff; padding: 15px; text-align: right;border: 1px solid #eee">
      <el-radio-group v-model="layout">
        <el-radio-button label="force">网状</el-radio-button>
        <el-radio-button label="circular">环形</el-radio-button>
      </el-radio-group>
    </div>
    <div
      v-if="showTable"
      :style="{top: tableCoordinate.y + 'px', left: tableCoordinate.x + 'px' }"
      class="pop-table">
      <p style="font-size: 16px; margin-bottom: 20px; line-height: 1">{{ gridName }}</p>
      <el-table
        v-loading="loading"
        :data="gridData"
        border>
        <el-table-column
          property="ruleName"
          label="规则名"/>
        <el-table-column
          min-width="60"
          property="targetValue"
          label="目标值"/>
        <el-table-column
          min-width="60"
          property="resultValue"
          label="实际值"/>
        <el-table-column
          width="80"
          property="ruleStatus"
          label="预警状态">
          <template
            v-slot="{row}"
          >
            <el-tag
              :type="row.ruleStatus ? 'danger' : 'success'"
              disable-transitions
            >{{ row.ruleStatus ? '预警中' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>

</template>

<script>
import { post } from '@/lib/Util'
import {
  deleteDiagram,
  deleteKpiIndex,
  findKpiCateRelationByKid,
  findKpiDiagram,
  findKpiTree,
  findRulesOfKpi,
  saveDiagram,
  updateIsShow
} from '@/api/kpi'
import { ENUM } from '@/lib/Constant'
import { findBySpecification } from '@/api/kpi'
export default {
  name: 'KpiDiagram',
  props: {
    nodeId: {
      type: Number,
      default: null
    }
  },
  data: () => {
    return {
      showTable: false,
      loading: false,
      gridData: [],
      gridName: '',
      tableCoordinate: {
        x: null,
        y: null
      },
      tableY: null,
      selected: null,
      chart: null,
      showData: {
        nodes: [],
        links: [],
        categories: []
      },
      chartOption: null,
      layout: 'force',
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: findBySpecification, //分页接口地址
        delete: deleteKpiIndex //删除接口地址
      },
      editUserId: null,
      data: [], // 树状数据
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      factory: 1,
      factoryList: [
        {
          value: 1,
          label: '第一炼钢厂'
        },
        {
          value: 2,
          label: '中板厂'
        },
        {
          value: 3,
          label: '板卷厂'
        },
        {
          value: 4,
          label: '宽厚板厂'
        }
      ],
      kpiFunction: ENUM.kpiFunction,
      levelList: ENUM.levelList,
      statusList: [
        {
          value: 0,
          label: '正常',
          type: 'success'
        },
        {
          value: 1,
          label: '废弃',
          type: 'warning'
        }
      ],
      rightMenuVisible: false,
      rightMenuLeft: 0,
      rightMenuTop: 0,
      rightMenuData: null,
      showTree: false,
      warningColor: '#ee6666'
    }
  },
  watch: {
    layout: function(value) {
      this.chart.setOption({
        series: [
          {
            layout: value,
            data: this.showData.nodes.map(item => {
              if (item.warningStatus === '1') {
                item.itemStyle = {
                  color: this.warningColor
                }
              }
              return Object.assign({}, item, {
                symbolSize: (
                  item.symbolSize / (this.layout === 'circular' ? 3 : 1)
                ).toFixed(0)
              })
            })
          }
        ]
      })
    },
    showData: {
      deep: true,
      handler() {
        this.showData.nodes.forEach(function(node) {
          node.symbolSize = 65 / Math.sqrt(node.rank)
          node.label = {
            show: node.symbolSize > 30
          }
        })
        this.chart.setOption({
          legend: [
            {
              // selectedMode: 'single',
              data: this.showData.categories.map(function(a) {
                return a.name
              })
            }
          ],
          series: [
            {
              data: this.showData.nodes.map(item => {
                if (item.warningStatus === '1') {
                  item.itemStyle = {
                    color: this.warningColor
                  }
                }
                return item
              }),
              links: this.showData.links,
              categories: this.showData.categories
            }
          ]
        })
      }
    },
    rightMenuVisible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.init()
      // this.loadData()
    })
  },
  methods: {
    // 页面初始化
    async init() {
      //
      const chartDom = document.getElementById('diagram')
      this.chart = this.$echarts.init(chartDom)
      this.showData.nodes.forEach(function(node) {
        node.label = {
          show: node.symbolSize > 30
        }
      })
      this.chartOption = {
        title: {
          text: '指标关系图',
          subtext: 'Default layout',
          top: 'bottom',
          left: 'right'
        },
        color: [
          '#5470c6',
          '#91cc75',
          '#fac858',
          '#73c0de',
          '#3ba272',
          '#fc8452',
          '#9a60b4',
          '#ea7ccc',
          '#67d2ac'
        ],
        tooltip: {
          show: false
        },
        legend: [
          {
            data: this.showData.categories.map(function(a) {
              return a.name
            })
          }
        ],
        animationDuration: 0,
        animationEasingUpdate: 'quinticInOut',
        series: [
          {
            name: '',
            type: 'graph',
            edgeSymbol: ['circle', 'arrow'],
            layout: this.layout,
            force: {
              repulsion: 280,
              edgeLength: 100
            },
            data: [],
            links: [],
            categories: [],
            roam: true,
            label: {
              position: 'right',
              formatter: '{b}'
            },
            lineStyle: {
              color: 'source',
              curveness: 0.3
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 10
              }
            }
          }
        ]
      }
      this.chart.setOption(this.chartOption)
      this.chart.on('click', async params => {
        console.log(params)
        if (params.dataType === 'node') {
          // 1
        }
      })
      this.chart.on('mousemove', async params => {
        this.tableCoordinate.x = params.event.event.clientX + 30
        this.tableCoordinate.y = params.event.event.clientY + 30
      })
      this.chart.on('mouseover', async params => {
        console.log(params)
        this.tableCoordinate.x = params.event.event.clientX + 30
        this.tableCoordinate.y = params.event.event.clientY + 30
        if (params.dataType === 'node') {
          this.gridName = params.data.name
          this.gridData = []
          this.loading = true
          this.showTable = true
          // 1
          post(findRulesOfKpi, { kid: params.data.id })
            .then(res => {
              this.gridData = res.data
              this.loading = false
            })
            .catch(e => {
              this.loading = false
            })
        }
      })
      this.chart.on('mouseout', async params => {
        if (params.dataType === 'node') {
          this.showTable = false
          this.gridData = []
        }
      })
      window.onresize = () => {
        this.chart.resize()
      }
      // 获取关系图数据
      await this.getDiagram()
    },
    async getDiagram() {
      this.selected = null
      this.chart.showLoading()
      const { data } = await post(findKpiCateRelationByKid, {
        kid: this.nodeId
      })
      const nodes = data.nodes.filter(item => {
        return item.id == this.nodeId
      })
      this.diagramFilter(nodes, [...nodes], data)
      this.showData = Object.assign(data, {
        nodes
      })
      this.chart.hideLoading()
    },

    // 指标递归
    diagramFilter(nodes, addList, data) {
      const newList = []
      addList.forEach(item => {
        const tem = data.nodes.filter(node => {
          //links包含匹配
          const matches = data.links.find(link => {
            return link.source == node.id && link.target == item.id
          })
          // 且未添加
          const notHas = !nodes.filter(node1 => node1.id == node.id).length
          return matches && notHas
        })
        newList.push(...tem)
        nodes.push(...tem)
      })
      if (newList.length) {
        this.diagramFilter(nodes, newList, data)
      }
    },

    // 生成联系
    async generateRelation(source, target) {
      this.$confirm(
        `是否确认建立 ${source.name} → ${target.name} 的链接?`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        // 建立链接
        const data = await post(saveDiagram, {
          sourceId: source.id,
          targetId: target.id
        })
        if (data.success) {
          this.showData.links.push({
            source: source.id,
            target: target.id
          })
          this.selected = null
        }
      })
    },
    // 删除联系
    removeRelation(source, target) {
      this.$confirm(`是否确认删除该链接?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除连线
        post(deleteDiagram, {
          targetId: target,
          sourceId: source
        }).then(res => {
          if (res.success) {
            const index = this.showData.links.findIndex(
              item => item.target === target && item.source === source
            )
            this.showData.links.splice(index, 1)
            this.selected = null
          }
        })
      })
    },

    async loadData() {
      const { data } = await post(findKpiTree, { factory: this.factory })
      // this.data = data
      const list = this.kpiFunction.map(item => {
        item.id = 'kpi' + item.value
        item.name = item.label
        return item
      })
      if (data) {
        this.data = list
          .map(item => {
            item.children = data.filter(kpi => kpi.feature == item.value)
            return item
          })
          .filter(item => item.children.length)
      }
    },
    async handleNodeClick(data) {
      this.closeMenu()
      this.highLightChart(data)
    },

    // 增加子节点
    handleAddChild(node, data) {
      console.log(node, data)
      this.$refs.modalForm.add()
      this.$nextTick(() => {
        this.$refs.modalForm.formData.pid = node.level === 0 ? '0' : data.id
        this.$refs.modalForm.formData.rank = data.rank ? data.rank + 1 : 1
        this.$refs.modalForm.formData.category =
          node.level === 0 ? null : data.category
        this.$refs.modalForm.formData.parentName = data.name
      })
      this.$refs.modalForm.visible = true
    },
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$nextTick(() => {
        this.$refs.modalForm.formData.parentName = row.name
      })
      this.$refs.modalForm.visible = true
      this.selected = null
    },
    rightAddNext() {
      //
      console.log(this.rightMenuData)
      this.handleAddChild(this.rightMenuData, this.rightMenuData.data)
    },
    rightAddBrother() {
      //
      this.handleAddChild(
        this.rightMenuData.parent,
        this.rightMenuData.parent.data
      )
    },
    rightMod() {
      //
      this.handleEdit(this.rightMenuData.data)
    },
    rightDel() {
      //
      this.handleDelete(this.rightMenuData.data)
    },
    oncontextmenu(e, data, node) {
      return
      this.rightMenuTop = e.clientY
      this.rightMenuLeft = e.clientX
      this.rightMenuVisible = true
      this.rightMenuData = node
    },
    closeMenu(e) {
      this.rightMenuVisible = false
      this.rightMenuData = null
    },

    // 更新节点
    updateNode(type, data) {
      console.log(data)
      // 根据编辑情况更新页面信息
      if (type === 'add') {
        if (data.pid === '0') {
          // 添加根节点
          this.data.push(data)
        } else {
          // 添加子节点
          this.$refs.tree.append(data, data.pid)
          this.showData.nodes.push(data)
          this.generateRelation(data.pid, data.id)
        }
      } else if (type === 'edit') {
        this.$refs.tree.getNode(data.id).data = data // 更新树数据
      }
    },

    // 删除节点
    handleDelete: function(e, data) {
      console.log(e, data)
      // 删除操作
      post(updateIsShow, { id: data.id, isShow: !data.isShow }).then(res => {
        if (res.success) {
          // this.$message.info('删除成功')
          // this.$refs.tree.remove(data)
          // this.showData.nodes = this.showData.nodes.filter(
          //   res => res.id !== data.id
          // )
          this.updateShowStatus(data, !data.isShow)
          this.$refs.tree.getNode(data.id).data = data // 更新树数据
          this.getDiagram()
        }
      })
    },

    // 更新显示状态
    updateShowStatus(data, status) {
      data.isShow = status
      const list = data.children
      list &&
        list.length &&
        list.forEach(item => {
          this.updateShowStatus(item, status)
        })
    },

    // 取消选择
    cancelSelect() {
      this.selected = null
    },

    getShowStatus(id) {
      const node = this.$refs.tree.getNode(id)
      if (node) {
        return node.data.isShow
      } else {
        return true
      }
    },
    highLightChart(data) {
      this.selected &&
        this.chart.dispatchAction({
          type: 'downplay',
          // 用 index 或 id 或 name 来指定系列。
          // 可以使用数组指定多个系列。
          seriesIndex: 0,
          name: [data.name]
        })
      if (
        this.showData.nodes.findIndex(item => item.name === data.name) !== -1
      ) {
        this.selected = data
        this.selected.kid = data.id
        this.chart.dispatchAction({
          type: 'highlight',
          // 用 index 或 id 或 name 来指定系列。
          // 可以使用数组指定多个系列。
          seriesIndex: 0,
          name: [data.name]
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
.page-title {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  margin-bottom: 15px;
}

.page-content {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  box-shadow: 0 0 10px rgba(117, 116, 116, 0.1);
  height: 100%;
}

.search-wrapper {
  margin-bottom: 10px;
}

.page-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .operate-icon {
    margin-left: 8px;
  }
}

.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}
.tree-wrapper {
  height: 100%;
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.first-node {
  font-size: 18px;
}
/deep/ .el-tree-node {
  margin: 5px 0;
}
/deep/ .el-tree > .el-tree-node {
  margin: 15px 0 12px;
}
.contextmenu {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  background: #fff;
  font-size: 14px;
  color: #666;
  border: 1px solid #e9e9e9;
  min-width: 250px;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
  li {
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #f4f4f5;
    }
  }
  li:last-child {
    border-top: 1px solid #e9e9e9;
  }
}
.pop-table {
  position: fixed;
  width: 420px;
  padding: 24px;
  border-radius: 6px;
  box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.12),
    0px 4px 8px rgba(0, 0, 0, 0.08), 0px 4px 16px 4px rgba(0, 0, 0, 0.04);
  background: #fff;
}
</style>
