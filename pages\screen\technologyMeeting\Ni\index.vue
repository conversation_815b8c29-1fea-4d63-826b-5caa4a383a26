<template>
  <div class="content">
    <div 
      class="content-item">
      <custom-table
        :title="'镍系钢成材率'"
        :setting="tableObj1.setting"
        :url-list="tableObj1.url.list"
        :url-save="tableObj1.url.save"
        :select-date="selectDate"
        :table-row-class="tableRowClass"
        :table-class="'big-table'"/>
    </div>
    <div class="content-hold"/>
    <div 
      class="content-item">
      <screen-border :title="'镍系钢成材率损失'">
        <template v-slot:headerRight>
          <span
            class="screen-btn"
            @click="dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            明细
          </span>
        </template>
        <el-row
          :gutter="32"
          class="full-height">
          <el-col
            :span="8"
            class="full-height">
            <div 
              ref="table1" 
              class="chart-wrapper">
              <el-table
                :data="tableData['5Ni']"
                :max-height="tableData.maxHeight"
                :class="''"
                class="center-table font-table"
                border>
                <el-table-column
                  :property="'name'"
                  :label="'缺陷名称'"/>
                <el-table-column
                  :label="'5Ni'">
                  <el-table-column
                    :property="'pfl'"
                    :label="'判废量'"/>
                  <el-table-column
                    :property="'ccl'"
                    :label="'影响成材率'"/>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
          <el-col
            :span="8"
            class="full-height">
            <el-table
              :data="tableData['7Ni']"
              :max-height="tableData.maxHeight"
              :class="''"
              class="center-table font-table"
              border>
              <el-table-column
                :property="'name'"
                :label="'缺陷名称'"/>
              <el-table-column
                :label="'7Ni'">
                <el-table-column
                  :property="'pfl'"
                  :label="'判废量'"/>
                <el-table-column
                  :property="'ccl'"
                  :label="'影响成材率'"/>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col
            :span="8"
            class="full-height">
            <el-table
              :data="tableData['9Ni']"
              :max-height="tableData.maxHeight"
              :class="''"
              class="center-table font-table"
              border>
              <el-table-column
                :property="'name'"
                :label="'缺陷名称'"/>
              <el-table-column
                :label="'9Ni'">
                <el-table-column
                  :property="'pfl'"
                  :label="'判废量'"/>
                <el-table-column
                  :property="'ccl'"
                  :label="'影响成材率'"/>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </screen-border>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'94%'"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      class="screen-dialog"
      z-index="99"
      title="">
      <div style="height: 500px;">
        <custom-table
          :title="'镍系钢成材率损失'"
          :setting="tableObj2.setting"
          :url-list="tableObj2.url.list"
          :url-save="tableObj2.url.save"
          :height-auto="true"
          :show-edit="false"
          :table-row-class="tableRowClass"
          :select-date="selectDate"
          :table-class="'big-table'"/>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/technologyMeeting/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/technologyMeeting/component/custom-table'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import {
  findNiYieldByDate,
  findNiYieldLossByDate,
  findNiYieldLossInfoByDate,
  NiSteelSaveAll,
  saveNiYieldLoss
} from '@/api/screenTechnolagy'
import moment from 'moment'
import { post } from '@/lib/Util'
export default {
  name: 'Ni',
  components: { ScreenBorder, CustomTable, SingleBarsChart },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      dialogVisible: false,
      tableObj1: {
        url: {
          save: NiSteelSaveAll,
          list: findNiYieldByDate
        },
        setting: [
          {
            keyQuery: 'plt',
            keySave: 'plt',
            label: '产线',
            width: 80
          },
          {
            keyQuery: 'grade',
            keySave: 'grade',
            label: '钢种'
          },
          {
            keyQuery: 'scl',
            keySave: 'scl',
            label: '生产量'
          },
          {
            keyQuery: 'hgl',
            keySave: 'hgl',
            label: '综合合格量'
          },
          {
            keyQuery: 'pll',
            keySave: 'pll',
            label: '坯料量'
          },
          {
            keyQuery: 'designYield',
            keySave: 'designYield',
            label: '设计成材率'
          },
          {
            keyQuery: 'realityYield',
            keySave: 'realityYield',
            label: '实际成材率'
          },
          {
            keyQuery: 'yieldLoss',
            keySave: 'yieldLoss',
            label: '成材率损失'
          },
          {
            keyQuery: 'content',
            keySave: 'content',
            label: '备注',
            inputType: 'textarea'
          }
        ]
      },
      tableObj2: {
        url: {
          save: saveNiYieldLoss,
          list: findNiYieldLossByDate
        },
        setting: [
          {
            keyQuery: 'plt',
            keySave: 'plt',
            label: '产线',
            width: 80
          },
          {
            keyQuery: 'name',
            keySave: 'name',
            label: '缺陷名称'
          },
          {
            keyQuery: 'ni5',
            keySave: 'ni5',
            label: '5Ni判废量'
          },
          {
            keyQuery: 'ni5Yield',
            keySave: 'ni5Yield',
            label: '5Ni影响成材率'
          },
          {
            keyQuery: 'ni7',
            keySave: 'ni7',
            label: '7Ni判废量'
          },
          {
            keyQuery: 'ni7Yield',
            keySave: 'ni7Yield',
            label: '7Ni影响成材率'
          },
          {
            keyQuery: 'ni9',
            keySave: 'ni9',
            label: '9Ni判废量'
          },
          {
            keyQuery: 'ni9Yield',
            keySave: 'ni9Yield',
            label: '9Ni影响成材率'
          },
          {
            keyQuery: 'ni',
            keySave: 'ni',
            label: '判废总量'
          },
          {
            keyQuery: 'niYield',
            keySave: 'niYield',
            label: '影响成材率汇总'
          }
        ]
      },
      tableData: {
        maxHeight: null,
        '5Ni': [],
        '7Ni': [],
        '9Ni': []
      }
    }
  },
  computed: {
    selectedMonth: function() {
      return moment(this.selectDate).format('YYYY-MM')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.loadData()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.loadData()
  },
  mounted() {
    this.calculate()
  },
  methods: {
    loadData() {
      post(findNiYieldLossInfoByDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.tableData['5Ni'] = res.data[0] ? res.data[0]['5Ni'] : []
        this.tableData['7Ni'] = res.data[1] ? res.data[1]['7Ni'] : []
        this.tableData['9Ni'] = res.data[2] ? res.data[2]['9Ni'] : []
      })
    },
    tableRowClass(obj) {
      if (obj.row.grade === '合计') {
        return 'total'
      }
      if (obj.row.name === '合计') {
        return 'total'
      }
      return ''
    },
    calculate() {
      this.tableData.maxHeight = this.$refs.table1.offsetHeight
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
