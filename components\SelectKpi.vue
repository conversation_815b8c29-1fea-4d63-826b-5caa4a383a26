<template>
  <el-popover
    v-model="visible"
    placement="bottom"
    width="400"
    trigger="click">
    <div class="tree-box">
      <div class="tree-mode">
        <el-form
          ref="searchForm"
          :label-width="'80px'"
          :model="form"
          size="mini"
          inline
          @submit.native.prevent="handleSearch"
        >
          <el-input
            v-model="form.searchKey"
            size="small"
            placeholder="请输入指标描述"
            style="width: 200px;"
          />
          <el-button
            size="small"
            @click="handleSearch"
          >搜索</el-button>
          <el-button
            size="small"
            @click="show"
          >重置</el-button>
        </el-form>
      </div>

      <el-table
        v-if="showSearch"
        :data="tableData"
        :show-header="false"
        :size="'small'"
        border
        style="width: 100%"
        @row-click="nodeClick"
      >
        <el-table-column
          label="指标名称"
          prop="name"
        />
      </el-table>
      <el-tree
        v-else
        :data="data"
        :load="loadNode"
        :props="defaultProps"
        highlight-current
        lazy
        node-key="id"
        @node-click="nodeClick"
      />
    </div>
    <el-input
      slot="reference"
      v-model="form.name"
      :title="form.name"
      readonly
      placeholder="请选择上级指标"
    />
  </el-popover>
</template>

<script>
import { post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'
import { findBySpecification, findNextRank } from '@/api/kpi'

export default {
  name: 'SelectKpi',
  props: {
    value: {
      type: Number,
      default: null
    },
    parentName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      mode: false,
      data: [], // 树状数据
      dataSearch: [], // 树状数据
      url: {
        list: findBySpecification //分页接口地址
      },
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'leaf'
      },
      form: {
        id: null,
        name: null,
        searchKey: ''
      },
      kpiFunction: ENUM.kpiFunction,
      page: {
        page: 1,
        size: 10,
        total: 0
      },
      tableData: [],
      showSearch: false
    }
  },
  watch: {
    parentName: function(newVal) {
      console.log(newVal)
      this.form.name = newVal
    }
  },
  created() {
    // this.loadData()
  },
  methods: {
    async handleSearch(reset = false) {
      if (this.form.searchKey === '') {
        return this.$message.warning('请输入查询条件')
      }
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.page = 1
      }
      // 搜索
      const { data } = await post(
        this.url.list,
        Object.assign(
          {},
          {
            name: this.form.searchKey,
            page: this.page.page - 1,
            size: this.page.size
          }
        )
      )
      this.showSearch = true
      // console.log(data)
      this.tableData = data.results ? data.results.content : []
      this.page.page = data.results.pageable.pageNumber + 1
      this.page.size = data.results.pageable.pageSize
      this.page.total = data.results.totalElements
    },
    async loadRootData() {
      //
      const list = this.kpiFunction.map(item => {
        item.id = 'kpi' + item.value
        item.name = item.label
        return item
      })
      return Promise.resolve(list)
    },
    async loadData(parentId) {
      const { data } = await post(findNextRank, { parentId: parentId })
      return Promise.resolve(data)
    },
    async loadNode(node, resolve) {
      let data = null
      if (node.level === 0) {
        data = await this.loadRootData()
      } else {
        const parentId = node.level === 1 ? 0 : node.data.id
        data = await this.loadData(parentId)
        console.log(data)
        if (node.level === 1) {
          data = data.filter(kpi => kpi.feature == node.data.value)
        }
      }
      resolve(data)
    },
    // async loadNode(node, resolve) {
    //   const data = await this.loadData(node.data.orgCode || 'X50000000')
    //   resolve(data)
    // },
    nodeClick(data, node = null) {
      this.form.id = node && node.level === 1 ? 0 : data.id
      this.form.name = data.name
      this.$emit('input', this.form.id)
      // 推送变化
      this.$emit('on-change', this.form.id)
      this.visible = false
    },
    /**
     * 根据后端全量列表生成树状菜单结构数据
     * @param orgList 需处理菜单列表
     * @param pid  父级id
     * @returns {*[]}
     */
    getOrgData(orgList = [], pid = '') {
      const data = []
      // 本次递归第一级菜单
      for (let item of orgList) {
        if (!item.parentOrgCode) item.parentOrgCode = ''
        if (item.parentOrgCode === pid) {
          data.push(item)
        }
      }
      // 本次递归二级菜单
      for (let item of data) {
        item.children = this.getOrgData(orgList, item.orgCode)
        if (!item.children.length) {
          delete item.children
        }
      }
      // console.log(data)
      return data
    },
    handleReset() {
      this.form.searchKey = ''
      this.showSearch = false
      this.tableData = []
    },
    show() {
      console.log(this.form.name)
    }
  }
}
</script>

<style scoped>
.tree-box {
  max-height: 300px;
  overflow: auto;
}
.tree-mode {
  margin-bottom: 8px;
}
</style>
