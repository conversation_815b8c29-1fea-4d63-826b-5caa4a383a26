<template>
  <div class="bigBox">
    <el-row :gutter="32">
      <el-col :span="24">
        <screen-border title="四条龙劳动竞赛指标">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(1)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <el-table
            :data="listData1"
            height="300">
            <el-table-column
              type="index"
              label="序号"
              width="60" />
            <el-table-column
              prop="A1"
              label="班别"
              width="100"
              align="center" />
            <el-table-column
              prop="B1"
              label="考核产量"
              width="100"
              align="center" />
            <el-table-column
              prop="C1"
              label="排名"
              width="100"
              align="center" />
            <el-table-column
              prop="D1"
              label="接班装炉块数及自装自轧切割坯扣分"
              width="200"
              align="center" />
            <el-table-column
              prop="E1"
              label="排名"
              width="100"
              align="center" />
            <el-table-column
              prop="F1"
              label="剪切块数"
              width="100"
              align="center" />
            <el-table-column
              prop="G1"
              label="排名"
              width="100"
              align="center" />
            <el-table-column
              prop="H1"
              label="单订尺成材率"
              width="140"
              align="center" />
            <el-table-column
              prop="I1"
              label="排名"
              width="100"
              align="center" />
            <el-table-column
              prop="J1"
              label="厚度公差(0-25)"
              width="100"
              align="center" />
            <el-table-column
              prop="K1"
              label="排名"
              width="100"
              align="center" />
            <el-table-column
              prop="L1"
              label="首次降级"
              width="100"
              align="center" />
            <el-table-column
              prop="M1"
              label="排名"
              width="100"
              align="center" />
            <el-table-column
              prop="N1"
              label="加热炉单耗"
              width="120"
              align="center" />
            <el-table-column
              prop="O1"
              label="排名"
              width="100"
              align="center" />
            <el-table-column
              prop="P1"
              label="电耗"
              width="100"
              align="center" />
            <el-table-column
              prop="Q1"
              label="排名"
              width="100"
              align="center" />
            <el-table-column
              prop="R1"
              label="二切热装比例"
              width="140"
              align="center" />
            <el-table-column
              prop="S1"
              label="排名"
              width="100"
              align="center" />
            <el-table-column
              prop="T1"
              label="热装堆冷时间"
              width="140"
              align="center" />
            <el-table-column
              prop="U1"
              label="排名"
              width="100"
              align="center" />
            <el-table-column
              prop="V1"
              label="合计"
              width="100"
              align="center" />
            <el-table-column
              prop="W1"
              label="排名"
              width="100"
              align="center" />
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="24">
        <screen-border title="当班安全生产简要评价">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(2)">
              <el-icon class="el-icon-edit-outline" />
              操作
            </span>
          </template>
          <div class="disData">
            <div class="block">
              <div class="title">月度目标产量</div>
              <div class="context"><span>88</span><i>吨</i></div>
            </div>
            <div class="block">
              <div class="title">月度日历天数</div>
              <div class="context"><span>30</span><i>天</i></div>
            </div>
            <div class="block">
              <div class="title">实际天数</div>
              <div class="context"><span>30</span><i>天</i></div>
            </div>
          </div>
          <el-table
            :data="listData2"
            height="calc(100vh - 650px)">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="A1"
              label="班别"
              width="100"
              align="center" />
            <el-table-column
              prop="B1"
              label="折算产量"
              width="100"
              align="center" />
            <el-table-column
              prop="C1"
              label="实际产量"
              width="100"
              align="center" />
            <el-table-column
              prop="D1"
              label="考核产量"
              width="100"
              align="center" />
            <el-table-column
              prop="E1"
              label="计划产量"
              width="100"
              align="center" />
            <el-table-column
              prop="F1"
              label="延误"
              width="100"
              align="center" />
            <el-table-column
              prop="G1"
              label="6-7.5mm产量"
              width="140"
              align="center" />
            <el-table-column
              prop="H1"
              label="≥230坯料产量"
              width="140"
              align="center" />
            <el-table-column
              prop="I1"
              label="7.51～9.9㎜产量"
              width="140"
              align="center" />
            <el-table-column
              prop="J1"
              label="切割坯轧制宽度2000㎜及以上产量"
              width="180"
              align="center" />
            <el-table-column
              prop="K1"
              label="切割坯轧制宽度1800㎜～2000㎜产量"
              width="180"
              align="center" />
            <el-table-column
              prop="L1"
              label="切割坯轧制宽度小于1800㎜产量"
              width="180"
              align="center" />
            <el-table-column
              prop="M1"
              label="二切流通板"
              width="140"
              align="center" />
            <el-table-column
              prop="N1"
              label="6-12㎜G1产量"
              width="150"
              align="center" />
            <el-table-column
              prop="O1"
              label="6-12㎜G1产量"
              width="150"
              align="center" />
            <el-table-column
              prop="P1"
              label="不锈钢等NI系、特钢"
              width="140"
              align="center" />
            <el-table-column
              prop="Q1"
              label="修正"
              width="100"
              align="center" />
            <el-table-column
              prop="R1"
              label="检修（分钟）+外部"
              width="140"
              align="center" />
          </el-table>
        </screen-border>
      </el-col>
    </el-row>

    <!--弹框-->
    <el-dialog
      :visible.sync="dialogBox"
      :width="'90%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      @close="closeDialogBox">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span> -->
            <span
              v-show="title!='原因说明'"
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download" />
              下载
            </span>
            <span
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-printer" />
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <div>
        <el-table
          id="table"
          :data="formData"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60" />
          <el-table-column
            v-for="(item,index) in Header"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            width="160"
            align="center">
            <template v-slot="{ row }">
              <el-input v-model="row[item.prop]" />
              <span v-show="false">{{ row[item.prop] }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            align="center"
            width="100"
            label="操作">
            <template v-slot="{ row, $index }">
              <span
                class="screen-btn"
                @click="delRow($index)">
                <el-icon class="el-icon-delete" />
                删除
              </span>
            </template>
          </el-table-column> -->
        </el-table>
        <!-- <div class="text-center">
          <span
            class="screen-btn"
            @click="addNewRow()">
            <el-icon class="el-icon-circle-plus-outline" />
            增加数据
          </span>
        </div> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import { saveAs } from 'file-saver'
import * as echarts from 'echarts'
import moment from 'moment'

import {} from '@/api/screen'

export default {
  name: 'TeamRank',
  components: {
    ScreenBorder
  },
  props: {
    selTime: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      //四条龙劳动竞赛指标
      listData1: [
        {
          A1: 'A1',
          B1: 'B1',
          C1: 'C1',
          D1: 'D1',
          E1: 'E1',
          F1: 'F1',
          G1: 'G1',
          H1: 'H1',
          I1: 'I1',
          J1: 'J1',
          K1: 'K1',
          L1: 'L1',
          M1: 'M1',
          N1: 'N1',
          O1: 'O1',
          P1: 'P1',
          Q1: 'Q1',
          R1: 'R1',
          S1: 'S1',
          T1: 'T1',
          U1: 'U1',
          V1: 'V1',
          W1: 'W1'
        }
      ],

      //当班安全生产简要评价
      listData2: [
        {
          A1: 'A1',
          B1: 'B1',
          C1: 'C1',
          D1: 'D1',
          E1: 'E1',
          F1: 'F1',
          G1: 'G1',
          H1: 'H1',
          I1: 'I1',
          J1: 'J1',
          K1: 'K1',
          L1: 'L1',
          M1: 'M1',
          N1: 'N1',
          O1: 'O1',
          P1: 'P1',
          Q1: 'Q1',
          R1: 'R1'
        }
      ],

      //弹框统一
      dialogBox: false,
      title: '',
      //弹框统一表字段
      Header: [],
      //弹框统一表数据
      formData: [],
      //上传Excel
      fileList: []
    }
  },
  watch: {
    selTime: function() {
      // this.getSecurityChecks()
    }
  },

  created() {
    // this.getSecurityChecks()
    // this.getProjectSecurity()
  },
  methods: {
    //安全检查情况
    // async getSecurityChecks() {
    //   this.securityChecks = []
    //   let res = await post(SECURITYCHECKS, {
    //     setTime: this.selTime
    //   })
    //   // console.log('安全检查情况', res)

    //   if (res.data.length != 0) {
    //     this.securityChecks = res.data
    //   }
    // },

    //弹框
    openView(nub) {
      this.dialogBox = true
      if (nub === 1) {
        this.title = '四条龙劳动竞赛指标'
        this.Header = [
          {
            label: '班组',
            prop: 'A1'
          },
          {
            label: '考核产量',
            prop: 'B1'
          },
          {
            label: '排名',
            prop: 'C1'
          },
          {
            label: '接班装炉块数及自装自轧切割坯扣分',
            prop: 'D1'
          },
          {
            label: '排名',
            prop: 'E1'
          },
          {
            label: '剪切块数',
            prop: 'F1'
          },
          {
            label: '排名',
            prop: 'G1'
          },
          {
            label: '单订尺成材率',
            prop: 'H1'
          },
          {
            label: '排名',
            prop: 'I1'
          },
          {
            label: '厚度公差(0-25)',
            prop: 'J1'
          },
          {
            label: '排名',
            prop: 'K1'
          },
          {
            label: '首次降级',
            prop: 'L1'
          },
          {
            label: '排名',
            prop: 'M1'
          },
          {
            label: '加热炉单耗',
            prop: 'N1'
          },
          {
            label: '排名',
            prop: 'O1'
          },
          {
            label: '电耗',
            prop: 'P1'
          },
          {
            label: '排名',
            prop: 'Q1'
          },
          {
            label: '二切热装比例',
            prop: 'R1'
          },
          {
            label: '排名',
            prop: 'S1'
          },
          {
            label: '热装堆冷时间',
            prop: 'T1'
          },
          {
            label: '排名',
            prop: 'U1'
          },
          {
            label: '合计',
            prop: 'V1'
          },
          {
            label: '排名',
            prop: 'W1'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData1))
      } else if (nub == 2) {
        this.title = '当班安全生产简要评价'
        this.Header = [
          {
            label: '班别',
            prop: 'A1'
          },
          {
            label: '拆算产量',
            prop: 'B1'
          },
          {
            label: '实际产量',
            prop: 'C1'
          },
          {
            label: '考核产量',
            prop: 'D1'
          },
          {
            label: '计划产量',
            prop: 'E1'
          },
          {
            label: '延误',
            prop: 'F1'
          },
          {
            label: '6-7.5㎜产量',
            prop: 'G1'
          },
          {
            label: '≥230坯料产量',
            prop: 'H1'
          },
          {
            label: '7.51～9.9㎜产量',
            prop: 'I1'
          },
          {
            label: '切割坯轧制宽度2000㎜及以上产量',
            prop: 'J1'
          },
          {
            label: '切割坯轧制宽度1800㎜～2000㎜产量',
            prop: 'K1'
          },
          {
            label: '切割坯轧制宽度小于1800㎜产量',
            prop: 'L1'
          },
          {
            label: '二切流通板',
            prop: 'M1'
          },
          {
            label: '6-12㎜G1产量',
            prop: 'N1'
          },
          {
            label: '外加工复合板',
            prop: 'O1'
          },
          {
            label: '不锈钢等NI系、特钢',
            prop: 'P1'
          },
          {
            label: '修正',
            prop: 'Q1'
          },
          {
            label: '检修（分钟）+外部',
            prop: 'R1'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData2))
      }
    },

    //添加行
    addNewRow() {
      this.formData.push({})
    },

    //删除行
    delRow(index) {
      this.formData.splice(index, 1)
    },

    //下载模板
    DownloadExcel() {
      let data = [{}]
      this.Header.forEach(item => {
        data[0][item.prop] = item.label
      })

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `${this.title}.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          `${this.title}.xlsx`
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      let header = {}
      let letter = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      this.Header.forEach((item, index) => {
        header[item.prop] = letter[index]
      })

      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, header)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        console.log(list)

        list.forEach(item => {
          item.selTime = this.selTime
          this.formData.push(item)
        })
      })
    },

    //保存新增数据
    async saveData() {
      let res
      if (this.title == '安全检查情况') {
        res = await post(SECURITYCHECKSSAVE, {
          setTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '施工项目安全检查情况') {
        res = await post(PROJECTSECURITYSAVE, {
          setTime: this.selTime,
          data: this.formData
        })
      }
      // console.log('保存', res)
      if (res.status == 1) {
        this.$message.success('保存成功')
        if (this.title == '安全检查情况') {
          this.getSecurityChecks()
        } else if (this.title == '施工项目安全检查情况') {
          this.getProjectSecurity()
        }

        this.closeDialogBox()
      }
    },

    //关闭弹框
    closeDialogBox() {
      this.dialogBox = false
      this.title = ''
      this.Header = []
      this.formData = []
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  .EchartsBox {
    height: 380px;

    .setRadio {
      /deep/ .el-radio {
        color: white;
      }
    }
  }

  .border-wrapper {
    margin-bottom: 15px;
  }

  /deep/ .el-textarea__inner {
    background-color: #041a21;
    border: 1px solid #1fc6ff;
    color: white;
    font-size: 14px;
    height: calc(100vh - 670px);
  }
}

//非计划数据统计
.disData {
  display: flex;
  margin-bottom: 15px;
  .block {
    border: 1px solid #1fc6ff;
    width: 220px;
    color: white;
    padding: 2px 10px;
    margin-right: 20px;
    .title {
      font-size: 14px;
    }
    .context {
      span {
        font-size: 24px;
        color: #1fc6ff;
        display: inline-block;
        margin: 5px 5px;
        i {
          display: inline-block;
          color: white;
        }
      }
    }
  }
}

/deep/ .el-table .el-table__cell {
  font-size: 19px;
}

/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}
</style>
