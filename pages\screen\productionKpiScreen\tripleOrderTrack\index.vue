<template>
  <div class="content">
    <div class="content-item">
      <el-tabs 
        v-model="activeName"
        type="card" 
        @tab-click="handleClick">
        <el-tab-pane 
          label="三重订单进程" 
          name="first">     
          <tripleOrderTrackpro style="height: 90vh;"/> 
        </el-tab-pane>
        <el-tab-pane 
          label="三重订单项目团队计划跟踪" 
          name="second"
        > <custom-table  
          :title="'三重订单项目团队计划跟踪'"
          :setting="tableObj1.setting"
          :url-list="tableObj1.url.list"
          :url-save="tableObj1.url.save"
          :select-date="selectDate"
          :table-class="'big-table'"
          style="height: 75vh;"
        /></el-tab-pane>
      </el-tabs>
     
    </div>
  </div>
</template>

<script>
import tripleOrderTrackpro from '../tripleOrderTrackpro'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import * as _ from 'lodash'
import { TripleOrderTrack, TripleOrderTrackSave } from '@/api/screen'
import moment from 'moment'
import CustomTable from '@/pages/screen/productionKpiScreen/component/custom-table2'

export default {
  name: 'TripleOrderTrack',
  components: { CustomTable, ScreenBorder, tripleOrderTrackpro },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      activeName: 'second',
      editIndex: null,
      tableObj1: {
        url: {
          save: TripleOrderTrackSave,
          list: TripleOrderTrack
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index',
            fixed: true
          },
          {
            keyQuery: 'groupName',
            keySave: 'groupName',
            label: '小组名称',
            fixed: true,
            width: 150
          },
          {
            keyQuery: 'orderId',
            keySave: 'orderId',
            label: '订单',
            fixed: true,
            width: 150
          },
          {
            keyQuery: 'dueDate',
            keySave: 'dueDate',
            label: '完成时间',
            inputType: 'textarea',
            fixed: true,
            width: 150
          },
          {
            keyQuery: 'projectManager',
            keySave: 'projectManager',
            label: '项目经理',
            fixed: true,
            inputType: 'textarea',
            width: 150
          },
          {
            keyQuery: 'teamMember',
            keySave: 'teamMember',
            label: '组员',
            fixed: true,
            inputType: 'textarea',
            width: 300
          },
          {
            keyQuery: 'oneWeekPlan',
            keySave: 'oneWeekPlan',
            label: '第一周重点工作及计划安排',
            inputType: 'textarea',
            width: 250
          },
          {
            keyQuery: 'oneWeekResult',
            keySave: 'oneWeekResult',
            label: '第一周检查反馈',
            inputType: 'textarea',
            width: 250
          },
          {
            keyQuery: 'twoWeekPlan',
            keySave: 'twoWeekPlan',
            label: '第二周重点工作及计划安排',
            inputType: 'textarea',
            width: 250
          },
          {
            keyQuery: 'twoWeekResult',
            keySave: 'twoWeekResult',
            label: '第二周检查反馈',
            inputType: 'textarea',
            width: 250
          },
          {
            keyQuery: 'threeWeekPlan',
            keySave: 'threeWeekPlan',
            label: '第三周重点工作及计划安排',
            inputType: 'textarea',
            width: 250
          },
          {
            keyQuery: 'threeWeekResult',
            keySave: 'threeWeekResult',
            label: '第三周检查反馈',
            inputType: 'textarea',
            width: 250
          },
          {
            keyQuery: 'fourWeekPlan',
            keySave: 'fourWeekPlan',
            label: '第四周重点工作及计划安排',
            inputType: 'textarea',
            width: 250
          },
          {
            keyQuery: 'fourWeekResult',
            keySave: 'fourWeekResult',
            label: '第四周检查反馈',
            inputType: 'textarea',
            width: 250
          },
          {
            keyQuery: 'projectEvaluation',
            keySave: 'projectEvaluation',
            label: '项目评价',
            inputType: 'textarea',
            fixed: 'right',
            width: 150
          }
        ]
      }
    }
  },
  computed: {
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'month')
        .format('yyyy-MM')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      // this.getpilotPlan()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    },

    calculate() {
      // this.pilotPlan1.maxHeight = this.$refs.table1.offsetHeight
    },
    totalClass(row) {
      if (row.row.serialNumber && row.row.serialNumber.trim() === '合计') {
        return 'table-total'
      }
      return ''
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.edit-btn {
  margin: 0 3px;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.dialog-item-tit {
  font-size: 18px;
  color: #dcdcdc;
  margin-bottom: 8px;
  margin-top: 15px;
}
/deep/.el-tabs--card > .el-tabs__header {
  border: 0px;
  font-size: 30;
}
.el-tabs__nav-wrap::before,
.el-tabs__nav-wrap::after,
.el-tabs__nav-bar {
  border: none !important; /* 使用 !important 来确保覆盖默认样式 */
}

/* 去掉每个 tab 项的左右边框（如果需要） */
/deep/.el-tabs__item {
  border-right: none !important; /* 根据需要调整选择器 */
  color: #f6f7ff;
}

/* 去掉第一个 tab 项的左边框（如果需要） */
.el-tabs__item:first-child {
  border-left: none !important; /* 根据需要调整选择器 */
}
/deep/.el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: none;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
  box-sizing: border-box;
}
/deep/.el-tabs--card > .el-tabs__header .el-tabs__item {
  border-bottom: 0;
  border-left: 0;
}
/deep/.el-tabs__item {
  font-size: 19px; /* 将字体大小设置为 16px，你可以根据需要调整这个值 */
}
/deep/.el-tabs__item.is-active {
  color: #e0e4ff;
  background-color: rgb(12 78 100);
}
/deep/ .el-tabs__nav-wrap .el-tabs__nav-item:not(.is-active) {
  color: #fff;
  background-color: #082f3c;
}
</style>
