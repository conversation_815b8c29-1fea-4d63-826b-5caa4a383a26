<template>
  <client-only>
    <div class="side-wrapper">
      <div class="menu-box">
        <el-menu
          :collapse="menuCollapse"
          :default-active="activeMenu"
          :unique-opened="true"
          class="el-menu-demo"
          background-color="#232253"
          text-color="#f1f1f1"
          active-text-color="#fff"
          @open="handleOpenNav"
          @select="handleChangeNav"
          @close="handleCloseNav">
          <menu-item
            v-for="item in userMenuList"
            :key="item.id"
            :menu="item"/>
        </el-menu>
      </div>
      <div
        :title="menuCollapse ? '展开' : '收起'"
        class="collapse-btn"
        @click="handleCollapse">
        <i :class="menuCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"/>
      </div>
    </div>
  </client-only>
</template>

<script>
import menuItem from '@/layouts/component/MenuItem'
import { mapState } from 'vuex'
import { filterUrl } from '@/lib/Menu'

export default {
  components: {
    menuItem
  },
  data: () => {
    return {
      isCollapse: true
    }
  },
  computed: {
    ...mapState('menu', ['userMenuList', 'menuCollapse']),
    activeMenu() {
      const route = this.$route
      const { fullPath } = route
      return filterUrl(fullPath)
    }
  },
  methods: {
    handleOpenNav(key, keyPath) {
      // console.log(key, keyPath)
    },
    handleCloseNav(key, keyPath) {
      // console.log(key, keyPath)
    },
    handleChangeNav(key, keyPath) {
      console.log(key, keyPath)
      this.$router.push(key)
    },
    handleCollapse() {
      this.$store.commit('menu/menuCollapse', !this.menuCollapse)
    }
  }
}
</script>

<style scoped lang="less">
.home {
  width: 220px;
  margin: 0 auto 10px;
  text-align: center;
  background-color: #93a9e1;
  height: 50px;
  line-height: 50px;
  border-radius: 4px;
  color: #ffffff;
  letter-spacing: 3px;
  cursor: pointer;
  .el-icon-s-home {
    font-size: 20px;
  }
}
.el-menu {
  border-right: none;
  //padding-left: 10px;
  &:not(.el-menu--collapse) {
    width: 260px;
  }
  .el-menu-item {
    font-size: 16px;
    letter-spacing: 2px;
  }
}
/deep/ .el-menu-demo {
  > div > .el-menu-item {
    padding-left: 30px !important;
  }
  > div > .el-submenu > .el-submenu__title {
    padding-left: 30px !important;
  }
  > div > .el-submenu > .el-menu > div > .el-submenu > .el-submenu__title {
    padding-left: 40px !important;
  }
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-menu-item,
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-submenu__title {
    padding-left: 48px !important;
  }
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-menu-item {
    padding-left: 60px !important;
  }
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-submenu__title {
    padding-left: 60px !important;
  }
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-menu-item,
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-submenu__title {
    padding-left: 94px !important;
  }
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    img {
    display: none;
  }

  > div > .el-submenu > .el-menu > div > .el-submenu.is-opened > .el-menu,
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu.is-opened
    .el-menu
    .el-submenu__title,
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu.is-opened
    .el-menu
    .el-menu-item {
    background-color: #171636 !important;
  }
  &.el-menu--collapse {
    > div > .el-menu-item {
      padding-left: 20px !important;
    }
    > div > .el-submenu > .el-submenu__title {
      padding-left: 20px !important;
    }
  }
}

/deep/.el-menu .el-menu-item {
  transition: border ease-in-out 0.3s;
  background: transparent !important;
  &:hover:before {
    content: '';
    position: absolute;
    top: 8px;
    bottom: 8px;
    left: 16px;
    right: 16px;
    border-radius: 8px;
    background-color: #38386c;
    z-index: 1;
  }
  span {
    position: relative;
    z-index: 999;
  }
}

/deep/ .el-menu .el-menu-item.is-active {
  color: #f2f2f2;
  &:before {
    content: '';
    position: absolute;
    top: 8px;
    bottom: 8px;
    left: 16px;
    right: 16px;
    border-radius: 8px;
    background-color: #4458fe;
    z-index: 1;
  }
}
/deep/ .el-menu-item.is-active {
  background-color: transparent !important;
}
/deep/ .el-submenu__title {
}
/deep/ .el-menu--collapse .el-menu-item.is-active:before {
  display: none;
}
/deep/
  .el-menu--collapse
  .el-submenu
  .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}
/deep/ .el-menu--collapse .el-menu-item span,
/deep/ .el-menu--collapse .el-submenu .el-submenu__title span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}
/deep/ .el-menu--popup .el-submenu__title {
  background-color: transparent !important;
}
/deep/ .is-opened > .el-submenu__title span {
  position: relative;
}
/deep/ .is-opened > .el-submenu__title span:before {
  content: '';
  position: absolute;
  border-radius: 50%;
  background-color: #4458fe;
  width: 8px;
  height: 8px;
  left: -50px;
  top: 50%;
  margin-top: -4px;
}
.side-wrapper {
  height: 100%;
  position: relative;
  background: #232253;
  .collapse-btn {
    width: 100%;
    height: 56px;
    display: flex;
    align-items: center;
    font-size: 22px;
    padding-left: 20px;
    background: #232253;
    color: #fff;
    cursor: pointer;
  }
  .menu-box {
    height: calc(100% - 56px);
    overflow: auto;
  }
}
.menu-box {
  /*滚动条样式 chrome内核*/
  /*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
  &::-webkit-scrollbar {
    width: 6px; /*对垂直流动条有效*/
    height: 6px; /*对水平流动条有效*/
  }
  /*定义滚动条的轨道颜色、内阴影及圆角*/
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.15);
    border-radius: 3px;
  }
  /*定义滑块颜色、内阴影及圆角*/
  &::-webkit-scrollbar-thumb {
    border-radius: 7px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.15);
    background: #c1cbdb;
  }
  /*定义两端按钮的样式*/
  &::-webkit-scrollbar-button {
    display: none;
  }
  /*定义右下角汇合处的样式*/
  &::-webkit-scrollbar-corner {
    display: none;
  }
}
</style>
