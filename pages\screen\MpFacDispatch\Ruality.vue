<template>
  <div class="bigBox">
    <el-row :gutter="32">
      <el-col :span="12">
        <screen-border-multi>
          <template v-slot:title>
            <div class="header">
              <div class="tabBox">
                <div
                  v-for="(item, index) in tabList"
                  :key="index"
                  :style="{color:item.active?'#FFFFFF':''}"
                  class="tab"
                  @click="selTab(item, index)">
                  <div class="tab_block">
                    <div>{{ item.title }}</div>
                    <div
                      v-if="item.active"
                      class="tab_img">
                      <img
                        class="tab_img2"
                        src="@/assets/images/screen/tab-pane-active-line2.png"
                        alt="">
                      <img
                        class="tab_img1"
                        src="@/assets/images/screen/tab-pane-active-line.png"
                        alt="">
                    </div>
                  </div>
                </div>
              </div>
              <span
                v-command="'/screen/MpFacDispatch/edit'"
                class="screen-btn"
                @click="openView(1,active)">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </div>
          </template>
          <template v-slot:default>
            <div v-if="active===0">
              <el-table
                :data="focusedType"
                height="380">
                <el-table-column
                  type="index"
                  label="序号"
                  width="60"/>
                <el-table-column
                  prop="category"
                  label="类别"
                  width="140"
                  align="center"/>
                <el-table-column
                  prop="steel"
                  label="主要钢种"
                  width="100"
                  align="center"/>
                <el-table-column
                  prop="content"
                  label="跟踪内容"
                  width="100"
                  align="center"/>
                <el-table-column
                  prop="wgt"
                  label="生产量(轧制块/吨)"
                  width="180"
                  align="center"/>
                <el-table-column
                  prop="classes"
                  label="生产班别"
                  width="140"
                  align="center"/>
                <el-table-column
                  prop="abnormalDescription"
                  label="异常描述"
                  width="140"
                  align="center"/>
                <el-table-column
                  prop="remark"
                  label="备注"
                  width="140"
                  align="center"/>
              </el-table>
            </div>
            <div v-else-if="active===1">
              <el-table
                :data="focusedOrders"
                height="290">
                <el-table-column
                  type="index"
                  label="序号"
                  width="60"/>
                <el-table-column
                  prop="classes"
                  label="班别"
                  width="60"
                  align="center"/>
                <el-table-column
                  prop="rolling"
                  label="生产量(块/吨)"
                  width="140"
                  align="center"/>
                <el-table-column
                  prop="trackingProblem"
                  label="坯料跟踪问题"
                  width="140"
                  align="center"/>
                <el-table-column
                  prop="qualityTracking"
                  label="轧钢质量跟踪"
                  width="100"
                  align="center"/>
                <el-table-column
                  prop="abnormalDescription"
                  label="异常描述"
                  width="180"
                  align="center"/>
                <el-table-column
                  prop="remark"
                  label="备注"
                  width="150"
                  align="center"/>  
                <el-table-column
                  prop="G1"
                  width="200"
                  label="照片">
                  <template v-slot="scope">
                    <div class="tasksList">
                      <div class="imgView">
                        <div 
                          v-for="(item,index) in scope.row.imgFile"
                          :key="index">
                          <img 
                            :src="item" 
                            @click="viewFile(item)" >
                          <i 
                            class="el-icon-error" 
                            @click="delFile(item,scope.row.id)"/>
                        </div>
                      </div>
                      <el-upload
                        ref="upload"
                        :http-request="uploadFileData"
                        :on-remove="handleRemove"
                        :file-list="fileImgList"
                        :multiple ="false"
                        :data="{id:scope.row.id}"
                        :on-exceed="handleExceed"
                        :before-upload="beforeUpload"
                        :show-file-list="false"
                        action=""
                        class="upload-demo">
                        <el-button
                          :loading="imgLoading"
                          size="mini"
                          type="primary"
                          icon="el-icon-upload"/>
                      </el-upload>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-input
                :rows="2"
                v-model="OrdersText"
                disabled
                style="white-space: pre-wrap;margin-top: 10px;"
                type="textarea"
                placeholder="备注!"/>
            </div>
          </template>
        </screen-border-multi>
      </el-col>
      <el-col :span="12">
        <screen-border title="产品质量跟踪">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(2,'')">
              <el-icon class="el-icon-edit-outline" />
              操作
            </span>
          </template>
          <el-table
            :data="productQuality"
            height="380">
            <el-table-column
              type="index"
              label="序号"
              width="60" />
            <el-table-column
              prop="graveyardShift"
              label="大夜班"
              align="center" >
              <template v-slot="scope">
                <el-input
                  :rows="2"
                  v-model="scope.row.graveyardShift"
                  disabled
                  style="white-space: pre-wrap;margin-bottom: 10px;"
                  type="textarea"
                  placeholder="备注!"/>
              </template>
            </el-table-column>
            <el-table-column
              prop="dayShift"
              label="白班"
              align="center" >
              <template v-slot="scope">
                <el-input
                  :rows="2"
                  v-model="scope.row.dayShift"
                  disabled
                  style="white-space: pre-wrap;margin-bottom: 10px;"
                  type="textarea"
                  placeholder="备注!"/>
              </template>
            </el-table-column>
            <el-table-column
              prop="nightShift"
              label="小夜班"
              align="center" >
              <template v-slot="scope">
                <el-input
                  :rows="2"
                  v-model="scope.row.nightShift"
                  disabled
                  style="white-space: pre-wrap;margin-bottom: 10px;"
                  type="textarea"
                  placeholder="备注!"/>
              </template>
            </el-table-column>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="成材率(待定)">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(3,'')">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <el-table
            :data="yieldData"
            height="calc(100vh - 670px)">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="classes"
              label="班次"
              width="80"
              align="center"/>
            <el-table-column
              prop="classes1"
              label="班别"
              width="80"
              align="center"/>
            <el-table-column
              prop="target"
              label="目标成材率(%)"
              align="center"/>
            <el-table-column
              prop="actual"
              label="实际成材率(%)"
              align="center"/>
            <el-table-column
              prop="situationDescription"
              label="情况说明"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="非计划数据统计">
          <template v-slot:headerRight>
            <!-- <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(4,'')">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span> -->
          </template>
          <div class="disData">
            <div class="block">
              <div class="title">1/2线当日瓢曲情况</div>
              <div class="context"><i/><span>{{ numberOfBlocks1 }}块</span><i/><span>{{ wgt1 }}t</span><i/><span>{{ rate1 }}%</span></div>
            </div>
            <div class="block">
              <div class="title">3线当日瓢曲情况</div>
              <div class="context"><i/><span>{{ numberOfBlocks2 }}块</span><i/><span>{{ wgt2 }}t</span><i/><span>{{ rate2 }}%</span></div>
            </div>
          </div>
          <el-table
            :data="unplannedData"
            height="calc(100vh - 746px)">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="classify"
              label="分类"
              width="60"
              align="center"/>
            <el-table-column
              prop="numberOfBlocks"
              label="块数"
              width="80"
              align="center"/>
            <el-table-column
              prop="wgt"
              label="重量"
              align="center"/>
            <el-table-column
              prop="realValue"
              label="实绩"
              align="center"/>
            <el-table-column
              prop="planValue"
              label="计划"
              align="center"/>
            <el-table-column
              prop="isComplete"
              label="是否完成"
              align="center"/>
            <el-table-column
              prop="remark"
              label="备注"
              align="center"/>
            <el-table-column
              prop="reason"
              label="降级原因"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
    </el-row>
    
    <!--弹框-->
    <el-dialog
      :visible.sync="dialogBox"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      @close="closeDialogBox">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span> -->
            <span
              v-show="title!='原因说明'"
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download" />
              下载
            </span>
            <span
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-printer" />
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <div>
        <el-table
          id="table"
          :data="formData"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60" />
          <el-table-column
            v-for="(item,index) in Header"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            align="center">
            <template v-slot="{ row }">
              <el-input 
                :disabled="item.disabled?item.disabled:false" 
                v-model="row[item.prop]" />
              <span v-show="false">{{ row[item.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            width="100"
            label="操作">
            <template v-slot="{ row, $index }">
              <div class="btn">
                <el-button 
                  :disabled="title=='成材率(待定)'||title=='非计划数据统计'||title=='胜代订单生产情况'"
                  type="danger"
                  icon="el-icon-delete"
                  @click="delRow($index)"/>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-input
          v-if="title=='重点订单跟踪情况'"
          :rows="2"
          v-model="textareaCopy"
          class="textarea"
          style="white-space: pre-wrap;margin-top: 20px;"
          type="textarea"
          placeholder="备注!"/>
          
        <div 
          v-if="title=='重点类别跟踪情况'||title=='产品质量跟踪'" 
          class="text-center">
          <span
            class="screen-btn"
            @click="addNewRow">
            <el-icon class="el-icon-circle-plus-outline" />
            增加数据
          </span>
        </div>
      </div>
    </el-dialog>

    <!-- 图片查看器 -->
    <div class="demo-image__preview">
      <el-image
        ref="previewImg"
        :preview-src-list="srcList"
        style="width: 100px; height: 100px;display: none"/>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { saveAs } from 'file-saver'
import * as echarts from 'echarts'
import moment from 'moment'

import {
  FOCUSEDTYPEDATA,
  FOCUSEDTYPESAVE,
  FOCUSEDORDERSDATA,
  FOCUSEDORDERSSAVE,
  FOCUSEDORDERSUPDATA,
  PRODUCTQUALITYDATA,
  PRODUCTQUALITYSAVE,
  YIELDDATA,
  YIELDSAVE,
  UNPLANNEDDATA,
  UNPLANNEDSAVE,
  minio_upload
} from '@/api/screen'
import { set } from 'lodash'

export default {
  name: 'Ruality',
  components: {
    // SingleBarsChart,
    // SteelBarsChart,
    ScreenBorder,
    ScreenBorderMulti
  },
  props: {
    selTime: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      active: 0,
      tabList: [
        {
          active: true,
          title: '重点类别跟踪情况'
        },
        {
          active: false,
          title: '胜代订单生产情况'
        }
      ],
      //重点类别跟踪情况
      focusedType: [],

      //胜代订单生产情况
      focusedOrders: [],
      imgLoading: false,
      fileImgList: [],
      //图片查看器
      srcList: [],
      //备注
      OrdersText: null,

      //产品质量跟踪
      productQuality: [],

      //成材率(待定)
      yieldData: [],

      //非计划
      unplannedData: [],
      numberOfBlocks1: 0,
      numberOfBlocks2: 0,
      wgt1: 0,
      wgt2: 0,
      rate1: 0,
      rate2: 0,

      //弹框统一
      dialogBox: false,
      title: '',
      //弹框统一表字段
      Header: [],
      //弹框统一表数据
      formData: [],
      //上传Excel
      fileList: [],
      //弹框备注
      textareaCopy: ''
    }
  },
  watch: {
    selTime: function() {
      this.getFocusedType()
      this.getFocusedOrders()
      this.getProductQuality()
      this.getYieldData()
      this.getUnplannedData()
    }
  },

  created() {
    this.getFocusedType()
    this.getFocusedOrders()
    this.getProductQuality()
    this.getYieldData()
    this.getUnplannedData()
  },

  methods: {
    //重点类别跟踪情况vs重点订单跟踪情况
    selTab(item, index) {
      this.tabList.forEach(item => {
        item.active = false
      })
      item.active = true
      this.active = index
      if (this.active == 0) {
        this.getFocusedType()
      } else {
        this.getFocusedOrders()
      }
    },

    //获取重点类别跟踪情况
    async getFocusedType() {
      let res = await post(FOCUSEDTYPEDATA, {
        setTime: this.selTime
      })
      // console.log('重点类别跟踪情况', res)
      if (res.data) {
        this.focusedType = res.data
      }
    },

    //获取胜代订单生产情况
    async getFocusedOrders() {
      let res = await post(FOCUSEDORDERSDATA, {
        setTime: this.selTime
      })
      // console.log('重点订单跟踪情况', res)
      if (res.data) {
        this.OrdersText = res.wholeRemark ? res.wholeRemark : null
        res.data.forEach(item => {
          if (item.fileId && item.fileId != '') {
            item.imgFile = item.fileId.split('|')
          }
        })
        this.focusedOrders = res.data
      }
    },

    //附件查看
    viewFile(item) {
      this.$refs.previewImg.showViewer = true
      this.srcList = []
      this.srcList.push(item)
    },

    //上传文件
    async uploadFileData(val) {
      this.imgLoading = true
      let fileUrl

      let formData = new FormData()
      formData.append('file', val.file)

      let res = await post(minio_upload, formData)

      if (res) {
        let index = res.indexOf('?')
        if (index !== -1) {
          fileUrl = res.substring(0, index)
        } else {
          fileUrl = res
        }
        //清空文件
        this.fileImgList = []

        this.focusedOrders.forEach(item => {
          if (item.id == val.data.id && item.fileId && item.fileId != '') {
            fileUrl = fileUrl + '|' + item.fileId
          }
        })

        //上传文件数据给后台
        setTimeout(async () => {
          if (fileUrl != '') {
            let res1 = await post(FOCUSEDORDERSUPDATA, {
              id: val.data.id,
              fileId: fileUrl
            })
            // console.log('数据上传', res1)
            if (res1.status == 1) {
              this.imgLoading = false
              this.getFocusedOrders()
              this.$message.success('上传成功!')
            }
          }
        }, 1000)
      }
    },

    //删除附件
    async delFile(row, id) {
      this.$confirm('此操作会删除此附件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let Arr = []
        let fileUrl = ''
        this.focusedOrders.forEach(item => {
          if (item.id == id) {
            let index = item.imgFile.indexOf(row)
            item.imgFile.splice(index, 1)
            Arr = item.imgFile
          }
        })

        if (Arr.length > 1) {
          Arr.forEach(item => {
            fileUrl += item + '|'
          })
          fileUrl = fileUrl.substring(0, fileUrl.length - 1)
        } else {
          fileUrl = Arr[0] ? Arr[0] : ''
        }

        setTimeout(async () => {
          let res = await post(FOCUSEDORDERSUPDATA, {
            id: id,
            fileId: fileUrl
          })
          // console.log('删除', res)
          if (res.status == 1) {
            this.getFocusedOrders()
            this.$message.success('删除成功!')
          }
        }, 1000)
      })
    },

    //上传了的文件给移除的事件
    handleRemove() {},
    //超出文件个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      })
      return
    },

    //上传成功后的回调
    handleSuccess() {},
    //上传文件之前
    beforeUpload(file) {
      if (file.type != '' || file.type != null || file.type != undefined) {
        //截取文件的后缀，判断文件类型
        const FileExt = file.name.replace(/.+\./, '').toLowerCase()
        //计算文件的大小
        const isLt5M = file.size / 1024 / 1024 < 500 //这里做文件大小限制
        let fileType = ['pdf', 'png', 'jpg'] //设置文件类型
        // 如果大于50M
        if (!isLt5M) {
          this.$message('上传文件大小不能超过 500MB!')
          return false
        }
        // 如果文件类型不在允许上传的范围内
        if (fileType.includes(FileExt)) {
          return true
        } else {
          this.$message.error('上传文件格式不正确!')
          return false
        }
      }
    },

    //产品质量跟踪
    async getProductQuality() {
      let res = await post(PRODUCTQUALITYDATA, {
        setTime: this.selTime
      })
      // console.log('产品质量跟踪', res)

      if (res.data) {
        this.productQuality = res.data
      }
    },

    //成材率
    async getYieldData() {
      let res = await post(YIELDDATA, {
        setTime: this.selTime
      })
      // console.log('成材率(待定)', res)

      if (res.data) {
        this.yieldData = res.data
      }
    },

    //非计划数据统计
    async getUnplannedData() {
      let res = await post(UNPLANNEDDATA, {
        setTime: this.selTime
      })
      console.log('非计划数据统计', res)

      if (res.data) {
        this.unplannedData = res.data

        this.numberOfBlocks1 = res.data1[0].numberOfBlocks
        this.numberOfBlocks2 = res.data1[1].numberOfBlocks
        this.wgt1 = res.data1[0].wgt
        this.wgt2 = res.data1[1].wgt
        this.rate1 = res.data1[0].rate
        this.rate2 = res.data1[1].rate
      }
    },

    //弹框
    openView(nub, tab) {
      this.dialogBox = true
      if (nub == 1 && tab == 0) {
        this.title = '重点类别跟踪情况'
        this.Header = [
          {
            label: '类别',
            prop: 'category'
          },
          {
            label: '主要钢种',
            prop: 'steel'
          },
          {
            label: '跟踪内容',
            prop: 'content'
          },
          {
            label: '生产量(轧制块/吨)',
            prop: 'wgt',
            disabled: true
          },
          {
            label: '生产班别',
            prop: 'classes',
            disabled: true
          },
          {
            label: '异常描述',
            prop: 'abnormalDescription',
            disabled: true
          },
          {
            label: '备注',
            prop: 'remark'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.focusedType))
      } else if (nub == 1 && tab == 1) {
        this.title = '胜代订单生产情况'
        this.Header = [
          {
            label: '班别',
            prop: 'classes',
            disabled: true
          },
          {
            label: '生产量(块/吨)',
            prop: 'rolling',
            disabled: true
          },
          {
            label: '坯料跟踪问题',
            prop: 'trackingProblem'
          },
          {
            label: '轧钢质量跟踪',
            prop: 'qualityTracking'
          },
          {
            label: '异常描述',
            prop: 'abnormalDescription',
            disabled: true
          },
          {
            label: '备注',
            prop: 'remark'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.focusedOrders))
        this.textareaCopy = JSON.parse(JSON.stringify(this.OrdersText))
      } else if (nub == 2 && tab == '') {
        this.title = '产品质量跟踪'
        this.Header = [
          {
            label: '大夜班',
            prop: 'graveyardShift'
          },
          {
            label: '白班',
            prop: 'dayShift'
          },
          {
            label: '小夜班',
            prop: 'nightShift'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.productQuality))
      } else if (nub == 3 && tab == '') {
        this.title = '成材率(待定)'
        this.Header = [
          {
            label: '班次',
            prop: 'classes',
            disabled: true
          },
          {
            label: '班别',
            prop: 'classes1',
            disabled: true
          },
          {
            label: '目标成材率(%)',
            prop: 'target',
            disabled: true
          },
          {
            label: '实际成材率(%)',
            prop: 'actual',
            disabled: true
          },
          {
            label: '情况说明',
            prop: 'situationDescription'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.yieldData))
      } else if (nub == 4 && tab == '') {
        this.title = '非计划数据统计'
        this.Header = [
          {
            label: '分类',
            prop: 'classify'
          },
          {
            label: '块数',
            prop: 'numberOfBlocks'
          },
          {
            label: '重量',
            prop: 'wgt'
          },
          {
            label: '实绩',
            prop: 'realValue'
          },
          {
            label: '计划',
            prop: 'planValue'
          },
          {
            label: '是否完成',
            prop: 'isComplete'
          },
          {
            label: '备注',
            prop: 'remark'
          },
          {
            label: '降级原因',
            prop: 'reason'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.unplannedData))
      }
    },

    //添加行
    addNewRow() {
      let row = {}
      this.Header.forEach(item => {
        row[item.prop] = ''
      })

      this.formData.push(row)
    },

    //删除行
    delRow(indexs) {
      this.formData.forEach((item, index) => {
        if (indexs == index) {
          this.formData.splice(index, 1)
        }
      })
    },

    //下载模板
    DownloadExcel() {
      let data = [{}]
      this.Header.forEach(item => {
        data[0][item.prop] = item.label
      })

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `${this.title}.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          `${this.title}.xlsx`
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      let header = {}
      let letter = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      this.Header.forEach((item, index) => {
        header[item.prop] = letter[index]
      })

      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, header)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        console.log(list)

        list.forEach(item => {
          item.selTime = this.selTime
          this.formData.push(item)
        })
      })
    },

    //保存新增数据
    async saveData() {
      let res
      if (this.title == '重点类别跟踪情况') {
        res = await post(FOCUSEDTYPESAVE, {
          setTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '胜代订单生产情况') {
        res = await post(FOCUSEDORDERSSAVE, {
          setTime: this.selTime,
          wholeRemark: this.textareaCopy,
          data: this.formData
        })
      } else if (this.title == '产品质量跟踪') {
        res = await post(PRODUCTQUALITYSAVE, {
          setTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '成材率(待定)') {
        res = await post(YIELDSAVE, {
          setTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '非计划数据统计') {
        res = await post(UNPLANNEDSAVE, {
          setTime: this.selTime,
          data: this.formData
        })
      }

      // console.log('保存', res)
      if (res.status == 1) {
        this.$message.success('保存成功')
        if (this.title == '重点类别跟踪情况') {
          this.getFocusedType()
        } else if (this.title == '胜代订单生产情况') {
          this.getFocusedOrders()
        } else if (this.title == '产品质量跟踪') {
          this.getProductQuality()
        } else if (this.title == '成材率(待定)') {
          this.getYieldData()
        } else if (this.title == '非计划数据统计') {
          this.getUnplannedData()
        }

        this.closeDialogBox()
      }
    },

    //关闭弹框
    closeDialogBox() {
      this.dialogBox = false
      this.title = ''
      this.Header = []
      this.formData = []
      this.textareaCopy = ''
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .tabBox {
      display: flex;
      .tab {
        color: #ffffffbf;
        margin-right: 20px;
      }
      .tab_block {
        display: flex;
        flex-direction: column;
        position: relative;
        .tab_img {
          .tab_img2 {
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
          }
          .tab_img1 {
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
            margin-bottom: 7px;
          }
        }
      }
    }
    .border-content {
      height: 380px;
    }
  }
  .EchartsBox {
    height: 380px;
    .setRadio {
      /deep/.el-radio {
        color: white;
      }
    }
  }
  .border-wrapper {
    margin-bottom: 15px;
  }
  /deep/.el-textarea__inner {
    background-color: #041a21;
    border: 1px solid #1fc6ff;
    color: white;
    font-size: 14px;
    height: 70px;
  }
}

//图片上传显示样式
.tasksList {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
  .imgView {
    display: flex;
    overflow: auto;
    > div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #ccc;
      margin: 0 2px 2px 0;
      text-align: center;
      padding: 0 2px;
      cursor: pointer;
      img {
        width: 40px;
        height: 40px;
        margin-right: 5px;
      }
    }
  }
}

.textarea {
  margin-bottom: 10px;
}
//非计划数据统计
.disData {
  display: flex;
  margin-bottom: 15px;
  .block {
    border: 1px solid #1fc6ff;
    width: 220px;
    color: white;
    padding: 2px 10px;
    margin-right: 20px;
    .title {
      font-size: 14px;
    }
    .context {
      i {
        display: inline-block;
        width: 6px;
        height: 16px;
        background: #19be6b;
      }
      span {
        font-size: 20px;
        display: inline-block;
        margin: 5px 5px;
      }
    }
  }
}

.btn {
  /deep/.el-button {
    font-size: 15px;
    padding: 4px 15px;
    border-radius: 4px;
  }
}

/deep/ .el-table .el-table__cell {
  font-size: 19px;
}

/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}
</style>
