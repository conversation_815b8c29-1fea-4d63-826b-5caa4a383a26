<template>
  <div class="container"> 
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="工艺执行情况">
          <custom-table
            ref="departmentAuditRef" 
            :show-table="true"
            :show-edit="false"
            :key="'departmentAudit'"
            :setting="tableObj.setting"
            :url-list="tableObj.url.list"
            :url-save="tableObj.url.save"
            :select-date="selectDate"
            title="工艺执行情况"
          />
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="上周快停情况">
          <custom-table
            ref="acceptQualityDisputeRef" 
            :show-table="true"
            :show-edit="true"
            :key="'acceptQualityDispute'"
            :setting="tableObj2.setting"
            :url-list="tableObj2.url.list"
            :url-save="tableObj2.url.save"
            :select-date="selectDate"
            title="上周快停情况"
          />
        </screen-border>
      </div>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityWeeklyReportScreen/components/screen-border.vue'
import CustomTable from '@/pages/screen/qualityWeeklyReportScreen/components/custom-table.vue'

export default {
  name: 'QualityNotification',
  components: {
    CustomTable,
    ScreenBorder
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      monthlyReport: '',
      tableObj: {
        setting: [
          // {
          //   keyQuery: 'index',
          //   keySave: 'index',
          //   label: '序号',
          //   type: 'index'
          // },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '缺陷类型',
            width: '400'
          },
          {
            keyQuery: 'temperature',
            keySave: 'temperature',
            label: '温度项',
            width: '400'
          },
          {
            keyQuery: 'abnormalLevel',
            keySave: 'abnormalLevel',
            label: '异常级别',
            width: '350'
          },
          {
            keyQuery: 'range',
            keySave: 'range',
            label: '范围',
            width: '350'
          },
          {
            keyQuery: 'number',
            keySave: 'number',
            label: '数量',
            width: '350'
          }
        ],
        url: {
          list: '',
          save: ''
        }
      },
      tableObj2: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'slab',
            keySave: 'slab',
            label: '板坯号',
            width: '200'
          },
          {
            keyQuery: 'rollingTime',
            keySave: 'rollingTime',
            label: '轧制时间',
            width: '200'
          },
          {
            keyQuery: 'steelType',
            keySave: 'steelType',
            label: '钢种',
            width: '200'
          },
          {
            keyQuery: 'orderThickness',
            keySave: 'orderThickness',
            label: '订单厚度',
            width: '200'
          },
          {
            keyQuery: 'slabWidth',
            keySave: 'slabWidth',
            label: '坯料宽度',
            width: '200'
          },
          {
            keyQuery: 'downgradeTon',
            keySave: 'downgradeTon',
            label: '降级吨位',
            width: '250'
          },
          {
            keyQuery: 'responsibleUnit',
            keySave: 'responsibleUnit',
            label: '责任单位',
            width: '250'
          },
          {
            keyQuery: 'assessment',
            keySave: 'assessment',
            label: '考核',
            width: '230'
          }
        ],
        url: {
          list: '',
          save: ''
        }
      }
    }
  },
  methods: {}
}
</script>

<style scoped lang="less">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: hidden;

  .chart-row {
    margin-bottom: 10px;
    height: 100%;
    flex-direction: column;
  }

  .chart-row,
  .table-row {
    display: flex;
    gap: 10px;
    width: 100%;
  }

  .chart-box,
  .table-box {
    flex: 1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .chart-footer {
    margin: 0px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    padding: 5px 8px;
    background: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);
    width: 100%;
  }

  .chart-input {
    height: 100%;
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      height: 100%;
      padding: 0;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
        border-color: transparent !important;
      }
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      border-color: transparent !important;
    }
  }

  /deep/ .el-table {
    background-color: transparent;
    width: 100% !important;
    table-layout: fixed;

    &::before {
      display: none;
    }

    th {
      background-color: rgba(31, 198, 255, 0.3);
      color: #fff;
      border-color: #1fc6ff;
    }

    td {
      background-color: transparent;
      color: #fff;
      border-color: #2e4262;
    }

    tr {
      background-color: transparent;
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-content {
    padding: 10px;
    box-sizing: border-box;
  }
}
</style>
