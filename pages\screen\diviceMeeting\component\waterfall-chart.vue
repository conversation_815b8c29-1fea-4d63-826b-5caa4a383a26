<template>
  <div 
    :id="containerId" 
    :style="{ height: '100%' }"/>
</template>

<script>
export default {
  name: 'bars-chart',
  props: {
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    chartData2: {
      type: Array,
      default: () => {
        return []
      }
    },
    chartLen: {
      type: Number,
      default: 0
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 0
    },
    unit: {
      type: String,
      default: '吨'
    },
    min: {
      type: String,
      default: ''
    },
    max: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
    window.addEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
      }
      // echart配置
      var option = {
        color: '#0A8BFF',
        title: {
          show: false
        },
        tooltip: {
          confine: true,
          //alwaysShowContent:true,
          hideDelay: 100,
          borderRadius: 5, //边框圆角
          padding: 5, // [5, 10, 15, 20] 内边距
          borderColor: '#1fc6ff',
          borderWidth: 1,
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: function(params) {
            return `（${params.data.value[3]}）${params.data.value[4]}`
          }
        },
        legend: {
          //图例
          show: false
        },
        grid: {
          //绘图网格
          left: '0%',
          right: '50%',
          top: '5%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'time',
          position: 'bottom',
          interval: 3600 * 24 * 1000, //以一个天递增
          maxInterval: 3600 * 24 * 1000, //以一个天递增
          max: `${this.max} 24:00:00`,
          min: `${this.min} 00:00:00`, //将data里最小时间的整点时间设为min,否则min会以data里面的min为开始进行整点递增
          axisLabel: {
            textStyle: {
              color: 'rgba(255,255,255,.8)', //更改坐标轴文字颜色
              fontSize: 12 //更改坐标轴文字大小
            },
            formatter: (value, index) => {
              return this.$moment(value).format('MM/DD')
            }
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(0,0,0,0.1)'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#2e4262'
            }
          }
        },
        yAxis: {
          axisLine: {
            lineStyle: {
              color: 'rgba(0,0,0,0.1)'
            }
          },
          data: this.xData,
          axisLabel: {
            textStyle: {
              color: 'rgba(255,255,255,.8)', //更改坐标轴文字颜色
              fontSize: 14 //更改坐标轴文字大小
            },
            rich: {
              a: {
                color: 'rgba(24, 144, 255, 0.7)',
                fontSize: 12
              },
              b: {
                color: 'red',
                fontSize: 14
              }
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#2e4262'
            }
          }
        },
        series: [
          // 用空bar来显示三个图例
          {
            type: 'custom',
            renderItem: (params, api) => {
              //开发者自定义的图形元素渲染逻辑，是通过书写 renderItem 函数实现的
              var categoryIndex = api.value(0) //这里使用 api.value(0) 取出当前 dataItem 中第一个维度的数值。
              var start = api.coord([api.value(1), categoryIndex]) // 这里使用 api.coord(...) 将数值在当前坐标系中转换成为屏幕上的点的像素值。
              var end = api.coord([api.value(2), categoryIndex])
              // var height = api.size([0, 1])[1];
              var height = 25
              return {
                type: 'rect', // 表示这个图形元素是矩形。还可以是 'circle', 'sector', 'polygon' 等等。
                shape: this.$echarts.graphic.clipRectByRect(
                  {
                    // 矩形的位置和大小。
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height
                  },
                  {
                    // 当前坐标系的包围盒。
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height
                  }
                ),
                textContent: {
                  type: 'text',
                  name: api.value(0),
                  info: api.value(0),
                  style: {
                    // text: api.value(0),
                    fill: '#fff'
                  }
                },
                textConfig: {
                  position: 'top'
                },
                style: api.style()
              }
            },
            encode: {
              x: [1, 2], // data 中『维度1』和『维度2』对应到 X 轴
              y: 0 // data 中『维度0』对应到 Y 轴
            },
            itemStyle: {
              normal: {
                color: function(params) {
                  return '#0A8BFF'
                }
              }
            },
            data: this.chartData
          },
          {
            type: 'custom',
            renderItem: (params, api) => {
              console.log(api.value(0))
              //开发者自定义的图形元素渲染逻辑，是通过书写 renderItem 函数实现的
              var categoryIndex = api.value(0) //这里使用 api.value(0) 取出当前 dataItem 中第一个维度的数值。
              var start = api.coord([api.value(1), categoryIndex]) // 这里使用 api.coord(...) 将数值在当前坐标系中转换成为屏幕上的点的像素值。
              var end = api.coord([api.value(2), categoryIndex])
              // var height = api.size([0, 1])[1];
              var height = 30
              return {
                type: 'rect', // 表示这个图形元素是矩形。还可以是 'circle', 'sector', 'polygon' 等等。
                shape: this.$echarts.graphic.clipRectByRect(
                  {
                    // 矩形的位置和大小。
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height
                  },
                  {
                    // 当前坐标系的包围盒。
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height
                  }
                ),
                textContent: {
                  type: 'text',
                  name: api.value(0),
                  info: api.value(0),
                  style: {
                    text: api.value(3),
                    fill: 'rgba(255,255,255,.8)',
                    fontSize: 14 //更改坐标轴文字大小
                  }
                },
                textConfig: {
                  position: 'right'
                },
                style: api.style()
              }
            },
            encode: {
              x: [1, 2], // data 中『维度1』和『维度2』对应到 X 轴
              y: 0 // data 中『维度0』对应到 Y 轴
            },
            tooltip: {
              formatter: params => {
                console.log(params)
                return params.value[3].split('；').join('<br>')
              }
            },
            itemStyle: {
              normal: {
                color: function(params) {
                  return '#0A8BFF'
                }
              }
            },
            data: this.chartData2
          }
        ]
      }
      this.myChart.setOption(option)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
