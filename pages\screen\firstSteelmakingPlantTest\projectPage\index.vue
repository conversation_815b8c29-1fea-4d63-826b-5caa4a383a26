<!--三重订单工艺异常-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border title="三重订单工艺异常">
                <template v-slot:headerRight>
                  <el-row>
                    <span
                      v-command="'/screen/firstSteelmakingPlant/edit'"
                      class="screen-btn"
                      @click="clickAddProject">
                      <el-icon class="el-icon-edit-outline"/>
                      新增
                    </span>
                  </el-row>

                </template>
                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="ProjectData.loading"
                    :data="ProjectData.showGridData"
                    :row-class-name="rowClassName"
                    border>
                    <el-table-column
                      show-overflow-tooltip
                      width="60"
                      align="center"
                      label="序号">
                      <template slot-scope="scope">
                        <div>{{ scope.$index+1 }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="日期"
                      align="center"
                      width="130">
                      <template slot-scope="scope">
                        <div>{{ scope.row.PROD_DATE }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="异常描述">
                      <template slot-scope="scope">
                        <div>{{ scope.row.PROBLEM }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="责任单位"
                      align="center"
                      width="145">
                      <template slot-scope="scope">
                        <div>{{ scope.row.FACTORY }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="责任人"
                      align="center"
                      width="145">
                      <template slot-scope="scope">
                        <div>{{ scope.row.RESPONSIBLE_PERSON }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="文档附件"
                      align="center"
                      width="100">
                      <template slot-scope="scope">
                        <span
                          v-if="scope.row.FILE_ATTACHMENTS != undefined"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="goOut(scope.row.FILE_ATTACHMENTS, true)">附件</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="150"
                      label="操作">
                      <template v-slot="scope">
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectItem(scope.row)">编辑</span>
                        <span
                          v-command="'/screen/firstSteelmakingPlant/edit'"
                          style="cursor: pointer;color: rgba(255,0,0,0.83);text-decoration: underline"
                          @click="clickProjectDeleteItem(scope.row)">删除</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--三重订单工艺异常新增修改-->
    <el-dialog
      v-loading="ProjectData.loading"
      :visible.sync="ProjectData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="三重订单工艺异常">
      <template v-slot:title>
        <div class="custom-dialog-title">
          三重订单工艺异常
        </div>
      </template>
      <div
        :style="{height: '520px'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">日期</div>
          <el-date-picker
            v-model="projectItem.PROD_DATE"
            :clearable="false"
            :size="'mini'"
            :value-format="'yyyy-MM-dd'"
            class="screen-input"/>
        </div>

        <div class="dialog-cell">
          <div class="dialog-cell-title">异常描述</div>
          <el-input
            v-model="projectItem.PROBLEM"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">责任单位</div>
          <el-input
            v-model="projectItem.FACTORY"
            :rows="3"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">责任人</div>
          <el-input
            v-model="projectItem.RESPONSIBLE_PERSON"
            :rows="3"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">文件附件</div>
          <div>
            <el-upload
              v-if="canEditQuality"
              ref="upload"
              :auto-upload="false"
              :show-file-list="false"
              :http-request="httpRequest"
              :on-change="
                (file) =>
                  handleChange(file)
              "
              multiple
              action="#"
              style="display: inline"
            >
              <el-button
                slot="trigger"
                :disabled="fileUrl.length > 0 || projectItem.FILE_ATTACHMENTS!=undefined"
                size="small"
                type="primary">选取文件</el-button>
            </el-upload>
            <div
              v-if="fileUrl.length > 0"
              class="upfile">
              <span
                class="open-btn"
                @click="goOut(fileUrl,false)">点击打开附件</span>
              <span
                class="close-btn"
                @click="delFile(fileUrl,false)">x</span>
            </div>
            <div
              v-if="projectItem.FILE_ATTACHMENTS!=undefined"
              class="upfile">
              <span
                class="open-btn"
                @click="goOut(projectItem.FILE_ATTACHMENTS,true)">点击打开附件</span>
              <span
                class="close-btn"
                @click="delFile(projectItem.FILE_ATTACHMENTS,true)">x</span>
            </div>
          </div>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          v-command="'/screen/firstSteelmakingPlant/edit'"
          class="screen-btn"
          @click="addProjectData"
        >
          确定
        </span>
      </div>
    </el-dialog>

    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="上月导入日期库存">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
// api板卷接口
import { firstMorningMeeting } from '@/api/screen'
import moment from 'moment'
export default {
  name: 'ProjectPage',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      ProjectData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      projectItem: {},
      file: null,
      fileUrl: '',
      delURL: ''
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getProjectDataInit([
        this.$moment(this.cDate)
          .subtract(30, 'days')
          .format('YYYY-MM-DD'),
        this.$moment(this.cDate)
          .add(1, 'days')
          .format('YYYY-MM-DD')
      ])
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    // 跳转外部链接
    goOut(url, isAfter) {
      if (isAfter) {
        window.open('http://172.25.63.188:9084/orgApi2/boardUploadfiles/' + url)
      } else {
        window.open(url)
      }
    },
    // 删除文件
    delFile(url, boolean) {
      if (boolean) {
        this.delURL = url
        this.projectItem.FILE_ATTACHMENTS = undefined
      } else {
        this.fileUrl = ''
        this.file = null
      }
    },
    rowClassName({ row, rowIndex }) {
      if (row.COMPLETION_STATUS === '未完成') {
        return 'class_red'
      } else if (
        row.COMPLETION_STATUS === '短期进行中' ||
        row.COMPLETION_STATUS === '进行中'
      ) {
        return 'class_yellow'
      } else if (row.COMPLETION_STATUS === '长期进行中') {
        return 'class_orange'
      } else {
        return ''
      }
    },
    //督办点击删除
    clickAddProject() {
      this.projectItem = _.cloneDeep({
        // 日期
        PROD_DATE: moment().format('yyyy-MM-DD')
      })
      this.ProjectData.dialogVisible = true
    },
    //督办点击查看详情
    clickProjectItem(row) {
      this.projectItem = _.cloneDeep(row)
      this.fileUrl = ''
      this.file = null
      this.delURL = ''
      this.ProjectData.dialogVisible = true
    },
    calculateHeight() {
      this.ProjectData.maxHeight = this.$refs.table1.offsetHeight
    },
    // 数据查询
    getProjectDataInit(date) {
      this.ProjectData.loading = true
      post(firstMorningMeeting.processInspectionInit, {
        startTime: date[0],
        endTime: date[1]
      })
        .then(res => {
          const data = _.cloneDeep(res.data)
          this.ProjectData.gridData = data
          this.ProjectData.showGridData = data
          this.ProjectData.loading = false
        })
        .catch(err => {
          this.ProjectData.loading = false
        })
    },
    // 删除文件
    delImgs(delImg) {
      this.$message.info('正在删除文件请勿操作！！！')
      if (delImg.length) {
        // 文件删除
        post(firstMorningMeeting.fileDelete, delImg)
          .then(res => {
            if (res.code === 200) {
              this.saveData()
            } else {
              this.$message.error('文件删除失败！')
            }
          })
          .catch(err => {
            this.$message.error('文件删除失败！')
          })
      }
    },
    // 数据保存
    projectData() {
      post(firstMorningMeeting.processInspectionUpdate, this.projectItem)
        .then(res => {
          this.getProjectDataInit([
            this.$moment(this.cDate)
              .subtract(30, 'days')
              .format('YYYY-MM-DD'),
            this.$moment(this.cDate)
              .add(1, 'days')
              .format('YYYY-MM-DD')
          ])
          this.ProjectData.dialogVisible = false
          this.ProjectData.loading = false
        })
        .catch(err => {
          this.ProjectData.loading = false
        })
    },
    saveData() {
      if (this.file != null) {
        this.$message.info('文件上传中...')
        const fd = new FormData()
        fd.append('file', this.file)
        post(firstMorningMeeting.fileUpdata, fd)
          .then(res => {
            if (res.code == 200) {
              this.$message.success('文件上传成功,开始保存数据')
              this.projectItem.FILE_ATTACHMENTS = res.data[0].data
              this.projectData()
            } else {
              this.$message.error('文件上传失败')
            }
          })
          .catch(err => {
            this.$message.error('文件上传失败')
          })
      } else {
        this.projectData()
      }
    },
    // 数据新增
    addProjectData() {
      let setDate = this.projectItem.PROD_DATE
      if (setDate === null || setDate.length === 0) {
        this.$message.warning('请选择日期！')
        return
      }
      this.ProjectData.loading = true
      if (this.delURL.length) {
        this.delImgs([this.delURL])
      } else {
        this.saveData()
      }
    },
    //督办点击删除
    clickProjectDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          post(firstMorningMeeting.processInspectionDelete(row.ID))
            .then(res => {
              if (row.FILE_ATTACHMENTS) {
                post(firstMorningMeeting.fileDelete, [row.FILE_ATTACHMENTS])
              }
              this.getProjectDataInit([
                this.$moment(this.cDate)
                  .subtract(30, 'days')
                  .format('YYYY-MM-DD'),
                this.$moment(this.cDate)
                  .add(1, 'days')
                  .format('YYYY-MM-DD')
              ])
            })
            .catch(err => {
              this.getProjectDataInit([
                this.$moment(this.cDate)
                  .subtract(30, 'days')
                  .format('YYYY-MM-DD'),
                this.$moment(this.cDate)
                  .add(1, 'days')
                  .format('YYYY-MM-DD')
              ])
            })
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    // 去除上传默认事件
    httpRequest(params) {},
    handleChange(file) {
      console.log(file)
      this.file = file.raw
      // 将文档类型转为url
      this.fileUrl = URL.createObjectURL(file.raw)
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
/deep/ .el-table .class_red {
  background: #fd0000;
  color: black;
}
/deep/ .el-table .class_yellow {
  background: #fdfd00;
  color: black;
}
/deep/ .el-table .class_orange {
  background: #f99f04;
  color: black;
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.upfile {
  width: 200px;
  height: 25px;
  display: flex;
  align-items: center;
  background-color: rgba(245, 247, 250, 0.2);
  color: #fff;
  justify-content: space-between;
  border-radius: 5px;
  margin-top: 10px;
  .open-btn {
    width: 175px;
    height: 25px;
    text-align: center;
    line-height: 25px;
    border-radius: 5px;
    cursor: pointer;
    &:hover {
      background-color: rgba(245, 247, 250, 0.4);
    }
  }

  .close-btn {
    width: 25px;
    height: 25px;
    text-align: center;
    line-height: 25px;
    border-radius: 5px;
    cursor: pointer;
    &:hover {
      background-color: rgba(245, 247, 250, 0.4);
      color: rgba(255, 0, 0, 0.784);
    }
  }
}
</style>
