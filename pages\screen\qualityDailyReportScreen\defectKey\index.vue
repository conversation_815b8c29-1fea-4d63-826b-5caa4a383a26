<template>
  <div class="container">
    <div class="chart-row">
      <!-- 缺陷汇总图表 -->
      <div class="chart-box">
        <screen-border title="缺陷汇总">
          <!-- <template v-slot:headerRight>
            <span
              v-command="'/screen/qualityDailyReportScreen/edit'"
              class="screen-btn"
              @click="handleDialogOperationVisible('process')"
            >
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template> -->
          <custom-table-type
            :show-table="true"
            :show-edit="false"
            :key="'defectSummary'"
            :title="'缺陷汇总'"
            :setting="tableObj.setting"
            :url-list="tableObj.url.list"
            :url-save="tableObj.url.save"
            :select-date="selectDate"
          />
        </screen-border>
      </div>

      <!-- 在制品统计 -->
      <div class="chart-box">
        <screen-border title="在制品统计">
          <!-- <template v-slot:headerRight>
            <span
              v-command="'/screen/qualityDailyReportScreen/edit'"
              class="screen-btn"
              @click="handleDialogOperationVisible"
            >
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template> -->
          <custom-table
            :show-table="true"
            :show-edit="true"
            :key="'workInProcess'"
            :title="'在制品统计'"
            :setting="tableObjTwo.setting"
            :url-list="tableObjTwo.url.list"
            :url-save="tableObjTwo.url.save"
            :select-date="selectDate"
          />
        </screen-border>
      </div>
    </div>

    <div class="table-row">
      <!-- 在线重点品种表格 -->
      <div class="table-box">
        <screen-border title="在线重点品种">
          <!-- <template v-slot:headerRight>
            <span
              v-command="'/screen/qualityDailyReportScreen/edit'"
              class="screen-btn"
              @click="handleDialogOperationVisible('online')"
            >
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template> -->
          <custom-table
            :show-table="true"
            :show-edit="false"
            :key="'onlineKeyVariety'"
            :title="'在线重点品种'"
            :setting="tableObjThree.setting"
            :url-list="tableObjThree.url.list"
            :url-save="tableObjThree.url.save"
            :select-date="selectDate"
          />
        </screen-border>
      </div>

      <!-- 热处理重点品种表格 -->
      <div class="table-box">
        <screen-border title="热处理重点品种">
          <custom-table
            ref="dialogOperationFour"
            :show-table="true"
            :show-edit="false"
            :key="'dialogOperationFour'"
            :title="'热处理重点品种'"
            :setting="tableObjFour.setting"
            :url-list="tableObjFour.url.list"
            :url-save="tableObjFour.url.save"
            :select-date="selectDate"
            width="54%"
            class="custom-table-origin"
          />
        </screen-border>
      </div>
    </div>
  </div>
</template>

<script>
import BarsChart from '@/pages/screen/qualityDailyReportScreen/components/bars-chart.vue'
import ScreenBorder from '@/pages/screen/qualityDailyReportScreen/components/screen-border.vue'
import CustomTable from '@/pages/screen/qualityDailyReportScreen/components/custom-table.vue'
import CustomTableType from '@/pages/screen/qualityDailyReportScreen/components/custom-table-type.vue'

import {
  findWorkInProgressByDate,
  heatTreatmentKeyVariety,
  findAllHeatTreatmentKeyVarietiesDate,
  heatTreatmentKeyVarietySave,
  findAllOnlineKeyVarietiesDate,
  findDefectSummaryByDate
} from '@/api/screen'
import { post } from '@/lib/Util'

export default {
  name: 'DefectKey',
  components: {
    BarsChart,
    ScreenBorder,
    CustomTable,
    CustomTableType
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableObj: {
        url: {
          list: findDefectSummaryByDate,
          save: ''
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'TYPE',
            keySave: 'TYPE',
            label: '类别',
            width: '150'
          },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '缺陷',
            width: '200'
          },
          {
            keyQuery: 'UNIT',
            keySave: 'UNIT',
            label: '责任单位',
            width: '180'
          },
          {
            keyQuery: 'WGT',
            keySave: 'WGT',
            label: '吨位',
            width: '150'
          },
          {
            keyQuery: 'TOP_3_STEEL_GRADES',
            keySave: 'TOP_3_STEEL_GRADES',
            label: '钢种',
            width: '250'
          },
          {
            keyQuery: 'thk1_range',
            keySave: 'thk1_range',
            label: '规格',
            width: '180'
          }
        ]
      },
      tableObjTwo: {
        url: {
          list: findWorkInProgressByDate,
          save: ''
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index',
            width: '80'
          },
          {
            keyQuery: 'type',
            keySave: 'type',
            label: '待判类别',
            width: '150'
          },
          {
            keyQuery: 'area',
            keySave: 'area',
            label: '分布区域',
            width: '270'
          },
          {
            keyQuery: 'wgt',
            keySave: 'wgt',
            label: '吨位',
            width: '120'
          },
          {
            keyQuery: 'remark',
            keySave: 'remark',
            label: '备注',
            width: '300'
          }
        ]
      },

      tableObjThree: {
        url: {
          list: findAllOnlineKeyVarietiesDate,
          save: ''
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index',
            width: '80'
          },
          {
            keyQuery: 'mill_stdspec',
            keySave: 'mill_stdspec',
            label: '品种',
            width: '250'
          },
          {
            keyQuery: 'thk1_range',
            keySave: 'thk1_range',
            label: '规格/mm',
            width: '140'
          },
          {
            keyQuery: 'non_planned_count',
            keySave: 'non_planned_count',
            label: '非计划',
            width: '100'
          },
          {
            keyQuery: 'pending_count',
            keySave: 'pending_count',
            label: '待判',
            width: '100'
          },
          {
            keyQuery: 'total_count',
            keySave: 'total_count',
            label: '合计',
            width: '110'
          },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '主要缺陷',
            width: '500'
          }
        ]
      },

      tableObjFour: {
        url: {
          list: findAllHeatTreatmentKeyVarietiesDate,
          save: heatTreatmentKeyVarietySave
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index',
            width: '80'
          },
          {
            keyQuery: 'mill_stdspec',
            keySave: 'mill_stdspec',
            label: '品种',
            width: '250'
          },
          {
            keyQuery: 'thk1_range',
            keySave: 'thk1_range',
            label: '规格',
            width: '100'
          },
          {
            keyQuery: 'non_planned_count',
            keySave: 'non_planned_count',
            label: '非计划',
            width: '110'
          },
          {
            keyQuery: 'pending_count',
            keySave: 'pending_count',
            label: '待判',
            width: '200'
          },
          {
            keyQuery: 'total_count',
            keySave: 'total_count',
            label: '合计',
            width: '110'
          },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '主要缺陷',
            width: '500'
          }
        ]
      },

      // ERP申诉数据
      erpAppealData: [],
      erpAppealXData: []
    }
  },
  watch: {
    selectDate: {
      handler() {
        console.log(this.selectDate)
      },
      immediate: true
    }
  },
  methods: {
    handleDialogOperationVisible() {
      this.$refs.dialogOperationFour.openDialog()
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: hidden;

  .chart-row {
    margin-bottom: 10px;
  }

  .chart-row,
  .table-row {
    display: flex;
    gap: 10px;
    height: 50%;
    width: 100%;
  }

  .chart-box,
  .table-box {
    flex: 1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .chart-footer {
    margin: 0px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    padding: 5px 8px;
    background: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);
    width: 100%;
  }

  .chart-input {
    height: 100%;
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      height: 100%;
      padding: 0;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
        border-color: transparent !important;
      }
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      border-color: transparent !important;
    }
  }

  /deep/ .el-table {
    background-color: transparent;
    width: 100% !important;
    table-layout: fixed;

    &::before {
      display: none;
    }

    th {
      background-color: rgba(31, 198, 255, 0.3);
      color: #fff;
      border-color: #1fc6ff;
    }

    td {
      background-color: transparent;
      color: #fff;
      border-color: rgba(31, 198, 255, 0.3);
    }

    tr {
      background-color: transparent;
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-wrapper .border-content {
    position: static !important;
    padding: 10px;
    box-sizing: border-box;

    .scroll-wrapper .chart-tit {
      margin: 0;
      height: 0;
      transform: translateY(-50px);
    }
  }

  .custom-table-origin {
    /deep/ .chart-tit {
      position: absolute;
      right: 10px;
      top: -50px;
    }
  }
}
</style>
