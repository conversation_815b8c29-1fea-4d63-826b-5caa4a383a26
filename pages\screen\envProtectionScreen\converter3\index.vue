<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <screen-border :title="'生产设备关键参数'">
            <div class="content-wrapper">
              <div
                v-loading="loading"
                class="top-wrapper top-wrapper1">
                <div
                  class="cards cards-left">
                  <template
                    v-for="(item, index) in oneData">
                    <div
                      v-if="item.position === 'left1'"
                      :key="index"
                      class="card">
                      <span class="name">
                        <span
                          class="left"
                          @click="clickOne(item.pointCode)"/>
                        {{ item.name }}</span>
                      <span class="num"><em>{{ item.value }}</em> {{ item.unit }}</span>
                    </div>
                  </template>
                </div>
                <div
                  class="cards cards-left2">
                  <template
                    v-for="(item, index) in oneData">
                    <div
                      v-if="item.position === 'left2'"
                      :key="index"
                      class="card">
                      <span class="name">
                        <span
                          class="left"
                          @click="clickOne(item.name)"/>
                        {{ item.name }}</span>
                      <span class="num"><em>{{ item.value }}</em> {{ item.unit }}</span>
                    </div>
                  </template>
                </div>
                <div
                  ref="imgWrapper"
                  class="top-inner">
                  <img
                    :style="{'height': imgHeight + 'px'}"
                    src="../../../../assets/images/screen/img-converter1.png"
                    alt=""
                  >
                </div>
              </div>
              <div
                class="bottom-wrapper">
                <div class="content-item">
                  <el-row
                    :gutter="32"
                    class="full-height">
                    <el-col
                      :span="12"
                      class="full-height">
                      <div
                        class="chart-wrapper">
                        <div class="chart-tit">
                          氧枪流量
                        </div>

                        <div
                          class="chart">
                          <env-line-chart
                            :color-start="'rgba(54,124,243,0.25)'"
                            :color-end="'rgba(54,124,243,0)'"
                            :show-legend="false"
                            :chart-data="echartsData[0].data"
                            :x-data="echartsData[0].xData"/>
                        </div>
                      </div>
                    </el-col>
                    <el-col
                      :span="12"
                      class="full-height">
                      <div class="chart-wrapper">
                        <div class="chart-tit">
                          工作氧枪高度
                        </div>
                        <div
                          class="chart">
                          <env-line-chart
                            :color="['#FF9800']"
                            :color-start="'rgba(255,152,0,0.26)'"
                            :color-end="'rgba(255,152,0,0)'"
                            :show-legend="false"
                            :chart-data="echartsData[1].data"
                            :x-data="echartsData[1].xData"/>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
                <div class="content-item">
                  <div class="chart-wrapper">
                    <div class="chart-tit">
                      兑铁信号
                    </div>
                    <div
                      class="chart">
                      <env-line-chart
                        :color="['#FF9800']"
                        :color-start="'rgba(255,152,0,0.26)'"
                        :color-end="'rgba(255,152,0,0)'"
                        :show-legend="false"
                        :chart-data="echartsData[3].data"
                        :x-data="echartsData[3].xData"/>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </screen-border>
        </el-col>

        <el-col
          :span="12"
          class="full-height">
          <screen-border :title="'除尘设备关键参数'">

            <div class="content-wrapper">
              <div class="top-wrapper top-wrapper1">
                <div
                  class="cards cards-left">
                  <template>
                    <div>
                      <span class="name">
                        转炉二次除尘 3#</span>
                    </div>
                  </template>
                </div>
                <div
                  class="cards cards-right">
                  <template
                    v-for="(item, index) in oneData">
                    <div
                      v-if="item.position === 'right'"
                      :key="index"
                      class="card">
                      <span class="name">
                        <span
                          class="left"
                          @click="clickOne(item.pointCode)"/>
                        {{ item.name }}</span>
                      <span class="num"><em>{{ item.value }}</em> {{ item.unit }}</span>
                    </div>
                  </template>
                </div>
                <div
                  class="top-inner1">
                  <img
                    src="../../../../assets/images/screen/img-converter3.png"
                    alt=""
                  >
                </div>
              </div>
              <div class="bottom-wrapper">
                <div class="content-item">
                  <el-row
                    :gutter="32"
                    class="full-height">
                    <el-col
                      :span="24"
                      class="full-height">
                      <div class="chart-wrapper">
                        <div class="chart-tit">
                          除尘烟气含量
                        </div>
                        <div class="chart">
                          <env-line-chart
                            :show-legend="false"
                            :color="['#A146B0']"
                            :color-start="'rgba(161,70,176,0.29)'"
                            :color-end="'rgba(161,70,176,0)'"
                            :chart-data="echartsData[2].data"
                            :x-data="echartsData[2].xData"/>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
                <div class="content-item">

                  <el-row
                    :gutter="32"
                    class="full-height">
                    <el-col
                      :span="12"
                      class="full-height">
                      <div class="chart-wrapper">
                        <div class="chart-tit">
                          风机电流
                        </div>
                        <div class="chart">
                          <env-line-chart
                            :show-legend="false"
                            :color="['#FF9800']"
                            :color-start="'rgba(255,152,0,0.26)'"
                            :color-end="'rgba(255,152,0,0)'"
                            :chart-data="echartsData[4].data"
                            :x-data="echartsData[4].xData"/>
                        </div>
                      </div>
                    </el-col>
                    <el-col
                      :span="12"
                      class="full-height">
                      <div class="chart-wrapper">
                        <div class="chart-tit">
                          风机转速
                        </div>
                        <div class="chart">
                          <env-line-chart
                            :color="['#55C6D4']"
                            :color-start="'rgba(85,198,212,0.25)'"
                            :color-end="'rgba(85,198,212,0)'"
                            :show-legend="false"
                            :chart-data="echartsData[5].data"
                            :x-data="echartsData[5].xData"/>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          趋势分析
        </div>
      </template>
      <div
        class="dialogDiv">
        <el-row :gutter="20">
          <el-col :span="5">
            <div
              class="stafftree">
              <el-tree
                v-if="dialogVisible"
                ref="tree"
                :data="dataTree"
                :props="defaultProps"
                :default-expanded-keys="defaultExpandedKeys"
                :default-checked-keys="checkedKeys"
                :check-strictly="true"
                :accordion="true"
                :default-expand-all="false"
                :highlight-current="true"
                show-checkbox
                node-key="id"
                @node-click="handleNodeClick"
                @change="handleClick"
                @check-change="treeCheckedChange"/>
            </div>

          </el-col>
          <el-col :span="19">
            <div>
              <el-form
                :inline="true"
                :model="formInline"
                class="formInput"
                label-width="0px">
                <el-date-picker
                  v-model="formInline.time1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="getSynthTable"/>
              </el-form>
              <div
                v-loading="loadingTree"
                style="height: 300px;margin-top: 40px"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.8)">
                <env-line-chart
                  :color-start="'rgba(54,124,243,0.25)'"
                  :color-end="'rgba(54,124,243,0)'"
                  :show-legend="true"
                  :chart-data="chartData1.data"
                  :x-data="chartData1.xData"/>
              </div>
          </div></el-col>
        </el-row>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogVisibleThree"
      :before-close="handleCloseThree"
      :width="'900px'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          趋势分析
        </div>
      </template>
      <div
        class="dialogDiv">
        <el-row :gutter="20">
          <el-col :span="24">
            <div>
              <el-form
                :inline="true"
                :model="formInlineThree"
                style="margin-left: 20px"
                class="formInput"
                label-width="0px">
                <el-date-picker
                  v-model="formInlineThree.time1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="getSynthTableThree"/>
              </el-form>
              <div
                v-loading="loadingTreeThree"
                style="height: 340px;margin-top: 40px"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.8)">
                <env-line-chart1
                  :color-start="'rgba(54,124,243,0.25)'"
                  :color-end="'rgba(54,124,243,0)'"
                  :show-legend="true"
                  :chart-data="chartData1Three.data"
                  :x-data="chartData1Three.xData"/>
              </div>
          </div></el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border.vue'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import CustomTable from '@/pages/screen/qualityMeeting/component/custom-table'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import { getChemQualifiedDetail } from '@/api/screen'
import PolarChart from '@/pages/screen/energyMeeting/component/polar-chart'
import PieRateChart from '@/pages/screen/qualityMeeting/component/pie-rate-chart'
import EnvLineChart from '@/pages/screen/envProtectionScreen/component/env-line-chart'
import EnvLineChart1 from '@/pages/screen/envProtectionScreen/component/env-line-chart1'
import moment from 'moment'
export default {
  name: 'Desulfurization1',
  components: {
    EnvLineChart,
    PieRateChart,
    PolarChart,
    CustomTable,
    SingleBarsChart,
    ScreenBorder,
    EnvLineChart1
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      dialogVisibleThree: false,
      loadingTreeThree: false,
      loadingTree: false,
      timer: null,
      cDate: '',
      loading: true,
      imgHeight: null,
      flawList3: [],
      dialogVisible: false,
      /*------弹窗数据-----*/
      chartData: [
        {
          pointCode: '2807060898891433',
          chart: 'left',
          data: [],
          xData: [12, 15, 17, 18, 20]
        }
      ],
      chartData1: {
        data: [],
        xData: []
      },
      chartData1Three: {
        data: [],
        xData: []
      },
      /*-----------曲线数据--------------*/
      echartsData: [
        {
          pointCode: '2807060894908048',
          chart: 'left',
          data: [
            {
              name: '氧枪流量',
              data: [0, 0, 0, 0, 0]
            }
          ],
          xData: []
        }, //氧枪流量
        {
          pointCode: '01030208_0006_0014',
          chart: 'left',
          data: [
            {
              name: '工作氧枪高度',
              data: [0, 0, 0, 0, 0]
            }
          ],
          xData: []
        }, //工作氧枪高度
        {
          pointCode: '2708031560013298',
          chart: 'right',
          data: [
            {
              name: '除尘烟气含量',
              data: []
            }
          ],
          xData: []
        }, //除尘烟气含量
        {
          pointCode: '01030208_0012_0001',
          chart: 'left',
          data: [
            {
              name: '兑铁信号',
              data: []
            }
          ],
          xData: []
        }, //兑铁信号
        {
          pointCode: '01030208_0001_0003',
          chart: 'right',
          data: [
            {
              name: '风机电流',
              data: []
            }
          ],
          xData: []
        }, //风机电流
        {
          pointCode: '2807061915478026',
          chart: 'right',
          data: [
            {
              name: '风机转速',
              data: []
            }
          ],
          xData: []
        } //风机转速
      ],
      /*-----------实时数据--------------*/
      oneData: [
        {
          name: '氧枪流量',
          pointCode: '2807060894908048',
          unit: 'Nm³/h',
          value: '',
          position: 'left1'
        },
        {
          name: '工作氧枪高度',
          pointCode: '01030208_0006_0014',
          unit: 'mm',
          value: '',
          position: 'left1'
        },
        {
          name: '兑铁信号',
          pointCode: '01030208_0012_0001',
          unit: '',
          value: '',
          position: 'left1'
        },
        {
          name: '加铁水量',
          pointCode: '',
          unit: 't',
          value: '',
          position: 'left2'
        },
        {
          name: '加废钢量',
          pointCode: '',
          unit: 't',
          value: '',
          position: 'left2'
        },
        {
          name: '出钢量',
          pointCode: '',
          unit: 't',
          value: '',
          position: 'left2'
        },
        {
          name: '出渣量',
          pointCode: '',
          unit: 't',
          value: '',
          position: 'left2'
        },
        {
          name: '除尘风量',
          pointCode: '010601_0050_0005',
          unit: 'm³/h',
          value: '',
          position: 'right'
        },
        {
          name: '除尘烟气含量',
          pointCode: '2708031560013298',
          unit: 'mg/m³',
          value: '',
          position: 'right'
        },
        {
          name: '风机电流',
          pointCode: '01030208_0001_0003',
          unit: 'A',
          value: '',
          position: 'right'
        },
        {
          name: '风机转速',
          pointCode: '2807061915478026',
          unit: 'rpm',
          value: '',
          position: 'right'
        },
        {
          name: '清灰周期',
          pointCode: '2708030552813600',
          unit: 'min',
          value: '',
          position: 'right'
        }
      ],
      /*-------------设备树----------*/
      dataTree: [
        {
          id: 1,
          label: '金石材料厂',
          children: [
            {
              id: 11,
              label: '1#石灰窑',
              children: [
                {
                  id: 111,
                  label: '除尘入口温度',
                  pointCode: ''
                },
                {
                  id: 112,
                  label: '煤气总管流量',
                  pointCode: '0106010601_0003_0018'
                },
                {
                  id: 113,
                  label: '除尘出口温度',
                  pointCode: '010601_0051_0004'
                },
                {
                  id: 114,
                  label: '除尘器颗粒物',
                  pointCode: '010601_0051_0001'
                },
                {
                  id: 115,
                  label: '除尘器电流',
                  pointCode: '1003250560117383'
                },
                {
                  id: 116,
                  label: '除尘器压差',
                  pointCode: '0912300872593306'
                },
                {
                  id: 117,
                  label: '除尘器运行信号',
                  pointCode: '1003311975592166'
                }
              ]
            },
            {
              id: 12,
              label: '2#石灰窑',
              children: [
                {
                  id: 121,
                  label: '除尘入口温度',
                  pointCode: ''
                },
                {
                  id: 122,
                  label: '煤气总管流量',
                  pointCode: '0106010601_0003_0019'
                },
                {
                  id: 123,
                  label: '除尘出口温度',
                  pointCode: '010601_0052_0004'
                },
                {
                  id: 124,
                  label: '除尘器颗粒物',
                  pointCode: '010601_0052_0001'
                },
                {
                  id: 125,
                  label: '除尘器电流',
                  pointCode: '1003251028335407'
                },
                {
                  id: 126,
                  label: '除尘器压差',
                  pointCode: '0912300953325650'
                },
                {
                  id: 127,
                  label: '除尘器运行信号',
                  pointCode: '0912300180930534'
                }
              ]
            }
          ]
        },
        {
          id: 2,
          label: '第一炼钢厂',
          children: [
            {
              id: 21,
              label: '1#脱硫',
              children: [
                {
                  id: 211,
                  label: '脱硫剂称重重量',
                  pointCode: '2806221431197019'
                }
              ]
            },
            {
              id: 22,
              label: '2#脱硫',
              children: [
                {
                  id: 221,
                  label: '除尘烟气含量',
                  pointCode: '2708031003909071'
                },
                {
                  id: 222,
                  label: '风机电流',
                  pointCode: '2708030502659000'
                },
                {
                  id: 223,
                  label: '风机转速',
                  pointCode: '2708030205072283'
                },
                {
                  id: 224,
                  label: '清灰周期',
                  pointCode: '2708030764098527'
                },
                {
                  id: 225,
                  label: '脱硫剂称重重量',
                  pointCode: '01030206_0002_0001'
                }
              ]
            },
            {
              id: 23,
              label: '1#转炉',
              children: [
                {
                  id: 2310,
                  label: '除尘风量',
                  pointCode: '010601_0049_0005'
                },
                {
                  id: 231,
                  label: '除尘烟气含量',
                  pointCode: '2708031182832379'
                },
                {
                  id: 232,
                  label: '风机电流',
                  pointCode: '2803260880611751'
                },
                {
                  id: 233,
                  label: '风机转速',
                  pointCode: '2807060961685217'
                },
                {
                  id: 234,
                  label: '清灰周期',
                  pointCode: '2708030121287503'
                },
                {
                  id: 235,
                  label: '兑铁水信号',
                  pointCode: '01030201_0010_0001'
                },
                {
                  id: 236,
                  label: '氧枪高度',
                  pointCode: '2807060564262538'
                },
                {
                  id: 237,
                  label: '氧枪流量',
                  pointCode: '2807060898891433'
                },
                {
                  id: 238,
                  label: '加铁水量',
                  pointCode: ''
                },
                {
                  id: 239,
                  label: '加废钢量',
                  pointCode: ''
                },
                {
                  id: 240,
                  label: '出钢量',
                  pointCode: ''
                },
                {
                  id: 999,
                  label: '出渣量',
                  pointCode: ''
                }
              ]
            },
            {
              id: 24,
              label: '2#转炉',
              children: [
                {
                  id: 2410,
                  label: '除尘风量',
                  pointCode: '2709010213893675'
                },
                {
                  id: 241,
                  label: '除尘烟气含量',
                  pointCode: '2708031714018611'
                },
                {
                  id: 242,
                  label: '风机电流',
                  pointCode: '2806271454515706'
                },
                {
                  id: 243,
                  label: '风机转速',
                  pointCode: '2807231107498168'
                },
                {
                  id: 244,
                  label: '清灰周期',
                  pointCode: '2708031358037684'
                },
                {
                  id: 245,
                  label: '兑铁水信号',
                  pointCode: '01030207_0011_0001'
                },
                {
                  id: 246,
                  label: '氧枪高度',
                  pointCode: '01030207_0005_0019'
                },
                {
                  id: 247,
                  label: '氧枪流量',
                  pointCode: '01030207_0005_0014'
                }
              ]
            },
            {
              id: 25,
              label: '3#转炉',
              children: [
                {
                  id: 2510,
                  label: '除尘风量',
                  pointCode: '010601_0050_0005'
                },
                {
                  id: 251,
                  label: '除尘烟气含量',
                  pointCode: '2708031560013298'
                },
                {
                  id: 252,
                  label: '风机电流',
                  pointCode: '01030208_0001_0003'
                },
                {
                  id: 253,
                  label: '风机转速',
                  pointCode: '2807061915478026'
                },
                {
                  id: 254,
                  label: '清灰周期',
                  pointCode: '2708030552813600'
                },
                {
                  id: 255,
                  label: '兑铁水信号',
                  pointCode: '01030208_0012_0001'
                },
                {
                  id: 256,
                  label: '氧枪高度',
                  pointCode: '01030208_0006_0014'
                },
                {
                  id: 257,
                  label: '氧枪流量',
                  pointCode: '2807060894908048'
                }
              ]
            }
          ]
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      checkedKeys: [],
      defaultExpandedKeys: [],
      formInlineThree: {
        time1: [
          moment(new Date().getTime() - 1000 * 2 * 24 * 3600).format(
            'YYYY-MM-DD'
          ) + ' 20:00:00',
          moment(new Date().getTime() - 1000 * 2 * 24 * 3600).format(
            'YYYY-MM-DD'
          ) + ' 23:59:59'
        ]
      },
      formInline: {
        time1: [
          moment(new Date().getTime() - 1000 * 2 * 24 * 3600).format(
            'YYYY-MM-DD'
          ) + ' 20:00:00',
          moment(new Date().getTime() - 1000 * 2 * 24 * 3600).format(
            'YYYY-MM-DD'
          ) + ' 23:59:59'
        ]
      },
      pointCodePram: '',
      selectPointCode: [],
      valueIndex: null
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.loadData()
    },
    //清除树的选择状态：监听弹框
    dialogVisible: function(newVal, oldVal) {
      if (newVal === false) {
        this.reset()
      }
    }
  },
  created() {
    this.cDate = this.selectDate
    this.loadData()
  },
  mounted() {
    this.calculateHeight()
    this.loading = false
  },
  beforeDestroy() {
    clearInterval(this.timer)
    this.timer = null
  },
  methods: {
    loadData() {
      /*-------页面加载数据------*/
      this.queryCurrentData()
      this.getEchartsData()
      // this.getWindowEchartsData()
      this.timer = setInterval(() => {
        setTimeout(this.queryCurrentData, 0)
      }, 120000)
    },
    calculateHeight() {
      this.imgHeight = this.$refs.imgWrapper.offsetHeight
      console.log(this.imgHeight)
    },
    /*--------------实时参数-------------*/
    queryCurrentData() {
      post('/ddm/iotController/getJhIotRealTimeByEq.ddm', {
        eqName: '3#转炉'
      }).then(res => {
        if (res.success && res.data !== null) {
          res.data.forEach(item => {
            this.oneData.forEach(val => {
              if (item.pointCode === val.pointCode) {
                item.value === 'true'
                  ? (item.value = '正在兑铁')
                  : item.value === 'false'
                    ? (item.value = '未兑铁')
                    : item.value
                val.value = item.value
                if (item.pointCode === '010601_0050_0005') {
                  val.value = (3600 * item.value).toFixed(0)
                }
              }
            })
          })
        }
      })
      post('/ddm/iotController/getZhuanLuDataByEq.ddm', {
        eqName: '3#转炉'
      }).then(res => {
        if (res.success && res.data !== null) {
          this.oneData[3].value = res.data.HM_NET_WGT_CONRSLT
          this.oneData[4].value = res.data.SCR_NET_WGT_CONRSLT
          this.oneData[5].value = res.data.STEEL_NET_WGT_CONRSLT
          this.oneData[6].value = res.data.DIS_CHARGE_WGT
        }
      })
    },
    /*--------曲线数据--------*/
    getEchartsData() {
      let startTime = Date.now()
      let now = new Date()
      let endTime = new Date(now.getTime() - 60 * 60 * 1000)
      let timesTamp = Math.floor(endTime / 1000) * 1000
      post('/dsm/iotController/getJhIotValueByIntervalAndTimesByEq.dsm', {
        eqName: '3#转炉',
        startTs: timesTamp,
        endTs: startTime,
        interval: '500'
      }).then(res => {
        if (res.success && res.data !== null) {
          res.data.forEach((item, index) => {
            this.echartsData.forEach((val, ind) => {
              if (val.pointCode === item.pointCode) {
                val.data[0].data = item.value
                val.xData = item.ts
              }
            })
          })
        }
        let itemList = []
        this.echartsData[3].data[0].data.forEach((item, ind) => {
          itemList.push(item === 'false' ? '0' : '1')
        })
        this.echartsData[3].data[0].data = itemList
      })
    },
    /*--------设备树--------*/
    //点击设备树
    handleNodeClick(data, node) {
      console.log('clickDataTree', data)
    },
    handleClick(data, tree) {
      // this.$refs.tree.setCheckedKeys([]) // 删除所有选中节点
      // this.$refs.tree.setCheckedNodes([data]) // 选中已选中节点
    },
    /*treeCheckedChange(data, check, childCheck) {
      console.log('data', data)
      console.log('check', check)
      if (check) {
        this.selectPointCode = []
        this.$refs.tree.setCheckedNodes([data]) // 选中已选中节点
        this.selectPointCode.push(data.pointCode)
        if (data.pointCode !== '') {
          this.getWindowEchartsData()
        } else {
          this.getWindowEchartsDataThree(data.label)
        }
      }
    },*/
    //复选设备树
    treeCheckedChange(data, isChecked, indeterminate) {
      this.loadingTree = true
      console.log('check-change', data.pointCode, isChecked, indeterminate)
      console.log('data.pointCode', data.pointCode)
      if (isChecked === true && data !== undefined && data !== null) {
        this.selectPointCode.push(data.pointCode)
        if (this.selectPointCode.length > 1) {
          let selectPointCode = this.selectPointCode.filter(item => {
            return item !== undefined
          })
          let selectPointCodeNew = [...new Set(selectPointCode)]
          this.selectPointCode = selectPointCodeNew
        }
        this.selectPointCode.forEach((item, ind) => {
          if (item.length < 6) {
            this.selectPointCode.splice(ind, 1)
          }
        })
        console.log('this.selectPointCode111', this.selectPointCode)
        this.getWindowEchartsData()
      }
      if (isChecked === false && data !== undefined && data !== null) {
        //过滤掉相等的
        this.selectPointCode = this.selectPointCode.filter(item => {
          return item !== data.pointCode
        })
        console.log('this.selectPointCode222', this.selectPointCode)
        this.getWindowEchartsData()
      }
    },
    clickOne(namePram) {
      this.$set(
        this.formInline.time1,
        0,
        moment(new Date().getTime() - 1000 * 2 * 24 * 3600).format(
          'YYYY-MM-DD'
        ) + ' 20:00:00'
      )
      this.$set(
        this.formInline.time1,
        1,
        moment(new Date().getTime() - 1000 * 2 * 24 * 3600).format(
          'YYYY-MM-DD'
        ) + ' 23:59:59'
      )
      console.log('namePram', namePram)
      this.selectPointCode = []
      this.dialogVisible = true
      this.loadingTree = true
      if (namePram === '010601_0050_0005') {
        this.defaultExpandedKeys = [1, 2, 25]
        this.checkedKeys = [2, 25, 2510]
      } else if (namePram === '2708031560013298') {
        this.defaultExpandedKeys = [1, 2, 25]
        this.checkedKeys = [2, 25, 251]
      } else if (namePram === '01030208_0001_0003') {
        this.defaultExpandedKeys = [1, 2, 25]
        this.checkedKeys = [2, 25, 252]
      } else if (namePram === '2807061915478026') {
        this.defaultExpandedKeys = [1, 2, 25]
        this.checkedKeys = [2, 25, 253]
      } else if (namePram === '2708030552813600') {
        this.defaultExpandedKeys = [1, 2, 25]
        this.checkedKeys = [2, 25, 254]
      } else if (namePram === '01030208_0012_0001') {
        this.defaultExpandedKeys = [1, 2, 25]
        this.checkedKeys = [2, 25, 255]
      } else if (namePram === '01030208_0006_0014') {
        this.defaultExpandedKeys = [1, 2, 25]
        this.checkedKeys = [2, 25, 256]
      } else if (namePram === '2807060894908048') {
        this.defaultExpandedKeys = [1, 2, 25]
        this.checkedKeys = [2, 25, 257]
      } else if (namePram === '加铁水量') {
        this.defaultExpandedKeys = [1, 2, 25]
        this.checkedKeys = [2, 25, 258]
      } else if (namePram === '加废钢量') {
        this.defaultExpandedKeys = [1, 2, 25]
        this.checkedKeys = [2, 25, 259]
      } else if (namePram === '出钢量') {
        this.defaultExpandedKeys = [1, 2, 25]
        this.checkedKeys = [2, 25, 250]
      } else if (namePram === '出渣量') {
        this.defaultExpandedKeys = [1, 2, 25]
        this.checkedKeys = [2, 25, 999]
      } else {
        return (this.dialogVisible = false)
      }
      this.selectPointCode.push(namePram)
      console.log('this.selectPointCode333', this.selectPointCode)
      if (this.selectPointCode.length > 1) {
        return this.selectPointCode.shift()
      }
      if (namePram.length > 4) {
        this.getWindowEchartsData(namePram)
      } else {
        this.getWindowEchartsDataThree(namePram)
      }
    },
    handleClose() {
      this.dialogVisible = false
    },
    clickOneThree(val) {
      console.log('三级val', val)
      this.$set(
        this.formInlineThree.time1,
        0,
        moment(new Date().getTime() - 1000 * 2 * 24 * 3600).format(
          'YYYY-MM-DD'
        ) + ' 20:00:00'
      )
      this.$set(
        this.formInlineThree.time1,
        1,
        moment(new Date().getTime() - 1000 * 2 * 24 * 3600).format(
          'YYYY-MM-DD'
        ) + ' 23:59:59'
      )
      this.dialogVisibleThree = true
      this.getWindowEchartsDataThree()
    },
    handleCloseThree() {
      this.dialogVisibleThree = false
    },
    getSynthTableThree() {
      this.getWindowEchartsDataThree()
    },
    //清除复选框状态：封装一个清除的函数，在需要清除的地方调用函数
    reset() {
      this.dataTree.forEach(ele => {
        // 把已选中的复选框清除
        this.$nextTick(() => {
          // this.$refs.tree.setCheckedKeys([])
          this.selectPointCode = []
          this.defaultExpandedKeys = []
          this.checkedKeys = []
        })
      })
    },
    getSynthTable() {
      this.getWindowEchartsData()
    },
    /*--------弹窗接口---------*/
    async getWindowEchartsData() {
      if (!this.dialogVisible) return
      const res = await post(
        '/ddm/iotController/getJhIotValueByIntervalAndTimes.ddm',
        {
          pointCode: this.selectPointCode.filter(item => !!item).join(','),
          startTs: this.formInline.time1[0],
          endTs: this.formInline.time1[1],
          interval: '500'
        }
      )
      if (res.success && res.data !== null) {
        const showData = []
        const showXData = []
        //弹窗曲线，兑铁信号false、true转化为0、1

        res.data.forEach(item => {
          if (
            [
              '010601_0049_0005',
              '2709010213893675',
              '010601_0050_0005'
            ].includes(item.pointCode)
          ) {
            item.value = item.value.map(val => Number(val) * 3600)
          } else {
            item.value = item.value.map(
              val => (val === 'false' ? '0' : val === 'true' ? '1' : val)
            )
          }
          item.pointCode === '2807060898891433' //1#转炉
            ? (item.pointCode = '1#转炉氧枪流量')
            : item.pointCode === '2807060564262538'
              ? (item.pointCode = '1#转炉工作氧枪高度')
              : item.pointCode === '010601_0049_0005'
                ? (item.pointCode = '1#转炉除尘风量')
                : item.pointCode === '01030201_0010_0001'
                  ? (item.pointCode = '1#转炉兑铁信号')
                  : item.pointCode === '2708031182832379'
                    ? (item.pointCode = '1#转炉除尘烟气含量')
                    : item.pointCode === '2803260880611751'
                      ? (item.pointCode = '1#转炉风机电流')
                      : item.pointCode === '2807060961685217'
                        ? (item.pointCode = '1#转炉风机转速')
                        : item.pointCode === '2708030121287503'
                          ? (item.pointCode = '1#转炉清灰周期') //2#转炉
                          : item.pointCode === '01030207_0005_0014'
                            ? (item.pointCode = '2#转炉氧枪流量')
                            : item.pointCode === '2709010213893675'
                              ? (item.pointCode = '2#转炉除尘风量')
                              : item.pointCode === '01030207_0005_0019'
                                ? (item.pointCode = '2#转炉工作氧枪高度')
                                : item.pointCode === '01030207_0011_0001'
                                  ? (item.pointCode = '2#转炉兑铁信号')
                                  : item.pointCode === '2708031714018611'
                                    ? (item.pointCode = '2#转炉除尘烟气含量')
                                    : item.pointCode === '2806271454515706'
                                      ? (item.pointCode = '2#转炉风机电流')
                                      : item.pointCode === '2807231107498168'
                                        ? (item.pointCode = '2#转炉风机转速')
                                        : item.pointCode === '2708031358037684'
                                          ? (item.pointCode = '2#转炉清灰周期') //3#转炉
                                          : item.pointCode ===
                                            '2807060894908048'
                                            ? (item.pointCode =
                                                '3#转炉氧枪流量')
                                            : item.pointCode ===
                                              '010601_0050_0005'
                                              ? (item.pointCode =
                                                  '3#转炉除尘风量')
                                              : item.pointCode ===
                                                '01030208_0006_0014'
                                                ? (item.pointCode =
                                                    '3#转炉工作氧枪高度')
                                                : item.pointCode ===
                                                  '01030208_0012_0001'
                                                  ? (item.pointCode =
                                                      '3#转炉兑铁信号')
                                                  : item.pointCode ===
                                                    '2708031560013298'
                                                    ? (item.pointCode =
                                                        '3#转炉除尘烟气含量')
                                                    : item.pointCode ===
                                                      '01030208_0001_0003'
                                                      ? (item.pointCode =
                                                          '3#转炉风机电流')
                                                      : item.pointCode ===
                                                        '2807061915478026'
                                                        ? (item.pointCode =
                                                            '3#转炉风机转速')
                                                        : item.pointCode ===
                                                          '2708030552813600'
                                                          ? (item.pointCode =
                                                              '3#转炉清灰周期') //1#脱硫
                                                          : item.pointCode ===
                                                            '2806221431197019'
                                                            ? (item.pointCode =
                                                                '1#脱硫脱硫剂称重称重量')
                                                            : item.pointCode ===
                                                              '2708031714018611'
                                                              ? (item.pointCode =
                                                                  '1#脱硫除尘烟气含量')
                                                              : item.pointCode ===
                                                                '01030201_0010_0001'
                                                                ? (item.pointCode =
                                                                    '1#脱硫兑铁信号')
                                                                : item.pointCode ===
                                                                  '2708031182832379'
                                                                  ? (item.pointCode =
                                                                      '1#脱硫除尘烟气含量')
                                                                  : item.pointCode ===
                                                                    '2806271454515706'
                                                                    ? (item.pointCode =
                                                                        '1#脱硫风机电流')
                                                                    : item.pointCode ===
                                                                      '2807231107498168'
                                                                      ? (item.pointCode =
                                                                          '1#脱硫风机转速')
                                                                      : item.pointCode ===
                                                                        '2708031358037684'
                                                                        ? (item.pointCode =
                                                                            '1#脱硫清灰周期') //2#脱硫
                                                                        : item.pointCode ===
                                                                          '01030206_0002_0001'
                                                                          ? (item.pointCode =
                                                                              '2#脱硫脱硫剂称重称重量')
                                                                          : item.pointCode ===
                                                                            '2708031003909071'
                                                                            ? (item.pointCode =
                                                                                '2#脱硫除尘烟气含量')
                                                                            : item.pointCode ===
                                                                              '01030201_0010_0001'
                                                                              ? (item.pointCode =
                                                                                  '2#脱硫兑铁信号')
                                                                              : item.pointCode ===
                                                                                '2708031182832379'
                                                                                ? (item.pointCode =
                                                                                    '2#脱硫除尘烟气含量')
                                                                                : item.pointCode ===
                                                                                  '2708030502659000'
                                                                                  ? (item.pointCode =
                                                                                      '2#脱硫风机电流')
                                                                                  : item.pointCode ===
                                                                                    '2708030205072283'
                                                                                    ? (item.pointCode =
                                                                                        '2#脱硫风机转速')
                                                                                    : item.pointCode ===
                                                                                      '2708030764098527'
                                                                                      ? (item.pointCode =
                                                                                          '2#脱硫清灰周期') //1#石灰窑
                                                                                      : item.pointCode ===
                                                                                        '0106010601_0003_0018'
                                                                                        ? (item.pointCode =
                                                                                            '1#石灰窑煤气总管流量')
                                                                                        : item.pointCode ===
                                                                                          '010601_0051_0004'
                                                                                          ? (item.pointCode =
                                                                                              '1#石灰窑除尘出口温度')
                                                                                          : item.pointCode ===
                                                                                            '010601_0051_0001'
                                                                                            ? (item.pointCode =
                                                                                                '1#石灰窑除尘器颗粒物')
                                                                                            : item.pointCode ===
                                                                                              '1003250560117383'
                                                                                              ? (item.pointCode =
                                                                                                  '1#石灰窑除尘器电流')
                                                                                              : item.pointCode ===
                                                                                                '0912300872593306'
                                                                                                ? (item.pointCode =
                                                                                                    '1#石灰窑除尘器压差')
                                                                                                : item.pointCode ===
                                                                                                  '1003311975592166'
                                                                                                  ? (item.pointCode =
                                                                                                      '1#石灰窑除尘器运行信号')
                                                                                                  : item.pointCode ===
                                                                                                    '2708030552813600'
                                                                                                    ? (item.pointCode =
                                                                                                        '1#石灰窑清灰周期') //2#石灰窑
                                                                                                    : item.pointCode ===
                                                                                                      '0106010601_0003_0019'
                                                                                                      ? (item.pointCode =
                                                                                                          '2#石灰窑煤气总管流量')
                                                                                                      : item.pointCode ===
                                                                                                        '010601_0052_0004'
                                                                                                        ? (item.pointCode =
                                                                                                            '2#石灰窑除尘出口温度')
                                                                                                        : item.pointCode ===
                                                                                                          '010601_0052_0001'
                                                                                                          ? (item.pointCode =
                                                                                                              '2#石灰窑除尘器颗粒物')
                                                                                                          : item.pointCode ===
                                                                                                            '1003251028335407'
                                                                                                            ? (item.pointCode =
                                                                                                                '2#石灰窑除尘器电流')
                                                                                                            : item.pointCode ===
                                                                                                              '0912300953325650'
                                                                                                              ? (item.pointCode =
                                                                                                                  '2#石灰窑除尘器压差')
                                                                                                              : item.pointCode ===
                                                                                                                '0912300180930534'
                                                                                                                ? (item.pointCode =
                                                                                                                    '2#石灰窑除尘器运行信号')
                                                                                                                : item.pointCode ===
                                                                                                                  '2708030552813600'
                                                                                                                  ? (item.pointCode =
                                                                                                                      '2#石灰窑清灰周期')
                                                                                                                  : ''
          showData.push({
            pointCode: item.pointCode,
            name: item.pointCode,
            data: item.value
          })
          showXData.xData = item.ts
          this.loadingTree = false
        })
        this.chartData1.data = showData
        this.chartData1.xData = showXData.xData
      }
    },
    /*--------三级数据弹窗接口---------*/
    async getWindowEchartsDataThree(dataPram) {
      if (!this.dialogVisible) return
      //将时间格式2023-08-01 00:00:00 处理为20230801000000传给后台
      let start = this.formInline.time1[0].split(' ')
      let start0 = start[0].split('-')
      let start1 = start[1].split(':')
      let start2 = start0.join('') + start1.join('')
      console.log('start2', start2)
      let end = this.formInline.time1[1].split(' ')
      let end0 = end[0].split('-')
      let end1 = end[1].split(':')
      let end2 = end0.join('') + end1.join('')
      console.log('end2', end2)
      const res = await post(
        '/ddm/iotController/getZhuanLuDataByEqAndDate.ddm',
        {
          eqName: '3#转炉',
          beginDate: start2,
          endDate: end2
        }
      )
      if (res.success) {
        const timeList = [] //时间
        const showXData = [] //横坐标-炉次号
        const hmNetWgtList = [] //铁水
        const steelNetWgtList = [] //出钢
        const scrNetWgtList = [] //废钢
        const disChargeWgtList = [] //出渣量
        res.data.forEach(item => {
          timeList.push(item.insDate)
          showXData.push(item.matId)
          hmNetWgtList.push(item.hmNetWgt)
          steelNetWgtList.push(item.steelNetWgt)
          scrNetWgtList.push(item.scrNetWgt)
          disChargeWgtList.push(item.disChargeWgt)
        })
        //自定义tooltip增加时间显示
        let timeData = []
        hmNetWgtList.forEach((val, ind) => {
          timeData.push({
            value: val
          })
        })
        timeList.forEach((item, index) => {
          timeData[index].timeList = item
        })
        const showYData = [
          {
            pointCode: '000',
            name: '加铁水量',
            data: timeData
          },
          {
            pointCode: '111',
            name: '加废钢量',
            data: scrNetWgtList
          },
          {
            pointCode: '333',
            name: '出钢量',
            data: steelNetWgtList
          },
          {
            pointCode: '444',
            name: '出渣量',
            data: disChargeWgtList
          }
        ]
        /*----------整合到一个设备树--------*/
        let hmNetWgtListLast = [] //加铁水量
        let scrNetWgtListLast = [] //加废钢量
        let steelNetWgtListLast = [] //出钢量
        let disChargeWgtListLast = [] //出渣量
        if (dataPram === '加铁水量') {
          hmNetWgtListLast.push({
            data: hmNetWgtList,
            name: '加铁水量'
          })
          this.chartData1.data = hmNetWgtListLast
          this.chartData1.xData = timeList
        } else if (dataPram === '加废钢量') {
          scrNetWgtListLast.push({
            data: scrNetWgtList,
            name: '加废钢量'
          })
          this.chartData1.data = scrNetWgtListLast
          this.chartData1.xData = timeList
        } else if (dataPram === '出钢量') {
          steelNetWgtListLast.push({
            data: steelNetWgtList,
            name: '出钢量'
          })
          this.chartData1.data = steelNetWgtListLast
          this.chartData1.xData = timeList
        } else if (dataPram === '出渣量') {
          disChargeWgtListLast.push({
            data: disChargeWgtList,
            name: '出渣量'
          })
          this.chartData1.data = disChargeWgtListLast
          this.chartData1.xData = timeList
        }
        this.loadingTree = false
        // this.chartData1Three.xData = showXData
        // this.chartData1Three.data = showYData
        //给自定义tooltip增加时间属性
      }
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }

  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  .cards {
    height: auto;
  }
  .top-wrapper1 {
    margin-top: 2%;
    height: 45%;
    position: relative;
    text-align: center;
    .cards-left {
      position: absolute;
      top: 0;
      left: 0;
      width: 130px;
    }
    .cards-left2 {
      position: absolute;
      top: 0;
      right: 10px;
      width: 130px;
    }
    .cards-right {
      position: absolute;
      top: 0;
      right: 10px;
      width: 274px;
    }
    .top-inner {
      height: 100%;
      display: inline-block;
      position: relative;
      margin-right: 150px;
      img {
        height: 100%;
      }
    }
    .top-inner1 {
      height: calc(100% - 66px);
      width: 50%;
      display: inline-block;
      position: relative;
      margin-right: 300px;
      margin-top: 70px;
      img {
        height: 90%;
      }
    }
  }
  .top-wrapper2 {
    margin-top: 2%;
    height: 45%;
    .top-inner {
      height: calc(100% - 56px);
      width: 72%;
      text-align: center;
      margin: auto;
      display: flex;
      align-items: center;
      img {
        max-width: 100%;
        margin-top: 20px;
      }
    }
  }
  .bottom-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .chart-tit {
      font-size: 14px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 20px;
      margin: 10px 0;
      &:before {
        content: '1';
        color: #ffffff;
        background: #ffffff;
        width: 6px;
        height: 100%;
        margin-right: 4px;
      }
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  overflow: auto;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}
/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
/deep/ .cards .cards-right {
  width: 280px;
}
.cards {
  height: 65px;
  text-align: right;
  .card {
    display: inline-block;
    white-space: nowrap;
    margin-left: 7px;
    margin-bottom: 7px;
    text-align: left;
    min-width: 134px;
    line-height: 20px;
    font-size: 14px;
    border: 1px solid rgba(31, 198, 255, 0.5);
    .left {
      float: right;
      width: 20px;
      height: 20px;
      background: url('../../../../assets/images/screen/linechart.png')
        no-repeat center;
      margin-left: 6px;
    }
    span {
      padding: 4px 5px;
      em {
        font-size: 16px;
        font-weight: bold;

        &.red {
          color: #ff2855;
        }

        &.red {
          color: #19be6b;
        }
      }
    }

    .name {
      display: block;
      background: rgba(31, 198, 255, 0.5);
    }

    .num {
      display: block;
    }
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .chart {
    flex: 1;
    overflow: hidden;
    position: relative;
    .operate-box {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}
/*-----------弹窗样式---------*/
.dialogDiv {
  height: 600px;
}
/deep/ .el-scrollbar__wrap {
  overflow-x: hidden;
  background-color: #2e4262;
}
.el-tree-node.is-current > .el-tree-node__content {
  background-color: #2e4262 !important;
}
/deep/.el-tree-node__content:hover {
  background-color: #0f2b3f;
}
/deep/.el-tree[data-v-889bbc90] {
  background-color: #041a21 !important;
}
/deep/.el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: #041a21;
}
/deep/.el-tree {
  position: relative;
  cursor: default;
  background: #041a21;
  color: #fff;
}
/*父节点不显示选项框，子节点显示选项框*/
/deep/
  .stafftree
  .el-tree
  .el-tree-node
  .is-leaf
  + .el-checkbox
  .el-checkbox__inner {
  display: inline-block;
}
/deep/ .stafftree .el-tree .el-tree-node .el-checkbox .el-checkbox__inner {
  display: none;
}
::v-deep.el-tree-node.is-current > .el-tree-node__content {
  background-color: #041a21 !important;
}
/deep/.el-date-editor .el-range-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: none;
  outline: 0;
  display: inline-block;
  height: 100%;
  margin: 0;
  padding: 0;
  width: 41%;
  text-align: center;
  font-size: 16px;
  color: #fff;
}
/deep/.el-date-editor .el-range-separator {
  display: inline-block;
  height: 100%;
  padding: 1px 4px;
  margin: 0;
  text-align: center;
  line-height: 27px;
  font-size: 14px;
  width: 5%;
  color: #fff;
}
</style>
