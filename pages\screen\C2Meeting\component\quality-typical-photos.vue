<template>
  <div class="bigBox">
    <div class="operate">
      <el-upload
        ref="upload"
        :http-request="uploadFileData"
        :on-remove="handleRemove"
        :file-list="fileList"
        :multiple ="false"
        :on-exceed="handleExceed"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :show-file-list="false"
        action="">
        <span
          class="screen-btn">
          <el-icon class="el-icon-plus"/>
          上传照片
        </span>
      </el-upload>
      <span 
        class="screen-btn steelVsflaw" 
        @click="imgEmpty">
        图片清空
      </span>
      <span 
        class="screen-btn steelVsflaw" 
        @click="updataStoveFlaw">
        钢种缺陷提交
      </span>
    </div>
    <div class="photoWall">
      <div 
        v-for="(item,index) in imgArr"
        :key="index"
        class="phote"
        @click="viveImg(item)">
        <img 
          :src="item" 
          alt="">
      </div>
    </div>
    <div class="suggestion">
      <div class="steel">
        <span>钢种：</span>
        <el-input
          :rows="5"
          v-model="steelArea"
          type="textarea"
          placeholder="请输入内容"/></div>
      <div class="flaw">
        <span>缺陷：</span>
        <el-input
          :rows="5"
          v-model="flawArea"
          type="textarea"
          placeholder="请输入内容"/></div>
    </div>
    <el-image 
      v-show="false"
      ref="previewImg"
      :preview-src-list="srcList"
      src=""/>
  </div>
</template>
<script>
import {
  FILE_FINGALL,
  FILE_UPLOADSECOND,
  STOVE_FLAW,
  UPDATA_STOVE_FLAW,
  DELET_ALL_IMG
} from '@/api/screenC2'
import { post, get } from '@/lib/Util'
import { setIsSubset } from 'mathjs'
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    setting: {
      type: Array,
      default: function() {
        return []
      }
    },
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileList: [], //文件上传
      imgArr: [],
      srcList: [],
      steelArea: '',
      flawArea: ''
    }
  },
  mounted() {
    this.getFileData()
    this.getStoveFlaw()
  },
  methods: {
    //获取页面图片数据
    async getFileData() {
      this.imgArr = []
      let res = await post(FILE_FINGALL, {})
      console.log('数据', res)
      if (res.data) {
        res.data.forEach(item => {
          let Imagelink = item.MINIO_URL.split('?')[0] //解析后预览图片地址
          let format = Imagelink.substring(item.MINIO_URL.lastIndexOf('.') + 1) //获取文件格式
          if (format == 'png' || format == 'jpeg' || format == 'jpg') {
            this.imgArr.push(
              Imagelink.replace('http://172.25.63.72:9123/', '/minoApi/')
            )
          }
        })
        this.imgArr.reverse()
      }
    },
    //获取钢种与缺陷数据
    async getStoveFlaw() {
      let res = await post(STOVE_FLAW, {})
      // console.log('数据', res)
      if (res.data.length != 0) {
        this.steelArea = res.data[0].FLAW
        this.flawArea = res.data[0].GRADE
      }
    },
    //上传钢种与缺陷数据
    async updataStoveFlaw() {
      let res = await post(UPDATA_STOVE_FLAW, {
        flaw: this.steelArea,
        grade: this.flawArea
      })
      console.log('数据', res)
      if (res.data == '保存数据成功') {
        this.$message.success(res.data)
      }
    },
    //上传文件
    async uploadFileData(val) {
      console.log(val.file)
      let formData = new FormData()
      let fileData = {}

      const blob = new Blob([JSON.stringify(fileData)], {
        type: 'application/json'
      })
      formData.append('file', val.file)
      formData.append('EquipmentTree', blob)

      let res = await post(FILE_UPLOADSECOND, formData)
      // console.log('数据', res)
      if (res.includes('http')) {
        this.fileList = []
        this.$message.success('上传成功')
        this.getFileData()
      }
    },
    //图片清空
    async imgEmpty() {
      let res = await post(DELET_ALL_IMG, {})
      // console.log(res)
      if (res.status == 1) {
        this.$message.success('删除成功')
        this.getFileData()
      }
    },

    //点击图片放大查看
    viveImg(val) {
      this.$refs['previewImg'].showViewer = true
      this.srcList = []
      this.srcList.push(val)
    },

    //***上传文件共用事件***//
    //上传了的文件给移除的事件
    handleRemove() {},
    //超出文件个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      })
      return
    },
    //上传成功后的回调
    handleSuccess(response, file, fileList) {
      console.log(response, file, fileList)
    },
    //上传文件之前
    beforeUpload(file) {
      if (file.type != '' || file.type != null || file.type != undefined) {
        //截取文件的后缀，判断文件类型
        const FileExt = file.name.replace(/.+\./, '').toLowerCase()
        //计算文件的大小
        const isLt5M = file.size / 1024 / 1024 < 500 //这里做文件大小限制
        let fileType = ['pdf', 'png', 'jpg'] //设置文件类型
        // 如果大于50M
        if (!isLt5M) {
          this.$message('上传文件大小不能超过 500MB!')
          return false
        }
        //如果文件类型不在允许上传的范围内
        if (fileType.includes(FileExt)) {
          return true
        } else {
          this.$message.error('上传文件格式不正确!')
          return false
        }
      }
    }
  }
}
</script>
<style lang='less' scoped>
.bigBox {
  .steelVsflaw {
    margin-left: 10px;
  }
  .operate {
    display: flex;
    justify-content: flex-end;
  }
  //   .photoWall::-webkit-scrollbar {
  //     display: none;
  //   }
  .photoWall {
    height: calc(100vh - 410px);
    margin-top: 3px;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: flex-start;
    overflow: scroll;
    border: 2px solid #0c4e64;
    .phote {
      width: 24%;
      height: 48%;
      margin: 4px 8px;
      box-sizing: border-box;
      border: 2px solid black;
    }
    .phote:nth-child(4) {
    }
    img {
      width: 100%;
      height: 100%;
    }
  }
  .suggestion {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    .steel,
    .flaw {
      width: 49%;
      font-size: 20px;
      font-weight: 900;
      > span {
        margin-bottom: 5px;
        display: inline-block;
      }
      /deep/.el-textarea__inner {
        background: #041a21;
        border: 2px solid #0c4e64;
        color: white;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
}
// 大屏按钮
.screen-btn {
  display: inline-block;
  min-width: 68px;
  height: 28px;
  padding: 0 5px;
  background: rgba(31, 198, 255, 0.3);
  border: 1px solid #1fc6ff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  color: #fff;
  &:hover {
    background: rgba(31, 198, 255, 0.6);
    border: 1px solid #1fc6ff;
  }
}
*::-webkit-scrollbar {
  width: 6px; /*对垂直流动条有效*/
  height: 6px; /*对水平流动条有效*/
  margin-left: 5px;
}
/*定义滑块颜色、内阴影及圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 7px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.15);
  background: #1fc6ff;
  opacity: 0.5;
}
</style>
