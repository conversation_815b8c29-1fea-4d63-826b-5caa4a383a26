<template>
  <el-select
    v-model="cid"
    filterable
    placeholder="请选择分类"
    @change="select($event)">
    <el-option
      v-for="item in options"
      :key="item.id"
      :label="item.name"
      :value="item.id"/>
  </el-select>
</template>

<script>
import { post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'
import { findBySpecification, findNextRank, kpiCategory } from '@/api/kpi'

export default {
  name: 'SelectKpiCategory',
  props: {
    value: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      options: []
    }
  },
  computed: {
    cid: {
      get: function() {
        return this.value
      },
      set: function(e) {
        this.$emit('input', e)
        // 推送变化
        this.$emit('on-change', e)
      }
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData(parentId) {
      const { data } = await post(kpiCategory, {
        page: 0,
        size: 999
      })
      this.options = data.content
    },
    select(e) {
      //
    }
  }
}
</script>

<style scoped>
.tree-box {
  max-height: 300px;
  overflow: auto;
}
.tree-mode {
  margin-bottom: 8px;
}
</style>
