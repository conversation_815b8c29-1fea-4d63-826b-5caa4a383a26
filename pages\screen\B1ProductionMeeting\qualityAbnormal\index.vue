<template>
  <div class="content">
    <div class="content-item top">
      <custom-table
        :title="'质量/设备异常'"
        :setting="tableObj1.setting"
        :url-list="tableObj1.url.list"
        :url-save="tableObj1.url.save"
        :select-date="selectDate"
        :table-class="'big-table'"/>
    </div>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import {
  qmsQualitySupervisionQuery,
  qmsQualitySupervisionSave
} from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/B1ProductionMeeting/component/custom-table'
import {
  fireCuttingFind,
  fireCuttingSave,
  rollingProductionFind,
  rollingProductionSave
} from '@/api/screenC2'
import {
  deviceAbnormalityFind,
  deviceAbnormalitySave
} from '@/api/screenB1Production'
export default {
  name: 'qualityAbnormal',
  components: { CustomTable, SingleBarsChart },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      tableObj1: {
        url: {
          save: deviceAbnormalitySave,
          list: deviceAbnormalityFind
        },
        setting: [
          {
            keyQuery: 'occrdate',
            keySave: 'occrDate',
            label: '时间'
          },
          {
            keyQuery: 'groupcd',
            keySave: 'groupCd',
            label: '班别'
          },
          {
            keyQuery: 'shift',
            keySave: 'shift',
            label: '班次'
          },
          {
            keyQuery: 'heatno',
            keySave: 'heatNo',
            label: '炉号'
          },
          {
            keyQuery: 'stlgrd',
            keySave: 'stlGrd',
            label: '钢种'
          },
          {
            keyQuery: 'anomaly',
            keySave: 'anomaly',
            label: '异常状态说明'
          },
          {
            keyQuery: 'disresult',
            keySave: 'disResult',
            label: '处置结果'
          },
          {
            keyQuery: 'resunit1',
            keySave: 'resUnit1',
            label: '初判责任单位'
          },
          {
            keyQuery: 'resreason',
            keySave: 'resReason',
            label: '责任判定原因'
          },
          {
            keyQuery: 'resunit2',
            keySave: 'resUnit2',
            label: '终判责任单位'
          },
          {
            keyQuery: 'anoname',
            keySave: 'anoName',
            label: '异常名称'
          }
        ]
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  methods: {}
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
