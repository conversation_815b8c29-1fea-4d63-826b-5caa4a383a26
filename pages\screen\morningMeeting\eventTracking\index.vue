<template>
  <div class="content">
    <div class="content-item">
      <screen-border title="未完成任务">
        <template v-slot:headerRight>
          <span
            v-command="'/screen/morningMeeting/edit'"
            class="screen-btn"
            @click="unfinished.dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            操作
          </span>
        </template>
        <div
          ref="table1"
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            :data="unfinished.showGridData"
            class="font-table center-table"
            border>
            <el-table-column
              type="index"
              label="序号"
              width="80"/>
            <!-- <el-table-column
              property="setTime"
              label="晨会日期"
              width="120"/> -->
            <el-table-column
              property="trackingContent"
              label="跟踪内容"
              align="left"
              class-name="big-font"/>
            <el-table-column
              property="leadingUnit"
              label="牵头单位"
              width="180"/>
            <el-table-column
              property="finishTime"
              label="完成时间"
              width="120"/>
            <el-table-column
              property="progress"
              label="进度"
              align="left"
              class-name="big-font"/>
          </el-table>
        </div>
      </screen-border>
    </div>
    <!--热处理详情-->
    <el-dialog
      :visible.sync="unfinished.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="重点事项跟踪详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!--            <span-->
            <!--              v-if="canEdit"-->
            <!--              class="screen-btn"-->
            <!--              @click="clearGridData('unfinished')">-->
            <!--              清空数据-->
            <!--            </span>-->
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importUnfinishedData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportUnplan">
              导出
            </span>
            <!--            <span-->
            <!--              v-if="canEdit"-->
            <!--              class="screen-btn"-->
            <!--              @click="saveUnfinished">-->
            <!--              <el-icon class="el-icon-document-checked"/>-->
            <!--              保存-->
            <!--            </span>-->
          </div>
          重点事项跟踪详情  -  {{ cDate }}
        </div>
      </template>
      <el-form :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="unfinished.gridData"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="50"/>
          <el-table-column
            prop="rank"
            width="80"
            label="排序">
            <template slot-scope="scope">
              <div
                class="upper"
                @click="handelUpper(scope.$index)">
                <i class="el-icon-top"/>
              </div>
              <div
                class="lower"
                @click="handelDown(scope.$index)">
                <i class="el-icon-bottom"/>
              </div>
            </template>
          </el-table-column>
          <!--          <el-table-column
            property="setTime"
            label="晨会日期"
            width="110">
            <template v-slot="{ row }">
              <el-input v-model="row.setTime" />
            </template>
          </el-table-column>-->
          <el-table-column
            property="trackingContent"
            label="跟踪内容">
            <template v-slot="{ row }">
              <el-input v-model="row.trackingContent" />
            </template>
          </el-table-column>
          <el-table-column
            property="leadingUnit"
            label="牵头单位">
            <template v-slot="{ row }">
              <el-input v-model="row.leadingUnit" />
            </template>
          </el-table-column>
          <el-table-column
            property="finishTime"
            label="完成时间"
            width="110">
            <template v-slot="{ row }">
              <el-input v-model="row.finishTime" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="进度">
            <template v-slot="{ row }">
              <el-input
                v-model="row.progress"
                :rows="4"
                type="textarea" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                style="margin-bottom: 5px"
                @click="addGridItem($index)">
                <el-icon class="el-icon-delete"/>
                保存
              </span>
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index, 'unfinished')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEdit"
          class="screen-btn"
          @click="addGridData('unfinished')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { batchUpdateResource } from '@/api/system'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import {
  findHeatTreatmentYieldByDate,
  findKeyTrackingItemsByDate,
  findSteelOutputByDate,
  keyTrackingDeleteById,
  KeyTrackingUpdateById,
  saveHeatTreatmentYield,
  saveKeyTrackingItems,
  saveSteelOutput
} from '@/api/screen'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { progressReportingSave } from '@/api/screenTechnolagy'
export default {
  name: 'EventTracking',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      unfinished: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      unitList: ['吨数', '炉数'],
      maxHeight: null
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getUnfinished()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    setTimeout(() => {
      this.calculateHeight()
    }, 1000)
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    handelUpper(index) {
      if (index <= 0) {
        this.$message('已经是第一条，不可上移')
        return
      }
      let upDate = this.unfinished.gridData[index - 1]
      this.unfinished.gridData.splice(index - 1, 1)
      this.unfinished.gridData.splice(index, 0, upDate)
      console.log(this.unfinished.gridData)
      this.saveUnfinished()
    },
    handelDown(index) {
      if (index + 1 === this.unfinished.gridData.length) {
        this.$message('已经是最后一条，不可下移')
      } else {
        let downDate = this.unfinished.gridData[index + 1]
        this.unfinished.gridData.splice(index + 1, 1)
        this.unfinished.gridData.splice(index, 0, downDate)
        this.saveUnfinished()
      }
    },
    calculateHeight() {
      console.dir(this.$refs.table1)
      this.maxHeight = this.$refs.table1.offsetHeight
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          /*  A: "序号"
          B: "晨会日期"
          C: "跟踪内容"
          D: "牵头单位"
          E: "完成时间"
          F: "进度"*/
          /* "num":"1","setDate":"2023-03-07","trackingContent":"宽板","leadingUnit":"设备处","finishTime":"2日","progress":"昨日小夜班19:29T1过跨车修复"*/
          num: 'A',
          setTime: 'B',
          trackingContent: 'C',
          leadingUnit: 'D',
          finishTime: 'E',
          progress: 'F'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unfinished.gridData = sheet.map(item => {
          item.setTime = LAY_EXCEL.dateCodeFormat(item.setTime, 'MM月DD日')
          item.trackingContent = item.trackingContent.trim()
          item.leadingUnit = item.leadingUnit.trim()
          item.finishTime = item.finishTime.trim()
          item.progress = item.progress.trim()
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    exportUnplan() {
      const data = [
        {
          num: '序号',
          setTime: '晨会日期',
          trackingContent: '跟踪内容',
          leadingUnit: '牵头单位',
          finishTime: '完成时间',
          progress: '进度'
        }
      ].concat(_.cloneDeep(this.unfinished.gridData))
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `未完成任务详情（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    getUnfinished() {
      post(findKeyTrackingItemsByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.$emit('statusChange', !res.data.length)
        this.unfinished.showGridData = res.data.map(item => {
          return {
            num: item.num,
            setTime: item.settime,
            trackingContent: item.trackingcontent,
            leadingUnit: item.leadingunit,
            finishTime: item.finishtime,
            progress: item.progress,
            id: item.id
          }
        })
        this.unfinished.gridData = lodash.cloneDeep(
          this.unfinished.showGridData
        )
      })
    },
    saveUnfinished() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.unfinished.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(saveKeyTrackingItems, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          // this.$message.success('保存成功！')
          // this.unfinished.dialogVisible = false
          this.getUnfinished()
        }
      })
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
    },
    getMergeData(rowIndex, columnIndex) {
      const matchLeftTop = this.unfinished.gridMerge.find(
        item => item.s.c === columnIndex && item.s.r === rowIndex
      )
      if (matchLeftTop) {
        return [
          matchLeftTop.e.r - matchLeftTop.s.r + 1,
          matchLeftTop.e.c - matchLeftTop.s.c + 1
        ]
      }
      const merged = this.unfinished.gridMerge.find(item => {
        return (
          item.s.c < columnIndex &&
          columnIndex <= item.e.c &&
          item.s.r < rowIndex &&
          rowIndex <= item.e.r
        )
      })
      if (merged) {
        console.log(merged)
        return [0, 0]
      }
    },
    importUnfinishedData(date) {
      post(findKeyTrackingItemsByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.unfinished.gridData = res.data.map(item => {
          return {
            num: item.num,
            setTime: item.settime,
            trackingContent: item.trackingcontent,
            leadingUnit: item.leadingunit,
            finishTime: item.finishtime,
            progress: item.progress
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    addGridItem(index) {
      this.loading = true
      post(KeyTrackingUpdateById, {
        data: [
          Object.assign(this.unfinished.gridData[index], {
            setDate: this.cDate
          })
        ]
      }).then(res => {
        this.loading = false
        if (res.status === 1) {
          this.unfinished.gridData.splice(index, 1)
          this.$message.success('保存成功！')
          this.getUnfinished()
        }
      })
    },

    delGridData(index, name) {
      console.log(this[name].gridData[index])
      if (this[name].gridData[index].id) {
        // 删除接口
        this.$confirm(`是否删除这条记录?`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.loading = true
          post(keyTrackingDeleteById, {
            id: this[name].gridData[index].id
          }).then(res => {
            this.loading = false
            if (res.status === 1) {
              this[name].gridData.splice(index, 1)
              this.$message.success('删除成功！')
            }
          })
        })
      } else {
        // 直接删除行
        this[name].gridData.splice(index, 1)
      }
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.scroll-wrapper {
  height: 100%;
  overflow: auto;
}
.upper {
  display: inline-block;
  width: 45%;
  font-size: 18px;
  cursor: pointer;
  text-align: center;
}
.lower {
  display: inline-block;
  width: 45%;
  font-size: 18px;
  cursor: pointer;
  text-align: center;
}
</style>
