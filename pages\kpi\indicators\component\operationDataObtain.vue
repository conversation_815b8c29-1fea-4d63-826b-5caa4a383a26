<!--
 * @Author: your name
 * @Date: 2020-08-07 09:58:50
 * @LastEditTime: 2022-08-31 09:23:26
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: \iet-idm-web\pages\SM\StatisticalSM.vue
-->
<template>
  <div class="page-content">
    <!-- 添加/编辑弹窗 (变量)-->
    <el-dialog
      :visible.sync="dialogVisible"
      :title="ruleForm.name? ruleForm.name + ' 编辑': '添加数据配置'"
      :show-close="false"
      :append-to-body="true"
      :modal="true"
      class="u-dialog"
      width="80%"
      center>
      <el-form
        ref="formInfo"
        :model="ruleForm"
        :rules="rules"
        label-width="100px"
        class="form-inline">
        <el-divider content-position="left">数据项定义</el-divider>
        <el-row>
          <el-col :span="6">
            <el-form-item
              label="名称"
              prop="name">
              <el-input
                :readonly="true"
                v-model="ruleForm.name"
                @focus="handleSearchName('bl')"
              />
              <!-- @blur="handleNameClick" -->
            </el-form-item>
          </el-col>
          <el-col 
            :span="2" 
            style="text-align: center;">
            <el-button 
              :disabled="request !=2?true:false"
              type="primary"
              size="small"
              round
              @click="dialogVisibleBtn = true">自定义</el-button>
          </el-col>
          <el-dialog 
            :visible.sync="dialogVisibleBtn"
            class="u-dialog"
            append-to-body
            width="30%"
            title="自定义">
            <el-form :model="ruleForm">
              <el-form-item 
                label="自定义名称">
                <el-input
                  v-model="ruleForm.name"
                  clearable/>
              </el-form-item>
              <el-button 
                type="primary" 
                @click="dialogVisibleBtn =false">确定</el-button>
            </el-form>
          </el-dialog>
          <el-col :span="8">
            <el-form-item
              label="别名"
              prop="aliasName">
              <el-input
                v-model="ruleForm.aliasName"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="描述"
              prop="desc">
              <el-input
                v-model="ruleForm.desc"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="产线"
              prop="plant">
              <el-select
                :disabled="isTZZ"
                v-model="ruleForm.plant"
                clearable
                placeholder="请选择产线">
                <el-option
                  v-for="(item, index) in plantArray"
                  :key="index"
                  :value="item.value"
                  :label="item.label"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="分组"
              prop="customGroupName">
              <el-cascader
                v-model="ruleForm.customGroupName"
                :options="treeMgrList"
                :props="groupProps"
                :show-all-levels="false"
                clearable
                filterable
                @change="handlefindGroupInfo"/>
            </el-form-item>
          </el-col>

          <!-- 版本 -->
          <el-col :span="8">
            <el-form-item
              label="版本"
              prop="version">
              <el-input
                :disabled = "true"
                v-model="ruleForm.version"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="创建时间"
              prop="tOC"
              width="200">
              <el-date-picker
                v-model="ruleForm.tOC"
                :disabled="true"
                type="datetime"
                placeholder="选择创建时间"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="修改时间"
              prop="tOM"
              width="200">
              <el-date-picker
                v-model="ruleForm.tOM"
                :disabled="true"
                type="datetime"
                placeholder="选择修改时间"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="用户名称"
              prop="createUser">
              <el-input
                :disabled="true"
                v-model="ruleForm.createUser"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">数据规范</el-divider>
        <el-row>
          <el-col :span="8">
            <el-form-item
              label="变量类型"
              prop="varType">
              <!-- :disabled="ruleForm.curveType === 1 ? false: true" -->
              <el-select
                :disabled="isTZZ"
                v-model="ruleForm.varType"
                clearable
                placeholder="请选择变量类型" >
                <el-option
                  v-for="(item, index) in varTypeArray"
                  :key="index"
                  :value="item.value"
                  :label="item.label"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="数据类型"
              prop="dataType">
              <el-select
                :disabled="isTZZ"
                v-model="ruleForm.dataType"
                clearable
                placeholder="请选择数据类型">
                <el-option
                  v-for="(item, index) in dataTypeArray"
                  :key="index"
                  :value="item.value"
                  :label="item.label"/>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item
              label="是否启用"
              prop="enable">
              <el-switch
                :inactive-value="0"
                :active-value="1"
                :disabled="isTZZ"
                v-model="ruleForm.enable"
                inactive-color="#ff4949"
              />
            </el-form-item>
          </el-col>
          <span
            slot="footer"
            class="dialog-footer">
            <el-button
              :loading="submitLoading"
              type="primary"
              size="small"
              @click="onSubmit">确 定</el-button>
            <el-button
              :disabled="submitLoading"
              type="primary"
              size="small"
              @click="resetForm('formInfo')">取 消</el-button>
          </span>
        </el-row>
        <el-divider content-position="left">ETL设置</el-divider>
        <el-row>
          <el-col :span="8">
            <el-form-item
              label="更新事件"
              prop="updateEvent" >
              <el-cascader
                :options="eventArray"
                v-model="updateEvent"
                :props="updateEventProps"
                :disabled="isTZZ"
                clearable
                filterable
                @change="handleUpdataChange"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="开始事件"
              prop="startEvent">
              <el-cascader
                :options="eventArray"
                v-model="startEvent"
                :props="updateEventProps"
                :disabled="isTZZ"
                clearable
                filterable
                @change="handleUpdataChange1"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="结束事件"
              prop="endEvent">
              <el-cascader
                :options="eventArray"
                :props="updateEventProps"
                v-model="endEvent"
                :disabled="isTZZ"
                clearable
                filterable
                @change="handleUpdataChange2"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="更新方式"
              prop="updateType">
              <el-radio-group
                :disabled="isTZZ"
                v-model="request"
                @change="handleUpdateType">
                <el-radio :label="1">计算</el-radio>
                <el-radio :label="2">接口</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="request==2?true:false"
              label="数据接口"
              prop="dataInterface">
              <el-cascader
                :options="apiArray"
                v-model="dataInterface"
                :props="dataInterFaceProps"
                placeholder="请选择数据接口"
                clearable
                filterable
                @change="dataResourceChange"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- <el-form-item
              v-if="request==1?true:false"
              label="物料号类型"
              prop="matType">
              <el-select
                :disabled="isTZZ"
                v-model="conditionsList.matType"
                placeholder="请选择物料号类型"
                clearable>
                <el-option
                  v-for="(item, index) in marTypeArray"
                  :key="index"
                  :value="item.value"
                  :label="item.label"/>
              </el-select>
            </el-form-item> -->
            <el-form-item
              v-if="request==1?true:false"
              label="时间变量"
              prop="timeVar">
              <el-cascader
                v-if="eventShow"
                :options="timeVarArray"
                v-model="conditionsList.timeVar.name"
                :props="timeVarArrayProps"
                :disabled="isTZZ"
                placeholder="请选择时间变量"
                clearable
                filterable
                @change="timeVarType"/>
            <!-- <el-select
                :disabled="isTZZ"
                v-model="conditionsList.timeVar.name"
                placeholder="请选择时间变量"
                clearable
                @change="timeVarType">
                <el-option
                  v-for="(item, index) in timeVarArray"
                  :key="index"
                  :value="item.value"
                  :label="item.label"/>
              </el-select> -->
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="request==1?true:false"
              label="统计方法"
              prop="method">
              <el-select
                :disabled="isTZZ"
                v-model="conditionsList.method"
                placeholder="请选择统计方法"
                clearable>
                <el-option
                  v-for="item in methodCount"
                  :value="item.value"
                  :key="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <div
              v-if="request==1?true:false"
              style="text-indent: 33px; margin-bottom:10px">统计条件：</div>
          </el-col>
          <div
            v-if="request==1?true:false"
            style="margin:0 10px">
            <el-col :span="24" >
              <el-table
                :data="conditionsList.conditions"
                stripe
                border
                highlight-current-row>
                <el-table-column
                  show-overflow-tooltip
                  prop="name"
                  label="变量名"
                  min-width="150">
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.name"
                      :readonly="true"
                      @focus="handleSearchName((scope.$index))"/>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="type"
                  show-overflow-tooltip
                  label="变量类型"
                  min-width="150">
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.type"
                      :readonly="true"/>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="method"
                  label="比较方法"
                  min-width="150">
                  <template slot-scope="scope">
                    <el-select
                      v-if="type_if"
                      v-model="scope.row.method"
                      placeholder="请选择">
                      <el-option
                        v-for="item in methods"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        style="height: 40px;"/>
                    </el-select>
                    <el-select
                      v-if="type_else"
                      v-model="scope.row.method"
                      placeholder="请选择">
                      <el-option
                        v-for="item in methods1"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        style="height: 40px;"/>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="value"
                  label="变量值"
                  min-width="150">
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.value"/>
                  </template>
                </el-table-column>
                <el-table-column
                  :render-header="renderHeader"
                  label="操作"
                  width="120"
                  align="center">
                  <template slot-scope="scope">
                    <el-button
                      type="danger"
                      @click="deleteItem(scope.row,scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- <el-form-item
                label="变量名"
                prop="names">
                <el-input
                  v-model="names"
                  :disabled="true">
                  <el-button
                    slot="append"
                    icon="el-icon-search"
                    align="right"
                    @click="handleSearchName"/>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item
                label="变量类型"
                prop="type">
                <el-input
                  :disabled="true"
                  v-model="conditionsObj.type"/>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item
                label="比较方法"
                prop="method">
                <el-select
                  :disabled="isTZZ"
                  v-model="conditionsObj.method "
                  placeholder="请选择比较方法"
                  clearable>
                  <el-option
                    v-for="item in methods"
                    :value="item.value"
                    :key="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item
                prop="value"
                label="变量值">
                <el-input
                  :disabled="isTZZ"
                  v-model="conditionsObj.value"/>
              </el-form-item>
            </el-col>
            <div
              v-if="ruleForm.updateType==1?true:false"
              style="font-size:30px;cursor: pointer;">
              <i
                style="margin-left:50px;margin-top:6px;color:#f78989;"
                type="error"
                class="el-icon-remove-outline"
                @click="deleteItem()"/>
              <i
                style="margin-left:25px;color:#85ce61;"
                type="success"
                class="el-icon-circle-plus-outline"
                @click="addItem()"/>
            </div> -->
          </el-col></div>
          <el-col :span="8">
            <el-form-item
              style="display:none"
              prop="dimension">
              <el-input
                v-model="ruleForm.dimension"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              style="display:none"
              prop="paramType">
              <el-input
                :v-model="ruleForm.paramType"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              style="display:none"
              prop="configInfo">
              <el-input
                :v-model="ruleForm.configInfo"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item
          label="备注"
          prop="remark">
          <el-input
            v-model="ruleForm.remark"
            type="textarea"/>
        </el-form-item>
      </el-form>

      <!-- 位置曲线配置 -->
      <el-dialog
        :visible.sync="innerVisible"
        :before-close="handleClose"
        class="u-dialog"
        width="30%"
        title="位置API信息"
        append-to-body>
        <div class="layout-one-input dictionary-one-select">
          <el-select
            v-model="positiondata"
            placeholder="请选择对应的位置信息">
            <el-option
              v-for="(item, index) in positionOptions"
              :key="index"
              :value="item.value"
              :label="item.label"/>
          </el-select>
        </div>
        <span
          slot="footer"
          class="dialog-footer">
          <el-button
            type="primary"
            size="small"
            @click="savePosition">确 定</el-button>
        </span>
      </el-dialog>
      <span
        slot="footer"
        class="dialog-footer">
        <el-button
          :loading="submitLoading"
          type="primary"
          size="small"
          @click="onSubmit">确 定</el-button>
        <el-button
          :disabled="submitLoading"
          type="primary"
          size="small"
          @click="resetForm('formInfo')">取 消</el-button>
      </span>
    </el-dialog>
    
    <el-dialog
      :visible.sync="dialogVisibleName"
      :modal="true"
      :append-to-body = "true"
      class="u-dialog"
      title="变量名"
      width="900px">
      <div style="overflow: hidden;">
        <div style="width: 350px;float: left;">
          <el-tree
            ref="tree"
            :data="treeMgrListName"
            :expand-on-click-node="false"
            :props="defaultProps"
            :filter-node-method="filterNode"
            draggable
            highlight-current
            node-key="id"
            @node-click="handleNodeClickName"
          >
            <span
              slot-scope="{ node, data }"
              class="custom-tree-node"
            >
              <span :title="'GROUPID：'+data.id">{{ node.label }}</span>
            </span>
          </el-tree>
        </div>
        <div style="width: 490px;float: right;">
          <div>
            <el-input
              v-model="queryName"
              style="width: 400px;float: left;"
              placeholder="请输入变量名"
              clearable/>
            <el-button
              style="width: 80px;float: right;"
              type="primary"
              @click="handleNodeClickAll">查询</el-button>
          </div>
          <el-table
            v-loading="loadingBL"
            :data="paginationsList"
            :header-cell-style="{background: '#17316E',color: '#eae8c5',border:'0.1px solid #17316E'}"
            border
            @row-click="handleRowClick">
            <el-table-column
              prop="aliasName"
              label="变量名"
              width="290"
              show-overflow-tooltip/>
            <el-table-column
              prop="desc"
              width="200"
              label="变量描述"
              show-overflow-tooltip/>
          </el-table>
          <div>
            <el-pagination
              :current-page="nameAll.page"
              :page-sizes="nameAll.pageSizes"
              :page-size="nameAll.size"
              :total="nameAll.total"
              :pager-count="5"
              style="white-space: normal !important;text-align:center;"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChangeNames"
              @current-change="handleCurrentChangeCoilIdNames"/>
          </div>
        </div>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import axios from 'axios'
import commonURL from '@/lib/commonURL'
import {
  GET_CLIENT_FIND_BASIC_DATA_CONFIG_BY_TYPE // 查询单个订单类型
} from '@/lib/ApiURL'
import moment from 'moment'
import { post, fixMenus, findBasicDataConfigByType } from '@/lib/Util'
import eventBus from '@/utils/eventBus'

export default {
  filters: {
    timeFormat: function(value) {
      return moment(value).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  data() {
    return {
      request: 2,
      radio: '2',
      dialogVisibleBtn: false,
      dateFg: false,
      monthFg: true,
      currentCoil1: '',
      currentCoil2: '',
      type_if: true,
      type_else: false,
      loadingBL: false,
      dialogVisibleFile: false,
      dialogVisibleName: false,
      dialogRecalculation: false,
      dialogAddRecal: false,
      tableDataRecal: [],
      variablesRecal: [],
      EventRecal: [],
      findAddForm: {
        event: '',
        id: '',
        variables: []
      },
      flagName: 0,
      subscript: {},
      findForm: {},
      queryAllName: '',
      nodePathClick: '',
      queryName: '',
      treeMgrListName: [],
      fileLists: [],
      formData: null,
      isCollapse: '',
      handleText: '隐藏搜索<',
      hideOrShow: true,
      filterText: '',
      list: [],
      eventShow: false,
      isTZZ: false,
      enableDis: true,
      tableData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      }, // 树配置
      treeMgrList: [], // 树信息v
      findInfo: {
        enable: false,
        condition: ''
      }, // 查询条件
      parent: {},
      pageIndex: 1,
      pageSize: 16,
      inputInfo: '', // 查询内容
      total: 0,
      dialogVisible: true, //弹窗
      isDataResource: false,
      startEventData: true,
      nameAll: {
        size: 10,
        total: 0,
        page: 1,
        currentPage: 0,
        pageSizes: [10, 20, 50, 100]
      },
      Recal: {
        size: 10,
        total: 0,
        page: 1,
        currentPage: 0,
        pageSizes: [10, 20, 50, 100]
      },
      matType: '', //物料号类型
      method: '', //统计方法
      names: '', //变量名
      proOptions: [],
      paginationsList: [],
      paginations: {
        page_index: 1, //当前页
        total: 0, //总数
        page_size: 50000, //一页显示多少
        page_sizes: [50, 100, 150, 200], //每页显示多少条
        layout: 'total, sizes, prev, pager, next'
      },
      conditionsList: {
        request: 'rest',
        timeVar: {
          name: '',
          format: ''
        },
        method: '',
        conditions: []
      },
      // conditionsObj: {
      //   name: '',
      //   value: '',
      //   method: '',
      //   type: ''
      // },
      optionsRecal: [
        {
          value: 'plate_no_dsrtrkw',
          label: 'plate_no_dsrtrkw'
        },
        {
          value: 'len_dsrtrkw',
          label: 'len_dsrtrkw'
        }
      ],
      methodCount: [
        {
          value: 'sum',
          label: 'sum'
        },
        {
          value: 'avg',
          label: 'avg'
        }
      ],
      methods: [
        {
          value: '==',
          label: '=='
        },
        {
          value: '!=',
          label: '!='
        },
        {
          value: '<',
          label: '<'
        },
        {
          value: '>',
          label: '>'
        },
        {
          value: '<=',
          label: '<='
        },
        {
          value: '>=',
          label: '>='
        },
        {
          value: 'in',
          label: 'in'
        },
        {
          value: 'not in',
          label: 'not in'
        },
        {
          value: 'like',
          label: 'like'
        }
      ],
      methods1: [
        {
          value: '==',
          label: '=='
        },
        {
          value: '!=',
          label: '!='
        },
        {
          value: 'in',
          label: 'in'
        },
        {
          value: 'not in',
          label: 'not in'
        },
        {
          value: 'like',
          label: 'like'
        }
      ],
      ruleForm: {
        dimension: 3,
        paramType: 3,
        configInfo: {},
        dataResource: '',
        dataSource: '',
        aliasName: '', // 别名
        addInfo: [], // 附加消息
        dataResourceType: null,
        dataType: '', // 数据类型
        desc: '', //描述
        group: '',
        groupType: '', //分组
        curveType: '', //曲线标识
        dataInterface: '', //数据接口
        startEvent: '', //开始事件
        endEvent: '', //结束事件
        tOC: '', //创建时间
        tOM: '', //修改时间
        name: '',
        version: 0, //版本
        plant: '',
        procedure: null,
        prodFactors: null,
        updateCycle: '',
        updateType: 2, //更新方式
        copyable: '', // 变量配置
        varType: '', //变量类型
        updateEvent: '', //更新事件
        enable: false, //是否启用
        remark: ''
      },
      treeInfo: {},
      nameInfo: {
        name: '',
        id: ''
      },
      treeDataInfo: {
        plant: '1',
        groupType: '',
        group: ''
      },
      searchInfo: {},
      flag: true,
      dialogFlag: false,
      clickTreeInfo: {}, // 被点击的tree信息
      trans: {
        left: 0,
        top: 0
      },
      menu: false,
      menuVisable: false,
      formInfo: {}, // 右键菜单
      rightClickInfo: {},
      rightFlag: true, // 用来添加分组是新增、编辑
      groupProps: {
        value: 'id', //匹配响应数据中的id
        label: 'name', //匹配响应数据中的name
        children: 'children' //匹配响应数据中的children
      }, // 设置级联选择器的属性
      findGroupInfo: [], //分组信息
      innerVisible: false, // 内层弹窗
      positionOptions: [],
      positiondata: '', // 位置曲线信息
      addinfoVisable: false, // 附加消息弹窗
      basicDataItem: [], // 配置附加消息
      passNoOption: [],
      eventArray: [], // 事件数组
      plantArray: [], // 产线分组
      procedureArray: [], // 工序分组
      prodFactorsArray: [], // 生产要素
      varTypeArray: [], // 变量类型
      dataTypeArray: [], // 数据类型
      apiArray: [], //数据API
      // dataInterfaceArray: [], //数据接口
      dataInterfaceArray: [], //数据接口
      marTypeArray: [], //物料号类型
      timeVarArray: [],
      dataResourceTypeArray: [], // 数据源
      rules: {
        name: [{ required: true, message: '请输入变量名称', trigger: 'blur' }],
        plant: [{ required: true, message: '请选择产线', trigger: 'blur' }],
        customGroupName: [
          { required: true, message: '请选择分组信息', trigger: 'blur' }
        ],
        aliasName: [
          { required: true, message: '请输入变量别名', trigger: 'blur' }
        ],
        varType: [
          { required: true, message: '请输入变量类型', trigger: 'blur' }
        ]
      },
      copyable: [], //变量配置
      updateEvent: [], // 更新事件
      startEvent: [], //开始事件
      dataInterface: [], //数据接口
      endEvent: [], //结束事件
      dialogVarLine: false, //查询一定时间范围内变量数据——曲线弹框
      lineVar: '', //当前选中变量
      titleLine: '', //弹框表头
      timeLine: '', //时间范围
      ecahartsKey: 0, //echarts更新
      lineOption: {
        daylegend: { show: true },
        lineSeries: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            smooth: true
          }
        ],
        lineXaxis: {
          type: 'category',
          axisLine: {
            lineStyle: {
              color: '#EAE8C5'
            }
          },
          data: []
        },
        lineYaxis: [
          {
            type: 'value',
            min: 0,
            axisLine: {
              lineStyle: {
                color: '#EAE8C5'
              }
            }
          }
        ]
      }, //查询一定时间范围内变量曲线
      loading: false,
      isCopy: false,
      aliasNameFlag: false, // 复制设置别名
      handleType: null, // 启用判断是否重名
      GETDATAAPIData: [], //获取数据API配置
      positonDataUrl: '',
      dialogSingle: {
        dialogSingleVarLine: false, //查询单点值根据变量物料号
        SingleVarCoilId: '', //需要查询数据的物料号
        slectSingleVal: '', //选中的变量
        slectSingleValData: '', //选中的变量返回值
        steelArr: [], //物料号
        steelInfo: '',
        findCoilNoPage: {
          startTime: '',
          endTime: '',
          size: 5,
          total: 0,
          page: 1,
          currentPage: 0
        } //物料号分页信息
      },
      updateEventPropss: {
        value: 'id', //匹配响应数据中的id
        label: 'name', //匹配响应数据中的name
        children: 'children' //匹配响应数据中的children
      },
      updateEventProps: {
        value: 'value', //匹配响应数据中的id
        label: 'label', //匹配响应数据中的name
        children: 'children' //匹配响应数据中的children
      }, // 设置级联选择器的属性
      timeVarArrayProps: {
        value: 'value',
        label: 'label',
        children: 'children'
      },
      dataInterFaceProps: {
        value: 'value', //匹配响应数据中的id
        label: 'label', //匹配响应数据中的name
        children: 'children' //匹配响应数据中的children
      }, // 设置级联选择器的属性
      commonUrl: commonURL,
      submitLoading: false
    }
  },
  watch: {
    'ruleForm.dataResource': {
      handler(newVal, oldVal) {
        if (newVal !== this.positonDataUrl) {
          this.ruleForm.positiondata = ''
        }
      }
    },
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    updateEvent: function(newVal, oldVal) {
      if (newVal.length > 0) {
        this.isDataResource = true
      } else {
        this.isDataResource = false
      }
    },
    //数据接口
    dataInterface: function(newVal, oldVal) {
      if (newVal.length > 0) {
        this.isDataResource = true
      } else {
        this.isDataResource = false
      }
    },
    //变量配置
    copyable: function(newVal, oldVal) {
      if (newVal.length > 0) {
        this.isDataResource = true
      } else {
        this.isDataResource = false
      }
    },
    //开始事件
    startEvent: function(newVal, oldVal) {
      if (newVal.length > 0) {
        this.isDataResource = true
      } else {
        this.isDataResource = false
      }
    },
    //结束事件
    endEvent: function(newVal, oldVal) {
      if (newVal.length > 0) {
        this.isDataResource = true
      } else {
        this.isDataResource = false
      }
    },
    //打开弹窗
    dialogVisible: function(newVal, oldVal) {
      if (newVal == false) {
        this.isDataResource = false
        this.isTZZ = false
      }
    }
  },
  created() {
    this.GETDATAAPI()
    this.getTreeData()
    this.defaultDate()
  },
  mounted() {
    this.getDataBasc()
    eventBus.$on('isCollapse', info => {
      this.isCollapse = info
    })
    if (
      this.$route.query.isCollapse == 'false' ||
      this.$route.query.isCollapse == false
    ) {
      this.isCollapse = false
    } else if (
      this.$route.query.isCollapse == 'true' ||
      this.$route.query.isCollapse == true
    ) {
      this.isCollapse = true
    }
  },
  methods: {
    recalculation() {
      this.handleSearchClickRecal()
      this.dialogRecalculation = true
    },
    indexMethod(index) {
      return index + 1
    },
    handleSearchClickRecal() {
      this.Recal.pageIndex = 1
      this.Recal.pageSize = 10
      this.findAllRecal()
    },
    async findAllRecal() {
      let res = await post(this.commonUrl.findAllByPageRecal, {
        event: this.queryAllName,
        page: this.Recal.pageIndex,
        size: this.Recal.pageSize
      })
      this.tableDataRecal = res.data.content
      this.Recal.total = res.data.totalElements
    },

    timeVarType() {
      if (this.conditionsList.timeVar.name.length != 0) {
        var configInfo2 = ''
        for (var i = 0; i < this.conditionsList.timeVar.name.length; i++) {
          if (i > 0) {
            var aaa = configInfo2
            configInfo2 = aaa + '///' + this.conditionsList.timeVar.name[i]
          } else {
            configInfo2 = this.conditionsList.timeVar.name[i]
          }
        }
        this.conditionsList.timeVar.name = this.conditionsList.timeVar.name[
          this.conditionsList.timeVar.name.length - 1
        ]
        if (!this.ruleForm['configInfo'])
          this.ruleForm['configInfo'] = {
            timeVar: configInfo2
          }
        else this.ruleForm['configInfo']['timeVar'] = configInfo2
      } else {
        this.ruleForm.timeVar = ''
      }
      if (
        this.conditionsList.timeVar.name != '' &&
        this.conditionsList.timeVar.name != null
      ) {
        // for (let i = 0; i < this.timeVarArrays.length; i++) {
        //   if (this.conditionsList.timeVar.name == this.timeVarArrays[i].value) {
        //     this.conditionsList.timeVar.format = this.timeVarArrays[i].dateType
        //   }
        // }
      }
    },

    handleEditRecals(row) {
      this.findAddForm = row
      this.findAddForm.variables = JSON.parse(row.variables)
      this.EventRecal = row.event
      this.dialogAddRecal = true
    },

    handleSizeChangeRecal(val) {
      this.Recal.size = val
      this.findAllRecal()
    },

    handleCurrentChangeCoilIdRecal(val) {
      this.Recal.page = val
      this.findAllRecal()
    },

    addDataRecal() {
      this.findAddForm = {
        event: '',
        id: '',
        variables: []
      }
      this.EventRecal = ''
      this.dialogAddRecal = true
    },

    quxiao() {
      this.dialogAddRecal = false
      this.findAllRecal()
    },

    async handleEditRecal() {
      let res = await post(this.commonUrl.saveRecal, {
        varNames: this.findAddForm.variables,
        event: this.findAddForm.event
      })
      if (res.code == 200) {
        this.$message({
          type: 'success',
          message: '保存成功'
        })
        this.dialogAddRecal = false
      }
      this.handleSearchClickRecal()
    },
    handleDeleteRecal(row) {
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          post(this.commonUrl.deleteRecal, {
            id: row.id
          }).then(res => {
            if (res) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
            }
            this.findAllRecal()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    // async handleDeleteRecal(row) {
    //   let res = await post(this.commonUrl.deleteRecal, {
    //     id: row.id
    //   })
    //   if (res.code == 200) {
    //     this.$message({
    //       type: 'success',
    //       message: '操作成功'
    //     })
    //   }
    //   this.handleSearchClickRecal()
    // },

    renderHeader(h, params) {
      let a = [
        h('el-button', {
          props: {
            type: 'success',
            icon: 'el-icon-plus',
            circle: true
          },
          on: {
            click: () => {
              this.addItem()
            }
          }
        })
      ]
      return h('div', a)
    },

    addItem() {
      this.conditionsList.conditions.push({
        name: '',
        value: '',
        method: '',
        type: ''
      })
    },

    deleteItem(row, index) {
      this.conditionsList.conditions.splice(index, 1)
    },

    importUp() {
      this.dialogVisibleFile = true
    },
    handleChange(file, fileList) {
      this.fileLists.push(file)
    },
    timeFlag() {
      if (this.radio == '1') {
        this.dateFg = true
        this.monthFg = false
        this.querySingleVarCoilId('1')
      } else if (this.radio == '2') {
        this.dateFg = false
        this.monthFg = true
      }
    },
    // 文件上传
    submitUploadFile() {
      for (let i = 0; i < this.fileLists.length; i++) {
        this.formData.append('file', this.fileLists[i].raw)
      }

      axios
        .create({
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        .post(this.commonUrl.uploadFile, this.formData)
        .then(result => {
          if (result.status === 200) {
            this.formData = new window.FormData()
            this.fileLists = []
            this.$refs.upload.submit()
            this.dialogVisibleFile = false
          } else {
            this.fileLists = []
            this.formData = new window.FormData()
            this.$refs.upload.submit()
          }
        })
        .catch(err => {
          info.onError() // 调用 onError
        })
    },
    hideShow() {
      this.hideOrShow = !this.hideOrShow
      if (this.hideOrShow) {
        this.handleText = '隐藏搜索＜'
      } else {
        this.handleText = '显示搜索＞'
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    //时间格式化
    dateFormat: function(row, column) {
      var date = row[column.property]
      if (date == undefined) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    findSteel() {
      this.dialogSingle.findCoilNoPage.page = 1
      this.findByCoilNo()
    },
    // //输入框查询物料号
    // findByCoilNo() {
    //   if (
    //     this.dialogSingle.findCoilNoPage.startTime != null &&
    //     this.dialogSingle.findCoilNoPage.endTime != null
    //   ) {
    //     if (
    //       new Date(this.dialogSingle.findCoilNoPage.endTime).getTime() -
    //         new Date(this.dialogSingle.findCoilNoPage.startTime).getTime() >
    //       31 * 24 * 60 * 60 * 1000
    //     ) {
    //       this.$message.error('查询时间不能超过31天！')
    //       return false
    //     }
    //     let startTime = moment(
    //       this.dialogSingle.findCoilNoPage.startTime
    //     ).format('YYYY-MM-DD HH:mm:ss')
    //     let endTime = moment(this.dialogSingle.findCoilNoPage.endTime).format(
    //       'YYYY-MM-DD HH:mm:ss'
    //     )
    //     post(this.commonUrl.findByCondition, {
    //       startTime: startTime,
    //       endTime: endTime,
    //       size: this.dialogSingle.findCoilNoPage.size,
    //       page: this.dialogSingle.findCoilNoPage.page,
    //       matType: '5',
    //       matId: this.dialogSingle.steelInfo
    //     }).then(res => {
    //       this.dialogSingle.findCoilNoPage.total = res.totalElements
    //       this.dialogSingle.steelArr = res.content
    //     })
    //   } else {
    //     this.$message.error('查询时间不能为空')
    //   }
    // },
    handleCurrentChangeCoilId(val) {
      this.dialogSingle.findCoilNoPage.page = val
      // this.findByCoilNo()
    },
    // 变量名称失去焦点事件
    handleNameClick() {
      if (this.ruleForm.dataResource === this.positonDataUrl) {
        this.findPositionCurveByVarName({ varName: this.ruleForm.name })
      }
    },
    // 获得数据配置
    getDataBasc() {
      findBasicDataConfigByType('EVENTS').then(res => {
        // 事件数组
        this.eventArray = fixMenus(res)
      })
      findBasicDataConfigByType('DATAAPI').then(res => {
        //数据API
        this.apiArray = fixMenus(res)
      })
      findBasicDataConfigByType('MATTYPE').then(res => {
        //物料号类型
        this.marTypeArray = res
      })
      findBasicDataConfigByType('TIMEVAR').then(res => {
        //物料号类型
        this.timeVarArray = fixMenus(res)
        this.timeVarArrays = res
      })
      findBasicDataConfigByType('PLANT').then(res => {
        // 产线
        this.plantArray = res
      })
      findBasicDataConfigByType('VAR').then(res => {
        // varTypeArray: [], // 变量类型
        res.map(item => {
          item.value = Number(item.value)
        })
        this.varTypeArray = res
      })
      findBasicDataConfigByType('DATATYPE').then(res => {
        // dataTypeArray: [], // 数据类型
        this.dataTypeArray = res
      })
      findBasicDataConfigByType('singleVariableConfig').then(res => {
        this.copyable = res // 变量配置
      })
      // 获得附加消息
      findBasicDataConfigByType('ADDINFO').then(res => {
        for (const item of res) {
          this.passNoOption.push({
            label: item.label,
            value: item.value
          })
        }
      })
      // 位置信息
      findBasicDataConfigByType('POSUTION').then(res => {
        if (res) {
          for (const item of res) {
            this.positionOptions.push({
              label: item.label,
              value: item.value
            })
          }
        }
      })
    },
    // 键盘事件
    KeyUpEnter() {
      this.flag = true
      this.pageIndex = 1
      this.pageSize = 16
      this.findAll()
    },
    // 分组信息编辑
    handlefindGroupInfo() {
      if (this.ruleForm.customGroupName) {
        for (const item of this.findGroupInfo) {
          if (
            item.id ===
              this.ruleForm.customGroupName[
                this.ruleForm.customGroupName.length - 1
              ] &&
            this.ruleForm.customGroupName
          ) {
            this.ruleForm.groupType = item.groupType
            this.ruleForm.groupId = item.id
            this.ruleForm.group = item.name
            this.ruleForm.nodePath = item.nodePath
          }
        }
      }
    },
    /**
     * @description 获得tree结构
     */
    async getTreeData() {
      let res = await post(commonURL.findGroups, {
        groupType: 'STATISTICS'
      })
      this.findGroupInfo = res
      this.list = []
      //展示到第三级菜单
      for (var i = 0; i < this.findGroupInfo.length; i++) {
        //判断分组中的每一个 i 的名称是否 等于 板材事业部
        if (this.findGroupInfo[i].name == '板材事业部')
          this.list.push(this.findGroupInfo[i].id)
      }
      this.treeMgrList = fixMenus(res)
      if (this.treeMgrList.length != 0) {
        this.handleNodeClick(this.treeMgrList[0])
      }
    },
    /**
     * @description 点击tree节点
     */
    async handleNodeClick(data, node) {
      this.clickTreeInfo = data
      this.searchInfo.groupId = data.id
      this.pageIndex = 1
      this.pageSize = 16
      this.findDataInfo()
    },
    // 鼠标右键点击时触发该事件
    mouseRightCLick(even, data, val, node) {
      this.trans.left = even.clientX + 'px'
      this.trans.top = even.clientY + 'px'
      this.menu = true
      this.rightClickInfo = data
      document.addEventListener('click', this.Listen)
    },
    Listen() {
      document.removeEventListener('click', this.Listen)
      this.menu = false
    },
    // 右键菜单选项
    selsectMenu(val) {
      switch (val) {
        case 1:
          this.formInfo = {}
          this.menuVisable = true
          this.rightFlag = true
          break
        case 2:
          this.deleteResource(this.rightClickInfo)
          break
        case 3:
          this.menuVisable = true
          this.rightFlag = false
          this.formInfo.name = this.rightClickInfo.name
      }
    },
    change(e) {
      this.$forceUpdate()
    },
    async handleSubmit() {
      // 防止重复提交
      if (this.submitLoading) return

      this.submitLoading = true

      let data = {}
      let resinfo = ''
      if (this.rightFlag) {
        data = {
          name: this.formInfo.name,
          pId: this.rightClickInfo.id,
          nodePath: this.rightClickInfo.nodePath,
          groupType: 'STATISTICS'
        }
        resinfo = '添加成功'
      } else {
        data = {
          name: this.formInfo.name,
          pId: this.rightClickInfo.pId,
          id: this.rightClickInfo.id,
          nodePath: this.rightClickInfo.nodePath,
          groupType: 'STATISTICS'
        }
        resinfo = '编辑成功'
      }
      let res = await post(this.commonUrl.saveGroup, data)
      if (res) {
        this.$message({
          type: 'success',
          message: resinfo
        })
      }
      this.menuVisable = false
      this.getTreeData()

      return Promise.resolve(res)
    },
    /**
     * @author: wangHongFei
     * @description: 树组件删除
     */
    async deleteResource(data) {
      let res = await post(this.commonUrl.deleteById, { id: data.id })
      if (res === 'success') {
        this.$message({
          message: '删除成功',
          type: 'success'
        })
        this.getTreeData()
      } else {
        this.$message({
          message: '删除失败',
          type: 'error'
        })
      }
    },
    /**
     * @description 查询明细
     */
    async findDataInfo() {
      this.flag = false
      this.searchInfo.page = this.pageIndex
      this.searchInfo.size = this.pageSize
      let res = ''
      if (this.findInfo.enable) {
        res = await post(this.commonUrl.findGroupIdEn, {
          groupId: this.searchInfo.groupId,
          enable: this.findInfo.enable,
          page: this.pageIndex,
          size: this.pageSize
        })
      } else {
        res = await post(this.commonUrl.DataDictfindByConditions, {
          query: this.findInfo.condition,
          nodePath: this.searchInfo.groupId,
          enable: false,
          page: this.pageIndex,
          dimension: this.ruleForm.dimension,
          size: this.pageSize
        })
      }

      this.tableData = res.content
      for (var i = 0; i < this.tableData.length; i++) {
        if (this.tableData[i].desc != null) {
          var descs = this.tableData[i].desc
          descs = descs.substr(descs.lastIndexOf('_') + 1)
          this.tableData[i].descstr = descs
        } else {
          this.tableData[i].descstr = this.tableData[i].desc
        }
      }
      this.total = res.totalElements
    },
    async findAll() {
      if (this.flag) {
        let res = await post(this.commonUrl.DataDictfindByConditions, {
          query: this.findInfo.condition,
          nodePath: this.searchInfo.groupId,
          enable: true,
          page: this.pageIndex,
          dimension: this.ruleForm.dimension,
          size: this.pageSize
        })
        this.tableData = res.content
        for (var i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i].desc != null) {
            var descs = this.tableData[i].desc
            descs = descs.substr(descs.lastIndexOf('_') + 1)
            this.tableData[i].descstr = descs
          } else {
            this.tableData[i].descstr = this.tableData[i].desc
          }
        }
        this.total = res.totalElements
      }
    },
    // 编辑数据
    handleEdit(val) {
      if (typeof val.configInfo == 'string')
        val.configInfo = JSON.parse(val.configInfo)
      this.ruleForm.configInfo = val.configInfo
      // if (typeof val.addInfo == 'string') {
      //   if (val.addInfo != '') {
      //     val.addInfo = JSON.parse(val.addInfo)
      //     this.conditionsList = val.addInfo
      //   } else {
      //     this.conditionsList = {
      //       request: 'statistics',
      //       matType: '',
      //       method: '',
      //       conditions: []
      //     }
      //   }
      // } else if (val.addInfo == null) {
      //   this.conditionsList = {
      //     request: 'statistics',
      //     matType: '',
      //     method: '',
      //     conditions: []
      //   }
      // } else {
      let list = JSON.parse(val.addInfo)
      this.conditionsList = list[0].value
      // }
      if (this.conditionsList.timeVar == null) {
        this.conditionsList.timeVar = {
          name: '',
          format: ''
        }
      }
      if (this.conditionsList.request == 'statistics') {
        this.request = 1
      } else if (this.conditionsList.request == 'rest') {
        this.request = 2
      }
      if (val.mOP == 'TZZ') {
        this.isTZZ = true
      }
      if (val.paramType == '6') {
        this.isL2 = true
      } else {
        this.isL2 = false
      }
      this.eventShow = false
      this.$nextTick(() => {
        this.eventShow = true
      })
      this.isCopy = false
      let arr = []
      this.handleType = 'update'
      this.basicDataItem = [] //配置附加属性
      this.dialogFlag = false
      this.ruleForm = Object.assign({}, val)
      this.aliasNameFlag = false
      if (this.ruleForm.dataInterface !== '' && this.ruleForm.dataInterface) {
        //paramType + dataInterface进行拼接赋值给新变量 dataInterfaceTogether
        var dataInterfaceTogether =
          this.ruleForm.paramType + '/' + this.ruleForm.dataInterface
        //对新变量进行分割处理
        var dataInterfaceSplit = dataInterfaceTogether.split('/')
        var NewDataInterface = ''
        //遍历dataInterfaceSplit这个字段
        for (var i = 1; i < dataInterfaceSplit.length; i++) {
          //判断该NewDataInterface变量非空就拼接否则不拼接
          if (NewDataInterface != '') {
            NewDataInterface = NewDataInterface + '/' + dataInterfaceSplit[i]
          } else {
            NewDataInterface = dataInterfaceSplit[i]
          }
        }
        if (this.ruleForm.dimension == 0 || this.ruleForm.dimension == 1) {
          this.ruleForm.dimension = this.dimensionArray[0].value
        }
        if (this.ruleForm.dimension == 0 || this.ruleForm.dimension == 2) {
          this.ruleForm.dimension = this.dimensionArray[1].value
        }
        this.dataInterface = []
        this.dataInterface.push(dataInterfaceSplit[0])
        this.dataInterface.push(NewDataInterface)
      }
      if (this.ruleForm.updateEvent !== '' && this.ruleForm.updateEvent) {
        this.updateEvent = this.ruleForm.updateEvent.split('/')
      }
      if (this.ruleForm.startEvent !== '' && this.ruleForm.startEvent) {
        this.startEvent = this.ruleForm.startEvent.split('/')
      }
      if (this.ruleForm.endEvent !== '' && this.ruleForm.endEvent) {
        this.endEvent = this.ruleForm.endEvent.split('/')
      }
      arr = []
      this.dialogVisible = true

      let nodeid = []
      let data = []
      if (this.ruleForm.nodePath) {
        nodeid = this.ruleForm.nodePath.split('/')
        for (const item of nodeid) {
          if (item !== '') {
            data.push(Number(item))
          }
        }
        this.ruleForm.customGroupName = data
      }
      if (this.ruleForm.dataResource === this.positonDataUrl) {
        this.findPositionCurveByVarName({ varName: this.ruleForm.name })
      }
      if (val.configInfo.dataInterface) {
        this.dataInterface = val.configInfo.dataInterface.split('///')
      }
      if (val.configInfo.updateEvent) {
        this.updateEvent = val.configInfo.updateEvent.split('///')
      }
      if (val.configInfo.startEvent) {
        this.startEvent = val.configInfo.startEvent.split('///')
      }
      if (val.configInfo.endEvent) {
        this.endEvent = val.configInfo.endEvent.split('///')
      }
      this.handleUpdateEvent()
    },
    // 查询位置信息
    async findPositionCurveByVarName(val) {
      let res = await post(this.commonUrl.findPositionCurveByVarName, val)
      if (res === '') {
        this.ruleForm.positiondata = ''
        this.ruleForm.dataResource = ''
        this.positiondata = ''
      } else {
        this.$set(
          this.ruleForm,
          'positiondata',
          res.varLength + '/' + res.varLengthName
        )
        this.positiondata = res.varLength
      }
    },
    //时间显示
    defaultDate() {
      //获取新的时间(2019.4.12）
      let date = new Date()
      //获取当前时间的年份转为字符串
      let year = date.getFullYear().toString() //'2019'
      //获取月份，由于月份从0开始，此处要加1，判断是否小于10，如果是在字符串前面拼接'0'
      let month =
        date.getMonth() + 1 < 10
          ? '0' + (date.getMonth() + 1).toString()
          : (date.getMonth() + 1).toString() //'04'
      //获取天，判断是否小于10，如果是在字符串前面拼接'0'
      let da =
        date.getDate() < 10
          ? '0' + date.getDate().toString()
          : date.getDate().toString() //'12'
      //字符串拼接，开始时间，结束时间
      this.currentCoil1 = year + month + da
      this.currentCoil2 = year + month
      this.dialogSingle.findCoilNoPage.endTime =
        year + '-' + month + '-' + da + ' 23:23:59' //当天'2019-04-12 23:23:59'
      let WeekFirstDay = new Date(date - 7 * 1000 * 60 * 60 * 24)
      year = WeekFirstDay.getFullYear().toString() //'2019'
      //获取月份，由于月份从0开始，此处要加1，判断是否小于10，如果是在字符串前面拼接'0'
      month =
        WeekFirstDay.getMonth() + 1 < 10
          ? '0' + (WeekFirstDay.getMonth() + 1).toString()
          : (WeekFirstDay.getMonth() + 1).toString() //'04'
      //获取天，判断是否小于10，如果是在字符串前面拼接'0'
      da =
        WeekFirstDay.getDate() < 10
          ? '0' + WeekFirstDay.getDate().toString()
          : WeekFirstDay.getDate().toString() //'12'
      this.dialogSingle.findCoilNoPage.startTime =
        year + '-' + month + '-' + da + ' 00:00:00'
      // this.findByCoilNo()
    },
    // 查询变量数据
    async findVarData(val) {
      if (val.varType == 2) {
        this.defaultDate()
        this.dialogSingle.dialogSingleVarLine = true
        this.dialogSingle.slectSingleVal = val.aliasName || val.name
        this.dialogSingle.slectSingleValData = ''
        // this.findByCoilNo()
      } else if (val == '' || val.varType == 1) {
        this.loading = true
        if (val == '') {
          val = this.lineVar
        } else {
          this.lineVar = val.name
          this.titleLine = val.name
        }
        var startTime, endTime

        if (this.timeLine == '' || this.timeLine == null) {
          //（1min）内数据
          endTime = new Date().getTime() - 180000 //当前时间戳
          startTime = endTime - 60000 //往前推1min
          this.timeLine = [
            moment(startTime).format('YYYY-MM-DD HH:mm:ss'),
            moment(endTime).format('YYYY-MM-DD HH:mm:ss')
          ]
        } else {
          startTime = this.timeLine[0]
          endTime = this.timeLine[1]
        }

        let res = await post(this.commonUrl.queryTimeCurveVariablesByName, {
          // let res = await post('subject/findTCurves.idm', {
          tagNames: [this.lineVar],
          start: "'" + moment(startTime).format('YYYY-MM-DD HH:mm:ss') + "'",
          end: "'" + moment(endTime).format('YYYY-MM-DD HH:mm:ss') + "'"
        })

        let serData = []
        let xData = []

        if (res.data && res.data.length > 0) {
          let lineData = res.data
          for (var i = 0; i < lineData.length; i++) {
            serData.push(lineData[i].value)
            xData.push(lineData[i].ts)
          }
        } else {
          this.$message('数据为空')
        }
        this.lineOption.lineSeries[0].data = serData
        this.lineOption.lineXaxis.data = xData
        this.lineOption.lineYaxis[0].min = Math.min.apply(null, serData)
        this.ecahartsKey++
        this.loading = false
        this.dialogVarLine = true
      }
    },
    //查询单点数据
    async querySingleVarCoilId(dm) {
      if (dm == '1') {
        this.dialogSingle.slectSingleValData = await post(
          this.commonUrl.findSingleVariableValue,
          {
            matId: this.currentCoil1,
            varName: this.dialogSingle.slectSingleVal
          }
        )
      } else {
        this.dialogSingle.slectSingleValData = await post(
          this.commonUrl.findSingleVariableValue,
          {
            matId: this.currentCoil2,
            varName: this.dialogSingle.slectSingleVal
          }
        )
      }
    },
    lineDilog() {
      this.timeLine = '' //清空复位
      this.dialogVarLine = false
    },
    // 复制数据
    handlecopy(val) {
      this.isCopy = true
      if (
        this.copyable[0].copyable == 'true' ||
        this.copyable[0].copyable == true
      ) {
        if (val.varType === 2) {
          this.$message('变量类型为单点，不能复制')
          return
        }
      }
      if (typeof val.configInfo == 'string')
        val.configInfo = JSON.parse(val.configInfo)
      this.ruleForm.configInfo = val.configInfo
      // if (typeof val.addInfo == 'string') {
      //   if (val.addInfo != '') {
      //     val.addInfo = JSON.parse(val.addInfo)
      //     this.conditionsList = val.addInfo
      //   } else {
      //     this.conditionsList = {
      //       request: 'statistics',
      //       matType: '',
      //       method: '',
      //       conditions: []
      //     }
      //   }
      // } else if (val.addInfo == null) {
      //   this.conditionsList = {
      //     request: 'statistics',
      //     matType: '',
      //     method: '',
      //     conditions: []
      //   }
      // } else {
      //   this.conditionsList = val.addInfo
      // }
      let list = JSON.parse(val.addInfo)
      this.conditionsList = list[0].value
      if (this.conditionsList.request == 'statistics') {
        this.request = 1
      } else if (this.conditionsList.request == 'rest') {
        this.request = 2
      }
      if (val.mOP == 'TZZ') {
        this.isTZZ = true
      }
      if (val.paramType == '  6') {
        this.isL2 = true
      } else {
        this.isL2 = false
      }
      this.eventShow = false
      this.$nextTick(() => {
        this.eventShow = true
      })
      this.isCopy = false
      let arr = []
      this.handleType = 'add'
      this.dialogFlag = true
      this.aliasNameFlag = true
      this.ruleForm = Object.assign({}, val)
      delete this.ruleForm.id
      if (this.ruleForm.propType) {
        delete this.ruleForm.propType
      }
      if (this.ruleForm.propId) {
        delete this.ruleForm.propId
      }
      this.basicDataItem = [] //配置附加属性
      if (this.ruleForm.dataInterface !== '' && this.ruleForm.dataInterface) {
        //paramType + dataInterface进行拼接赋值给新变量 dataInterfaceTogether
        var dataInterfaceTogether =
          this.ruleForm.paramType + '/' + this.ruleForm.dataInterface
        //对新变量进行分割处理
        var dataInterfaceSplit = dataInterfaceTogether.split('/')
        var NewDataInterface = ''
        //遍历dataInterfaceSplit这个字段
        for (var i = 1; i < dataInterfaceSplit.length; i++) {
          //判断该NewDataInterface变量非空就拼接否则不拼接
          if (NewDataInterface != '') {
            NewDataInterface = NewDataInterface + '/' + dataInterfaceSplit[i]
          } else {
            NewDataInterface = dataInterfaceSplit[i]
          }
        }
        if (this.ruleForm.dimension == 0 || this.ruleForm.dimension == 1) {
          this.ruleForm.dimension = this.dimensionArray[0].value
        }
        if (this.ruleForm.dimension == 0 || this.ruleForm.dimension == 2) {
          this.ruleForm.dimension = this.dimensionArray[1].value
        }
        this.dataInterface = []
        this.dataInterface.push(dataInterfaceSplit[0])
        this.dataInterface.push(NewDataInterface)
      }
      if (this.ruleForm.updateEvent !== '' && this.ruleForm.updateEvent) {
        this.updateEvent = this.ruleForm.updateEvent.split('/')
      }
      if (this.ruleForm.startEvent !== '' && this.ruleForm.startEvent) {
        this.startEvent = this.ruleForm.startEvent.split('/')
      }
      if (this.ruleForm.endEvent !== '' && this.ruleForm.endEvent) {
        this.endEvent = this.ruleForm.endEvent.split('/')
      }
      arr = []
      this.dialogVisible = true
      let data = []
      if (this.ruleForm.nodePath) {
        nodeid = this.ruleForm.nodePath.split('/')
        for (const item of nodeid) {
          if (item !== '') {
            data.push(Number(item))
          }
        }
        this.ruleForm.customGroupName = data
      }
      if (this.ruleForm.dataResource === this.positonDataUrl) {
        this.findPositionCurveByVarName({ varName: this.ruleForm.name })
      }
      if (val.configInfo.dataInterface) {
        this.dataInterface = val.configInfo.dataInterface.split('///')
      }
      if (val.configInfo.updateEvent) {
        this.updateEvent = val.configInfo.updateEvent.split('///')
      }
      if (val.configInfo.startEvent) {
        this.startEvent = val.configInfo.startEvent.split('///')
      }
      if (val.configInfo.endEvent) {
        this.endEvent = val.configInfo.endEvent.split('///')
      }
      arr = []
      this.dialogVisible = true
      let nodeid = []
      if (this.ruleForm.nodePath) {
        nodeid = this.ruleForm.nodePath.split('/')
        for (const item of nodeid) {
          if (item !== '') {
            data.push(Number(item))
          }
        }
        this.ruleForm.customGroupName = data
      }
      if (this.ruleForm.dataResource === this.positonDataUrl) {
        this.findPositionCurveByVarName({ varName: this.ruleForm.name })
      }
    },
    // 删除数据
    handleDelete(val) {
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          post(this.commonUrl.delDataDict, { id: val.id }).then(res => {
            if (res.data == '') {
              this.$message('删除成功')
            } else {
              this.$message(
                '删除失败，该变量已配置主题，主题ID为：' + res.data.toString()
              )
            }
            if (this.flag) {
              this.findAll()
            } else {
              this.findDataInfo()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleSearchClick() {
      this.flag = true
      this.pageIndex = 1
      this.pageSize = 16
      this.findAll()
    },
    // 打开添加弹窗
    addData() {
      this.basicDataItem = [] //配置附加属性
      this.handleType = 'add'
      this.ruleForm = {
        createUser: 'root', //添加弹窗默认显示用户名称
        dimension: 3,
        paramType: 3,
        addInfo: ''
      }
      this.conditionsList = {
        request: 'rest',
        timeVar: {
          name: '',
          format: ''
        },
        method: '',
        conditions: []
      }
      this.eventShow = false
      this.$nextTick(() => {
        this.eventShow = true
      })
      this.enableDis = true //点击添加默认 是否启用 为禁用
      this.updateEvent = '' // 重置更新事件
      this.startEvent = '' //重置开始事件
      this.dataInterface = '' //重置数据接口
      this.endEvent = '' //重置结束事件
      this.positiondata = '' // 重置位置曲线
      this.dialogFlag = true // 判断是添加还是编辑
      this.dialogVisible = true // 打开弹窗
      this.aliasNameFlag = false
    },
    handleEnableChange() {
      // 变量名称拥有 + 启用true
      if (!this.ruleForm.name || this.ruleForm.name === '') {
        this.$message('请输入变量名')
        this.ruleForm.enable = true
      }
      if (this.ruleForm.enable) {
        if (!this.ruleForm.aliasName || this.ruleForm.aliasName === '') {
          let data = {
            name: this.ruleForm.name,
            handleType: this.handleType
          }
          this.findVarName(data)
        }
      }
    },
    // 查询重复变量名
    findVarName(val) {
      post(this.commonUrl.checkVarNameIsRepeat, val).then(res => {
        if (res.code == 500) {
          this.$message(res.msg)
        }
      })
    },
    // 提交添加内容
    async sendInfo() {
      try {
        if (this.aliasNameFlag) {
          if (!this.ruleForm.aliasName || this.ruleForm.aliasName === '') {
            this.$message('复制变量必须设置别名')
            return Promise.reject('别名未设置')
          }
        }

        if (this.isCopy) {
          this.isCopy = false
          this.ruleForm.propType = ''
          this.ruleForm.propId = ''
        }
        if (!this.ruleForm.configInfo) this.ruleForm.configInfo = '{}'
        this.ruleForm.configInfo = JSON.stringify(
          this.ruleForm.configInfo || {}
        )
        let list = []
        let object = {
          name: 'additionalConditions',
          value: this.conditionsList
        }
        list.push(object)
        // this.ruleForm.addInfo = JSON.stringify(list)
        this.ruleForm.addInfo = list
        this.ruleForm.updateType = 2
        let res = await post(this.commonUrl.saveDataDict, this.ruleForm)
        if (this.dialogFlag) {
          if (res.code != 500) {
            this.$emit('aliasName', res.aliasName)
            this.$message({
              message: '添加成功',
              type: 'success'
            })
          } else {
            this.$message.error(res.msg)
          }
        } else {
          if (res.code != 500) {
            this.$emit('aliasName', res.aliasName)
            this.$message({
              message: '编辑成功',
              type: 'success'
            })
          } else {
            this.$message.error(res.msg)
          }
        }
        this.dialogVisible = false
        this.$refs['formInfo'].resetFields()
        this.updateEvent = [] // 更新事件
        this.startEvent = [] //开始事件
        this.dataInterface = [] //数据接口
        this.endEvent = [] //结束事件
        this.basicDataItem = [] //配置附加属性
        if (this.flag) {
          this.findAll()
        } else {
          this.findDataInfo()
        }

        return Promise.resolve(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    onSubmit() {
      // 防止重复提交
      if (this.submitLoading) return

      this.submitLoading = true

      if (this.isTZZ) {
        this.sendInfo().finally(() => {
          this.submitLoading = false
        })
      } else {
        this.$refs['formInfo'].validate(valid => {
          if (valid) {
            this.sendInfo().finally(() => {
              this.submitLoading = false
            })
          } else {
            this.submitLoading = false
            return false
          }
        })
      }
    },
    // 取削表单
    resetForm(formName) {
      this.dialogVisible = false
      this.ruleForm = {}
      this.positiondata = ''
      this.updateEvent = [] // 更新事件
      this.startEvent = [] //开始事件
      this.dataInterface = [] //数据接口
      this.endEvent = [] //结束事件
      this.basicDataItem = [] //配置附加属性
    },
    resetFormMenu() {
      this.menuVisable = false
      this.formInfo.name = ''
    },
    handleCurrentChange(val) {
      if (this.flag) {
        this.pageIndex = val
        this.findAll()
      } else {
        this.pageIndex = val
        this.findDataInfo()
      }
    },
    handleSizeChange(val) {
      if (this.flag) {
        this.pageSize = val
        this.findAll()
      } else {
        this.pageSize = val
        this.findDataInfo()
      }
    },

    async savePosition() {
      if (this.ruleForm.name === '' && this.positiondata === '') {
        this.$message({
          type: 'warning',
          message: '请填写变量名称或选择相应的位置曲线信息'
        })
        return
      }
      let res = await post(this.commonUrl.savePositionCurveToVarName, {
        varName: this.ruleForm.name, // 当前变量名
        varLength: this.positiondata, // 用户选择的位置曲线
        varLengthName: '' // 不用填写
      })
      this.$message({
        type: 'success',
        message: '保存成功'
      })
      this.innerVisible = false
      this.positionOptions.map(item => {
        if (item.value === this.positiondata) {
          this.ruleForm.positiondata = item.value + '/' + item.label
        }
      })
    },
    handleClose() {
      this.positiondata = ''
      this.innerVisible = false
    },
    // 添加附加消息
    handleAddInfo() {
      //启动附加消息弹窗
      this.addinfoVisable = true
      if (this.ruleForm.addInfo) {
        if (this.ruleForm.addInfo instanceof Object) {
          this.basicDataItem = this.ruleForm.addInfo
        } else if (typeof this.ruleForm.addInfo == 'string') {
          this.basicDataItem = JSON.parse(this.ruleForm.addInfo)
        }
      }
      this.addContentItem()
      this.ruleForm.addInfo = this.basicDataItem
    },
    // 删除附加消息
    reduceContentItem(val) {
      this.basicDataItem.splice(val, 1)
    },
    // 添加附加消息
    addContentItem() {
      this.basicDataItem.push({
        name: 'additionalConditions',
        value: JSON.stringify({
          request: 'statistics',
          timeVar: {
            name: '',
            format: ''
          },
          method: 'sum',
          conditions: [
            {
              name: 'value1',
              value: 'value1',
              method: '==/!=/</>/<=/>=',
              type: 'number'
            },
            {
              name: 'value1',
              value: 'value1',
              method: 'equals',
              type: 'string'
            }
          ]
        })
      })
    },
    // 保存附加消息设置
    saveContentItem() {
      this.ruleForm.addInfo = this.basicDataItem
      this.addinfoVisable = false
    },
    //查询数据API配置
    async GETDATAAPI() {
      var info = await post(
        GET_CLIENT_FIND_BASIC_DATA_CONFIG_BY_TYPE,
        {
          type: 'DATAAPI'
        },
        'no'
      )
      this.GETDATAAPIData = JSON.parse(info.content)
      for (var i = 0; i < this.GETDATAAPIData.length; i++) {
        let midData = JSON.parse(this.GETDATAAPIData[i])
        if (midData.label == '位置API') {
          this.positonDataUrl = midData.value
        }
      }
    },
    //enable是否启用的判断方法
    handleUpdateEvent() {
      //判断更新方式和更新事件
      if (this.request == 1 && this.updateEvent != '') {
        this.enableDis = false
      } else if (
        //判断更新方式、更新事件、数据接口
        this.request == 2 &&
        this.updateEvent != '' &&
        this.dataInterface != '' &&
        (this.ruleForm.paramType == 2 ||
          this.ruleForm.paramType == 4 ||
          this.ruleForm.paramType == 5)
      ) {
        this.enableDis = false
      } else if (
        //判断更新方式、更新事件、数据接口、开始事件、结束事件
        this.request == 2 &&
        this.updateEvent != '' &&
        this.dataInterface != '' &&
        (this.ruleForm.paramType == 1 || this.ruleForm.paramType == 3) &&
        this.startEvent != '' &&
        this.endEvent != ''
      ) {
        //是否启用为启用
        this.enableDis = false
      } else {
        //是否启用为禁用
        this.enableDis = true
      }
    },
    // 更新方式
    handleUpdateType() {
      //判断更新方式的值去进行赋空
      if (this.request == 1 || this.request == 2) {
        //数据接口
        this.dataInterface = []
        this.ruleForm.dataInterface = ''
        this.name = ''
      }
      if (this.request == 2) {
        this.conditionsList = {
          request: 'rest'
        }
      } else {
        this.conditionsList = {
          request: 'statistics',
          timeVar: {
            name: '',
            format: ''
          },
          method: '',
          conditions: []
        }
      }
    },
    // 更新事件
    handleUpdataChange() {
      //判断更新事件长度不等于0
      if (this.updateEvent.length !== 0) {
        this.ruleForm.updateEvent =
          this.updateEvent[0] +
          '/' +
          this.updateEvent[this.updateEvent.length - 1]
        var configInfo2 = ''
        for (var i = 0; i < this.updateEvent.length; i++) {
          if (i > 0) {
            configInfo2 = configInfo2 + '///' + this.updateEvent[i]
          } else {
            configInfo2 = this.updateEvent[i]
          }
        }
        if (!this.ruleForm['configInfo'])
          this.ruleForm['configInfo'] = {
            updateEvent: configInfo2
          }
        else this.ruleForm['configInfo']['updateEvent'] = configInfo2
      } else {
        this.ruleForm.updateEvent = ''
      }
    },
    // 数据接口
    dataResourceChange() {
      //判断更新事件长度不等于0
      if (this.dataInterface.length !== 0) {
        this.ruleForm.paramType = this.dataInterface[0]
        this.ruleForm.dataInterface = this.dataInterface[
          this.dataInterface.length - 1
        ]
        var configInfo2 = ''
        for (let i = 0; i < this.dataInterface.length; i++) {
          if (i == 0) {
            configInfo2 = this.dataInterface[i]
          } else {
            configInfo2 = configInfo2 + '///' + this.dataInterface[i]
          }
        }
        if (!this.ruleForm['configInfo'])
          this.ruleForm['configInfo'] = {
            dataInterface: configInfo2
          }
        else this.ruleForm['configInfo']['dataInterface'] = configInfo2
        // debugger
        //判断数据接口有无值都清空 开始/结束 事件
        if (this.dataInterface == '' || this.dataInterface == null) {
          //开始事件
          this.startEvent = []
          this.ruleForm.startEvent = ''
          //结束事件
          this.endEvent = []
          this.ruleForm.endEvent = ''
        }
      } else {
        this.ruleForm.dataInterface = ''
      }
    },
    // 开始事件
    handleUpdataChange1() {
      //判断开始事件长度不等于0
      if (this.startEvent.length !== 0) {
        this.ruleForm.startEvent =
          this.startEvent[0] + '/' + this.startEvent[this.startEvent.length - 1]
        var configInfo2 = ''
        for (var i = 0; i < this.startEvent.length; i++) {
          if (i > 0) {
            configInfo2 = configInfo2 + '///' + this.startEvent[i]
          } else {
            configInfo2 = this.startEvent[i]
          }
        }
        if (!this.ruleForm['configInfo'])
          this.ruleForm['configInfo'] = {
            startEvent: configInfo2
          }
        else this.ruleForm['configInfo']['startEvent'] = configInfo2
      } else {
        this.ruleForm.startEvent = ''
      }
    },
    handleUpdataChangeRecal() {
      //判断开始事件长度不等于0
      if (this.EventRecal.length !== 0) {
        this.findAddForm.event = this.EventRecal[this.EventRecal.length - 1]
      } else {
        this.findAddForm.event = ''
      }
    },
    // 结束事件
    handleUpdataChange2() {
      //判断结束事件长度不等于0
      if (this.endEvent.length !== 0) {
        this.ruleForm.endEvent =
          this.endEvent[0] + '/' + this.endEvent[this.endEvent.length - 1]
        var configInfo2 = ''
        for (var i = 0; i < this.endEvent.length; i++) {
          if (i > 0) {
            configInfo2 = configInfo2 + '///' + this.endEvent[i]
          } else {
            configInfo2 = this.endEvent[i]
          }
        }
        if (!this.ruleForm['configInfo'])
          this.ruleForm['configInfo'] = {
            endEvent: configInfo2
          }
        else this.ruleForm['configInfo']['endEvent'] = configInfo2
      } else {
        this.ruleForm.endEvent = ''
      }
    },

    async handleSearchName(row) {
      if (row == 'bl') {
        this.flagName = 1
        let res = await post(commonURL.findGroups, { groupType: 'DICT' })
        this.treeMgrListName = fixMenus(res)
        this.nodePathClick = this.treeMgrListName[0].id
      } else if (row == 'js') {
        this.flagName = 3
        let res = await post(commonURL.findGroups, {
          groupType: 'STATISTICS'
        })
        this.treeMgrListName = fixMenus(res)
        this.nodePathClick = this.treeMgrListName[0].id
      } else {
        this.flagName = 2
        let res = await post(commonURL.findGroups, { groupType: 'DICT' })
        this.treeMgrListName = fixMenus(res)
        this.nodePathClick = this.treeMgrListName[0].id
        this.subscript = row
      }
      this.paginationsList = []
      this.nameAll.total = 0
      this.dialogVisibleName = true
    },

    handleRowClick(row) {
      if (this.flagName == 1) {
        this.ruleForm.name = row.aliasName
      } else if (this.flagName == 3) {
        this.findAddForm.variables.push(row.aliasName)
      } else {
        this.conditionsList.conditions[this.subscript].name = row.aliasName
        this.conditionsList.conditions[this.subscript].type = row.dataType
        if (row.dataType == 'string') {
          this.type_else = true
          this.type_if = false
        } else {
          this.type_else = false
          this.type_if = true
        }
      }
      this.dialogVisibleName = false
    },

    handleNodeClickName(row) {
      this.nodePathClick = row.id
      this.handleNodeClickAll()
    },

    async handleNodeClickAll() {
      this.loadingBL = true
      var dimensionpd = 1
      if (this.flagName == 3) {
        dimensionpd = 3
      } else {
        dimensionpd = 1
      }
      let res = await post(this.commonUrl.DataDictfindByConditions, {
        query: this.queryName,
        dimension: dimensionpd,
        nodePath: this.nodePathClick,
        groupId: '',
        enable: 1,
        page: this.nameAll.page,
        size: this.nameAll.size
      })
      this.paginationsList = res.content
      for (var i = 0; i < this.paginationsList.length; i++) {
        if (this.paginationsList[i].desc != null) {
          var descs = this.paginationsList[i].desc
          descs = descs.substr(descs.lastIndexOf('_') + 1)
          this.paginationsList[i].desc = descs
        }
      }
      this.nameAll.total = res.totalElements
      this.loadingBL = false
    },

    handleSizeChangeNames(val) {
      this.nameAll.size = val
      this.handleNodeClickAll()
    },

    handleCurrentChangeCoilIdNames(val) {
      this.nameAll.page = val
      this.handleNodeClickAll()
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background: #dcdfe6;
}
.dictionary-one-select .el-select .el-input__inner {
  max-width: 250px !important;
}

.enableClass {
  position: relative;
}

.successPoint::before {
  content: '';
  height: 5px;
  display: block;
  width: 5px;
  background: #67c23a;
  border-radius: 50%;
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
}

.warnPoint::before {
  content: '';
  height: 5px;
  display: block;
  width: 5px;
  background: #f56c6c;
  border-radius: 50%;
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
}

.xw_el_col {
  width: calc(100% - 280px);
}
.el-radio__input.is-disabled + span.el-radio__label {
  color: #606266;
}
.contentOpen {
  margin: 0px 4px;
}
.recal {
  padding-left: 20px;
}
// table 操作列
#doCopy {
  margin-left: 12px;
  margin-right: 12px;
  color: #63c7bb;
}
.doEdit {
  color: #ffa958;
}
.inputRecal {
  height: auto !important;
}
::v-deep .el-table {
  height: 100% !important;
}
</style>
