<!--施工明细-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border :title="title">
                <template v-slot:headerRight>
                  <el-row :gutter="20">
                    <span>当日实际降本额(元/吨)：</span>
                    <span style="margin-right: 20px;">{{ costReduction }}</span>
                    <span
                      v-command="'/first/steel/cost/operate'"
                      class="screen-btn"
                      @click="clickAddTitle">
                      <el-icon class="el-icon-edit-outline"/>
                      维护
                    </span>
                    <span
                      class="screen-btn"
                      @click="exportExcel">
                      <el-icon class="el-icon-download"/>
                      模板
                    </span>
                    <span
                      v-command="'/first/steel/cost/operate'"
                      class="screen-btn"
                      @click="clickAddProject">
                      <el-icon class="el-icon-edit-outline"/>
                      新增
                    </span>
                    <span
                      v-command="'/first/steel/cost/operate'"
                      class="screen-btn"
                      @click="handleDelete">
                      <el-icon class="el-icon-delete"/>
                      删除
                    </span>
                    <span
                      class="screen-btn"
                      @click="clickAddData">
                      <el-icon class="el-icon-upload2"/>
                      导入
                    </span>
                    <span
                      class="screen-btn"
                      @click="clickDownloadExcel">
                      <el-icon class="el-icon-download"/>
                      导出
                    </span>
                  </el-row>
                </template>
                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="ProjectData.loading"
                    :data="ProjectData.showGridData"
                    :row-class-name="rowClassName"
                    :span-method="spanMethod"
                    border
                    height="800"
                    @selection-change="handleSelectionChange">
                    <!--<el-table-column
                      type="selection"
                      align="center"/>-->
                    <el-table-column
                      show-overflow-tooltip
                      width="70"
                      align="center"
                      label="序号">
                      <template v-slot="scope">
                        <div>{{ scope.$index + 1 }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="成本项目"
                      align="center"
                      width="200"
                      prop="COST_PROJECT" />
                    <el-table-column
                      label="当日计划指标"
                      align="center"
                      width="100"
                      prop="DAY_PLAN_INDEX" />
                    <el-table-column
                      label="当日实际指标"
                      align="center"
                      width="100"
                      prop="DAY_ACTUAL_INDEX" />
                    <el-table-column
                      label="当日实际降本额(元/吨)"
                      align="center"
                      width="100"
                      prop="DAY_COST_REDUCTION" />
                    <!--<el-table-column
                      label="月度计划指标"
                      align="center"
                      width="100"
                      prop="MONTH_PLAN_INDEX" />
                    <el-table-column
                      label="月度计划降本"
                      align="center"
                      width="100"
                      prop="MONTH_PLAN_COST_REDUCTION" />
                    <el-table-column
                      label="月度累计指标"
                      align="center"
                      width="100"
                      prop="MONTH_CUMULATIVE_INDEX" />
                    <el-table-column
                      label="月度累计降本(元/吨)"
                      align="center"
                      width="90"
                      prop="MONTH_CUMULATIVE_COST_REDUCTION" />-->
                    <el-table-column
                      label="负责厂领导"
                      align="center"
                      width="90"
                      prop="RESPONSIBLE_LEADER" />
                    <el-table-column
                      label="具体细项跟踪"
                      align="center"
                      width=""
                      prop="DETAIL_TRACKING" />
                    <el-table-column
                      label="细项计划降本"
                      align="center"
                      width="100"
                      prop="DETAIL_PLAN_COST_REDUCTION" />
                    <el-table-column
                      label="每日跟踪情况"
                      align="center"
                      width=""
                      prop="DAY_TRACKING_SITUATION" />
                    <el-table-column
                      label="细项实际降本或单耗"
                      align="center"
                      width="90"
                      prop="DETAIL_ACTUAL_COST_REDUCTION" />
                    <el-table-column
                      label="具体推进人"
                      align="center"
                      width="90"
                      prop="EXECUTOR" />
                    <el-table-column
                      label="实施时间"
                      align="center"
                      width="110"
                      prop="EXECUTION_TIME" />
                    <el-table-column
                      label="实施单位"
                      align="center"
                      width="120"
                      prop="EXECUTION_DEPT" />
                    <el-table-column
                      align="center"
                      property=""
                      width="120"
                      label="操作">
                      <template v-slot="scope">
                        <span
                          v-command="'/first/steel/cost/operate'"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectItem(scope.row)">编辑</span>
                        <span
                          v-command="'/first/steel/cost/operate'"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectDeleteItem(scope.row)">删除</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--导入时间选择-->
    <el-dialog
      v-loading="TimeData.loading"
      :visible.sync="TimeData.dialogVisible"
      :width="'800px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="成本指标跟踪——导入数据">
      <template v-slot:title>
        <div class="custom-dialog-title">
          成本指标跟踪——导入数据
        </div>
      </template>
      <div
        :style="{height: 'calc(80vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">文件时间</div>
          <el-date-picker
            v-model="timeItem.morningMeetingDate"
            :rows="3"
            :clearable="false"
            :value-format = "'yyyy-MM-dd HH:mm:ss'"
            type="datetime"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">文件</div>
          <el-upload
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :before-upload="beforeUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
            :multiple="false"
            action=""
            class="upload-demo">
            <el-button
              size="small"
              type="primary">点击上传</el-button>
            <div
              slot="tip"
              class="el-upload__tip">请选择文件</div>
          </el-upload>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="addTimeData()">
          确定
        </span>
      </div>
    </el-dialog>
    <!--修改标题-->
    <el-dialog
      v-loading="TitleData.loading"
      :visible.sync="TitleData.dialogVisible"
      :width="'500px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="成本指标跟踪——标题修改">
      <template v-slot:title>
        <div class="custom-dialog-title">
          成本指标跟踪——标题修改
        </div>
      </template>
      <div
        :style="{height: 'calc(50vh - 300px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">标题</div>
          <el-input
            v-model="titleItem.morningMeetingTitle"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">降本额</div>
          <el-input
            v-model="titleItem.costReductionAmount"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="addTitleData(titleItem)">
          确定
        </span>
      </div>
    </el-dialog>
    <!--新增修改-->
    <el-dialog
      v-loading="ProjectData.loading"
      :visible.sync="ProjectData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="成本指标跟踪">
      <template v-slot:title>
        <div class="custom-dialog-title">
          成本指标跟踪
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">成本项目</div>
          <el-select
            v-model="projectItem.costProject"
            class="screen-input"
            clearable
            placeholder="请选择">
            <el-option
              v-for="item in projectList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">当日计划指标</div>
          <el-input
            v-model="projectItem.dayPlanIndex"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">当日实际指标</div>
          <el-input
            v-model="projectItem.dayActualIndex"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">当日实际降本额（元/吨）</div>
          <el-input
            v-model="projectItem.dayCostReduction"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <!--<div class="dialog-cell">
          <div class="dialog-cell-title">月度计划指标</div>
          <el-input
            v-model="projectItem.monthPlanIndex"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">月度计划降本</div>
          <el-input
            v-model="projectItem.monthPlanCostReduction"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">月度累计指标</div>
          <el-input
            v-model="projectItem.monthCumulativeIndex"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">月度累计降本（元/吨）</div>
          <el-input
            v-model="projectItem.monthCumulativeCostReduction"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>-->
        <div class="dialog-cell">
          <div class="dialog-cell-title">负责厂领导</div>
          <el-input
            v-model="projectItem.responsibleLeader"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">具体细项跟踪</div>
          <el-input
            v-model="projectItem.detailTracking"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">细项计划降本</div>
          <el-input
            v-model="projectItem.detailPlanCostReduction"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">每日跟踪情况</div>
          <el-input
            v-model="projectItem.dayTrackingSituation"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">细项实际降本或单耗</div>
          <el-input
            v-model="projectItem.detailActualCostReduction"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">具体推进人</div>
          <el-input
            v-model="projectItem.executor"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">实施时间</div>
          <el-input
            v-model="projectItem.executionTime"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">实施单位</div>
          <el-input
            v-model="projectItem.executionDept"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="addProjectData(projectItem)">
          确定
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  mino_download,
  oneBulletinCost_downloadCostExcel,
  oneBulletinCost_importCostExcel,
  oneBulletinCost_getCostDetails,
  oneBulletinCost_saveCostDetails,
  oneBulletinCost_updateCostDetails,
  oneBulletinCost_saveCostTitle,
  oneBulletinCost_deleteCostDetails,
  oneBulletinCost_deleteAllCostDetails,
  oneBulletinCost_exportCostDetails
} from '@/api/firstMeeting'
import moment from 'moment'

export default {
  name: 'ProjectPage',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      title: '',
      costReduction: '',
      titleId: '',
      TimeData: {
        loading: false,
        dialogVisible: false
      },
      timeItem: {
        morningMeetingDate: ''
      },
      TitleData: {
        loading: false,
        dialogVisible: false
      },
      titleItem: {
        id: '',
        morningMeetingTitle: '',
        costReductionAmount: ''
      },
      ProjectData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      projectItem: {
        id: '',
        rowId: '',
        titleId: '',
        costProject: '',
        dayPlanIndex: '',
        dayActualIndex: '',
        dayCostReduction: '',
        monthPlanIndex: '',
        monthPlanCostReduction: '',
        monthCumulativeIndex: '',
        monthCumulativeCostReduction: '',
        responsibleLeader: '',
        detailTracking: '',
        detailPlanCostReduction: '',
        dayTrackingSituation: '',
        detailActualCostReduction: '',
        executor: '',
        executionTime: '',
        executionDept: ''
      },
      projectList: [
        {
          text: '产量（万吨）',
          value: '产量（万吨）'
        },
        {
          text: '铁耗（kg/t）',
          value: '铁耗（kg/t）'
        },
        {
          text: '钢铁料消耗（kg/t）',
          value: '钢铁料消耗（kg/t）'
        },
        {
          text: '合金消耗（元/吨）',
          value: '合金消耗（元/吨）'
        },
        {
          text: '辅料消耗（kg/t）',
          value: '辅料消耗（kg/t）'
        },
        {
          text: '燃料动力（元/吨）',
          value: '燃料动力（元/吨）'
        },
        {
          text: '制造费用（元/吨）',
          value: '制造费用（元/吨）'
        }
      ],
      fileList: [],
      fileUrl: '',
      file: null,
      multipleSelection: [],
      spanArrFs: [], // 表格中的列名
      spanArr: {}
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getProjectData()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    // 批量删除
    handleDelete() {
      this.$confirm(`是否确认删除当前查询时间下的所有数据?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteProjectAll()
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    deleteProjectAll() {
      post(
        oneBulletinCost_deleteAllCostDetails + `?titleId=${this.titleId}`
      ).then(res => {
        if (res.operationResult === 'SUCCESS') {
          this.$notify.success('删除成功！')
          this.getProjectData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handlePreview(file) {
      console.log(file)
    },
    handleRemove(file, fileList) {
      console.log(file, fileList)
    },
    beforeRemove(file, fileList) {
      const isDel = this.$confirm(`确定移除 ${file.name}？`)
      if (isDel) {
        this.file = null
      }
      return isDel
    },
    beforeUpload(file) {
      this.file = file
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },
    // 导入按钮
    clickAddData() {
      this.fileList = []
      this.timeItem = {
        morningMeetingDate: ''
      }
      this.TimeData.dialogVisible = true
    },
    addTimeData() {
      let formData = new FormData()
      formData.append('excel', this.file)
      post(
        oneBulletinCost_importCostExcel +
          `?morMeetingDate=${this.timeItem.morningMeetingDate}`,
        formData
      )
        .then(res => {
          if (res.operationResult === 'SUCCESS') {
            this.$message.success(res.queryData)
            this.TimeData.dialogVisible = false
            this.getProjectData()
          } else {
            this.$message.error(res.operationMessage)
          }
        })
        .catch(err => {
          this.$message.error('导入失败！' + err)
        })
    },
    addProjectData(list) {
      if (this.projectItem.id === '') {
        this.addProjectDataSave(list)
      } else {
        this.addProjectDataUpdate(list)
      }
    },
    //新增
    addProjectDataSave(list) {
      const params = list
      this.ProjectData.loading = true
      post(oneBulletinCost_saveCostDetails, params)
        .then(res => {
          if (res.operationResult === 'SUCCESS') {
            this.$notify.success('操作成功！')
            this.ProjectData.dialogVisible = false
            this.getProjectData()
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    //修改
    addProjectDataUpdate(list) {
      const params = list
      this.ProjectData.loading = true
      post(oneBulletinCost_updateCostDetails, params)
        .then(res => {
          if (res.operationResult === 'SUCCESS') {
            this.$notify.success('操作成功！')
            this.ProjectData.dialogVisible = false
            this.getProjectData()
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    // 修改标题
    addTitleData(list) {
      const params = list
      this.TitleData.loading = true
      post(oneBulletinCost_saveCostTitle, params)
        .then(res => {
          if (res.operationResult === 'SUCCESS') {
            this.$notify.success('操作成功！')
            this.TitleData.dialogVisible = false
            this.getProjectData()
          }
        })
        .finally(_ => {
          this.TitleData.loading = false
        })
    },
    exportExcel() {
      post(oneBulletinCost_downloadCostExcel).then(res => {
        let data = res
        if (!data) {
          return
        }
        let url = window.URL.createObjectURL(new Blob([data]))
        let link = document.createElement('a')
        let myDate = new Date()
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '成本指标跟踪导入模板.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) //下载完成移除元素
        window.URL.revokeObjectURL(url) //释放掉blob对象
      })
    },
    rowClassName({ row, rowIndex }) {
      if (row.COST_PROJECT.includes('产量')) {
        if (row.DAY_PLAN_INDEX > row.DAY_ACTUAL_INDEX) {
          return 'class_yellow'
        } else {
          return ''
        }
      } else if (row.COST_PROJECT.includes('铁耗')) {
        if (row.DAY_PLAN_INDEX < row.DAY_ACTUAL_INDEX) {
          return 'class_yellow'
        } else {
          return ''
        }
      } else if (row.COST_PROJECT.includes('钢铁料消耗')) {
        if (row.DAY_PLAN_INDEX < row.DAY_ACTUAL_INDEX) {
          return 'class_yellow'
        } else {
          return ''
        }
      } else if (row.COST_PROJECT.includes('辅料消耗')) {
        if (row.DAY_PLAN_INDEX < row.DAY_ACTUAL_INDEX) {
          return 'class_yellow'
        } else {
          return ''
        }
      } else if (row.COST_PROJECT.includes('燃料动力')) {
        if (row.DAY_PLAN_INDEX < row.DAY_ACTUAL_INDEX) {
          return 'class_yellow'
        } else {
          return ''
        }
      } else if (row.COST_PROJECT.includes('制造费用')) {
        if (row.DAY_PLAN_INDEX < row.DAY_ACTUAL_INDEX) {
          return 'class_yellow'
        } else {
          return ''
        }
      } else {
        return ''
      }
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // 定义需要合并的列字段，有哪些列需要合并，就自定义添加字段即可
      const fields = [
        'COST_PROJECT',
        'DAY_PLAN_INDEX',
        'DAY_ACTUAL_INDEX',
        'DAY_COST_REDUCTION',
        'MONTH_PLAN_INDEX',
        'MONTH_PLAN_COST_REDUCTION',
        'MONTH_CUMULATIVE_INDEX',
        'MONTH_CUMULATIVE_COST_REDUCTION',
        'RESPONSIBLE_LEADER',
        'DETAIL_TRACKING',
        'DETAIL_PLAN_COST_REDUCTION',
        'DAY_TRACKING_SITUATION',
        'DETAIL_ACTUAL_COST_REDUCTION',
        'EXECUTOR',
        'EXECUTION_TIME',
        'EXECUTION_DEPT'
      ]
      // 当前行的数据
      const cellValue = row[column.property]
      // 判断只合并定义字段的列数据
      if (cellValue && fields.includes(column.property)) {
        const prevRow = this.ProjectData.showGridData[rowIndex - 1] //上一行数据
        let nextRow = this.ProjectData.showGridData[rowIndex + 1] //下一行数据
        // 当上一行的数据等于当前行数据时，当前行单元格隐藏
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          // 反之，则循环判断若下一行数据等于当前行数据，则当前行开始进行合并单元格
          let countRowspan = 1 //用于合并计数多少单元格
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = this.ProjectData.showGridData[++countRowspan + rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    //点击新增
    clickAddProject() {
      this.projectItem = {
        id: '',
        costProject: '',
        dayPlanIndex: '',
        dayActualIndex: '',
        dayCostReduction: '',
        monthPlanIndex: '',
        monthPlanCostReduction: '',
        monthCumulativeIndex: '',
        monthCumulativeCostReduction: '',
        responsibleLeader: '',
        detailTracking: '',
        detailPlanCostReduction: '',
        dayTrackingSituation: '',
        detailActualCostReduction: '',
        executor: '',
        executionTime: '',
        executionDept: ''
        // setDate: this.cDate
      }
      this.projectItem.titleId = this.titleId
      this.ProjectData.dialogVisible = true
    },
    //点击维护
    clickAddTitle() {
      this.titleItem.morningMeetingTitle = this.title
      this.titleItem.costReductionAmount = this.costReduction
      this.titleItem.id = this.titleId
      this.TitleData.dialogVisible = true
    },
    //点击查看详情
    clickProjectItem(row) {
      // this.projectItem = JSON.parse(JSON.stringify(row))
      this.projectItem.id = row.ID
      this.projectItem.rowId = row.ROW_ID
      this.projectItem.titleId = row.TITLE_ID
      this.projectItem.costProject = row.COST_PROJECT
      this.projectItem.dayPlanIndex = row.DAY_PLAN_INDEX
      this.projectItem.dayActualIndex = row.DAY_ACTUAL_INDEX
      this.projectItem.dayCostReduction = row.DAY_COST_REDUCTION
      // this.projectItem.monthPlanIndex = row.MONTH_PLAN_INDEX
      // this.projectItem.monthPlanCostReduction = row.MONTH_PLAN_COST_REDUCTION
      // this.projectItem.monthCumulativeIndex = row.MONTH_CUMULATIVE_INDEX
      // this.projectItem.monthCumulativeCostReduction =
      //   row.MONTH_CUMULATIVE_COST_REDUCTION
      this.projectItem.responsibleLeader = row.RESPONSIBLE_LEADER
      this.projectItem.detailTracking = row.DETAIL_TRACKING
      this.projectItem.detailPlanCostReduction = row.DETAIL_PLAN_COST_REDUCTION
      this.projectItem.dayTrackingSituation = row.DAY_TRACKING_SITUATION
      this.projectItem.detailActualCostReduction =
        row.DETAIL_ACTUAL_COST_REDUCTION
      this.projectItem.executor = row.EXECUTOR
      this.projectItem.executionTime = row.EXECUTION_TIME
      this.projectItem.executionDept = row.EXECUTION_DEPT
      this.ProjectData.dialogVisible = true
    },
    //点击删除
    clickProjectDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteProject(row)
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    //删除
    deleteProject(row) {
      post(oneBulletinCost_deleteCostDetails + `?id=${row.ID}`).then(res => {
        if (res.operationResult === 'SUCCESS') {
          this.$notify.success('删除成功！')
          this.getProjectData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    calculateHeight() {
      this.ProjectData.maxHeight = this.$refs.table1.offsetHeight
    },
    getProjectData() {
      this.ProjectData.loading = true
      post(oneBulletinCost_getCostDetails, {
        pageNum: '1',
        pageSize: '500',
        morMeetingDate: this.cDate + ' 00:00:00'
      }).then(res => {
        if (res.operationResult === 'SUCCESS') {
          if (res.queryData.numberOfElements === 0) {
            this.ProjectData.showGridData = []
            this.ProjectData.loading = false
          }
          this.ProjectData.showGridData = res.queryData.content
          this.title = res.queryData.content[0].MORNING_MEETING_TITLE
          this.costReduction = res.queryData.content[0].COST_REDUCTION_AMOUNT
          this.titleId = res.queryData.content[0].TITLE_ID
        } else {
          this.$message.error(res.message)
        }
        this.ProjectData.loading = false
      })
    },
    clickDownloadExcel() {
      this.downloadFile()
    },
    //导出Excel
    downloadFile() {
      let param = {
        morMeetingDate: this.cDate + ' 00:00:00'
      }
      post(oneBulletinCost_exportCostDetails, param).then(res => {
        let data = res
        if (!data) {
          return
        }
        const url = window.URL.createObjectURL(new Blob([data]))
        let link = document.createElement('a')
        let myDate = moment(new Date()).format('yyyy-MM-DD')

        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '成本指标跟踪' + myDate + '.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) //下载完成移除元素
        window.URL.revokeObjectURL(url) //释放掉blob对象
      })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
/deep/ .el-table__body tr:hover > td {
  background-color: rgba(245, 247, 250, 0.1) !important;
  //background-color: transparent !important;
}

/deep/ .el-table__body tr.current-row > td {
  background-color: rgba(245, 247, 250, 0.1) !important;
  //background-color: transparent !important;
}

/deep/ .el-table .class_red {
  background: #fd0000;
  color: black;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
  color: black;
  font-weight: bolder;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
  color: black;
}

.dialog-body {
  overflow: scroll;

  .dialog-cell {
    margin-bottom: 12px;

    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }

    .dialog-cell-input {
    }
  }
}

.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }

  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
