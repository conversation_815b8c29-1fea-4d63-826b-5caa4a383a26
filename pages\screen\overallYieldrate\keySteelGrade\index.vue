<template>
  <div class="container">
    <div
      class="config"
    >
      <span 
        class="config-btn" 
        @click="openConfigDialog">
        <el-icon 
          class="el-icon-edit-outline" 
          style="margin-right: 4px;"/>
        配置钢种
      </span>
    </div>
    <div 
      v-if="steelGradeData.length > 0" 
      class="chart-container">
      <div 
        v-for="(item, index) in steelGradeData" 
        :key="index"
        class="chart-box">
        <screen-border :title="item.steelName || `重点钢种${index + 1}`">
          <div class="radio-selector">
            <el-radio 
              v-model="selectedTypes[index]" 
              label="全流程原钢种收得率">全流程原钢种收得率</el-radio>
            <el-radio 
              v-model="selectedTypes[index]" 
              label="钢原钢种收得率">钢原钢种收得率</el-radio>
            <el-radio 
              v-model="selectedTypes[index]" 
              label="材原钢种收得率">材原钢种收得率</el-radio>
          </div>
          <line-chart 
            :chart-data="getChartData(item, selectedTypes[index])"
            :month-avg-data="getAvgData(item, selectedTypes[index])" 
            :history-best-data="getMaxData(item, selectedTypes[index])"
            :show-legend="true"
            :x-data="item.xDate"
            :height="250" 
            :show-label="true" 
            :bar-width="20" 
            unit="%"/>
        </screen-border>
      </div>
    </div>
    <div 
      v-else 
      class="no-data">
      暂无钢种数据，请点击"配置钢种"进行设置
    </div>
    
    <el-dialog 
      :visible.sync="dialogVisible"
      :width="'600px'" 
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="配置钢种">
      <template v-slot:title>
        <div class="custom-dialog-title">
          配置钢种
        </div>
      </template>
      <div class="config-content">
        <div 
          v-for="(item, index) in steelGradeList" 
          :key="index" 
          class="steel-grade-item">
          <el-select
            v-model="item.value"
            class="steel-select"
            style="width:485px;margin-left: 5px;"
            filterable
            clearable
            placeholder="请选择钢种"
            @change="handleSteelGradeChange">
            <el-option
              v-for="option in getAvailableSteelTypes(index)"
              :key="option"
              :label="option"
              :value="option"/>
          </el-select>
          <el-button 
            type="danger" 
            size="small" 
            @click="removeSteelGrade(index)">删除</el-button>
        </div>
        <div class="add-btn-wrapper">
          <el-button 
            type="primary" 
            size="small" 
            @click="addSteelGrade">+ 新增</el-button>
        </div>
      </div>
      <span 
        slot="footer" 
        class="dialog-footer">
        <el-button 
          :loading="saveLoading"
          :disabled="!steelGradeList.length || steelGradeList.some(item => !item.value)" 
          type="primary" 
          @click="handleSave">保存</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityDailyReportScreen/components/screen-border.vue'
import LineChart from '@/pages/screen/overallYieldrate/component/line-three-chart.vue'
import {
  FindSteelGrade,
  slabFindGradeData,
  slabSaveGradeData,
  slabFindGradeCurve
} from '@/api/screen'
import { post } from '@/lib/Util'

export default {
  name: 'KeySteelGrade',
  components: {
    ScreenBorder,
    LineChart
  },
  data() {
    return {
      gradesName: '',
      dialogVisible: false,
      saveLoading: false,
      steelTypeAll: '',
      steelGradeData: [], // 后台返回的钢种数据
      selectedTypes: [], // 每个图表选中的类型
      steelGradeList: []
    }
  },
  computed: {
    // 获取已选择的钢种列表
    selectedSteelGrades() {
      return this.steelGradeList
        .map(item => item.value)
        .filter(value => value !== '')
    }
  },
  created() {
    this.getSteelGradeList()
    this.getSlabFindGradeCurve()
  },
  methods: {
    // 获取钢种列表数据
    async getSlabFindGradeCurve() {
      try {
        const res = await post(slabFindGradeCurve)
        console.log('%c 钢种列表数据', 'color: red', res)

        if (res.data && Array.isArray(res.data)) {
          this.steelGradeData = res.data
          // 初始化每个图表选中的类型
          this.selectedTypes = res.data.map(() => '全流程原钢种收得率')
        }
      } catch (error) {
        console.error('获取钢种曲线数据失败', error)
      }
    },

    // 获取钢种名称列表
    async getSteelGradeList() {
      const res = await post(FindSteelGrade)
      let obj = []
      for (const item of res.data) {
        for (let key in item) {
          obj.push(key)
        }
      }
      this.steelTypeAll = obj
    },

    // 获取图表数据
    getChartData(item, selectedType) {
      // 根据选中的类型返回对应的数据
      let dataKey, dataName

      if (selectedType === '全流程原钢种收得率') {
        dataKey = 'qDate'
        dataName = '全流程原钢种收得率'
      } else if (selectedType === '钢原钢种收得率') {
        dataKey = 'gDate'
        dataName = '钢原钢种收得率'
      } else {
        dataKey = 'cDate'
        dataName = '材原钢种收得率'
      }

      // 添加空值检查
      if (!item[dataKey] || !Array.isArray(item[dataKey])) {
        return [
          {
            name: '月收得率',
            data: []
          }
        ]
      }

      // 将数据从字符串转换为数值，保留null值
      const numericData = item[dataKey].map(value => {
        if (value === null) return null
        if (value === '0%') return 0
        return parseFloat(value.replace('%', ''))
      })

      return [
        {
          name: '月收得率',
          data: numericData
        }
      ]
    },

    // 获取平均值数据
    getAvgData(item, selectedType) {
      let avgKey

      if (selectedType === '全流程原钢种收得率') {
        avgKey = 'qDateAvg'
      } else if (selectedType === '钢原钢种收得率') {
        avgKey = 'gDateAvg'
      } else {
        avgKey = 'cDateAvg'
      }

      // 添加空值检查
      if (
        !item[avgKey] ||
        !item[avgKey].length ||
        item[avgKey][0] === null ||
        item[avgKey][0] === undefined
      ) {
        return 0
      }

      // 获取第一个值并转换为数字
      const avgValue = item[avgKey][0]
      if (avgValue === '0%') return 0
      return parseFloat(avgValue.replace('%', ''))
    },

    // 获取最好值数据
    getMaxData(item, selectedType) {
      let maxKey

      if (selectedType === '全流程原钢种收得率') {
        maxKey = 'qDateMax'
      } else if (selectedType === '钢原钢种收得率') {
        maxKey = 'gDateMax'
      } else {
        maxKey = 'cDateMax'
      }

      // 添加空值检查
      if (
        !item[maxKey] ||
        !item[maxKey].length ||
        item[maxKey][0] === null ||
        item[maxKey][0] === undefined
      ) {
        return 0
      }

      // 获取第一个值并转换为数字
      const maxValue = item[maxKey][0]
      if (maxValue === '0%') return 0
      return parseFloat(maxValue.replace('%', ''))
    },

    async openConfigDialog() {
      this.dialogVisible = true
      // 获取已配置的钢种数据
      try {
        const res = await post(slabFindGradeData)
        if (res.data && Array.isArray(res.data)) {
          // 将API返回的数据转换为表单需要的格式
          this.steelGradeList = res.data.map(item => {
            return { value: item.gradesName }
          })
          // 如果没有数据，默认添加一个空选项
          if (this.steelGradeList.length === 0) {
            this.steelGradeList = [{ value: '' }]
          }
        }
      } catch (error) {
        console.error('获取钢种数据失败', error)
        this.steelGradeList = [{ value: '' }]
      }
    },

    handleSave() {
      // 处理保存逻辑
      this.saveLoading = true
      //获取gradesName列表
      const gradesNameList = this.steelGradeList.map(item => {
        return {
          gradesName: item.value
        }
      })
      // 调用保存接口
      post(slabSaveGradeData, {
        data: gradesNameList
      })
        .then(res => {
          console.log('%c res', 'color: red', res)
          if (res.data === '导入数据成功') {
            this.$message.success('保存成功')
            this.dialogVisible = false
            // 保存成功后重新获取数据
            this.getSlabFindGradeCurve()
          }
        })
        .catch(err => {
          console.error(err)
        })
        .finally(() => {
          this.saveLoading = false
        })
    },

    // 获取每个下拉框可选的钢种列表
    getAvailableSteelTypes(currentIndex) {
      if (!this.steelTypeAll || !Array.isArray(this.steelTypeAll)) {
        return []
      }

      // 获取当前项的选择值
      const currentValue = this.steelGradeList[currentIndex].value

      // 过滤掉已经在其他下拉框中选择的选项，但保留当前项的选择
      return this.steelTypeAll.filter(option => {
        // 如果选项是当前项的值，则应该可选
        if (option === currentValue) {
          return true
        }

        // 检查选项是否已经被其他下拉框选择
        return !this.selectedSteelGrades.includes(option)
      })
    },
    // 处理钢种选择变化
    handleSteelGradeChange() {
      // 这个函数会在选择变化时触发，用于更新界面
      // 由于Vue的响应式特性，只需要调用就可以触发计算属性重新计算
    },
    addSteelGrade() {
      this.steelGradeList.push({ value: '' })
    },
    removeSteelGrade(index) {
      this.steelGradeList.splice(index, 1)
    }
  }
}
</script>

<style scoped lang="less">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: auto;

  .config {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 10px;

    .config-btn {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      width: 100px;
      height: 28px;
      padding: 0 10px;
      background: rgba(31, 198, 255, 0.3);
      border: 1px solid #1fc6ff;
      border-radius: 4px;
      font-size: 14px;
      line-height: 28px;
      text-align: center;
      font-weight: bold;
      cursor: pointer;
      color: #fff;
      margin: 0 0 10px 0;

      &:hover {
        background: rgba(31, 198, 255, 0.6);
        border: 1px solid #1fc6ff;
      }
    }
  }

  .chart-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 0 10px;
  }

  .chart-box {
    width: calc(50% - 10px);
    min-height: 300px;
    margin-bottom: 20px;

    @media screen and (max-width: 1200px) {
      width: 100%;
    }
  }

  .no-data {
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 16px;
  }

  .radio-selector {
    display: flex;
    margin: 5px 30px;
    flex-wrap: wrap;

    .el-radio {
      margin-right: 15px;
      margin-bottom: 5px;
      color: #fff;

      /deep/ .el-radio__label {
        color: #fff;
        font-size: 12px;
      }

      /deep/ .el-radio__input.is-checked .el-radio__inner {
        border-color: #1fc6ff;
        background: #1fc6ff;
      }

      /deep/ .el-radio__input.is-checked + .el-radio__label {
        color: #1fc6ff;
      }

      /deep/ .el-radio__inner {
        background-color: transparent;
        border: 1px solid #fff;
      }
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-content-wrapper {
    gap: 0px !important;
  }

  /deep/ .border-content {
    padding: 10px;
    box-sizing: border-box;
  }

  .custom-select {
    width: 84px;
    margin-right: 10px;

    /deep/ .el-input__inner {
      background: #082f3c;
      border: 1px solid #1fc6ff !important;
      color: #fff;
      border: none;
      border-radius: 4px;
      height: 32px;
      line-height: 32px;
    }

    /deep/ .el-input__suffix {
      right: 5px;
    }

    /deep/ .el-select__caret {
      color: #fff;
      font-weight: bold;
    }

    /deep/ .el-input.is-focus .el-input__inner {
      box-shadow: none;
      border: none;
    }

    /deep/ .el-popper {
      background: #082f3c;
      border: 1px solid #1fc6ff;

      .el-select-dropdown__item {
        color: #fff;

        &.selected,
        &.hover {
          background-color: #082f3c;
        }
      }

      .popper__arrow {
        border-bottom-color: #1fc6ff;
      }
    }
  }

  .config-content {
    padding: 20px;

    .steel-grade-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .steel-select {
        width: 100%;
        margin-right: 10px;
      }
    }

    .add-btn-wrapper {
      margin-top: 20px;
    }
  }

  .dialog-footer {
    text-align: right;
    padding: 10px 20px;

    .el-button {
      margin-left: 10px;
      min-width: 80px;
      border-radius: 4px;
    }
  }

  /deep/ .screen-dialog {
    .el-dialog__body {
      padding: 0;
    }

    .custom-dialog-title {
      font-size: 16px;
      color: #fff;
    }

    // 修改按钮样式
    .el-button {
      background: transparent;
      border: 1px solid #1fc6ff;
      color: #1fc6ff;

      &:hover {
        background: rgba(31, 198, 255, 0.2);
      }

      &.el-button--primary {
        background: rgba(31, 198, 255, 0.2);

        &:hover {
          background: rgba(31, 198, 255, 0.4);
        }
      }
    }

    // 修改删除按钮样式
    .el-button--danger {
      border-color: #ff4d4f;
      color: #ff4d4f;

      &:hover {
        background: rgba(255, 77, 79, 0.2);
      }
    }
  }

  // 修改下拉选择框样式
  /deep/ .el-select {
    .el-input__inner {
      background: #082f3c;
      border: 1px solid #1fc6ff !important;
      color: #fff;
      border-radius: 4px;
      height: 32px;
      line-height: 32px;
    }

    .el-input__suffix {
      right: 5px;
    }

    .el-select__caret {
      color: #fff;
      font-weight: bold;
    }
  }

  // 这里需要修改，添加全局样式覆盖下拉菜单
}
</style>

<!-- 添加全局样式，确保下拉面板样式正确应用 -->
<style lang="less">
/* 下拉面板样式 */
.el-select-dropdown {
  background-color: #082f3c !important;
  border: 1px solid #1fc6ff !important;

  .el-select-dropdown__item {
    color: #fff !important;

    &:hover,
    &.selected,
    &.hover {
      background-color: rgba(31, 198, 255, 0.2) !important;
      color: #1fc6ff !important;
    }
  }

  .popper__arrow {
    border-bottom-color: #1fc6ff !important;

    &::after {
      border-bottom-color: #082f3c !important;
    }
  }
}

/* 搜索框样式 */
.el-select-dropdown__wrap {
  .el-scrollbar__view {
    background-color: #082f3c;
  }
}

/* 下拉面板滚动条样式 */
.el-scrollbar__thumb {
  background-color: rgba(31, 198, 255, 0.5);
  &:hover {
    background-color: rgba(31, 198, 255, 0.7);
  }
}
</style>
