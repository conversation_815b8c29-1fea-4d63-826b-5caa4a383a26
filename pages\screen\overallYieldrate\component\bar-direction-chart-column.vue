<template>
  <div class="bar-direction-chart-column">
    <div
      :id="containerId"
      :style="{ height: typeof height === 'number' ? height + 'px' : height }"/>

    <!-- 详情弹窗 -->
    <el-dialog
      :visible.sync="dialogVisible"
      :title="dialogTitle"
      class="screen-dialog"
      width="80%">
      <template v-slot:title>
        <div class="custom-dialog-title">
          {{ dialogTitle }}
        </div>
      </template>

      <div class="dialog-content">
        <!-- 原有需求1的表格，根据currentApiType条件显示 -->
        <el-table
          v-loading="tableLoading"
          v-if="currentApiType === 'noPage'"
          :data="tableData"
          border
          size="mini"
          max-height="500"
          style="width: 100%">
          <el-table-column
            prop="SLABNO"
            label="子坯料号"
            width="120" />
          <el-table-column
            prop="MOTHERSLAB"
            label="母坯坯料号"
            width="120" />
          <el-table-column
            prop="HEATNO"
            label="炉号"
            width="100" />
          <el-table-column
            prop="STLGRD"
            label="坯料钢种"
            width="100" />
          <el-table-column
            prop="STLGRDORDDETAIL"
            label="坯料原钢种"
            width="120" />
          <el-table-column
            prop="WGT"
            label="子坯料重量"
            width="100" />
          <el-table-column
            prop="THK"
            label="入炉坯料厚度"
            width="120" />
          <el-table-column
            prop="ORDNO"
            label="订单号"
            width="140" />
          <el-table-column
            prop="PRODDATE"
            label="母坯生产时间"
            width="160" />
          <el-table-column
            prop="OUTPLT"
            label="流向"
            width="80" />
          <el-table-column
            prop="OVERFL"
            label="坯料种类"
            width="150" />
          <el-table-column
            prop="SLABTYPE"
            label="缺陷类型"
            width="200" />
          <el-table-column
            prop="CADCOMMENT"
            label="CAD锁定详称"
            width="200" />
          <el-table-column
            prop="CADMANANO"
            label="原因代码"
            width="120" />
          <el-table-column
            prop="CADMANANO1"
            label="CAD代码"
            width="160" />
          <el-table-column
            prop="ESTCD"
            label="处理"
            width="80" />
          <el-table-column
            prop="ESTCOMMENT"
            label="坯料判废原因"
            width="150" />
          <el-table-column
            prop="ESTCOMMENT1"
            label="处理详细内容"
            width="150" />
          <el-table-column
            prop="STLGRDFLAG"
            label="是否替代轧制"
            width="120" />
          <el-table-column
            prop="CDSHORTNAME"
            label="处理名称"
            width="120" />
        </el-table>

        <!-- 新增需求2的表格，根据currentApiType条件显示 -->
        <el-table
          v-loading="tableLoading"
          v-else-if="currentApiType === 'qlt'"
          :data="qltTableData"
          border
          size="mini"
          max-height="500"
          style="width: 100%">
          <el-table-column
            prop="SLABWGT"
            label="原料重量"
            width="100" />
          <el-table-column
            prop="CORRTCTDEFECT"
            label="改判缺陷"
            width="180" />
          <el-table-column
            prop="STDSPECSTLGRD"
            label="标准钢种轧制标准"
            width="140" />
          <el-table-column
            prop="TEMPFIELD"
            label="缺陷位置"
            width="100" />
          <el-table-column
            prop="MILLSTDSPEC"
            label="轧制标准"
            width="140" />
          <el-table-column
            prop="WGT1"
            label="实绩钢板重量"
            width="120" />
          <el-table-column
            prop="PLATENO"
            label="钢板号"
            width="140" />
          <el-table-column
            prop="STDSPECSTAMP"
            label="标准钢种轧钢实绩"
            width="140" />
          <el-table-column
            prop="WID1"
            label="订单宽度"
            width="100" />
          <el-table-column
            prop="MILLOCCRDATE"
            label="轧制时间"
            width="160" />
          <el-table-column
            prop="SALEWAY"
            label="销售方式"
            width="100" />
          <el-table-column
            prop="CUSTNAME"
            label="客户名称"
            width="200" />
          <el-table-column
            prop="PLT"
            label="生产厂"
            width="80" />
          <el-table-column
            prop="PRODGRD"
            label="产品等级"
            width="100" />
          <el-table-column
            prop="PROCCD"
            label="钢板进程状态"
            width="120" />
          <el-table-column
            prop="CUTENDDATE"
            label="剪切时间"
            width="120" />
          <el-table-column
            prop="ORGORDNO"
            label="原始订单号"
            width="140" />
          <el-table-column
            prop="ORDNO1"
            label="最终订单号"
            width="140" />
          <el-table-column
            prop="THK1"
            label="订单厚度"
            width="100" />
          <el-table-column
            prop="APLYSTDSPEC"
            label="标准号"
            width="140" />
          <el-table-column
            prop="SUBCLASS"
            label="产品分类"
            width="140" />
          <el-table-column
            prop="LEN1"
            label="订单长度"
            width="100" />
        </el-table>
      </div>

      <span
        slot="footer"
        class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { findDetailsDateNoPage1, findDetailsDateQlt1 } from '@/api/screen'
import { post } from '@/lib/Util'

export default {
  name: 'BarDirectionChartColumn',
  props: {
    height: {
      type: [Number, String],
      default: '100%'
    },
    unit: {
      type: String,
      default: ''
    },
    chartData: {
      type: Array,
      default: () => []
    },
    xData: {
      type: Array,
      default: () => []
    },
    color: {
      type: Array,
      default: () => ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    dateFlag: {
      type: String,
      default: '1'
    },
    startDate: {
      type: String,
      default: ''
    },
    endDate: {
      type: String,
      default: ''
    },
    slabFlag: {
      type: String,
      default: '1'
    },
    title: {
      type: String,
      default: ''
    },
    barWidth: {
      type: Number,
      default: 25
    }
  },
  data() {
    return {
      containerId: '',
      myChart: null,
      dialogVisible: false,
      dialogTitle: '',
      tableData: [],
      tableLoading: false,
      selectedSteelType: '',
      qltTableData: [],
      currentApiType: '',
      dateRange: [] // 用于存储日期范围
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    },
    startDate: function(newVal) {
      if (newVal && this.dateRange.length > 0) {
        this.dateRange[0] = newVal
      } else if (newVal) {
        this.dateRange = [newVal, this.endDate || '']
      }
    },
    endDate: function(newVal) {
      if (newVal && this.dateRange.length > 0) {
        this.dateRange[1] = newVal
      } else if (newVal) {
        this.dateRange = [this.startDate || '', newVal]
      }
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
    if (this.myChart) {
      this.myChart.dispose()
      this.myChart = null
    }
  },
  mounted() {
    this.containerId = 'bar-chart-' + this.guid()

    // 初始化日期范围
    if (this.startDate && this.endDate) {
      this.dateRange = [this.startDate, this.endDate]
    }

    this.$nextTick(() => this.initChart())
    window.addEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        // 监听图表点击事件
        this.myChart.on('click', params => {
          console.log('柱图点击事件触发:', params)
          // 确保能够获取到正确的名称
          const name = params.name || (params.data && params.data.name)
          if (name) {
            this.handleChartItemClick(name)
          } else {
            console.warn('无法获取点击项的名称:', params)
          }
        })
      }

      const options = {
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 10
          },
          formatter: params => {
            return (
              params[0].axisValue +
              '<br/>' +
              params
                .map(
                  item =>
                    `<div style="display: flex; justify-content: space-between"><span>
<span style="display: inline-block; width: 8px; height: 8px; vertical-align: middle; border-radius: 50%;background: ${
                      item.color
                    }"></span>
                    ${item.data.name || item.seriesName}
</span> <span> &emsp;
                    ${item.value}</span></div>
                   `
                )
                .join('')
            )
          }
        },
        color: this.color,
        legend: {
          show: this.showLegend,
          align: 'left',
          top: 5,
          right: 2,
          padding: 0,
          icon: 'circle',
          textStyle: {
            color: '#C9E1FDF2',
            fontSize: 10
          },
          itemHeight: 10,
          itemWidth: 10,
          itemGap: 10,
          itemStyle: {
            borderWidth: 0,
            padding: 0
          }
        },
        grid: {
          top: this.showLegend ? '18%' : '0',
          left: '2%',
          right: '12%',
          bottom: '1%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'value',
            minInterval: 1,
            nameTextStyle: {
              color: '#fff'
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'category',
            data: this.xData,
            axisTick: { show: false },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              interval: 0
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            },
            inverse: true
          }
        ],
        series: this.chartData.map(item => {
          return {
            name: item.name,
            type: 'bar',
            barWidth: Math.min(25, this.barWidth), // 限制最大宽度为10
            showBackground: this.barBackground,
            backgroundStyle: {
              color: 'rgba(232, 236, 239, 0.3)'
            },
            label: {
              show: this.showLabel,
              color: '#fff',
              position: 'right',
              fontSize: 12,
              offset: [2, 0]
            },
            data: item.data.map(dataItem => {
              // 确保每个数据项都有name属性
              if (typeof dataItem === 'object' && !dataItem.name) {
                return {
                  ...dataItem,
                  name: (dataItem.value && dataItem.value.toString()) || '未知'
                }
              }
              return dataItem
            })
          }
        })
      }

      this.myChart.setOption(options, true)
    },

    // 处理图表项点击事件
    handleChartItemClick(steelType) {
      console.log('处理点击事件:', steelType)
      if (!steelType) return

      this.selectedSteelType = steelType

      // 判断是否为材料相关的图表
      if (
        this.title &&
        (this.title.includes('材改判协议') ||
          this.title.includes('材现货') ||
          this.title.includes('材判废'))
      ) {
        console.log('使用质量接口获取数据')

        // 根据图表标题确定正确的slabFlag和对话框标题前缀
        let slabFlagToUse = this.slabFlag
        let titlePrefix = ''

        if (this.title.includes('材判废')) {
          slabFlagToUse = '1'
          titlePrefix = '材判废'
        } else if (this.title.includes('材改判协议')) {
          slabFlagToUse = '2'
          titlePrefix = '材改判协议'
        } else if (this.title.includes('材现货')) {
          slabFlagToUse = '3'
          titlePrefix = '材现货'
        }

        // 设置对话框标题
        this.dialogTitle = `${titlePrefix}详情 - ${steelType}`

        this.fetchQltDetailData(steelType, slabFlagToUse)
        this.currentApiType = 'qlt'
      } else {
        console.log('使用常规接口获取数据')
        this.dialogTitle = `${this.getSlabFlagTitle(
          this.slabFlag
        )}详情 - ${steelType}`
        this.fetchDetailData(steelType)
        this.currentApiType = 'noPage'
      }
    },

    // 获取详细数据 (原有需求1接口)
    async fetchDetailData(steelType) {
      this.tableLoading = true
      this.dialogVisible = true
      this.tableData = []

      try {
        const res = await post(findDetailsDateNoPage1, {
          startDate:
            this.startDate || (this.dateRange && this.dateRange[0]) || '',
          endDate: this.endDate || (this.dateRange && this.dateRange[1]) || '',
          slabFlag: this.slabFlag,
          steelType: steelType
        })

        if (res.data && Array.isArray(res.data)) {
          this.tableData = res.data
        } else {
          this.tableData = []
        }
      } catch (error) {
        console.error('获取详情数据失败:', error)
        this.$message.error('获取详情数据失败')
      } finally {
        this.tableLoading = false
      }
    },

    // 获取质量详细数据 (新增需求2接口)
    async fetchQltDetailData(steelType, customSlabFlag = null) {
      this.tableLoading = true
      this.dialogVisible = true
      this.qltTableData = []

      console.log('获取质量详细数据:', steelType)

      try {
        const res = await post(findDetailsDateQlt1, {
          startDate:
            this.startDate || (this.dateRange && this.dateRange[0]) || '',
          endDate: this.endDate || (this.dateRange && this.dateRange[1]) || '',
          slabFlag: customSlabFlag || this.slabFlag,
          steelType: steelType
        })

        if (res.data && Array.isArray(res.data)) {
          this.qltTableData = res.data
        } else {
          this.qltTableData = []
        }
      } catch (error) {
        console.error('获取质量详情数据失败:', error)
        this.$message.error('获取详情数据失败')
      } finally {
        this.tableLoading = false
      }
    },

    // 获取slabFlag对应的标题
    getSlabFlagTitle(slabFlag) {
      const titles = {
        '1': '坯料判废',
        '2': '坯料替代',
        '3': '坯料库存'
      }
      return titles[slabFlag] || ''
    },

    resizeChart() {
      this.myChart && this.myChart.resize()
    },

    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.bar-direction-chart-column {
  width: 100%;
  height: 100%;
}

.detail-dialog {
  /deep/ .el-dialog {
    background: #041a21;
    border: 1px solid #1fc6ff;
  }

  /deep/ .el-dialog__header {
    padding: 10px 20px;
  }

  /deep/ .el-dialog__body {
    padding: 10px 20px;
  }

  /deep/ .el-table {
    background-color: transparent;
    color: #fff;

    &::before {
      display: none;
    }

    th {
      background-color: rgba(31, 198, 255, 0.3);
      color: #fff;
      border-color: #1fc6ff;
      font-weight: bold;
    }

    td {
      background-color: transparent;
      color: #fff;
      border-color: #2e4262;
    }

    tr {
      background-color: transparent;
    }
  }

  .custom-dialog-title {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
  }

  .table-container {
    max-height: 500px;
    overflow-y: auto;
  }
}

/deep/ .el-button {
  background-color: transparent;
  color: #fff;
  border: 1px solid #1fc6ff;
  &:hover {
    color: #fff;
    background-color: rgba(31, 198, 255, 0.2);
  }
}
</style>
