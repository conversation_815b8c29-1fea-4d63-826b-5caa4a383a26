<template>
  <div>
    <el-dialog
      :title="title + '报表配置'"
      :visible.sync="visible"
      :width="'800px'"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <div class="titles">选择数据</div>
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="150px"
        size="medium"
        class="elForm"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="主题"
          prop="directoryName"
        >
          <el-input
            v-model="formData.directoryName"
            :style="{width: '100%'}"
            clearable
            placeholder=""
          />
        </el-form-item>
        <el-form-item
          label="类型"
          prop="themeDirectoryId"
        >
          <el-input
            v-model="formData.themeDirectoryId"
            :style="{width: '100%'}"
            clearable
            placeholder="日报"
          />
        </el-form-item>
      </el-form>
      <div class="titles">创建数据源</div>
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="150px"
        size="medium"
        class="elForm"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="名称"
          prop="directoryName"
        >
          <el-input
            v-model="formData.directoryName"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入名称"
          />
        </el-form-item>
        <el-form-item
          label="数据采集"
          prop="themeDirectoryId"
        >
          <el-input
            v-model="formData.themeDirectoryId"
            :style="{width: '100%'}"
            clearable
            placeholder=""
          />
        </el-form-item>
      </el-form>
      <div class="titles">创建数据集</div>
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="150px"
        size="medium"
        class="elForm"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="名称"
          prop="directoryName"
        >
          <el-input
            v-model="formData.directoryName"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入名称"
          />
        </el-form-item>
        <el-form-item
          label="数据源"
          prop="themeDirectoryId"
        >
          <el-input
            v-model="formData.themeDirectoryId"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入数据源"
          />
        </el-form-item>
        <el-form-item
          label="相应字段"
          prop="wynDirectoryId"
        >
          <el-input
            v-model="formData.wynDirectoryId"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入分类ID"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="show">取消</el-button>
        <el-button
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { saveItem } from '@/api/setting'
import { ENUM } from '@/lib/Constant'

export default {
  components: {},
  mixins: [EditMixins],
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  data() {
    return {
      visible: false,
      url: {
        add: saveItem
      },
      kpiFunction: ENUM.kpiFunction,
      factoryList: ENUM.factoryList,
      levelList: ENUM.levelList,
      statusList: [
        {
          value: 0,
          label: '正常',
          type: 'success'
        },
        {
          value: 1,
          label: '废弃',
          type: 'warning'
        }
      ],
      formData: {
        directoryName: null,
        wynDirectoryId: null,
        themeDirectoryId: null
      },
      rules: {
        directoryName: [
          {
            required: true,
            message: '请输入目录名称',
            trigger: 'change'
          }
        ],
        wynDirectoryId: [
          {
            required: true,
            message: '请输入目录',
            trigger: 'change'
          }
        ],
        themeDirectoryId: [
          {
            required: true,
            message: '请输入分类',
            trigger: 'change'
          }
        ]
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    console.log('新增页面')
  },
  methods: {
    show() {
      this.visible = false
      console.log(this.formData.parentName)
    }
  }
}
</script>
<style scoped>
.titles {
  font-weight: bold;
  font-size: 16px;
  color: black;
  padding-bottom: 6px;
  border-bottom: 1px solid #dcdfe6;
}
.elForm {
  margin-top: 10px;
}
</style>
