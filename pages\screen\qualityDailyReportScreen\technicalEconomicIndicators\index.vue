<template>
  <div class="container">
    <div class="chart-row">
      <div class="chart-box">
        <screen-border-multi>
          <template v-slot:title>
            <div class="tabs-class">
              <div
                v-for="(item,index) in tabList"
                :key="item.id"
                :class="{'tab-pane-active': item.active}"
                class="tab-pane"
                @click="clickTabPane(item, index)">
                <div class="tab-pane-title-class">
                  <div>{{ item.title }}</div>
                  <div
                    v-if="item.active"
                    class="tab-pane-img">
                    <img
                      class="tab-pane-img2"
                      src="@/assets/images/screen/tab-pane-active-line2.png"
                      alt="">
                    <img
                      class="tab-pane-img1"
                      src="@/assets/images/screen/tab-pane-active-line.png"
                      alt="">
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-slot:default>
            <div
              v-if="isLoading"
              class="loading-container">
              <div class="loading-spinner"/>
              <span class="loading-text">数据加载中...</span>
            </div>
            <custom-table
              v-else-if="active === 0"
              ref="originRef"
              :show-table="true"
              :show-edit="true"
              :key="'origin'"
              :title="'原始非计划'"
              :setting="tableObj.setting"
              :url-list="tableObj.url.list"
              :url-save="tableObj.url.save"
              :select-date="selectDate"
              class="custom-table-origin"
              @close="tableVisible.origin = false"
            />
            <div
              v-else-if="active === 1"
              class="chart-content">
              <bars-chart-group
                :chart-data="processData"
                :x-data="processXData"
                :show-label="true"
                :show-legend="false"
                :bar-width="20"
                :height="200"
                class="chart-content-bars-chart-group"
                unit="吨"
              />
              <div
                v-if="processDescription"
                :title="processDescription"
                class="chart-content-description">
                {{ processDescription }}
              </div>
            </div>
            <div
              v-else-if="active === 2"
              class="chart-content">
              <bars-chart-group
                :chart-data="erpData"
                :x-data="erpXData"
                :show-label="true"
                :show-legend="false"
                :bar-width="20"
                :height="200"
                unit="吨"
                class="chart-content-bars-chart-group"
              />
              <div
                v-if="erpDescription"
                :title="erpDescription"
                class="chart-content-description">
                {{ erpDescription }}
              </div>
            </div>
          </template>
        </screen-border-multi>
      </div>
      <div class="chart-box">
        <screen-border title="终判产量及综合非计划率情况">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/qualityDailyReportScreen/edit'"
              class="screen-btn"
              @click="handleDialogOperationVisible('situation')"
            >
              <el-icon
                class="el-icon-edit-outline"

              />
              操作
            </span>
          </template>
          <line-chart
            :chart-data="lineChartData.chartData"
            :chart-data2="lineChartData.chartData2"
            :x-data="lineChartData.xData"
            :show-legend="true"
            :height="200"
            :show-label="true"
            :bar-width="20"
            unit="%"
          />
          <template v-slot:bottom>
            <div class="chart-footer">
              <el-input
                v-model="processAppealDesc"
                :rows="2"
                type="textarea"
                class="chart-input"
                resize="none"
              />
            </div>
          </template>
        </screen-border>
      </div>
    </div>

    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="技经指标统计">
          <!-- <template v-slot:headerRight>
            <span
              v-command="'/screen/qualityDailyReportScreen/edit'"
              class="screen-btn"
              @click="handleDialogOperationVisible('indicator')"
            >
              <el-icon
                class="el-icon-edit-outline"

              />
              操作
            </span>
          </template> -->
          <div class="wrapper">
            <div class="wrapper-box">
              <div
                v-for="(item, index) in textImageData"
                :key="index"
                class="text-image-item">
                <img
                  :src="item.image"
                  alt="图标"
                >
                <div class="text-image-item-wrapper">
                  <div class="text-image-item-title">{{ item.title }}</div>
                  <div class="text-image-item-value">
                    <span class="text">{{ item.value }}</span>
                    <span class="unit">{{ item.unit }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="wrapper-box-chart">
              <div class="wrapper-box-chart-item">
                <pie-chart
                  :chart-data="pieChartData"
                  :color="['#F6B93B', '#E74C3C', '#00B2A9', '#8E44AD']"
                  unit="数量"
                />
              </div>
            </div>
          </div>

        </screen-border>
      </div>

      <div class="chart-box">
        <screen-border title="判废情况">
          <!-- <template v-slot:headerRight>
            <span
              v-command="'/screen/qualityDailyReportScreen/edit'"
              class="screen-btn"
              @click="handleDialogOperationVisible('waste')"
            >
              <el-icon
                class="el-icon-edit-outline"

              />
              操作
            </span>
          </template> -->
          <bars-chart
            :chart-data="judgmentData"
            :x-data="judgmentXData"
            :show-label="true"
            :show-legend="false"
            :bar-width="20"
            :height="200"
            unit="吨"
          />
          <template v-slot:bottom>
            <div class="chart-footer">
              <el-input
                v-model="judgmentDataDesc"
                :rows="2"
                type="textarea"
                class="chart-input"
                resize="none"
              />
            </div>
          </template>
        </screen-border>
      </div>
    </div>
    <custom-table
      v-if="tableVisible.situation"
      ref="situationRef"
      :show-table="false"
      :key="'situation'"
      :title="'终判产量及综合非计划率情况'"
      :setting="tableObjTwo.setting"
      :url-list="tableObjTwo.url.list"
      :url-save="tableObjTwo.url.save"
      :select-date="selectDate"
      @close="tableVisible.situation = false"
      @save-success="fetchAllData"
    />
    <custom-table
      v-if="tableVisible.indicator"
      ref="indicatorRef"
      :show-table="false"
      :key="'indicator'"
      :title="'技经指标统计'"
      :setting="tableObjThree.setting"
      :url-list="tableObjThree.url.list"
      :url-save="tableObjThree.url.save"
      :select-date="selectDate"
      @close="tableVisible.indicator = false"
      @save-success="fetchAllData"
    />
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityDailyReportScreen/components/screen-border.vue'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import BarsChartGroup from '@/pages/screen/qualityDailyReportScreen/components/bars-chart-group.vue'
import PieChart from '@/pages/screen/qualityDailyReportScreen/components/pie-chart.vue'
import LineChart from '@/pages/screen/qualityDailyReportScreen/components/line-chart.vue'
import BarsChart from '@/pages/screen/qualityDailyReportScreen/components/bars-chart.vue'
import pieImg from '@/assets/images/screen/quality-daily-report-screen-pie.png'
import productionImg from '@/assets/images/screen/quality-daily-report-screen-production.png'
import rightImg from '@/assets/images/screen/quality-daily-report-screen-right.png'
import CustomTable from '@/pages/screen/qualityDailyReportScreen/components/custom-table.vue'
import {
  originalNonPlan,
  findAllOriginalUnplannedDate,
  originalNonPlanSave,
  finalJudgeProduction,
  finalJudgeProductionSave,
  technicalEconomicIndicators,
  technicalEconomicIndicatorsSave,
  qmsInvalidationStatusFind,
  findAllInvalidationStatusDate,
  qmsInvalidationStatusSave,
  newTechnicalEconomicIndicators,
  findAllProcessUnplannedDate,
  findAllErpUnplannedDate
} from '@/api/screen'
import { post } from '@/lib/Util'

export default {
  name: 'TechnicalEconomicIndicators',
  components: {
    ScreenBorder,
    LineChart,
    PieChart,
    BarsChart,
    BarsChartGroup,
    CustomTable,
    ScreenBorderMulti
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      active: 0,
      isLoading: false,
      tabList: [
        {
          id: '1',
          active: true,
          title: '原始非计划'
        },
        {
          id: '2',
          active: false,
          title: '过程非计划'
        },
        {
          id: '3',
          active: false,
          title: 'ERP非计划'
        }
      ],
      processData: [],
      processXData: [],
      processDescription: '',
      erpData: [],
      erpXData: [],
      erpDescription: '',
      tableVisible: {
        origin: false,
        situation: false,
        indicator: false,
        waste: false
      },
      tableObj: {
        url: {
          list: findAllOriginalUnplannedDate,
          save: originalNonPlanSave
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'plate_no',
            keySave: 'plate_no',
            label: '物料号',
            width: '150'
          },
          {
            keyQuery: 'res_ac',
            keySave: 'res_ac',
            label: '余材原因',
            width: '150'
          },
          {
            keyQuery: 'zrdw',
            keySave: 'zrdw',
            label: '责任单位',
            width: '150'
          },
          {
            keyQuery: 'org_ord_no',
            keySave: 'org_ord_no',
            label: '原始订单',
            width: '200'
          },
          {
            keyQuery: 'cust_name',
            keySave: 'cust_name',
            label: '责任公司',
            width: '200'
          },
          {
            keyQuery: 'aply_stdspec',
            keySave: 'aply_stdspec',
            label: '订单标准',
            width: '150'
          },
          {
            keyQuery: 'thk1',
            keySave: 'thk1',
            label: '厚度',
            width: '150'
          },
          {
            keyQuery: 'wid1',
            keySave: 'wid1',
            label: '宽度',
            width: '150'
          },
          {
            keyQuery: 'len1',
            keySave: 'len1',
            label: '长度',
            width: '150'
          },
          {
            keyQuery: 'wgt1',
            keySave: 'wgt1',
            label: '重量',
            width: '150'
          },
          {
            keyQuery: 'ord_fl1',
            keySave: 'ord_fl1',
            label: '余材代码',
            width: '150'
          },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '缺陷',
            width: '150'
          },
          {
            keyQuery: 'prod_remark',
            keySave: 'prod_remark',
            label: '产品备注',
            width: '500'
          }
        ]
      },
      tableObjTwo: {
        url: {
          list: finalJudgeProduction,
          save: finalJudgeProductionSave
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'setTime',
            keySave: 'setTime',
            label: '日期（格式：yyyy-MM-dd）',
            width: '430'
          },
          {
            keyQuery: 'targetValue',
            keySave: 'targetValue',
            label: '目标值',
            width: '430'
          },
          {
            keyQuery: 'actualValue',
            keySave: 'actualValue',
            label: '实际值',
            width: '430'
          },
          {
            keyQuery: 'remark',
            keySave: 'remark',
            label: '备注'
          }
        ]
      },
      tableObjThree: {
        url: {
          list: technicalEconomicIndicators,
          save: technicalEconomicIndicatorsSave
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'WGT',
            keySave: 'WGT',
            label: '生产量',
            width: '150'
          },
          {
            keyQuery: 'qualifiedQuantity',
            keySave: 'qualifiedQuantity',
            label: '原钢种合格量',
            width: '150'
          },
          {
            keyQuery: 'oneQualifiedQuantity',
            keySave: 'oneQualifiedQuantity',
            label: '原钢种一次合格量',
            width: '150'
          },
          {
            keyQuery: 'passRate',
            keySave: 'passRate',
            label: '原钢种合格率',
            width: '150'
          },
          {
            keyQuery: 'onePassRate',
            keySave: 'onePassRate',
            label: '原钢种一次合格率',
            width: '150'
          },
          {
            keyQuery: 'straighteningQuantity',
            keySave: 'straighteningQuantity',
            label: '矫直量',
            width: '150'
          },
          {
            keyQuery: 'revisedJudgmentQuantity',
            keySave: 'revisedJudgmentQuantity',
            label: '改判量',
            width: '150'
          },
          {
            keyQuery: 'agreementQuantity',
            keySave: 'agreementQuantity',
            label: '协议量',
            width: '150'
          },
          {
            keyQuery: 'wastesQuantity',
            keySave: 'wastesQuantity',
            label: '判废量',
            width: '150'
          }
        ]
      },
      rawMaterialPlanData: [],
      lineChartData: {
        chartData: [],
        chartData2: [],
        xData: []
      },
      textImageData: [],
      economicIndicatorsData: [],
      judgmentData: [],
      judgmentXData: [],
      judgmentDataDesc: '',
      pieChartData: [],
      processAppealDesc: '',
      processChartColors: [
        '#2772F0',
        '#F5B544',
        '#51DF81',
        '#FFE638',
        '#D45454'
      ]
    }
  },
  watch: {
    selectDate: {
      handler() {
        this.fetchAllData()
        this.fetchProcessData()
        this.fetchErpData()
      }
    }
  },
  created() {
    this.fetchAllData()
  },
  methods: {
    //点检tab切换
    clickTabPane(item, index) {
      this.tabList.forEach(item => {
        item.active = false
      })
      item.active = true
      this.active = index

      // 设置loading状态
      this.isLoading = true

      if (index === 1) {
        this.fetchProcessData().finally(() => {
          this.isLoading = false
        })
      } else if (index === 2) {
        this.fetchErpData().finally(() => {
          this.isLoading = false
        })
      } else {
        // 对于原始非计划，可能不需要异步加载数据，直接关闭loading
        setTimeout(() => {
          this.isLoading = false
        }, 300)
      }
    },
    // 独立请求方法 - 过程非计划
    async fetchProcessData() {
      try {
        const res = await post(findAllProcessUnplannedDate, {
          setTime: this.selectDate
        })
        console.log('%c 过程非计划', 'color: red', res.data)

        // 数据处理逻辑
        if (res.data) {
          // 维持X轴数据为所有res_ac
          this.processXData = res.data.flatMap(item =>
            item.dataList.map(subItem => subItem.res_ac)
          )

          // 创建zrdw到颜色的映射
          const zrdwColorMap = {}
          res.data.forEach((item, index) => {
            zrdwColorMap[item.zrdw] = this.processChartColors[
              index % this.processChartColors.length
            ]
          })

          // 准备柱状图数据，添加itemStyle来设置不同颜色
          this.processData = [
            {
              name: '非计划量',
              data: res.data.flatMap(item =>
                item.dataList.map(subItem => ({
                  value: subItem.WGT,
                  name: subItem.res_ac,
                  zrdw: item.zrdw, // 添加zrdw属性用于tooltip显示
                  itemStyle: {
                    color: zrdwColorMap[item.zrdw]
                  }
                }))
              )
            }
          ]

          // 设置描述文本
          this.processDescription = res.remark || ''
        }

        return res
      } catch (error) {
        console.error('过程非计划:', error)
        return null
      }
    },
    // 独立请求方法 - ERP非计划
    async fetchErpData() {
      try {
        const res = await post(findAllErpUnplannedDate, {
          setTime: this.selectDate
        })
        console.log('%c ERP非计划', 'color: red', res.data)

        // 数据处理逻辑
        if (res.data) {
          // 将 corrtct_defect 作为 X 轴数据
          this.erpXData = res.data.map(item => item.corrtct_defect)

          // 准备柱状图数据
          this.erpData = [
            {
              name: '非计划量',
              data: res.data.map(item => ({
                value: Math.round(item.WGT),
                name: item.corrtct_defect
              }))
            }
          ]

          // 设置描述文本
          this.erpDescription = res.remark || ''
        }

        return res
      } catch (error) {
        console.error('ERP非计划:', error)
        return null
      }
    },
    fetchAllData(type = '') {
      if (type === '终判产量及综合非计划率情况') {
        this.fetchSituationData()
      } else if (type === '技经指标统计') {
        this.fetchIndicatorData()
      } else if (type === '判废情况') {
        this.fetchWasteData()
      } else {
        this.fetchSituationData()
        this.fetchIndicatorData()
        this.fetchWasteData()
      }
    },
    // 独立请求方法 - 终判产量及综合非计划率情况
    async fetchSituationData() {
      try {
        const res = await post(finalJudgeProduction, {
          setTime: this.selectDate
        })

        // 新增数据处理逻辑
        this.lineChartData = {
          xData: res.data.map(item => item.setTime), // 提取日期作为x轴
          chartData: [
            {
              name: '目标',
              data: res.data.map(item => item.targetValue)
            }
          ],
          chartData2: [
            {
              name: '实际',
              data: res.data.map(item => item.actualValue)
            }
          ]
        }

        return res
      } catch (error) {
        console.error('终判产量及综合非计划率情况:', error)
        return null
      }
    },

    // 独立请求方法 - 技经指标统计
    async fetchIndicatorData() {
      try {
        const res = await post(newTechnicalEconomicIndicators, {
          setTime: this.selectDate
        })
        console.log('%c 新技经指标统计', 'color: red', res.data)

        // 新增数据处理逻辑
        const fieldMap = {
          straighteningQuantity: '矫直量',
          revisedJudgmentQuantity: '改判量',
          agreementQuantity: '协议量',
          wastesQuantity: '判废量'
        }

        let data = res.data.length > 0 && res.data[0] ? res.data[0] : {}

        // 转换数据结构
        this.pieChartData = Object.entries(data)
          .filter(([key]) => fieldMap[key])
          .map(([key, val]) => ({
            name: fieldMap[key],
            value: Number(val) || 0
          }))

        // 更新文本展示数据
        this.textImageData = [
          {
            image: productionImg,
            title: '生产量',
            value: data.WGT,
            unit: '吨'
          },
          {
            image: rightImg,
            title: '原钢种合格量',
            value: data.qualifiedQuantity,
            unit: '吨'
          },
          {
            image: pieImg,
            title: '原钢种合格率',
            value: data.passRate,
            unit: '%'
          },
          {
            image: pieImg,
            title: '原钢种一次合格量',
            value: data.oneQualifiedQuantity,
            unit: '吨'
          },
          {
            image: pieImg,
            title: '原钢种一次合格率',
            value: data.onePassRate,
            unit: '%'
          }
        ]

        return res
      } catch (error) {
        console.error('技经指标统计:', error)
        return null
      }
    },
    // 独立请求方法 - 判废情况
    async fetchWasteData() {
      try {
        const res = await post(findAllInvalidationStatusDate, {
          setTime: this.selectDate
        })
        console.log('%c 判废情况', 'color: red', res.data)

        // 数据处理逻辑
        if (res.data) {
          // 将 defect 作为 X 轴数据
          this.judgmentXData = res.data.map(item => item.defect)

          // 准备柱状图数据
          this.judgmentData = [
            {
              name: '判废量',
              data: res.data.map(item => ({
                value: item.WGT,
                name: item.defect
              }))
            }
          ]

          // 设置描述文本
          this.judgmentDataDesc = res.remark || ''
        }

        return res
      } catch (error) {
        console.error('判废情况:', error)
        return null
      }
    },

    handleDialogOperationVisible(type) {
      console.log('%c type', 'color', type)
      // 统一控制弹窗显示状态
      this.tableVisible = {
        origin: false,
        situation: false,
        indicator: false,
        waste: false
      }
      this.$set(this.tableVisible, type, true)

      this.$nextTick(() => {
        // 根据类型映射对应的ref名称
        const refMap = {
          origin: 'originRef',
          situation: 'situationRef',
          indicator: 'indicatorRef',
          waste: 'wasteRef'
        }
        const refName = refMap[type]
        this.$refs[refName] && this.$refs[refName].openDialog()
      })
    }
  }
}
</script>

<style scoped lang="less">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: hidden;

  .chart-row {
    margin-bottom: 10px;
  }

  .chart-row,
  .table-row {
    display: flex;
    gap: 10px;
    height: 50%;
    width: 100%;
  }

  .chart-box,
  .table-box {
    flex: 1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .chart-footer {
    margin: 0px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    padding: 5px 8px;
    background: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);
    width: 100%;
  }

  .chart-input {
    height: 100%;
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      height: 100%;
      padding: 0;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
        border-color: transparent !important;
      }
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      border-color: transparent !important;
    }
  }

  .tabs-class {
    display: flex;
    flex-direction: row;
    .tab-pane {
      color: #ffffffbf;
      margin-right: 21px;
    }
    .tab-pane-active {
      color: #ffffff;
    }
    .tab-pane-title-class {
      display: flex;
      flex-direction: column;
      position: relative;
      .tab-pane-img {
        .tab-pane-img2 {
          width: 100%;
          position: absolute;
          bottom: 0;
          left: 0;
        }
        .tab-pane-img1 {
          width: 100%;
          position: absolute;
          bottom: 0;
          left: 0;
          margin-bottom: 7px;
        }
      }
    }
  }

  /deep/ .el-table {
    background-color: transparent;
    width: 100% !important;
    table-layout: fixed;

    &::before {
      display: none;
    }

    th {
      background-color: rgba(31, 198, 255, 0.3);
      color: #fff;
      border-color: #1fc6ff;
    }

    td {
      background-color: transparent;
      color: #fff;
      border-color: #2e4262;
    }

    tr {
      background-color: transparent;
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-content {
    padding: 10px;
    box-sizing: border-box;
  }
  .text-image-section {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .wrapper {
    display: flex;
    flex-flow: row wrap;

    .wrapper-box {
      display: flex;
      flex-flow: row wrap;
      width: 52%;
      transform: translateX(24px);

      .text-image-item {
        display: flex;
        align-items: center;
        width: 200px;
        height: 64px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #fff;
        margin: 10px 10px 20px 10px;

        img {
          width: 64px;
          height: 64px;
        }

        .text-image-item-wrapper {
          flex: 1;
          margin-left: 10px;

          .text-image-item-title {
            margin-bottom: 8px;
          }
        }
      }
    }

    .wrapper-box-chart {
      width: 48%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;

      .wrapper-box-chart-item {
        width: 100%;
        height: 300px;
        position: absolute;
        top: 1px;
        right: 0;
      }
    }
  }

  .chart-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;

    .chart-content-description {
      height: 52px;
      margin-top: 10px;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid rgba(31, 198, 255, 0.3);
      background: rgba(0, 0, 0, 0.2);
      color: #fff;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      cursor: pointer;
    }
    .chart-content-bars-chart-group {
      flex: 1;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #fff;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(31, 198, 255, 0.3);
      border-radius: 50%;
      border-top-color: #1fc6ff;
      animation: spin 1s linear infinite;
      margin-bottom: 10px;
    }

    .loading-text {
      font-size: 14px;
      color: #1fc6ff;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /deep/ .border-content {
    position: unset !important;

    .scroll-wrapper .chart-tit {
      margin: 0;
    }
  }

  .custom-table-origin {
    /deep/ .chart-tit {
      position: absolute;
      right: 10px;
      top: 5px;
    }
  }
}
</style>
