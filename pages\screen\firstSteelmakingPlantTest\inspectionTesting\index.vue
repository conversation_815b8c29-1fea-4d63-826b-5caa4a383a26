<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="8"
          class="full-height">
          <screen-border :title="'铁水'">
            <div class="chart-wrapper">
              <p-line-chart
                :show-legend="true"
                :show-custom-format="true"
                :chart-data="steelYesterday.lineBar"
                :chart-data2="steelYesterday.lineBar2"
                :chart-data3="steelYesterday.lineBar3"
                :x-data="steelYesterday.lineBarX"/>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="6"
          class="full-height">
          <screen-border :title="'成分合格率'">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="()=>{
                  compositionRate.save = false;
                  compositionRate.dialogVisible = true
                  getCompositionRate()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明录入
              </span>
              <span
                class="screen-btn"
                @click="()=>{
                  compositionRate.save = true;
                  compositionRate.dialogVisible = true
                  getCompositionRate()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明
              </span>
            </template>
            <div class="chart-wrapper">
              <div
                class="operate-box text-right">
                <el-radio-group
                  v-model="pieChart.dateType1"
                  size="mini"
                  class="screen-input"
                  @input="changePieChart($event)">
                  <el-radio-button :label="0">日</el-radio-button>
                  <el-radio-button :label="1">月</el-radio-button>
                </el-radio-group>
              </div>
              <div
                class="chart"
                @click="showRateDetail()">
                <pie-rate-chart
                  :chart-data="Number(pieChart.RATE)"
                  :unit="'%'"
                  :title="'成分合格率'"
                  :title-num="pieChart.RATE + '%'"
                  :title-text="'不合格炉数：' + pieChart.NUM"
                  :label-width="22"
                  :color="pieChart.color"
                  :vertical="false"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="10"
          class="full-height">
          <screen-border :title="'工艺异常'">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="showProcessUpsets">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="processUpsets.showGridData"
                :span-method="handleObjectSpan"
                :format-span-data="processUpsets.showGridData"
                :height="unPlanedTotal.maxHeight"
                class="center-table"
                border>
                <el-table-column
                  property="OCCR_DATE"
                  width="120"
                  label="时间">
                  <template v-slot="{ row }">
                    <div>
                      {{ formatDate(row.OCCR_DATE) }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  property="HEAT_NO"
                  label="炉号"/>
                <el-table-column
                  property="STLGRD"
                  label="钢种"/>
                <el-table-column
                  property="ANOMALY"
                  label="异常描述"
                  width="170">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.ANOMALY)"/>
                  </template>
                </el-table-column>
                <el-table-column
                  property="RES_UNIT1"
                  label="责任单位"/>
                <el-table-column
                  property="RES_REASON"
                  label="原因"
                  width="170">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.RES_REASON)"/>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="7"
          class="full-height">
          <screen-border title="原辅料情况">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="unPlanedTotal.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div
              ref="table2"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="unPlanedTotal.showGridData"
                :max-height="unPlanedTotal.maxHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  align="center"
                  label="类别"
                  property="A_LIST"
                  width="100"/>
                <el-table-column
                  align="center"
                  property="B_LIST"
                  label="质量情况">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.B_LIST)"/>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <screen-border :title="'N含量'">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="nContentShow">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="chart-wrapper">
              <div
                class="chart">
                <p-line-chart
                  :show-legend="true"
                  :chart-data="[
                    {
                      name:'计划',
                      data:nContentData.showGridData.map(item=>{
                        return item.PLANNED_VALUE
                      })
                    },
                    {
                      name:'实际',
                      data:nContentData.showGridData.map(item=>{
                        return item.ACTUAL_VALUE
                      })
                    },
                    {
                      name:'每日最高值',
                      data:nContentData.showGridData.map(item=>{
                        return item.QUALITY_TYPES
                      })
                    },
                  ]"
                  :x-data="nContentData.showGridData.map(item=>{
                    return item.PROD_YEAR.slice(4,8)
                })"/>
              </div>
              <div style="height: 120px;display: flex;justify-content: center;align-items: center;font-weight: 600;font-size: 20px;color: #0c78ff;flex-wrap: wrap;overflow-y: auto">
                <div
                  v-for="(item, index) in nContentData.shownotes"
                  :key="index"
                  style="width:100% ;padding: 0 10px;display: flex;align-items: center">
                  {{ item }}
                </div>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="9"
          class="full-height">
          <screen-border :title="'渣样'">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="slagSample.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="slagSample.showGridData"
                :max-height="slagSample.maxHeight==null?null:slagSample.maxHeight - 55"
                class="center-table"
                border>
                <el-table-column
                  label="工序"
                  property="A_LIST"/>
                <el-table-column
                  property="B_LIST"
                  width="130"
                  label="炉次号"/>
                <el-table-column
                  property="C_LIST"
                  label="钢种"/>
                <el-table-column
                  property="S"
                  label="S">
                  <template v-slot="{ row }">
                    {{ fiex2(row, "S") }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="SiO2"
                  label="SiO2">
                  <template v-slot="{ row }">
                    {{ fiex2(row, "SiO2") }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="CaO"
                  label="CaO">
                  <template v-slot="{ row }">
                    {{ fiex2(row, "CaO") }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="Fe"
                  label="Fe">
                  <template v-slot="{ row }">
                    <template v-if="(row.Fe>18&&row.A_LIST=='转炉')||(row.Fe>1&&row.A_LIST=='精炼')">
                      <div style="color: red">
                        {{ fiex2(row, "Fe") }}
                      </div>
                    </template>
                    <template v-else>
                      {{ fiex2(row, "Fe") }}
                    </template>
                  </template>
                </el-table-column>
                <el-table-column
                  property="MgO"
                  label="MgO">
                  <template v-slot="{ row }">
                    {{ fiex2(row, "MgO") }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="Al2O3"
                  label="Al2O3">
                  <template v-slot="{ row }">
                    {{ fiex2(row, "Al2O3") }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="P2O5"
                  label="P2O5">
                  <template v-slot="{ row }">
                    {{ fiex2(row, "P2O5") }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="MnO"
                  label="MnO">
                  <template v-slot="{ row }">
                    {{ fiex2(row, "MnO") }}
                  </template>
                </el-table-column>
                <el-table-column
                  property="R"
                  label="R">
                  <template v-slot="{ row }">
                    <template v-if="(row.R>3.8&&row.A_LIST=='转炉')||(row.R>9&&row.A_LIST=='精炼')">
                      <div style="color: red">
                        {{ fiex2(row, "R") }}
                      </div>
                    </template>
                    <template v-else>
                      {{ fiex2(row, "R") }}
                    </template>
                  </template>
                </el-table-column>
              </el-table>
              <div style="height: 120px;display: flex;justify-content: center;align-items: center;font-weight: 600;font-size: 20px;color: #0c78ff;flex-wrap: wrap;overflow-y: auto">
                <div
                  v-for="(item, index) in slagSample.shownotes"
                  :key="index"
                  style="width:100% ;padding: 0 10px;display: flex;align-items: center">
                  {{ item }}
                </div>
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible.sync="dialogVisible3"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          成分不合格详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList3"
        class="center-table"
        border>
        <el-table-column
          label="物料号"
          property="matId"/>
        <el-table-column
          label="钢种"
          property="steelGrade"/>
        <el-table-column
          label="不合格元素及含量"
          property="chemCd"/>
        <el-table-column
          label="规格"
          property="thk"/>
        <el-table-column
          label="处置"
          property="opinion"/>
      </el-table>
    </el-dialog>
    <el-dialog
      :visible.sync="nContentData.show"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="N含量">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearContent">
              重置数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveContent">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          N含量
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <div style="display: flex;align-items: center;color: #fff">
          备注：<el-input
            v-loading="loading"
            v-model="nContentData.notes"
            type="textarea"
            style="width: 600px"/>
        </div><br>
        <el-table
          v-loading="loading"
          :data="nContentData.gridData"
          border>
          <el-table-column
            property="PROD_YEAR"
            label="时间"/>
          <el-table-column
            property="PLANNED_VALUE"
            label="计划">
            <template v-slot="{ row }">
              <el-input v-model="row.PLANNED_VALUE"/>
            </template>
          </el-table-column>
          <el-table-column
            property="ACTUAL_VALUE"
            label="实际">
            <template v-slot="{ row }">
              <el-input v-model="row.ACTUAL_VALUE"/>
            </template>
          </el-table-column>
          <el-table-column
            property="QUALITY_TYPES"
            label="每日最高值">
            <template v-slot="{ row }">
              <el-input v-model="row.QUALITY_TYPES"/>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="unPlanedTotal.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="unPlaned">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unPlanedTotal')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importUnfinishedData')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveunPlanedTotal">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          原辅料情况
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="unPlanedTotal.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            property="A_LIST"
            label="类别">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            label="原因详细">
            <template v-slot="{ row }">
              <el-input
                v-model="row.B_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>

          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'unPlanedTotal')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('unPlanedTotal')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="slagSample.dialogVisible"
      :width="'1500px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="渣样">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('slagSample')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importUnfinishedData2')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview2"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveslagSample">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          渣样
        </div>
      </template>
      <div style="display: flex;align-items: center;color: #fff">
        备注：<el-input
          v-loading="loading"
          v-model="slagSample.notes"
          type="textarea"
          style="width: 600px"/>
      </div><br>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="slagSample.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>

          <el-table-column
            label="工序"
            property="A_LIST">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            label="炉次号">
            <template v-slot="{ row }">
              <el-input v-model="row.B_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="C_LIST"
            label="钢种">
            <template v-slot="{ row }">
              <el-input v-model="row.C_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="S"
            label="S">
            <template v-slot="{ row }">
              <el-input v-model="row.S"/>
            </template>
          </el-table-column>
          <el-table-column
            property="SiO2"
            label="SiO2">
            <template v-slot="{ row }">
              <el-input v-model="row.SiO2"/>
            </template>
          </el-table-column>
          <el-table-column
            property="CaO"
            label="CaO">
            <template v-slot="{ row }">
              <el-input v-model="row.CaO"/>
            </template>
          </el-table-column>
          <el-table-column
            property="Fe"
            label="Fe">
            <template v-slot="{ row }">
              <el-input v-model="row.Fe"/>
            </template>
          </el-table-column>
          <el-table-column
            property="MgO"
            label="MgO">
            <template v-slot="{ row }">
              <el-input v-model="row.MgO"/>
            </template>
          </el-table-column>
          <el-table-column
            property="Al2O3"
            label="Al2O3">
            <template v-slot="{ row }">
              <el-input v-model="row.Al2O3"/>
            </template>
          </el-table-column>
          <el-table-column
            property="P2O5"
            label="P2O5">
            <template v-slot="{ row }">
              <el-input v-model="row.P2O5"/>
            </template>
          </el-table-column>
          <el-table-column
            property="MnO"
            label="MnO">
            <template v-slot="{ row }">
              <el-input v-model="row.MnO"/>
            </template>
          </el-table-column>
          <el-table-column
            property="R"
            label="R">
            <template v-slot="{ row }">
              <el-input v-model="row.R"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'slagSample')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('slagSample')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="compositionRate.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="成分合格率情况说明">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality&&!compositionRate.save"
              class="screen-btn"
              @click="saveCompositionRate">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          成分合格率情况说明
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="compositionRate.gridData"
          class="font-table"
          border>
          <el-table-column
            property="A_LIST"
            label="内容">
            <template
              v-slot="{ row }"
              v-if="!compositionRate.save">
              <el-input
                v-model="row.A_LIST"
                :rows="4"
                type="textarea"/>
            </template>
            <template
              v-slot="{ row }"
              v-else>
              <div
                slot="content"
                v-html="formatText(row.A_LIST)"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            width="210px"
            label="图片说明">
            <template v-slot="{ row, $index }">
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.B_LIST"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item.id"
                    :id="item.id"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !compositionRate.save"
                    @img-delete="handlePasteImgDeleteID($event, index, 'compositionRate')"
                  />
                </li>
              </ul>
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.showPic == unfinished ? [] : row.showPic"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item"
                    :src="item"
                    :is-id="false"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !compositionRate.save"
                    @img-delete="handlePasteImgDelete($event, index, 'compositionRate')"
                  />
                </li>
              </ul>
              <el-upload
                v-if="!compositionRate.save"
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file)=>{
                  handleChange(file, 'compositionRate', $index)
                }"
                :show-file-list="false"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = $index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>

    <!--工艺异常-->
    <el-dialog
      :visible.sync="processUpsets.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="工艺异常">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('processUpsets')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importProcessUpsetsData')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreviewProcessUpsets"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportProcessUpsets">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveProcessUpsets">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          工艺异常
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="processUpsets.gridData"
          border>
          <el-table-column
            property="OCCR_DATE"
            label="时间"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.OCCR_DATE"/>
            </template>
          </el-table-column>
          <el-table-column
            property="HEAT_NO"
            label="炉号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.HEAT_NO"/>
            </template>
          </el-table-column>
          <el-table-column
            property="STLGRD"
            label="钢种"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.STLGRD"/>
            </template>
          </el-table-column>
          <el-table-column
            property="ANOMALY"
            label="异常描述"
            width="290">
            <template v-slot="{ row }">
              <el-input
                v-model="row.ANOMALY"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="RES_UNIT1"
            label="责任单位"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.RES_UNIT1"/>
            </template>
          </el-table-column>
          <el-table-column
            property="RES_REASON"
            label="原因"
            width="280">
            <template v-slot="{ row }">
              <el-input
                v-model="row.RES_REASON"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                class="screen-btn"
                @click="delGridData($index, 'processUpsets')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('processUpsets')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { post } from '@/lib/Util'
import lodash from 'lodash'
import * as _ from 'lodash'
import {
  getChemQualified,
  getChemQualifiedDetail,
  castingMachine,
  firstMorningMeeting
} from '@/api/screen'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border.vue'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import CustomTable from '@/pages/screen/qualityMeeting/component/custom-table'
import PolarChart from '@/pages/screen/energyMeeting/component/polar-chart'
import PieRateChart from '@/pages/screen/qualityMeeting/component/pie-rate-chart'
import PieChart from '@/pages/screen/qualityMeeting/component/pie-chart.vue'
import PLineChart from '../component/p-line-chart.vue'
import { deleteFileByIds, uploadFile } from '@/api/system'
import ImgView from '@/components/ImgView.vue'
export default {
  name: 'inspectionTesting',
  components: {
    ImgView,
    PLineChart,
    PieChart,
    PieRateChart,
    PolarChart,
    CustomTable,
    SingleBarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      flawList3: [],
      editIndex: 0,
      dialogVisible3: false,
      pieChart: {
        total: '',
        RATE: 0, //
        NUM: 0, //
        color: '#19be6b',
        dateType1: 0
      },
      unPlanedTotal: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      nContentData: {
        gridData: [],
        showGridData: [],
        shownotes: [],
        notes: '',
        show: false
      },
      slagSample: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        notes: '',
        shownotes: [],
        dialogVisible: false
      },
      compositionRate: {
        gridData: [],
        save: true,
        dialogVisible: false
      },
      processUpsets: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      // 铁水曲线
      steelYesterday: {
        lineBar: [],
        lineBar2: [],
        lineBar3: {},
        lineBarX: [],
        dialogVisible: false
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getPartRate(
        this.$moment(this.cDate).format('yyyyMMDD'),
        this.$moment(this.cDate).format('yyyyMMDD')
      )
      this.getUnfinished({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '石灰情况'
      })
      this.getslagSample({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '渣样'
      })
      this.getProcessUpsets({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'processUpsets'
      })
      this.nContent()
      this.getCompositionRate()
      this.getSteal()
    }
  },
  mounted() {
    this.unPlanedTotal.maxHeight = this.$refs.table2.offsetHeight
  },
  created() {
    this.cDate = this.selectDate
    this.getPartRate(
      this.$moment(this.cDate).format('yyyyMMDD'),
      this.$moment(this.cDate).format('yyyyMMDD')
    )
  },
  methods: {
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          B_LIST: 'D'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unPlanedTotal.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    handlePreview2(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          B_LIST: 'D',
          C_LIST: 'E',
          S: 'F',
          SiO2: 'G',
          CaO: 'H',
          Fe: 'I',
          MgO: 'J',
          Al2O3: 'K',
          P2O5: 'L',
          MnO: 'I',
          R: 'N'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.slagSample.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    // 成分合格率详情
    showRateDetail() {
      const date = {
        startTime: null,
        endTime: null
      }
      if (this.pieChart.dateType1 === 1) {
        Object.assign(date, this.getResentMonth())
      } else {
        date.startTime = this.$moment(this.cDate).format('yyyyMMDD')
        date.endTime = this.$moment(this.cDate).format('yyyyMMDD')
      }
      post(
        getChemQualifiedDetail +
          `?startTime=${date.startTime}&endTime=${date.endTime}`,
        {}
      ).then(res => {
        this.flawList3 = res
        this.dialogVisible3 = true
      })
    },
    // 成分合格率
    getPartRate(date1, date2) {
      post(getChemQualified + `?startTime=${date1}&endTime=${date2}`, {}).then(
        res => {
          this.pieChart.NUM = res.NUM
          this.pieChart.color = res.PASS_RATE >= 0.96 ? '#19be6b' : '#f32651'
          this.pieChart.RATE = (res.PASS_RATE * 100).toFixed(2)
        }
      )
    },
    getResentMonth() {
      return {
        startTime:
          this.$moment(this.cDate)
            .subtract(1, 'month')
            .format('yyyyMM') + '26',
        endTime: this.$moment(this.cDate).format('yyyyMMDD')
      }
    },
    // 成分合格率 月度
    changePieChart(type) {
      if (type === 1) {
        const dateObj = this.getResentMonth()
        this.getPartRate(dateObj.startTime, dateObj.endTime)
      } else {
        this.getPartRate(
          this.$moment(this.cDate).format('yyyyMMDD'),
          this.$moment(this.cDate).format('yyyyMMDD')
        )
      }
    },
    getUnfinished(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.unPlanedTotal.gridData = _.cloneDeep(resData)
        this.unPlanedTotal.showGridData = _.cloneDeep(resData)
      })
    },
    saveunPlanedTotal() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '石灰情况',
        data: _.map(
          _.sortBy(this.unPlanedTotal.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              SORT_NUM: index + 1,
              FLAG: '石灰情况',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD')
            }
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.unPlanedTotal.dialogVisible = false
        this.getUnfinished({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '石灰情况'
        })
        this.loading = false
      })
    },
    // 铸坯质量日期导入
    importUnfinishedData(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        FLAG: '石灰情况',
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD')
      }).then(res => {
        //
        this.loading = false
        this.unPlanedTotal.gridData = lodash.cloneDeep(res.data)
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    importUnfinishedData2(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        FLAG: '渣样',
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD')
      }).then(res => {
        //
        this.loading = false
        let datanotes =
          _.filter(res.data, item => item.SORT_NUM == 0)[0] != undefined
            ? _.filter(res.data, item => item.SORT_NUM == 0)[0]
            : {}
        this.slagSample.shownotes = _.cloneDeep(
          datanotes.A_LIST != undefined ? datanotes.A_LIST.split('\n') : []
        )
        this.slagSample.notes = datanotes.A_LIST
        let resData = _.sortBy(
          _.filter(res.data, item => item.SORT_NUM != 0),
          item => item.SORT_NUM
        )
        let resData2 = _.map(resData, item => {
          return {
            ...item,
            ...JSON.parse(item.D_LIST)
          }
        })
        this.slagSample.gridData = _.cloneDeep(resData2)
        if (!resData2.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    getslagSample(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let datanotes =
          _.filter(res.data, item => item.SORT_NUM == 0)[0] != undefined
            ? _.filter(res.data, item => item.SORT_NUM == 0)[0]
            : {}
        this.slagSample.shownotes = _.cloneDeep(
          datanotes.A_LIST != undefined ? datanotes.A_LIST.split('\n') : []
        )
        this.slagSample.notes = datanotes.A_LIST
        let resData = _.sortBy(
          _.filter(res.data, item => item.SORT_NUM != 0),
          item => item.SORT_NUM
        )
        let resData2 = _.map(resData, item => {
          return {
            ...item,
            ...JSON.parse(item.D_LIST)
          }
        })
        this.slagSample.gridData = _.cloneDeep(resData2)
        this.slagSample.showGridData = _.cloneDeep(resData2)
      })
    },
    saveslagSample() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '渣样',
        data: _.map(
          _.sortBy(this.slagSample.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              SORT_NUM: index + 1,
              FLAG: '渣样',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              D_LIST: JSON.stringify({
                S: item.S,
                SiO2: item.SiO2,
                CaO: item.CaO,
                Fe: item.Fe,
                MgO: item.MgO,
                Al2O3: item.Al2O3,
                P2O5: item.P2O5,
                MnO: item.MnO,
                R: item.R
              })
            }
          }
        )
      }
      params.data.push({
        SORT_NUM: 0,
        A_LIST: this.slagSample.notes,
        FLAG: '渣样',
        PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD')
      })
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.slagSample.dialogVisible = false
        this.getslagSample({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '渣样'
        })
        this.loading = false
      })
    },
    //   获取N含量
    nContent() {
      post(firstMorningMeeting.inspectionInit, {
        startTime: this.$moment(this.cDate)
          .subtract(9, 'days')
          .format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'N'
      }).then(res => {
        const datas = {
          PROD_YEAR: '',
          PROD_DATE: '',
          PLANNED_VALUE: '',
          ACTUAL_VALUE: '',
          QUALITY_TYPES: ''
        }
        let data = []
        for (let i = 9; i >= 0; i--) {
          data.push({
            ...datas,
            PROD_YEAR: this.$moment(this.cDate)
              .subtract(i, 'days')
              .format('yyyyMMDD')
          })
        }
        if (res.code == 200) {
          data = lodash.map(data, item => {
            return lodash.filter(res.data['01'], items => {
              return items.PROD_YEAR == item.PROD_YEAR
            })[0] == undefined
              ? item
              : lodash.filter(res.data['01'], items => {
                  return items.PROD_YEAR == item.PROD_YEAR
                })[0]
          })
        }
        this.nContentData.showGridData = lodash.cloneDeep(data)
        this.nContentData.gridData = lodash.cloneDeep(data)
        if (res.data['02'][0]) {
          this.nContentData.notes = lodash.cloneDeep(
            res.data['02'][0].NOTES == undefined ? '' : res.data['02'][0].NOTES
          )
          this.nContentData.shownotes = lodash.cloneDeep(
            (res.data['02'][0].NOTES == undefined
              ? ''
              : res.data['02'][0].NOTES
            ).split('\n')
          )
        }
      })
    },
    nContentShow() {
      this.nContentData.show = true
      this.nContentData.gridData = lodash.cloneDeep(
        this.nContentData.showGridData
      )
    },
    saveContent() {
      this.loading = true
      post(firstMorningMeeting.notes, [
        {
          PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: 'N',
          NOTES: this.nContentData.notes
        }
      ])
        .then(res => {
          post(firstMorningMeeting.inspection, this.nContentData.gridData).then(
            res => {
              this.nContent()
              this.nContentData.show = false
              this.loading = false
            }
          )
        })
        .catch(err => {
          this.$message.error('备注保存失败')
        })
    },
    clearContent() {
      this.nContentData.gridData = lodash.cloneDeep(
        this.nContentData.showGridData
      )
    },
    async saveCompositionRate() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 'compositionRate',
        data: _.map(
          _.sortBy(this.compositionRate.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              FLAG: 'compositionRate',
              SORT_NUM: index + 1
            }
          }
        )
      }
      let del = null
      if (
        this.compositionRate.gridData[0].delImage &&
        this.compositionRate.gridData[0].delImage.length
      ) {
        del = await post(deleteFileByIds, {
          ids: this.compositionRate.gridData[0].delImage
        })
      }
      if (del == null || del.success) {
        if (
          this.compositionRate.gridData[0].file &&
          this.compositionRate.gridData[0].file.length
        ) {
          const formData = new FormData()
          this.compositionRate.gridData[0].file.forEach(item => {
            formData.append('files', item)
          })
          post(uploadFile, formData, false, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.success) {
              params.data[0].B_LIST = JSON.stringify([
                ...params.data[0].B_LIST,
                ...res.data
              ])

              post(firstMorningMeeting.furnaceCondition, params).then(res => {
                this.getCompositionRate()
                this.$message.success('保存成功')
                this.loading = false
              })
            } else {
              this.$message.warning('图片上传失败！')
              this.loading = false
            }
          })
        } else {
          params.data[0].B_LIST = JSON.stringify(params.data[0].B_LIST)
          post(firstMorningMeeting.furnaceCondition, params).then(res => {
            this.getCompositionRate()
            this.$message.success('保存成功')
            this.loading = false
          })
        }
      } else {
        this.$message.error('图片删除失败！！！')
      }
    },
    getCompositionRate() {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'compositionRate'
      }).then(res => {
        this.compositionRate.gridData = _.cloneDeep(
          lodash.map(res.data.length ? res.data : [{}], item => {
            return {
              ...item,
              B_LIST: item.B_LIST ? JSON.parse(item.B_LIST) : []
            }
          })
        )
      })
    },
    httpRequest(params) {},
    handleChange(file, row, index) {
      if (this[row].gridData[index].file == undefined) {
        this[row].gridData[index].file = [file.raw]
      } else {
        this[row].gridData[index].file.push(file.raw)
      }
      if (this[row].gridData[index].showPic == undefined) {
        this[row].gridData[index].showPic = [file.url]
      } else {
        this[row].gridData[index].showPic.push(file.url)
      }
      this[row] = _.cloneDeep(this[row])
    },
    handlePasteImgDelete(file, index, row) {
      this[row].gridData[0].file.splice(index, 1)
      this[row].gridData[0].showPic.splice(index, 1)
      this[row] = _.cloneDeep(this[row])
    },
    handlePasteImgDeleteID(file, index, row) {
      if (this[row].gridData[0].delImage == undefined) {
        this[row].gridData[0].delImage = [file.id]
      } else {
        this[row].gridData[0].delImage.push(file.id)
      }
      this[row].gridData[0].B_LIST.splice(index, 1)
      this[row] = _.cloneDeep(this[row])
    },
    getProcessUpsets(data) {
      post(firstMorningMeeting.processUpsetsInit, data).then(res => {
        let resData = res.data
        this.processUpsets.gridData = _.cloneDeep(resData)
        this.processUpsets.showGridData = _.cloneDeep(resData)
      })
    },
    showProcessUpsets() {
      this.processUpsets.gridData = lodash.cloneDeep(
        this.processUpsets.showGridData
      )
      this.processUpsets.dialogVisible = true
    },
    exportProcessUpsets() {
      const data = [
        {
          A_LIST: '时间',
          B_LIST: '炉号',
          C_LIST: '钢种',
          D_LIST: '异常描述',
          E_LIST: '责任单位',
          F_LIST: '原因'
        }
      ].concat(
        _.map(_.cloneDeep(this.processUpsets.gridData), item => {
          let datas = {}
          _.forEach(
            [
              'OCCR_DATE',
              'HEAT_NO',
              'STLGRD',
              'ANOMALY',
              'RES_UNIT1',
              'RES_REASON'
            ],
            items => {
              datas[items] = item[items]
            }
          )
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `工艺异常（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    saveProcessUpsets() {
      this.loading = true
      const params = _.map(this.processUpsets.gridData, (item, index) => {
        return {
          ...item,
          SORT_NUM: index + 1
        }
      })
      post(
        firstMorningMeeting.processUpsets(
          this.$moment(this.cDate).format('yyyyMMDD')
        ),
        params
      ).then(res => {
        this.processUpsets.dialogVisible = false
        this.getProcessUpsets({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: 'processUpsets'
        })
        this.loading = false
      })
    },
    importProcessUpsetsData(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: 'processUpsets'
      }).then(res => {
        this.processUpsets.gridData = _.cloneDeep(res.data)
        this.$message.success('导入成功！')
      })
    },
    handlePreviewProcessUpsets(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          OCCR_DATE: 'B',
          HEAT_NO: 'C',
          STLGRD: 'D',
          ANOMALY: 'E',
          RES_UNIT1: 'F',
          RES_REASON: 'G'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.processUpsets.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    //转%
    fiex2(row, name) {
      return row[name] != undefined && row[name] != null
        ? (row[name] * 1).toFixed(2)
        : row[name]
    },
    // 铁水曲线
    getSteal() {
      post(castingMachine, {
        startTime: this.$moment(this.cDate)
          .subtract(10, 'day')
          .format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD')
      }).then(res => {
        this.steelYesterday.lineBar = [
          {
            name: 'P',
            data: res.data.map(item => item.P.toFixed(3))
          },
          {
            name: 'SI',
            data: res.data.map(item => item.SI.toFixed(3))
          }
        ]
        this.steelYesterday.lineBar2 = [
          {
            name: 'S',
            data: res.data.map(item => item.S.toFixed(3))
          },
          {
            name: 'As',
            data: res.data.map(item => item.ARSENIC.toFixed(3))
          }
        ]
        this.steelYesterday.lineBar3 = {}
        res.data.forEach(item => {
          this.steelYesterday.lineBar3[item.HMPS_ARR_DATE.substring(5, 10)] = {}
          this.steelYesterday.lineBar3[
            item.HMPS_ARR_DATE.substring(5, 10)
          ].P = {
            avg: item.P.toFixed(3),
            max: item.MAXP.toFixed(3),
            min: item.MINP.toFixed(3)
          }
          this.steelYesterday.lineBar3[
            item.HMPS_ARR_DATE.substring(5, 10)
          ].SI = {
            avg: item.SI.toFixed(3),
            max: item.MAXSI.toFixed(3),
            min: item.MINSI.toFixed(3)
          }
          this.steelYesterday.lineBar3[
            item.HMPS_ARR_DATE.substring(5, 10)
          ].S = {
            avg: item.S.toFixed(3),
            max: item.MAXS.toFixed(3),
            min: item.MINS.toFixed(3)
          }
          this.steelYesterday.lineBar3[
            item.HMPS_ARR_DATE.substring(5, 10)
          ].As = {
            avg: item.ARSENIC.toFixed(3),
            max: item.MAXARSENIC.toFixed(3),
            min: item.MINARSENIC.toFixed(3)
          }
        })
        console.log(this.steelYesterday.lineBar3)
        this.steelYesterday.lineBarX = res.data.map(item =>
          item.HMPS_ARR_DATE.substring(5, 10)
        )
      })
      return
    },
    // 时间转换
    formatDate(date) {
      console.log(date)
      if (date == null && date == '' && date == undefined && date.length > 8)
        return date
      return this.$moment(date.toString().substring(0, 8)).format('YYYY-MM-DD')
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  overflow: auto;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}
/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
    position: relative;
    .operate-box {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}
</style>
