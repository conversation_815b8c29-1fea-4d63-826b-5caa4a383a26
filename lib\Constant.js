export const ENUM = {
  roleStatus: [
    {
      value: 1,
      label: '启用',
      type: 'success'
    },
    {
      value: 0,
      label: '禁用',
      type: 'warning'
    }
  ],
  // 板材所有顶级组织机构 (用于机构数过滤)
  // X32000000,中厚板卷厂,X
  // X38000000,宽厚板厂,X
  // X50000000,板材事业部,X
  // X66000000,中板厂,X
  // X84000000,金石材料厂,X
  orgTop: ['X32000000', 'X38000000', 'X50000000', 'X66000000', 'X84000000'],
  kpiFunction: [
    {
      value: '0',
      label: '成本',
      text: '成本',
      icon: require('../assets/kpi_icon/currency-rmb-circle.svg')
    },
    {
      value: '1',
      label: '生产',
      text: '生产',
      icon: require('../assets/kpi_icon/data-line.svg')
    },
    {
      value: '2',
      label: '效益',
      text: '效益',
      icon: require('../assets/kpi_icon/pie-chart.svg')
    },
    {
      value: '3',
      label: '设备',
      text: '设备',
      icon: require('../assets/kpi_icon/pie-chart.svg')
    },
    {
      value: '4',
      label: '研发',
      text: '研发',
      icon: require('../assets/kpi_icon/cpu.svg')
    },
    {
      value: '5',
      label: '质量',
      text: '质量',
      icon: require('../assets/kpi_icon/box.svg')
    },
    {
      value: '6',
      label: '营销',
      text: '营销',
      icon: require('../assets/kpi_icon/document.svg')
    },
    {
      value: '7',
      label: '安环',
      text: '安环',
      icon: require('../assets/kpi_icon/umbrella.svg')
    },
    {
      value: '8',
      label: '能源',
      text: '能源',
      icon: require('../assets/kpi_icon/odometer.svg')
    }
  ],
  factoryList: [
    {
      value: 0,
      label: '事业部'
    },
    {
      value: 4,
      label: '第一炼钢厂'
    },
    {
      value: 1,
      label: '板卷厂'
    },
    {
      value: 2,
      label: '宽厚板厂'
    },
    {
      value: 3,
      label: '中板厂'
    },
    {
      value: 5,
      label: '金石厂'
    },
    {
      value: 6,
      label: '金润厂'
    }
  ],
  factoryListForecast: [
    {
      value: 4,
      label: '第一炼钢厂'
    },
    {
      value: 1,
      label: '板卷厂'
    },
    {
      value: 2,
      label: '宽厚板厂'
    },
    {
      value: 3,
      label: '中板厂'
    }
  ],
  levelList: [
    {
      value: 1,
      label: '一级'
    },
    {
      value: 2,
      label: '二级'
    },
    {
      value: 3,
      label: '三级'
    },
    {
      value: 4,
      label: '四级'
    },
    {
      value: 5,
      label: '五级'
    },
    {
      value: 6,
      label: '六级'
    }
  ],
  // 指标等级
  gradeList: [
    {
      value: 1,
      label: '事业部'
    },
    {
      value: 2,
      label: '各厂区 '
    },
    {
      value: 3,
      label: '班组'
    },
    {
      value: 4,
      label: '其他'
    }
  ],
  // 班组
  teamList: [
    {
      value: '甲',
      label: '甲'
    },
    {
      value: '乙',
      label: '乙'
    },
    {
      value: '丙',
      label: '丙'
    },
    {
      value: '丁',
      label: '丁'
    }
  ],
  ruleType: [
    {
      value: 0,
      label: '预警规则'
    },
    {
      value: 1,
      label: '不预警规则'
    },
    {
      value: 2,
      label: '趋势预警规则'
    },
    {
      value: 3,
      label: '趋势预警不预警规则'
    }
  ],
  earlyWarningLogic: [
    {
      value: 0,
      label: '等于'
    },
    {
      value: 1,
      label: '大于'
    },
    {
      value: 2,
      label: '小于'
    },
    {
      value: 3,
      label: '大于等于'
    },
    {
      value: 4,
      label: '小于等于'
    },
    {
      value: 5,
      label: '不等于'
    },
    {
      value: 6,
      label: '数据项'
    }
  ],
  earlyWarningRule: [
    {
      value: 0,
      label: '比较目标值'
    },
    {
      value: 1,
      label: '月时序对比'
    },
    {
      value: 2,
      label: '固定日期对比'
    },
    {
      value: 3,
      label: '次级指标个数'
    },
    {
      value: 4,
      label: '年时序对比'
    }
  ],
  targetGetType: [
    {
      value: 0,
      label: '关联其他KPI'
    },
    {
      value: 1,
      label: '关联月时序值'
    },
    {
      value: 2,
      label: '每月固定值'
    },
    {
      value: 3,
      label: '每次手动输入'
    },
    {
      value: 4,
      label: '关联年时序值'
    }
  ],
  categoryList: [
    {
      value: 1,
      label: '仅炼钢',
      type: 'warning'
    },
    {
      value: 2,
      label: '仅轧钢',
      type: 'warning'
    },
    {
      value: 3,
      label: '通用',
      type: 'warning'
    }
  ],
  matchType: [
    { value: 0, label: '未匹配', tag: 'danger' },
    { value: 1, label: '完全匹配', tag: 'success' },
    { value: 2, label: '宽度匹配', tag: 'primary' },
    { value: 3, label: '厚度匹配', tag: 'warning' }
  ],
  // 8要素匹配
  matchType8: [
    {
      value: 0,
      label: '未匹配'
    },
    {
      value: 1,
      label: '完全匹配'
    },
    {
      value: 2,
      label: '去加热模式'
    },
    {
      value: 3,
      label: '去轧制模式'
    },
    {
      value: 4,
      label: '宽度适配(含坯料钢种)'
    },
    {
      value: 5,
      label: '宽度适配(不含坯料钢种)'
    },
    {
      value: 11,
      label: '厚度初次适配低于2(含坯料钢种)'
    },
    {
      value: 12,
      label: '厚度初次适配超过2(不含坯料钢种)'
    },
    {
      value: 13,
      label: '坯料厚度适配'
    }
  ],
  // 性能要求列表
  matrFlList: [
    { value: 'Y', label: '是', tag: 'success' },
    { value: 'N', label: '否', tag: 'danger' }
  ],
  // 可信度
  moReliableList: [
    { value: true, label: '是', tag: 'success' },
    { value: false, label: '否', tag: 'danger' }
  ],
  // 机时产量协作方式
  cooperation: [
    {
      label: '近1年最好值',
      // value: 'BEST_IN_MAT',
      value: 0,
      dict: 'bestInMat'
    }, // 0
    // { label: '近1年最差值', value: 'WORST_IN_MAT', type: 1 }, // 1
    {
      label: '近1年平均值',
      // value: 'AVG_IN_MAT',
      value: 2,
      dict: 'avgInMat'
    }, // 2
    { label: '近1年中位数', value: 3, dict: 'MID_IN_MAT' }, // 3
    // {
    //   label: '近3月平均值',
    //   // value: 'AVG_IN_LAST_3M',
    //   value: 4,
    //   dict: 'avgInLast3m'
    // }, // 4
    {
      label: '滚动3月最佳值',
      // value: 'BEST_IN_ROLLING_3M',
      value: 5,
      dict: 'bestInRolling3m'
    } // 5
    // { label: '本月均值', value: 'AVG_IN_THIS_MONTH', type: 6 }, // 6
    // { label: '上月均值', value: 'AVG_IN_LAST_MONTH', type: 7 } // 5
  ]
}
