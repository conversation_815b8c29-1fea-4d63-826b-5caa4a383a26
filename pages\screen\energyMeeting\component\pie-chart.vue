<template>
  <div 
    :id="containerId" 
    style="height: 100%"/>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return [
          '#19be6b',
          '#ff2855',
          '#FF7D00',
          '#0C75FF',
          '#91cc75',
          '#fac858',
          '#ee6666',
          '#73c0de',
          '#3ba272',
          '#fc8452',
          '#9a60b4',
          '#ea7ccc'
        ]
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    vertical: {
      type: Boolean,
      default: true
    },
    unit: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    titleNum: {
      type: Number,
      default: 0
    },
    labelWidth: {
      type: Number,
      default: 80
    },
    total: {
      type: Number,
      default: 0
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
        window.addEventListener('resize', this.resizeChart)
      }
      const options = {
        title: {
          show: !!this.title,
          text: '{tit|' + this.title + '}',
          subtext: '{stit|' + this.titleNum + '}',
          left: this.vertical ? '50%' : '28%',
          top: this.vertical ? '25%' : '40%',
          textAlign: 'center',
          padding: 0,
          textStyle: {
            fontSize: 23,
            rich: {
              tit: {
                borderColor: 'transparent',
                borderWidth: 12,
                borderRadius: 4,
                color: '#fff',
                fontSize: 12
              }
            }
          },
          subtextStyle: {
            rich: {
              stit: {
                borderColor: 'transparent',
                fontSize: 14,
                lineHeight: 0,
                fontWeight: 600,
                color: '#18bd6a'
              }
            }
          }
        },
        tooltip: {
          show: true,
          trigger: 'item',
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          left: this.vertical ? '20%' : '54%',
          y: this.vertical ? 'bottom' : 'center',
          x: this.vertical ? 'center' : 'auto',
          itemGap: 18,
          itemHeight: 12,
          itemWidth: 12,
          icon: 'roundRect',
          data: this.chartData.map((item, index) => {
            return {
              name: item.name,
              textStyle: {
                color: this.color[index]
              }
            }
          }),
          formatter: name => {
            let arr =
              '{name|' +
              name +
              '}{value|' +
              this.getNum(name).value +
              (this.unit ? this.unit : '') +
              '}'
            return arr
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                align: 'left',
                lineHeight: 36,
                fontWeight: 500,
                width: this.labelWidth,
                color: '#f1f1f1'
              },
              value: {
                fontSize: 14,
                align: 'left',
                fontWeight: 500
              }
            }
          }
        },
        color: this.color,
        series: [
          {
            type: 'pie',
            selectedMode: 'single',
            radius: ['60%', '80%'],
            center: this.vertical ? ['50%', '35%'] : ['28%', '50%'],
            label: {
              normal: {
                show: false,
                formatter: '{b}：{d}%',
                color: '#f2f2f2'
              }
            },
            data: this.chartData
          }
        ]
      }
      this.myChart.setOption(options)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    getNum(name) {
      const match = this.chartData.find(item => item.name === name)
      return match ? match : {}
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style scoped>
</style>
