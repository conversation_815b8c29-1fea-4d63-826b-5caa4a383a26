const path = 'ddm/'
const hfm = 'hfm/'
const mesAPI = 'mesAPI/'
const fsm = 'fsm/'
const dsm = 'dsm/'
//设备隐患   隐患管理数据
export const dangerStatisticsFactory =
  path + 'equipDangerController/dangerStatisticsFactory.ddm'
//设备隐患   隐患数量统计---table
export const dangerStatisticsFactoryDetail =
  path + 'equipDangerController/dangerStatisticsFactoryDetail.ddm'
//设备隐患   隐患数量统计---table --默认
export const findAllWithHandle =
  path + 'equipDangerController/findAllWithHandle.ddm'
//热处理     炉停时分析图
export const findFaultTime = path + 'heatTreatmentController/findFaultTime.ddm'
//热处理     烧嘴故障率
export const getBurnerFailureRate =
  hfm + 'BurnerMonitor/getBurnerFailureRate.hfm'
//热处理     点检漏检率
export const spotDetectMissRate =
  path + 'equipStandingBootController/spotDetectMissRate.ddm'
//延误     停时统计
export const findDelayNumAndHourToBC = mesAPI + 'delay/findDelayNumAndHourToBC'
//延误     停时详情
export const findDelayDataToBCC = mesAPI + 'delay/findDelayDataToBC'
//备件     库存
export const FindSparePartsInventory = mesAPI + 'erpApi/FindSparePartsInventory'
export const bigPurchaseSys = mesAPI + 'erpApi/bigPurchaseSys'
//备件     费用明细
export const expenseDetail = path + 'iempInterfaceController/expenseDetail.ddm'
//首页     费用
//检修计划
export const checkScheduleFindAll = path + '/checkScheduleContoller/findAll.ddm'
export const maintenanceCost =
  path + 'expenseStatisController/monthlyMaintenanceCostReport.ddm'
//首页     停时分析
export const getAllFailureStatisticData =
  path + 'equipFailureController/getAllFailureStatisticData.ddm'
//首页     隐患
export const troublePotentRate =
  path + 'equipDangerController/troublePotentRate.ddm'
//首页     隐患未处理数
export const unaddressDangerStatistics =
  path + 'equipDangerController/unaddressDangerStatistics.ddm'
//备件库
export const spareParts = path + 'expenseStatisController/spareParts.ddm'
export const queryNotDealDangerInfo =
  path + '/equipDangerController/unprocessedDangerStatistics.ddm'
//首页     数量统计
export const dangerQuantityStatisticsFactory =
  path + 'equipDangerController/dangerQuantityStatisticsFactory.ddm'
//首页    检修排程
export const overhaulMonth = mesAPI + 'erpApi/overhaulMonth'
//设备管理     隐患
export const getAlarmLevelCount =
  fsm + 'checkPointController/getAlarmLevelCount.fsm'
export const dangerStopReport =
  path + 'equipDangerController/dangerStopReport.ddm'
export const getFactoryFailureByMonth =
  path + 'equipFailureController/getFactoryFailureByMonth.ddm'
//设备管理     报警数量
export const getFactoryMainInfo =
  dsm + 'commonController/getFactoryMainInfoNew.dsm'
//设备管理     报警明细
export const alarmManagement = dsm + 'alarmAboutController/alarmManagement.dsm'
//设备管理     报警明细1
export const alarmConfig = fsm + 'alarmConfigController/alarmConfig.do'
//首页     检修计划
export const MaintenanceSchedule = mesAPI + 'erpApi/MaintenanceSchedule'

// 获取故障明细
export const unaddressDangerStatisticsDetail =
  path + 'equipFailureController/getAllFailureDetail.ddm'
// 获取隐患未处理明细
export const getAllFailureDetail =
  path + 'equipDangerController/unaddressDangerStatisticsDetail.ddm'

export const abnormalSpotCheck = mesAPI + 'mesApi/AbnormalSpotCheck'
