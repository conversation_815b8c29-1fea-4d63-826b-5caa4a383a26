import { Notification } from 'element-ui'

const notifications = []
const MAX_NOTIFICATIONS = 5
const OFFSET_STEP = 120

export function showNotification({
  title,
  message,
  type = 'warning',
  duration = 3000
}) {
  let offset = OFFSET_STEP * notifications.length
  if (notifications.length >= MAX_NOTIFICATIONS) {
    const oldestNotification = notifications[0]
    oldestNotification.afterClose = () => {
      const notification = Notification({
        title,
        message,
        type,
        duration,
        offset,
        onClose: () => {
          const index = notifications.indexOf(notification)
          if (index !== -1) {
            notifications.splice(index, 1)
          }
          updateOffsets()
        }
      })
      notifications.push(notification)
      updateOffsets()
    }
  } else {
    const notification = Notification({
      title,
      message,
      type,
      duration,
      offset,
      onClose: () => {
        const index = notifications.indexOf(notification)
        if (index !== -1) {
          notifications.splice(index, 1)
        }
        updateOffsets()
      }
    })
    notifications.push(notification)
  }
}
function updateOffsets() {
  notifications.forEach((notification, index) => {
    notification.$el.style.top = `${index * OFFSET_STEP}px`
  })
}
