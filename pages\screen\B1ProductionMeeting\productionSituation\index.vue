<template>
  <div class="content">
    <div class="content-item top">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <custom-table
            :title="'转炉生产情况'"
            :setting="tableObj1.setting"
            :merge-set="tableObj1.mergeObj"
            :url-list="tableObj1.url.list"
            :url-save="tableObj1.url.save"
            :select-date="selectDate"
            :table-class="'big-table'"/>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <custom-table
            :title="'精炼生产情况'"
            :setting="tableObj4.setting"
            :url-list="tableObj4.url.list"
            :url-save="tableObj4.url.save"
            :select-date="selectDate"
            :table-class="'big-table'"/>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold" />
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <custom-table
            :title="'浇余情况'"
            :setting="tableObj2.setting"
            :url-list="tableObj2.url.list"
            :url-save="tableObj2.url.save"
            :select-date="selectDate"
            :table-class="'big-table'"/>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <custom-table
            :title="'原料生产情况'"
            :setting="tableObj5.setting"
            :url-list="tableObj5.url.list"
            :url-save="tableObj5.url.save"
            :select-date="selectDate"
            :table-class="'big-table'"/>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/B1ProductionMeeting/component/custom-table'
import {
  conProductionSituationFind,
  conProductionSituationSave,
  pouringSurplusFind,
  pouringSurplusSave,
  productionMaterialFind,
  productionMaterialSave,
  refineProductionSituationFind,
  refineProductionSituationSave
} from '@/api/screenB1Production'
export default {
  name: 'ProductionSituation',
  components: { CustomTable, SingleBarsChart },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      tableObj1: {
        url: {
          save: conProductionSituationSave,
          list: conProductionSituationFind
        },
        mergeObj: {},
        setting: [
          {
            keyQuery: 'furnacenumber',
            keySave: 'furnaceNumber',
            label: '炉座号'
          },
          {
            keyQuery: 'realityfurnacenum',
            keySave: 'realityFurnaceNum',
            label: '全天实际炉数'
          },
          {
            keyQuery: 'smeltcycle',
            keySave: 'smeltCycle',
            label: '纯冶炼周期'
          },
          {
            keyQuery: 'fettling',
            keySave: 'fettling',
            label: '补炉'
          },
          {
            keyQuery: 'changeskateboard',
            keySave: 'ChangeSkateboard',
            label: '换滑板'
          },
          {
            keyQuery: 'steelscrap',
            keySave: 'steelScrap',
            label: '等废钢'
          },
          {
            keyQuery: 'molteniron',
            keySave: 'moltenIron',
            label: '铁水'
          },
          {
            keyQuery: 'slagpot',
            keySave: 'slagPot',
            label: '等渣盆'
          },
          {
            keyQuery: 'steelladle',
            keySave: 'steelLadle',
            label: '钢包'
          }
        ]
      },
      tableObj2: {
        url: {
          save: pouringSurplusSave,
          list: pouringSurplusFind
        },
        setting: [
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次'
          },
          {
            keyQuery: 'planfurnacenum',
            keySave: 'planFurnaceNum',
            label: '需浇余炉数'
          },
          {
            keyQuery: 'proportion',
            keySave: 'proportion',
            label: '实际浇余炉数'
          },
          {
            keyQuery: 'correct',
            keySave: 'correct',
            label: '2#机钢包矫正炉数(实际矫正/应矫正)'
          },
          {
            keyQuery: 'uncorrectedreasons',
            keySave: 'unCorrectedReasons',
            label: '未矫正原因'
          },
          {
            keyQuery: 'unpouredreasons',
            keySave: 'unPouredReasons',
            label: '未浇余原因'
          }
        ]
      },
      tableObj4: {
        url: {
          save: refineProductionSituationSave,
          list: refineProductionSituationFind
        },
        setting: [
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次'
          },
          {
            keyQuery: 'groups',
            keySave: 'groups',
            label: '班组'
          },
          {
            keyQuery: 'ladlesnum',
            keySave: 'ladlesNum',
            label: '在用钢包数'
          },
          {
            keyQuery: 'timeslot',
            keySave: 'timeSlot',
            label: '减钢包时间段'
          },
          {
            keyQuery: 'startstopsituation',
            keySave: 'startStopSituation',
            label: '精炼和RH开停情况'
          }
        ]
      },
      tableObj5: {
        url: {
          save: productionMaterialSave,
          list: productionMaterialFind
        },
        setting: [
          {
            keyQuery: 'project',
            keySave: 'project',
            label: '项目'
          },
          {
            keyQuery: 'productionday',
            keySave: 'productionDay',
            label: '当日产量'
          },
          {
            keyQuery: 'productionmonth',
            keySave: 'productionMonth',
            label: '全月产量'
          },
          {
            keyQuery: 'proportion',
            keySave: 'proportion',
            label: '当日鱼雷罐加入比例'
          },
          {
            keyQuery: 'notes',
            keySave: 'notes',
            label: '备注'
          }
        ]
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  methods: {}
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
