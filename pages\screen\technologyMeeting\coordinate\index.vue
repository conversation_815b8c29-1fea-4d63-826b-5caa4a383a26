<template>
  <div class="content">
    <div class="content-item">
      
      <custom-table2
        :title="'协调事项表'"
        :setting="tableObj2.setting"
        :url-list="tableObj2.url.list"
        :url-save="tableObj2.url.save"
        :select-date="selectDate"
        :table-class="'big-table'"/>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      
      <custom-table
        :title="'重点订单'"
        :setting="tableObj1.setting"
        :url-list="tableObj1.url.list"
        :url-save="tableObj1.url.save"
        :select-date="selectDate"
        :table-class="'big-table'"/>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import * as _ from 'lodash'
import {
  coordinationDelete,
  coordinationFindAll,
  coordinationSaveAll,
  findCountDeptBySetDate,
  findCountPltBySetDate,
  findDpPltBySetDate,
  findKeyOrderByDate,
  saveKeyOrders,
  findCoordinationItemByDate,
  CoordinationItemSave,
  CoordinationItemdeleteById
} from '@/api/screenTechnolagy'
import { findOneUserByUserNo } from '@/api/system'
import moment from 'moment'
import CustomTable from '@/pages/screen/technologyMeeting/component/custom-table'
import CustomTable2 from '@/pages/screen/technologyMeeting/component/custom-table2'

export default {
  name: 'Coordinate',
  components: { CustomTable, ScreenBorder, CustomTable2 },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      editIndex: null,
      tableObj1: {
        url: {
          save: saveKeyOrders,
          list: findKeyOrderByDate
        },
        setting: [
          {
            keyQuery: 'plt',
            keySave: 'plt',
            label: '产线',
            width: 80
          },
          {
            keyQuery: 'ordStlgrd',
            keySave: 'ordStlgrd',
            label: '牌号'
          },
          {
            keyQuery: 'ordNo',
            keySave: 'ordNo',
            label: '订单号'
          },
          {
            label: '原钢种合格率',
            children: [
              {
                keyQuery: 'qualified',
                keySave: 'qualified',
                label: '合格量'
              },
              {
                keyQuery: 'passRate',
                keySave: 'passRate',
                label: '指标（%）'
              }
            ]
          },
          {
            label: '原钢种一次合格率',
            children: [
              {
                keyQuery: 'oriQualified',
                keySave: 'oriQualified',
                label: '一次合格量'
              },
              {
                keyQuery: 'oriPassRate',
                keySave: 'oriPassRate',
                label: '指标（%）'
              }
            ]
          },
          {
            label: '性能一次合格率',
            children: [
              {
                keyQuery: 'unPerfor',
                keySave: 'unPerfor',
                label: '不合格量'
              },
              {
                keyQuery: 'unQualified',
                keySave: 'unQualified',
                label: '合格量'
              },
              {
                keyQuery: 'unPassRate',
                keySave: 'unPassRate',
                label: '合格率（%）'
              }
            ]
          },
          {
            label: '订单完成率',
            children: [
              {
                keyQuery: 'ordQualified',
                keySave: 'ordQualified',
                label: '综判合计量'
              },
              {
                keyQuery: 'ordTotal',
                keySave: 'ordTotal',
                label: '订单量'
              },
              {
                keyQuery: 'ordPassRate',
                keySave: 'ordPassRate',
                label: '指标（%）'
              }
            ]
          },
          {
            keyQuery: 'content',
            keySave: 'content',
            label: '协调事项',
            inputType: 'textarea'
          }
        ]
      },
      tableObj2: {
        url: {
          save: CoordinationItemSave,
          list: findCoordinationItemByDate
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'submit_date',
            keySave: 'submit_date',
            label: '提出时间',
            inputType: 'date'
          },
          {
            keyQuery: 'matters_info',
            keySave: 'matters_info',
            label: '协调事项内容',
            shouType: true
          },
          {
            keyQuery: 'objective_result',
            keySave: 'objective_result',
            label: '意愿结果',
            shouType: true
          },
          {
            keyQuery: 'handle_unit',
            keySave: 'handle_unit',
            label: '主要协作部门(按主次)'
          },
          {
            keyQuery: 'achieve_date',
            keySave: 'achieve_date',
            label: '实现时间'
            // inputType: 'date'
          },
          {
            keyQuery: 'need_depar',
            keySave: 'need_depar',
            label: '需求科室'
          }
        ]
      },
      pilotPlan1: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      pivotTable: {
        table1: [],
        table2: [],
        table3: [],
        dialogVisible: false,
        maxHeight: null
      },
      pltList: ['第一炼钢厂', '中厚板卷厂', '中板厂', '宽厚板厂'],
      departmentList: [
        '工艺研究室',
        '调质钢研发室',
        '结构船板研发室',
        '低温容器研发室',
        '能源用钢研发室'
      ],
      varietyList: ['工艺抽查', '工装备件'],
      conclusionList: ['符合', '不符合'],
      levelList: [
        '/',
        '一般1级',
        '一般2级',
        '一般3级',
        '重要1级',
        '重要2级',
        '重要3级'
      ]
    }
  },
  computed: {
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'month')
        .format('yyyy-MM')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getpilotPlan(1)
      this.getpilotPlan(2)
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          index: 'A',
          orderNo: 'B',
          stlgrd: 'C',
          fps: 'D',
          plt: 'E',
          billetSpecifications: 'F',
          orderQuantity: 'G',
          deliveryStatus: 'H',
          coordinationMatters: 'I'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        const datas = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
        this.savepilotPlan(datas, 1)
      })
    },
    exportpilotPlan() {
      const data = [
        {
          index: '序号',
          orderNo: '订单号',
          stlgrd: '钢种',
          fps: '成品规格',
          plt: '生产线',
          billetSpecifications: '坯料(厚*宽)mm',
          orderQuantity: '订单量',
          deliveryStatus: '交货状态',
          coordinationMatters: '协调事项'
        }
      ].concat(
        _.cloneDeep(this.pilotPlan1.gridData).map((item, index) => {
          delete item.id
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `重点订单（${this.cDate}）.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },
    // 获取数据
    getpilotPlan(type) {
      post(coordinationFindAll, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.$emit('statusChange', !res.data.length)
        let data = res.data.map((item, index) => {
          return {
            index: index + 1,
            orderNo: item.orderno,
            stlgrd: item.stlgrd,
            fps: item.fps,
            plt: item.plt,
            billetSpecifications: item.billetspecifications,
            orderQuantity: item.orderquantity,
            deliveryStatus: item.deliverystatus,
            coordinationMatters: item.coordinationmatters,
            id: item.id
          }
        })
        this['pilotPlan1'].gridData = lodash.cloneDeep(data)
        this['pilotPlan1'].showGridData = data
        this.formatSpanData(this['pilotPlan1'].showGridData)
      })
    },
    savepilotPlan(items, type) {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: items.map(item => {
          Object.assign(item, { type, setDate: this.cDate })
          return item
        })
      }
      post(coordinationSaveAll, params).then(res => {
        //
        this.loading = false
        if (res !== -1) {
          this.$message.success('保存成功！')
          this.getpilotPlan(type)
          this.editIndex = null
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    editItem(data, index, type) {
      this.editIndex = index
    },
    deleteItem(data, index) {
      post(coordinationDelete, {
        id: data.id
      }).then(res => {
        this.$message.success('删除成功！')
        this.getpilotPlan()
      })
    },
    getPivot() {
      post(findCountPltBySetDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.pivotTable.table1 = res
      })
      post(findCountDeptBySetDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.pivotTable.table2 = res
      })
      post(findDpPltBySetDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.pivotTable.table3 = res
      })
    },
    calculate() {
      // this.pilotPlan1.maxHeight = this.$refs.table1.offsetHeight
    },
    totalClass(row) {
      if (row.row.serialNumber && row.row.serialNumber.trim() === '合计') {
        return 'table-total'
      }
      return ''
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总计'
          return
        }
        console.log(column)
        if (![1, 2, 3].includes(index)) return (sums[index] = '')
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          sums[index] = ''
        }
      })
      return sums
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.edit-btn {
  margin: 0 3px;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.dialog-item-tit {
  font-size: 18px;
  color: #dcdcdc;
  margin-bottom: 8px;
  margin-top: 15px;
}
</style>
