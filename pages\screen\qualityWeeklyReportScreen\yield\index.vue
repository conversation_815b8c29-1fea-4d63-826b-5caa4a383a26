<template>
  <div class="container">
    <div class="header-footer">
      <el-input
        v-model="monthlyReport"
        :rows="3"
        type="textarea"
        class="chart-input"
        resize="none"
      />
      <el-button 
        :loading="submitLoading" 
        type="primary" 
        class="submit-btn"
        icon="el-icon-finished"
        @click="handleSave">提交</el-button>
    </div>
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="原因分析">
          <custom-table 
            ref="reasonAnalysisRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'reasonAnalysis'"
            :title="'原因分析'" 
            :setting="tableObj.setting" 
            :url-list="tableObj.url.list" 
            :url-save="tableObj.url.save"
            :select-date="selectDate" 
            :dialog-width="'50%'"
          />
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="班组成材率">
          <custom-table 
            ref="teamYieldRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'teamYield'"
            :title="'班组成材率'" 
            :setting="tableObj2.setting" 
            :url-list="tableObj2.url.list"
            :url-save="tableObj2.url.save" 
            :select-date="selectDate"
            :dialog-width="'50%'"
          />
        </screen-border>
      </div>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityWeeklyReportScreen/components/screen-border.vue'
import CustomTable from '@/pages/screen/qualityWeeklyReportScreen/components/custom-table.vue'
import { findAllDateRemark, saveAllRemark } from '@/api/screen'
import { post } from '@/lib/Util'

export default {
  name: 'Yield',
  components: {
    ScreenBorder,
    CustomTable
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      submitLoading: false,
      monthlyReport: '',
      tableObj: {
        setting: [
          // {
          //   keyQuery: 'index',
          //   keySave: 'index',
          //   label: '序号',
          //   type: 'index'
          // },
          {
            keyQuery: 'project',
            keySave: 'project',
            label: '项目',
            width: '300'
          },
          {
            keyQuery: 'average',
            keySave: 'average',
            label: '23年平均',
            width: '300'
          },
          {
            keyQuery: 'year',
            keySave: 'year',
            label: '24年9月',
            width: '300'
          }
        ],
        url: {
          list: '',
          save: ''
        }
      },
      tableObj2: {
        setting: [
          {
            keyQuery: 'team',
            keySave: 'team',
            label: '班组',
            width: '200'
          },
          {
            keyQuery: 'weight',
            keySave: 'weight',
            label: '坯料重量',
            width: '200'
          },
          {
            keyQuery: 'plan',
            keySave: 'plan',
            label: '计划成品重量',
            width: '200'
          },
          {
            keyQuery: 'actual',
            keySave: 'actual',
            label: '实际成品重量',
            width: '200'
          },
          {
            keyQuery: 'design',
            keySave: 'design',
            label: '设计成材率',
            width: '200'
          },
          {
            keyQuery: 'yield',
            keySave: 'yield',
            label: '实际成材率',
            width: '200'
          },
          {
            keyQuery: 'difference',
            keySave: 'difference',
            label: '实际-设计',
            width: '200'
          }
        ],
        url: {
          list: '',
          save: ''
        }
      }
    }
  },
  watch: {
    selectDate: function() {
      this.getRemarkInfo()
    }
  },
  created() {
    this.getRemarkInfo()
  },
  methods: {
    async getRemarkInfo() {
      const params = {
        setTime: this.selectDate,
        type: 3 // 1:质量通报 2:非计划统计 3.成材率 4.专利受理情况 5.专利授权情况
      }
      const res = await post(findAllDateRemark, params)
      this.monthlyReport = res.data.length ? res.data[0].remark : ''
    },
    async handleSave() {
      this.submitLoading = true
      const params = {
        setTime: this.selectDate,
        type: 3,
        data: {
          remark: this.monthlyReport
        }
      }
      const res = await post(saveAllRemark, params)
      if (res.status === 1) {
        this.$message.success('已提交')
      } else {
        this.$message.error(res.data)
      }
      this.submitLoading = false
    }
  }
}
</script>

<style scoped lang="less">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: hidden;

  .header-footer {
    margin-bottom: 10px;
    height: 80px;
    border-radius: 4px;
    border: 1px solid rgba(31, 198, 255, 0.3);
    padding: 5px 8px;
    display: flex;
    align-items: flex-end;
    gap: 20px;

    .submit-btn {
      width: 68px !important;
      height: 28px !important;
      padding: 0 !important;
      line-height: 26px !important;
      background: rgba(31, 198, 255, 0.3);
      border: 1px solid #1fc6ff;
      color: #fff;
      font-size: 14px;
      // margin: 10px 0;

      &:hover {
        background: rgba(31, 198, 255, 0.6);
      }
    }
  }

  .chart-row {
    margin-bottom: 10px;
    height: 100%;
    flex-direction: column;
  }

  .chart-row,
  .table-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 10px;
    width: 100%;
  }

  .chart-box,
  .table-box {
    width: 50%;
    flex: 1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .chart-footer {
    margin: 0px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    padding: 5px 8px;
    background: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);
    width: 100%;
  }

  .chart-input {
    height: 100%;
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      height: 100%;
      padding: 0;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
        border-color: transparent !important;
      }
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      border-color: transparent !important;
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-content {
    padding: 10px;
    box-sizing: border-box;
  }
}
</style>
