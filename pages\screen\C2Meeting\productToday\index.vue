<template>
  <div class="content">
    <div class="content-item top">
      <custom-table
        :title="'大夜班轧制生产情况'"
        :setting="tableObj2.setting"
        :url-list="tableObj2.url.list"
        :url-save="tableObj2.url.save"
        :select-date="selectDate"/>
    </div>
    <div class="content-hold" />
    <div class="content-item">
      <custom-table
        :title="'大夜班火切线生产情况'"
        :setting="tableObj1.setting"
        :url-list="tableObj1.url.list"
        :url-save="tableObj1.url.save"
        :select-date="selectDate"/>
    </div>
    <div class="content-hold" />
    <div class="content-item top">

      <screen-border title="大夜班设备运行情况">
        <el-table
          :data="deviceSetting.dataList"
          :size="'medium'"
          class="center-table font-big-table"
          border>
          <template
            v-for="(item, index) in deviceSetting.setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.children"
                :key="index"
                :width="item.width || ''"
                :property="item.keySave"
                :label="item.label"
                :align="item.align">
                <template
                  v-for="(cItem, cIndex) in item.children">
                  <template v-if="item.inputType === 'textarea'">
                    <el-table-column
                      :key="cIndex"
                      :width="cItem.width || ''"
                      :property="cItem.keySave"
                      :label="cItem.label"
                      :align="cItem.align">
                      <template v-slot="{ row }">
                        <div
                          slot="content"
                          v-html="formatText(row[cItem.keySave], cItem.split)"
                        />
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      :key="cIndex"
                      :width="cItem.width || ''"
                      :property="cItem.keySave"
                      :label="cItem.label"
                      :align="cItem.align"/>
                  </template>
                </template>
              </el-table-column>
              <template v-else>
                <el-table-column
                  v-if="item.type === 'index'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                />
                <template v-else>
                  <template v-if="item.inputType === 'textarea'">
                    <el-table-column
                      :key="index"
                      :width="item.width || ''"
                      :property="item.keySave"
                      :label="item.label"
                      :align="item.align">
                      <template v-slot="{ row }">
                        <div
                          slot="content"
                          v-html="formatText(row[item.keySave], item.split)"
                        />
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      :key="index"
                      :width="item.width || ''"
                      :property="item.keySave"
                      :label="item.label"
                      :align="item.align"/>
                  </template>
                </template>
              </template>
            </template>
          </template>
        </el-table>
      </screen-border>
    </div>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import {
  qmsQualitySupervisionQuery,
  qmsQualitySupervisionSave
} from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/C2Meeting/component/custom-table'
import {
  findNightEquipmentOperation,
  fireCuttingFind,
  fireCuttingSave,
  rollingProductionFind,
  rollingProductionSave
} from '@/api/screenC2'
import { post } from '@/lib/Util'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
export default {
  name: 'productToday',
  components: { ScreenBorder, CustomTable, SingleBarsChart },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      tableObj1: {
        url: {
          save: fireCuttingSave,
          list: fireCuttingFind
        },
        setting: [
          {
            keyQuery: 'classification',
            keySave: 'classification',
            label: '分类'
          },
          {
            keyQuery: 'plan',
            keySave: 'plan',
            label: '计划'
          },
          {
            keyQuery: 'piece',
            keySave: 'piece',
            label: '数量（块）'
          },
          {
            keyQuery: 'wgt',
            keySave: 'wgt',
            label: '重量（吨）'
          },
          {
            keyQuery: 'reason',
            keySave: 'reason',
            label: '未完成计划原因'
          }
        ]
      },
      tableObj2: {
        url: {
          save: rollingProductionSave,
          list: rollingProductionFind
        },
        setting: [
          {
            keyQuery: 'currentdate',
            keySave: 'currentDate',
            label: '日期'
          },
          {
            keyQuery: 'groups',
            keySave: 'groups',
            label: '班别'
          },
          {
            keyQuery: 'plannedproduction',
            keySave: 'plannedProduction',
            label: '计划产量（吨）'
          },
          {
            keyQuery: 'realityproduction',
            keySave: 'realityProduction',
            label: '实际产量（吨）'
          },
          {
            keyQuery: 'avgweight',
            keySave: 'avgWeight',
            label: '平均单重（吨）'
          },
          {
            keyQuery: 'reason',
            keySave: 'reason',
            label: '未完成计划原因'
          },
          {
            keyQuery: 'impacttime',
            keySave: 'impactTime',
            label: '影响时间（分钟）'
          },
          {
            keyQuery: 'impactton',
            keySave: 'impactTon',
            label: '影响吨位'
          },
          {
            keyQuery: 'unit',
            keySave: 'unit',
            label: '责任单位'
          }
        ]
      },

      deviceSetting: {
        url: {
          list: findNightEquipmentOperation
        },
        dataList: [],
        setting: [
          {
            keyQuery: 'T_DATE_FROM',
            keySave: 'T_DATE_FROM',
            label: '停机开始'
          },
          {
            keyQuery: 'T_DATE_TO',
            keySave: 'T_DATE_TO',
            label: '停机结束'
          },
          {
            keyQuery: 'TIMES',
            keySave: 'TIMES',
            label: '影响时间'
          },
          {
            keyQuery: 'FAULT_DESCRIPTION',
            keySave: 'FAULT_DESCRIPTION',
            label: '事故描述'
          },
          {
            keyQuery: 'CHG_GRD_DEP',
            keySave: 'CHG_GRD_DEP',
            label: '责任单位'
          }
        ]
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.loadData()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.loadData()
  },
  methods: {
    loadData() {
      post(this.deviceSetting.url.list, {
        startTime: this.$moment(this.cDate).format('YYYYMMDD'),
        endTime: this.$moment(this.cDate).format('YYYYMMDD')
      }).then(res => {
        this.deviceSetting.dataList = res.rows.filter(item => {
          return (
            item.T_DATE_TO <
              this.$moment(this.cDate).format('YYYY-MM-DD') + ' 08:00:00' ||
            item.T_DATE_FROM <
              this.$moment(this.cDate).format('YYYY-MM-DD') + ' 08:00:00'
          )
        })
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .top {
    min-height: 200px;
    flex: 0.5;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
