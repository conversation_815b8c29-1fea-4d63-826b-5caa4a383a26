<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <screen-border :title="'轧钢检验批次一次合格率'">
            <template v-slot:headerRight>
              <el-date-picker
                v-model="data1"
                :clearable="false"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right"
                class="screen-input-picker"
                @change="getData1()" />
              <span
                v-command="'/screen/technologyMeeting/edit'"
                class="screen-btn"
                @click="tableData1.dialogVisible = true">
                <el-icon class="el-icon-edit-outline" />
                操作
              </span>
            </template>
            <div
              ref="table1"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="tableData1.showGridData"
                :row-class-name="tableRowClassName"
                :summary-method="getSummaries"
                :max-height="tableData1.maxHeight"
                :default-sort = "{prop: 'inspectionDate', order: 'descending'}"
                show-summary
                border>
                <el-table-column
                  align="center"
                  property="inspectionDate"
                  label="检验日期"
                  sortable
                  width="80" />
                <el-table-column
                  align="center"
                  property="department"
                  label="科室" />
                <el-table-column
                  align="center"
                  property="stdspec"
                  label="标准号" />
                <el-table-column
                  align="center"
                  property="nonconformingLot"
                  label="不合格批次"
                  sortable
                  width="100" />
                <el-table-column
                  align="center"
                  property="inspectionLot"
                  label="检验批次"
                  sortable
                  width="80" />
                <el-table-column
                  align="center"
                  property="FPY"
                  label="一次合格率(%)"
                  sortable
                  width="120" />
                <el-table-column
                  align="center"
                  property="Description"
                  label="情况说明" />
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <screen-border title="热处理检验批次一次合格率">
            <template v-slot:headerRight>
              <el-date-picker
                v-model="data2"
                :clearable="false"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right"
                class="screen-input-picker"
                @change="getData2()" />
              <span
                v-command="'/screen/technologyMeeting/edit'"
                class="screen-btn"
                @click="tableData2.dialogVisible = true">
                <el-icon class="el-icon-edit-outline" />
                操作
              </span>
            </template>
            <div
              ref="table2"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="tableData2.showGridData"
                :row-class-name="tableRowClassName"
                :summary-method="getSummaries"
                :max-height="tableData2.maxHeight"
                :default-sort = "{prop: 'inspectionDate', order: 'descending'}"
                show-summary
                border>
                <el-table-column
                  align="center"
                  property="inspectionDate"
                  label="检验日期"
                  sortable
                  width="120" />
                <el-table-column
                  align="center"
                  property="department"
                  label="科室" />
                <el-table-column
                  align="center"
                  property="stdspec"
                  label="标准号" />
                <el-table-column
                  align="center"
                  property="nonconformingLot"
                  label="不合格批次"
                  sortable
                  width="100" />
                <el-table-column
                  align="center"
                  property="inspectionLot"
                  label="检验批次"
                  sortable
                  width="80" />
                <el-table-column
                  align="center"
                  property="FPY"
                  label="一次合格率(%)"
                  sortable
                  width="120" />
                <el-table-column
                  align="center"
                  property="Description"
                  label="情况说明" />
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <!--轧钢检验详情-->
    <el-dialog
      :visible.sync="tableData1.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="轧钢检验批次一次合格率详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('tableData1')">
              清空数据
            </span>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importRollRateData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreviewRoll"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline" />
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportRollRate">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveRollRate">
              <el-icon class="el-icon-document-checked" />
              保存
            </span>
          </div>
          轧钢检验批次一次合格率详情
        </div>
      </template>
      <el-form :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="tableData1.gridData"
          border>
          <el-table-column
            property="inspectionDate"
            label="检验日期">
            <template v-slot="{ row }">
              <el-input v-model="row.inspectionDate" />
            </template>
          </el-table-column>
          <el-table-column
            property="department"
            label="部门">
            <template v-slot="{ row }">
              <el-input v-model="row.department" />
            </template>
          </el-table-column>
          <el-table-column
            property="stdspec"
            label="标准号">
            <template v-slot="{ row }">
              <el-input v-model="row.stdspec" />
            </template>
          </el-table-column>
          <el-table-column
            property="nonconformingLot"
            label="不合格批次">
            <template v-slot="{ row }">
              <el-input v-model="row.nonconformingLot" />
            </template>
          </el-table-column>
          <el-table-column
            property="inspectionLot"
            label="检验批次">
            <template v-slot="{ row }">
              <el-input v-model="row.inspectionLot" />
            </template>
          </el-table-column>
          <el-table-column
            property="FPY"
            label="一次合格率(%)">
            <template v-slot="{ row }">
              <el-input v-model="row.FPY" />
            </template>
          </el-table-column>
          <el-table-column
            property="Description"
            label="情况说明">
            <template v-slot="{ row }">
              <el-input v-model="row.Description" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index, 'tableData1')">
                <el-icon class="el-icon-delete" />
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEdit"
          class="screen-btn"
          @click="addGridData('tableData1')">
          <el-icon class="el-icon-circle-plus-outline" />
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--热处理详情-->
    <el-dialog
      :visible.sync="tableData2.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="热处理检验批次一次合格率详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('tableData2')">
              清空数据
            </span>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importHeatRateData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline" />
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportHeatRate">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveHeatRate">
              <el-icon class="el-icon-document-checked" />
              保存
            </span>
          </div>
          热处理检验性能一次合格率详情
        </div>
      </template>
      <el-form :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="tableData2.gridData"
          border>
          <el-table-column
            property="inspectionDate"
            label="检验日期">
            <template v-slot="{ row }">
              <el-input v-model="row.inspectionDate" />
            </template>
          </el-table-column>
          <el-table-column
            property="department"
            label="部门">
            <template v-slot="{ row }">
              <el-input v-model="row.department" />
            </template>
          </el-table-column>
          <el-table-column
            property="stdspec"
            label="标准号">
            <template v-slot="{ row }">
              <el-input v-model="row.stdspec" />
            </template>
          </el-table-column>
          <el-table-column
            property="nonconformingLot"
            label="不合格批次">
            <template v-slot="{ row }">
              <el-input v-model="row.nonconformingLot" />
            </template>
          </el-table-column>
          <el-table-column
            property="inspectionLot"
            label="检验批次">
            <template v-slot="{ row }">
              <el-input v-model="row.inspectionLot" />
            </template>
          </el-table-column>
          <el-table-column
            property="FPY"
            label="一次合格率(%)">
            <template v-slot="{ row }">
              <el-input v-model="row.FPY" />
            </template>
          </el-table-column>
          <el-table-column
            property="Description"
            label="情况说明">
            <template v-slot="{ row }">
              <el-input v-model="row.Description" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index, 'tableData2')">
                <el-icon class="el-icon-delete" />
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEdit"
          class="screen-btn"
          @click="addGridData('tableData2')">
          <el-icon class="el-icon-circle-plus-outline" />
          增加数据
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  checklistBySetDate,
  checklistDelete,
  passRateFind,
  passRateSave,
  steelRollingInspectionFindAll,
  steelRollingInspectionSaveAll,
  steelRollingFindAll
} from '@/api/screenTechnolagy'
import moment from 'moment'
import BarsChart from '@/pages/screen/technologyMeeting/component/bars-chart'
import { math } from '@/lib/Math'
import {
  findHtFpylestPerformanceByDate,
  findShapePassByDate,
  savehtFpylestPerformance,
  saveShapePass,
  findHtFpyTestByBetweenDate
} from '@/api/screen'
import LineChart from '@/pages/decision/analysis/component/line-chart'
import CustomTable from '@/pages/screen/technologyMeeting/component/custom-table'

export default {
  name: 'PassRate',
  components: { CustomTable, LineChart, BarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      shapeUrl: {
        save: passRateSave,
        list: passRateFind
      },
      shape: [
        {
          keyQuery: 'type',
          keySave: 'type',
          label: '类型'
        },
        {
          keyQuery: 'rollingMill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'value',
          keySave: 'Value',
          label: '实际值'
        },
        {
          keyQuery: 'targetValue',
          keySave: 'targetValue',
          label: '目标值'
        },
        {
          keyQuery: 'description',
          keySave: 'description',
          label: '未完成原因'
        }
      ],
      msChart: {
        bar1: [],
        barX1: ['C1', 'C2', 'C3'],
        failReason: '',
        dateType: 0
      },
      bxChart: {
        bar1: [],
        barX1: ['C1', 'C2', 'C3'],
        failReason: '',
        dateType: 0
      },
      HeatRate: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      tableData: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      tableData1: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      tableData2: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      data1: null,
      data2: null
    }
  },
  computed: {
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'day')
        .format('yyyy-MM-DD')
    },
    nextDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(-1, 'day')
        .format('yyyy-MM-DD')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.loadData()
    }
  },
  destroyed() {},
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    const today = this.$moment()
    const dayOfWeek = today.day()
    const lastMonday = today
      .clone()
      .subtract(1, 'week')
      .startOf('week')
      .add(1, 'day')
      .format('YYYY-MM-DD')
    const monday = today
      .clone()
      .startOf('week')
      .add(1, 'day')
      .format('YYYY-MM-DD')
    const yesterday = today
      .clone()
      .subtract(1, 'day')
      .format('YYYY-MM-DD')
    if (dayOfWeek === 1) {
      this.data1 = [lastMonday, yesterday]
      this.data2 = [lastMonday, yesterday]
    } else {
      this.data1 = [monday, yesterday]
      this.data2 = [monday, yesterday]
    }
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    loadData() {
      // this.getHeatRate()
      // this.getRollRate()
      this.getData1()
      this.getData2()
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          inspectionDate: 'A',
          department: 'B',
          stdspec: 'C',
          nonconformingLot: 'D',
          inspectionLot: 'E',
          FPY: 'F',
          Description: 'G'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.tableData2.gridData = sheet
          .filter(
            item => item.inspectionDate && item.inspectionDate !== '检验日期'
          )
          .map(item => {
            item.stdspec = item.stdspec.trim()
            item.FPY = item.FPY.toString()
            if (item.FPY.includes('%')) {
              item.FPY = item.FPY.replace('%', '')
            }
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportHeatRate() {
      const data = [
        {
          inspectionDate: '检修日期',
          department: '科室',
          stdspec: '标准号',
          nonconformingLot: '不合格批次',
          inspectionLot: '检验批次',
          FPY: '一次合格率',
          Description: '情况说明'
        }
      ].concat(
        _.cloneDeep(this.tableData2.gridData).map(item => {
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `热处理检验批次一次合格率（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    getHeatRate() {
      post(findHtFpylestPerformanceByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        console.log('数据1', res.data)
        this.loading = false
        this.tableData2.showGridData = res.data.map(item => {
          return {
            inspectionDate: item.inspectiondate,
            //   item.inspectiondate.substr(0, 4) +
            //   // '-' +
            //   item.inspectiondate.substr(4, 2) +
            //   // '-' +
            //   item.inspectiondate.substr(6, 2),
            department: item.department,
            stdspec: item.stdspec,
            inspectionLot: item.inspectionlot,
            nonconformingLot: item.nonconforminglot,
            FPY: item.fpy,
            Description: item.description
          }
        })
        this.tableData2.gridData = lodash.cloneDeep(
          this.tableData2.showGridData
        )
      })
    },
    saveHeatRate() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        startDate: this.data2[0],
        endDate: this.data2[1],
        data: this.tableData2.gridData
      }
      post(savehtFpylestPerformance, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.tableData2.dialogVisible = false
          // this.getHeatRate()
          this.getData2()
        }
      })
    },
    importHeatRateData(date) {
      post(steelRollingInspectionFindAll, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.tableData2.gridData = res.data.map(item => {
          return {
            inspectionDate: item.inspectiondate,
            department: item.department,
            stdspec: item.stdspec,
            inspectionLot: item.inspectionlot,
            nonconformingLot: item.nonconforminglot,
            FPY: item.fpy,
            Description: item.description
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    handlePreviewRoll(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          inspectionDate: 'A',
          department: 'B',
          stdspec: 'C',
          nonconformingLot: 'D',
          inspectionLot: 'E',
          FPY: 'F',
          Description: 'G'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.tableData1.gridData = sheet
          .filter(
            item => item.inspectionDate && item.inspectionDate !== '检验日期'
          )
          .map(item => {
            item.stdspec = item.stdspec.trim()
            item.FPY = item.FPY.toString()
            if (item.FPY.includes('%')) {
              item.FPY = item.FPY.replace('%', '')
            }
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportRollRate() {
      const data = [
        {
          inspectionDate: '检修日期',
          department: '科室',
          stdspec: '标准号',
          nonconformingLot: '不合格批次',
          inspectionLot: '检验批次',
          FPY: '一次合格率',
          Description: '情况说明'
        }
      ].concat(
        _.cloneDeep(this.tableData1.gridData).map(item => {
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `热处理检验批次一次合格率（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    getRollRate() {
      post(steelRollingInspectionFindAll, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.tableData1.showGridData = res.data.map(item => {
          return {
            inspectionDate:
              item.inspectiondate.substr(0, 4) +
              // '-' +
              item.inspectiondate.substr(4, 2) +
              // '-' +
              item.inspectiondate.substr(6, 2),
            department: item.department,
            stdspec: item.stdspec,
            inspectionLot: item.inspectionlot,
            nonconformingLot: item.nonconforminglot,
            FPY: item.fpy,
            Description: item.description
          }
        })
        this.tableData1.gridData = lodash.cloneDeep(
          this.tableData1.showGridData
        )
      })
    },
    saveRollRate() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        startDate: this.data1[0],
        endDate: this.data1[1],
        data: this.tableData1.gridData
      }
      post(steelRollingInspectionSaveAll, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.tableData1.dialogVisible = false
          // this.getRollRate()
          this.getData1()
        }
      })
    },
    importRollRateData(date) {
      post(steelRollingInspectionFindAll, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.tableData1.gridData = res.data.map(item => {
          return {
            inspectionDate: item.inspectiondate,
            department: item.department,
            stdspec: item.stdspec,
            inspectionLot: item.inspectionlot,
            nonconformingLot: item.nonconforminglot,
            FPY: item.fpy,
            Description: item.description
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    calculateHeight() {
      this.tableData1.maxHeight = this.$refs.table1.offsetHeight
      this.tableData2.maxHeight = this.$refs.table2.offsetHeight
    },
    tableRowClassName() {
      return ''
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        if (![3, 4, 5].includes(index)) return (sums[index] = '')
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          sums[index] = ''
        }
      })
      if (sums[4]) {
        sums[5] = (100 - math.divide(sums[3], sums[4]) * 100).toFixed(2)
      }
      return sums
    },

    // 生成图表
    getShape(data) {
      let dataObj = { C1: '', C2: '', C3: '' }
      data.filter(item => item.type === '麻水').forEach(item => {
        dataObj[item.rollingMill] = item.Value
      })
      this.msChart.bar1 = [
        {
          name: '发生率',
          data: [dataObj.C1, dataObj.C2, dataObj.C3]
        }
      ]
      dataObj = { C1: '', C2: '', C3: '' }
      data.filter(item => item.type === '板形').forEach(item => {
        dataObj[item.rollingMill] = item.Value
      })
      this.bxChart.bar1 = [
        {
          name: '合格率',
          data: [dataObj.C1, dataObj.C2, dataObj.C3]
        }
      ]
    },
    getData1() {
      post(steelRollingFindAll, {
        startDate: this.data1[0],
        endDate: this.data1[1]
      }).then(res => {
        //
        this.loading = false
        this.tableData1.showGridData = res.data.map(item => {
          return {
            inspectionDate:
              item.inspectiondate.substr(0, 4) +
              // '-' +
              item.inspectiondate.substr(4, 2) +
              // '-' +
              item.inspectiondate.substr(6, 2),
            department: item.department,
            stdspec: item.stdspec,
            inspectionLot: item.inspectionlot,
            nonconformingLot: item.nonconforminglot,
            FPY: parseFloat(item.fpy),
            Description: item.description
          }
        })
        this.tableData1.gridData = lodash.cloneDeep(
          this.tableData1.showGridData
        )
      })
    },
    getData2() {
      post(findHtFpyTestByBetweenDate, {
        startDate: this.data2[0],
        endDate: this.data2[1]
      }).then(res => {
        //
        console.log('数据1', res.data)
        this.loading = false
        this.tableData2.showGridData = res.data.map(item => {
          return {
            inspectionDate: item.inspectiondate,
            //   item.inspectiondate.substr(0, 4) +
            //   // '-' +
            //   item.inspectiondate.substr(4, 2) +
            //   // '-' +
            //   item.inspectiondate.substr(6, 2),
            department: item.department,
            stdspec: item.stdspec,
            inspectionLot: item.inspectionlot,
            nonconformingLot: item.nonconforminglot,
            FPY: parseFloat(item.fpy),
            Description: item.description
          }
        })
        this.tableData2.gridData = lodash.cloneDeep(
          this.tableData2.showGridData
        )
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.edit-btn {
  margin: 0 3px;
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart-tit {
    font-size: 16px;
    font-weight: bolder;
    color: #ffffff;
    line-height: 20px;
    margin: 10px 0 5px;

    &:before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 6px;
      height: 100%;
      margin-right: 4px;
    }
  }

  .chart {
    flex: 1;
    height: 0;
  }
}

.dialog-item-tit {
  font-size: 18px;
  color: #dcdcdc;
  margin-bottom: 8px;
  margin-top: 15px;
}

.screen-input-picker {
  background: rgba(31, 198, 255, 0.2);
  color: #fff;
  border-color: rgba(31, 198, 255, 0.6);
  padding-right: 5px;

  /deep/ .el-range-input {
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
    padding-right: 5px;
  }

  /deep/ .el-range-separator {
    color: #fff;
  }
}
</style>
