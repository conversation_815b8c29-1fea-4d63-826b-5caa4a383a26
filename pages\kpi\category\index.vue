<template>
  <div class="page-content">
    <div class="page-operate">
      <div class="search-wrapper">
        <el-form
          ref="searchForm"
          :model="searchForm"
          size="mini"
          inline
          @keyup.enter.native="handleSearch(true)"
        >
          <el-form-item
            prop="name"
          >
            <el-input
              v-model="searchForm.name"
              clearable
              size="small"
              placeholder="请输入分类名称"
              style="width: 140px"
              suffix-icon="el-icon-search"
            />
          </el-form-item>
          <el-form-item
            prop="id"
          >
            <el-input
              v-model="searchForm.id"
              clearable
              size="small"
              placeholder="请输入分类ID"
              style="width: 140px"
              suffix-icon="el-icon-search"
            />
          </el-form-item>
          <el-form-item
            label="适用对象"
            prop="feature"
          >
            <el-select
              v-model="searchForm.type"
              :style="{width: '120px'}"
              size="small"
              clearable
              placeholder="选择适用对象"
            >
              <el-option
                v-for="(item, index) in typeList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="text-right">
        <el-button
          icon="el-icon-search"
          type="primary"
          size="small"
          @click="handleSearch"
        >搜索
        </el-button>
        <el-button
          v-command="'/kpi/category/save'"
          icon="el-icon-circle-plus-outline"
          size="small"
          type="success"
          @click="handleAdd"
        >新增
        </el-button>
      </div>
    </div>
    <div class="page-card shadow-light">

      <el-table
        v-loading="loading"
        :data="tableData"
        :size="size"
        border
        style="width: 100%"
      >
        <el-table-column
          label="序号"
          type="index"
          width="60"
        />
        <el-table-column
          label="ID"
          prop="id"
          width="100"
        />

        <el-table-column
          label="指标名称"
          prop="name"
          min-width="80"
          show-overflow-tooltip
        />

        <el-table-column
          v-for="(item, index) in typeList"
          :key="index"
          :label="item.label"
          prop="name"
          min-width="80"
          show-overflow-tooltip
        >
          <template
            v-slot="{row}"
          >
            <i
              v-if="row.type === item.value"
              class="el-icon-check"
              style="font-weight: bold"/>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="180"
        >
          <template
            v-slot="{row}"
          >
            <span v-command="'/kpi/category/save'">
              <el-button
                size="small"
                type="text"
                @click="handleEdit(row)"
              >编辑
              </el-button>
              <el-divider
                direction="vertical" />
            </span>
            <el-button
              v-command="'/kpi/category/delete'"
              slot="reference"
              type="text"
              @click="handleDelete(row)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-row
        align="middle"
        class="table-pagination"
        justify="end"
        type="flex"
      >
        <el-pagination
          :current-page="page.page"
          :page-size="page.size"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-row>
          
    </div>
    <Edit
      ref="modalForm"
      @success="handleSearch"/>
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import { post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'
import { findNextRank, kpiCategory, kpiCategoryDel } from '@/api/kpi'
export default {
  name: 'kpi-category',
  components: {
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: kpiCategory, //分页接口地址
        delete: kpiCategoryDel //删除接口地址
      },
      editUserId: null,
      data: [], // 树状数据
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      kpiFunction: ENUM.kpiFunction,
      factoryList: ENUM.factoryList,
      levelList: ENUM.levelList,
      statusList: [
        {
          value: 1,
          label: '正常',
          type: 'success'
        },
        {
          value: 0,
          label: '废弃',
          type: 'warning'
        }
      ],
      typeList: ENUM.categoryList,
      rightMenuVisible: false,
      rightMenuLeft: 0,
      rightMenuTop: 0,
      rightMenuData: null
    }
  },
  watch: {
    rightMenuVisible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  created() {},
  methods: {
    getValue: function(list = [], value) {
      return list.find(item => item.value == value) || {}
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}
.tree-wrapper {
  overflow: auto;
  padding: 10px;
  border: 1px solid #eee;
  background: #fff;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.first-node {
  font-size: 18px;
}
/deep/ .el-tree-node {
  margin: 5px 0;
}
/deep/ .el-tree > .el-tree-node {
  margin: 15px 0 12px;
}
.contextmenu {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  background: #fff;
  font-size: 14px;
  color: #666;
  border: 1px solid #e9e9e9;
  min-width: 250px;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
  li {
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #f4f4f5;
    }
  }
  li:last-child {
    border-top: 1px solid #e9e9e9;
  }
}
.red {
  color: red;
}
</style>
