<!-- 公告栏组件 -->
<template>
  <div 
    class="notice-bar" 
    @click="tipClick">
    <div class="notice-bar__icon">
      <img src="../../../../assets/images/icon-voice.png">
      <span>喜报：</span>
    </div>
    <div 
      ref="wrap" 
      class="notice-bar__wrap">
      <div 
        ref="content" 
        :style="contentStyle" 
        class="notice-bar__content">{{ text }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NoticeBar',
  props: {
    text: {
      type: String,
      default: ''
    },
    speed: {
      type: Number,
      default: 100
    },
    defaultWidth: {
      type: Number,
      default: 1920
    }
  },
  data() {
    return {
      contentStyle: {
        transitionDuration: '0s',
        transform: 'translateX(0px)'
      },
      wrapWidth: 0,
      contentWidth: 0,
      time: 0,
      timer: null,
      convertSpeed: 1
    }
  },
  watch: {
    text(val) {
      this.$nextTick(() => {
        this.init()
      })
    }
  },
  created() {},
  mounted() {
    if (this.text) {
      this.init()
    }
  },
  methods: {
    init() {
      this.timer && clearInterval(this.timer)
      const wrapper_width = this.$refs.wrap.offsetWidth
      const _width = window.innerWidth
      this.convertSpeed = this.speed // 根据分辨率转化成不同的速度
      this.wrapWidth = this.$refs.wrap.offsetWidth
      this.contentWidth = this.$refs.content.offsetWidth
      this.startAnimate()
      this.timer = setInterval(() => {
        this.startAnimate()
      }, this.time * 1000)
      this.$once('hook:beforeDestroy', () => {
        clearInterval(this.timer)
        this.timer = null
      })
    },
    startAnimate() {
      this.contentStyle.transitionDuration = '0s'
      this.contentStyle.transform = 'translateX(' + this.wrapWidth + 'px)'
      this.time = (this.wrapWidth + this.contentWidth) / this.convertSpeed
      setTimeout(() => {
        this.contentStyle.transitionDuration = this.time + 's'
        this.contentStyle.transform = 'translateX(-' + this.contentWidth + 'px)'
      }, 200)
    },
    tipClick() {
      this.$emit('click')
    }
  }
}
</script>
<style scoped lang='less'>
.notice-bar {
  position: relative;
  width: 100%;
  font-weight: 400;
  font-size: 24px;
  color: #ff0000;
  display: flex;
  align-items: center;
  height: 100%;
  .notice-bar__icon {
    width: 110px;
    span {
      display: inline-block;
      vertical-align: middle;
      font-size: 20px;
    }
    img {
      width: 30px;
      vertical-align: middle;
    }
  }
  .notice-bar__wrap {
    position: relative;
    display: flex;
    flex: 1;
    height: 100%;
    align-items: center;
    overflow: hidden;
    .notice-bar__content {
      position: absolute;
      white-space: nowrap;
      transition-timing-function: linear;
    }
  }
}
</style>
