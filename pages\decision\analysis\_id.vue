<template>
  <div class="full-height">
    <template v-if="!showComponent">
      <div class="categories">
        <el-radio-group
          :value="'Z'"
          @change="changeFactoryType($event)"
        >
          <el-radio-button label="Z">{{ getDict(factory, 'factoryList').label }}</el-radio-button>
        </el-radio-group>
        &emsp;<el-date-picker
          v-model="period"
          :value-format="'yyyy-MM'"
          type="month"
          placeholder="选择月"/>
      </div>
      <div class="content evaluate shadow-light">
        <div class="card-title">产量评价</div>
        <el-row
          :gutter="20"
          type="flex"
          align="middle"
        >
          <el-col :span="12">
            <div class="evaluate-detail">
              <div class="detail-left">
                <el-progress
                  :stroke-width="15"
                  :percentage="getTotalScore() <= 100 ? getTotalScore() : 100"
                  :color="'#19be6b'"
                  :format="getTotalScore"
                  :width="160"
                  type="circle"
                />
              </div>
              <div class="product-detail">
                <el-descriptions
                  :column="3"
                  :size="'medium'"
                  border
                >
                  <el-descriptions-item label="订单评价">
                    <span
                      :class="{
                        'red': getEvaluateObj('avgOrdWid').currentScore <= 100
                    }">
                      {{ getEvaluateObj('avgOrdWid').currentScore }} 分
                    </span>
                  </el-descriptions-item>
                  <el-descriptions-item label="设备作业评价">
                    <span
                      :class="{
                        'red': getEvaluateObj('totalStops').currentScore <= 100
                    }">
                      {{ getEvaluateObj('totalStops').currentScore }} 分</span>
                  </el-descriptions-item>
                  <el-descriptions-item label="生产效率得分">
                    <span
                      :class="{
                        'red': getEvaluateObj('dayGroupFactory').currentScore <= 100
                    }">
                      {{ getEvaluateObj('daySumGroupFactory').currentScore }} 分</span>
                  </el-descriptions-item>
                </el-descriptions>
                <br>
                <el-descriptions
                  :column="2"
                  :size="'medium'"
                  border>
                  <el-descriptions-item
                    label="当前应生产量">
                    <span>{{ model.planWgt.toFixed(2) || 0 }}吨</span>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="当前实际产量">
                    <span>{{ model.realWgt.toFixed(2) || 0 }} 吨</span>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="本月目标产量">
                    <span>{{ model.targetValue.toFixed(2) || 0 }}吨</span>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="滚动预测产量">
                    <span>{{ model.rollingForecastWgt.toFixed(2) || '0' }}吨</span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="chart-box">
              <line-chart
                :show-legend="false"
                :show-symbol="true"
                :x-data="factoryData.dataX"
                :chart-data="factoryData.data" />
            </div>
          </el-col>
        </el-row>
        <br>
        <div class="card-sub-title">订单评价</div>
        <el-row
          :gutter="40"
          type="flex"
          align="middle"
        >
          <el-col :span="12">
            <div class="evaluate-detail">
              <div class="detail-left">
                <liquidfill-chart
                  :show-text="getEvaluateObj('avgOrdThk').currentScore"
                  :chart-data="(getEvaluateObj('avgOrdThk').currentScore -50) / 100"/>
              </div>
              <div class="product-detail">
                <el-descriptions
                  :column="1"
                  :size="'medium'"
                  border>
                  <el-descriptions-item
                    label="平均宽度">
                    <p
                      @click="changeEvaluate('orderData', 'avgOrdWid', '')">
                      <em class="value value-underline"> {{ getEvaluateObj('avgOrdWid').monthActualVal }}/{{ getEvaluateObj('avgOrdWid').monthTargetVal }} </em>
                    </p>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="平均厚度">
                    <p
                      @click="changeEvaluate('orderData', 'avgOrdThk', '')">
                      <em class="value value-underline"> {{ getEvaluateObj('avgOrdThk').monthActualVal }}/{{ getEvaluateObj('avgOrdThk').monthTargetVal }} </em>
                    </p>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="坯料单重">
                    <p>
                      <em class="value"> {{ model.slabSingleWgt }} </em>
                    </p>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="生产订单计划机时产量/月目标机时产量">
                    <p>
                      <em class="value"> {{ model.actualForecastMachineOutput.toFixed(2) }}/{{ model.targetMachineOutput.toFixed(2) }} </em>
                    </p>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="chart-box">
              <div class="chart-select">
                <div class="chart-name">
                  <template v-for="item in orderParamsList">
                    <template v-if="item.code === orderData.type">{{ item.name }}</template>
                  </template>
                </div>
                <el-radio-group
                  v-model="orderData.source"
                  @change="changeEvaluate('orderData', orderData.type, $event)"
                >
                  <el-radio label="dayDetailSumVal">月累积</el-radio>
                  <el-radio label="dayDetailVal">月每天</el-radio>
                </el-radio-group>
              </div>
              <line-chart
                :show-legend="false"
                :show-symbol="true"
                :x-data="orderData.dataX"
                :chart-data="orderData.data"
              />
            </div>
          </el-col>
        </el-row>
        <el-divider />
        <div class="card-sub-title">设备作业评价</div>
        <el-row
          :gutter="40"
          type="flex"
          align="middle"
        >
          <el-col :span="12">
            <div class="evaluate-detail">
              <div
                class="detail-left"
                @click="changeEvaluate('deviceData', 'totalStops', '')">
                <liquidfill-chart
                  :show-text="getEvaluateObj('totalStops').currentScore"
                  :chart-data="(getEvaluateObj('totalStops').currentScore -50) / 100"/>
              </div>
              <div class="product-detail">
                <el-descriptions
                  :column="1"
                  :size="'medium'"
                  border>
                  <el-descriptions-item
                    label="检修停时">
                    <p
                      @click="changeEvaluate('deviceData', 'repairStops', '')">
                      <em class="value value-underline"> {{ getEvaluateObj('repairStops').monthActualVal }}/{{ getEvaluateObj('repairStops').monthTargetVal }} </em>
                      <span
                        :class="{
                          'red': getEvaluateObj('repairStops').currentScore < 100
                      }">{{ getEvaluateObj('repairStops').currentScore }} 分</span>
                    </p>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="换辊停时"
                  >
                    <p @click="changeEvaluate('deviceData', 'rollerChangeStops', '')">
                      <em class="value value-underline"> {{ getEvaluateObj('rollerChangeStops').monthActualVal }}/{{ getEvaluateObj('rollerChangeStops').monthTargetVal }} </em>
                      <span
                        :class="{
                          'red': getEvaluateObj('rollerChangeStops').currentScore < 100
                      }">{{ getEvaluateObj('rollerChangeStops').currentScore }} 分</span>
                    </p>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="故障停时">
                    <p @click="changeEvaluate('deviceData', 'faultStops', '')">
                      <em class="value value-underline"> {{ getEvaluateObj('faultStops').monthActualVal }}/{{ getEvaluateObj('faultStops').monthTargetVal }} </em>
                      <span
                        :class="{
                          'red': getEvaluateObj('faultStops').currentScore < 100
                      }">{{ getEvaluateObj('faultStops').currentScore }} 分</span>
                    </p>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="品种试验停时">
                    <p @click="changeEvaluate('deviceData', 'specTestStops', '')">
                      <em class="value value-underline"> {{ getEvaluateObj('specTestStops').monthActualVal }}/{{ getEvaluateObj('specTestStops').monthTargetVal }} </em>
                      <span
                        :class="{
                          'red': getEvaluateObj('specTestStops').currentScore < 100
                      }">{{ getEvaluateObj('specTestStops').currentScore }} 分</span>
                    </p>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="其他停时">
                    <p @click="changeEvaluate('deviceData', 'otherStops', '')">
                      <em class="value value-underline"> {{ getEvaluateObj('otherStops').monthActualVal }}/{{ getEvaluateObj('otherStops').monthTargetVal }} </em>
                      <span
                        :class="{
                          'red': getEvaluateObj('otherStops').currentScore < 100
                      }">{{ getEvaluateObj('otherStops').currentScore }} 分</span>
                    </p>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="chart-box">
              <div class="chart-select">
                <div class="chart-name">
                  <template v-for="item in deviceParamsList">
                    <template v-if="item.code === deviceData.type">{{ item.name }}</template>
                  </template>
                </div>
                <el-radio-group
                  v-model="deviceData.source"
                  @change="changeEvaluate('deviceData', deviceData.type, $event)"
                >
                  <el-radio label="dayDetailSumVal">月累积</el-radio>
                  <el-radio label="dayDetailVal">月每天</el-radio>
                </el-radio-group>
              </div>
              <line-chart
                :show-legend="false"
                :show-symbol="true"
                :x-data="deviceData.dataX"
                :chart-data="deviceData.data"
              />
            </div>
          </el-col>
        </el-row>
        <el-divider />
        <div class="card-sub-title">生产效率评价</div>
        <el-row
          :gutter="40"
          type="flex"
          align="middle"
        >
          <el-col :span="12">
            <div class="evaluate-detail">
              <div
                class="detail-left"
                @click="changeTeamEvaluate('productData', 'dayGroupFactory', '', 'score', 'contributeValue')">
                <liquidfill-chart
                  :show-text="getEvaluateObj('daySumGroupFactory').currentScore"
                  :chart-data="(getEvaluateObj('daySumGroupFactory').currentScore -50) / 100"/>
              </div>
              <div class="product-detail">
                <el-descriptions
                  :column="1"
                  :size="'medium'"
                  border>
                  <el-descriptions-item
                    label="甲班">
                    <p @click="changeTeamEvaluate('productData', 'dayGroupTeam', 'A', 'score', 'contributeValue')">
                      <em class="value value-underline"> {{ getEvaluateObj('daySumGroupTeam', 'A').dayDetailSumVal || 0 }} 块</em>
                      <span
                        :class="{
                          'red': getEvaluateObj('daySumGroupTeam', 'A').currentScore <= 100
                      }">{{ getEvaluateObj('daySumGroupTeam', 'A').currentScore }} 分</span>
                    </p>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="乙班">
                    <p @click="changeTeamEvaluate('productData','dayGroupTeam', 'B', 'score', 'contributeValue')">
                      <em class="value value-underline"> {{ getEvaluateObj('daySumGroupTeam', 'B').dayDetailSumVal || 0 }} 块</em>
                      <span
                        :class="{
                          'red': getEvaluateObj('daySumGroupTeam', 'B').currentScore <= 100
                      }">{{ getEvaluateObj('daySumGroupTeam', 'B').currentScore }} 分</span>
                    </p>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="丙班">
                    <p @click="changeTeamEvaluate('productData', 'dayGroupTeam', 'C', 'score', 'contributeValue')">
                      <em class="value value-underline"> {{ getEvaluateObj('daySumGroupTeam', 'C').dayDetailSumVal || 0 }} 块</em>
                      <span
                        :class="{
                          'red': getEvaluateObj('daySumGroupTeam', 'C').currentScore <= 100
                      }">{{ getEvaluateObj('daySumGroupTeam', 'C').currentScore || 0 }} 分</span>
                    </p>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="丁班">
                    <p @click="changeTeamEvaluate('productData', 'dayGroupTeam', 'D', 'score', 'contributeValue')">
                      <em class="value value-underline"> {{ getEvaluateObj('daySumGroupTeam', 'D').dayDetailSumVal || 0 }} 块</em>
                      <span
                        :class="{
                          'red': getEvaluateObj('daySumGroupTeam', 'D').currentScore <= 100
                      }">{{ getEvaluateObj('daySumGroupTeam', 'D').currentScore }} 分</span>
                    </p>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="chart-box">
              <div class="chart-select">
                <div class="chart-name">
                  <template v-for="item in productParamsList">
                    <template v-if="item.code === productData.team">{{ item.name }}</template>
                  </template>
                </div>
                <el-radio-group
                  v-if="productData.team === ''"
                  v-model="productData.type"
                  @change="changeTeamEvaluate('productData', $event, productData.team, 'score', 'contributeValue')"
                >
                  <el-radio label="daySumGroupFactory">月累积</el-radio>
                  <el-radio label="dayGroupFactory">月每天</el-radio>
                </el-radio-group>
                <el-radio-group
                  v-else
                  v-model="productData.type"
                  @change="changeTeamEvaluate('productData', $event, productData.team, 'score', 'contributeValue')"
                >
                  <el-radio label="daySumGroupTeam">月累积</el-radio>
                  <el-radio label="dayGroupTeam">月每天</el-radio>
                </el-radio-group>
              </div>
              <team-line-chart
                :chart-data="productData.data"
                :show-symbol="true"
                :show-legend="true"
                :x-data="productData.dataX"
                @selected="teamChartSelect($event)"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </template>
    <output-search 
      v-else
      :select-date="selectDate" 
      :team="selectTeam"
      :factory="factory"
      :period="period"
      @cancel="showComponent = false"/>
  </div>
</template>
<script>
import { analysisEvaluationFind, getEvalOverview } from '@/api/kpi'
import { post } from '@/lib/Util'
import KpiNode from '@/components/kpiTree/KpiNode'
import KpiDef from '@/components/KpiDef'
import { ENUM } from '@/lib/Constant'
import * as _ from 'lodash'
import LineChart from '@/pages/decision/analysis/component/line-chart'
import LiquidfillChart from '@/pages/decision/analysis/component/liquidfill-chart'
import baseMixins from '@/mixins/BaseMixins'
import TeamLineChart from '@/pages/decision/analysis/component/team-line-chart'
import { math } from '@/lib/Math'
import OutputSearch from '@/pages/decision/analysis/component/output-search'

export default {
  name: 'feature-factory',
  components: {
    OutputSearch,
    TeamLineChart,
    LiquidfillChart,
    LineChart
  },
  mixins: [baseMixins],
  data() {
    return {
      showComponent: false,
      selectTeam: null,
      selectDate: null,
      activeName: '0',
      dataList: [],
      factory: null,
      period: '',
      loading: true,
      isFactory: false,
      factoryList: ENUM.factoryList,
      dataSource: [], // 查询到的全量源数据
      model: {
        planWgt: 0,
        realWgt: 0,
        targetValue: 0,
        rollingForecastWgt: 0,
        actualForecastMachineOutput: 0,
        targetMachineOutput: 0
      },
      factoryData: {
        data: [],
        dataX: []
      },
      orderData: {
        type: 'avgOrdWid',
        source: 'dayDetailSumVal',
        data: [],
        dataX: []
      },
      orderParamsList: [
        {
          name: '平均宽度',
          code: 'avgOrdWid'
        },
        {
          name: '平均厚度',
          code: 'avgOrdThk'
        }
      ],
      deviceData: {
        type: 'repairStops',
        source: 'dayDetailSumVal',
        data: [],
        datax: []
      },
      deviceParamsList: [
        {
          name: '总停时',
          code: 'totalStops'
        },
        {
          name: '检修停时',
          code: 'repairStops'
        },
        {
          name: '换辊停时',
          code: 'rollerChangeStops'
        },
        {
          name: '故障停时',
          code: 'faultStops'
        },
        {
          name: '品种试验停时',
          code: 'specTestStops'
        },
        {
          name: '其他停时',
          code: 'otherStops'
        }
      ],
      productData: {
        type: 'dayGroupTeam',
        team: '',
        source: 'score',
        source2: 'contributeValue',
        data: [],
        dataX: []
      },
      productParamsList: [
        {
          name: '分厂',
          code: ''
        },
        {
          name: '甲班',
          code: 'A'
        },
        {
          name: '乙班',
          code: 'B'
        },
        {
          name: '丙班',
          code: 'C'
        },
        {
          name: '丁班',
          code: 'D'
        }
      ]
    }
  },
  computed: {
    days: function() {
      return this.$moment(this.period, 'YYYY-MM').daysInMonth()
    },
    hours: function() {
      return this.days * 24
    },
    times: function() {
      return this.days * 3
    },
    dateStr: function() {
      return this.$moment()
        .subtract(1, 'day')
        .format('yyyy 年 MM 月 DD 日')
    }
  },
  watch: {
    period: function() {
      this.loadData()
    }
  },
  created() {
    this.period = this.$moment()
      .subtract(1, 'day')
      .format('yyyy-MM')
    this.factory = parseInt(this.$route.params.id)
  },
  methods: {
    loadData() {
      // avgOrdThk avgOrdWid
      // 订单评价
      //
      post(analysisEvaluationFind, {
        period: this.period,
        factory: this.factory
      }).then(res => {
        this.dataSource = res.data.content.map(item => {
          item.score = item.score ? JSON.parse(item.score) : []
          item.dayArray = item.dayArray ? JSON.parse(item.dayArray) : []
          item.dayDetailVal = item.dayDetailVal
            ? JSON.parse(item.dayDetailVal)
            : []
          item.dayDetailSumVal = item.dayDetailSumVal
            ? JSON.parse(item.dayDetailSumVal)
            : []
          item.contributeValue = item.contributeValue
            ? JSON.parse(item.contributeValue)
            : []
          item.currentScore = item.score.length
            ? item.score[item.score.length - 1]
            : 0
          return item
        })
        // 获取总评分趋势
        const match = this.dataSource.find(item => item.category === 0)
        if (match) {
          this.factoryData.data = [{ data: match.score }]
          this.factoryData.dataX = match.dayArray.map(item =>
            Number(item.substr(8, 2))
          )
          // 数据概览
          post(getEvalOverview, {
            factory: this.factory,
            millDate: match.dayArray[match.dayArray.length - 1]
          }).then(res => {
            Object.assign(this.model, res.data)
          })
        }
        // 订单默认
        this.changeEvaluate('orderData', 'avgOrdWid', 'dayDetailVal')
        // 设备默认
        this.changeEvaluate('deviceData', 'totalStops', 'dayDetailVal')
        // 生产
        this.changeTeamEvaluate(
          'productData',
          'dayGroupFactory',
          '',
          'score',
          'contributeValue'
        )
      })
    },
    // 改变评价图表
    changeEvaluate(chart, type, source) {
      //
      const match = this.dataSource.find(
        item => item.code === type || item.team === type
      )
      if (!match) return
      this[chart].source = source || this[chart].source
      this[chart].type = type
      if (match.dayArray) {
        this[chart].dataX = match.dayArray.map(item =>
          Number(item.substr(8, 2))
        )
      }
      this[chart].data = [{ data: match[this[chart].source] }]
    },
    // 改变带有TEAM评价图表
    changeTeamEvaluate(chart, type, team, source, source2) {
      //
      this[chart].type = type || this[chart].type
      const match = this.dataSource.find(item => {
        if (!team) {
          return item.code === this[chart].type
        } else {
          return item.code === this[chart].type && item.team === team
        }
      })
      if (!match) return
      this[chart].source = source || this[chart].source
      this[chart].source2 = source2 || this[chart].source2
      this[chart].team = team
      if (match.dayArray) {
        this[chart].dataX = match.dayArray.map(item =>
          Number(item.substr(8, 2))
        )
      }
      console.log([{ data: match[source] }, { data: match[source2] }])
      this[chart].data = [
        {
          name: '得分',
          data: match[this[chart].source].map(item => {
            return {
              value: item,
              team: team,
              dateStr: match.dayArray[0].substr(0, 8)
            }
          })
        },
        {
          name: '贡献值',
          data: match[this[chart].source2].map(item => {
            return {
              value: item,
              team: team,
              dateStr: match.dayArray[0].substr(0, 8)
            }
          })
        }
      ]
    },
    // 获取code对应数据
    getEvaluateObj(code, team) {
      const match = this.dataSource.find(item => {
        if (!team) {
          return item.code === code
        } else {
          return item.code === code && item.team === team
        }
      })
      if (!match) return {}
      return match
    },
    // 获取score
    getScore(category) {
      const match = this.dataSource.filter(item => {
        if (category !== 3) {
          return item.category === category
        } else {
          return item.category === category && item.team == null
        }
      })
      if (!match.length) return { score: 0, percent: 0 }
      return {
        score: Number(
          math
            .divide(
              Number(_.sumBy(match, o => Number(o.currentScore)).toFixed(2)),
              match.length
            )
            .toFixed(2)
        ),
        percent:
          (Number(_.sumBy(match, o => Number(o.currentScore)).toFixed(2)) /
            match.length -
            50) /
          100
      }
    },
    getTotalScore() {
      return Number(
        (
          math.divide(this.model.realWgt || 0, this.model.planWgt || 0) * 100
        ).toFixed(2)
      )
    },
    changeFactoryType(E) {
      //
    },
    teamChartSelect(params) {
      this.selectDate = params.data.dateStr + params.name
      this.selectTeam = params.data.team
      this.showComponent = true
    }
  }
}
</script>

<style
  scoped lang="less"
>
.content {
  padding: 24px;
  margin-bottom: 20px;
  background: #fff;

  /deep/ .el-progress--circle .el-progress__text {
    font-size: 30px !important;
    font-weight: bold;
    color: #19be6b;
  }

  .card-title {
    font-size: 20px;
    margin-bottom: 20px;
    line-height: 28px;
    font-weight: bold;
    color: #3a3f63;
  }
  .card-sub-title {
    position: relative;
    padding-left: 15px;
    font-weight: 900;
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 20px;
    color: #3a3f63;
    &:before {
      content: '';
      position: absolute;
      top: 4px;
      bottom: 4px;
      width: 8px;
      left: 0;
      background: #3a3f63;
    }
  }

  .time-info {
    font-size: 20px;
    .date {
      float: right;
      font-size: 26px;
      color: #00b0f0;
      font-weight: 500;
    }
  }
  .product-total {
    text-align: center;
    border: 2px solid #0073c2;
    border-radius: 10px;
    background: #00b0f0;
    overflow: hidden;
    .name {
      font-size: 24px;
      line-height: 70px;
      color: #fff;
    }
    .num {
      background: #fff;
      font-size: 40px;
      font-weight: bold;
      color: #666;
      line-height: 70px;
    }
  }
  .product-detail {
    .value {
      float: right;
    }
    .value-underline {
      text-decoration: underline;
    }
    p {
      color: #4458fe;
      cursor: pointer;
    }
    span {
      color: #4458fe;
    }
    .red {
      color: #ff2855;
    }
  }
  .chart-box {
    .chart-select {
      display: flex;
      align-items: center;
      justify-content: space-between;
      text-align: right;
      margin-bottom: 15px;
    }
  }
}
.categories {
  display: flex;
  margin-bottom: 20px;

  .time {
    margin-left: 10px;
  }
}

.evaluate {
  padding: 20px;
  .evaluate-detail {
    display: flex;
    .detail-left {
      width: 160px;
      height: 160px;
      text-align: center;
      border-radius: 100%;
      float: left;
      .name {
        font-size: 18px;
        line-height: 30px;
      }

      .num {
        font-size: 40px;
        font-weight: bold;
        line-height: 50px;
      }
    }
    .product-detail {
      flex: 1;
      margin-left: 20px;
    }
  }
}
</style>
