<template>
  <div class="full-height">
    <slot name="content"/>
    <div
      v-if="showTable"
      class="scroll-wrapper">
      <div class="chart-tit text-right">
        <span
          v-command="'/screen/C2Meeting/edit'"
          v-if="showEdit"
          class="screen-btn"
          @click="dialogVisible = true">
          <el-icon class="el-icon-edit-outline"/>
          操作
        </span>
      </div>
      <div
        ref="table1"
        class="chart">
        <el-table
          v-loading="loading"
          :data="title=='待探伤'?showTestData:showGridData"
          :max-height="maxHeight"
          :size="'medium'"
          :span-method="spanMethod"
          class="center-table font-big-table"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.children"
                :key="index"
                :width="item.width || ''"
                :property="item.keySave"
                :label="item.label"
                :align="item.align">
                <template
                  v-for="(cItem, cIndex) in item.children">
                  <template v-if="item.inputType === 'textarea'">
                    <el-table-column
                      :key="cIndex"
                      :width="cItem.width || ''"
                      :property="cItem.keySave"
                      :label="cItem.label"
                      :align="cItem.align">
                      <template v-slot="{ row }">
                        <div
                          slot="content"
                          v-html="formatText(row[cItem.keySave], cItem.split)"
                        />
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      :key="cIndex"
                      :width="cItem.width || ''"
                      :property="cItem.keySave"
                      :label="cItem.label"
                      :align="cItem.align"/>
                  </template>
                </template>
              </el-table-column>
              <template v-else>
                <el-table-column
                  v-if="item.type === 'index'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                />
                <template v-else>
                  <template v-if="item.inputType === 'textarea'">
                    <el-table-column
                      :key="index"
                      :width="item.width || ''"
                      :property="item.keySave"
                      :label="item.label"
                      :align="item.align">
                      <template v-slot="{ row }">
                        <div
                          slot="content"
                          v-html="formatText(row[item.keySave], item.split)"
                        />
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else-if="item.inputType === 'file'">
                    <el-table-column
                      :key="index"
                      :width="item.width || ''"
                      :property="item.keySave"
                      :label="item.label"
                      :align="item.align">
                      <template v-slot="{ row }">
                        <div
                          v-if="row.attachmentUpload"
                          class="file-list">
                          <div
                            v-for="file in parseAttachmentFiles(row.attachmentUpload, row.fileNames)"
                            :key="file.id"
                            class="file-item">
                            <a
                              :href="file.url"
                              target="_blank"
                              class="file-link">
                              <i class="el-icon-document"/>
                              {{ file.name }}
                            </a>
                          </div>
                        </div>
                        <div v-else>无附件</div>
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      :key="index"
                      :width="item.width || ''"
                      :property="item.keySave"
                      :label="item.label"
                      :align="item.align"/>
                  </template>
                </template>
              </template>
            </template>
          </template>
        </el-table>
        <!-- 若hasPage为真则增加分页 -->
        <div
          v-if="hasPage"
          class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pageSize"
            :total="totalElements"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"/>
        </div>

        <slot name="bottom"/>
        <div class="calculationHints">
          <br>
          <br>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event)">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportTable">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="gridData"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.children"
                :key="index"
                :width="item.width || ''"
                :label="item.label">
                <template
                  v-for="(cItem, cIndex) in item.children">
                  <el-table-column
                    :key="cIndex"
                    :width="cItem.width || ''"
                    :property="cItem.keySave"
                    :label="cItem.label">
                    <template v-slot="{ row }">
                      <template v-if="cItem.inputType === 'textarea'">
                        <el-input
                          v-model="row[cItem.keySave]"
                          :rows="4"
                          type="textarea"
                        />
                      </template>
                      <template v-else>
                        <el-input v-model="row[cItem.keySave]"/>
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </el-table-column>
              <template v-else>
                <el-table-column
                  v-if="item.type === 'index'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                />
                <template v-else>
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label">
                    <template v-slot="{ row }">
                      <template v-if="item.inputType === 'textarea'">
                        <el-input
                          v-model="row[item.keySave]"
                          :rows="4"
                          type="textarea"
                        />
                      </template>
                      <template v-else-if="item.inputType === 'date'">
                        <el-date-picker
                          v-model="row[item.keySave]"
                          :size="'mini'"
                          :value-format="'yyyy-MM-dd'"
                          type="date"
                          class="screen-input"
                          @focus.native="row[item.keySave] = formatDateValue(row[item.keySave])"/>
                      </template>
                      <template v-else-if="item.inputType === 'month'">
                        <el-date-picker
                          v-model="row[item.keySave]"
                          :size="'mini'"
                          :value-format="'yyyy-MM'"
                          type="month"
                          class="screen-input"
                          @focus.native="row[item.keySave] = formatDateValue(row[item.keySave])"/>
                      </template>
                      <template v-else-if="item.inputType === 'file'">
                        <div>
                          <!-- 展示已上传的文件列表 -->
                          <div
                            v-if="row.attachmentUpload"
                            class="upload-file-list">
                            <div
                              v-for="file in parseAttachmentFiles(row.attachmentUpload, row.fileNames)"
                              :key="file.id"
                              class="file-item">
                              <a
                                :href="file.url"
                                target="_blank"
                                class="file-link">
                                <i class="el-icon-document"/>
                                {{ file.name }}
                              </a>
                              <i
                                class="el-icon-delete"
                                @click="deleteAttachment(file.id, row)"/>
                            </div>
                          </div>

                          <!-- 文件上传组件 -->
                          <el-upload
                            ref="upload"
                            :auto-upload="false"
                            :http-request="httpRequest"
                            :on-change="(file) => handleFileChange(file, row)"
                            :show-file-list="false"
                            action="#"
                            class="upload-file-list-button"
                          >
                            <el-button
                              size="small"
                              type="primary">
                              <i class="el-icon-upload2"/> 上传附件
                            </el-button>
                          </el-upload>
                        </div>
                      </template>
                      <template v-else>
                        <el-input v-model="row[item.keySave]"/>
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </template>
            </template>
          </template>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, row)">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData()">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

  <script>
import moment from 'moment'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import { qmsQualityQuery, qmsQualitySave } from '@/api/screen'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import {
  batchUpdateResource,
  deleteFileByIds,
  uploadFile,
  downloadFileById
} from '@/api/system'
import ImgView from '@/components/ImgView'

export default {
  name: 'custom-table-research',
  components: { ScreenBorder },
  props: {
    title: {
      type: String,
      default: ''
    },
    setting: {
      type: Array,
      default: function() {
        return []
      }
    },
    mergeSet: {
      type: Object,
      default: function() {
        return {}
      }
    },
    selectDate: {
      type: String,
      default: ''
    },
    urlList: {
      type: String,
      default: ''
    },
    urlSave: {
      type: String,
      default: ''
    },
    urlDelete: {
      type: String,
      default: ''
    },
    showTable: {
      type: Boolean,
      default: true
    },
    showEdit: {
      type: Boolean,
      default: true
    },
    heightAuto: {
      type: Boolean,
      default: true
    },
    hasPage: {
      type: Boolean,
      default: false
    }
  },
  data: function() {
    return {
      cDate: '',
      loading: false,
      baseURL: 'http://172.25.63.67:9800/' + downloadFileById,
      dialogVisible: false,
      showGridData: [],
      showTestData: [],
      gridData: [],
      importDate: null,
      importDateVisible: false,
      importFunName: '',
      mergeArr: [],
      spanArr: {},
      position: 0,
      maxHeight: null,
      // 分页相关数据
      currentPage: 1,
      pageSize: 30,
      totalElements: 0,
      totalPages: 0,
      ABC: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'K'
      ],
      fileNameCache: {}
    }
  },
  computed: {
    canEditQuality: function() {
      // console.log(moment().format('yyyy-MM-DD'), this.cDate)
      return (
        moment().format('yyyy-MM-DD') <=
        moment(this.cDate)
          .add(1, 'day')
          .format('yyyy-MM-DD')
      )
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getData()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.getData()
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    httpRequest(params) {},
    async handleChange(file, fileList, index) {
      console.log(file, fileList, index)
      const formData = new FormData()
      formData.append('files', file.raw)
      post(uploadFile, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        if (res.success) {
          this.$message.success('图片上传成功！')
          const obj = this.unfinished.gridData[index]
          const pictures = obj.picture ? obj.picture.split('|') : []
          pictures.push(res.data[0].id)
          this.unfinished.gridData.splice(
            index,
            1,
            Object.assign({}, obj, {
              picture: pictures.join('|')
            })
          )
          // this.unfinished.gridData[index].picture = pictures.join('|')
        } else {
          this.$message.warning('文件上传失败！')
          this.loading = false
        }
      })
    },
    async handleImgDelete(file, index) {
      const del = await post(deleteFileByIds, { ids: [file.id] })
      if (del.success) {
        const obj = this.unfinished.gridData[index]
        this.unfinished.gridData.splice(
          index,
          1,
          Object.assign({}, obj, {
            picture: obj.picture
              .split('|')
              .filter(item => item !== file.id)
              .join('|')
          })
        )
      }
    },
    getPictureList(picture) {
      return picture.split('|')
    },
    // 导入文件
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = this.ABC[index]
      })
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, obj)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.gridData = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    // 导出表格
    exportTable() {
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = item.label
      })
      const data = [obj].concat(
        _.cloneDeep(
          this.gridData.map(item => {
            const objRow = {}
            this.setting.forEach(set => {
              objRow[set.keySave] = item[set.keySave]
            })
            return objRow
          })
        )
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `${this.title}（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 添加日期格式化方法
    formatDateValue(dateValue) {
      if (!dateValue) return ''

      // 如果已经是标准格式yyyy-MM-dd，直接返回
      if (
        typeof dateValue === 'string' &&
        /^\d{4}-\d{2}-\d{2}$/.test(dateValue)
      ) {
        return dateValue
      }

      // 处理类似"45658-04"这样的混合格式
      if (typeof dateValue === 'string' && dateValue.includes('-')) {
        const parts = dateValue.split('-')
        if (/^\d{5}$/.test(parts[0])) {
          // 第一部分是5位数字，可能是Excel日期数字
          const excelDate = parseInt(parts[0])
          const date = this.excelDateToJSDate(excelDate)
          // 合并年份和后面的月份
          return `${date.getFullYear()}-${parts[1].padStart(2, '0')}`
        }
        return dateValue
      }

      // 处理纯数字格式，如"45658"
      if (/^\d+$/.test(String(dateValue))) {
        const excelDate = parseInt(dateValue)
        const date = this.excelDateToJSDate(excelDate)
        return moment(date).format('YYYY-MM')
      }

      return dateValue
    },

    // Excel日期转换为JavaScript日期对象
    excelDateToJSDate(excelDate) {
      // Excel日期从1900年1月1日开始，但有一个1900年2月29日的bug，实际上这一天不存在
      // JavaScript的Date从1970年1月1日开始计算
      const date = new Date((excelDate - 25569) * 86400 * 1000)
      return date
    },
    // 获取数据
    getData() {
      this.loading = true
      post(this.urlList, {
        setTime: this.cDate,
        //如果hasPage为真则传递page和size
        ...(this.hasPage
          ? { page: this.currentPage - 1, size: this.pageSize }
          : {})
      })
        .then(res => {
          this.loading = false
          this.showTestData = []

          // 如果启用了分页，从响应中提取分页信息
          if (this.hasPage && res.data) {
            // 更新分页信息
            this.totalElements = res.data.totalElements || 0
            this.totalPages = res.data.totalPages || 0

            // 确保当前页码不超过总页数
            if (this.totalPages > 0 && this.currentPage > this.totalPages) {
              this.currentPage = this.totalPages
              // 重新获取数据
              this.getData()
              return
            }
          }

          const content = this.hasPage ? res.data.content || [] : res.data || []

          this.showGridData = content.map(item => {
            // 处理日期格式
            const processedItem = { ...item }
            Object.keys(processedItem).forEach(key => {
              if (
                key.toLowerCase().includes('time') ||
                key.toLowerCase().includes('date')
              ) {
                processedItem[key] = this.formatDateValue(processedItem[key])
              }
            })

            // 确保有附件的数据项有fileNames字段
            if (processedItem.attachmentUpload && !processedItem.fileNames) {
              const fileIds = processedItem.attachmentUpload.split(',')
              const fileNames = fileIds.map(
                id => this.fileNameCache[id] || `附件${fileIds.indexOf(id) + 1}`
              )
              processedItem.fileNames = fileNames.join('|')
            }

            this.showTestData.unshift(processedItem)
            const obj = {}
            this.setting.forEach(set => {
              if (set.children && set.children.length) {
                set.children.forEach(child => {
                  obj[child.keySave] =
                    processedItem[child.keyQuery || child.keySave]
                })
              } else {
                obj[set.keySave] = processedItem[set.keyQuery || set.keySave]
              }
            })

            // 确保附件和文件名信息被正确传递
            if (processedItem.attachmentUpload) {
              obj.attachmentUpload = processedItem.attachmentUpload
              obj.fileNames = processedItem.fileNames
            }

            // 确保ID字段被保留
            if (processedItem.id) {
              obj.id = processedItem.id
            }

            return obj
          })
          this.gridData = _.cloneDeep(this.showGridData)
          this.$nextTick(() => {
            this.$emit('change', this.showGridData)
          })
        })
        .catch(error => {
          this.loading = false
          console.error('获取数据失败:', error)
          this.$message.error('获取数据失败')
        })
    },
    change() {
      this.saveData()
    },
    // 更新数据
    saveData() {
      this.loading = true

      // 不再使用全局fileNames

      // 确保每行数据都有fileNames字段
      this.gridData.forEach(item => {
        if (item.attachmentUpload && !item.fileNames) {
          // 如果有附件但没有fileNames，从缓存中获取文件名
          const fileIds = item.attachmentUpload.split(',')
          const fileNames = fileIds.map(
            id => this.fileNameCache[id] || `附件${fileIds.indexOf(id) + 1}`
          )
          item.fileNames = fileNames.join('|')
        }
      })

      // 数据信息
      const params = {
        setTime: this.cDate,
        data: this.gridData.map(item => {
          // 确保保留ID字段，用于后端识别更新的记录
          const saveItem = { ...item }
          // 如果没有ID字段，则是新增记录，不需要传ID
          return saveItem
        })
      }

      post(this.urlSave, params)
        .then(res => {
          this.loading = false
          if (res.status == 1) {
            this.$message.success('保存成功！')
            this.dialogVisible = false
            // 刷新数据，保持在当前页
            this.getData()
          } else {
            this.$message.warning('保存失败！')
          }
        })
        .catch(error => {
          this.loading = false
          console.error('保存数据失败:', error)
          this.$message.error('保存数据失败')
        })
    },
    // 导入日期数据
    importData(date) {
      post(this.urlList, {
        setTime: date
      }).then(res => {
        //
        this.loading = false
        this.gridData = res.data.map(item => {
          const obj = {}
          this.setting.forEach(set => {
            obj[set.keySave] = item[set.keyQuery]
          })

          // 确保有附件的数据项有fileNames字段
          if (item.attachmentUpload) {
            obj.attachmentUpload = item.attachmentUpload

            if (item.fileNames) {
              obj.fileNames = item.fileNames
            } else {
              const fileIds = item.attachmentUpload.split(',')
              const fileNames = fileIds.map(
                id => this.fileNameCache[id] || `附件${fileIds.indexOf(id) + 1}`
              )
              obj.fileNames = fileNames.join('|')
            }
          }

          return obj
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 执行导入
    importHistoryData() {
      this.importData(this.importDate)
      this.importDateVisible = false
    },
    // 下拉菜单指令
    handleProcessedCommand(command) {
      if (command === 'yesterday') {
        this.importData(
          this.$moment(this.cDate)
            .subtract(1, 'day')
            .format('yyyy-MM-DD')
        )
      } else {
        this.importDate = this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
        this.importDateVisible = true
      }
    },
    // 数据管理
    clearGridData() {
      this.gridData = []
    },
    addGridData() {
      this.gridData.push({})
    },
    delGridData(index, rowData) {
      if (rowData && rowData.id) {
        //接口调用
        this.$confirm('确定要删除此行数据吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.gridData.splice(index, 1)
            post(this.urlDelete, {
              data: [rowData]
            }).then(res => {
              if (res.status == 1) {
                this.$message.success('删除成功！')
                this.getData()
              } else {
                this.$message.warning('删除失败！')
              }
            })
          })
          .catch(() => {})
      } else {
        this.gridData.splice(index, 1)
      }
    },
    // 日期改变推送
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    // 计算需要合并的单元格
    formatSpanData(data) {
      this.mergeArr.forEach(keyName => {
        this.spanArr[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
    },
    // 合并单元格
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // 排除序号列，只对第二列（索引为1的列）进行合并
      if (column.type === 'index' || columnIndex === 0) {
        return [1, 1] // 序号列不合并
      }

      if (columnIndex === 1) {
        const currentValue = row[column.property]
        const preRow = this.showGridData[rowIndex - 1]
        //上一行这一列的数据
        const preValue = preRow ? preRow[column.property] : null
        // 如果当前值和上一行的值相同，则将当前单元格隐藏
        if (currentValue === preValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let rowspan = 1
          // 计算应该合并的行数
          for (let i = rowIndex + 1; i < this.showGridData.length; i++) {
            const nextRow = this.showGridData[i]
            const nextValue = nextRow[column.property]
            if (nextValue === currentValue) {
              rowspan++
            } else {
              break
            }
          }
          return { rowspan, colspan: 1 }
        }
      }
    },
    // 生成带换行数据
    formatText(text, split) {
      if (!text) {
        return ''
      }
      if (split) text = text.split(split).join('\n')
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },
    // 计算高度
    calculate() {
      this.showTable &&
        this.heightAuto &&
        (this.maxHeight = this.$refs.table1.offsetHeight - 60)
    },

    // 分页相关方法
    // 处理每页条数变化
    handleSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1 // 重置为第一页
      this.getData()
    },

    // 处理页码变化
    handleCurrentChange(page) {
      this.currentPage = page
      this.getData()
    },
    parseAttachmentFiles(attachmentStr, rowFileNames) {
      if (!attachmentStr) return []

      const fileIds = attachmentStr.split(',')
      let fileNameArr = []

      // 使用行级别的fileNames
      if (rowFileNames) {
        fileNameArr = rowFileNames.split('|')
      }

      return fileIds.map((id, index) => {
        // 优先使用对应位置的文件名，其次使用缓存中的文件名，最后使用默认名称
        const fileName =
          (fileNameArr[index] && fileNameArr[index].trim()) ||
          this.fileNameCache[id] ||
          `附件${index + 1}`

        // 将文件名存入缓存
        this.fileNameCache[id] = fileName

        return {
          id: id,
          name: fileName,
          url: `${this.baseURL}/${id}`
        }
      })
    },
    handleFileChange(file, row) {
      const formData = new FormData()
      formData.append('files', file.raw)

      this.loading = true
      post(uploadFile, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
        .then(res => {
          this.loading = false
          if (res.success && res.data && res.data.length > 0) {
            this.$message.success('附件上传成功！')

            const fileId = res.data[0].id
            const fileName = res.data[0].name

            this.fileNameCache[fileId] = fileName

            // 使用Vue.set确保响应式更新
            const newAttachments = [
              ...(row.attachmentUpload ? row.attachmentUpload.split(',') : []),
              fileId
            ].join(',')

            const newFileNames = [
              ...(row.fileNames ? row.fileNames.split('|') : []),
              fileName
            ].join('|')

            // 正确写法：创建新对象触发响应式更新
            this.$set(row, 'attachmentUpload', newAttachments)
            this.$set(row, 'fileNames', newFileNames)

            // 强制更新表格（可选）
            this.gridData = [...this.gridData]
          } else {
            this.$message.warning('附件上传失败！')
          }
        })
        .catch(error => {
          this.loading = false
          console.error('上传失败：', error)
          this.$message.error('附件上传失败')
        })
    },
    deleteAttachment(fileId, row) {
      this.$confirm('确定要删除此附件吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        post(deleteFileByIds, { ids: [fileId] }).then(res => {
          if (res.success) {
            this.$message.success('附件删除成功')

            // 新增：清理文件名缓存
            delete this.fileNameCache[fileId]

            const currentAttachments = row.attachmentUpload.split(',')
            const currentFileNames = row.fileNames
              ? row.fileNames.split('|')
              : []

            // 优化索引查找方式
            const index = currentAttachments.indexOf(fileId)
            if (index > -1) {
              currentAttachments.splice(index, 1)
              // 确保文件名和附件ID的索引一致
              if (index < currentFileNames.length) {
                currentFileNames.splice(index, 1)
              }
            }

            row.attachmentUpload = currentAttachments.join(',')
            row.fileNames = currentFileNames.join('|')

            // 强制更新视图
            this.$forceUpdate()
          }
        })
      })
    }
  }
}
</script>

  <style scoped lang="less">
// 大屏按钮
.screen-btn {
  display: inline-block;
  min-width: 68px;
  height: 28px;
  padding: 0 5px;
  background: rgba(31, 198, 255, 0.3);
  border: 1px solid #1fc6ff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  color: #fff;
  &:hover {
    background: rgba(31, 198, 255, 0.6);
    border: 1px solid #1fc6ff;
  }
}

// 分页容器样式
.pagination-container {
  margin-top: 15px;
  text-align: right;
  border-radius: 4px;

  /deep/ .el-pagination {
    padding: 0 20px;

    .el-pagination__total,
    .el-pagination__sizes .el-input .el-input__inner,
    .el-pagination__jump .el-input .el-input__inner,
    .btn-prev,
    .btn-next,
    .el-pager li {
      background-color: transparent;
      color: #fff;
      border-color: rgba(31, 198, 255, 0.3);
    }

    .el-pager li:not(.disabled).active {
      background-color: rgba(31, 198, 255, 0.6);
      color: #fff;
    }

    .el-pager li:hover:not(.disabled):not(.active) {
      color: #1fc6ff;
    }
  }
}
.scroll-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .chart-tit {
    font-size: 16px;
    font-weight: bolder;
    color: #ffffff;
    line-height: 20px;
    margin: 5px 0 10px;
  }
  .chart {
    flex: 1;
    height: 0;
  }
}
/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}
/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
.calculationHints {
  margin-top: 28px;
  font-size: 18px;
  color: skyblue;
}
.text-center {
  margin-top: 20px;
}

.screen-input {
  width: 130px;
}

.upload-file-list-button {
  display: flex;
  justify-content: center;
  align-items: center;

  /deep/ .el-button--primary {
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
  }
}

.upload-file-list {
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .file-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .file-link {
      color: #1fc6ff;
      text-decoration: none;
      margin-right: 10px;

      &:hover {
        text-decoration: underline;
      }
    }

    .el-icon-delete {
      color: #ff4d4f;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}
.file-list {
  .file-item {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 5px;

    .file-link {
      color: #1fc6ff;
      text-decoration: none;
      display: flex;
      align-items: center;

      &:hover {
        text-decoration: underline;
      }

      .el-icon-document {
        margin-right: 5px;
        font-size: 16px;
      }
    }
  }
}
</style>
