/**
 * 日期转换为: yyyy-MM-dd hh:mm
 * eg:2021-01-01 01:01
 * eg:2021-11-11 11:11
 * @param value
 * @returns {string}
 */
export function date2ymdhs(value) {
  let year = value.getFullYear()
  let month =
    value.getMonth() + 1 < 10
      ? `0${value.getMonth() + 1}`
      : value.getMonth() + 1
  let date = value.getDate() < 10 ? `0${value.getDate()}` : value.getDate()
  let hour = value.getHours() < 10 ? `0${value.getHours()}` : value.getHours()
  let minute =
    value.getMinutes() < 10 ? `0${value.getMinutes()}` : value.getMinutes()
  return `${year}-${month}-${date} ${hour}:${minute}`
}
/**
 * 日期转换为: yyyy-MM
 * eg:2021-01
 * @param value
 * @returns {string}
 */
export function date2ym(value) {
  let year = value.getFullYear()
  let month = add0(value.getMonth() + 1)
  return `${year}-${month}`
}

/**
 * 日期转换为: yyyy-MM-dd
 * eg:2021-01-01 01:01
 * eg:2021-11-11 11:11
 * @param value
 * @returns {string}
 */
export function date2ymd(value) {
  let year = value.getFullYear()
  let month = add0(value.getMonth() + 1)
  let date = value.getDate() < 10 ? `0${value.getDate()}` : value.getDate()
  return `${year}-${month}-${date}`
}

/**
 * 日期转换为: MM-dd
 * eg:01月01日
 * @param value
 * @returns {string}
 */
export function date2md(value) {
  // let year = value.getFullYear()
  let month = add0(value.getMonth() + 1)
  let date = value.getDate() < 10 ? `0${value.getDate()}` : value.getDate()
  return `${month}月${date}日`
}
/**
 * 比较两个时间，大于 返回true
 * @param time1
 * @param time2
 * @returns {number}
 */
export function dateCompare(time1, time2) {
  let temp = new Date(time1).getTime() - new Date(time2).getTime()
  return temp > 0
}

/*获取当天*/
export function getNowDay() {
  const date = new Date()
  return date2ymd(date)
}
/*获取当月最后一天*/
export function getMonthLastDayByTime(time) {
  const date = new Date(time)
  const y = date.getFullYear()
  const m = date.getMonth()
  return date2ymd(new Date(y, m + 1, 0))
}
/*获取当年第一天*/
export function getYearFirstDay() {
  const date = new Date()
  const y = date.getFullYear()
  return date2ymd(new Date(y, 0, 1))
}
/*获取当年最后一天*/
export function getYearLastDay() {
  const date = new Date()
  const y = date.getFullYear() + 1
  return date2ymd(new Date(y, 0, 0))
}
/*获取当年第一月*/
export function getFirstMonth() {
  const date = new Date()
  const y = date.getFullYear()
  return `${y}-01`
}
/*获取去年第一月*/
export function getLastFirstMonth() {
  const date = new Date()
  const y = date.getFullYear()
  return `${y - 1}-01`
}
/*获取距离当月往后12个月*/
export function getLastOneYearMonth() {
  const date = new Date()
  const y = date.getFullYear()
  const m = date2ym(date)
  let m1 = m.split('-')
  let m2 = m1[1]
  let m3 = m2.split('')
  let m4 = parseInt(m3[1]) + 1
  return `${y - 1}-0${m4}`
}
/*获取当月*/
export function getMonthDay() {
  const date = new Date()
  return date2ym(date)
}
/*获取当月第一天*/
export function getMonthFirstDay() {
  const date = new Date()
  const y = date.getFullYear()
  const m = date.getMonth()
  return date2ymd(new Date(y, m, 1))
}
/*获取当月最后一天*/
export function getMonthLastDay() {
  const date = new Date()
  const y = date.getFullYear()
  const m = date.getMonth()
  return date2ymd(new Date(y, m + 1, 0))
}

//将excel的日期格式转成Date()对象;
export function getFormatDate_XLSX(serial) {
  const utc_days = Math.floor(serial - 25569)
  const utc_value = utc_days * 86400
  const date_info = new Date(utc_value * 1000)
  const fractional_day = serial - Math.floor(serial) + 0.0000001
  let total_seconds = Math.floor(86400 * fractional_day)
  const seconds = total_seconds % 60
  total_seconds -= seconds
  const hours = Math.floor(total_seconds / (60 * 60))
  const minutes = Math.floor(total_seconds / 60) % 60
  const d = new Date(
    date_info.getFullYear(),
    date_info.getMonth(),
    date_info.getDate(),
    hours,
    minutes,
    seconds
  )
  return d
}

/**
 * 日期转换为: yyyy-MM-dd hh:mm:ss
 * eg:2021-01-01 01:01:33
 * @param value
 * @returns {string}
 */
export function date2ymdhs2(value, format = '-', format2 = ':') {
  const YYYY = value.getFullYear()
  const MM = add0(value.getMonth() + 1)
  const DD = add0(value.getDate())
  const hh = add0(value.getHours())
  const mm = add0(value.getMinutes())
  const ss = add0(value.getSeconds())

  return `${YYYY}${format}${MM}${format}${DD} ${hh}${format2}${mm}${format2}${ss}`
}

function add0(m) {
  return m < 10 ? '0' + m : m
}
/**
 * 是否距离当前时间多少小时
 * @param value 时间
 */
export function dateToNowH(value) {
  let date = new Date()
  //毫秒
  let temp = new Date(value).getTime() - date.getTime()
  //毫秒转小时
  return temp / 1000 / 3600
}
