<template>
  <div class="container"> 
    <div class="header-footer">
      <el-input
        v-model="monthlyReport"
        :rows="3"
        type="textarea"
        class="chart-input"
        resize="none"
      />
      <el-button 
        :loading="submitLoading" 
        type="primary" 
        class="submit-btn"
        icon="el-icon-finished"
        @click="handleSave">提交</el-button>
    </div>
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="事业部稽查(月度)">
          <custom-table
            ref="departmentAuditRef" 
            :show-table="true"
            :show-edit="true"
            :key="'departmentAudit'"
            :title="'事业部稽查(月度)'"
            :setting="tableObj.setting"
            :url-list="tableObj.url.list"
            :url-save="tableObj.url.save"
            :select-date="selectDate"
          />
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="受理质量异议(月度)">
          <custom-table
            ref="acceptQualityDisputeRef" 
            :show-table="true"
            :show-edit="true"
            :key="'acceptQualityDispute'"
            :title="'受理质量异议(月度)'"
            :setting="tableObj2.setting"
            :url-list="tableObj2.url.list"
            :url-save="tableObj2.url.save"
            :select-date="selectDate"
          />
        </screen-border>
      </div>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityWeeklyReportScreen/components/screen-border.vue'
import CustomTable from '@/pages/screen/qualityWeeklyReportScreen/components/custom-table.vue'
import {
  departmentAuditSaveAll,
  departmentAuditFindAllDate,
  qualityObjectionSaveAll,
  qualityObjectionFindAllDate,
  findAllDateRemark,
  saveAllRemark
} from '@/api/screen'
import { post } from '@/lib/Util'

export default {
  name: 'QualityNotification',
  components: {
    CustomTable,
    ScreenBorder
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      submitLoading: false,
      monthlyReport: '',
      tableObj: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '缺陷类型',
            width: '550'
          },
          {
            keyQuery: 'problem',
            keySave: 'problem',
            label: '稽查问题',
            width: '575'
          },
          {
            keyQuery: 'unit',
            keySave: 'unit',
            label: '责任单位',
            width: '600'
          }
        ],
        url: {
          list: departmentAuditFindAllDate,
          save: departmentAuditSaveAll
        }
      },
      tableObj2: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'plt',
            keySave: 'plt',
            label: '生产厂',
            width: '200'
          },
          {
            keyQuery: 'brand',
            keySave: 'brand',
            label: '牌号',
            width: '195'
          },
          {
            keyQuery: 'specs',
            keySave: 'specs',
            label: '规格/mm',
            width: '200'
          },
          {
            keyQuery: 'blocksNum',
            keySave: 'blocksNum',
            label: '块数',
            width: '200'
          },
          {
            keyQuery: 'wgt',
            keySave: 'wgt',
            label: '重量/吨',
            width: '200'
          },
          {
            keyQuery: 'manufacturer',
            keySave: 'manufacturer',
            label: '使用厂家',
            width: '250'
          },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '异议缺陷',
            width: '250'
          },
          {
            keyQuery: 'situation',
            keySave: 'situation',
            label: '具体情况',
            width: '230'
          },
          {
            keyQuery: 'unit',
            keySave: 'unit',
            label: '责任单位',
            width: '230'
          }
        ],
        url: {
          list: qualityObjectionFindAllDate,
          save: qualityObjectionSaveAll
        }
      }
    }
  },
  watch: {
    selectDate: function() {
      this.getRemarkInfo()
    }
  },
  created() {
    this.getRemarkInfo()
  },
  methods: {
    async getRemarkInfo() {
      const params = {
        setTime: this.selectDate,
        type: 1 // 1:质量通报 2:非计划统计 3.成材率 4.专利受理情况 5.
      }
      const res = await post(findAllDateRemark, params)
      this.monthlyReport = res.data.length ? res.data[0].remark : ''
    },
    async handleSave() {
      this.submitLoading = true
      const params = {
        setTime: this.selectDate,
        type: 1,
        data: {
          remark: this.monthlyReport
        }
      }
      const res = await post(saveAllRemark, params)
      if (res.status === 1) {
        this.$message.success('已提交')
      } else {
        this.$message.error(res.data)
      }
      this.submitLoading = false
    }
  }
}
</script>


<style scoped lang="less">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: hidden;

  .header-footer {
    width: 100%;
    margin-bottom: 20px;
    gap: 10px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-between;
    position: relative;

    // padding: 5px 8px;

    .submit-btn {
      width: 68px !important;
      height: 28px !important;
      padding: 0 !important;
      line-height: 26px !important;
      background: rgba(31, 198, 255, 0.3);
      border: 1px solid #1fc6ff;
      color: #fff;
      font-size: 14px;
      position: absolute;
      right: 10px;
      bottom: 10px;
      // margin: 10px 0;

      &:hover {
        background: rgba(31, 198, 255, 0.6);
      }
    }
    /deep/ .el-textarea__inner,
    /deep/ .el-input__inner {
      background: transparent;
      border: 1px solid rgba(31, 198, 255, 0.3);
      color: #fff;
      padding: 10px 80px 10px 10px !important;

      &:focus {
        border-color: #1fc6ff;
      }
    }
  }

  .chart-row {
    margin-bottom: 10px;
    height: 100%;
    flex-direction: column;
  }

  .chart-row,
  .table-row {
    display: flex;
    gap: 10px;
    width: 100%;
  }

  .chart-box,
  .table-box {
    flex: 1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .chart-footer {
    margin: 0px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    padding: 5px 8px;
    background: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);
    width: 100%;
  }

  .chart-input {
    height: 100%;
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      // border: none;
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      height: 100%;
      padding: 0;

      // &:focus {
      //   outline: none !important;
      //   box-shadow: none !important;
      //   border: none !important;
      //   border-color: transparent !important;
      // }
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      border-color: transparent !important;
    }
  }

  /deep/ .el-table {
    background-color: transparent;
    width: 100% !important;
    table-layout: fixed;

    &::before {
      display: none;
    }

    th {
      background-color: rgba(31, 198, 255, 0.3);
      color: #fff;
      border-color: #1fc6ff;
    }

    td {
      background-color: transparent;
      color: #fff;
      border-color: #2e4262;
    }

    tr {
      background-color: transparent;
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-content {
    padding: 10px;
    box-sizing: border-box;
  }
}
</style>
