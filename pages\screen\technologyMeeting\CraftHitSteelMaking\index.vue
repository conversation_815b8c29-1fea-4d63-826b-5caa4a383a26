<template>
  <div class="content">
    <div
      class="content-item"
      style="height: 45%; flex: unset">
      <screen-border :title="'成分控制'">
        <template v-slot:headerRight>
          <!--          <span-->
          <!--            v-command="'/screen/technologyMeeting/edit'"-->
          <!--            class="screen-btn"-->
          <!--            @click="pilotPlan1.dialogVisible = true">-->
          <!--            <el-icon class="el-icon-edit-outline"/>-->
          <!--            操作-->
          <!--          </span>-->
        </template>
        <div
          class="scroll-wrapper">
          <el-row
            :gutter="32"
            class="full-height">
            <el-col
              :span="8"
              class="full-height">
              <div class="chart-wrapper">
                <div
                  class="operate-box text-right">
                  <el-radio-group
                    v-model="pieChart.dateType1"
                    size="mini"
                    class="screen-input"
                    @input="changePieChart($event)">
                    <el-radio-button :label="0">日

                    </el-radio-button>
                    <el-radio-button :label="1">月</el-radio-button>
                  </el-radio-group>
                </div>
                <div
                  class="chart"
                  @click="showRateDetail()">
                  <pie-rate-chart
                    :chart-data="Number(pieChart.RATE)"
                    :unit="'炉数'"
                    :title="'成分合格率'"
                    :title-num="pieChart.RATE + '%'"
                    :title-text="'不合格炉数：' + pieChart.NUM"
                    :label-width="22"
                    :color="pieChart.color"
                    :vertical="false"/>
                </div>
              </div>
            </el-col>
            <el-col
              :span="16"
              class="full-height">
              <bars-stack-chart
                ref="chart1"
                :bar-width="40"
                :show-label="true"
                :show-legend="true"
                :color="['#86e597']"
                :unit="'炉'"
                :chart-data="chartData.bar1"
                :x-data="chartData.bar1X"/>
            </el-col>
          </el-row>
        </div>
      </screen-border>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <screen-border :title="'炼钢工序关键参数'">
        <template v-slot:headerRight>
          <!--          <span-->
          <!--            v-command="'/screen/technologyMeeting/edit'"-->
          <!--            class="screen-btn"-->
          <!--            @click="pilotPlan1.dialogVisible = true">-->
          <!--            <el-icon class="el-icon-edit-outline"/>-->
          <!--            操作-->
          <!--          </span>-->
        </template>
        <div class="chart-wrapper">
          <el-row
            :gutter="32"
            class="full-height">
            <el-col
              :span="12"
              class="full-height">
              <div
                class="chart-wrapper">

                <div class="chart-tit"> 拉速命中率(趋势图)</div>
                <div class="chart">
                  <line-chart2
                    ref="chart1"
                    :show-legend="true"
                    :unit="'%'"
                    :chart-data="lineData1.data1"
                    :x-data="lineData1.dataX"/>
                </div>
              </div>
            </el-col>
            <el-col
              :span="12"
              class="full-height">
              <div
                class="chart-wrapper">
                <div class="chart-tit"> 过热度命中率(趋势图)</div>
                <div class="chart">
                  <line-chart2
                    ref="chart1"
                    :show-legend="true"
                    :unit="'%'"
                    :chart-data="lineData2.data1"
                    :x-data="lineData2.dataX"/>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </screen-border>
    </div>

    <el-dialog
      :visible.sync="pieChart.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          成分不合格详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="pieChart.flawList"
        class="center-table"
        border>
        <el-table-column
          label="物料号"
          property="matId"/>
        <el-table-column
          label="钢种"
          property="steelGrade"/>
        <el-table-column
          label="不合格元素及含量"
          property="chemCd"/>
        <el-table-column
          label="规格"
          property="thk"/>
        <el-table-column
          label="处置"
          property="opinion"/>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  checklistBySetDate,
  checklistDelete,
  rollingParameters,
  steelMakingCompositionMax,
  steelMakingCompositionMin,
  steelMakingPhr,
  findAlarmCount
} from '@/api/screenTechnolagy'
import moment from 'moment'
import BarsChart from '@/pages/screen/technologyMeeting/component/bars-chart'
import LineChart2 from '@/pages/screen/technologyMeeting/component/line-chart2'
import { getChemQualified, getChemQualifiedDetail } from '@/api/screen'
import PieRateChart from '@/pages/screen/qualityMeeting/component/pie-rate-chart'
import BarsStackChart from '@/pages/screen/technologyMeeting/component/bars-stack-chart'
// import LineChart from '../../../../../desktop-web/components/chart/line-chart.vue'

export default {
  name: 'CraftHitSteelMaking',
  components: {
    BarsStackChart,
    PieRateChart,
    BarsChart,
    ScreenBorder,
    LineChart2
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      pieChart: {
        total: '',
        RATE: 0, //
        NUM: 0, //
        color: '#19be6b',
        dateType1: 0,
        flawList: [],
        dialogVisible: false
      },
      chartData: {
        bar1: [
          {
            name: '≥目标值炉数',
            color: '#86e597',
            label: 'top',
            data: []
          },
          {
            name: '＜目标值炉数',
            color: '#f5b544',
            label: 'top',
            data: []
          }
        ],
        bar1X: ['Mn', 'Nb', 'Ni', 'Cr', 'Cu', 'Mo', 'V']
      },
      lineData1: {
        dataX: [],
        data1: [
          {
            name: '铸机0',
            data: []
          },
          {
            name: '铸机1',
            data: []
          },
          {
            name: '铸机2',
            data: []
          },
          {
            name: '铸机3',
            data: []
          }
        ]
      },
      lineData2: {
        dataX: [],
        data1: [
          {
            name: '铸机0',
            data: []
          },
          {
            name: '铸机1',
            data: []
          },
          {
            name: '铸机2',
            data: []
          },
          {
            name: '铸机3',
            data: []
          }
        ]
      },
      chartData2: {
        bar1: [
          {
            name: '日违章炉数',
            data: []
          },
          {
            name: '月违章炉数',
            data: []
          }
        ],
        bar1X: ['LF静搅时间', 'RH静搅时间', '真空度', '真空时间']
      },
      chartData3: {
        bar1: [
          {
            name: '日违章块数',
            data: []
          },
          {
            name: '月违章块数',
            data: []
          }
        ],
        bar1X: ['中包吨位(0#)', '中包吨位(1、2、3#)', '拉速', '过热度']
      }
    }
  },
  computed: {
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'day')
        .format('yyyy-MM-DD')
    },
    nextDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(-1, 'day')
        .format('yyyy-MM-dd')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.loadData()
    }
  },
  destroyed() {},
  created() {
    this.cDate = this.selectDate
    this.loadData()
  },
  mounted() {},
  methods: {
    loadData() {
      this.getPartRate(
        moment(this.prevDate).format('yyyyMMDD'),
        moment(this.prevDate).format('yyyyMMDD')
      )
      this.getSteelMakingPhr()
    },
    getResentMonth() {
      return {
        startTime:
          this.$moment(this.cDate)
            .subtract(1, 'month')
            .format('yyyyMM') + '26',
        endTime: this.$moment(this.cDate).format('yyyyMMDD')
      }
    },
    // 成分合格率详情
    showRateDetail() {
      const date = {
        startTime: null,
        endTime: null
      }
      if (this.pieChart.dateType1 === 1) {
        Object.assign(date, this.getResentMonth())
      } else {
        date.startTime = this.$moment(this.prevDate).format('yyyyMMDD')
        date.endTime = this.$moment(this.prevDate).format('yyyyMMDD')
      }
      post(
        getChemQualifiedDetail +
          `?startTime=${date.startTime}&endTime=${date.endTime}`,
        {}
      ).then(res => {
        this.pieChart.flawList = res
        this.pieChart.dialogVisible = true
      })
    },
    // 成分合格率
    getPartRate(date1, date2) {
      post(getChemQualified + `?startTime=${date1}&endTime=${date2}`, {}).then(
        res => {
          this.pieChart.NUM = res.NUM
          this.pieChart.color = res.PASS_RATE >= 0.96 ? '#19be6b' : '#f32651'
          this.pieChart.RATE = (res.PASS_RATE * 100).toFixed(2)
        }
      )
    },
    // 成分合格率 月度
    changePieChart(type) {
      if (type === 1) {
        const dateObj = this.getResentMonth()
        this.getPartRate(dateObj.startTime, dateObj.endTime)
      } else {
        this.getPartRate(
          this.$moment(this.prevDate).format('yyyyMMDD'),
          this.$moment(this.prevDate).format('yyyyMMDD')
        )
      }
    },
    percentStringToNumber(percentArray) {
      return percentArray.map(percentStr => {
        if (typeof percentStr !== 'string' || !percentStr.endsWith('%')) {
          throw new Error('数组中的每个元素必须是一个以 "%" 结尾的字符串')
        }

        // 去掉百分号并转换为浮点数
        const percentWithoutSign = percentStr.slice(0, -1)
        const percentNumber = parseFloat(percentWithoutSign)
        return percentNumber
      })
    },
    // 获取炼钢工艺信息
    async getSteelMakingPhr() {
      const dateStr = this.$moment(this.prevDate).format('yyyyMMDD')
      // 工艺命中
      post(findAlarmCount, {
        setDate: this.cDate
      }).then(res => {
        this.lineData1.dataX = res.data.xDate
        this.lineData2.dataX = res.data.xDate
        this.lineData1.data1[0].data = this.percentStringToNumber(
          res.data.bf02.zhuji0
        )
        this.lineData1.data1[1].data = this.percentStringToNumber(
          res.data.bf02.zhuji1
        )
        this.lineData1.data1[2].data = this.percentStringToNumber(
          res.data.bf02.zhuji2
        )
        this.lineData1.data1[3].data = this.percentStringToNumber(
          res.data.bf02.zhuji3
        )
        this.lineData2.data1[0].data = this.percentStringToNumber(
          res.data.bf03.zhuji0
        )
        this.lineData2.data1[1].data = this.percentStringToNumber(
          res.data.bf03.zhuji1
        )
        this.lineData2.data1[2].data = this.percentStringToNumber(
          res.data.bf03.zhuji2
        )
        this.lineData2.data1[3].data = this.percentStringToNumber(
          res.data.bf03.zhuji3
        )
      })
      // post(steelMakingPhr, {
      //   beforeTime: dateStr,
      //   afterTime: dateStr
      // }).then(res => {
      //   this.chartData2.bar1[0].data = [
      //     res['LF静搅时间'],
      //     res['RH静搅时间'],
      //     res['真空度'],
      //     res['真空时间']
      //   ]
      //   this.chartData3.bar1[0].data = [
      //     res['0#连铸机中包吨位超限'],
      //     Number(res['#连铸机中包吨位超限']) -
      //       Number(res['0#连铸机中包吨位超限']),
      //     res['拉速'],
      //     res['过热度']
      //   ]
      // })
      // post(steelMakingPhr, {
      //   beforeTime: this.$moment(this.cDate)
      //     .startOf('month')
      //     .format('yyyyMMDD'),
      //   afterTime: this.$moment(this.prevDate).format('yyyyMMDD')
      // }).then(res => {
      //   this.chartData2.bar1[1].data = [
      //     res['LF静搅时间'],
      //     res['RH静搅时间'],
      //     res['真空度'],
      //     res['真空时间']
      //   ]
      //   this.chartData3.bar1[1].data = [
      //     res['0#连铸机中包吨位超限'],
      //     Number(res['#连铸机中包吨位超限']) -
      //       Number(res['0#连铸机中包吨位超限']),
      //     res['拉速'],
      //     res['过热度']
      //   ]
      // })

      // 目标命中
      const maxs = await post(steelMakingCompositionMax, {
        startTime: dateStr,
        endTime: dateStr
      })
      const mins = await post(steelMakingCompositionMin, {
        startTime: dateStr,
        endTime: dateStr
      })
      this.chartData.bar1[0].data = this.chartData.bar1X.map(item => {
        for (let i = 0; i < maxs.data.length; i++) {
          if (maxs.data[i].ELEMENT_CD == item) {
            return maxs.data[i].MAX
          }
        }
      })
      this.chartData.bar1[1].data = this.chartData.bar1X.map(item => {
        for (let i = 0; i < mins.data.length; i++) {
          if (mins.data[i].ELEMENT_CD == item) {
            return mins.data[i].MIN
          }
        }
      })
      console.log('min', mins.data)
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.edit-btn {
  margin: 0 3px;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .chart-tit {
    font-size: 16px;
    font-weight: bolder;
    color: #ffffff;
    line-height: 20px;
    margin: 10px 0;
    &:before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 6px;
      height: 100%;
      margin-right: 4px;
    }
  }
  .chart {
    flex: 1;
    height: 0;
  }
}
.dialog-item-tit {
  font-size: 18px;
  color: #dcdcdc;
  margin-bottom: 8px;
  margin-top: 15px;
}
</style>
