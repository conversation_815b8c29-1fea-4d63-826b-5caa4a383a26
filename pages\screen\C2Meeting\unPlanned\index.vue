<template>
  <div class="content">
    <div class="content-item">
      <screen-border-multi>
        <template v-slot:title>
          <div class="tabs-class">
            <div
              v-for="(item) in tabList"
              :key="item.id"
              :class="{'tab-pane-active': active === item.id}"
              class="tab-pane"
              @click="active = item.id">
              <div class="tab-pane-title-class">
                <div>{{ item.title }}</div>
                <div
                  v-if="active === item.id"
                  class="tab-pane-img">
                  <img
                    class="tab-pane-img2"
                    src="@/assets/images/screen/tab-pane-active-line2.png"
                    alt="">
                  <img
                    class="tab-pane-img1"
                    src="@/assets/images/screen/tab-pane-active-line.png"
                    alt="">
                </div>
              </div>
            </div>
          </div>
        </template>
        <custom-table-noheader4
          v-if="active === '1'"
          :title="'ERP综合非计划率'"
          :key="'productYes1'"
          :setting="tableObj1.setting"
          :url-list="tableObj1.url.list"
          :url-save="tableObj1.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader8
          v-if="active === '4'"
          :key="'productYes4Original'"
          :title="'原始非计划'"
          :setting="tableObj4.setting"
          :url-list="tableObj4.url.list"
          :url-save="tableObj4.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader10
          v-if="active === '2'"
          :key="'productYes2'"
          :title="'原钢种一次合格率'"
          :setting="tableObj2.setting"
          :url-list="tableObj2.url.list"
          :url-save="tableObj2.url.save"
          :select-date="selectDate"/>
        <Quality
          v-if="active === '8'"
          :select-date="selectDate"
          @dateChange="changeDate"/>
        <custom-table-noheader10
          v-if="active === '3'"
          :key="'productYes3'"
          :title="'剪切非计划率'"
          :setting="tableObj3.setting"
          :url-list="tableObj3.url.list"
          :url-save="tableObj3.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '4'"
          :key="'productYes4Summary'"
          :title="'生产作业情况汇总'"
          :setting="tableObj4.setting"
          :url-list="tableObj4.url.list"
          :url-save="tableObj4.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '5'"
          :key="'productYes5'"
          :title="'生产计划完成情况报表'"
          :setting="tableObj5.setting"
          :url-list="tableObj5.url.list"
          :url-save="tableObj5.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader15
          v-if="active === '12'"
          :key="'productYes12'"
          :title="'板材非计划指标综合'"
          :setting="tableObj12.setting"
          :url-list="tableObj12.url.list"
          :url-save="tableObj12.url.save"
          :select-date="selectDate"/>
        <template
          v-if="active === '6'">
          <el-table
            v-loading="loading"
            :data="deviceSetting.dataList"
            :size="'medium'"
            class="center-table font-big-table"
            border>
            <template
              v-for="(item, index) in deviceSetting.setting">
              <template v-if="item.show !== false">
                <el-table-column
                  v-if="item.children"
                  :key="index"
                  :width="item.width || ''"
                  :property="item.keySave"
                  :label="item.label"
                  :align="item.align">
                  <template
                    v-for="(cItem, cIndex) in item.children">
                    <template v-if="item.inputType === 'textarea'">
                      <el-table-column
                        :key="cIndex"
                        :width="cItem.width || ''"
                        :property="cItem.keySave"
                        :label="cItem.label"
                        :align="cItem.align">
                        <template v-slot="{ row }">
                          <div
                            slot="content"
                            v-html="formatText(row[cItem.keySave], cItem.split)"
                          />
                        </template>
                      </el-table-column>
                    </template>
                    <template v-else>
                      <el-table-column
                        :key="cIndex"
                        :width="cItem.width || ''"
                        :property="cItem.keySave"
                        :label="cItem.label"
                        :align="cItem.align"/>
                    </template>
                  </template>
                </el-table-column>
                <template v-else>
                  <el-table-column
                    v-if="item.type === 'index'"
                    :key="index"
                    :label="item.label"
                    type="index"
                    width="100"
                  />
                  <template v-else>
                    <template v-if="item.inputType === 'textarea'">
                      <el-table-column
                        :key="index"
                        :width="item.width || ''"
                        :property="item.keySave"
                        :label="item.label"
                        :align="item.align">
                        <template v-slot="{ row }">
                          <div
                            slot="content"
                            v-html="formatText(row[item.keySave], item.split)"
                          />
                        </template>
                      </el-table-column>
                    </template>
                    <template v-else>
                      <el-table-column
                        :key="index"
                        :width="item.width || ''"
                        :property="item.keySave"
                        :label="item.label"
                        :align="item.align"/>
                    </template>
                  </template>
                </template>
              </template>
            </template>
          </el-table>
        </template>
      </screen-border-multi>
    </div>
  </div>
</template>
 
 <script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import {
  qmsQualitySupervisionQuery,
  qmsQualitySupervisionSave
} from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/C2Meeting/component/custom-table'
import Quality from '@/pages/screen/C2Meeting/quality'
import CustomTableNoheader8 from '@/pages/screen/C2Meeting/component/custom-table-noheader8'
import {
  cutUnplannedRateFind,
  cutUnplannedRateSave,
  findEquipmentOperation,
  finishingShearingFind,
  finishingShearingSave,
  hotRollingSituationFind,
  hotRollingSituationSave,
  UnplannedIndicator,
  UnplannedIndicatorFind,
  PSCDayFind,
  PSCDaySave,
  unplaneRateFindAllBySetDate,
  unplaneRatesaveAll,
  steeleRatesaveAll,
  steeleRateFindAllBySetDate,
  QualityOriginalNonPlanFindAllBySetDate,
  QualityOriginalNonPlansaveAll
} from '@/api/screenC2'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import CustomTableNoheader from '@/pages/screen/C2Meeting/component/custom-table-noheader'
import CustomTableNoheader12 from '@/pages/screen/C2Meeting/component/custom-table-noheader12'
import CustomTableNoheader14 from '@/pages/screen/C2Meeting/component/custom-table-noheader14'
import CustomTableNoheader15 from '@/pages/screen/C2Meeting/component/custom-table-noheader15'
import CustomTableNoheader5 from '@/pages/screen/C2Meeting/component/custom-table-noheader5'
import { post } from '@/lib/Util'
import CustomTableNoheader4 from '@/pages/screen/C2Meeting/component/custom-table-noheader4'
import CustomTableNoheader10 from '@/pages/screen/C2Meeting/component/custom-table-noheader10'
import { expenseDetail } from '@/api/device'
export default {
  name: 'unPlanned',
  components: {
    CustomTableNoheader,
    ScreenBorderMulti,
    CustomTable,
    SingleBarsChart,
    Quality,
    CustomTableNoheader4,
    CustomTableNoheader8,
    CustomTableNoheader5,
    CustomTableNoheader10,
    CustomTableNoheader12,
    CustomTableNoheader14,
    CustomTableNoheader15
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      active: '1',
      loading: false,
      tabList: [
        {
          id: '1',
          active: true,
          title: 'ERP综合非计划率'
        },
        {
          id: '4',
          active: false,
          title: '原始非计划'
        },
        {
          id: '8',
          active: false,
          title: '原钢种工序一次合格率'
        },
        {
          id: '2',
          active: false,
          title: '原钢种一次合格率'
        },
        {
          id: '12',
          active: false,
          title: '板材非计划指标综合'
        }
      ],
      tableObj1: {
        url: {
          save: unplaneRatesaveAll,
          list: unplaneRateFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'target',
            keySave: 'target',
            label: '目标'
          },
          {
            keyQuery: 'rate',
            keySave: 'rate',
            label: '非计划率'
          },
          {
            keyQuery: 'unPlanned',
            keySave: 'unPlanned',
            label: '非计划总量'
          },
          {
            keyQuery: 'yield',
            keySave: 'yield',
            label: '产量（吨）'
          },
          {
            keyQuery: 'setDate',
            keySave: 'setDate',
            label: '晨会日期'
          }
        ]
      },
      tableObj2: {
        url: {
          save: steeleRatesaveAll,
          list: steeleRateFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'jzl',
            keySave: 'jzl',
            label: '矫直量'
          },
          {
            keyQuery: 'qgl',
            keySave: 'qgl',
            label: '切割量'
          },
          {
            keyQuery: 'xml',
            keySave: 'planCut',
            label: '修磨量'
          },
          {
            keyQuery: 'rclwjl',
            keySave: 'rclwjl',
            label: '热处理挽救'
          },
          {
            keyQuery: 'gpl',
            keySave: 'gpl',
            label: '改判量'
          },
          {
            keyQuery: 'xyl',
            keySave: 'xyl',
            type: 'textarea',
            label: '协议量'
          },
          {
            keyQuery: 'cpl',
            keySave: 'cpl',
            type: 'textarea',
            label: '次品量'
          },
          {
            keyQuery: 'fpl',
            keySave: 'fpl',
            type: 'textarea',
            label: '废品量'
          }
        ]
      },
      tableObj3: {
        url: {
          save: cutUnplannedRateSave,
          list: cutUnplannedRateFind
        },
        setting: [
          {
            keyQuery: 'project',
            keySave: 'project',
            label: '项目'
          },
          {
            keyQuery: 'situationday',
            keySave: 'situationDay',
            label: '当日情况'
          },
          {
            keyQuery: 'accumulatemonth',
            keySave: 'accumulateMonth',
            label: '本月累计'
          }
        ]
      },
      // tableObj4: {
      //   url: {
      //     save: productionSituationDaySave,
      //     list: productionSituationDayFind
      //   },
      //   setting: [
      //     {
      //       keyQuery: 'project',
      //       keySave: 'project',
      //       label: '项目'
      //     },
      //     {
      //       keyQuery: 'plan',
      //       keySave: 'plan',
      //       label: '计划'
      //     },
      //     {
      //       keyQuery: 'reality',
      //       keySave: 'reality',
      //       label: '实际'
      //     },
      //     {
      //       keyQuery: 'reason',
      //       keySave: 'reason',
      //       label: '未完成计划原因'
      //     }
      //   ]
      // },
      tableObj4: {
        url: {
          save: QualityOriginalNonPlansaveAll,
          list: QualityOriginalNonPlanFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'REASON',
            keySave: 'REASON',
            label: '余材原因'
          },
          {
            keyQuery: 'RESPONSIBLE_DEPARTMENT',
            keySave: 'RESPONSIBLE_DEPARTMENT',
            label: '责任单位'
          },
          {
            keyQuery: 'KS',
            keySave: 'KS',
            label: '块数'
          },
          {
            keyQuery: 'WEIGHT_TON',
            keySave: 'WEIGHT_TON',
            label: '吨位'
          }
        ]
      },
      tableObj5: {
        url: {
          save: PSCDaySave,
          list: PSCDayFind
        },
        setting: [
          {
            keyQuery: 'project',
            keySave: 'project',
            label: '项目'
          },
          {
            keyQuery: 'plan',
            keySave: 'plan',
            label: '计划'
          },
          {
            keyQuery: 'big',
            keySave: 'big',
            label: '大'
          },
          {
            keyQuery: 'white',
            keySave: 'white',
            label: '白'
          },
          {
            keyQuery: 'small',
            keySave: 'small',
            label: '小'
          },
          {
            keyQuery: 'amountto',
            keySave: 'amountTo',
            label: '合计'
          }
        ]
      },
      tableObj12: {
        url: {
          save: UnplannedIndicator,
          list: UnplannedIndicatorFind
        },
        setting: [
          {
            keyQuery: 'planAll',
            keySave: 'planAll',
            label: '平轧计划综合'
          },
          {
            keyQuery: 'reality',
            keySave: 'reality',
            label: '实绩'
          },
          {
            keyQuery: 'unplanned',
            keySave: 'unplanned',
            label: '非计划分类'
          },
          {
            keyQuery: 'unplannedItem',
            keySave: 'unplannedItem',
            label: '非计划细分项'
          },
          {
            keyQuery: 'itemIndicator',
            keySave: 'itemIndicator',
            label: '细分指标'
          },
          {
            keyQuery: 'itemReality',
            keySave: 'itemReality',
            // type: 'textarea',
            label: '细分实绩'
          }
        ]
      },
      deviceSetting: {
        url: {
          list: findEquipmentOperation
        },
        dataList: [],
        setting: [
          {
            keyQuery: 'T_DATE_FROM',
            keySave: 'T_DATE_FROM',
            label: '停机开始'
          },
          {
            keyQuery: 'T_DATE_TO',
            keySave: 'T_DATE_TO',
            label: '停机结束'
          },
          {
            keyQuery: 'TIMES',
            keySave: 'TIMES',
            label: '影响时间'
          },
          {
            keyQuery: 'FAULT_DESCRIPTION',
            keySave: 'FAULT_DESCRIPTION',
            label: '事故描述'
          },
          {
            keyQuery: 'CHG_GRD_DEP',
            keySave: 'CHG_GRD_DEP',
            label: '责任单位'
          }
        ]
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.loadData()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.loadData()
  },
  methods: {
    loadData() {
      post(this.deviceSetting.url.list, {
        startTime: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('YYYYMMDD'),
        endTime: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('YYYYMMDD')
      }).then(res => {
        this.deviceSetting.dataList = res.rows
      })
    }
  }
}
</script>
 
 <style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
