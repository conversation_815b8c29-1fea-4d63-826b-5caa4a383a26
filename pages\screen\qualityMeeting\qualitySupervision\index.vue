<template>
  <div class="content">
    <div class="content-item top">
      <custom-table
        :title="'质量监督'"
        :setting="steelMaking"
        :url-list="steelMakingUrl.list"
        :url-save="steelMakingUrl.save"
        :select-date="selectDate"/>
    </div>
    <div class="content-hold" />
    <div class="content-item">
      <custom-table
        :title="'重点跟踪及协调事项'"
        :setting="project"
        :url-list="projectUrl.list"
        :url-save="projectUrl.save"
        :select-date="selectDate"/>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import {
  findSuperviseProjectByDate,
  qmsQualitySupervisionQuery,
  qmsQualitySupervisionSave,
  saveSuperviseProject
} from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/qualityMeeting/component/custom-table'
export default {
  name: 'qualitySupervision',
  components: { CustomTable, SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      steelMakingUrl: {
        save: qmsQualitySupervisionSave,
        list: qmsQualitySupervisionQuery
      },
      steelMaking: [
        {
          keyQuery: 'factorytype',
          keySave: 'factoryType',
          label: '厂别',
          width: '130'
        },
        {
          keyQuery: 'processexecution',
          keySave: 'processExecution',
          label: '工艺执行'
        },
        {
          keyQuery: 'companycheck',
          keySave: 'companyCheck',
          label: '公司稽查'
        },
        {
          keyQuery: 'businessunitcheck',
          keySave: 'businessUnitCheck',
          label: '事业部稽查'
        },
        {
          keyQuery: 'batchqualityissues',
          keySave: 'batchQualityIssues',
          label: '批量质量问题'
        },
        {
          keyQuery: 'qualityaccident',
          keySave: 'qualityAccident',
          label: '质量事故'
        },
        {
          keyQuery: 'other',
          keySave: 'other',
          label: '钢板退判统计',
          inputType: 'textarea',
          split: '；'
        }
      ],
      projectUrl: {
        save: saveSuperviseProject,
        list: findSuperviseProjectByDate
      },
      project: [
        {
          keyQuery: 'project',
          keySave: 'project',
          label: '项目',
          width: '150'
        },
        {
          keyQuery: 'content',
          keySave: 'content',
          label: '检查内容',
          align: 'left',
          inputType: 'textarea'
        }
      ]
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  methods: {}
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
