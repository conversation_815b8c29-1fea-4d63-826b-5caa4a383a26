<template>
  <div class="container">
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="车间考核">
          <custom-table 
            ref="departmentAuditRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'departmentAudit'"
            :title="'车间考核'" 
            :setting="tableObj.setting" 
            :url-list="tableObj.url.list" 
            :url-save="tableObj.url.save"
            :select-date="selectDate" 
            :dialog-width="'50%'"
          />
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="车间质量履职情况">
          <custom-table 
            ref="acceptQualityDisputeRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'acceptQualityDispute'"
            :title="'车间质量履职情况'" 
            :setting="tableObj2.setting" 
            :url-list="tableObj2.url.list"
            :url-save="tableObj2.url.save" 
            :select-date="selectDate"
            :dialog-width="'80%'"
          />
        </screen-border>
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="相关方考核">
          <custom-table 
            ref="departmentAuditRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'departmentAudit'"
            :title="'相关方考核'" 
            :setting="tableObj3.setting" 
            :url-list="tableObj3.url.list" 
            :url-save="tableObj3.url.save"
            :select-date="selectDate" 
            :dialog-width="'50%'"
          />
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="离线探伤检查">
          <custom-table 
            ref="acceptQualityDisputeRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'acceptQualityDispute'"
            :title="'离线探伤检查'" 
            :setting="tableObj4.setting" 
            :url-list="tableObj4.url.list"
            :url-save="tableObj4.url.save" 
            :select-date="selectDate" 
            :dialog-width="'62%'"
          />
        </screen-border>
      </div>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityWeeklyReportScreen/components/screen-border.vue'
import CustomTable from '@/pages/screen/qualityWeeklyReportScreen/components/custom-table.vue'
import {
  workshopAssessmentFindAllDate,
  workshopAssessmentSaveAll,
  workshopQualityPerformanceFindAllDate,
  workshopQualityPerformanceSaveAll,
  stakeholderAssessmentFindAllDate,
  stakeholderAssessmentSaveAll,
  offlineFlawDetectionFindAllDate,
  offlineFlawDetectionSaveAll
} from '@/api/screen'

export default {
  name: 'InternalQualityManagement',
  components: {
    CustomTable,
    ScreenBorder
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      monthlyReport: '',
      tableObj: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'workshop',
            keySave: 'workshop',
            label: '车间',
            width: '285'
          },
          {
            keyQuery: 'checkType',
            keySave: 'checkType',
            label: '考核类型',
            width: '250'
          },
          {
            keyQuery: 'checkMoney',
            keySave: 'checkMoney',
            label: '本周考核金额',
            width: '250'
          }
        ],
        url: {
          list: workshopAssessmentFindAllDate,
          save: workshopAssessmentSaveAll
        }
      },
      tableObj2: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'workshop',
            keySave: 'workshop',
            label: '车间',
            width: '200'
          },
          {
            keyQuery: 'zlxcOneWeek',
            keySave: 'zlxcOneWeek',
            label: '质量巡查(第一周)',
            width: '200'
          },
          {
            keyQuery: 'xkbfOneWeek',
            keySave: 'xkbfOneWeek',
            label: '巡库帮扶(第一周)',
            width: '200'
          },
          {
            keyQuery: 'zlxcTwoWeek',
            keySave: 'zlxcTwoWeek',
            label: '质量巡查(第二周)',
            width: '200'
          },
          {
            keyQuery: 'xkbfTwoWeek',
            keySave: 'xkbfTwoWeek',
            label: '巡库帮扶(第二周)',
            width: '200'
          },
          {
            keyQuery: 'zlxcThreeWeek',
            keySave: 'zlxcThreeWeek',
            label: '质量巡查(第三周)',
            width: '200'
          },
          {
            keyQuery: 'xkbfThreeWeek',
            keySave: 'xkbfThreeWeek',
            label: '巡库帮扶(第三周)',
            width: '200'
          },
          {
            keyQuery: 'zlxcFourWeek',
            keySave: 'zlxcFourWeek',
            label: '质量巡查(第四周)',
            width: '200'
          },
          {
            keyQuery: 'xkbfFourWeek',
            keySave: 'xkbfFourWeek',
            label: '巡库帮扶(第四周)',
            width: '200'
          }
        ],
        url: {
          list: workshopQualityPerformanceFindAllDate,
          save: workshopQualityPerformanceSaveAll
        }
      },
      tableObj3: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'workshop',
            keySave: 'workshop',
            label: '车间',
            width: '250'
          },
          {
            keyQuery: 'problem',
            keySave: 'problem',
            label: '稽查问题',
            width: '250'
          },
          {
            keyQuery: 'checkMoney',
            keySave: 'checkMoney',
            label: '本周考核金额',
            width: '285'
          }
        ],
        url: {
          list: stakeholderAssessmentFindAllDate,
          save: stakeholderAssessmentSaveAll
        }
      },
      tableObj4: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'type',
            keySave: 'type',
            label: '分类',
            width: '100'
          },
          {
            keyQuery: 'onLine',
            keySave: 'onLine',
            label: '在线',
            width: '175'
          },
          {
            keyQuery: 'unqualified',
            keySave: 'unqualified',
            label: '不合格',
            width: '170'
          },
          {
            keyQuery: 'qualified',
            keySave: 'qualified',
            label: '合格',
            width: '170'
          },
          {
            keyQuery: 'countTotal',
            keySave: 'countTotal',
            label: '总计',
            width: '170'
          },
          {
            keyQuery: 'qualifiedRate',
            keySave: 'qualifiedRate',
            label: '合格率',
            width: '170'
          }
        ],
        url: {
          list: offlineFlawDetectionFindAllDate,
          save: offlineFlawDetectionSaveAll
        }
      }
    }
  },
  methods: {}
}
</script>

<style scoped lang="less">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: hidden;

  .chart-row {
    margin-bottom: 10px;
    height: 50%;
    flex-direction: column;
  }

  .chart-row,
  .table-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 10px;
    width: 100%;
  }

  .chart-box,
  .table-box {
    width: 50%;
    flex: 1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .chart-footer {
    margin: 0px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    padding: 5px 8px;
    background: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);
    width: 100%;
  }

  .chart-input {
    height: 100%;
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      height: 100%;
      padding: 10px;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
        border-color: transparent !important;
      }
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      border-color: transparent !important;
    }
  }

  /deep/ .el-table {
    background-color: transparent;
    width: 100% !important;
    table-layout: fixed;

    &::before {
      display: none;
    }

    th {
      background-color: rgba(31, 198, 255, 0.3);
      color: #fff;
      border-color: #1fc6ff;
    }

    td {
      background-color: transparent;
      color: #fff;
      border-color: #2e4262;
    }

    tr {
      background-color: transparent;
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-content {
    padding: 10px;
    box-sizing: border-box;
  }
}
</style>
