<template>
  <div class="content">
    <div class="content-item">
      <screen-border title="安全环保消防">
        <template v-slot:headerRight>
          <span
            v-command="'/screen/B1ProductionMeeting/edit'"
            class="screen-btn"
            @click="unfinished.dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            操作
          </span>
        </template>
        <div
          ref="table1"
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            :data="unfinished.showGridData"
            :max-height="maxHeight"
            class="font-table center-table"
            border>
            <el-table-column
              property="project"
              label="项目"
              width="160"/>
            <el-table-column
              property="description"
              label="情况说明"/>
            <el-table-column
              property="picture"
              label="图片">
              <template v-slot="{ row }">
                <template v-if="row.picture">
                  <ul class="el-upload-list el-upload-list--picture-card">
                    <li
                      v-for="(item) in getPictureList(row.picture)"
                      :key="item"
                      class="el-upload-list__item is-ready">
                      <img-view
                        :key="item"
                        :id="item"
                      />
                    </li>
                  </ul>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              property="notes"
              label="备注"/>
          </el-table>
        </div>
      </screen-border>
    </div>
    <!--热处理详情-->
    <el-dialog
      :visible.sync="unfinished.dialogVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="安全环保消防">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveUnfinished">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          安全环保消防
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="unfinished.gridData"
          :row-class-name="getRowClass"
          border>
          <el-table-column
            property="project"
            label="项目"
            width="160">
            <template v-slot="{ row }">
              <el-select
                :popper-append-to-body="false"
                v-model="row.project">
                <el-option
                  v-for="(item, index) in objectionList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            property="description"
            label="情况说明">
            <template v-slot="{ row }">
              <el-input v-model="row.description" />
            </template>
          </el-table-column>
          <el-table-column
            property="picture"
            label="图片">
            <template v-slot="{ row, $index }">
              <template v-if="row.picture">
                <ul class="el-upload-list el-upload-list--picture-card">
                  <li
                    v-for="(item, index) in getPictureList(row.picture)"
                    :key="index"
                    class="el-upload-list__item is-ready">
                    <img-view
                      :key="item"
                      :id="item"
                      deleteable
                      @img-delete="handleImgDelete($event, $index)"
                    />
                  </li>
                </ul>
              </template>
              <el-upload
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file, fileList) => handleChange(file, fileList, $index)"
                :show-file-list="false"
                :before-upload="beforeUpload"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = row.index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
          <el-table-column
            property="notes"
            label="备注">
            <template v-slot="{ row }">
              <el-input v-model="row.notes" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row }">
              <template v-if="row.type === 'picture'">
                {{ row.project }}
              </template>
              <el-select
                v-else
                :popper-append-to-body="false"
                v-model="row.project">
                <el-option
                  v-for="(item, index) in objectionList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
            </template>
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index , 'unfinished')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <br>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('unfinished')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { deleteFileByIds, uploadFile } from '@/api/system'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { qmsRollingsteelQualityControlQuery } from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ImgView from '@/components/ImgView'
import { ssefFind, ssefSave } from '@/api/screenB1Production'
export default {
  name: 'EnvFireProtect',
  components: { ImgView, SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      editIndex: 0,
      maxHeight: null,
      unfinished: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      unitList: ['吨数', '炉数'],
      objectionList: [
        '安全事故',
        '安全险肇事故',
        '现场违章情况',
        '环保设施运行',
        '环保事故',
        '消防事故'
      ]
    }
  },
  computed: {
    showTable: function() {
      const arr = []
      this.unfinished.gridData.forEach((item, index) => {
        arr.push(item)
        arr.push({
          index,
          project: (item.project || '') + '图片',
          type: 'picture',
          objectionNum: '',
          picture: item.picture
        })
      })
      return arr
    },
    showOutTable: function() {
      const arr = []
      this.unfinished.showGridData.forEach((item, index) => {
        arr.push(item)
        arr.push({
          index,
          project: (item.project || '') + '图片',
          type: 'picture',
          objectionNum: '',
          picture: item.picture
        })
      })
      return arr
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getUnfinished()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.maxHeight = this.$refs.table1.offsetHeight
  },
  methods: {
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          factoryType: 'A',
          checkNorm: 'B',
          sampleNorm: 'B',
          cleanerProduction: 'C',
          drift: 'D',
          repair: 'E',
          other: 'F'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unfinished.gridData = sheet
          .filter(item => item.setTime !== '日期')
          .map(item => {
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportunfinished() {
      const data = [
        {
          factoryType: '生产厂',
          checkNorm: '检验规范',
          sampleNorm: '取样规范',
          cleanerProduction: '清洁生产',
          drift: '瓢曲发生量',
          repair: '钢板修复挽救',
          other: '其他'
        }
      ].concat(
        _.cloneDeep(
          this.unfinished.gridData.map(item => {
            return {
              factoryType: item.factoryType,
              checkNorm: item.checkNorm,
              sampleNorm: item.sampleNorm,
              cleanerProduction: item.cleanerProduction,
              drift: item.drift,
              repair: item.repair,
              other: item.other
            }
          })
        )
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `质量异议（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    getRowClass(row) {
      if (row.row['type'] === 'picture') {
        return 'split-row'
      } else {
        return ''
      }
    },
    beforeUpload() {},
    httpRequest(params) {},
    async handleChange(file, fileList, index) {
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) {
        return this.$message.error('上传图片大小不能超过 5MB!')
      }
      const formData = new FormData()
      formData.append('files', file.raw)
      post(uploadFile, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        if (res.success) {
          this.$message.success('图片上传成功！')
          const obj = this.unfinished.gridData[index]
          const pictures = obj.picture ? obj.picture.split('|') : []
          pictures.push(res.data[0].id)
          this.unfinished.gridData.splice(
            index,
            1,
            Object.assign({}, obj, {
              picture: pictures.join('|')
            })
          )
          console.log(this.unfinished.gridData)
        } else {
          this.$message.warning('图片上传失败！')
          this.loading = false
        }
      })
    },
    getPictureList(picture) {
      return picture.split('|')
    },
    async handleImgDelete(file, index) {
      this.editIndex = index
      const del = await post(deleteFileByIds, { ids: [file.id] })
      if (del.success) {
        const obj = this.unfinished.gridData[index]
        this.unfinished.gridData.splice(
          this.editIndex,
          1,
          Object.assign({}, obj, {
            picture: obj.picture
              .split('|')
              .filter(item => item !== file.id)
              .join('|')
          })
        )
      }
    },
    getUnfinished() {
      post(ssefFind, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.$emit('statusChange', !res.data.length)
        if (!res.data.length) return
        this.unfinished.showGridData = res.data.map((item, index) => {
          return {
            index: index,
            project: item['project'],
            description: item.description,
            picture: item.picture,
            notes: item.notes
          }
        })
        this.unfinished.gridData = lodash.cloneDeep(
          this.unfinished.showGridData
        )
      })
    },
    saveUnfinished() {
      this.loading = true
      const params = {
        setDate: this.cDate,
        data: this.unfinished.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(ssefSave, params).then(res => {
        //
        this.loading = false
        if (res !== -1) {
          this.$message.success('保存成功！')
          unfinished.dialogVisible = false
          this.getUnfinished()
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
      if (row.type === 'picture') {
        if (columnIndex === 1) {
          return [1, 8]
        } else if (columnIndex > 1 && columnIndex < 9) {
          return [0, 0]
        }
      }
    },
    getMergeData(rowIndex, columnIndex) {
      const matchLeftTop = this.unfinished.gridMerge.find(
        item => item.s.c === columnIndex && item.s.r === rowIndex
      )
      if (matchLeftTop) {
        return [
          matchLeftTop.e.r - matchLeftTop.s.r + 1,
          matchLeftTop.e.c - matchLeftTop.s.c + 1
        ]
      }
      const merged = this.unfinished.gridMerge.find(item => {
        return (
          item.s.c < columnIndex &&
          columnIndex <= item.e.c &&
          item.s.r < rowIndex &&
          rowIndex <= item.e.r
        )
      })
      if (merged) {
        console.log(merged)
        return [0, 0]
      }
    },
    importUnfinishedData(date) {
      post(qmsRollingsteelQualityControlQuery, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.unfinished.gridData = res.map(item => {
          return {
            project: item['project'],
            description: item.description,
            picture: item.picture,
            notes: item.notes
          }
        })
        if (!res) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.scroll-wrapper {
  height: 100%;
  overflow: auto;
}
</style>
