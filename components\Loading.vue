<template>
  <div class="loading">
    <i class="el-icon-loading"/>
    <p>加载中...</p>
  </div>
</template>

<script>
export default {
  name: 'Loading'
}
</script>

<style scoped lang="less">
.loading {
  width: 100%;
  min-height: 80px;
  display: flex;
  align-items: center;
  flex-direction: column;
  color: #5e93ed;
  .el-icon-loading {
    font-size: 36px;
  }
  p {
    margin-top: 10px;
  }
}
</style>
