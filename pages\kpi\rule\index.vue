<template>
  <div class="page-content">
    <el-row
      :gutter="20"
      class="row-bg full-height"
      justify="start"
      type="flex"
    >
      <el-col
        :span="5"
        class="full-height"
      >
        <div class="tree-wrapper full-height shadow-light overflow-auto" >
          <el-tree
            :data="data"
            :load="loadNode"
            :props="defaultProps"
            highlight-current
            lazy
            node-key="id"
            @node-click="handleNodeClick"
          >
            <template
              v-slot="{node, data}">
              <span
                :class="{'first-node': node.level === 1}"
                class="custom-tree-node">
                <span>{{ node.label }}</span>
              </span>

            </template>
          </el-tree>
        </div>

      </el-col>
      <el-col
        :span="19"
        class="full-height overflow-auto"
      >
        <div class="page-operate">

          <div class="table-title">
            当前指标：{{ editKid.name || '暂未选择' }}
          </div>
          <div
            v-if="editKid.name"
            class="text-right">
            <el-button
              v-command="'/kpi/rule/add'"
              icon="el-icon-circle-plus-outline"
              size="small"
              type="success"
              @click="handleAdd"
            >新增
            </el-button>
          </div>
        </div>
        <div class="page-card shadow-light">
          <el-table
            v-loading="loading"
            :data="tableData"
            :size="size"
            border
            style="width: 100%"
          >
            <el-table-column
              label="序号"
              type="index"
              width="50"
            />
            <el-table-column
              label="规则类型"
              prop="ruleSign"
              min-width="100"
              show-overflow-tooltip
            >
              <template
                v-slot="{row}"
              >
                {{ getValue(ruleType, row.ruleSign).label }}
              </template>
            </el-table-column>
            <el-table-column
              label="预警逻辑"
              prop="logic"
              width="100"
            >
              <template
                v-slot="{row}"
              >
                {{ getValue(earlyWarningLogic, row.logic).label }}
              </template>
            </el-table-column>
            <el-table-column
              label="预警规则"
              prop="rule"
              width="120"
              show-overflow-tooltip
            >
              <template
                v-slot="{row}"
              >
                {{ getValue(earlyWarningRule, row.rule).label }}
              </template>
            </el-table-column>
            <el-table-column
              label="预警参数"
              prop="warningParam"
              width="100"
              show-overflow-tooltip
            >
              <template
                v-slot="{row}"
              >
                {{ row.warningParam }}
              </template>
            </el-table-column>
            <el-table-column
              label="比较值说明"
              prop="remarks"
              min-width="100"
              show-overflow-tooltip
            >
              <template
                v-slot="{row}"
              >
                {{ row.remarks }}
              </template>
            </el-table-column>
            <el-table-column
              label="目标值读取方式"
              prop="targetGetType"
              min-width="100"
              show-overflow-tooltip
            >
              <template
                v-slot="{row}"
              >
                {{ getValue(targetGetType, row.targetGetType).label }}
              </template>
            </el-table-column>
            <el-table-column
              label="关联目标ID"
              prop="linkedKpiId"
              width="100"
            >
              <template
                v-slot="{row}"
              >
                {{ row.linkedKpiId }}
              </template>
            </el-table-column>
            <el-table-column
              label="基础目标值"
              prop="basicValue"
              width="100"
            >
              <template
                v-slot="{row}"
              >
                {{ row.basicValue }}
              </template>
            </el-table-column>
            <el-table-column
              label="目标值倍数"
              prop="multiple"
              width="100"
            >
              <template
                v-slot="{row}"
              >
                {{ row.multiple }}
              </template>
            </el-table-column>
            <el-table-column
              label="是否核心指标"
              prop="multiple"
              width="100"
            >
              <template
                v-slot="{row}"
              >
                <el-tag
                  :type="row.isCoreRule ? 'success' : 'primary'"
                  disable-transitions
                >{{ row.isCoreRule ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="120"
            >
              <template
                v-slot="scope"
              >
                <span v-command="'/kpi/rule/edit'">
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(scope)"
                  >编辑
                  </el-button>
                  <el-divider
                    direction="vertical" />
                </span>
                <el-button
                  v-command="'/kpi/rule/delete'"
                  slot="reference"
                  type="text"
                  @click="handleDelete(scope.row)"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
    <RuleEdit
      ref="modalForm"
      :kid="editKid.id"
      :kpi-name="editKid.name"
      :rank="editKid.rank"
      :has-day-code="hasDayCode"
      @success="loadRules"
    />
  </div>
</template>

<script>
import listMixins from '@/mixins/ListMixins'
import { post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'
import {
  findBySpecification,
  findNextRank,
  findRulesByKid,
  kpiDelete
} from '@/api/kpi'
import RuleEdit from '@/pages/kpi/indicators/component/ruleEdit'
export default {
  name: 'kpi-indicators',
  components: {
    RuleEdit
  },
  mixins: [listMixins],
  data: () => {
    return {
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: findBySpecification, //分页接口地址
        delete: kpiDelete //删除接口地址
      },
      editUserId: null,
      data: [], // 树状数据
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      kpiFunction: ENUM.kpiFunction,
      factoryList: ENUM.factoryList,
      levelList: ENUM.levelList,
      statusList: [
        {
          value: 1,
          label: '正常',
          type: 'success'
        },
        {
          value: 0,
          label: '废弃',
          type: 'warning'
        }
      ],
      rightMenuVisible: false,
      rightMenuLeft: 0,
      rightMenuTop: 0,
      rightMenuData: null,
      editKid: {},
      hasDayCode: false, // 当前编辑状态是否提示日取值覆盖信息
      ruleType: ENUM.ruleType,
      earlyWarningLogic: ENUM.earlyWarningLogic,
      earlyWarningRule: ENUM.earlyWarningRule,
      targetGetType: ENUM.targetGetType
    }
  },
  watch: {
    rightMenuVisible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  created() {},
  methods: {
    async handleSearch(reset = false) {},
    getValue: function(list = [], value) {
      return list.find(item => item.value == value) || {}
    },
    async loadRootData() {
      //
      const list = this.kpiFunction.map(item => {
        item.id = 'kpi' + item.value
        item.name = item.label
        item.hasChild = true
        return item
      })
      return Promise.resolve(list)
    },
    async loadData(parentId) {
      const { data } = await post(findNextRank, { parentId: parentId })
      return Promise.resolve(data)
    },
    async loadNode(node, resolve) {
      let data = null
      if (node.level === 0) {
        data = await this.loadRootData()
      } else {
        const parentId = node.level === 1 ? 0 : node.data.id
        console.log(node)
        data = await this.loadData(parentId)
        if (node.level === 1) {
          data = data.filter(kpi => kpi.feature == node.data.value)
        }
      }
      // data.forEach(item => (item.isLeaf = !item.hasChild))
      resolve(data)
    },
    async handleNodeClick(data, node) {
      console.log(node)
      if (node.level === 1) return
      this.editKid = data
      this.loadRules()
    },
    loadRules() {
      this.loading = true
      post(findRulesByKid, { kid: this.editKid.id }).then(res => {
        this.tableData = res.data
        this.loading = false
      })
    },
    handleAdd: function() {
      this.hasDayCode = !!this.tableData.find(item => !!item.dayCode)
      this.$refs.modalForm.add()
      this.$refs.modalForm.visible = true
    },
    handleAddChild(node, data) {
      this.$refs.modalForm.add()
      this.$nextTick(() => {
        this.$refs.modalForm.formData.parentId = node.level === 1 ? 0 : data.id
        this.$refs.modalForm.formData.parentName = data.name
      })
      this.$refs.modalForm.visible = true
    },
    handleEdit: function(scope) {
      this.hasDayCode = !!this.tableData
        .filter((item, index) => {
          return index !== scope.$index
        })
        .find(item => !!item.dayCode)
      this.$refs.modalForm.edit(scope.row)
      this.$refs.modalForm.visible = true
    }
    // rightAddNext() {
    //   //
    //   console.log(this.rightMenuData)
    //   this.handleAddChild(this.rightMenuData, this.rightMenuData.data)
    // },
    // rightAddBrother() {
    //   //
    //   this.handleAddChild(
    //     this.rightMenuData.parent,
    //     this.rightMenuData.parent.data
    //   )
    // },
    // rightMod() {
    //   //
    //   this.handleEdit(this.rightMenuData.data)
    // },
    // rightDel() {
    //   //
    //   this.handleDelete(this.rightMenuData.data)
    // },
    // oncontextmenu(e, data, node) {
    //   //
    //   this.rightMenuTop = e.clientY
    //   this.rightMenuLeft = e.clientX
    //   this.rightMenuVisible = true
    //   this.rightMenuData = node
    // },
    // closeMenu(e) {
    //   this.rightMenuVisible = false
    //   this.rightMenuData = null
    // },
    // filterHandler(value, row, column) {
    //   console.log(value, row, column)
    //   const property = column['property']
    //   return row[property] === value
    // },
    // filterChange(i) {
    //   console.log(i)
    // }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}
.tree-wrapper {
  overflow: auto;
  padding: 10px;
  border: 1px solid #eee;
  background: #fff;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.first-node {
  font-size: 18px;
}
/deep/ .el-tree-node {
  margin: 5px 0;
}
/deep/ .el-tree > .el-tree-node {
  margin: 15px 0 12px;
}
.contextmenu {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  background: #fff;
  font-size: 14px;
  color: #666;
  border: 1px solid #e9e9e9;
  min-width: 250px;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
  li {
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #f4f4f5;
    }
  }
  li:last-child {
    border-top: 1px solid #e9e9e9;
  }
}
</style>
