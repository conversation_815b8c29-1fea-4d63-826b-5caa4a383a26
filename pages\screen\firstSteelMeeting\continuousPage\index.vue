<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <custom-table
            :title="'0#连铸机离线设备情况'"
            :key="'productYes1'"
            :setting="tableObj1.setting"
            :url-list="tableObj1.url.list"
            :url-save="tableObj1.url.save"
            :select-date="selectDate"/>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <custom-table
            :key="'productYes2'"
            :title="'1#连铸机离线设备情况'"
            :setting="tableObj1.setting"
            :url-list="tableObj2.url.list"
            :url-save="tableObj2.url.save"
            :select-date="selectDate"/>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <custom-table
            :title="'2#连铸机离线设备情况'"
            :key="'productYes1'"
            :setting="tableObj1.setting"
            :url-list="tableObj3.url.list"
            :url-save="tableObj3.url.save"
            :select-date="selectDate"/>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <custom-table
            :key="'productYes2'"
            :title="'3#连铸机离线设备情况'"
            :setting="tableObj1.setting"
            :url-list="tableObj4.url.list"
            :url-save="tableObj4.url.save"
            :select-date="selectDate"/>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import moment from 'moment'
import CustomTable from '@/pages/screen/firstSteelMeeting/component/custom-table2'
import {
  findAllBySetDateFour,
  findAllBySetDateOne,
  findAllBySetDateThree,
  findAllBySetDateTwo,
  saveAllFour,
  saveAllOne,
  saveAllThree,
  saveAllTwo
} from '@/api/firstMeeting'
export default {
  name: 'continuousPage',
  components: {
    CustomTable,
    ScreenBorderMulti
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      tableObj1: {
        url: {
          save: saveAllOne,
          list: findAllBySetDateOne
        },
        setting: [
          {
            keyQuery: 'name',
            keySave: 'name',
            label: '区域',
            class: 'red'
          },
          {
            keyQuery: 'alreadyRepaired',
            keySave: 'alreadyRepaired',
            label: '已修'
          },
          {
            keyQuery: 'underRepair',
            keySave: 'underRepair',
            label: '在/待修'
          },
          //  {
          //    keyQuery: 'steel',
          //    keySave: 'steel',
          //    label: '装机辊子过钢量'
          //  },
          //  {
          //    keyQuery: 'schedule',
          //    keySave: 'schedule',
          //    label: '设备修复进度'
          //  },
          {
            keyQuery: 'remarks',
            keySave: 'remarks',
            label: '备注'
          }
        ]
      },
      tableObj2: {
        url: {
          save: saveAllTwo,
          list: findAllBySetDateTwo
        },
        setting: []
      },
      tableObj3: {
        url: {
          save: saveAllThree,
          list: findAllBySetDateThree
        },
        setting: []
      },
      tableObj4: {
        url: {
          save: saveAllFour,
          list: findAllBySetDateFour
        },
        setting: []
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = moment(this.selectDate).format('YYYYMMDD')
      this.loadData()
    }
  },
  created() {
    this.cDate = moment(this.selectDate).format('YYYYMMDD')
    this.loadData()
  },
  methods: {
    loadData() {}
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
