<template>
  <!--订单详情查询-->
  <div>
    <el-dialog
      :title="'订单详情'"
      :width="'1400px'"
      :close-on-click-modal="false"
      :visible.sync="orderDetailVisible"
      @close="close()">
      <div class="table">
        <el-table
          :data="orderProductionData"
          border
          style="width: 100%">
          <el-table-column
            type="index"
            label="序号"/>
          <el-table-column
            prop="owfid"
            label="订单项次"/>
          <el-table-column
            prop="steelType"
            label="钢种"/>
          <el-table-column
            prop="standardNo"
            label="标准号"/>
          <el-table-column
            prop="otherSteelType"
            label="其他钢种"/>
          <el-table-column
            prop="dealResult"
            label="保性能"
            width="80">
            <template v-slot="{ row }">
              <el-tag
                v-if="row.matrFl !== null"
                :type="getDict(row.matrFl, 'matrFlList').type"
                disable-transitions>{{ getDict(row.matrFl, 'matrFlList').label }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="thickness"
            label="厚度"/>
          <el-table-column
            prop="width"
            label="宽度"/>
          <el-table-column
            prop="logs"
            label="调整记录"/>
          <el-table-column
            prop="createUserNo"
            label="修改人"/>
          <el-table-column
            prop="createDateTime"
            label="修改日期"/>
        </el-table>
        <br>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="orderProductionForm.page"
            :page-size="orderProductionForm.size"
            :page-sizes="[10, 20, 30, 40]"
            :total="orderProductionForm.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import {
  deleteOrders,
  findDetailedOrders,
  findLogs,
  findMachineOutputByConditions,
  findOrders,
  saveOrder,
  updateOrder
} from '@/api/kpi'
import { ENUM } from '@/lib/Constant'

export default {
  name: 'orderLogDetail',
  // eslint-disable-next-line vue/require-prop-types
  props: ['factory', 'period', 'orderType'],
  data: () => {
    return {
      searchForm: {},
      matchType: ENUM.matchType,
      // 性能要求列表
      matrFlList: ENUM.matrFlList,
      orderProductionData: [],
      multipleSelection: [],
      orderDetailVisible: false,
      orderProductionForm: {
        newOrder: false,
        handleDateRange: null,
        page: 1,
        size: 10,
        total: 0
      },
      factoryList: ENUM.factoryListForecast,
      orderState: '0',
      orderMachineOutputVisible: false, // 机时产量查询
      orderMachineOutputForm: {
        searchForm: {
          standardNo: '',
          steelType: ''
        },
        data: [],
        page: 1,
        size: 10,
        total: 0,
        edit: {}
      }
    }
  },
  watch: {
    orderDetailVisible: function() {
      if (this.orderDetailVisible) {
        this.getOrders(true)
      }
    }
  },
  methods: {
    close() {
      this.searchForm = {}
      this.$emit('update', true)
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.orderProductionForm.size = val
      this.getOrders()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.orderProductionForm.page = val
      this.getOrders()
    },
    getOrders(reset = false) {
      if (reset) {
        this.orderProductionForm.page = 1
        this.orderProductionForm.size = 10
      }
      //orderType： 手持： 1， 手补：2， 中间品：3  历史所有： 0
      post(
        findLogs,
        Object.assign({}, this.searchForm, {
          period: this.period,
          factory: this.factory,
          page: this.orderProductionForm.page - 1,
          size: this.orderProductionForm.size
        })
      ).then(res => {
        this.orderProductionData = res.data.content || []
        this.orderProductionForm.total = res.data.totalElements
        // this.getMachineProduction(name)
      })
    },

    getDict(value, list) {
      const match = this[list].find(item => item.value == value)
      return match ? match : {}
    },
    // 更新订单
    userDefChange(e, row) {
      if (!row.orderWeight || !row.userDefMachineOutput) return
      row.orderState = 0
      post(updateOrder, {
        id: row.id,
        newWeight: row.orderWeight,
        newUserDefMachineOutput: row.userDefMachineOutput
      }).then(res => {
        if (res.success) {
          this.$message.success('保存订单成功')
          this.$emit('modify', res.data)
          this.getOrders()
        }
      })
    },
    // 更新订单
    saveOrderFu(e, row) {
      row.orderState = 0
      post(saveOrder, row).then(res => {
        this.$message.success('保存订单成功')
        this.getOrders()
      })
    },
    // 批量删除
    handleDelete() {
      if (!this.multipleSelection.length)
        return this.$message.warning('请先选择订单！')
      // /productionForecast/deleteOrders
      post(deleteOrders, this.multipleSelection.map(item => item.id)).then(
        res => {
          if (res.success) {
            this.$message.success('批量删除成功')
            this.getOrders()
          } else {
            this.$message.warning('批量删除失败')
          }
        }
      )
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleTabClick() {
      //
      this.getOrders(true)
    },

    orderMachineOutput(row = {}) {
      this.orderMachineOutputForm.edit = row.id ? row : {}
      this.orderMachineOutputForm.searchForm.steelType = row.id
        ? row.steelType
        : ''
      this.orderMachineOutputForm.searchForm.matrFl = row.id ? row.matrFl : ''
      this.orderMachineOutputForm.searchForm.factory = row.id ? row.factory : ''
      this.orderMachineOutputVisible = true
      this.getOrderMachineOutput(true)
    },

    getOrderMachineOutput(reset = false) {
      if (reset) {
        this.orderMachineOutputForm.page = 1
        this.orderMachineOutputForm.size = 10
      }
      post(
        findMachineOutputByConditions,
        Object.assign({}, this.orderMachineOutputForm.searchForm, {
          page: this.orderMachineOutputForm.page - 1,
          size: this.orderMachineOutputForm.size
        })
      ).then(res => {
        this.orderMachineOutputForm.data = res.data.content || []
        this.orderMachineOutputForm.total = res.data.totalElements
        // this.getMachineProduction(name)
      })
    },
    handleMachineSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.orderMachineOutputForm.size = val
      this.getOrderMachineOutput()
    },
    handleMachineCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.orderMachineOutputForm.page = val
      this.getOrderMachineOutput()
    },

    // 快捷设置机时产量
    setMachineOutput(num) {
      if (this.orderMachineOutputForm.edit.id) {
        this.orderMachineOutputForm.edit.userDefMachineOutput = num
        this.userDefChange({}, this.orderMachineOutputForm.edit)
        this.orderMachineOutputVisible = false
      }
    }
  }
}
</script>

<style scoped lang="less">
.point {
  display: inline-block;
  height: 22px;
  width: 22px;
  background: #b5e61d;
  border: 3px solid #28b349;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 5px;
  &.red {
    background: #ff7f27;
    border: 3px solid #ed1c24;
  }
}
.input-output {
  display: inline-block;
  width: 130px;
  vertical-align: middle;
}
</style>
