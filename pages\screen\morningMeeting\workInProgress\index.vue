<template>
  <div class="content">
    <div class="content-item top">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <screen-border :title="'库存情况（在制品） ' + searchTime.format('yyyy年MM月')">
            <template v-slot:headerRight>
              <span
                class="screen-btn"
                @click="stock2.detailVisible = true">
                详情
              </span>
            </template>
            <div class="chart-wrapper">
              <div
                class="chart">
                <stock-line-chart
                  :last-month-data="stock2.processedLastMonth || '0'"
                  :month-plan-data="stock2.processedMonthPlan || '0'"
                  :show-legend="true"
                  :chart-data="stock2.bar1"
                  :x-data="stock2.barX"
                  code="1"
                  @setDate="setDate"/></div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <screen-border :title="'在制品统计'">
            <template v-slot:headerRight/>
            <div
              ref="table1"
              class="chart-wrapper">
              <el-table
                v-loading="loading"
                ref="tableTag"
                :data="stockList"
                :max-height="stockHeight"
                show-summary
                border>
                <el-table-column
                  align="center"
                  property="PLT"
                  label="工厂"
                  width="60"/>
                <el-table-column
                  align="center"
                  property="D_STATUS"
                  label="订单材">
                  <el-table-column
                    align="center"
                    property="DDC_D"
                    label="D状态"/>
                  <el-table-column
                    align="center"
                    property="DDC_Q"
                    label="Q状态"/>
                  <el-table-column
                    align="center"
                    property="DDC_QAH"
                    label="QAH状态"/>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="余材">
                  <el-table-column
                    align="center"
                    property="YC_D"
                    label="D状态"/>
                  <el-table-column
                    align="center"
                    property="YC_Q"
                    label="Q状态"/>
                </el-table-column>
                <el-table-column
                  align="center"
                  property="CGB"
                  label="CGB"/>
                <el-table-column
                  align="center"
                  property="WGC"
                  label="外购"/>
               
                <el-table-column
                  align="center"
                  property="FHB"
                  label="复合板"/>
                <el-table-column
                  align="center"
                  property="ZNGC"
                  label="智能工厂"/>
                <el-table-column
                  align="center"
                  property="OVER_D"
                  label="超1个月D"/>
                <el-table-column
                  align="center"
                  property="total"
                  label="累计"/>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <screen-border title="日均处置跟踪（各工序完成情况）">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/morningMeeting/edit'"
                class="screen-btn"
                @click="processed.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="chart-wrapper">
              <div class="chart">
                <processed-bars-chart
                  :show-legend="false"
                  :chart-data="processed.bar1"
                  :chart-data2="processed.bar2"
                  :x-data="processed.barX"/>
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div
      v-if="false"
      class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <screen-border title="昨日后道工序作业率">
            <!--            <template v-slot:headerRight>-->
            <!--              <span-->
            <!--                v-command="'/screen/morningMeeting/edit'"-->
            <!--                class="screen-btn"-->
            <!--                @click="processed.dialogVisible = true">-->
            <!--                <el-icon class="el-icon-edit-outline"/>-->
            <!--                操作-->
            <!--              </span>-->
            <!--            </template>-->
            <div class="chart-wrapper">
              <div class="chart">
                <div style="position: absolute; top: 3px; right: 7px; display: flex;justify-content: right;align-items: center;margin-left: 100px">
                  <div
                    :style="'background-color: #19BE6B'"
                    style="width: 20px;height: 12px;border-radius: 5px"/>
                  <span style="font-weight: 400;font-size: 12px; margin-left: 4px;margin-right: 5px">运行</span>
                  <div
                    :style="'background-color: #FF2855'"
                    style="width: 20px;height: 12px;border-radius: 5px"/>
                  <span style="font-weight: 400;font-size: 12px; margin-left: 4px;margin-right: 5px">停机</span>
                  <div
                    :style="'background-color: #8b8589'"
                    style="width: 20px;height: 12px;border-radius: 5px"/>
                  <span style="font-weight: 400;font-size: 12px; margin-left: 4px;margin-right: 5px">无状态</span>
                </div>
                <point-bars-chart
                  :show-legend="false"
                  :chart-data="divDaily.bar1"
                  :chart-data2="divDaily.bar2"
                  :x-data="divDaily.barX"/>
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <!--在制品详情-->
    <el-dialog
      :visible.sync="stock2.detailVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="在制品库存详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          在制品库存详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="stock2.gridData"
        show-summary
        border>
        <el-table-column
          align="center"
          property="PLT"
          label=""/>
        <el-table-column
          align="center"
          property="D_STATUS"
          label="D状态"/>
        <el-table-column
          align="center"
          property="Q_STATUS"
          label="Q状态"/>
        <el-table-column
          align="center"
          property="QAH_STATUS"
          label="QAH状态"/>
        <el-table-column
          align="center"
          property="CGB_STATUS"
          label="CGB"/>
        <el-table-column
          align="center"
          property="WG_STATUS"
          label="外购"/>
        <el-table-column
          align="center"
          property="FHB_STATUS"
          label="复合板"/>
        <el-table-column
          align="center"
          property="ZNGC"
          label="智能工厂"/>
        <el-table-column
          align="center"
          property="JR_TOTAL"
          label="今日累计"/>
        <el-table-column
          align="center"
          property="ZR_TOTAL"
          label="昨日累计"/>
        <el-table-column
          align="center"
          property="DY_TOTAL"
          label="当月计划"/>
        <el-table-column
          align="center"
          property="LAST_WGT"
          label="上月月底实绩"/>
      </el-table>
    </el-dialog>
    <!--在制品-->
    <el-dialog
      :visible.sync="processed.dialogVisible"
      :width="'1300px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="在制品">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="SyncData(wipInventoryTrackingTask)">
              手动同步数据
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('processed')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importProcessedData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handleProcessedPreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportProcessed">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveProcessed">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          在制品库存及日均处置跟踪（各工序完成情况）
        </div>
      </template>
      <el-form
        v-loading="syncLoading"
        :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="processed.gridData"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="50"/>
          <el-table-column
            property="factoryType"
            label="厂别"
            width="80">
            <template v-slot="{ row }">
              <el-input v-model="row.factoryType" />
            </template>
          </el-table-column>
          <el-table-column
            property="plan"
            label="生产工序">
            <template v-slot="{ row }">
              <el-input v-model="row.productionProcess" />
            </template>
          </el-table-column>
          <el-table-column
            property="plan"
            label="工序日均处置量达标线">
            <template v-slot="{ row }">
              <el-input
                v-model="row.targetLine"/>
            </template>
          </el-table-column>
          <el-table-column
            property="plan"
            label="库存">
            <template v-slot="{ row }">
              <el-input
                v-model="row.inventory"
                :class="{'input-green': 1}" />
            </template>
          </el-table-column>
          <el-table-column
            property="reality"
            label="昨日处置量">
            <template v-slot="{ row }">
              <el-input
                v-model="row.yesterdayDisposal"
                :class="{'input-green': 1}" />
            </template>
          </el-table-column>
          <el-table-column
            property="reality"
            label="是否达标">
            <template v-slot="{ row }">
              <el-select
                :popper-append-to-body="false"
                v-model="row.qualify">
                <el-option
                  v-for="(item, index) in finishList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column
            property="reality"
            label="昨日处块数">
            <template v-slot="{ row }">
              <el-input
                v-model="row.yesterdayBlocks"
                :class="{'input-green': 1}" />
            </template>
          </el-table-column>
          <el-table-column
            property="complete"
            label="说明">
            <template v-slot="{ row }">
              <el-input v-model="row.description" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index, 'processed')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEdit"
          class="screen-btn"
          @click="addGridData('processed')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
    <!--曲线在制品库存详情-->
    <el-dialog
      :visible.sync="OnLineVisible"
      :close-on-click-modal="false"
      width="90%"
      top="5vh"
      class="screen-dialog"
      title="在制品库存详情">
      <template v-slot:title>
        <div class="bnt_flex">
          <div class="custom-dialog-title">
            在制品库存详情
          </div>
          <div>
            <span
              class="screen-btn"
              @click="exportTable">
              导出
            </span>
            <span
              v-show="lineTabelData.length!=0"
              class="screen-btn"
              @click="saveInventory">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
        </div>
      </template>
      <el-table
        v-loading="loading"
        id="table1"
        :data="lineTabelData"
        height="700"
        border>
        <el-table-column
          align="center"
          property="FACTORY"
          label="厂别"
          fixed="left"/>
        <el-table-column
          align="center"
          width="200"
          property="PRODUCTPRO"
          label="生产工序"
          fixed="left"/>
        <el-table-column
          align="center"
          property="MIDDLE_FJ"
          label="月份最高库存分解"
          fixed="left"/>
        <el-table-column
          align="center"
          property="LAST_FJ"
          label="月底指标分解"
          fixed="left"/>
        <el-table-column
          v-for="(item,index) in Days"
          :key = "index"
          :property="item>9?'DCLL_WGT'+ item:'DCLL_WGT0'+item"
          :label="'库存'+item"
          width="100"
          align="center"/>
        <el-table-column
          label="前日实际"
          align="center">
          <el-table-column
            v-for="(item,index) in Days"
            :key = "index"
            :property="item>9?'CLL_WGT'+ item:'CLL_WGT0'+item"
            :label="item+'日'"
            align="center"/>
        </el-table-column>
        <el-table-column
          align="center"
          property="GX_DBL"
          label="工序达标率"
          fixed="right"
          width="120">
          <template v-slot="{ row }">
            <el-input
              v-model="row.GX_DBL"
              :class="{'input-green': 1}" />
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          property="RJDB_CNT"
          label="工序日均处理量达标率"
          fixed="right"
          width="120">
          <template v-slot="{ row }">
            <el-input
              v-model="row.RJDB_CNT"
              :class="{'input-green': 1}" />
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          property="DOWN_YQ"
          label="降库要求"
          fixed="right"
          width="180">
          <template v-slot="{ row }">
            <el-input
              v-model="row.DOWN_YQ"
              :class="{'input-green': 1}" />
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          property="ZR_EMP"
          label="责任人"
          fixed="right"
          width="120">
          <template v-slot="{ row }">
            <el-input
              v-model="row.ZR_EMP"
              :class="{'input-green': 1}" />
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { saveAs } from 'file-saver'
import { batchUpdateResource } from '@/api/system'
import { post } from '@/lib/Util'
import * as _ from 'lodash'
import {
  findBlankStock,
  findBlankStockDetails,
  findBlankStockPltZj,
  findBoardParameterByDateAndPara,
  findDivDailyRate,
  findEQStatus,
  findHeatTreatmentYieldByDate,
  findSteelOutputByDate,
  findWipInventory,
  findWipInventoryByDate,
  findWipInventoryPltZj,
  findWipInventoryTrackingByDate,
  findWorkDetail,
  findZrTotal,
  saveBoardParameter,
  saveSteelOutput,
  saveWipInventoryTracking,
  wipInventoryTrackingTask,
  LINEBYDATE,
  SAVE_ALL,
  ydayCutDisposeAmountQuery
} from '@/api/screen'
import { math } from '@/lib/Math'
import ProcessedBarsChart from '@/pages/screen/morningMeeting/component/processed-bars-chart'
import StockLineChart from '@/pages/screen/morningMeeting/component/stock-line-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import PointBarsChart from '@/pages/screen/morningMeeting/component/point-bars-chart'
import moment from 'moment'
export default {
  name: 'WorkInProgress',
  components: {
    PointBarsChart,
    StockLineChart,
    ProcessedBarsChart,
    SingleBarsChart,
    SteelBarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      wipInventoryTrackingTask: wipInventoryTrackingTask,
      stock2: {
        bar1: [],
        barX: [],
        gridData: [],
        detailVisible: false,
        processedMonthPlan: '',
        processedLastMonth: ''
      },
      stockList: [],
      stockHeight: null,
      formInline: {
        processedMonthPlan: '',
        processedLastMonth: '',
        blankMonthPlan: '',
        blankLastMonth: ''
      },
      processed: {
        bar1: [],
        bar2: [],
        barX: [],
        gridData: [],
        gridMerge: [],
        dialogVisible: false,
        output: 0,
        targetSchedule: 0,
        percent: 0
      },
      divDaily: {
        bar1: [],
        bar2: [],
        barX: []
      },
      yescutquery: '',
      finishList: ['是', '否'],
      //曲线在制品库存情况弹框
      OnLineVisible: false,
      lineTabelData: [],
      Days: '',
      Times: ''
    }
  },
  computed: {
    searchTime: function() {
      return this.$moment(this.cDate).subtract(1, 'day')
    },
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'day')
        .format('yyyy-MM-DD')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getDetail()
      this.getZZP()
      this.getProcessed()
      this.getDivDailyRate()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
    // this.ydayCutDisposeAmountQuery()
  },
  methods: {
    //点击库存情况（在制品）曲线打开弹框
    setDate(val) {
      this.OnLineVisible = true

      this.Days = new Date(val.substr(0, 4), val.substr(4, 2), 0).getDate()
      this.Times =
        val.substr(0, 4) + '-' + val.substr(4, 2) + '-' + val.substr(6, 2)
      this.getLineWipInventory()
    },
    //获取曲线在制品库存详情
    async getLineWipInventory(val) {
      this.lineTabelData = []

      let res = await post(LINEBYDATE, {
        setDate: this.Times
      })
      console.log('数据', res)
      if (res && res.data.length != 0) {
        this.lineTabelData = res.data
      }
    },

    //保存曲线在制品库存详情
    async saveInventory() {
      let res = await post(SAVE_ALL, {
        setDate: this.Times,
        data: this.lineTabelData
      })
      console.log(res)
      if (res.status == 1) {
        this.$message.success('保存成功')
        this.getLineWipInventory()
      }
    },

    calculateHeight() {
      this.stockHeight = this.$refs.table1.offsetHeight
    },
    getDetail() {
      post(findWipInventory, {
        date: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyyMMDD')
      }).then(res => {
        this.stock2.gridData = res.data
      })
    },
    async getZZP() {
      const monthStart = this.$moment(this.cDate).format('D') == 2
      // 参数
      const parameters = await post(findBoardParameterByDateAndPara, {
        setDate: this.searchTime.format('yyyy-MM')
      })
      this.formInline.processedMonthPlan = this.stock2.processedMonthPlan = this.getParam(
        'processedMonthPlan',
        parameters.data
      )
      this.formInline.processedLastMonth = this.stock2.processedLastMonth = this.getParam(
        'processedLastMonth',
        parameters.data
      )
      // 在制品
      const zrTotal = await post(findZrTotal, {
        date: this.searchTime.format('yyyyMM')
      })
      const zrTotal2 = monthStart
        ? await post(findZrTotal, {
            date: this.$moment(this.cDate).format('yyyyMM')
          })
        : { data: [] }
      const pltZj = await post(findWipInventoryPltZj, {
        zxDate: this.searchTime.format('yyyyMM')
      })
      const pltZj2 = monthStart
        ? await post(findWipInventoryPltZj, {
            zxDate: this.$moment(this.cDate).format('yyyyMM')
          })
        : { data: [] }
      this.stock2.barX = zrTotal
        .concat(zrTotal2)
        .filter(item => item.PLT === 'C1')
        .map(item => item.ZX_DATE.slice(6, 8) + '日')
      // .substr(4, 4)
      //  .map(item => item))
      let obj = pltZj.rows
        .concat(pltZj2.rows)
        .slice(0, pltZj.rows.concat(pltZj2.rows).length - 1)
      let chartData = [
        {
          name: '总计',
          data: obj.map(item => parseInt(item.总计))
        },
        {
          name: 'C1',
          data: zrTotal
            .concat(zrTotal2)
            .filter(item => item.PLT === 'C1')
            .map(item => parseInt(item.ZR_TOTAL))
        },
        {
          name: 'C2',
          data: zrTotal
            .concat(zrTotal2)
            .filter(item => item.PLT === 'C2')
            .map(item => parseInt(item.ZR_TOTAL))
        },
        {
          name: 'C3',
          data: zrTotal
            .concat(zrTotal2)
            .filter(item => item.PLT === 'C3')
            .map(item => parseInt(item.ZR_TOTAL))
        },
        {
          name: 'QAB',
          hidden: true,
          data: obj.map(item => parseInt(item.Q_STATUS))
        }
      ]

      // 找出最长的数据数组长度
      const maxLength = Math.max(...chartData.map(item => item.data.length))

      // 确保所有数据数组长度一致
      this.stock2.bar1 = chartData.map(item => {
        // 如果长度不足，用最后一个值填充
        if (item.data.length < maxLength) {
          const lastValue = item.data[item.data.length - 1]
          while (item.data.length < maxLength) {
            item.data.push(lastValue)
          }
        }
        return item
      })

      // 在制品统计
      const zrTj = await post(findWorkDetail, {
        setDate: this.selectDate
      })
      this.stockList = zrTj.data.map(item => {
        item.DDC_D && (item.DDC_D = Number(item.DDC_D).toFixed(0))
        item.DDC_Q && (item.DDC_Q = Number(item.DDC_Q).toFixed(0))
        item.DDC_QAH && (item.DDC_QAH = Number(item.DDC_QAH).toFixed(0))
        item.FHB && (item.FHB = Number(item.FHB).toFixed(0))
        item.WGC && (item.WGC = Number(item.WGC).toFixed(0))
        item.YC_D && (item.YC_D = Number(item.YC_D).toFixed(0))
        item.YC_Q && (item.YC_Q = Number(item.YC_Q).toFixed(0))
        item.CGB && (item.CGB = Number(item.CGB).toFixed(0))
        item.ZNGC && (item.ZNGC = Number(item.ZNGC).toFixed(0))
        item.total = math.add(
          item.DDC_D || 0,
          item.DDC_Q || 0,
          item.DDC_QAH || 0,
          item.FHB || 0,
          item.WGC || 0,
          item.YC_D || 0,
          item.YC_Q || 0,
          item.ZNGC || 0,
          item.CGB || 0
        )
        return item
      })
      this.$nextTick(() => {
        this.$refs.tableTag.doLayout()
      })
    },
    handleProcessedPreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          factoryType: 'B',
          productionProcess: 'C',
          targetLine: 'D',
          inventory: 'E',
          yesterdayDisposal: 'F',
          qualify: 'G',
          description: 'H'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.processed.gridData = sheet
        this.$message.success('解析成功！')
      })
    },
    exportProcessed() {
      const data = [
        {
          tableName: '在制品库存及日均处置跟踪',
          factoryType: '厂别',
          productionProcess: '生产工序',
          targetLine: '工序日均处置量达标线',
          inventory: '库存',
          yesterdayDisposal: '昨日处置量',
          qualify: '是否达标',
          yesterdayBlocks: '昨日处置块数',
          description: '说明'
        }
      ].concat(
        this.processed.gridData.map(item => {
          return {
            tableName: '',
            factoryType: item.factoryType,
            productionProcess: item.productionProcess,
            targetLine: item.targetLine,
            inventory: item.inventory,
            yesterdayDisposal: item.yesterdayDisposal,
            qualify: item.qualify,
            yesterdayBlocks: item.yesterdayBlocks,
            description: item.description
          }
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `在制品库存及日均处置跟踪（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    //  ydayCutDisposeAmountQuery() {
    //    post(ydayCutDisposeAmountQuery).then(res => {
    //      if (res) {
    //        this.yescutquery = res.data.rows[0].ZRCLL
    //      }
    //    })
    //  },
    getProcessed() {
      post(findWipInventoryTrackingByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        //   res.data[0].yesterdaydisposal = this.yescutquery.split('/')[1]
        //   res.data[0].yesterdayblocks = this.yescutquery.split('/')[0]
        this.processed.gridData = res.data.map(item => {
          return {
            factoryType: item.factorytype,
            productionProcess: item.productionprocess,
            processName: item.productionprocess
              ? item.productionprocess.split('/')[0]
              : '',
            unit: item.productionprocess
              ? item.productionprocess.split('/')[1]
              : '吨',
            targetLine: item.targetline,
            inventory: item.inventory,
            yesterdayDisposal: item.yesterdaydisposal,
            qualify: item.qualify,
            description: item.description,
            yesterdayBlocks: item.yesterdayblocks,
            setDate: item.setdate
          }
        })
        this.processed.barX = this.processed.gridData.map(
          item =>
            item.factoryType +
            item.productionProcess +
            '\n当前库存：' +
            item.inventory
        )
        this.processed.bar1 = this.processed.gridData.map(item => {
          if (item.unit === '吨') {
            return {
              value: item.yesterdayDisposal,
              plan: item.targetLine,
              unit: item.unit,
              piece: item.yesterdayBlocks, // 块数
              show: true,
              finished: item.qualify !== '否',
              description: item.description
            }
          } else {
            return {
              value: 0,
              plan: 0,
              pieceValue: item.yesterdayDisposal,
              piecePlan: item.targetLine,
              show: false,
              unit: item.unit,
              description: item.description
            }
          }
        })
        this.processed.bar2 = this.processed.gridData.map(item => {
          if (item.unit === '块') {
            return {
              value: item.yesterdayDisposal,
              plan: item.targetLine,
              unit: item.unit,
              show: true,
              finished: item.qualify !== '否',
              description: item.description
            }
          } else {
            return {
              value: 0,
              plan: 0,
              show: false,
              unit: item.unit
            }
          }
        })
      })
    },
    saveProcessed() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.processed.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(saveWipInventoryTracking, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.processed.dialogVisible = false
          this.getProcessed()
        }
      })
    },
    getParam(name, list) {
      const match = list.find(item => item.parameter === name)
      return match ? match.content : null
    },
    importProcessedData(date) {
      post(findWipInventoryTrackingByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.processed.gridData = res.data.map(item => {
          return {
            factoryType: item.factorytype,
            productionProcess: item.productionprocess,
            processName: item.productionprocess
              ? item.productionprocess.split('/')[0]
              : '',
            unit: item.productionprocess
              ? item.productionprocess.split('/')[1]
              : '吨',
            targetLine: item.targetline,
            inventory: item.inventory,
            yesterdayDisposal: item.yesterdaydisposal,
            qualify: item.qualify,
            yesterdayBlocks: item.yesterdayblocks,
            description: item.description,
            setDate: item.setdate
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    async getDivDailyRate() {
      let res = await post(findDivDailyRate, {
        beginDate: this.prevDate,
        endDate: this.prevDate
      })
      let XData = []
      let YData = []
      if (res.success) {
        //加了后两位小数点，图标才展示，（可能因为需要保持所有数据拥有小数点
        res.data.forEach((val, ind) => {
          if (res.data[ind].RATE === '100%') {
            res.data[ind].RATE = '100.00%'
          }
        })
        // list = res.data
        res.data.forEach(item => {
          XData.push(item.EQUIP_NAME + '\n' + item.FACTORY_NAME)
          YData.push(Number(item.RATE.slice(0, -1)))
        })
        this.divDaily.barX = XData
        this.divDaily.bar1 = YData
        console.log(this.divDaily.bar1)
        await this.getEquipStatus(res.data)
      }
    },
    async getEquipStatus(list) {
      //柱状图横坐标
      let seriesDatum = []
      //顶部圆形图案个数
      let symbolArray = []
      //顶部圆形图案颜色
      let symbolColors = []
      let markPointDatum = []
      list.forEach(item => {
        seriesDatum.push(item.RATE.slice(0, -1))
      })
      console.log('seriesDatum', seriesDatum)
      this.divDaily.data = []
      let res = await post(findEQStatus, {})
      if (res.success && res.data !== null) {
        res.data.forEach((val, index) => {
          list.forEach((val2, index2) => {
            if (val.EQUIP_NO === val2.EQUIP_NO) {
              list[index2].status = res.data[index].STATUS
            }
          })
        })
        console.log('list', list)
      }
      list.forEach((item, ind) => {
        if (item.status !== null || item.status !== undefined) {
          if (item.status === true) {
            symbolColors[ind] = '#19BE6B'
          } else if (item.status === false) {
            symbolColors[ind] = '#FF2855'
          } else {
            symbolColors[ind] = '#8b8589' //透明色#ffffff00
          }
        }
      })
      console.log('颜色symbolColors', symbolColors)
      for (var i = 0; i < seriesDatum.length; i++) {
        symbolArray[i] = 'circle'
        let _obj = {
          symbol: symbolArray[i],
          symbolSize: [10, 10],
          symbolRotate: 0,
          symbolOffset: ['0', -28],
          coord: [i, seriesDatum[i] + 10],
          // value: seriesDatum[i] - 20,
          label: {
            show: true,
            color: '#000',
            fontSize: 12,
            position: 'right'
          },
          itemStyle: {
            borderWidth: 3,
            borderColor: symbolColors[i],
            color: symbolColors[i]
          }
        }
        markPointDatum.push(_obj)
        this.divDaily.bar2 = markPointDatum
      }
    },
    // 导出表格
    exportTable() {
      console.log(1111)
      let fileName = '库存情况（在制品）' + this.cDate
      let table = document.querySelector('#table1')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          fileName + '.xlsx'
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 38px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      letter-spacing: 1px;
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.green {
            color: #19be6b;
          }
        }
      }
      .name {
        display: inline-block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: inline-block;
        padding: 0 8px;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.dialog-text {
  color: #fff;
  font-size: 20px;
}
.bnt_flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
