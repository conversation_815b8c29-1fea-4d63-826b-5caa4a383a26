<template>
  <div class="content">
    <div class="content-item">
      <screen-border-multi>
        <template v-slot:title>
          <div class="tabs-class">
            <div
              v-for="(item) in tabList"
              :key="item.id"
              :class="{'tab-pane-active': active === item.id}"
              class="tab-pane"
              @click="active = item.id">
              <div class="tab-pane-title-class">
                <div>{{ item.title }}</div>
                <div
                  v-if="active === item.id"
                  class="tab-pane-img">
                  <img
                    class="tab-pane-img2"
                    src="@/assets/images/screen/tab-pane-active-line2.png"
                    alt="">
                  <img
                    class="tab-pane-img1"
                    src="@/assets/images/screen/tab-pane-active-line.png"
                    alt="">
                </div>
              </div>
            </div>
          </div>
        </template>
        <custom-table-noheader
          v-if="active === '1'"
          :title="'当日工艺待判'"
          :key="'productYes1'"
          :setting="tableObj1.setting"
          :url-list="tableObj1.url.list"
          :url-save="tableObj1.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '2'"
          :key="'productYes2'"
          :title="'在制品总表'"
          :setting="tableObj2.setting"
          :url-list="tableObj2.url.list"
          :url-save="tableObj2.url.save"
          :select-date="selectDate"/>
        <!-- <tracking-progress
          v-if="active === 3"
          :select-date="selectDate"
          @dateChange="changeDate"/> -->
        <custom-table-noheader20
          v-if="active === '3'"
          :key="'productYes3'"
          :title="'工序处理跟踪'"
          :setting="tableObj3.setting"
          :url-list="tableObj3.url.list"
          :url-save="tableObj3.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader9
          v-if="active === '4'"
          :key="'productYes4'"
          :title="'退判分析'"
          :setting="tableObj4.setting"
          :url-list="tableObj4.url.list"
          :url-save="tableObj4.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader6
          v-if="active === '5'"
          :key="'productYes5'"
          :title="'XAA未入库'"
          :setting="tableObj5.setting"
          :url-list="tableObj5.url.list"
          :url-save="tableObj5.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader12
          v-if="active === '9'"
          :key="'productYes9'"
          :title="'厚度≤16mm订单超7天未判定'"
          :setting="tableObj9.setting"
          :url-list="tableObj9.url.list"
          :url-save="tableObj9.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader13
          v-if="active === '10'"
          :key="'productYes10'"
          :title="'超2个月在制品情况'"
          :setting="tableObj10.setting"
          :url-list="tableObj10.url.list"
          :url-save="tableObj10.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader14
          v-if="active === '11'"
          :key="'productYes11'"
          :title="'超3个月在制品情况'"
          :setting="tableObj11.setting"
          :url-list="tableObj11.url.list"
          :url-save="tableObj11.url.save"
          :select-date="selectDate"/>
          <!-- <custom-table-noheader
          v-if="active === '12'"
          :key="'productYes12'"
          :title="'板材非计划指标综合'"
          :setting="tableObj12.setting"
          :url-list="tableObj12.url.list"
          :url-save="tableObj12.url.save"
          :select-date="selectDate"/> -->
      </screen-border-multi>
    </div>
  </div>
</template>
 
 <script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import {
  qmsQualitySupervisionQuery,
  qmsQualitySupervisionSave
} from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/C2Meeting/component/custom-table'
import {
  cutUnplannedRateFind,
  cutUnplannedRateSave,
  findEquipmentOperation,
  finishingShearingFind,
  finishingShearingSave,
  hotRollingSituationFind,
  hotRollingSituationSave,
  productionSituationDayFind,
  productionSituationDaySave,
  PSCDayFind,
  PSCDaySave,
  ProductionNotStorageFindAllBySetDate,
  ProductionNotStoragesaveAll,
  tobeHeatFindAllBySetDate,
  tobeHeatsaveAll,
  workProgressFindAllBySetDate,
  workProgresssaveAll,
  flawDetectionFindAllBySetDate,
  flawDetectionsaveAll,
  refuaslReasonFindAllBySetDate,
  refuaslReasonsaveAll,
  UndeterminedOrderrFind,
  UndeterminedOrder,
  WorkInProgressFind,
  WorkInProgress
} from '@/api/screenC2'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import CustomTableNoheader from '@/pages/screen/C2Meeting/component/custom-table-noheader'
import CustomTableNoheader6 from '@/pages/screen/C2Meeting/component/custom-table-noheader6'
import CustomTableNoheader9 from '@/pages/screen/C2Meeting/component/custom-table-noheader9'
import CustomTableNoheader12 from '@/pages/screen/C2Meeting/component/custom-table-noheader12'
import CustomTableNoheader13 from '@/pages/screen/C2Meeting/component/custom-table-noheader13'
import CustomTableNoheader14 from '@/pages/screen/C2Meeting/component/custom-table-noheader14'
import CustomTableNoheader20 from '@/pages/screen/C2Meeting/component/custom-table-noheader20'
import trackingProcess from '@/pages/screen/C2Meeting/component/trackingProcess'
import { post } from '@/lib/Util'
import { expenseDetail } from '@/api/device'
export default {
  name: 'productYest',
  components: {
    CustomTableNoheader,
    ScreenBorderMulti,
    CustomTable,
    SingleBarsChart,
    CustomTableNoheader6,
    CustomTableNoheader9,
    CustomTableNoheader12,
    CustomTableNoheader13,
    CustomTableNoheader14,
    trackingProcess,
    CustomTableNoheader20
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      active: '1',
      loading: false,
      tabList: [
        {
          id: '1',
          active: true,
          title: '当日工艺待判'
        },
        {
          id: '2',
          active: false,
          title: '在制品总表'
        },
        {
          id: '3',
          active: false,
          title: '工序处理跟踪'
        },
        {
          id: '4',
          active: false,
          title: '退判分析'
        },
        {
          id: '5',
          active: false,
          title: 'XAA未入库'
        },
        {
          id: '9',
          active: false,
          title: '厚度≤16mm订单超7天未判定 '
        },
        {
          id: '10',
          active: false,
          title: '超2个月在制品情况 '
        },
        {
          id: '11',
          active: false,
          title: '超3个月在制品情况 '
        }
      ],
      tableObj1: {
        url: {
          save: tobeHeatsaveAll,
          list: tobeHeatFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'name',
            keySave: 'name',
            label: '工艺项目',
            width: '100'
          },
          {
            keyQuery: 'weight',
            keySave: 'weight',
            label: '重量(吨)'
          },
          {
            keyQuery: 'grade',
            keySave: 'grade',
            label: '钢种'
          },
          {
            keyQuery: 'specifications',
            keySave: 'specifications',
            label: '规格'
          },
          {
            keyQuery: 'content',
            keySave: 'content',
            label: '备注'
          },
          {
            keyQuery: 'setDate',
            keySave: 'setDate',
            label: '晨会时间'
          }
        ]
      },
      tableObj2: {
        url: {
          save: workProgresssaveAll,
          list: workProgressFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'productionProcess',
            keySave: 'productionProcess',
            label: '生产工序'
          },
          {
            keyQuery: 'plan',
            keySave: 'plan',
            label: '分项计划指标'
          },
          {
            keyQuery: 'productionCapacity',
            keySave: 'productionCapacity',
            label: '生产能力'
          },
          {
            keyQuery: 'zrcll',
            keySave: 'zrcll',
            label: '昨日处理量'
          },
          {
            keyQuery: 'dcll',
            keySave: 'dcll',
            label: '目前在制品量'
          },
          {
            keyQuery: 'jjcll',
            keySave: 'jjcll',
            type: 'textarea',
            label: '警戒处理量'
          },
          {
            keyQuery: 'excePlan',
            keySave: 'excePlan',
            type: 'textarea',
            label: '超计划量'
          },
          {
            keyQuery: 'avgPlan',
            keySave: 'avgPlan',
            type: 'textarea',
            label: '本月每日须多处理量'
          },
          {
            keyQuery: 'remarks',
            keySave: 'remarks',
            type: 'textarea',
            label: '备注'
          },
          {
            keyQuery: 'dept',
            keySave: 'dept',
            type: 'textarea',
            label: '责任人'
          },
          {
            keyQuery: 'department',
            keySave: 'department',
            type: 'textarea',
            label: '责任单位'
          },
          {
            keyQuery: 'setDate',
            keySave: 'setDate',
            type: 'textarea',
            label: '责任单位'
          }
        ]
      },
      tableObj3: {
        url: {
          save: flawDetectionsaveAll,
          list: flawDetectionFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'classification',
            keySave: 'classification',
            label: '分类'
          },
          {
            keyQuery: 'weight',
            keySave: 'weight',
            label: '吨位(吨)'
          },
          {
            keyQuery: 'millStdspec',
            keySave: 'millStdspec',
            label: '钢种'
          },
          {
            keyQuery: 'specifications',
            keySave: 'specifications',
            label: '规格'
          },
          {
            keyQuery: 'remark',
            keySave: 'remark',
            label: '备注'
          },
          {
            keyQuery: 'setDate',
            keySave: 'setDate',
            label: '晨会日期'
          }
        ]
      },
      tableObj4: {
        url: {
          save: refuaslReasonsaveAll,
          list: refuaslReasonFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'reason',
            keySave: 'reason',
            label: '退判原因'
          },
          {
            keyQuery: 'tonnage',
            keySave: 'tonnage',
            label: '吨位(吨)'
          },
          {
            keyQuery: 'block',
            keySave: 'block',
            label: '块数'
          },
          {
            keyQuery: 'content',
            keySave: 'content',
            label: '具体原因'
          }
        ]
      },
      tableObj5: {
        url: {
          save: ProductionNotStoragesaveAll,
          list: ProductionNotStorageFindAllBySetDate
        },
        setting: [
          //  {
          //    keyQuery: 'index',
          //    keySave: 'index',
          //    label: '序号'
          //  },
          {
            keyQuery: 'name',
            keySave: 'name',
            label: ''
          },
          {
            keyQuery: 'num',
            keySave: 'num',
            label: ''
          },
          {
            keyQuery: 'wei',
            keySave: 'wei',
            label: ''
          },
          {
            keyQuery: 'alert',
            keySave: 'alert',
            label: ''
          }
        ]
      },
      tableObj9: {
        url: {
          save: UndeterminedOrder,
          list: UndeterminedOrderrFind
        },
        setting: [
          {
            keyQuery: 'workUnit',
            keySave: 'workUnit',
            label: '责任单位'
          },
          {
            keyQuery: 'workNum',
            keySave: 'workNum',
            label: '块数'
          },
          {
            keyQuery: 'workWgt',
            keySave: 'workWgt',
            label: '吨位'
          }
        ]
      },
      tableObj10: {
        url: {
          save: WorkInProgress,
          list: WorkInProgressFind
        },
        setting: [
          {
            keyQuery: 'workUnit',
            keySave: 'workUnit',
            label: '责任单位'
          },
          {
            keyQuery: 'process',
            keySave: 'process',
            label: '工序'
          },
          {
            keyQuery: 'workNum',
            keySave: 'workNum',
            label: '块数'
          },
          {
            keyQuery: 'workWgt',
            keySave: 'workWgt',
            label: '吨位'
          }
        ]
      },
      tableObj11: {
        url: {
          save: WorkInProgress,
          list: WorkInProgressFind
        },
        setting: [
          {
            keyQuery: 'workUnit',
            keySave: 'workUnit',
            label: '责任单位'
          },
          {
            keyQuery: 'process',
            keySave: 'process',
            label: '工序'
          },
          {
            keyQuery: 'workNum',
            keySave: 'workNum',
            label: '块数'
          },
          {
            keyQuery: 'workWgt',
            keySave: 'workWgt',
            label: '吨位'
          }
        ]
      },
      deviceSetting: {
        url: {
          list: findEquipmentOperation
        },
        dataList: [],
        setting: [
          {
            keyQuery: 'T_DATE_FROM',
            keySave: 'T_DATE_FROM',
            label: '停机开始'
          },
          {
            keyQuery: 'T_DATE_TO',
            keySave: 'T_DATE_TO',
            label: '停机结束'
          },
          {
            keyQuery: 'TIMES',
            keySave: 'TIMES',
            label: '影响时间'
          },
          {
            keyQuery: 'FAULT_DESCRIPTION',
            keySave: 'FAULT_DESCRIPTION',
            label: '事故描述'
          },
          {
            keyQuery: 'CHG_GRD_DEP',
            keySave: 'CHG_GRD_DEP',
            label: '责任单位'
          }
        ]
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.loadData()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.loadData()
  },
  methods: {
    loadData() {
      post(this.deviceSetting.url.list, {
        startTime: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('YYYYMMDD'),
        endTime: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('YYYYMMDD')
      }).then(res => {
        this.deviceSetting.dataList = res.rows
      })
    }
  }
}
</script>
 
 <style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
