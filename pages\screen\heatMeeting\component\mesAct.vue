<template>
  <div class="mesAct">
    <!--     class="screen-input"-->
    <div class="search-box">
      <el-form
        ref="form"
        :model="form"
        size="mini"
        class="el-form--inline"
        label-width="80px">
        <el-form-item label="出炉时间">
          <!--          <el-date-picker
            v-model="form.measureTime"
            type="datetimerange"
            align="right"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            placeholder="选择日期"/>-->
          <el-date-picker
            v-model="form.measureTime"
            class="screen-input"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"/>

            <!--          <el-col :span="11">
          <el-date-picker
            v-model="form.startTime"
            class="screen-input"
            type="datetime"
            placeholder="选择日期"
            style="width: 100%;"/>
        </el-col>
        <el-col
          :span="2"
          class="line">-</el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="form.endTime"
            class="screen-input"
            type="datetime"
            placeholder="选择时间"
            style="width: 100%;"/>
        </el-col>-->
        </el-form-item>
        <el-form-item label="物料号">
          <el-input
            v-model="form.MAT_NO"
            class="screen-input"/>
        </el-form-item>

        <el-form-item label="炉座号">
          <el-select
            v-model="form.CHARGE_FUR_LINE"
            class="screen-input"
            clearable
            placeholder="请选择炉座号">
            <el-option
              label="请选择炉座号"
              value=""/>
            <el-option
              label="5号炉"
              value="0"/>
            <el-option
              label="6号炉"
              value="1"/>
          </el-select>
        </el-form-item>

        <el-form-item label="工厂">
          <el-input
            v-model="form.PLT"
            class="screen-input"/>
        </el-form-item>
        <el-form-item label="班次">
          <el-select
            v-model="form.CHG_SHIFT"
            class="screen-input"
            clearable
            placeholder="请选择班次">
            <el-option
              label="请选择炉班次"
              value=""/>
            <el-option
              label="白班"
              value="2"/>
            <el-option
              label="小夜班"
              value="3"/>
            <el-option
              label="大夜班"
              value="1"/>
          </el-select>
        </el-form-item>
        <el-form-item label="班别">
          <el-select
            v-model="form.CHG_GROUP_CD"
            class="screen-input"
            clearable
            placeholder="请选择班别">
            <el-option
              label="请选择炉班别"
              value=""/>
            <el-option
              label="甲"
              value="A"/>
            <el-option
              label="乙"
              value="B"/>
            <el-option
              label="丙"
              value="C"/>
            <el-option
              label="丁"
              value="D"/>
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="标准号">
          <el-input
            v-model="form.STDSPEC"
            class="screen-input"/>
        </el-form-item>-->
        <el-form-item style="margin-left: 60px">
          <el-button
            type="primary"
            @click="onSubmit">查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        :data="tableData"
        :header-cell-style="{'text-align':'center'}"
        :cell-style="{'text-align':'center'}"
        height="850px"
        style="width: 100%">
        <el-table-column
          prop="MAT_NO"
          width="120px"
          label="物料号"/>
        <el-table-column
          prop="THK"
          label="厚度"/>
        <!--        <el-table-column
          prop="STDSPEC"
          label="标准号"/>-->
        <el-table-column
          prop="CHARGE_DATE"
          width="120px"
          label="入炉时间"/>
        <el-table-column
          prop="DIS_CHARGE_DATE"
          width="120px"
          label="出炉时间"/>
        <el-table-column
          prop="IN_FCE_TM"
          width="100px"
          label="在炉时间(分钟)"/>
        <el-table-column
          prop="REHEAT_DT"
          label="加热段时间"/>
        <el-table-column
          prop="UNIFORM_DT"
          label="均热段时间"/>
        <el-table-column
          prop="COL_OUT_TEMP"
          width="100px"
          label="钢板出炉温度"/>
        <el-table-column
          prop="HEAT_RATIO"
          label="加热速率"/>
        <el-table-column
          prop="HTM_CNT"
          label="热处理次数"/>
        <el-table-column
          prop="CHARGE_FUR_LINE"
          label="炉座号"/>
        <el-table-column
          prop="SEQ_NO"
          label="序号"/>
        <el-table-column
          prop="PRC_LINE"
          label="产线别"/>
        <el-table-column
          prop="PLT"
          label="工厂"/>
        <el-table-column
          prop="CHG_SHIFT"
          label="班次">
          <template slot-scope="scope">
            <span v-if="scope.row.CHG_SHIFT == '1'">大夜班</span>
            <span v-if="scope.row.CHG_SHIFT == '2'">白班</span>
            <span v-if="scope.row.CHG_SHIFT == '3'">小夜班</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="CHG_GROUP_CD"
          label="班别">
          <template slot-scope="scope">
            <span v-if="scope.row.CHG_GROUP_CD == 'A'">甲</span>
            <span v-if="scope.row.CHG_GROUP_CD == 'B'">乙</span>
            <span v-if="scope.row.CHG_GROUP_CD == 'C'">丙</span>
            <span v-if="scope.row.CHG_GROUP_CD == 'D'">丁</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="WRK_DATE"
          width="100px"
          label="抛丸作业时间"/>
        <!--(H:热处理实绩；S:抛丸实绩)-->
        <el-table-column
          prop="RSLT_FLAG"
          width="100px"
          label="作业实绩标记">
          <template slot-scope="scope">
            <span v-if="scope.row.RSLT_FLAG == 'H'">热处理实绩</span>
            <span v-if="scope.row.RSLT_FLAG == 'S'">抛丸实绩</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="UPPER_FLOW"
          label="头部水量"/>
        <el-table-column
          prop="LOWER_FLOW"
          label="尾部水量"/>
        <el-table-column
          prop="COOL_MODE"
          width="110px"
          label="淬火机冷却模式"/>
        <el-table-column
          prop="CONTROL_MODE"
          width="110px"
          label="淬火机控制模式"/>
        <el-table-column
          prop="SW_TIME"
          width="110px"
          label="淬火机摆动时间"/>
        <el-table-column
          prop="HZ_SET_WATERRATE"
          width="160px"
          label="淬火高压区上下水比(设定)"/>
        <el-table-column
          prop="HZ_ACT_WATERRATE"
          width="160px"
          label="淬火高压区上下水比(实际)"/>
        <el-table-column
          prop="LZ_SET_WATERRATE"
          width="160px"
          label="淬火低压区上下水比(设定)"/>
        <el-table-column
          prop="LZ_ACT_WATERRATE"
          width="160px"
          label="淬火低压区上下水比(实际)"/>
        <el-table-column
          prop="WATER_ZONG"
          width="100px"
          label="各支管总水量"/>
        <el-table-column
          prop="WATER_ZHI1"
          width="100px"
          label="支管1水消耗量"/>
        <el-table-column
          prop="WATER_ZHI2"
          width="100px"
          label="支管2水消耗量"/>
        <el-table-column
          prop="WATER_ZHI3"
          width="100px"
          label="支管3水消耗量"/>
        <el-table-column
          prop="WATER_ZHI4"
          width="100px"
          label="支管4水消耗量"/>
        <el-table-column
          prop="WATER_ZHI5"
          width="100px"
          label="支管5水消耗量"/>
        <el-table-column
          prop="WATER_ZHI6"
          width="100px"
          label="支管6水消耗量"/>
        <el-table-column
          prop="WATER_ZHI7"
          width="100px"
          label="支管7水消耗量"/>
        <el-table-column
          prop="WATER_ZHI8"
          width="100px"
          label="支管8水消耗量"/>
        <el-table-column
          prop="WATER_ZHI9"
          width="100px"
          label="支管9水消耗量"/>
        <el-table-column
          prop="WATER_ZHI10"
          width="110px"
          label="支管10水消耗量"/>
        <el-table-column
          prop="WATER_ZHI11"
          width="110px"
          label="支管11水消耗量"/>
        <el-table-column
          prop="WATER_ZHI12"
          width="110px"
          label="支管12水消耗量"/>
        <el-table-column
          prop="WATER_ZHI13"
          width="110px"
          label="支管13水消耗量"/>
        <el-table-column
          prop="WATER_ZHI14"
          width="110px"
          label="支管14水消耗量"/>
        <el-table-column
          prop="QUEN_START"
          width="100px"
          label="淬火开始时刻"/>
        <el-table-column
          prop="QUEN_END"
          width="100px"
          label="淬火完成时刻"/>
        <el-table-column
          prop="QUEN_TEMP_START"
          width="110px"
          label="淬火机开淬温度"/>
        <el-table-column
          prop="QUEN_TEMP_END"
          width="110px"
          label="淬火机终淬温度"/>

        <el-table-column
          prop="QUEN_WAT_TEMP"
          width="100px"
          label="淬火介质温度"/>
        <el-table-column
          prop="QUEN_COOL_RATIO"
          width="100px"
          label="淬火冷却速率"/>
        <el-table-column
          prop="QUEN_SPEED"
          width="100px"
          label="钢板运行速度"/>
        <el-table-column
          prop="QUEN_EMP"
          width="100px"
          label="淬火作业人员"/>
        <el-table-column
          prop="TEMP_AVE_CAL"
          width="120px"
          label="平均温度（计算）"/>
        <el-table-column
          prop="HTM_METH1"
          width="100px"
          label="热处理方法1"/>
        <el-table-column
          prop="HTM_METH2"
          width="100px"
          label="热处理方法2"/>
        <el-table-column
          prop="HTM_METH3"
          width="100px"
          label="热处理方法3"/>
        <el-table-column
          prop="HTM_METH4"
          width="100px"
          label="热处理方法4"/>
        <el-table-column
          prop="HTM_METH5"
          width="100px"
          label="热处理方法5"/>
        <el-table-column
          prop="HTM_METHOD"
          label="热处理方法"/>
        <el-table-column
          prop="SEQ_NUM"
          label="序列号"/>
        <el-table-column
          prop="MSG_TS"
          width="120px"
          label="数据写入时间"/>
        <el-table-column
          prop="MSG_FLAG"
          label="信息状态"/>
        <el-table-column
          prop="INF_FL"
          label="增删标记"/>
      </el-table>
    </div>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import moment from 'moment'

export default {
  name: 'mesAct',
  data() {
    return {
      loading: true,
      tableData: [],
      form: {
        measureTime: [
          moment().format('YYYY-MM-DD') + ' 00:00:00',
          moment().format('YYYY-MM-DD') + ' 23:59:59'
        ],
        MAT_NO: '', //物料号
        CHARGE_FUR_LINE: '', //炉座号
        PLT: '', //生产厂
        CHG_SHIFT: '', //班次
        CHG_GROUP_CD: '', //班别
        // THK: '', //厚度
        STDSPEC: '' //标准号
      }
    }
  },
  mounted() {
    this.onSubmit()
  },
  methods: {
    onSubmit() {
      this.loading = true
      post('mesAPI/mesApi/findHtmrslt', {
        matId: this.form.MAT_NO, //物料号
        plt: this.form.PLT, //生产厂
        line: this.form.CHARGE_FUR_LINE,
        shift: this.form.CHG_SHIFT, //班组
        group: this.form.CHG_GROUP_CD, //班别
        startTime:
          this.form.measureTime == null
            ? ''
            : moment(this.form.measureTime[0]).format('YYYYMMDD') + '000000',
        endTime:
          this.form.measureTime == null
            ? ''
            : moment(this.form.measureTime[1]).format('YYYYMMDD') + '235959'
      }).then(res => {
        this.tableData = res.data
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="less">
.mesAct {
  margin-top: 10px;

  .el-date-editor--datetimerange {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
    padding-right: 5px;
  }

  /deep/ .el-range-input,
  /deep/ .el-range-separator {
    color: #fff !important;
  }

  .el-table .el-table__header-wrapper tr {
    white-space: nowrap;
    overflow: hidden;
  }

  .el-form--inline .el-form-item {
    display: inline-block;
    margin-right: 10px;
    vertical-align: top;

    /deep/ .el-form-item__label {
      color: #ffffff;
    }

    /deep/ .el-form-item__content {
      margin-left: 0px !important;
    }

    .el-button--primary {
      background-color: #0c4e64;
      border-color: #0c4e64;
      border-radius: 3px;
    }

    /deep/ .el-radio__label {
      color: #ffffff;
    }

    /deep/ .el-radio__input.is-checked + .el-radio__label {
      color: #1fc6ff;
    }

    /deep/ .el-radio__input.is-checked .el-radio__inner {
      border-color: #1fc6ff;
      background: #1fc6ff;
    }

    /deep/ .el-button--primary:focus,
    /deep/ .el-button--primary:hover {
      background: #057499;
      border-color: #057499;
    }
  }
}
</style>
