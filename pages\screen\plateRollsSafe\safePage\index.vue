<!--安全检查-->
<template>
  <div class="content">
    <div class="content-item">
      <screen-border-multi :title="'安全检查'">
        <template v-slot:headerRight>
          <el-row>
            <span>已完成：</span>
            <span style="margin-right: 10px;">{{ monthData.finish }}</span>
            <span>整改中：</span>
            <span style="margin-right: 10px;">{{ monthData.ongoing }}</span>
            <span
              v-if="monthData.ongoingSet.length!==0"
              style="margin-right: 10px;font-size: 14px">{{ monthData.ongoingSet }}</span>
            <span>未完成：</span>
            <span style="margin-right: 10px;">{{ monthData.unFinish }}</span>
            <span
              v-if="monthData.unFinishSet.length!==0"
              style="margin-right: 10px;font-size: 14px">{{ monthData.unFinishSet }}</span>
            <span
              v-command="'/screen/plateRollsSafe/edit'"
              class="screen-btn"
              @click="AddData">
              <el-icon class="el-icon-edit-outline"/>
              新增
            </span>
            <span
              v-command="'/screen/plateRollsSafe/edit'"
              class="screen-btn"
              @click="handDel">
              <el-icon class="el-icon-delete"/>
              删除
            </span>
          </el-row>
        </template>

        <el-table
          v-loading="loading"
          :data="HT_data"
          :row-class-name="rowClassName"
          border
          @selection-change="getSelectRow">
          <el-table-column
            type="selection"
            align="center"/>
          <el-table-column
            show-overflow-tooltip
            width="70"
            label="序号">
            <template v-slot="scope">
              <div>{{ scope.$index+1 }}</div>
            </template>
          </el-table-column>
          <el-table-column
            :filters="workshopList"
            :filter-method="filterMethod"
            property="workshop"
            label="车间"
            width="130">
            <template v-slot="scope">
              <div>{{ scope.row.workshop }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="隐患类别"
            width="130">
            <template v-slot="scope">
              <div>{{ scope.row.dangerType }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="隐患描述">
            <template v-slot="scope">
              <div>{{ scope.row.description }}</div>
            </template>
          </el-table-column>
          <el-table-column
            :filters="workshopList"
            :filter-method="filterMethod"
            property="modifyWorkshop"
            label="整改车间"
            width="130">
            <template v-slot="scope">
              <div>{{ scope.row.modifyWorkshop }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="整改负责人"
            width="120">
            <template v-slot="scope">
              <div>{{ scope.row.modifyHead }}</div>
            </template>
          </el-table-column>
          <el-table-column
            :filters="statusList"
            :filter-method="filterMethod"
            property="modifyExecution"
            label="完成情况"
            width="120">
            <template v-slot="scope">
              <div>{{ scope.row.modifyExecution }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="考核金额"
            width="100">
            <template v-slot="scope">
              <div>{{ scope.row.amount }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="日期"
            width="130">
            <template v-slot="scope">
              <div>{{ scope.row.setDate }}</div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property=""
            width="150"
            label="操作">
            <template v-slot="scope">
              <span @click="WatchRow(scope.row, scope.$index)">查看</span>
              <span @click="Edit(scope.row)">修改</span>
              <span @click="Del(scope.row)">删除</span>
            </template>
          </el-table-column>
        </el-table>
      </screen-border-multi>
    </div>

    <!--隐患随手拍-->
    <el-dialog
      :visible.sync="HT_view"
      :close-on-click-modal="false"
      width="60%"
      class="screen-dialog"
      @close="Close_HT_view">
      <template v-slot:title>
        <div class="custom-dialog-title">
          安全随手拍
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">车间</div>
          <el-select
            v-model="FormData.workshop"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in workshopList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">隐患类别</div>
          <el-select
            v-model="FormData.dangerType"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in dangerTypeList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">隐患描述</div>
          <el-input
            v-model="FormData.description"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改车间</div>
          <el-select
            v-model="FormData.modifyWorkshop"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in workshopList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改负责人</div>
          <el-input
            v-model="FormData.modifyHead"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">完成情况</div>
          <el-select
            v-model="FormData.modifyExecution"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改前图片上传</div>
          <el-upload
            :before-remove="beforeRemove"
            :before-upload="beforeUpload"
            :limit="4"
            :on-exceed="handleExceed"
            :file-list="fileList"
            :multiple="false"
            action=""
            accept="image/*"
            class="upload-demo">
            <el-button
              size="small"
              type="primary">点击上传</el-button>
            <div
              slot="tip"
              class="el-upload__tip">请选择图片</div>
          </el-upload>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改后图片上传</div>
          <el-upload
            :before-remove="beforeRemove2"
            :before-upload="beforeUpload2"
            :limit="4"
            :on-exceed="handleExceed2"
            :file-list="fileList2"
            :multiple="false"
            action=""
            accept="image/*"
            class="upload-demo">
            <el-button
              size="small"
              type="primary">点击上传</el-button>
            <div
              slot="tip"
              class="el-upload__tip">请选择图片</div>
          </el-upload>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">金额考核</div>
          <el-input
            v-model="FormData.amount"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          v-loading="HT_loading"
          class="screen-btn"
          @click="SubmitData">
          确定
        </span>
      </div>
    </el-dialog>

    <!--查看-->
    <el-dialog
      :visible.sync="Watch_view"
      :close-on-click-modal="false"
      width="70%"
      class="screen-dialog"
      @close="Close_Watch_view">
      <template v-slot:title>
        <div class="custom-dialog-title">
          安全检查
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <el-row>
          <el-col :span="12">
            <div class="dialog-cell">
              <div class="dialog-cell-title">整改前图片</div>
              <el-empty
                v-if="img_1_list.length===0"
                description="暂无图片"/>
              <div v-else>
                <div
                  v-for="(item, index) in img_1_list"
                  :key="item"
                  style="display: inline">
                  <el-image
                    :src="item"
                    :preview-src-list="img_1_list"
                    :initial-index="index"
                    fit="scale-down"
                    style="height: 250px">
                    <div
                      slot="placeholder"
                      class="image-slot">
                      加载中<span class="dot">...</span>
                    </div>
                  </el-image>
                </div>
              </div>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">整改后图片</div>
              <el-empty
                v-if="img_2_list.length===0"
                description="暂无图片"/>
              <div v-else>
                <div
                  v-for="(item, index) in img_2_list"
                  :key="item"
                  style="display: inline">
                  <el-image
                    :src="item"
                    :preview-src-list="img_2_list"
                    :initial-index="index"
                    fit="scale-down"
                    style="height: 250px">
                    <div
                      slot="placeholder"
                      class="image-slot">
                      加载中<span class="dot">...</span>
                    </div>
                  </el-image>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="dialog-cell">
              <div class="dialog-cell-title">车间</div>
              <el-input
                v-model="WatchData.workshop"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">隐患类别</div>
              <el-input
                v-model="WatchData.dangerType"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">隐患描述</div>
              <el-input
                v-model="WatchData.description"
                :rows="3"
                type="textarea"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">整改车间</div>
              <el-input
                v-model="WatchData.modifyWorkshop"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">整改负责人</div>
              <el-input
                v-model="WatchData.modifyHead"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">完成情况</div>
              <el-input
                v-model="WatchData.modifyExecution"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">金额考核</div>
              <el-input
                v-model="WatchData.amount"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">修改人</div>
              <el-input
                v-model="WatchData.updateUser"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
            <div class="dialog-cell">
              <div class="dialog-cell-title">修改时间</div>
              <el-input
                v-model="WatchData.updateDate"
                :rows="3"
                type="input"
                clearable
                readonly
                placeholder="请输入内容"
                class="dialog-cell-input"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  HT_INQUIRE,
  HT_NEWS,
  minio_upload,
  HT_DEL,
  HT_COMPLETION
} from '@/api/screen'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi.vue'
export default {
  name: 'safePage',
  components: {
    ScreenBorderMulti
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      loading: false,
      //数据
      HT_data: [],

      //弹框数据
      HT_view: false,
      HT1_view: false,
      FormData: {},
      HT_loading: false,

      //隐患类别下拉
      dangerTypeList: [
        {
          text: '行为规范',
          value: '行为规范'
        },
        {
          text: '高处作业',
          value: '高处作业'
        },
        {
          text: '安全标识',
          value: '安全标识'
        },
        {
          text: '焊机使用',
          value: '焊机使用'
        },
        {
          text: '安全手续',
          value: '安全手续'
        },
        {
          text: '能量源挂牌上锁',
          value: '能量源挂牌上锁'
        },
        {
          text: '安全用电',
          value: '安全用电'
        },
        {
          text: '消防管理',
          value: '消防管理'
        },
        {
          text: '安全防护',
          value: '安全防护'
        },
        {
          text: '基础管理',
          value: '基础管理'
        },
        {
          text: '加门上锁',
          value: '加门上锁'
        },
        {
          text: '设备本质',
          value: '设备本质'
        },
        {
          text: '特殊工种',
          value: '特殊工种'
        },
        {
          text: '十大禁令',
          value: '十大禁令'
        },
        {
          text: '相关方管理',
          value: '相关方管理'
        },
        {
          text: '物体打击',
          value: '物体打击'
        },
        {
          text: '车辆伤害',
          value: '车辆伤害'
        },
        {
          text: '机械伤害',
          value: '机械伤害'
        },
        {
          text: '起重伤害',
          value: '起重伤害'
        },
        {
          text: '触电',
          value: '触电'
        },
        {
          text: '淹溺',
          value: '淹溺'
        },
        {
          text: '灼烫',
          value: '灼烫'
        },
        {
          text: '火灾',
          value: '火灾'
        },
        {
          text: '高处坠落',
          value: '高处坠落'
        },
        {
          text: '坍塌',
          value: '坍塌'
        },
        {
          text: '其他爆炸',
          value: '其他爆炸'
        },
        {
          text: '中毒和窒息',
          value: '中毒和窒息'
        },
        {
          text: '其他伤害',
          value: '其他伤害'
        },
        {
          text: '四不准—作业项目未申报，不作业',
          value: '四不准—作业项目未申报，不作业'
        },
        {
          text: '四不准—作业无方案、sop，不作业',
          value: '四不准—作业无方案、sop，不作业'
        },
        {
          text:
            '四不准—施工单位负责人（区域负责人、项目管理人员）不在现场，不作业',
          value:
            '四不准—施工单位负责人（区域负责人、项目管理人员）不在现场，不作业'
        },
        {
          text: '四不准—无安全监护人员，不作业',
          value: '四不准—无安全监护人员，不作业'
        },
        {
          text: '五必查—动火、高处、吊运、用电、能量源挂牌上锁',
          value: '五必查—动火、高处、吊运、用电、能量源挂牌上锁'
        },
        {
          text: '六必须—检修作业必须有方案',
          value: '六必须—检修作业必须有方案'
        },
        {
          text: '六必须—监护人员必须到位',
          value: '六必须—监护人员必须到位'
        },
        {
          text: '六必须—危险源与措施必须交底清楚',
          value: '六必须—危险源与措施必须交底清楚'
        },
        {
          text: '六必须—作业方案必须严格执行',
          value: '六必须—作业方案必须严格执行'
        },
        {
          text: '六必须—能量源上锁挂牌必须执行到位',
          value: '六必须—能量源上锁挂牌必须执行到位'
        },
        {
          text: '六必须—非生产作业必须先办理手续再作业',
          value: '六必须—非生产作业必须先办理手续再作业'
        }
      ],
      //整改车间下拉
      workshopList: [
        {
          text: '原料车间',
          value: '原料车间',
          type: 'YLCJ'
        },
        {
          text: '炼钢车间',
          value: '炼钢车间',
          type: 'LGCJ'
        },
        {
          text: '精炼车间',
          value: '精炼车间',
          type: 'JLCJ'
        },
        {
          text: '连铸车间',
          value: '连铸车间',
          type: 'LZCJ'
        },
        {
          text: '运行车间',
          value: '运行车间',
          type: 'YXCJ'
        },
        {
          text: '坯料车间',
          value: '坯料车间',
          type: 'PLCJ'
        },
        {
          text: '综合管理室',
          value: '综合管理室',
          type: 'ZHGLS'
        },
        {
          text: '设备管理室',
          value: '设备管理室',
          type: 'SBGLS'
        },
        {
          text: '品质室',
          value: '品质室',
          type: 'PZS'
        },
        {
          text: '生产管理室',
          value: '生产管理室',
          type: 'SCGLS'
        }
      ],
      //完成情况下拉
      statusList: [
        {
          text: '未完成',
          value: '未完成'
        },
        {
          text: '整改中',
          value: '整改中'
        },
        {
          text: '已完成',
          value: '已完成'
        }
      ],

      fileList: [],
      fileList2: [],
      uploadFileList: [], //上传
      uploadFileList2: [], //上传

      userNo: localStorage.getItem('userId'),
      //多选
      rowData: [],

      //查看
      Watch_view: false,
      WatchData: [],
      img_1_list: [],
      img_2_list: [],

      monthData: {
        finish: '',
        ongoing: '',
        ongoingSet: [],
        unFinish: '',
        unFinishSet: []
      }
    }
  },
  watch: {
    selectDate: function() {
      this.getHiddenTrouble()
    }
  },
  mounted() {
    this.getMonthData()
    this.getHiddenTrouble()
  },
  methods: {
    //完成情况总计
    async getMonthData() {
      let res = await post(HT_COMPLETION, {
        setDate: this.selectDate
      })
      // console.log('完成情况', res)
      if (res.data) {
        this.monthData = res.data
      }
    },

    //查询隐患随手拍数据
    async getHiddenTrouble() {
      this.loading = true
      let res = await post(HT_INQUIRE, {
        setDate: this.selectDate
      })
      // console.log('总数据', res)
      if (res.data) {
        this.loading = false
        this.HT_data = res.data
      }
    },

    //新增
    AddData() {
      this.HT_view = true
      this.FormData = {
        workshop: '',
        dangerType: '',
        description: '',
        amount: '',
        modifyWorkshop: '',
        modifyHead: '',
        modifyExecution: '',
        updateUser: this.userNo,
        fileUrl: '',
        fileList: []
      }
    },

    //确认新增/修改
    async SubmitData() {
      this.HT_loading = true

      //新图片上传
      if (this.uploadFileList.length > 0) {
        for (let i = 0; i < this.uploadFileList.length; i++) {
          let fileItem = this.uploadFileList[i]
          const loading = this.$loading({
            lock: true,
            text: `正在上传整改前第${i + 1}张图片`,
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          let formData = new FormData()
          formData.append('file', fileItem.file)
          const res = await post(minio_upload, formData)
          loading.close()
          let index = res.indexOf('?')
          if (index !== -1) {
            fileItem.fileUrl = res.substring(0, index)
          } else {
            fileItem.fileUrl = res
          }
        }
      }
      if (this.uploadFileList2.length > 0) {
        for (let i = 0; i < this.uploadFileList2.length; i++) {
          let fileItem = this.uploadFileList2[i]
          const loading = this.$loading({
            lock: true,
            text: `正在上传整改后第${i + 1}张图片`,
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          let formData = new FormData()
          formData.append('file', fileItem.file)
          const res = await post(minio_upload, formData)
          loading.close()
          let index = res.indexOf('?')
          if (index !== -1) {
            fileItem.fileUrl = res.substring(0, index)
          } else {
            fileItem.fileUrl = res
          }
        }
      }
      //新增的上传图片
      let fileList = []
      this.uploadFileList.forEach(item => {
        if (item.fileUrl) {
          fileList.push({
            type: 'A',
            fileUrl: item.fileUrl,
            fileName: item.fileName,
            parentId: this.FormData.id
          })
        }
      })
      this.uploadFileList2.forEach(item => {
        if (item.fileUrl) {
          fileList.push({
            type: 'B',
            fileUrl: item.fileUrl,
            fileName: item.fileName,
            parentId: this.FormData.id
          })
        }
      })

      //原先fileList
      this.FormData.fileList.forEach(item => {
        fileList.push(item)
      })

      //把新旧图片全部放到第一个fileList数组中提交
      this.FormData.fileList = fileList

      // console.log('上传', this.FormData)

      let res = await post(HT_NEWS, [this.FormData])

      // console.log('上传', res)

      if (res.status == 1) {
        this.HT_loading = false
        this.$message.success('操作成功！')
        this.Close_HT_view()
        this.getHiddenTrouble()
      }
    },

    //关闭新增弹框
    Close_HT_view() {
      this.HT_view = false
      this.FormData = {}
      this.uploadFileList = []
      this.uploadFileList2 = []
      this.fileList = []
      this.fileList2 = []
    },

    //多选
    getSelectRow(val) {
      this.rowData = val
    },

    // 批量删除
    handDel() {
      let IdData = []
      if (this.rowData.length != 0) {
        this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.rowData.forEach(item => {
            IdData.push({ id: item.id })
          })
          let res = await post(HT_DEL, IdData)
          // console.log('删除', res)
          if (res.status == 1) {
            this.$message.success(res.data)
            this.getHiddenTrouble()
          }
        })
      } else {
        this.$message.warning('请勾选删除行!')
      }
    },

    //查看
    WatchRow(row, index) {
      this.Watch_view = true
      this.WatchData = JSON.parse(JSON.stringify(row))

      this.WatchData.fileList.forEach(item => {
        switch (item.type) {
          case 'A':
            this.img_1_list.push(item.fileUrl)
            break
          case 'B':
            this.img_2_list.push(item.fileUrl)
            break
        }
      })
    },

    //关闭查看弹框
    Close_Watch_view() {
      this.Watch_view = false
      this.WatchData = {}
      this.img_1_list = []
      this.img_2_list = []
    },

    //修改
    Edit(row) {
      this.HT_view = true
      row.fileList.forEach(item => {
        switch (item.type) {
          case 'A':
            this.fileList.push({
              name: item.fileName,
              url: item.fileUrl,
              id: item.id,
              parentId: item.parentId,
              type: item.type
            })
            break
          case 'B':
            this.fileList2.push({
              name: item.fileName,
              url: item.fileUrl,
              id: item.id,
              parentId: item.parentId,
              type: item.type
            })
            break
        }
      })
      this.FormData = JSON.parse(JSON.stringify(row))
    },

    //删除
    Del(row) {
      this.rowData = [
        {
          id: row.id
        }
      ]
      this.handDel()
    },

    //筛选
    filterMethod(value, row, column) {
      const property = column['property']
      return row[property] === value
    },

    //行颜色
    rowClassName({ row, rowIndex }) {
      if (
        row.modifyExecution === null ||
        row.modifyExecution === '未完成' ||
        row.modifyExecution === ''
      ) {
        return 'class_red'
      } else if (row.modifyExecution === '整改中') {
        return 'class_yellow'
      } else {
        return ''
      }
    },

    //上传文件之前函数
    beforeUpload(file) {
      this.uploadFileList.push({
        fileUrl: '',
        fileName: file.name,
        file: file
      })
    },
    beforeUpload2(file) {
      this.uploadFileList2.push({
        fileUrl: '',
        fileName: file.name,
        file: file
      })
    },

    //文件超出限制函数
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 4 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },
    handleExceed2(files, fileList) {
      this.$message.warning(
        `当前限制选择 4 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },

    //删除文件之前函数
    async beforeRemove(file, fileList) {
      const isDel = await this.$confirm(`确定移除 ${file.name}？`)
      if (isDel) {
        if (file.url) {
          const index = this.FormData.fileList.findIndex(
            item => item.fileUrl === file.url
          )
          this.FormData.fileList.splice(index, 1)
        } else {
          const index = this.uploadFileList.findIndex(
            item => item.file === file.raw
          )
          this.uploadFileList.splice(index, 1)
        }
      }
      return isDel
    },
    async beforeRemove2(file, fileList) {
      const isDel = await this.$confirm(`确定移除 ${file.name}？`)
      if (isDel) {
        if (file.url) {
          const index = this.FormData.fileList.findIndex(
            item => item.url === file.url
          )
          this.FormData.fileList.splice(index, 1)
        } else {
          const index = this.uploadFileList2.findIndex(
            item => item.fileUrl === file.raw
          )
          this.uploadFileList2.splice(index, 1)
        }
      }
      return isDel
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
/deep/ .el-table .class_red {
  background: #fd0000;
  color: black;
}
/deep/ .el-table .class_yellow {
  background: #fdfd00;
  color: black;
}
/deep/ .el-table .class_orange {
  background: #f99f04;
  color: black;
}

.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
</style>
