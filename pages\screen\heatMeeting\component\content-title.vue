<template>
  <div class="content-title">
    <div class="cascader">
      <el-cascader
        v-model="cascader.cascaderValue"
        :options="cascader.cascaderOptions"
        :props="{ expandTrigger: 'hover' }"
        class="screen-input"
        style="width: 160px"
        @change="cascaderHandleChange"/>
    </div>
    <div class="time-info">
      <ul>
        <li class="time">
          <span class="font-bold">{{ nowTime }}</span>
        </li>
        <li class="date">
          <span>{{ nowDate }} {{ nowWeek }}</span>
        </li>
        <li class="banzu">
          <span>班组：
            <i v-if="contentTitleData.group == 'A'">甲</i>
            <i v-if="contentTitleData.group == 'B'">乙</i>
            <i v-if="contentTitleData.group == 'C'">丙</i>
            <i v-if="contentTitleData.group == 'D'">丁</i>
          </span>
        </li>
        <li class="banci">
          <span>班次：
            <i v-if="contentTitleData.shift == '1'">大夜班</i>
            <i v-if="contentTitleData.shift == '2'">白班</i>
            <i v-if="contentTitleData.shift == '3'">小夜班</i>
          </span>
        </li>
        <li class="banci">
          <span
            v-if="currentCarousel == 0"
            style="color: rgba(255, 152, 0, 1);font-size: 20px; font-weight: bold">5#热处理炉</span>
          <span
            v-if="currentCarousel == 1"
            style="color: rgba(255, 152, 0, 1);font-size: 20px; font-weight: bold">6#热处理炉</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  name: 'content-title',
  props: {
    currentCarousel: {
      type: Number,
      default: 0
    },
    contentTitleData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data: () => {
    return {
      timer: null,
      nowDate: '',
      nowTime: '',
      nowWeek: '',
      cascader: {
        cascaderValue: ['1'],
        cascaderOptions: [
          {
            value: '1',
            label: '5#热处理炉'
          },
          {
            value: '2',
            label: '6#热处理炉'
          }
        ]
      }
    }
  },
  mounted() {
    this.getNowDateTime()
    this.timer = setInterval(() => {
      this.getNowDateTime()
    }, 1000)
  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = null
  },
  methods: {
    cascaderHandleChange() {
      console.log('zi', this.cascader.cascaderValue)
      this.$emit('checkLu', this.cascader.cascaderValue)
    },
    getNowDateTime() {
      let weekdays = [
        '星期日',
        '星期一',
        '星期二',
        '星期三',
        '星期四',
        '星期五',
        '星期六'
      ]
      let weekIndex = moment().weekday()
      // console.log('weekIndex', weekIndex)
      this.nowDate = moment().format('YYYY-MM-DD')
      this.nowTime = moment().format('HH:mm:ss')
      this.nowWeek = moment().format('dddd')
      this.nowWeek = weekdays[weekIndex]
    }
  }
}
</script>

<style scoped lang="less">
.content-title {
  width: 100%;
  height: 32px;
  line-height: 32px;
  display: flex;

  .time-info {
    color: rgba(31, 198, 255, 1);
    margin-left: 22px;

    ul {
      display: flex;

      li {
        margin-left: 10px;

        > span {
          border-right: 1px solid rgba(19, 82, 128, 1);
          padding-right: 12px;
        }
      }

      :nth-child(4) span {
        border: none;
      }
    }
  }

  .font-bold {
    font-weight: bold;
  }
}
</style>
