<template>
  <div class="bigBox">
    <el-row :gutter="32">
      <el-col
        :span="12"
        style="height:450px">
        <!-- <screen-border title="在制品"> -->
        <custom-table-3
          :title="'切割'"
          :key="'productYes1'"
          :setting="tableObj1.setting"
          :url-list="tableObj1.url.list"
          :url-save="tableObj1.url.save"
          :select-date="selTime"/>
          <!-- <template v-slot:headerRight>
            <span
              v-command="'/screen/coilScreen/bjsc'"
              class="screen-btn"
              @click="openView(1)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template> --> <!-- <el-table
            :data="rollDecideData"
            height="380">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="project"
              label="项目"
              align="center"/>
            <el-table-column
              prop="planYield"
              label="计划产量(吨)"
              align="center"/>
            <el-table-column
              prop="accruedYield"
              label="累计产量(吨)"
              align="center"/>
            <el-table-column
              prop="accruedYield"
              label="时间进度(%)"
              align="center"/>
            <el-table-column
              prop="timeProgress"
              label="产量进度(%)"
              align="center"/>
            <el-table-column
              prop="yieldProgress"
              label="日需均产(吨)"
              align="center"/>
            <el-table-column
              prop="averageYield"
              label="月预计产量(吨)"
              align="center"/>
          </el-table> --><!-- </screen-border> -->
      </el-col>
      <el-col
        :span="12"
        style="height:450px">
        <custom-table
          :title="'强力冷矫'"
          :key="'productYes1'"
          :setting="tableObj2.setting"
          :url-list="tableObj2.url.list"
          :url-save="tableObj2.url.save"
          :select-date="selTime"/>
          <!-- <screen-border title="班组轧制节奏">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/coilScreen/bjsc'"
              class="screen-btn"
              @click="openView(2)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <div class="EchartsBox">
            <div class="setRadio">
              <el-radio
                v-model="radio"
                label="30"
                @input="getRollingRhythm">30秒</el-radio>
              <el-radio
                v-model="radio"
                label="60"
                @input="getRollingRhythm">60秒</el-radio>
            </div>
            <div
              id="Team"
              style="width: 100%; height:400px;"/>
          </div>

        </screen-border> -->
      </el-col>
      <el-col
        :span="12"
        style="height: calc(100vh - 580px);" >
        <custom-table
          :title="'1#,2#矫'"
          :key="'productYes1'"
          :setting="tableObj3.setting"
          :url-list="tableObj3.url.list"
          :url-save="tableObj3.url.save"
          :select-date="selTime"/>
      </el-col>
      <el-col
        :span="12"
        style="height: calc(100vh - 580px);">
        <custom-table
          :title="'离线探伤'"
          :key="'productYes1'"
          :setting="tableObj4.setting"
          :url-list="tableObj4.url.list"
          :url-save="tableObj4.url.save"
          :select-date="selTime"/>
          <!-- <screen-border title="原因说明">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/coilScreen/bjsc'"
              class="screen-btn"
              @click="openView(4)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <el-input
            v-model="textarea"
            :rows="10"
            :disabled="true"
            type="textarea"
            placeholder="请输入内容"/>
        </screen-border> -->
      </el-col>
    </el-row>

    <!--弹框-->
    <el-dialog
      :visible.sync="dialogBox"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      @close="closeDialogBox">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span> -->
            <span
              v-show="title!='原因说明'"
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download"/>
              下载
            </span>
            <span
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-printer"/>
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <el-table
        v-if="title!='原因说明'"
        id="table"
        :data="formData"
        border>
        <el-table-column
          type="index"
          label="序号"
          width="60"/>
        <el-table-column
          v-for="(item,index) in Header"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          align="center">
          <template v-slot="{ row }">
            <el-input v-model="row[item.prop]"/>
            <span v-show="false">{{ row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          width="100"
          label="操作">
          <template v-slot="scope">
            <div class="btn">
              <el-button
                :disabled="title=='当月轧制/终判'||title=='班组轧制节奏'||title=='相关方考核'"
                type="danger"
                icon="el-icon-delete"
                @click="delRow(scope.$index)"/>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <el-input
        v-else
        v-model="textareaCopy"
        :rows="10"
        type="textarea"
        placeholder="请输入内容"/>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import CustomTable from '@/pages/screen/MtPlateRollsFacDispatch/component/custom-table'
import CustomTable3 from '@/pages/screen/MtPlateRollsFacDispatch/component/custom-table-3'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { saveAs } from 'file-saver'
import * as echarts from 'echarts'
import moment from 'moment'

import {
  ROLLDECIDE,
  ROLLDECIDE_SAVE,
  ROLLINGRHYTHM,
  ROLLINGRHYTHM_SAVE,
  ASSESS,
  ASSESS_SAVE,
  CAUSE_EXPLAIN,
  CAUSE_EXPLAIN_SAVE,
  mtpPostWorkInProgress1,
  mtpPostWorkInProgress1Save,
  mtpPostWorkInProgress2,
  mtpPostWorkInProgress2Save,
  mtpRectify1and2,
  mtpRectify1and2Save,
  mtpOfflineFlawDetection,
  mtpOfflineFlawDetectionSave
} from '@/api/screen'

export default {
  name: 'MainlineProduction',
  components: {
    // SingleBarsChart,
    // SteelBarsChart,
    ScreenBorder,
    CustomTable,
    CustomTable3
  },
  props: {
    selTime: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      //当月轧制/终判数据
      rollDecideData: [
        {
          project: '当月轧制',
          planYield: 0,
          accruedYield: 0,
          timeProgress: 0,
          yieldProgress: 0,
          averageYield: 0,
          monYield: 0
        },
        {
          project: '当月轧制',
          planYield: 0,
          accruedYield: 0,
          timeProgress: 0,
          yieldProgress: 0,
          averageYield: 0,
          monYield: 0
        }
      ],
      tableObj1: {
        url: {
          save: mtpPostWorkInProgress1Save,
          list: mtpPostWorkInProgress1
        },
        setting: [
          {
            label: '精整T区切割',
            children: [
              {
                keyQuery: 'classes',
                keySave: 'classes',
                label: '班次'
              },
              {
                keyQuery: 'planNumber_t',
                keySave: 'planNumber_t',
                label: '计划块数'
              },
              {
                keyQuery: 'actualNumber_t',
                keySave: 'actualNumber_t',
                label: '实际块数'
              },
              {
                keyQuery: 'tonnage_t',
                keySave: 'tonnage_t',
                label: '吨位'
              },
              {
                keyQuery: 'remark_t',
                keySave: 'remark_t',
                label: '备注'
              }
            ]
          },
          {
            label: '产管L区切割',
            children: [
              {
                keyQuery: 'planNumber',
                keySave: 'planNumber',
                label: '计划块数'
              },
              {
                keyQuery: 'actualNumberL',
                keySave: 'actualNumberL',
                label: '实际块数'
              },
              {
                keyQuery: 'tonnageL',
                keySave: 'tonnageL',
                label: '吨位'
              },
              {
                keyQuery: 'actualNumberR',
                keySave: 'actualNumberR',
                label: '实际块数'
              },
              {
                keyQuery: 'tonnageR',
                keySave: 'tonnageR',
                label: '吨位'
              },
              {
                keyQuery: 'remark',
                keySave: 'remark',
                label: '备注'
              }
            ]
          }
        ]
      },
      tableObj3: {
        url: {
          save: mtpRectify1and2Save,
          list: mtpRectify1and2
        },
        setting: [
          {
            label: '1#矫',
            children: [
              {
                keyQuery: 'classes',
                keySave: 'classes',
                label: '班次'
              },
              {
                keyQuery: 'planNumber_1',
                keySave: 'planNumber_1',
                label: '计划块数'
              },
              {
                keyQuery: 'actualNumber_1',
                keySave: 'actualNumber_1',
                label: '实际块数'
              },
              {
                keyQuery: 'tonnage_1',
                keySave: 'tonnage_1',
                label: '吨位'
              },
              {
                keyQuery: 'remark_1',
                keySave: 'remark_1',
                label: '备注'
              }
            ]
          },
          {
            label: '2#矫',
            children: [
              {
                keyQuery: 'planNumber_2',
                keySave: 'planNumber_2',
                label: '计划块数'
              },
              {
                keyQuery: 'actualNumber_2',
                keySave: 'actualNumber_2',
                label: '实际块数'
              },
              {
                keyQuery: 'tonnage_2',
                keySave: 'tonnage_2',
                label: '吨位'
              },
              {
                keyQuery: 'remark_2',
                keySave: 'remark_2',
                label: '备注'
              }
            ]
          }
        ]
      },
      tableObj2: {
        url: {
          save: mtpPostWorkInProgress2Save,
          list: mtpPostWorkInProgress2
        },
        setting: [
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次'
          },
          {
            keyQuery: 'planNumber',
            keySave: 'planNumber',
            label: '计划块数'
          },
          {
            keyQuery: 'actualNumber',
            keySave: 'actualNumber',
            label: '实际块数'
          },
          {
            keyQuery: 'actualTonnage',
            keySave: 'actualTonnage',
            label: '实际吨位'
          },
          {
            keyQuery: 'qualifiedNumber',
            keySave: 'qualifiedNumber',
            label: '合格块数'
          },
          {
            keyQuery: 'qualifiedTonnage',
            keySave: 'qualifiedTonnage',
            label: '合格吨位'
          },
          {
            keyQuery: 'remark',
            keySave: 'remark',
            label: '备注'
          }
        ]
      },
      tableObj4: {
        url: {
          save: mtpOfflineFlawDetectionSave,
          list: mtpOfflineFlawDetection
        },
        setting: [
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次'
          },
          {
            keyQuery: 'planNumber',
            keySave: 'planNumber',
            label: '计划块数'
          },
          {
            keyQuery: 'actualNumber',
            keySave: 'actualNumber',
            label: '实际块数'
          },
          {
            keyQuery: 'actualTonnage',
            keySave: 'actualTonnage',
            label: '实际吨位'
          },
          {
            keyQuery: 'qualifiedNumber',
            keySave: 'qualifiedNumber',
            label: '合格块数'
          },
          {
            keyQuery: 'qualifiedTonnage',
            keySave: 'qualifiedTonnage',
            label: '合格吨位'
          },
          {
            keyQuery: 'remark',
            keySave: 'remark',
            label: '备注'
          }
        ]
      },
      //班组轧制节奏
      radio: '30',
      rollingRhythmData: [
        {
          team: '甲',
          Day30: 0,
          Month30: 0,
          Day60: 0,
          Month60: 0
        },
        {
          team: '乙',
          Day30: 0,
          Month30: 0,
          Day60: 0,
          Month60: 0
        },
        {
          team: '丙',
          Day30: 0,
          Month30: 0,
          Day60: 0,
          Month60: 0
        },
        {
          team: '丁',
          Day30: 0,
          Month30: 0,
          Day60: 0,
          Month60: 0
        },
        {
          team: '合计',
          Day30: 0,
          Month30: 0,
          Day60: 0,
          Month60: 0,
          total: 0
        }
      ],

      //相关方考核
      assessData: [
        {
          team: '甲',
          sp: 0,
          dpp: 0,
          actualBlocks: 0,
          actualTonnage: 0,
          machineHour: 0,
          jobRate: 0
        },
        {
          team: '乙',
          sp: 0,
          dpp: 0,
          actualBlocks: 0,
          actualTonnage: 0,
          machineHour: 0,
          jobRate: 0
        },
        {
          team: '丙',
          sp: 0,
          dpp: 0,
          actualBlocks: 0,
          actualTonnage: 0,
          machineHour: 0,
          jobRate: 0
        },
        {
          team: '丁',
          sp: 0,
          dpp: 0,
          actualBlocks: 0,
          actualTonnage: 0,
          machineHour: 0,
          jobRate: 0
        },
        {
          team: '合计',
          sp: 0,
          dpp: 0,
          actualBlocks: 0,
          actualTonnage: 0,
          machineHour: 0,
          jobRate: 0
        }
      ],

      //原因说明
      textarea: '',
      textareaCopy: '',

      //弹框统一
      dialogBox: false,
      title: '',
      //弹框统一表字段
      Header: [],
      //弹框统一表数据
      formData: [],
      //上传Excel
      fileList: []
    }
  },

  created() {
    this.getRollDecideData()
    setTimeout(() => {
      this.getRollingRhythm()
    }, 500)
    this.getAssess()
    this.getTextarea()
  },
  methods: {
    //获取当月轧制/终判数据
    async getRollDecideData() {
      let res = await post(ROLLDECIDE, {
        selTime: this.selTime
      })
      // console.log('月轧制/终判数据', res)

      if (res.data.length != 0) {
        this.rollDecideData = res.data
      }
    },

    //班组轧制节奏30秒、60秒选择数据
    async getRollingRhythm() {
      let XData = []
      let YData1 = []
      let YData2 = []
      let res = await post(ROLLINGRHYTHM, {
        selTime: this.selTime
      })

      // console.log('班组轧制节奏', res)
      if (res.data.length != 0) {
        this.rollingRhythmData = res.data

        res.data.forEach(item => {
          XData.push(item.team)
          if (this.radio == 30) {
            YData1.push(item.Day30)
            YData2.push(item.Month30)
          } else if (this.radio == 60) {
            YData1.push(item.Day60)
            YData2.push(item.Month60)
          }
        })

        this.getEcharts(XData, YData1, YData2)
      }
    },

    //相关方考核数据
    async getAssess() {
      let res = await post(ASSESS, {
        selTime: this.selTime
      })

      // console.log('相关方考核', res)
      if (res.data.length != 0) {
        this.assessData = res.data
      }
    },

    //原因说明数据
    async getTextarea() {
      let res = await post(CAUSE_EXPLAIN, {
        selTime: this.selTime
      })

      // console.log('原因说明', res)
      if (res.data.length != 0) {
        this.textarea = res.data[0].textarea
      }
    },

    //弹框
    openView(nub) {
      this.dialogBox = true
      if (nub == 1) {
        this.title = '当月轧制/终判'
        this.Header = [
          {
            label: '项目',
            prop: 'project'
          },
          {
            label: '计划产量(吨)',
            prop: 'planYield'
          },
          {
            label: '累计产量(吨)',
            prop: 'accruedYield'
          },
          {
            label: '时间进度(%)',
            prop: 'timeProgress'
          },
          {
            label: '产量进度(%)',
            prop: 'yieldProgress'
          },
          {
            label: '日需均产(吨)',
            prop: 'averageYield'
          },
          {
            label: '月预计产量(吨)',
            prop: 'monYield'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.rollDecideData))
      } else if (nub == 2) {
        this.title = '班组轧制节奏'
        this.Header = [
          {
            label: '班组',
            prop: 'team'
          },
          {
            label: '30秒当日',
            prop: 'Day30'
          },
          {
            label: '30秒当月',
            prop: 'Month30'
          },
          {
            label: '60秒当日',
            prop: 'Day60'
          },
          {
            label: '60秒当月',
            prop: 'Month60'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.rollingRhythmData))
      } else if (nub == 3) {
        this.title = '相关方考核'
        this.Header = [
          {
            label: '班组',
            prop: 'team'
          },
          {
            label: '进度计划产量',
            prop: 'sp'
          },
          {
            label: '动态计划产量',
            prop: 'dpp'
          },
          {
            label: '实际块数',
            prop: 'actualBlocks'
          },
          {
            label: '实际吨位',
            prop: 'actualTonnage'
          },
          {
            label: '机时产量',
            prop: 'machineHour'
          },
          {
            label: '作业率',
            prop: 'jobRate'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.assessData))
      } else if (nub == 4) {
        this.title = '原因说明'
        this.textareaCopy = JSON.parse(JSON.stringify(this.textarea))
      }
    },

    //添加行
    addNewRow() {
      let row = {}
      this.Header.forEach(item => {
        row[item.prop] = ''
      })

      this.formData.push(row)
    },

    //删除行
    delRow(indexs) {
      this.formData.forEach((item, index) => {
        if (indexs == index) {
          this.formData.splice(index, 1)
        }
      })
    },

    //下载模板
    DownloadExcel() {
      let data = [{}]
      this.Header.forEach(item => {
        data[0][item.prop] = item.label
      })

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `${this.title}.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          `${this.title}.xlsx`
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      let header = {}
      let letter = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      this.Header.forEach((item, index) => {
        header[item.prop] = letter[index]
      })

      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, header)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        console.log(list)

        list.forEach(item => {
          item.selTime = this.selTime
          this.formData.push(item)
        })
      })
    },

    //保存新增数据
    async saveData() {
      let res
      if (this.title == '当月轧制/终判') {
        res = await post(ROLLDECIDE_SAVE, {
          selTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '班组轧制节奏') {
        res = await post(ROLLINGRHYTHM_SAVE, {
          selTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '相关方考核') {
        res = await post(ASSESS_SAVE, {
          selTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '原因说明') {
        res = await post(CAUSE_EXPLAIN_SAVE, {
          selTime: this.selTime,
          data: this.textareaCopy
        })
      }
      // console.log('保存', res)
      if (res.status == 1) {
        this.$message.success('保存成功')
        if (this.title == '当月轧制/终判') {
          this.getRollDecideData()
        } else if (this.title == '班组轧制节奏') {
          this.getRollingRhythm()
        } else if (this.title == '相关方考核') {
          this.getAssess()
        } else if (this.title == '原因说明') {
          this.getTextarea()
        }

        this.closeDialogBox()
      }
    },

    //关闭弹框
    closeDialogBox() {
      this.dialogBox = false
      this.title = ''
      this.Header = []
      this.formData = []
      this.textareaCopy = ''
    },

    //班组轧制节奏柱状图
    getEcharts(XData, YData1, YData2) {
      echarts.init(document.getElementById('Team')).dispose() // 销毁实例
      // 找到容器
      let dayEcharts = echarts.init(document.getElementById('Team'))

      // 开始渲染
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function(params) {
            let name1 =
              params[0].marker +
              params[0].seriesName +
              ':' +
              params[0].data +
              '%'
            let name2 =
              params[1].marker +
              params[1].seriesName +
              ':' +
              params[1].data +
              '%'

            return params[0].axisValue + '<br>' + name1 + '<br>' + name2
          }
        },
        legend: {
          data: [this.radio + '秒当日', this.radio + '秒当月'],
          textStyle: {
            fontSize: 14, //字体大小
            color: '#ffffff' //字体颜色
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          top: '12%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: XData,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#FFCC22',
              width: 0,
              type: 'solid'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '%',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#FFCC22',
              width: 0,
              type: 'solid'
            }
          }
        },
        series: [
          {
            data: YData1,
            type: 'bar',
            name: this.radio + '秒当日',
            barWidth: '25%',
            itemStyle: {
              normal: {
                color: '#55C6D4',
                label: {
                  show: true, //开启显示
                  position: 'top', //在上方显示
                  textStyle: {
                    //数值样式
                    color: 'white',
                    fontSize: 14
                  }
                }
              }
            }
          },
          {
            data: YData2,
            type: 'bar',
            name: this.radio + '秒当月',
            barWidth: '25%',
            itemStyle: {
              normal: {
                color: '#A146B0',
                label: {
                  show: true, //开启显示
                  position: 'top', //在上方显示
                  textStyle: {
                    //数值样式
                    color: 'white',
                    fontSize: 14
                  }
                }
              }
            }
          }
        ]
      }

      dayEcharts.setOption(option)
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  .EchartsBox {
    height: 380px;
    .setRadio {
      /deep/.el-radio {
        color: white;
      }
    }
  }
  .border-wrapper {
    margin-bottom: 15px;
  }
  /deep/.el-textarea__inner {
    background-color: #041a21;
    border: 1px solid #1fc6ff;
    color: white;
    font-size: 14px;
    height: calc(100vh - 670px);
  }
}

.btn {
  /deep/.el-button {
    font-size: 15px;
    padding: 4px 15px;
    border-radius: 4px;
  }
}

/deep/ .el-table .el-table__cell {
  font-size: 19px;
}

/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}
.full-height {
  display: flex;
  flex-direction: column;
}
</style>
