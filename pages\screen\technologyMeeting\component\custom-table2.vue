<template>
  <div class="full-height">
    <screen-border :title="title">
      <template v-slot:headerRight>
        <slot name="headerRight"/>
        <template v-if="showEdit">
          <span
            v-command="'/screen/technologyMeeting/edit'"
            class="screen-btn"
            @click="dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            操作
          </span>
        </template>

      </template>
      <slot name="content"/>
      <div
        v-if="showTable"
        ref="table1"
        class="scroll-wrapper">
        <el-table
          v-loading="loading"
          :data="showGridData"
          :max-height="maxHeight"
          :span-method="spanMethod"
          :row-class-name="tableRowClass"
          :cell-class-name="tableCellClass"
          :class="tableClass"
          class="center-table font-table"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false && !item.hideText">
              <el-table-column
                v-if="item.children"
                :key="index"
                :width="item.width || ''"
                :property="item.keySave"
                :label="item.label"
                :align="item.align">
                <template
                  v-for="(cItem, cIndex) in item.children">
                  <template v-if="item.inputType === 'textarea'">
                    <el-table-column
                      :key="cIndex"
                      :width="cItem.width || ''"
                      :property="cItem.keySave"
                      :label="cItem.label"
                      :class-name="cItem.class"
                      :align="cItem.align">
                      <template v-slot="{ row }">
                        <div
                          slot="content"
                          v-html="formatText(row[cItem.keySave], cItem.split)"
                        />
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      :key="cIndex"
                      :width="cItem.width || ''"
                      :property="cItem.keySave"
                      :label="cItem.label"
                      :class-name="cItem.class"
                      :align="cItem.align"/>
                  </template>
                </template>
              </el-table-column>
              <template v-else>
                <el-table-column
                  v-if="item.type === 'index'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                />
                <!-- 文件上传-->
                <el-table-column
                  v-else-if="item.type === 'file'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                >
                  <template v-slot="{ row }">
                    <template v-if="row[item.keySave]">
                      <a
                        style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                        @click="clickProjectDownloadItem(row)">下载</a>
                    </template>
                  </template>
                </el-table-column>
                <template v-else>
                  <template v-if="item.inputType === 'textarea'">
                    <el-table-column
                      :key="index"
                      :width="item.width || ''"
                      :property="item.keySave"
                      :label="item.label"
                      :class-name="item.class"
                      :align="item.align">
                      <template v-slot="{ row }">
                        <div
                          slot="content"
                          v-html="formatText(row[item.keySave], item.split)"
                        />
                      </template>
                    </el-table-column>
                  </template>
                
                  <template v-else>
                    <template v-if="item.keySave === 'matters_info'">
                      <el-table-column
                        :key="index"
                        :label="item.label"
                        type="index"
                        width="200"
                      >
                        <template v-slot="{row}">
                          <text-display :text="row.matters_info"/>
                        </template>
                      </el-table-column>
                    </template>
                    <template v-else-if="item.keySave === 'objective_result'">
                      <el-table-column
                        :key="index"
                        :label="item.label"
                        type="index"
                        width="200"
                      >
                        <template v-slot="{row}">
                          <text-display :text="row.objective_result"/>
                        </template>
                      </el-table-column>
                    </template>
                    <template v-else>
                      <el-table-column
                        :key="index"
                        :width="item.width || ''"
                        :property="item.keySave"
                        :label="item.label"
                        :class-name="item.class"
                        :align="item.align"
                        sortable/>
                    </template>
                  </template>
                </template>
              </template>
            </template>
          </template>
        </el-table>
      </div>
    </screen-border>
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      z-index="99999"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEdit">
              <el-dropdown @command="handleProcessedCommand($event)">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportTable">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <el-form :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="gridData"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.children"
                :key="index"
                :width="item.width || ''"
                :label="item.label">
                <template
                  v-for="(cItem, cIndex) in item.children">
                  <el-table-column
                    :key="cIndex"
                    :width="cItem.width || ''"
                    :property="cItem.keySave"
                    :label="cItem.label">
                    <template v-slot="{ row }">
                      <template v-if="cItem.inputType === 'textarea'">
                        <el-input
                          v-model="row[cItem.keySave]"
                          :rows="4"
                          type="textarea"
                        />
                      </template>
                      <template v-else>
                        <el-input v-model="row[cItem.keySave]"/>
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </el-table-column>
              <template v-else>
                <el-table-column
                  v-if="item.type === 'index'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                />
                <el-table-column
                  v-else-if="item.type === 'file'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                >
                  <template v-slot="{ row, $index }">
                    <template v-if="row[item.keySave]">
                      <a
                        style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                        @click="clickProjectDownloadItem(row)">下载</a>
                      <a
                        style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                        @click="handleImgDelete(row[item.keySave], $index)">删除</a>
                    </template>
                    <el-upload
                      v-else
                      ref="upload"
                      :auto-upload="false"
                      :http-request="httpRequest"
                      :on-change="(file, fileList) => handleChange(file, fileList, $index)"
                      :show-file-list="false"
                      :before-upload="beforeUpload"
                      multiple
                      action="#"
                      style="display: inline"
                      @click.native="editIndex = row.index">
                      <el-button
                        size="small"
                        type="primary">点击上传</el-button>
                    </el-upload>
                  </template>
                </el-table-column>
                <template v-else>
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label">
                    <template v-slot="{ row }">
                      <template v-if="item.inputType === 'textarea'">
                        <el-input
                          v-model="row[item.keySave]"
                          :rows="4"
                          type="textarea"
                        />
                      </template>
                      <template v-else-if="item.inputType === 'date'">
                        <el-date-picker
                          v-model="row[item.keySave]"
                          :size="'mini'"
                          :value-format="'yyyy-MM-dd'"
                          type="date"
                          class="screen-input"/>
                      </template>
                      <template v-else>
                        <el-input v-model="row[item.keySave]"/>
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </template>
            </template>
          </template>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index)">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEdit"
          class="screen-btn"
          @click="addGridData()">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import { qmsQualityQuery, qmsQualitySave } from '@/api/screen'
import { findOneUserByUserNo, orgListByCode1 } from '@/api/system'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { deleteFileByIds, downloadFileById, uploadFile } from '@/api/system'
import TextDisplay from '@/pages/screen/technologyMeeting/component/text-display'

export default {
  name: 'custom-table',
  components: { ScreenBorder, TextDisplay },
  props: {
    title: {
      type: String,
      default: ''
    },
    setting: {
      type: Array,
      default: function() {
        return []
      }
    },
    tableRowClass: {
      type: Function,
      default: function() {
        return ''
      }
    },
    tableCellClass: {
      type: Function,
      default: function() {
        return ''
      }
    },
    mergeSet: {
      type: Object,
      default: function() {
        return {}
      }
    },
    selectDate: {
      type: String,
      default: ''
    },
    tableClass: {
      type: String,
      default: ''
    },
    urlList: {
      type: String,
      default: ''
    },
    urlSave: {
      type: String,
      default: ''
    },
    showTable: {
      type: Boolean,
      default: true
    },
    showEdit: {
      type: Boolean,
      default: true
    },
    heightAuto: {
      type: Boolean,
      default: true
    }
  },
  data: function() {
    return {
      cDate: '',
      orgName: '',
      loading: false,
      dialogVisible: false,
      showGridData: [],
      gridData: [],
      editIndex: null,
      importDate: null,
      importDateVisible: false,
      importFunName: '',
      mergeArr: [],
      spanArr: {},
      position: 0,
      maxHeight: null,
      baseURL: 'http://172.25.63.67:9800/' + downloadFileById,
      ABC: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'K'
      ]
    }
  },
  computed: {
    canEdit: function() {
      // console.log(moment().format('yyyy-MM-DD'), this.cDate)
      return (
        moment().format('yyyy-MM-DD') <=
        moment(this.cDate)
          .subtract(-1, 'day')
          .format('yyyy-MM-DD')
      )
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getData()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.getData()
    this.findOneUserByUserNo()
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    // 导入文件
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = this.ABC[index]
      })
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, obj)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.gridData = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    // 导出表格
    exportTable() {
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = item.label
      })
      const data = [obj].concat(
        _.cloneDeep(
          this.gridData.map(item => {
            const objRow = {}
            this.setting.forEach(set => {
              objRow[set.keySave] = item[set.keySave]
            })
            return objRow
          })
        )
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `${this.title}（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 获取数据
    getData() {
      post(this.urlList, {
        setDate: this.cDate
        //   date: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.showGridData = res.data.map(item => {
          const obj = {}
          this.setting.forEach(set => {
            if (set.children && set.children.length) {
              set.children.forEach(child => {
                obj[child.keySave] = !isNaN(Number(item[child.keyQuery]))
                  ? Number(item[child.keyQuery])
                  : item[child.keyQuery]
              })
            } else {
              obj[set.keySave] = !isNaN(Number(item[set.keyQuery]))
                ? Number(item[set.keyQuery])
                : item[set.keyQuery]
            }
          })
          return obj
        })
        this.gridData = _.cloneDeep(this.showGridData)
        this.$nextTick(() => {
          this.$emit('change', this.showGridData)
        })
      })
    },
    // 更新数据
    saveData() {
      this.loading = true
      // 数据信息
      const params = {
        setDate: this.cDate,
        data: this.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(this.urlSave, params).then(res => {
        //
        this.loading = false
        if (res.status == 1) {
          this.$message.success('保存成功！')
          this.dialogVisible = false
          this.getData()
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    // 导入日期数据
    importData(date) {
      post(this.urlList, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.gridData = res.data.map(item => {
          const obj = {}
          this.setting.forEach(set => {
            obj[set.keySave] = item[set.keyQuery]
          })
          return obj
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 执行导入
    importHistoryData() {
      this.importData(this.importDate)
      this.importDateVisible = false
    },
    // 下拉菜单指令
    handleProcessedCommand(command) {
      if (command === 'yesterday') {
        this.importData(
          this.$moment(this.cDate)
            .subtract(1, 'day')
            .format('yyyy-MM-DD')
        )
      } else {
        this.importDate = this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
        this.importDateVisible = true
      }
    },
    // 数据管理
    clearGridData() {
      this.gridData = []
    },
    findListByOrgCode(param) {
      post(orgListByCode1, {
        orgCode: param
      }).then(res => {
        if (res.success) {
          this.orgName = res.data.orgAllName
          //  console.log('bbb', res)
        }
      })
    },
    findOneUserByUserNo() {
      post(findOneUserByUserNo, {
        userNo: localStorage.getItem('userId')
      }).then(res => {
        if (res.success) {
          //  this.userName = res.data.userName
          //  console.log('aaa', res.data.orgCode)
          this.findListByOrgCode(res.data.orgCode)
        }
      })
    },
    addGridData() {
      this.gridData.push({ submit_date: this.cDate, need_depar: this.orgName })
      this.gridData.push()
      console.log('this.gridData', this.gridData)
      // console.log('windows', window.cookie)
    },
    delGridData(index) {
      this.gridData.splice(index, 1)
    },
    // 日期改变推送
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    // 计算需要合并的单元格
    formatSpanData(data) {
      this.mergeArr.forEach(keyName => {
        this.spanArr[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
    },
    // 合并单元格
    handleObjectSpan({ row, column, rowIndex, columnIndex }) {
      // 需要合并的列
      // [0, 1, 2].includes(columnIndex ), 表示合并前三列
      if (this.mergeArr.includes(column.property)) {
        const _row = this.spanArr[column.property][rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 生成带换行数据
    formatText(text, split) {
      if (!text) {
        return ''
      }
      if (split) text = text.split(split).join('\n')
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },
    // 计算高度
    calculate() {
      this.showTable &&
        this.heightAuto &&
        (this.maxHeight = this.$refs.table1.offsetHeight)
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (!Object.keys(this.mergeSet).length) return [1, 1]
      if (this.mergeSet[rowIndex + '-' + columnIndex] !== undefined) {
        console.log(rowIndex + '-' + columnIndex)
        return !this.mergeSet[rowIndex + '-' + columnIndex]
          ? [0, 0]
          : this.mergeSet[rowIndex + '-' + columnIndex]
      }
      return [1, 1]
    },
    clickProjectDownloadItem(row) {
      if (row.fileid) {
        window.open(this.baseURL + row.fileid)
      } else {
        this.$message.error('没有文件url无法下载！')
      }
    },
    beforeUpload() {},
    httpRequest(params) {},
    async handleChange(file, fileList, index) {
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        return this.$message.error('上传文件大小不能超过 10MB!')
      }
      const formData = new FormData()
      formData.append('files', file.raw)
      post(uploadFile, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        if (res.success) {
          this.$message.success('文件上传成功！')
          const obj = this.gridData[index]
          this.gridData.splice(
            index,
            1,
            Object.assign({}, obj, {
              fileid: res.data[0].id
            })
          )
          console.log(this.gridData)
        } else {
          this.$message.warning('文件上传失败！')
          this.loading = false
        }
      })
    },
    async handleImgDelete(file, index) {
      const del = await post(deleteFileByIds, { ids: [file] })
      if (del.success) {
        this.$message.success('文件删除成功！')
        const obj = this.gridData[index]
        this.gridData.splice(
          index,
          1,
          Object.assign({}, obj, {
            fileid: ''
          })
        )
      }
    }
  }
}
</script>

<style scoped lang="less">
// 大屏按钮
.screen-btn {
  display: inline-block;
  min-width: 68px;
  height: 28px;
  padding: 0 5px;
  background: rgba(31, 198, 255, 0.3);
  border: 1px solid #1fc6ff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  color: #fff;
  &:hover {
    background: rgba(31, 198, 255, 0.6);
    border: 1px solid #1fc6ff;
  }
}
.scroll-wrapper {
  height: 100%;
  overflow: auto;
}
/deep/ .red {
  color: #ff0000;
}
/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}
/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
/deep/ .el-table tr.total {
  background: #0a4456;
}
</style>
