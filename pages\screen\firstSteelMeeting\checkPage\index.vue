<!--点检-->
<template>
  <div class="content">
    <div
      class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <screen-border-multi :title="'设备安全提升'">
            <template v-slot:headerRight>
              <span
                class="screen-btn"
                @click="CheckUnionData.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <template v-slot:default>
              <div
                ref="table1"
                class="chart-wrapper">
                <el-table
                  v-loading="CheckUnionData.loading"
                  :data="CheckUnionData.showGridData"
                  height="450"
                  border>
                  <el-table-column
                    type="index"
                    label="序号"
                    width="50"/>
                  <el-table-column
                    show-overflow-tooltip
                    label="项目"
                    width="100">
                    <template slot-scope="scope">
                      <div>{{ scope.row.progectName }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="责任人"
                    width="100">
                    <template slot-scope="scope">
                      <div>{{ scope.row.chargePerson }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="施工单位">
                    <template slot-scope="scope">
                      <div>{{ scope.row.workUnit }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="完成时间">
                    <template slot-scope="scope">
                      <div>{{ scope.row.completionDate }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="费用来源">
                    <template slot-scope="scope">
                      <div>{{ scope.row.fundsSource }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    width="100"
                    label="当前进度">
                    <template slot-scope="scope">
                      <div>{{ scope.row.currentProgress }}</div>
                    </template>
                    <!-- <template slot-scope="scope">
                      <div :style="{color:scope.row.rectification==='未整改'?'#FF2855':scope.row.rectification==='已整改'?'#19BE6B':''}">{{ scope.row.rectification }}</div>
                    </template> -->
                  </el-table-column>
                  <!-- <el-table-column
                    show-overflow-tooltip
                    width="110"
                    label="整改时间">
                    <template slot-scope="scope">
                      <div>{{ scope.row.rectificationTime }}</div>
                    </template>
                  </el-table-column> -->
                  <el-table-column
                    show-overflow-tooltip
                    width="300"
                    label="需补事项">
                    <template slot-scope="scope">
                      <div>{{ scope.row.coordinationItem }}</div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>

          </screen-border-multi>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div
      class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <screen-border-multi :title="'线上点检'">
            <template v-slot:default>
              <div
                ref="table2"
                class="chart-wrapper">
                <el-table
                  v-loading="checkOnlineLoading"
                  :data="checkOnlineList"
                  height="450"
                  border>
                  <el-table-column
                    type="index"
                    label="序号"
                    width="50"/>
                  <el-table-column
                    show-overflow-tooltip
                    label="分类名称">
                    <template slot-scope="scope">
                      <div>{{ scope.row.name }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="启用设备路线总数">
                    <template slot-scope="scope">
                      <div>{{ scope.row.routeTotal }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="路线未完成数">
                    <template slot-scope="scope">
                      <div>{{ scope.row.routeNotChked }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="路线漏检率">
                    <template slot-scope="scope">
                      <div>{{ scope.row.routeNotChkedBate }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="任务总数">
                    <template slot-scope="scope">
                      <div>{{ scope.row.totalCount }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="任务漏检数">
                    <template slot-scope="scope">
                      <div>{{ scope.row.notChked }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="漏检率">
                    <template slot-scope="scope">
                      <div :style="{color:scope.row.notChkedBate==='0%'?'':'#FF2855'}">{{ scope.row.notChkedBate }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="完成率">
                    <template slot-scope="scope">
                      <div>{{ scope.row.chkedBate }}</div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>

          </screen-border-multi>
        </el-col>
      </el-row>
    </div>

    <!--设备安全提升-->
    <el-dialog
      :visible.sync="CheckUnionData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="设备安全提升">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('CheckUnionData')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importCheckUnionData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportCheckUnionData">
              导出
            </span>
            <span
              class="screen-btn"
              @click="saveCheckUnionData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          设备安全提升
        </div>
      </template>
      <el-form >
        <el-table
          v-loading="CheckUnionData.loading"
          :data="CheckUnionData.gridData"
          :height="'calc(100vh - 345px)'"
          border>
          <el-table-column
            property="progectName"
            label="项目">
            <template v-slot="{ row }">
              <el-input
                v-model="row.progectName"
              />
            </template>
          </el-table-column>
          <el-table-column
            property="chargePerson"
            label="责任人">
            <template v-slot="{ row }">
              <el-input v-model="row.chargePerson" />
            </template>
          </el-table-column>
          <el-table-column
            property="workUnit"
            label="施工单位">
            <template v-slot="{ row }">
              <el-input v-model="row.workUnit" />
            </template>
          </el-table-column>
          <el-table-column
            property="completionDate"
            label="完成时间">
            <template v-slot="{ row }">
              <el-date-picker
                v-model="row.completionDate"
                :clearable="false"
                :size="'mini'"
                :value-format="'yyyy-MM-dd'"
                class="screen-input"/>
                <!-- <el-input v-model="row.rectificationTime" />-->
            </template>
          </el-table-column>
          <el-table-column
            property="fundsSource"
            label="费用来源">
            <template v-slot="{ row }">
              <el-input v-model="row.fundsSource" />
            </template>
          </el-table-column>
          <!-- <el-table-column
            property="memo"
            label="整改情况">
            <template v-slot="{ row }">
              <el-select
                v-model="row.rectification"
                class="screen-input"
                placeholder="请选择">
                <el-option
                  v-for="item in dealList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"/>
              </el-select>
            </template>
          </el-table-column> -->
          <el-table-column
            property="currentProgress"
            label="当前进度">
            <template v-slot="{ row }">
              <el-input v-model="row.currentProgress" />
            </template>
          </el-table-column>
      
          <el-table-column
            property="coordinationItem"
            label="需协调事项">
            <template v-slot="{ row }">
              <el-input v-model="row.coordinationItem" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                class="screen-btn"
                @click="delGridData($index,'CheckUnionData')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          class="screen-btn"
          @click="addGridData('CheckUnionData')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import FirstSteelChart from '@/pages/screen/firstSteelMeeting/component/first-steel-chart'
import FirstSteelPie from '@/pages/screen/firstSteelMeeting/component/first-steel-pie.vue'
import { post } from '@/lib/Util'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import {
  firstMeetingCheck1,
  firstMeetingCheck2,
  firstMeetingCheck4
} from '@/api/firstMeeting'
import lodash from 'lodash'
export default {
  name: 'checkPage',
  components: {
    ScreenBorder,
    ScreenBorderMulti,
    FirstSteelChart,
    FirstSteelPie
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      checkOnlineLoading: false,
      checkOnlineList: [],
      dialogVisible: false,
      dealList: [
        {
          name: '未整改',
          value: '未整改'
        },
        {
          name: '已整改',
          value: '已整改'
        }
      ],
      CheckUnionData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      originData: [
        {
          id: '',
          areaName: '运行车间',
          routeName: '',
          dangerDesc: '',
          reason: '',
          rectification: '',
          rectificationTime: '',
          remarks: ''
        },
        {
          id: '',
          areaName: '炼钢车间',
          routeName: '',
          dangerDesc: '',
          reason: '',
          rectification: '',
          rectificationTime: '',
          remarks: ''
        },
        {
          id: '',
          areaName: '原料车间',
          routeName: '',
          dangerDesc: '',
          reason: '',
          rectification: '',
          rectificationTime: '',
          remarks: ''
        },
        {
          id: '',
          areaName: '精炼车间',
          routeName: '',
          dangerDesc: '',
          reason: '',
          rectification: '',
          rectificationTime: '',
          remarks: ''
        },
        {
          id: '',
          areaName: '连铸车间',
          routeName: '',
          dangerDesc: '',
          reason: '',
          rectification: '',
          rectificationTime: '',
          remarks: ''
        },
        {
          id: '',
          areaName: '坯料车间',
          routeName: '',
          dangerDesc: '',
          reason: '',
          rectification: '',
          rectificationTime: '',
          remarks: ''
        }
      ]
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.$nextTick(() => {
        this.init()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    // this.init()
  },
  methods: {
    init() {
      this.getCheckUnionData()
      this.getCheckOnlineData()
    },
    //导入昨天数据或者选择的某一天导入数据
    importCheckUnionData(date) {
      post(firstMeetingCheck1, {
        time: date
      }).then(res => {
        //
        this.CheckUnionData.loading = false
        res.data.forEach(item => {
          const checkItem = this.CheckUnionData.gridData.find(
            checkItem => checkItem.progectName === item.progectName
          )
          if (checkItem) {
            // checkItem.id = item.id
            // checkItem.progectName = item.progectName
            checkItem.chargePerson = item.chargePerson
            checkItem.workUnit = item.workUnit
            checkItem.completionDate = item.completionDate
            checkItem.fundsSource = item.fundsSource
            checkItem.currentProgress = item.currentProgress
            checkItem.coordinationItem = item.coordinationItem
          }
        })
        // this.CheckUnionData.gridData = res.data.map(item => {
        //   return {
        //     areaName: item.areaName,
        //     routeName: item.routeName,
        //     dangerDesc: item.dangerDesc,
        //     rectification: item.rectification,
        //     rectificationTime: item.rectificationTime,
        //     remarks: item.remarks
        //   }
        // })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    //导入
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          progectName: 'A',
          chargePerson: 'B',
          workUnit: 'C',
          completionDate: 'D',
          fundsSource: 'E',
          currentProgress: 'F',
          coordinationItem: 'G'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()

        sheet.forEach(item => {
          const checkItem = this.CheckUnionData.gridData.find(
            checkItem => checkItem.progectName === item.progectName
          )
          if (checkItem) {
            // checkItem.id = item.id
            checkItem.progectName = item.progectName
            checkItem.chargePerson = item.chargePerson
            checkItem.workUnit = item.workUnit
            checkItem.completionDate = item.completionDate
            checkItem.fundsSource = item.fundsSource
            checkItem.currentProgress = item.currentProgress
            checkItem.coordinationItem = item.coordinationItem
          }
        })
        // // 表格信息
        // this.CheckUnionData.gridData = sheet.map(item => {
        //   return item
        // })
        this.$message.success('解析成功！')
      })
    },
    //导出
    exportCheckUnionData() {
      const data = [
        {
          progectName: '项目名称',
          chargePerson: '责任人',
          workUnit: '施工单位',
          completionDate: '完成时间',
          fundsSource: '费用来源',
          currentProgress: '当前进度',
          coordinationItem: '需协调事项'
        }
      ].concat(
        _.cloneDeep(this.CheckUnionData.gridData).map(item => {
          return item
        })
      )
      console.log('aaa', data)

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `联合点检（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    //联合点检查询
    getCheckUnionData() {
      const params = {
        time: this.cDate
      }
      this.CheckUnionData.loading = true
      // this.CheckUnionData.gridData = JSON.parse(JSON.stringify(this.originData))
      post(firstMeetingCheck1, params)
        .then(res => {
          //  if (res.data && res.data.length !== 0) {
          //    res.data.forEach(item => {
          //      const checkItem = this.CheckUnionData.gridData.find(
          //        item1 => item1.areaName === item.areaName
          //      )
          //      if (checkItem) {
          //        checkItem.id = item.id
          //        checkItem.routeName = item.routeName
          //        checkItem.dangerDesc = item.dangerDesc
          //        checkItem.reason = item.reason
          //        checkItem.rectification = item.rectification
          //        checkItem.rectificationTime = item.rectificationTime
          //        checkItem.remarks = item.remarks
          //      }
          //    })
          //  }
          this.CheckUnionData.gridData = res.data
          this.CheckUnionData.showGridData = lodash.cloneDeep(
            this.CheckUnionData.gridData
          )
          // this.CheckUnionData.showGridData = res.data.map(item => {
          //   return {
          //     areaName: item.areaName,
          //     routeName: item.routeName,
          //     dangerDesc: item.dangerDesc,
          //     rectification: item.rectification,
          //     rectificationTime: item.rectificationTime,
          //     remarks: item.remarks
          //   }
          // })
          // this.CheckUnionData.gridData = lodash.cloneDeep(
          //   this.CheckUnionData.showGridData
          // )
        })
        .finally(_ => {
          this.CheckUnionData.loading = false
        })
    },
    //保存数据
    saveCheckUnionData() {
      this.CheckUnionData.loading = true
      // 保存钢铁产量信息
      const params = {
        date: this.cDate,
        data: this.CheckUnionData.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(firstMeetingCheck2, params)
        .then(res => {
          if (res.success) {
            this.$message.success('保存成功！')
            this.CheckUnionData.dialogVisible = false
            this.getCheckUnionData()
          }
        })
        .finally(_ => {
          this.CheckUnionData.loading = false
        })
    },
    //线上点检查询
    getCheckOnlineData() {
      const params = {
        setDate: this.cDate
      }
      this.checkOnlineLoading = true
      post(firstMeetingCheck4, params)
        .then(res => {
          if (res.success) {
            this.checkOnlineList = res.data
          }
        })
        .finally(_ => {
          this.checkOnlineLoading = false
        })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
.dialog-body {
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}

.tabs-class {
  display: flex;
  flex-direction: row;
  .tab-pane {
    color: #ffffffbf;
    margin-right: 21px;
  }
  .tab-pane-active {
    color: #ffffff;
  }
  .tab-pane-title-class {
    display: flex;
    flex-direction: column;
    position: relative;
    .tab-pane-img {
      .tab-pane-img2 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
      .tab-pane-img1 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        margin-bottom: 7px;
      }
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }

  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  overflow: auto;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
