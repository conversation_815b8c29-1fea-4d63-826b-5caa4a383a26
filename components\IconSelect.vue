<template>
  <el-popover
    v-model="visible"
    placement="bottom"
    trigger="click"
    width="420"
  >
    <el-input
      v-model="searchKey"
      placeholder="输入关键字搜索图标"
    />
    <div class="icon-list">
      <IconSvg
        v-for="item in showIcons"
        :key="item.iconName"
        :icon-name="item.iconName"
        @click="selectIcon"/>
    </div>
    <el-button slot="reference">选择图标</el-button>
  </el-popover>
</template>

<script>
import { icons } from '@/lib/Icons'
import IconSvg from '@/components/IconSvg'

export default {
  components: { IconSvg },
  data: () => {
    return {
      icons: icons,
      searchKey: null,
      visible: false
    }
  },
  computed: {
    showIcons: function() {
      return this.searchKey === '' || this.searchKey === null
        ? this.icons
        : this.icons.filter(
            item => item.iconName.indexOf(this.searchKey) !== -1
          )
    }
  },
  methods: {
    selectIcon(name) {
      this.$emit('select', name)
      this.visible = false
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
/**/
.search-input {
  width: 80%;
  margin: auto;
  display: block;
  text-align: center;
}

.icon-list {
  height: 300px;
  overflow: auto;
  margin-top: 20px;

  .icon-svg {
    height: 3em;
    width: 3em;
    margin: 10px;
    cursor: pointer;
  }
}
</style>
