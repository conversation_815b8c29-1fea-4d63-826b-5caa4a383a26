<template>
  <div class="bigBox">
    <el-row :gutter="32">
      <el-col :span="12">
        <screen-border title="昨日生产简要评价">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(1,'')">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <el-table
            :data="listData1"
            height="379">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="A1"
              label="班次"
              width="100"
              align="center"/>
            <el-table-column
              prop="B1"
              label="评价结论"
              width="100"
              align="center"/>
            <el-table-column
              prop="C1"
              label="不及格原因"
              align="center"/>
            <el-table-column
              prop="D1"
              label="责任单位"
              width="120"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="大夜班生产情况">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              v-if="radio==1"
              class="screen-btn"
              @click="openView(2,radio)">
              <el-icon class="el-icon-edit-outline" />
              操作
            </span>
            <span
              v-command="'/screen/MpFacDispatch/edit'" 
              v-else
              class="screen-btn"
              @click="setHeadquartersData">
              <el-icon class="el-icon-printer" />
              保存
            </span>
          </template>
          <div class="radioBox">
            <el-radio-group v-model="radio">
              <el-radio :label="0">本部</el-radio>
              <el-radio :label="1">港池</el-radio>
            </el-radio-group>
          </div>
          <!-- 本部 -->
          <div 
            v-if="radio==0" 
            class="headquarters">
            <el-descriptions 
              :column="2" 
              border>
              <el-descriptions-item :span="2">
                <template slot="label">
                  施工安全交底情况
                </template>
                <el-input v-model="listData2.text1"/>
              </el-descriptions-item>
              <el-descriptions-item :span="2">
                <template slot="label">
                  安全检查
                </template>
                <el-input v-model="listData2.text2"/>
              </el-descriptions-item>
              <el-descriptions-item :span="2">
                <template slot="label">
                  产量
                </template>
                <el-descriptions 
                  :column="4" 
                  direction="vertical" 
                  border>
                  <el-descriptions-item label="计划"><el-input v-model="listData2.text3"/></el-descriptions-item>
                  <el-descriptions-item label="实际"><el-input v-model="listData2.text4"/></el-descriptions-item>
                  <el-descriptions-item label="当班预计"><el-input v-model="listData2.text5"/></el-descriptions-item>
                  <el-descriptions-item label="未完成原因"><el-input v-model="listData2.text6"/></el-descriptions-item>
                </el-descriptions>
              </el-descriptions-item>
              <el-descriptions-item :span="2">
                <template slot="label">
                  非计划
                </template>
                <el-descriptions 
                  :column="5" 
                  direction="vertical" 
                  border>
                  <el-descriptions-item label="改判(块/吨)"><el-input v-model="listData2.text7"/></el-descriptions-item>
                  <el-descriptions-item label="协议(块/吨)"><el-input v-model="listData2.text8"/></el-descriptions-item>
                  <el-descriptions-item label="待判(块/吨)"><el-input v-model="listData2.text9"/></el-descriptions-item>
                  <el-descriptions-item label="非计划率(%)"><el-input v-model="listData2.text10"/></el-descriptions-item>
                  <el-descriptions-item label="分析"><el-input v-model="listData2.text11"/></el-descriptions-item>
                </el-descriptions>
              </el-descriptions-item>
              <el-descriptions-item :span="2">
                <template slot="label">
                  当班替代块
                </template>
                <el-input v-model="listData2.text12"/>
              </el-descriptions-item>
              <el-descriptions-item :span="2">
                <template slot="label">
                  成材率
                </template>
                <el-descriptions 
                  :column="3" 
                  direction="vertical" 
                  border>
                  <el-descriptions-item label="目标"><el-input v-model="listData2.text13"/></el-descriptions-item>
                  <el-descriptions-item label="实际(%)"><el-input v-model="listData2.text14"/></el-descriptions-item>
                  <el-descriptions-item label="原因分析"><el-input v-model="listData2.text15"/></el-descriptions-item>
                </el-descriptions>
              </el-descriptions-item>
              <el-descriptions-item :span="2">
                <template slot="label">
                  能耗
                </template>
                <el-descriptions 
                  :column="3" 
                  direction="vertical" 
                  border>
                  <el-descriptions-item label="考核"><el-input v-model="listData2.text16"/></el-descriptions-item>
                  <el-descriptions-item label="实际(%)"><el-input v-model="listData2.text17"/></el-descriptions-item>
                  <el-descriptions-item label="当班热装比/400%占比%(出炉热装比/400%占比)"><el-input v-model="listData2.text15"/></el-descriptions-item>
                </el-descriptions>
              </el-descriptions-item>
              <el-descriptions-item :span="2">
                <template slot="label">
                  作业率
                </template>
                <el-descriptions 
                  :column="5" 
                  direction="vertical" 
                  border>
                  <el-descriptions-item label="计划生产时间"><el-input v-model="listData2.text18"/></el-descriptions-item>
                  <el-descriptions-item label="实际生产时间"><el-input v-model="listData2.text19"/></el-descriptions-item>
                  <el-descriptions-item label="停时"><el-input v-model="listData2.text20"/></el-descriptions-item>
                  <el-descriptions-item label="作业率(%)"><el-input v-model="listData2.text21"/></el-descriptions-item>
                  <el-descriptions-item label="情况说明"><el-input v-model="listData2.text22"/></el-descriptions-item>
                </el-descriptions>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  吊上块数
                </template>
                <el-input v-model="listData2.text23"/>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  吊下块数
                </template>
                <el-input v-model="listData2.text24"/>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  精整剪切块数
                </template>
                <el-input v-model="listData2.text25"/>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  坯料情况
                </template>
                <el-input v-model="listData2.text26"/>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <!-- 港池 -->
          <el-table
            v-else
            :data="listData3"
            height="342">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="A1"
              label="设备"
              width="150"
              align="center"/>
            <el-table-column
              prop="B1"
              label="计划"
              width="150"
              align="center"/>
            <el-table-column
              prop="C1"
              label="实际"
              width="150"
              align="center"/>
            <el-table-column
              prop="D1"
              label="备注"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="当班安全生产简要评价">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(3,'')">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <el-table
            :data="listData4"
            height="calc(100vh - 670px)">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="A1"
              label="单位"
              width="150"
              align="center"/>
            <el-table-column
              prop="B1"
              label="评价"
              align="center"/>
            <el-table-column
              prop="C1"
              label="评价结论(合格/不合格)"
              width="220"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="当班现场督察报告">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(4,'')">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <el-table
            :data="listData5"
            height="calc(100vh - 670px)">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="A1"
              label="类别(安全、质量、生产、设备、其他)"
              width="180"
              align="center"/>
            <el-table-column
              prop="B1"
              label="督察发现"
              align="center"/>
            <el-table-column
              prop="C1"
              label="责任单位"
              width="150"
              align="center"/>
            <el-table-column
              prop="D1"
              label="反馈情况"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
    </el-row>
    
    <!--弹框-->
    <el-dialog
      :visible.sync="dialogBox"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      @close="closeDialogBox">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span> -->
            <span
              v-show="title!='原因说明'"
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download" />
              下载
            </span>
            <span
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-printer" />
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <div>
        <el-table
          id="table"
          :data="formData"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60" />
          <el-table-column
            v-for="(item,index) in Header"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            align="center">
            <template v-slot="{ row }">
              <el-input v-model="row[item.prop]" />
              <span v-show="false">{{ row[item.prop] }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            v-if="title!='中板库坯料报表'&&title!='中板订单坯料报表'" 
            align="center"
            width="100"
            label="操作">
            <template v-slot="{ row, $index }">
              <span
                class="screen-btn"
                @click="delRow($index)">
                <el-icon class="el-icon-delete" />
                删除
              </span>
            </template>
          </el-table-column> -->
        </el-table>

        <!-- <div 
          v-if="title!='中板库坯料报表'&&title!='中板订单坯料报表'" 
          class="text-center">
          <span
            class="screen-btn"
            @click="addNewRow">
            <el-icon class="el-icon-circle-plus-outline" />
            增加数据
          </span>
        </div> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { saveAs } from 'file-saver'
import * as echarts from 'echarts'
import moment from 'moment'

import {} from '@/api/screen'

export default {
  name: 'MainlineProduction',
  components: {
    // SingleBarsChart,
    // SteelBarsChart,
    ScreenBorder,
    ScreenBorderMulti
  },
  props: {
    selTime: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      radio: 0,

      //昨日生产简要评价
      listData1: [
        {
          A1: 'A1',
          B1: 'B1',
          C1: 'C1',
          D1: 'D1'
        }
      ],

      //大夜班生产情况-本部
      listData2: {
        text1: '',
        text2: '',
        text3: '',
        text4: '',
        text5: '',
        text6: '',
        text7: '',
        text8: '',
        text9: '',
        text10: '',
        text11: '',
        text12: '',
        text13: '',
        text14: '',
        text15: '',
        text16: '',
        text17: '',
        text18: '',
        text19: '',
        text20: '',
        text21: '',
        text22: '',
        text23: '',
        text24: '',
        text25: '',
        text26: ''
      },

      //大夜班生产情况-港池
      listData3: [
        {
          A1: 'A1',
          B1: 'B1',
          C1: 'C1',
          D1: 'D1'
        }
      ],

      //当班安全生产简要评价
      listData4: [
        {
          A1: 'A1',
          B1: 'B1',
          C1: 'C1'
        }
      ],

      //当班现场督察报告
      listData5: [
        {
          A1: 'A1',
          B1: 'B1',
          C1: 'C1',
          D1: 'D1'
        }
      ],

      //弹框统一
      dialogBox: false,
      title: '',
      //弹框统一表字段
      Header: [],
      //弹框统一表数据
      formData: [],
      //上传Excel
      fileList: []
    }
  },
  watch: {
    selTime: function() {
      // this.getProduceReport()
      // setTimeout(() => {
      //   this.getHP_reports()
      // }, 500)
      // this.getCT_reports()
      // this.getProducePlan()
    }
  },

  created() {
    // this.getProduceReport()
    // setTimeout(() => {
    //   this.getHP_reports()
    // }, 500)
    // this.getCT_reports()
    // this.getProducePlan()
  },

  methods: {
    //获取重点类别跟踪情况
    async getFocusedType() {
      // let res = await post(PRODUCEREPORT, {
      //   selTime: this.selTime
      // })
      // console.log('重点类别跟踪情况', res)
      // if (res.data.length != 0) {
      //   this.focusedType = res.data
      // }
    },

    //弹框
    openView(nub, tab) {
      this.dialogBox = true
      if (nub == 1 && tab == '') {
        this.title = '昨日生产简要评价'
        this.Header = [
          {
            label: '班次',
            prop: 'A1'
          },
          {
            label: '评价结论',
            prop: 'B1'
          },
          {
            label: '不合格原因',
            prop: 'C1'
          },
          {
            label: '责任单位',
            prop: 'D1'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData1))
      } else if (nub == 2 && tab == 1) {
        this.title = '港池'
        this.Header = [
          {
            label: '设备',
            prop: 'A1'
          },
          {
            label: '计划',
            prop: 'B1'
          },
          {
            label: '实际',
            prop: 'C1'
          },
          {
            label: '备注',
            prop: 'D1'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData3))
      } else if (nub == 3 && tab == '') {
        this.title = '当班安全生产简要评价'
        this.Header = [
          {
            label: '单位',
            prop: 'A1'
          },
          {
            label: '评价',
            prop: 'B1'
          },
          {
            label: '评价结论(合格/不合格)',
            prop: 'C1'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData4))
      } else if (nub == 4 && tab == '') {
        this.title = '当班现场督察报告'
        this.Header = [
          {
            label: '类别(安全、质量、生产、设备、其他)',
            prop: 'A1'
          },
          {
            label: '督察发现',
            prop: 'B1'
          },
          {
            label: '责任单位',
            prop: 'C1'
          },
          {
            label: '反馈情况',
            prop: 'D1'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData5))
      }
    },

    //添加行
    addNewRow() {
      let row = {}
      this.Header.forEach(item => {
        row[item.prop] = ''
      })

      this.formData.push(row)
    },

    //删除行
    delRow(indexs) {
      this.formData.forEach((item, index) => {
        if (indexs == index) {
          this.formData.splice(index, 1)
        }
      })
    },

    //下载模板
    DownloadExcel() {
      let data = [{}]
      this.Header.forEach(item => {
        data[0][item.prop] = item.label
      })

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `${this.title}.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          `${this.title}.xlsx`
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      let header = {}
      let letter = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      this.Header.forEach((item, index) => {
        header[item.prop] = letter[index]
      })

      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, header)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        console.log(list)

        list.forEach(item => {
          item.selTime = this.selTime
          this.formData.push(item)
        })
      })
    },

    //保存新增数据
    async saveData() {
      let res
      if (this.title == '生产报告') {
        res = await post(PRODUCEREPORT_SAVE, {
          selTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '生产日报表') {
        res = await post(PRODUCEDAYTABLE_SAVE, {
          selTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '港池生产报告-柱图') {
        res = await post(HP_REPORTS_SAVE, {
          selTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '港池生产报告-热处理炉') {
        res = await post(HP_HT_STOVE_SAVE, {
          selTime: this.selTime,
          data: this.formData,
          notes: this.textareaCopy
        })
      } else if (this.title == '合同跟踪报告') {
        res = await post(CT_REPORTS_SAVE, {
          selTime: this.selTime,
          data: this.formData,
          notes: this.textareaCopy
        })
      } else if (this.title == '生产计划') {
        res = await post(PRODUCEPLAN_SAVE, {
          selTime: this.selTime,
          data: this.formData
        })
      }

      // console.log('保存', res)
      if (res.status == 1) {
        this.$message.success('保存成功')
        if (this.title == '生产报告') {
          this.getProduceReport()
        } else if (this.title == '生产日报表') {
          this.getProduceDayTable()
        } else if (this.title == '港池生产报告-柱图') {
          setTimeout(() => {
            this.getHP_reports()
          }, 500)
        } else if (this.title == '港池生产报告-热处理炉') {
          this.getHP_HT_stove()
        } else if (this.title == '合同跟踪报告') {
          this.getCT_reports()
        } else if (this.title == '生产计划') {
          this.getProducePlan()
        }

        this.closeDialogBox()
      }
    },

    //大夜班生产情况本部直接保存
    setHeadquartersData() {},

    //关闭弹框
    closeDialogBox() {
      this.dialogBox = false
      this.title = ''
      this.Header = []
      this.formData = []
      this.textareaCopy = ''
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .tabBox {
      display: flex;
      .tab {
        color: #ffffffbf;
        margin-right: 20px;
      }
      .tab_block {
        display: flex;
        flex-direction: column;
        position: relative;
        .tab_img {
          .tab_img2 {
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
          }
          .tab_img1 {
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
            margin-bottom: 7px;
          }
        }
      }
    }
    .border-content {
      height: 380px;
    }
  }
  .EchartsBox {
    height: 380px;
    .setRadio {
      /deep/.el-radio {
        color: white;
      }
    }
  }
  .border-wrapper {
    margin-bottom: 15px;
  }
  /deep/.el-textarea__inner {
    background-color: #041a21;
    border: 1px solid #1fc6ff;
    color: white;
    font-size: 14px;
    height: 70px;
  }
}

//radio样式
.radioBox {
  margin: 5px 0 10px 0;
  .el-radio {
    margin-right: 15px;
    color: #fff;
    margin-bottom: 5px;

    /deep/ .el-radio__label {
      color: #fff;
      font-size: 14px;
    }

    /deep/ .el-radio__input.is-checked .el-radio__inner {
      border-color: #1fc6ff;
      background: #1fc6ff;
    }

    /deep/ .el-radio__input.is-checked + .el-radio__label {
      color: #1fc6ff;
    }

    /deep/ .el-radio__inner {
      background-color: transparent;
      border: 1px solid #fff;
    }
  }
}

//描述列表样式
.headquarters {
  height: 343px;
  overflow: auto;
  /deep/ .el-descriptions--small {
    font-size: 16px;
  }

  /deep/.el-descriptions__body {
    color: white;
    background: transparent;
  }
  /deep/.el-descriptions-item__label.is-bordered-label {
    color: white;
    background: transparent;
  }
  /deep/.el-input__inner {
    background-color: #041a21;
    border: 1px solid #1fc6ff;
    color: white;
  }
}

// table样式
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}

/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}
</style>
