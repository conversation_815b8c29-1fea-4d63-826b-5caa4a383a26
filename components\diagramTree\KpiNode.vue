<template>
  <div
    v-if="treeData.name"
    class="tree-wrapper">
    <div
      v-if="!onlyWarning || treeData.warningStatus"
      :class="{
        'warning': treeData.warningStatus,
        'trend': treeData.trendWarningStatus
      }"
      class="tree-root tree-node">
      <Node
        :key="treeData.id"
        :node="treeData"
        @changeStatus="changeStatus"/>
    </div>
    <template v-if="treeData.children && treeData.children.length && !treeData.hiddenChildren">
      <NodeItem
        :node="treeData.children"
        :only-warning="onlyWarning"
        :rank="rank"
        :level="level"/>
    </template>
  </div>
</template>

<script>
import NodeItem from '@/components/diagramTree/NodeItem'
import { getCoreResultValue } from '@/lib/Util'
import Node from '@/components/diagramTree/Node'
export default {
  name: 'KpiNode',
  components: { Node, NodeItem },
  props: {
    node: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {}
    },
    onlyWarning: {
      type: Boolean,
      default: false
    },
    rank: {
      type: Number,
      default: 3
    },
    level: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      treeData: Object.assign(this.node, {
        hiddenChildren: this.rank < this.level
      })
    }
  },
  methods: {
    changeStatus() {
      console.log(this.treeData)
      // this.$set(this.treeData, 'hiddenChildren', !this.treeData.hiddenChildren)
      this.treeData.hiddenChildren = !this.treeData.hiddenChildren
    }
  }
}
</script>

<style scoped lang="less">
.tree-wrapper {
  min-height: 500px;
  font-size: 0;
  padding: 20px;
  white-space: nowrap;
  overflow: auto;
}
.tree-root {
  &:before {
    display: none;
  }
  &:after {
    display: none;
  }
}
.tree-node {
  position: relative;
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  padding: 5px 15px;
  border-radius: 4px;
  font-size: 18px;
  background: #eff4fd;
  border-left: 3px solid #5e93ed;
  float: left;
  .node-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .node-describe {
      margin-right: 15px;
      small {
        color: #666;
        font-size: 10px;
      }
    }
    .node-arrow {
      cursor: pointer;
    }
  }
  span.result {
    display: block;
    color: #5e93ed;
    font-weight: 600;
    font-size: 20px;
    line-height: 28px;
    height: 28px;
  }
  &.trend {
    border-color: #ffa958;
    background: #fff6ee;
    /deep/ span.result {
      color: #ffa958;
    }
  }
  &.warning {
    border-color: #f56c6c;
    background: #fef0f0;
    /deep/ span.result {
      color: #f56c6c;
    }
  }
}
</style>
