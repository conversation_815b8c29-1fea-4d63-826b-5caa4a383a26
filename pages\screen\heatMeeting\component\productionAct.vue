<template>
  <div class="production-act">
    <div class="search-box">
      <el-form
        ref="form"
        :model="form"
        size="mini"
        class="el-form--inline"
        label-width="120px">
        <el-form-item label="钢板号">
          <el-input
            v-model="form.matid"
            clearable
            class="screen-input"/>
        </el-form-item>
        <el-form-item label="钢种">
          <el-input
            v-model="form.grade"
            class="screen-input"/>
        </el-form-item>
        <el-form-item label="出炉时间">
          <el-date-picker
            v-model="form.disChargeDate"
            class="screen-input"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"/>
        </el-form-item>
        <el-form-item label="入炉时间">
          <el-date-picker
            v-model="form.chargeDate"
            class="screen-input"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"/>
        </el-form-item>
        <el-form-item label="班别">
          <el-select
            v-model="form.shift"
            class="screen-input"
            clearable
            placeholder="请选择班次">
            <el-option
              label="请选择班次"
              value=""/>
            <el-option
              label="白班"
              value="2"/>
            <el-option
              label="小夜班"
              value="3"/>
            <el-option
              label="大夜班"
              value="1"/>
          </el-select>
        </el-form-item>
        <el-form-item label="班组">
          <el-select
            v-model="form.group"
            class="screen-input"
            clearable
            placeholder="请选择班组">
            <el-option
              label="请选择班组"
              value=""/>
            <el-option
              label="甲"
              value="A"/>
            <el-option
              label="乙"
              value="B"/>
            <el-option
              label="丙"
              value="C"/>
            <el-option
              label="丁"
              value="D"/>
          </el-select>
        </el-form-item>
        <el-form-item label="炉座号">
          <el-select
            v-model="form.facid"
            class="screen-input"
            clearable
            placeholder="请选择炉座号">
            <el-option
              label="请选择炉座号"
              value=""/>
            <el-option
              label="5#"
              value="5"/>
            <el-option
              label="6#"
              value="6"/>
          </el-select>
        </el-form-item>
        <el-form-item label="是否升降温">
          <el-select
            v-model="form.temperatureRiseFall"
            clearable
            class="screen-input"
            placeholder="请选择">
            <el-option
              label="请选择"
              value=""/>
            <el-option
              label="是"
              value="1"/>
            <el-option
              label="否"
              value="0"/>
          </el-select>
        </el-form-item>
        <el-form-item label="是否规格转换">
          <el-select
            v-model="form.specificationConversion"
            clearable
            class="screen-input"
            placeholder="请选择">
            <el-option
              label="请选择"
              value=""/>
            <el-option
              label="是"
              value="1"/>
            <el-option
              label="否"
              value="0"/>
          </el-select>
        </el-form-item>
        <el-form-item label="是否装钢延误">
          <el-select
            v-model="form.installationDelay"
            clearable
            class="screen-input"
            placeholder="请选择">
            <el-option
              label="请选择"
              value=""/>
            <el-option
              label="是"
              value="1"/>
            <el-option
              label="否"
              value="0"/>
          </el-select>
        </el-form-item>
        <el-form-item style="margin-left: 60px">
          <el-button
            type="primary"
            @click="onSubmit">查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        :data="tableData"
        :header-cell-style="{'text-align':'center'}"
        :cell-style="{'text-align':'center'}"
        height="850px"
        style="width: 100%">
        <el-table-column
          prop="matid"
          label="钢板号"/>
        <el-table-column
          prop="thickness"
          label="厚度"/>
        <el-table-column
          prop="width"
          label="宽度"/>
        <el-table-column
          prop="length"
          label="长度"/>
        <el-table-column
          prop="weight"
          label="重量"/>
        <el-table-column
          prop="chargeDate"
          label="入炉时间">
          <template slot-scope="scope">
            <span>{{ scope.row.chargeDate | forTimeG }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="disChargeDate"
          label="出炉时间">
          <template slot-scope="scope">
            <span>{{ scope.row.disChargeDate | forTimeG }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="heatGroup"
          label="班组">
          <template slot-scope="scope">
            <span v-if="scope.row.heatGroup == 'A'">甲</span>
            <span v-if="scope.row.heatGroup == 'B'">乙</span>
            <span v-if="scope.row.heatGroup == 'C'">丙</span>
            <span v-if="scope.row.heatGroup == 'D'">丁</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="heatShift"
          label="班别">
          <template slot-scope="scope">
            <span v-if="scope.row.heatShift == '1'">大夜班</span>
            <span v-if="scope.row.heatShift == '2'">白班</span>
            <span v-if="scope.row.heatShift == '3'">小夜班</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="steelGrade"
          label="钢种"/>
        <el-table-column
          prop="plateRollerConveyor"
          label="钢板上辊道时间">
          <template slot-scope="scope">
            <span> {{ scope.row.plateRollerConveyor | forTimeG }}  </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="installationInterval"
          label="装钢间隔">
          <template slot-scope="scope">
            <span> {{ scope.row.installationInterval | forTime }}  </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="loadingForecast"
          label="装钢预报时间">
          <template slot-scope="scope">
            <span> {{ scope.row.loadingForecast | forTime }}  </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="installationDelay"
          label="装钢延误时间">
          <template slot-scope="scope">
            <span> {{ scope.row.installationDelay | forTime }}  </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="tappingForecast"
          label="出钢预报时间">
          <template slot-scope="scope">
            <span> {{ scope.row.tappingForecast | forTime }}  </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="tappingDelay"
          label="出钢延误时间">
          <template slot-scope="scope">
            <span> {{ scope.row.tappingDelay | forTime }}  </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="tappingInterval"
          label="出钢间隔">
          <template slot-scope="scope">
            <span> {{ scope.row.tappingInterval | forTime }}  </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="utilization"
          label="利用率"/>
        <el-table-column
          prop="temperatureRiseFall"
          label="升降温时间">
          <template slot-scope="scope">
            <span> {{ scope.row.temperatureRiseFall | forTime }}  </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="specificationConversion"
          label="规格转换时间">
          <template slot-scope="scope">
            <span> {{ scope.row.specificationConversion | forTime }}  </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="installationSpacing"
          label="装钢间距"/>
        <el-table-column
          prop="stoveLoading"
          label="装炉模式">
          <template slot-scope="scope">
            <span v-if="scope.row.stoveLoading == 0">手动装钢</span>
            <span v-if="scope.row.stoveLoading == 1">自动装钢</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="furnaceDischarge"
          label="出炉模式">
          <template slot-scope="scope">
            <span v-if="scope.row.furnaceDischarge == 0">手动装钢</span>
            <span v-if="scope.row.furnaceDischarge == 1">自动装钢</span>
          </template>
        </el-table-column>
        <!--<el-table-column
          prop="motherBoard"
          label="母板号"/>-->
        <el-table-column
          prop="burnerMalfunctions"
          label="烧嘴故障数"/>
        <el-table-column
          prop="furnaceRollerCurrent"
          label="炉辊电流超限次数"/>
        <el-table-column
          prop="setInsulateTime"
          label="设定保温时间">
          <template slot-scope="scope">
            <span> {{ scope.row.setInsulateTime | forTime }}  </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="actualInsulateTime"
          label="实际保温时间">
          <template slot-scope="scope">
            <span> {{ scope.row.actualInsulateTime | forTime }}  </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="actualInHeatTime"
          label="实际在炉时间">
          <template slot-scope="scope">
            <span> {{ scope.row.actualInHeatTime | forTime }}  </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="setInHeatTime"
          label="设定在炉时间">
          <template slot-scope="scope">
            <span> {{ scope.row.setInHeatTime | forTime }}  </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="enterInsulateTime"
          label="进入保温区时间"/>
          <!--        <el-table-column
        prop="createTime"
        label="createTime"/>
      <el-table-column
        prop="updateTime"
        label="updateTime"/>-->

      </el-table>
    </div>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import moment from 'moment'

export default {
  name: 'productionAct',
  filters: {
    forTimeG(val) {
      return moment(val, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')
    },
    forTime(seconds) {
      // 计算小时
      const h = Math.floor(seconds / 3600)
      // 计算剩余的分钟
      const m = Math.floor((seconds % 3600) / 60)
      // 计算剩余的秒数
      const s = seconds % 60

      // 格式化为两位数的字符串
      const hh = String(h).padStart(2, '0')
      const mm = String(m).padStart(2, '0')
      const ss = String(s).padStart(2, '0')

      // 拼接为 "HH:mm:ss" 格式
      return `${hh}:${mm}:${ss}`
    }
  },
  data() {
    return {
      loading: true,
      tableData: [],
      form: {
        matid: '', //钢板号
        disChargeDate: [
          moment().format('YYYY-MM-DD') + ' 00:00:00',
          moment().format('YYYY-MM-DD') + ' 23:59:59'
        ], //出炉时间
        chargeDate: [], //入炉时间
        group: '', //班组
        shift: '', //班别
        grade: '', //钢种
        temperatureRiseFall: '', //升降温时间
        specificationConversion: '', //规格转换时间
        installationDelay: '', //装钢延误时间
        facid: '' //炉座号
      }
    }
  },
  mounted() {
    this.onSubmit()
  },
  methods: {
    onSubmit() {
      this.loading = true
      console.log(this.form)
      //单独处理时间
      let timeTemp = {
        startDisChargeDate: '',
        endDisChargeDate: '',
        startChargeDate: '',
        endChargeDate: ''
      }
      if (this.form.disChargeDate) {
        if (this.form.disChargeDate[0]) {
          timeTemp.startDisChargeDate = moment(
            this.form.disChargeDate[0]
          ).format('YYYYMMDDHHmmss')
        } else {
          timeTemp.startDisChargeDate = ''
        }
        if (this.form.disChargeDate[1]) {
          timeTemp.endDisChargeDate = moment(this.form.disChargeDate[1]).format(
            'YYYYMMDDHHmmss'
          )
        } else {
          timeTemp.endDisChargeDate = ''
        }
      }
      if (this.form.chargeDate) {
        if (this.form.chargeDate[0]) {
          timeTemp.startChargeDate = moment(this.form.chargeDate[0]).format(
            'YYYYMMDDHHmmss'
          )
        } else {
          timeTemp.startChargeDate = ''
        }
        if (this.form.chargeDate[1]) {
          timeTemp.endChargeDate = moment(this.form.chargeDate[1]).format(
            'YYYYMMDDHHmmss'
          )
        } else {
          timeTemp.endChargeDate = ''
        }
      }

      post('ht/HeatBasicData/findByCondition', {
        matid: this.form.matid, //钢板号
        facid: this.form.facid,
        startDisChargeDate: timeTemp.startDisChargeDate,
        endDisChargeDate: timeTemp.endDisChargeDate,
        startChargeDate: timeTemp.startChargeDate,
        endChargeDate: timeTemp.endChargeDate,
        group: this.form.group, //班组
        shift: this.form.shift, //班别
        grade: this.form.grade, //钢种
        temperatureRiseFall: this.form.temperatureRiseFall, //升降温时间
        specificationConversion: this.form.specificationConversion, //规格转换时间
        installationDelay: this.form.installationDelay //装钢延误时间
      }).then(res => {
        this.tableData = res.data
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="less">
.production-act {
  margin-top: 10px;

  .el-date-editor--datetimerange {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
    padding-right: 5px;
  }

  /deep/ .el-range-input,
  /deep/ .el-range-separator {
    color: #fff !important;
  }

  .el-table .el-table__header-wrapper tr {
    white-space: nowrap;
    overflow: hidden;
  }

  .el-form--inline .el-form-item {
    display: inline-block;
    margin-right: 10px;
    vertical-align: top;

    /deep/ .el-form-item__label {
      color: #ffffff;
    }

    /deep/ .el-form-item__content {
      margin-left: 0px !important;
    }

    .el-button--primary {
      background-color: #0c4e64;
      border-color: #0c4e64;
      border-radius: 3px;
    }

    /deep/ .el-radio__label {
      color: #ffffff;
    }

    /deep/ .el-radio__input.is-checked + .el-radio__label {
      color: #1fc6ff;
    }

    /deep/ .el-radio__input.is-checked .el-radio__inner {
      border-color: #1fc6ff;
      background: #1fc6ff;
    }

    /deep/ .el-button--primary:focus,
    /deep/ .el-button--primary:hover {
      background: #057499;
      border-color: #057499;
    }
  }
}
</style>
