<template>
  <div class="content">
    <div class="content-item">
      <screen-border title="质量体系">
        <template v-slot:headerRight>
          <span
            v-command="'/screen/qualityMeeting/edit'"
            class="screen-btn"
            @click="unfinished.dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            操作
          </span>
        </template>
        <div class="scroll-wrapper">
          <el-table
            v-loading="loading"
            :data="unfinished.showGridData"
            :span-method="handleObjectSpan"
            class="font-table center-table"
            border>
            <el-table-column
              property="qualitySystem"
              label="质量体系"
              width="120"/>
            <el-table-column
              property="num"
              label="序号"
              width="70"/>
            <el-table-column
              property="time"
              label="时间"/>
            <el-table-column
              property="content"
              label="内容">
              <template v-slot="{ row }">
                <div
                  slot="content"
                  v-html="formatText(row.content)"
                />
              </template>
            </el-table-column>
            <el-table-column
              property="proofResult"
              label="认证计划/结果">
              <template v-slot="{ row }">
                <div
                  slot="content"
                  v-html="formatText(row.proofResult)"
                />
              </template>
            </el-table-column>
            <el-table-column
              property="discrepancy"
              label="注意要点/不符合项目">
              <template v-slot="{ row }">
                <div
                  slot="content"
                  v-html="formatText(row.discrepancy)"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </screen-border>
    </div>
    <!--热处理详情-->
    <el-dialog
      :visible.sync="unfinished.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="质量体系复盘">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importUnfinishedData')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportunfinished">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveUnfinished">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          质量体系情况复盘
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="unfinished.gridData"
          border>
          <el-table-column
            property="qualitySystem"
            label="质量体系"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.qualitySystem"/>
            </template>
          </el-table-column>
          <el-table-column
            property="num"
            label="序号"
            width="80">
            <template v-slot="{ row }">
              <el-input v-model="row.num"/>
            </template>
          </el-table-column>
          <el-table-column
            property="time"
            label="时间"
            width="140">
            <template v-slot="{ row }">
              <el-input v-model="row.time"/>
            </template>
          </el-table-column>
          <el-table-column
            property="content"
            label="内容">
            <template v-slot="{ row }">
              <el-input 
                v-model="row.content"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="proofResult"
            label="认证结果">
            <template v-slot="{ row }">
              <el-input 
                v-model="row.proofResult"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="discrepancy"
            label="不符合项">
            <template v-slot="{ row }">
              <el-input 
                v-model="row.discrepancy"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'unfinished')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('unfinished')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { qmsQualitySystemSaveNew, qmsQualitySystem } from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'

export default {
  name: 'system',
  components: { ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      unfinished: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getUnfinished()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  methods: {
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          qualitySystem: 'B',
          num: 'C',
          time: 'D',
          content: 'E',
          proofResult: 'F',
          discrepancy: 'G'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unfinished.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportunfinished() {
      const data = [
        {
          qualitySystem: '质量体系',
          num: '序号',
          time: '时间',
          content: '内容',
          proofResult: '认证结果',
          discrepancy: '不符合项'
        }
      ].concat(_.cloneDeep(this.unfinished.gridData))
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `质量体系（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 获取数据
    getUnfinished() {
      post(qmsQualitySystem, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.$emit('statusChange', !res.data.length)
        this.unfinished.showGridData = res.data.map(item => {
          return {
            qualitySystem: item.qualitysystem,
            num: item.num,
            time: item.time,
            content: item.content,
            proofResult: item.proofresult,
            discrepancy: item.discrepancy
          }
        })
        this.unfinished.gridData = lodash.cloneDeep(
          this.unfinished.showGridData
        )
        this.formatSpanData(this.unfinished.showGridData)
      })
    },
    saveUnfinished() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.unfinished.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(qmsQualitySystemSaveNew, params).then(res => {
        //
        this.loading = false
        if (res !== -1) {
          this.$message.success('保存成功！')
          this.unfinished.dialogVisible = false
          this.getUnfinished()
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    importUnfinishedData(date) {
      post(qmsQualitySystem, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.unfinished.gridData = res.data.map(item => {
          return {
            qualitySystem: item.qualitysystem,
            num: item.num,
            time: item.time,
            content: item.content,
            proofResult: item.proofresult,
            discrepancy: item.discrepancy
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
