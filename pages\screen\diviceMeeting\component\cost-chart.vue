<template>
  <div 
    :id="containerId" 
    :style="{ height: '100%' }"/>
</template>

<script>
export default {
  name: 'bars-chart',
  props: {
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 0
    },
    unit: {
      type: String,
      default: '吨'
    },
    getZr: {
      type: Boolean,
      default: false
    },
    tooltipbg: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
    window.addEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
      }
      const options = {
        tooltip: {
          show: this.showToolbox,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          padding: 10
          // formatter: function(params) {
          //   //console.log(params)
          //   return `<div><span style="float: left;">生产：</span><span style="float: right;color:#2772F0;">${
          //     params[0]['data'].produce
          //   }</span></div><br/>
          //   <div><span style="float: left;">维修：</span><span style="float: right;color:#2772F0;">${
          //     params[0]['data'].maintenance
          //   }</span></div><br/>
          //   <div><span style="float: left;">轧辊：</span><span style="float: right;color:#2772F0;">${
          //     params[0]['data'].roll
          //   }</span></div><br/>`
          // }
        },
        color: this.color,
        legend: {
          show: this.showLegend,
          align: 'left',
          top: 5,
          right: 2,
          padding: 0,
          // icon: 'circle',
          textStyle: {
            color: '#C9E1FDF2',
            fontSize: 12
          },
          // itemHeight: 10, // 修改icon图形大小
          // itemWidth: 10, // 修改icon图形大小
          // itemGap: 10, // 修改间距
          itemStyle: {
            borderWidth: 0,
            padding: 0
          }
        },
        grid: {
          top: this.showLegend ? '18%' : '6%',
          left: '0%',
          right: '1%',
          bottom: '1%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: this.xData,
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              interval: 0,
              rotate: this.labelRotate || 0
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            }
          }
        ],
        yAxis: [
          {
            name: this.unit,
            type: 'value',
            minInterval: 1,
            axisLine: {
              show: false
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'left'
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        series: this.chartData.map(item => {
          console.log(item)
          return {
            name: item.name,
            type: 'bar',
            label: {
              show: true
            },
            stack: 'total',
            barWidth: this.barWidth || 60 / this.chartData.length + '%',
            barMaxWidth: this.barWidth || 12,
            data: item.data
          }
        })
      }
      this.myChart.setOption(options)
      if (this.getZr) {
        this.myChart.off('click') //防止触发两次点击事件
        this.myChart.getZr().on('click', params => {
          let pointInPixel = [params.offsetX, params.offsetY]
          if (this.myChart.containPixel('grid', pointInPixel)) {
            let pointInGrid = this.myChart.convertFromPixel(
              {
                seriesIndex: 0
              },
              pointInPixel
            )
            let xIndex = pointInGrid[0] //索引
            let handleIndex = Number(xIndex)
            let seriesObj = this.myChart.getOption() //图表object对象
            var op = this.myChart.getOption()
            //获得图表中点击的列
            var month = op.xAxis[0].data[handleIndex] //获取点击的列名
            let data = { month, handleIndex, seriesObj }
            this.$emit('child', data)
          }
        })
      }
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
