<template>
  <div 
    :id="containerId" 
    :style="{ height: '100%' }"/>
</template>

<script>
export default {
  name: 'bars-chart',
  props: {
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 0
    },
    unit: {
      type: String,
      default: ''
    },
    scrollable: {
      type: Boolean,
      default: false
    },
    scrollStart: {
      type: Number,
      default: 0
    },
    scrollEnd: {
      type: Number,
      default: 100
    }
    // max: {
    //   type: Number,
    //   default: 100
    // }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
    window.addEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
      }
      const options = {
        grid: {
          top: '40'
        },
        tooltip: {
          confine: true,
          show: this.showToolbox,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: function(params) {
            return (
              params[0].axisValue +
              '<br/>' +
              params
                .map(
                  item =>
                    `<div style="display: flex; justify-content: space-between"><span>
<span style="display: inline-block; width: 8px; height: 8px; vertical-align: middle; border-radius: 50%;background: ${
                      item.color
                    }"></span>
                    ${item.data.name || item.seriesName}
</span> <span> &emsp;
                    ${item.value}%</span></div>
                   `
                )
                .join('')
            )
          }
        },
        dataZoom: this.scrollable
          ? [
              {
                type: 'slider',
                show: true,
                xAxisIndex: [0],
                start: this.scrollStart,
                end: this.scrollEnd,
                height: 15,
                bottom: 5,
                borderColor: 'transparent',
                backgroundColor: 'rgba(47, 69, 84, 0.3)',
                dataBackground: {
                  lineStyle: { color: '#1fc6ff' },
                  areaStyle: { color: 'rgba(31, 198, 255, 0.2)' }
                },
                fillerColor: 'rgba(31, 198, 255, 0.2)',
                handleStyle: {
                  color: '#1fc6ff'
                },
                textStyle: {
                  color: '#ddd'
                }
              }
            ]
          : [],
        color: this.color,
        legend: {
          show: this.showLegend,
          align: 'left',
          top: '10px',
          right: 2,
          padding: [5, 10],
          icon: 'circle',
          textStyle: {
            color: '#C9E1FDF2',
            fontSize: 12
          },
          itemHeight: 10,
          itemWidth: 10,
          itemGap: 10,
          itemStyle: {
            borderWidth: 0,
            padding: 0
          }
        },
        grid: {
          top: '15%',
          left: '2%',
          right: '50',
          bottom: this.scrollable ? '20px' : '1%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: this.xData,
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              interval: 0,
              rotate: this.labelRotate || 0
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            }
          }
        ],
        yAxis: [
          {
            name: this.unit,
            nameTextStyle: {
              color: '#ddd',
              nameLocation: 'center'
            },
            type: 'value',
            minInterval: 0.5,
            axisLine: {
              show: false
            },
            axisLabel: {
              color: '#ddd',
              fontSize: 12,
              align: 'right'
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        series: this.chartData.map(item => {
          return {
            name: item.name,
            type: 'bar',
            barGap: '40%',
            barWidth: this.barWidth || 60 / this.chartData.length + '%',
            barMaxWidth: this.barWidth || 12,
            showBackground: this.barBackground,
            backgroundStyle: {
              color: 'rgba(232, 236, 239, 0.3)'
            },
            markPoint: {
              symbolSize: 5
            },
            label: {
              show: this.showLabel,
              color: '#fff',
              position: 'top',
              fontSize: 12,
              formatter: function(params) {
                return params.value + ' %'
              },
              offset: [0, 2]
            },
            data: item.data
          }
        })
      }
      this.myChart.setOption(options)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
