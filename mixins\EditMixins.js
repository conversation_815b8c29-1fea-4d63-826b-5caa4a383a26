// 编辑 新增详情用mixins，不适应页面特有操作的需要自定义相同名称函数覆盖

import { post } from '@/lib/Util'

export default {
  data: () => {
    return {
      title: '',
      editType: null
    }
  },
  methods: {
    add(nodeInfo) {
      this.title = '新增'
      this.editType = 'add'
      this.formData.status = 1 // 新增默认启用
      //设置层级
      // this.formData.rank = nodeInfo.level - 1
      // console.error(this.formData.rank, nodeInfo.level)

      //设置上级指标
      if (nodeInfo && this.editType === 'add') {
        let parentId =
          nodeInfo.level === 1
            ? Number(nodeInfo.data.value)
            : Number(nodeInfo.data.id)
        this.$set(this.formData, 'parentId', parentId) // 设置上级指标nodeInfo.data.value
        // 设置上级指标名称
        this.$set(this.formData, 'parentName', nodeInfo.data.name)
        this.$set(this.formData, 'rank', nodeInfo.level)
        this.$set(this.formData, 'factory', nodeInfo.data.factory)
      }
    },
    /**
     * 开启编辑
     * @param data 编辑元数据
     */
    edit(data) {
      this.title = '编辑'
      this.editType = 'edit'
      this.formData = Object.assign({}, this.formData, data)
    },
    clearForm() {
      for (let k in this.formData) {
        if (Array.isArray(this.formData[k])) {
          this.formData[k] = []
        } else {
          this.formData[k] = undefined
        }
      }
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].clearValidate == 'function'
      ) {
        this.$nextTick(() => {
          this.$refs['form'].clearValidate()
        })
      }
    },
    onOpen() {},
    close() {
      this.visible = false
      this.clearForm()
    },
    submitBefore() {
      // 提交前操作
    },
    submitAfter(res) {
      // 成功提交后操作
      this.$emit('success', res)
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          switch (this.editType) {
          }
          if (this.editType === 'edit') {
            if (!this.url || !this.url.edit) {
              this.$message.warning('请设置url.edit属性!')
              return
            }
            this.submitBefore()
            post(this.url.edit, this.formData).then(res => {
              if (res.success) {
                this.submitAfter(res)
                this.close()
              }
            })
          } else if (this.editType === 'add') {
            if (!this.url || !this.url.add) {
              this.$message.warning('请设置url.add属性!')
              return
            }
            this.submitBefore()
            post(this.url.add, this.formData).then(res => {
              if (res.success) {
                this.submitAfter(res)
                this.close()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        })
      }
    }
  }
}
