<!--督办事项-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border title="督办事项">
                <template v-slot:headerRight>
                  <el-row>
                    <span>已完成：</span>
                    <span style="margin-right: 10px;">{{ monthData.finish }}</span>
                    <span>进行中：</span>
                    <span style="margin-right: 10px;">{{ monthData.ongoing }}</span>
                    <span>未完成：</span>
                    <span style="margin-right: 10px;">{{ monthData.unFinish }}</span>
                    <span
                      v-command="'/screen/coilScreen/bjsc'"
                      class="screen-btn"
                      @click="clickAddProject">
                      <el-icon class="el-icon-edit-outline"/>
                      新增
                    </span>
                  </el-row>

                </template>
                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="ProjectData.loading"
                    :data="ProjectData.showGridData"
                    :row-class-name="rowClassName"
                    border>
                    <el-table-column
                      show-overflow-tooltip
                      width="60"
                      align="center"
                      label="序号">
                      <template slot-scope="scope">
                        <div>{{ scope.$index+1 }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="日期"
                      align="center"
                      width="130">
                      <template slot-scope="scope">
                        <div>{{ scope.row.PROD_DATE }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="[{text: '安全', value: '安全'}, {text: '环保', value: '环保'},{text: '生产', value: '生产'}, {text: '成本', value: '成本'},{text: '质量', value: '质量'}, {text: '设备', value: '设备'}, {text: '综合', value: '综合'}]"
                      :filter-method="filterMethod"
                      label="类别"
                      align="center"
                      property="CATEGORY"
                      width="80">
                      <template slot-scope="scope">
                        <div>{{ scope.row.CATEGORY }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="下发部门"
                      align="center"
                      width="100">
                      <template slot-scope="scope">
                        <div>{{ scope.row.SUB_DEPARTMENT }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="晨会内容">
                      <template slot-scope="scope">
                        <div>{{ scope.row.CONTENT }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="责任单位"
                      align="center"
                      width="160">
                      <template slot-scope="scope">
                        <div>{{ scope.row.RESPONSIBILITY }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="牵头人"
                      align="center"
                      width="100">
                      <template slot-scope="scope">
                        <div>{{ scope.row.LED }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :filters="[{text: '未完成', value: '未完成'}, {text: '短期进行中', value: '短期进行中'},{text: '长期进行中', value: '长期进行中'}, {text: '已完成', value: '已完成'}]"
                      :filter-method="filterMethod"
                      property="COMPLETION_STATUS"
                      label="完成情况"
                      align="center"
                      width="120">
                      <template slot-scope="scope">
                        <div>{{ scope.row.COMPLETION_STATUS }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="情况说明">
                      <template slot-scope="scope">
                        <div>{{ scope.row.DESCRIPTION }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="完成时间"
                      align="center"
                      width="130">
                      <template slot-scope="scope">
                        <div>{{ scope.row.COMPLETE_TIME }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property=""
                      width="150"
                      label="操作">
                      <template v-slot="scope">
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectItem(scope.row)">编辑</span>
                        <span
                          v-command="'/screen/coilScreen/bjsc'"
                          style="cursor: pointer;color: rgba(255,0,0,0.83);text-decoration: underline"
                          @click="clickProjectDeleteItem(scope.row)">删除</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--督办事项新增修改-->
    <el-dialog
      v-loading="ProjectData.loading"
      :visible.sync="ProjectData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="督办事项">
      <template v-slot:title>
        <div class="custom-dialog-title">
          督办事项
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">日期</div>
          <el-date-picker
            v-model="projectItem.PROD_DATE"
            :clearable="false"
            :size="'mini'"
            :value-format="'yyyy-MM-dd'"
            class="screen-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">类别</div>
          <el-select
            v-model="projectItem.CATEGORY"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in categoryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">下发部门</div>
          <el-select
            v-model="projectItem.SUB_DEPARTMENT"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in deptList"
              :key="item.id"
              :label="item.name"
              :value="item.id"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">晨会内容</div>
          <el-input
            v-model="projectItem.CONTENT"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">责任单位</div>
          <el-input
            v-model="projectItem.RESPONSIBILITY"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">牵头人</div>
          <el-input
            v-model="projectItem.LED"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">情况说明</div>
          <el-input
            v-model="projectItem.DESCRIPTION"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">完成情况</div>
          <el-select
            v-model="projectItem.COMPLETION_STATUS"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in statusList"
              :key="item.id"
              :label="item.name"
              :value="item.id"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">完成时间</div>
          <el-date-picker
            v-model="projectItem.COMPLETE_TIME"
            :clearable="false"
            :size="'mini'"
            :value-format="'yyyy-MM-dd'"
            class="screen-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          v-command="'/screen/coilScreen/bjsc'"
          class="screen-btn"
          @click="addProjectData"
        >
          确定
        </span>
      </div>
    </el-dialog>

    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="上月导入日期库存">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
// api板卷接口
import { coilScreen } from '@/api/screen'
import moment from 'moment'
export default {
  name: 'ProjectPage',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: Array,
      default: () => []
    }
  },
  data: () => {
    return {
      cDate: '',
      ProjectData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      projectItem: {},
      deptList: [
        {
          id: '公司',
          name: '公司'
        },
        {
          id: '事业部',
          name: '事业部'
        },
        {
          id: '厂级',
          name: '厂级'
        }
      ],
      statusList: [
        {
          id: '未完成',
          name: '未完成'
        },
        {
          id: '短期进行中',
          name: '短期进行中'
        },
        {
          id: '长期进行中',
          name: '长期进行中'
        },
        {
          id: '已完成',
          name: '已完成'
        }
      ],
      categoryList: [
        {
          id: '安全',
          name: '安全'
        },
        {
          id: '环保',
          name: '环保'
        },
        {
          id: '生产',
          name: '生产'
        },
        {
          id: '成本',
          name: '成本'
        },
        {
          id: '质量',
          name: '质量'
        },
        {
          id: '设备',
          name: '设备'
        },
        {
          id: '综合',
          name: '综合'
        }
      ],
      monthData: {
        finish: '',
        ongoing: '',
        unFinish: ''
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getProjectDataInit(this.cDate)
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    filterMethod(value, row, column) {
      console.log(value, row, column)
      const property = column['property']
      return row[property] === value
    },
    rowClassName({ row, rowIndex }) {
      if (row.COMPLETION_STATUS === '未完成') {
        return 'class_red'
      } else if (
        row.COMPLETION_STATUS === '短期进行中' ||
        row.COMPLETION_STATUS === '进行中'
      ) {
        return 'class_yellow'
      } else if (row.COMPLETION_STATUS === '长期进行中') {
        return 'class_orange'
      } else {
        return ''
      }
    },
    //督办点击删除
    clickAddProject() {
      this.projectItem = _.cloneDeep({
        // 类别
        CATEGORY: '',
        // 下发部门
        SUB_DEPARTMENT: '',
        // 晨会内容
        CONTENT: '',
        // 完成时间
        COMPLETE_TIME: '',
        // 责任单位
        RESPONSIBILITY: '',
        // 牵头人
        LED: '',
        // 完成情况
        COMPLETION_STATUS: '',
        // 情况说明
        DESCRIPTION: '',
        // 日期
        PROD_DATE: moment().format('yyyy-MM-DD')
      })
      this.ProjectData.dialogVisible = true
    },
    //督办点击查看详情
    clickProjectItem(row) {
      this.projectItem = _.cloneDeep(row)
      this.ProjectData.dialogVisible = true
    },
    calculateHeight() {
      this.ProjectData.maxHeight = this.$refs.table1.offsetHeight
    },
    // 数据查询
    getProjectDataInit(date) {
      this.ProjectData.loading = true
      post(coilScreen.coilInit, {
        startTime: date[0],
        endTime: date[1]
      })
        .then(res => {
          const data = _.cloneDeep(res.data)
          this.ProjectData.gridData = data
          this.ProjectData.showGridData = data
          this.monthData.finish = _.filter(data, item => {
            return item.COMPLETION_STATUS === '已完成'
          }).length
          this.monthData.ongoing = _.filter(data, item => {
            return (
              item.COMPLETION_STATUS === '短期进行中' ||
              item.COMPLETION_STATUS === '进行中' ||
              item.COMPLETION_STATUS === '长期进行中'
            )
          }).length
          this.monthData.unFinish = _.filter(data, item => {
            return item.COMPLETION_STATUS === '未完成'
          }).length
          this.ProjectData.loading = false
        })
        .catch(err => {
          this.ProjectData.loading = false
        })
    },
    // 数据新增
    addProjectData() {
      let setDate = this.projectItem.PROD_DATE
      if (setDate === null || setDate.length === 0) {
        this.$message.warning('请选择日期！')
        return
      }
      this.ProjectData.loading = true
      post(coilScreen.coilUpdate, this.projectItem)
        .then(res => {
          this.getProjectDataInit(this.cDate)
          this.ProjectData.dialogVisible = false
          this.ProjectData.loading = false
        })
        .catch(err => {
          this.ProjectData.loading = false
        })
    },
    //督办点击删除
    clickProjectDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          post(coilScreen.coilDelete(row.ID))
            .then(res => {
              this.getProjectDataInit(this.cDate)
            })
            .catch(err => {
              this.getProjectDataInit(this.cDate)
            })
        })
        .catch(e => {
          console.log('e', e)
        })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
/deep/ .el-table .class_red {
  background: #fd0000;
  color: black;
}
/deep/ .el-table .class_yellow {
  background: #fdfd00;
  color: black;
}
/deep/ .el-table .class_orange {
  background: #f99f04;
  color: black;
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
