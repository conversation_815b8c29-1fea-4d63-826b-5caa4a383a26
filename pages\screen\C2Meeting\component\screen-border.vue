<template>
  <div class="border-wrapper">
    <div 
      :class="headerClass" 
      class="border-head">
      <em class="line"/>
      <em class="line-bottom"/>
      <em class="square"/>
      <div class="right-content">
        <slot name="headerRight"/>
      </div>
      <slot name="title">
        {{ title }}
      </slot>
    </div>
    <div 
      :class="contentClass" 
      class="border-content">
      <div class="border-content-wrapper">
        <slot/>
        <slot name="bottom"/>
      </div>
      <div class="line" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScreenBorder',
  props: {
    title: {
      type: String,
      default: ''
    },
    headerClass: {
      type: String,
      default: ''
    },
    contentClass: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      active: 1
    }
  }
}
</script>

<style lang="less" scoped>
.border-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .border-head {
    position: relative;
    font-size: 18px;
    height: 50px;
    line-height: 50px;
    padding: 0 15px 0 25px;
    border-top: 1px solid #136480;
    font-weight: bold;
    color: #fff;
    background: linear-gradient(
      90deg,
      rgba(31, 180, 255, 0.12) 0%,
      rgba(31, 180, 255, 0) 100%
    );
    &:after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 32px;
      height: 1px;
      background: #1fc6ff;
    }
    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 32px;
      height: 1px;
      background: #1fc6ff;
    }
    .line {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 8px;
      height: 1px;
      background: #1fc6ff;
      &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 10px;
        width: 8px;
        height: 1px;
        background: rgba(31, 198, 255, 0.56);
      }
      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 20px;
        width: 8px;
        height: 1px;
        background: rgba(31, 198, 255, 0.24);
      }
    }
    .line-bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      width: 50%;
      margin: auto;
      height: 1px;
      background: linear-gradient(
        90deg,
        rgba(31, 199, 255, 0.1) 0%,
        #1fc7ff 50%,
        rgba(31, 199, 255, 0.1) 100%
      );
    }
    .square {
      position: absolute;
      display: block;
      bottom: 1px;
      right: 1px;
      width: 18px;
      height: 4px;
      background: #1fc6ff;
      transform: skewX(-45deg);
      margin: auto;
    }
    .right-content {
      float: right;
    }
  }
  .border-content {
    &.content-auto {
      overflow: hidden;
      height: auto;
    }
    position: relative;
    flex: 1;
    padding-top: 10px;
    padding: 10px 0px;
    overflow: visible;
    height: 0;
    border-bottom: 1px solid rgba(31, 199, 255, 0.24);
    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 1px;
      bottom: 0;
      background: rgba(31, 198, 255, 0.56);
      background: url(../../../../assets/images/screen/border-line.png) repeat-y
        top;
    }
    &:before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      width: 1px;
      background: url(../../../../assets/images/screen/border-line.png) repeat-y
        top;
    }
    .line {
      position: absolute;
      left: 20px;
      right: 20px;
      bottom: 0;
      height: 1px;
      background: rgba(31, 199, 255, 0.72);
    }

    .border-content-wrapper {
      position: relative;
      height: 100%;
      padding: 0px 10px;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  }
}
</style>
