<template>
  <img
    :src="iconPath"
    :alt="iconName"
    draggable="false"
    class="icon-svg"
    @click="clickIcon"
  >
</template>

<script>
import { iconApp } from '@/lib/Icons'

export default {
  props: {
    iconName: {
      type: String,
      require: true,
      default: ''
    }
  },
  computed: {
    iconPath: function() {
      console.log(this.iconName)
      return iconApp[this.iconName]
        ? iconApp[this.iconName].path
        : iconApp['icons-side-pane'].path
    }
  },
  methods: {
    clickIcon() {
      this.$emit('click', this.iconName)
    }
  }
}
</script>

<style scoped>
.icon-svg {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
  user-select: none;
}
</style>
