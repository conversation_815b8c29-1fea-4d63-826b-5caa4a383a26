<template>
  <div class="content">
    <div class="content-item">
      <screen-border :title="'检维修施工作业公示'">
        <template v-slot:headerRight>
          <span
            v-command="'/screen/C2Meeting/coordinate'"
            class="screen-btn"
            @click="pilotPlan1.dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            操作
          </span>
        </template>
        <div
          ref="table1"
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            ref="tableShow"
            :data="pilotPlan1.showGridData"
            :span-method="handleObjectSpan"
            :max-height="pilotPlan1.maxHeight"
            :row-class-name="totalClass"
            :size="'medium'"
            class="center-table font-big-table"
            border>
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              property="workName"
              label="作业名称"/>
            <el-table-column
              property="workInfo"
              label="工作内容"/>
            <el-table-column
              property="dangerLevel"
              label="危险等级"/>
            <el-table-column
              property="workDays"
              label="工期(天)"
              width="110"/>
            <el-table-column
              v-if="!isFactoryUser"
              property="workTime"
              label="作业时间"
              width="155"/>
            <el-table-column
              v-if="!isFactoryUser"
              :label="'作业单位'"
              property="workUnit"/>
            <el-table-column
              :label="'作业人数(人)'"
              align="left"
              property="workNumber"
              width="140"/>
            <el-table-column
              :label="'作业风险'"
              align="left"
              property="workRisk"/>
            <el-table-column
              :label="'属地车间'"
              align="left"
              property="workShop"/>
            <el-table-column
              :label="'计划开始时间'"
              align="left"
              property="startTime"
              width="140"/>
            <el-table-column
              :label="'监护人'"
              align="left"
              property="guardian"/>
          </el-table>
        </div>
      </screen-border>
    </div>
    <!--工艺绩效评价-->
    <el-dialog
      :visible.sync="pilotPlan1.dialogVisible"
      :width="'95%'"
      :top="'50px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="施工作业展示">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="$refs.tableEdit.clearFilter()">
              清除筛选
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              type="date"
              @change="changeDate"/>
            <template>
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
            </template>
            <span
              class="screen-btn"
              @click="exportpilotPlan">
              导出
            </span>
          </div>
        </div>
      </template>
      <el-form>
        <el-table
          v-loading="loading"
          ref="tableEdit"
          :data="pilotPlan1.gridData"
          :max-height="tableMaxHeight"
          class="center-table"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60"/>
          <el-table-column
            property="workName"
            label="作业名称">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.workName"
                :rows="4"
                type="textarea"/>
              <template v-else>
                {{ row.workName }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="workInfo"
            label="工作内容">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.workInfo"
                :rows="4"
                type="textarea"/>
              <template v-else>
                {{ row.workInfo }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="dangerLevel"
            label="危险等级">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.dangerLevel"/>
              <template v-else>
                {{ row.dangerLevel }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            property="workDays"
            label="工期(天)"
            width="120">
            <!-- <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.workDays"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>
                {{ row.workDays }}
              </template>
            </template> -->
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.workDays"/>
              <template v-else>
                {{ row.workDays }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            property="workTime"
            label="作业时间"
            width="120">
            <!-- <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.workTime"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%"/>
              <template v-else>
                {{ row.workTime }}
              </template>
            </template> -->
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.workTime"/>
              <template v-else>
                {{ row.workTime }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            :label="'作业单位'"
            property="workUnit">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.workUnit"/>
              <template v-else>
                {{ row.workUnit }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'作业人数(人)'"
            align="left"
            property="workNumber">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.workNumber"/>
              <template v-else>
                {{ row.workNumber }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'作业风险'"
            align="left"
            property="workRisk">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.workRisk"/>
              <template v-else>
                {{ row.workRisk }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'属地车间'"
            align="left"
            property="workShop">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.workShop"/>
              <template v-else>
                {{ row.workShop }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'计划开始时间'"
            align="left"
            property="startTime">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.startTime"/>
              <template v-else>
                {{ row.startTime }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'监护人'"
            align="left"
            property="guardian">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.guardian"/>
              <template v-else>
                {{ row.guardian }}
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'操作'"
            fixed="right"
            property="proofResult">
            <template v-slot="{ row, $index}">
              <!-- 部分编辑-->
              <el-button
                v-if="$index !== editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="editItem(pilotPlan1.gridData, $index, 1)">编辑</el-button>
              <el-button
                v-if="$index === editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="savepilotPlanItem(row)">保存</el-button>
              <el-button
                class="screen-btn edit-btn"
                type="text"
                @click="deleteItem(row, $index)">删除</el-button>
            </template>
          </el-table-column>

        </el-table>
      </el-form>
      <div class="text-center">
        <span
          class="screen-btn"
          style="margin-top: 10px"
          @click="addGridData('pilotPlan1');editIndex = pilotPlan1.gridData.length - 1">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM'"
            type="month"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

 <script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { qmsQualitySystemSaveNew, qmsQualitySystem } from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  checklistBySetDate,
  checklistDelete,
  checklistSave,
  findCountDeptBySetDate,
  findCountPltBySetDate,
  findDpPltBySetDate,
  progressReportingDeleteAllById,
  progressReportingFindAllBySetDate,
  progressReportingSave,
  constructionWorkInfoFindAllDate,
  constructionWorkInfoDelById,
  constructionWorkInfoSaveAll
} from '@/api/screenTechnolagy'
import moment from 'moment'
import { math } from '@/lib/Math'
import TextDisplay from '@/pages/screen/technologyMeeting/component/text-display'
import { findOneUserByUserNo } from '@/api/system'
import { mapState } from 'vuex'
import {
  trackingMattersDelete,
  trackingMattersFind,
  trackingMattersSave
} from '@/api/screenC2'

export default {
  name: 'constructionWork',
  components: { TextDisplay, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      editIndex: null,
      editPartIndex: null, //部分编辑
      tableMaxHeight: null,
      pilotPlan1: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      pivotTable: {
        table1: [],
        table2: [],
        table3: [],
        dialogVisible: false,
        maxHeight: null
      },
      pltList: [
        { value: '第一炼钢厂', text: '第一炼钢厂' },
        { value: '宽厚板厂', text: '宽厚板厂' },
        { value: '中厚板卷厂', text: '中厚板卷厂' },
        { value: '中板厂', text: '中板厂' }
      ],
      departmentList: [
        '工艺研究室',
        '调质钢研发室',
        '结构船板研发室',
        '低温容器研发室',
        '能源用钢研发室'
      ],
      varietyList: ['工艺抽查', '工装备件'],
      conclusionList: ['符合', '不符合'],
      factoryList: [
        { code: 'X73', name: '第一炼钢厂' },
        { code: 'X38', name: '宽厚板厂' },
        { code: 'X32', name: '中厚板卷厂' },
        { code: 'X66', name: '中板厂' }
      ],
      isFactoryUser: false
    }
  },
  computed: {
    ...mapState('menu', ['pageButtonPower']),
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'month')
        .format('yyyy-MM')
    },
    levelList: function() {
      if (
        this.editIndex !== null &&
        this.pilotPlan1.gridData[this.editIndex] &&
        this.pilotPlan1.gridData[this.editIndex].inspectionContent ===
          '工装备件'
      ) {
        return [
          '一般1级',
          '一般2级',
          '一般3级',
          '重要1级',
          '重要2级',
          '重要3级'
        ]
      }
      return []
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.loadData()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    loadData() {
      this.getpilotPlan()
    },
    filterConclusion(value, row) {
      return row['conclusion'] === value
    },
    filterPlt(value, row) {
      return row['plt'] === value
    },
    async findOneUserByUserNo() {
      this.userNo = localStorage.getItem('userId')
      const user = await post(findOneUserByUserNo, {
        userNo: this.userNo
      })
      return new Promise(resolve => resolve(user.data))
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          index: 'A',
          workName: 'B',
          workInfo: 'C',
          dangerLevel: 'D',
          workDays: 'E',
          workTime: 'F',
          workUnit: 'G',
          workNumber: 'H',
          workRisk: 'I',
          workShop: 'J',
          startTime: 'K',
          guardian: 'L'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        const datas = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
        this.savepilotPlan(datas)
      })
    },
    exportpilotPlan() {
      const data = [
        {
          index: '序号',
          workName: '作业名称',
          workInfo: '工作内容',
          dangerLevel: '危险等级',
          workDays: '工期(天)',
          workTime: '作业时间',
          workUnit: '作业单位',
          workNumber: '作业人数(人)',
          workRisk: '作业风险',
          workShop: '属地车间',
          startTime: '计划开始时间',
          guardian: '监护人'
        }
      ].concat(
        _.cloneDeep(this.pilotPlan1.gridData).map((item, index) => {
          delete item.id
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `施工作业展示（${this.cDate}）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {
              '!merges': LAY_EXCEL.makeMergeConfig([])
            }
          }
        }
      )
    },
    // 获取数据
    async getpilotPlan() {
      post(constructionWorkInfoFindAllDate, {
        setDate: moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
      }).then(res => {
        this.loading = false
        this.$emit('statusChange', !res.data.length)
        let data = res.data.map((item, index) => {
          return {
            index: index + 1,
            workName: item.workName,
            workInfo: item.workInfo,
            dangerLevel: item.dangerLevel,
            workDays: item.workDays,
            workTime: item.workTime,
            workUnit: item.workUnit,
            workNumber: item.workNumber,
            workRisk: item.workRisk,
            workShop: item.workShop,
            startTime: item.startTime,
            guardian: item.guardian,
            id: item.id
          }
        })
        this['pilotPlan1'].gridData = lodash.cloneDeep(data)
        this['pilotPlan1'].showGridData = data
        this.formatSpanData(this['pilotPlan1'].showGridData)
      })
    },
    savepilotPlanItem(row) {
      let err = 0
      let arr = []
      arr.forEach(item => {
        if (row[item] === '' || row[item] === null) {
          console.log(item)
          err++
        }
      })
      if (err > 0) {
        return this.$message.warning('请补全信息！')
      }
      this.savepilotPlan([row])
    },
    savepilotPlan(items) {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: items.map(item => {
          Object.assign(item, { setDate: this.cDate })
          return item
        })
      }
      post(constructionWorkInfoSaveAll, params).then(res => {
        //
        this.loading = false
        if (res && res !== -1) {
          this.$message.success('保存成功！')
          this.getpilotPlan()
          this.editIndex = null
          this.editPartIndex = null
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    editItem(data, index, type) {
      this.editIndex = index
    },
    editPartItem(data, index, type) {
      this.editPartIndex = index
    },
    deleteItem(items) {
      const params = {
        setDate: this.cDate,
        data: [items].map(item => {
          Object.assign(item, { setDate: this.cDate })
          return item
        })
      }
      // console.log('item', params)
      this.$confirm(`是否确认删除这条记录?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ;(this.loading = true),
          post(constructionWorkInfoDelById, params).then(res => {
            this.loading = false
            this.$message.success('删除成功！')
            this.getpilotPlan()
          })
      })
    },
    conclusionChange($event, data) {
      if ($event === '符合') {
        console.log(data)
        data[this.editIndex].deductPoints = 0
        data[this.editIndex].rftDeadline = null
        data[this.editIndex].deductPointsDisabled = true
      } else {
        data[this.editIndex].deductPointsDisabled = false
      }
    },
    getPivot() {
      post(findCountPltBySetDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.pivotTable.table1 = res
      })
      post(findCountDeptBySetDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.pivotTable.table2 = res
      })
      post(findDpPltBySetDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.pivotTable.table3 = res.map(item => {
          item.DF = 100 - item.ZJ
          return item
        })
      })
    },
    calculate() {
      this.pilotPlan1.maxHeight = this.$refs.table1.offsetHeight
      this.tableMaxHeight = document.body.clientHeight - 240
    },
    totalClass(row) {
      if (row.row.serialNumber && row.row.serialNumber.trim() === '合计') {
        return 'table-total'
      }
      return ''
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总计'
          return
        }
        console.log(column)
        if (![1, 2, 3].includes(index)) return (sums[index] = '')
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          sums[index] = ''
        }
      })
      return sums
    }
  }
}
</script>

 <style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.edit-btn {
  margin: 0 3px;
  &:first-child {
    margin-bottom: 5px;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.dialog-item-tit {
  font-size: 18px;
  color: #dcdcdc;
  margin-bottom: 8px;
  margin-top: 15px;
}
</style>
