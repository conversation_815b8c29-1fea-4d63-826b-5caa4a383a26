import Vue from 'vue'
import Element from 'element-ui'
import locale from 'element-ui/lib/locale/lang/zh-CN'
// import locale from 'element-ui/lib/locale/lang/en'
import 'font-awesome/css/font-awesome.css'
import '../assets/css/theme/index.css'

Element.Button.props.round = { type: Boolean, default: true }
Element.Dialog.props.closeOnClickModal = { type: Boolean, default: false }
export default () => {
  Vue.use(Element, {
    locale,
    size: 'small'
  })
}
