const path = 'mesAPI/'
const pathEnergy = 'ems/'

// 全天成产计划完成情况报表
// 保存
// ************:9800/mesAPI/PSCDay/saveAll
export const PSCDaySave = path + '/PSCDay/saveAll'
export const MeetingRecordFind = path + '/MeetingRecord/findAllDate'
export const MeetingRecordSave = path + '/MeetingRecord/saveAll'
export const CoordinationIteDel = path + '/CoordinationItem/deleteById'
export const WorkTeamProductionFind = path + '/WorkTeamProduction/findAllDate'
export const WorkTeamProductionSave = path + '/WorkTeamProduction/saveAll'
//热处理生产情况
export const RCLSituationFind = path + '/RCLSituation/findAllDate'
export const RCLSituationSave = path + '/RCLSituation/saveAll'
// {"setDate":"2023-07","data":[{"project":"aaa","plan":"222","setDate":"2023-07"}]}
// 查询
// ************:9800/mesAPI/PSCDay/findAllBySetDate
export const PSCDayFind = path + '/PSCDay/findAllBySetDate'
//宽厚板厂板材非计划指标-
export const UnplannedIndicator = path + 'UnplannedIndicator/saveAll'
export const UnplannedIndicatorFind = path + 'UnplannedIndicator/findAllDate'
//厚度<=16mm订单超7天未判定-
export const UndeterminedOrder = path + 'UndeterminedOrder/saveAll'
export const UndeterminedOrderrFind = path + 'UndeterminedOrder/findAllDate'
//超2个月在制品情况-
export const WorkInProgress = path + 'WorkInProgress/saveAll'
export const WorkInProgressFind = path + 'WorkInProgress/findAllDate'
// 设备运行
// ************:9800/mesAPI/PSCDay/findAllBySetDate
export const findEquipmentOperation = path + '/PSCDay/findEquipmentOperation'
export const findNightEquipmentOperation =
  path + '/PSCDay/findNightEquipmentOperation'
// {"setDate":"2023-07"}
//
//
// 当天生产作业情况汇总
// 保存
// ************:9800/mesAPI/productionSituationDay/saveAll
export const productionSituationDaySave =
  path + '/productionSituationDay/saveAll'
// {"setDate":"2023-07","data":[{"project":"aaa","plan":"222","setDate":"2023-07"}]}
// 查询
// ************:9800/mesAPI/productionSituationDay/findAllBySetDate
export const productionSituationDayFind =
  path + '/productionSituationDay/findAllBySetDate'
// {"setDate":"2023-07"}
//
//
// 热轧轧制情况
// 保存
// ************:9800/mesAPI/hotRollingSituation/saveAll
export const hotRollingSituationSave = path + '/hotRollingSituation/saveAll'
// {"setDate":"2023-07","data":[{"classes":"aaa","groups":"222","setDate":"2023-07"}]}
// 查询
// ************:9800/mesAPI/hotRollingSituation/findAllBySetDate
export const hotRollingSituationFind =
  path + '/hotRollingSituation/findAllBySetDate'
// {"setDate":"2023-07"}
//
//
// 精整剪切情况
// 保存
// ************:9800/mesAPI/finishingShearing/saveAll
export const finishingShearingSave = path + '/finishingShearing/saveAll'
// {"setDate":"2023-07","data":[{"classes":"aaa","groups":"222","setDate":"2023-07"}]}
// 查询
// ************:9800/mesAPI/finishingShearing/findAllBySetDate
export const finishingShearingFind =
  path + '/finishingShearing/findAllBySetDate'
// {"setDate":"2023-07"}
//
//
// 剪切非计划率
// 保存
// ************:9800/mesAPI/cutUnplannedRate/saveAll
export const cutUnplannedRateSave = path + '/cutUnplannedRate/saveAll'
// {"setDate":"2023-07","data":[{"project":"aaa","situationDay":"222","setDate":"2023-07"}]}
// 查询
// ************:9800/mesAPI/cutUnplannedRate/findAllBySetDate
export const cutUnplannedRateFind = path + '/cutUnplannedRate/findAllBySetDate'
// {"setDate":"2023-07"}

// 大夜班火切线生产情况
// 保存
// ************:9800/mesAPI/fireCuttingProduction/saveAll
export const fireCuttingSave = path + '/fireCuttingProduction/saveAll'
// {"setDate":"2023-07","data":[{"classification":"aaa","plan":"222","setDate":"2023-07"}]}
//
// 查询
// ************:9800/mesAPI/fireCuttingProduction/findAllBySetDate
export const fireCuttingFind = path + '/fireCuttingProduction/findAllBySetDate'
// {"setDate":"2023-07"}
//
//
//
// 热轧轧制情况
// 保存
// ************:9800/mesAPI/rollingProduction/saveAll
export const rollingProductionSave = path + '/rollingProduction/saveAll'
// {"setDate":"2023-07","data":[{"currentDate":"aaa","groups":"222","setDate":"2023-07"}]}
//
// 查询
// ************:9800/mesAPI/rollingProduction/findAllBySetDate
export const rollingProductionFind =
  path + '/rollingProduction/findAllBySetDate'
// {"setDate":"2023-07"}

//
// 当月累计停时汇报
// 保存
// ************:9800/mesAPI/downtimeMonth/saveAll
export const downtimeMonthSave = path + '/downtimeMonth/saveAll'
//
// {"setDate":"2023-07","data":[{"unit":"aaa","mechanicalPlan":"111","setDate":"2023-07"}]}
//
// 查询
// ************:9800/mesAPI/downtimeMonth/findAllBySetDate
export const downtimeMonthFind = path + '/downtimeMonth/findAllBySetDate'
// {"setDate":"2023-07"}
//
//
// 本月累计隐性停时汇总
// 保存
// ************:9800/mesAPI/hiddenDowntimeMonth/saveAll
export const hiddenDowntimeMonthSave = path + '/hiddenDowntimeMonth/saveAll'
// {"setDate":"2023-07","data":[{"unit":"aaa","mechanicalPlan":"111","setDate":"2023-07"}]}
//
// 查询
// ************:9800/mesAPI/hiddenDowntimeMonth/findAllBySetDate
export const hiddenDowntimeMonthFind =
  path + '/hiddenDowntimeMonth/findAllBySetDate'
// {"setDate":"2023-07"}
//
// 待处理情况
// 保存
// ************:9800/mesAPI/pendingSituation/saveAll
// {"setDate":"2023-07",
export const pendingSituationSave = path + '/pendingSituation/saveAll'
// "data":[{"classification":"aaa","plan":"111","setDate":"2023-07"}]}
//
// 查询
// ************:9800/mesAPI/pendingSituation/findAllBySetDate
export const pendingSituationFind = path + '/pendingSituation/findAllBySetDate'
// {"setDate":"2023-07"}
//
//
// 当月计划和产量进度
// 保存
// ************:9800/mesAPI/planProductionMonth/saveAll
export const planProductionMonthSave = path + '/planProductionMonth/saveAll'
export const findMonthBySetDate =
  path + '/planProductionMonth/findMonthBySetDate'
// {"setDate":"2023-07","data":[{"classification":"aaa","plannedProduction":"111","setDate":"2023-07"}]}
//
// 查询
// ************:9800/mesAPI/planProductionMonth/findAllBySetDate
export const planProductionMonthFind =
  path + '/planProductionMonth/findAllBySetDate'
// {"setDate":"2023-07"}

//当天主线能源消耗情况汇总
export const htpYiedConsume = '/ems/statement/htpYiedConsume'
export const eleConsume = '/ems/statement/eleConsume'
export const htrConsume = '/ems/statement/htrConsume'
export const costReportC2 = '/ems/statement/costReportC2'

export const trackingMattersSave = path + '/trackingMatters/saveAll'
export const trackingMattersFind = path + '/trackingMatters/findAllBySetDate'
//宽厚板持续跟踪
export const findRemarkBySetDate = path + '/trackingMattersTwo/findFirstOne'
export const updateRemarkBySetDate = path + 'trackingMattersTwo/saveAll'
export const findRemarkBySetDateMonth =
  path + '/trackingMattersMonth/findAllBySetDate'
export const CostPRPlanData = path + '/CostPRPlan/findAllDate'
export const CostPRPlanSave = path + '/CostPRPlan/saveAll'
export const updateRemarkBySetDateMonth = path + 'trackingMattersMonth/saveAll'
export const deleteById = path + 'trackingMattersTwo/deleteById'
export const deleteById3 = path + 'trackingMattersMonth/deleteAllById'
// 安环

export const addQualityEvent = path + '/safetyEnvir/addQuality' //'质量事件新增'

export const addSafety = path + '/safetyEnvir/addSafety' //'安消环事件新增'

export const addStopDetails = path + '/safetyEnvir/addStopDetails' //'停时明细新增'

export const findAllQuality = path + '/safetyEnvir/findAllQuality' //'质量事件'

export const findAllSafety = path + '/safetyEnvir/findAllSafety' //'安消环事件'

export const findAllStopDetail = path + '/safetyEnvir/findAllStopDetail' //'停时明细

// 宽厚板，质量查询
export const qualityFind = path + '/productionQuality/findAllBySetDate'
export const qualitySave = path + '/productionQuality//saveAll'
export const qualityDel = path + '/productionQuality//delById'
export const defrctFind = path + '/CategoryDefect/findByCondition'
export const defrctSave = path + '/CategoryDefect/saveAll'
export const defrctDel = path + '/CategoryDefect//delById'
export const trackingMattersDelete = path + '/trackingMatters/deleteAllById'

export const totalInventory = path + '/blank/totalInventory'
export const availableBillets = path + '/blank/availableBillets'
export const blankFindAllBySetDate = path + '/blank/findAllBySetDate'
export const saveAll = path + '/pendJud/saveAll'
export const findAllBySetDate = path + '/pendJud/findAllBySetDate'
export const mainLineFindAllBySetDate = path + '/mainLine/findAllBySetDate'
export const mainLinesaveAll = path + 'mainLine/saveAll'
export const findInspectionPassRateByDateAndPlt =
  path + '/InspectionPassRate/findInspectionPassRateByDateAndPlt'
export const QualityJudgeDayFindAllBySetDate =
  path + '/QualityJudgeDay/findAllByDate'
export const QualityJudgeDaysaveAll = path + 'QualityJudgeDay/saveAll'
export const keySteelChangeFindAllBySetDate =
  path + '/keySteelChange/findAllBySetDate'
export const keySteelChangesaveAll = path + 'keySteelChange/saveAll'
export const unplaneRateFindAllBySetDate = path + 'unplaneRate/findAllBySetDate'
export const unplaneRatesaveAll = path + 'unplaneRate/saveAll'
export const findSummary = path + 'unplaneRate/findSummary'
export const steeleRateFindAllBySetDate = path + 'steeleRate/findAllBySetDate'
export const steeleRatesaveAll = path + 'steeleRate/saveAll'
export const findMonthAllByDate =
  path + 'PlateYgzFirstPassRate/findMonthAllByDate'
export const tobeHeatFindAllBySetDate = path + 'tobeHeat/findAllBySetDate'
export const tobeHeatsaveAll = path + 'tobeHeat/saveAll'
export const workProgressFindAllBySetDate =
  path + 'workProgress/findAllBySetDate'
export const workProgresssaveAll = path + 'workProgress/saveAll'
export const flawDetectionFindAllBySetDate =
  path + 'flawDetection/findAllBySetDate'
export const flawDetectionsaveAll = path + 'flawDetection/saveAll'
export const refuaslReasonFindAllBySetDate =
  path + 'refuaslReason/findAllBySetDate'
export const refuaslReasonsaveAll = path + 'refuaslReason/saveAll'
export const ProductionNotStorageFindAllBySetDate =
  path + 'ProductionNotStorage/findAllBydate'
export const ProductionNotStoragesaveAll = path + 'ProductionNotStorage/SaveAll'
export const QualityOriginalNonPlanFindAllBySetDate =
  path + 'QualityOriginalNonPlan/findAllByDate'
export const QualityOriginalNonPlansaveAll =
  path + 'QualityOriginalNonPlan/SaveAll'
export const RecordEvaluationFindAllBySetDate =
  path + 'RecordEvaluation/findAllByDate'
export const RecordEvaluationsaveAll = path + 'RecordEvaluation/saveAll'

//获取质量典型张片数据
export const FILE_FINGALL = path + 'File/findAll'
export const FILE_UPLOADSECOND = path + 'File/uploadSecond'
export const STOVE_FLAW = path + 'TypicalImg/findAllByDate'
export const UPDATA_STOVE_FLAW = path + 'TypicalImg/updateRow1'
export const DELET_ALL_IMG = path + 'File/deleteAll'

//重大风险
export const RISKALL = path + 'MajorRisk/findAllByCondition'
export const RISKADDS = path + 'MajorRisk/insertAll'
export const RISKDEL = path + 'MajorRisk/deleteById'
export const RISKUPDATA = path + 'MajorRisk/updateById'
export const RISKUPLOADBEFORE = path + 'MajorRisk/uploadBefore'
export const RISKUPLOADAFTER = path + 'MajorRisk/uploadAfter'

//安全检查
export const SECURITYCHECKSFINDALL = path + 'SecurityCheck/findAllByCondition'
export const SECURITYCHECKSADDS = path + 'SecurityCheck/saveAll'
export const SECURITYCHECKSDEL = path + 'SecurityCheck/deleteById'
export const SECURITYCHECKSBEFORE = path + 'SecurityCheck/uploadBefore'
export const SECURITYCHECKSAFTER = path + 'SecurityCheck/uploadAfter'

//隐患整改
export const HIDDENDANGERREC = path + 'HiddenDanger/findAllByCondition'
export const HIDDENDANGERADDS = path + 'HiddenDanger/saveAll'
export const HIDDENDANGERDEL = path + 'HiddenDanger/deleteById'
export const HIDDENDANGERBEFORE = path + 'HiddenDanger/uploadBefore'
export const HIDDENDANGERAFTER = path + 'HiddenDanger/uploadAfter'

//宽厚板厂 => 质量攻关项目
//查询
export const qualityFindAllDate = path + 'DisQualityPr/findAllDate'
//保存
export const qualitySaveAll = path + 'DisQualityPr/saveAll'

//宽厚板厂 => 设备精度
//查询
export const deviceAccuracyFindAllDate = path + 'DisDeviceAccuracy/findAllDate'
//保存
export const deviceAccuracySaveAll = path + 'DisDeviceAccuracy/saveAll'

//宽厚板厂 => 质量改进-专利计划
//查询
export const patentProgramFindAllDate = path + 'PatentProgram/findAllDate'
//保存
export const patentProgramSaveAll = path + 'PatentProgram/saveAll'
//删除
export const patentProgramDeleteAll = path + 'PatentProgram/deleteAll'

//宽厚板厂 => 过程管理-车间履职
//查询
export const workshopPerformanceFindAllDate =
  path + 'WorkshopPerformance/findAllDate'
//保存
export const workshopPerformanceSaveAll = path + 'WorkshopPerformance/saveAll'
//删除
export const workshopPerformanceDeleteAll =
  path + 'WorkshopPerformance/deleteAll'
// 补焊数据分析
export const weldingDataAnalysisFindAllDate =
  path + 'DwrPrdGpBhInsplateJcF/findAllByBhDate'

//质量改进-典型问题
export const typicalQualityProblemFindAllDate =
  path + 'TypicalProblems/findAllDate'
export const typicalQualityProblemSaveAll = path + 'TypicalProblems/saveAll'

//宽厚板厂 => 质量 坯判废列表查询
export const findBlankWastesAllByDate =
  path + '/QualityJudgeDay/findBlankWastesAllByDate'
