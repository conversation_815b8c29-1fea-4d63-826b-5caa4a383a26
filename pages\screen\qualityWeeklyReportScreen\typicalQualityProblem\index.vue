<template>
  <div class="container">
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="典型质量问题">
          <div class="content-wrapper">
            <el-row
              :gutter="10"
              style="width: 100%;">
              <!-- 根据当前页码显示对应的列数据 -->
              <el-col
                v-for="columnIndex in 4"
                :key="columnIndex"
                :span="6">
                <div class="problem-item">
                  <el-button
                    :loading="submitLoading[columnIndex]"
                    type="primary"
                    class="submit-btn"
                    icon="el-icon-finished"
                    @click="submitColumn(columnIndex)">提交</el-button>
                  <div class="images-container">
                    <div
                      v-if="getCurrentColumnData(columnIndex).image1"
                      class="problem-img"
                      draggable="true"
                      @dragstart="handleDragStart($event, columnIndex, 1)"
                      @dragend="handleDragEnd"
                      @dragover.prevent
                      @dragenter="handleDragEnter($event)"
                      @dragleave="handleDragLeave($event)"
                      @drop="handleDrop($event, columnIndex, 1)">
                      <img
                        :src="getCurrentColumnData(columnIndex).image1"
                        :style="{ transform: `rotate(${getCurrentColumnData(columnIndex).rotation1}deg)` }"
                        class="full-img"
                        alt="问题图片"
                        @error="handleImgError">
                      <div class="img-actions">
                        <span
                          class="img-action-item"
                          @click.stop="handleImgPreview(getCurrentColumnData(columnIndex).image1)">
                          <i class="el-icon-zoom-in"/>
                        </span>
                        <span
                          class="img-action-item"
                          @click.stop.prevent="handleRotateImage(columnIndex, 1)">
                          <i class="el-icon-refresh"/>
                        </span>
                        <span
                          class="img-action-item"
                          @click.stop="handleDeleteImage(columnIndex, 1, $event)">
                          <i class="el-icon-delete"/>
                        </span>
                      </div>
                    </div>
                    <div
                      v-else
                      class="problem-img no-image"
                      @click="uploadImage(columnIndex, 1)"
                      @dragover.prevent
                      @dragenter="handleDragEnter($event)"
                      @dragleave="handleDragLeave($event)"
                      @drop="handleDrop($event, columnIndex, 1)">
                      <i class="el-icon-picture-outline"/>
                      <p>请上传图片</p>
                    </div>

                    <div
                      v-if="getCurrentColumnData(columnIndex).image2"
                      class="problem-img"
                      draggable="true"
                      @dragstart="handleDragStart($event, columnIndex, 2)"
                      @dragend="handleDragEnd"
                      @dragover.prevent
                      @dragenter="handleDragEnter($event)"
                      @dragleave="handleDragLeave($event)"
                      @drop="handleDrop($event, columnIndex, 2)">
                      <img
                        :src="getCurrentColumnData(columnIndex).image2"
                        :style="{ transform: `rotate(${getCurrentColumnData(columnIndex).rotation2}deg)` }"
                        alt="问题图片"
                        class="full-img"
                        @error="handleImgError">
                      <div class="img-actions">
                        <span
                          class="img-action-item"
                          @click.stop="handleImgPreview(getCurrentColumnData(columnIndex).image2)">
                          <i class="el-icon-zoom-in"/>
                        </span>
                        <span
                          class="img-action-item"
                          @click.stop.prevent="handleRotateImage(columnIndex, 2)">
                          <i class="el-icon-refresh"/>
                        </span>
                        <span
                          class="img-action-item"
                          @click.stop="handleDeleteImage(columnIndex, 2, $event)">
                          <i class="el-icon-delete"/>
                        </span>
                      </div>
                    </div>
                    <div
                      v-else
                      class="problem-img no-image"
                      @click="uploadImage(columnIndex, 2)"
                      @dragover.prevent
                      @dragenter="handleDragEnter($event)"
                      @dragleave="handleDragLeave($event)"
                      @drop="handleDrop($event, columnIndex, 2)">
                      <i class="el-icon-picture-outline"/>
                      <p>请上传图片</p>
                    </div>
                  </div>
                  <div class="form-container">
                    <el-input
                      :rows="5"
                      :value="getCurrentColumnData(columnIndex).content"
                      type="textarea"
                      resize="none"
                      class="problem-textarea"
                      @input="updateColumnContent(columnIndex, $event)"/>
                  </div>
                </div>
              </el-col>
            </el-row>
            <!-- 公共分页组件 -->
            <div class="common-pagination-container">
              <el-pagination
                :current-page.sync="currentPage"
                :page-size="pageSize"
                :total="totalElements"
                layout="prev, pager, next"
                @current-change="handleCommonPageChange"/>
            </div>
          </div>
        </screen-border>
      </div>
    </div>

    <!-- 图片预览弹窗 -->
    <el-dialog
      :visible.sync="previewVisible"
      :append-to-body="true"
      title="图片预览"
      width="800px"
      @close="handlePreviewClose">
      <div class="preview-container">
        <img
          v-if="previewImage"
          :src="previewImage"
          :style="{ transform: `rotate(${previewRotation}deg)` }"
          style="width: 100%;"
          alt="预览图片"
          @error="handleImgError">
        <div class="preview-actions">
          <el-button
            type="primary"
            icon="el-icon-refresh-left"
            circle
            @click="rotatePreviewImage(-90)"/>
          <el-button
            type="primary"
            icon="el-icon-refresh-right"
            circle
            @click="rotatePreviewImage(90)"/>
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer">
        <el-button @click="cancelPreviewChanges">取消</el-button>
        <el-button
          type="primary"
          @click="applyPreviewChanges">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityWeeklyReportScreen/components/screen-border.vue'
import {
  typicalQualityProblemFindAllDate,
  typicalQualityProblemSaveAll
} from '@/api/screen'
import { deleteFileByIds, uploadFile, downloadFileById } from '@/api/system'
import { post } from '@/lib/Util'

export default {
  name: 'TypicalQualityProblem',
  components: {
    ScreenBorder
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      baseURL: 'http://************:9800/' + downloadFileById,
      defaultTemplate: '质量问题：\n责任单位：\n考核：',
      // 当前页码
      currentPage: 1,
      // 每页显示的数量
      pageSize: 4,
      // 总页数
      totalPages: 3,
      // 总记录数
      totalElements: 12,
      // 所有列的数据
      columnData: {},
      // 提交按钮加载状态
      submitLoading: {
        1: false,
        2: false,
        3: false,
        4: false
      },
      currentUploadColumn: 1,
      currentUploadPosition: 1,
      previewVisible: false,
      previewImage: '',
      previewRotation: 0, // 预览图片的旋转角度
      originalRotation: 0, // 原始图片的旋转角度，用于取消时恢复
      currentPreviewColumn: 0, // 当前预览的列
      currentPreviewPosition: 0, // 当前预览的位置
      uploadVisible: false,
      uploadFile: null,
      dragInfo: null // 保存拖拽时的图片信息
    }
  },
  watch: {
    selectDate: {
      handler() {
        this.getTypicalQualityProblem()
      }
    }
  },
  created() {
    this.getTypicalQualityProblem()
  },
  methods: {
    // 获取当前列的当前页数据
    getCurrentColumnData(columnIndex) {
      // 生成唯一的键，用于标识当前页面的特定列
      const key = `${this.currentPage}_${columnIndex}`

      // 如果该键不存在，则初始化一个空对象
      if (!this.columnData[key]) {
        // 使用Vue的响应式方法设置对象
        this.$set(this.columnData, key, {
          id: null,
          content: this.defaultTemplate,
          image1: '',
          image2: '',
          fileList: [], // 初始化为空数组
          rotation1: 0,
          rotation2: 0
        })
      }

      // 确保fileList是一个数组
      if (!this.columnData[key].fileList) {
        this.$set(this.columnData[key], 'fileList', [])
      }

      return this.columnData[key]
    },

    // 处理分页变化
    handleCommonPageChange(page) {
      this.currentPage = page
      // 获取新页的数据
      this.getTypicalQualityProblem()
    },

    async getTypicalQualityProblem() {
      const params = {
        setTime: this.selectDate,
        size: this.pageSize,
        page: this.currentPage
      }
      try {
        const res = await post(typicalQualityProblemFindAllDate, params)

        if (res && res.data) {
          // 更新分页信息
          // this.totalPages = Math.max(res.data.totalPages || 0, 3)
          // this.totalElements = Math.max(res.data.totalElements || 0, 12)

          this.totalPages = res.totalPages
          this.totalElements = res.totalElements

          // 回显数据
          this.handleDataDisplay(res.data)
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取典型质量问题数据失败')
      }
    },

    // 处理返回的数据并回显
    handleDataDisplay(data) {
      // 为当前页的每一列初始化默认数据
      for (let columnIndex = 1; columnIndex <= 4; columnIndex++) {
        const key = `${this.currentPage}_${columnIndex}`
        this.columnData[key] = {
          id: null,
          content: this.defaultTemplate,
          image1: '',
          image2: '',
          fileList: [],
          rotation1: 0,
          rotation2: 0
        }
      }

      // 处理API返回的数据
      if (data.content && data.content.length > 0) {
        // 遍历返回的数据项
        data.content.forEach((item, index) => {
          // 根据index决定放在哪一列（这里假设按顺序放入4列）
          const columnIndex = (index % 4) + 1
          const key = `${this.currentPage}_${columnIndex}`

          // 设置基本数据
          this.columnData[key].id = item.id
          this.columnData[key].content = item.problem || this.defaultTemplate

          // 确保fileList是一个有效的数组，并且每个元素都有fileNames属性
          if (item.fileList && Array.isArray(item.fileList)) {
            // 深拷贝fileList数据，避免引用问题
            const validFiles = item.fileList.filter(
              file => file && file.fileId && file.fileNames
            )
            // 使用Vue的响应式方法设置数组
            this.$set(
              this.columnData[key],
              'fileList',
              JSON.parse(JSON.stringify(validFiles))
            )
          } else {
            this.columnData[key].fileList = []
          }

          // 如果有图片，设置展示
          if (
            this.columnData[key].fileList &&
            this.columnData[key].fileList.length > 0
          ) {
            // 添加时间戳防止浏览器缓存
            const timestamp = new Date().getTime()

            // 设置第一张图片
            if (
              this.columnData[key].fileList[0] &&
              this.columnData[key].fileList[0].fileNames
            ) {
              this.columnData[key].image1 =
                this.baseURL +
                this.columnData[key].fileList[0].fileNames +
                '?t=' +
                timestamp
            } else {
              this.columnData[key].image1 = ''
            }

            // 设置第二张图片
            if (
              this.columnData[key].fileList[1] &&
              this.columnData[key].fileList[1].fileNames
            ) {
              this.columnData[key].image2 =
                this.baseURL +
                this.columnData[key].fileList[1].fileNames +
                '?t=' +
                timestamp
            } else {
              this.columnData[key].image2 = ''
            }
          } else {
            // 如果没有有效的文件列表，确保图片也被清空
            this.columnData[key].image1 = ''
            this.columnData[key].image2 = ''
          }
        })
      }
    },

    // 提交列数据
    async submitColumn(columnIndex) {
      const columnData = this.getCurrentColumnData(columnIndex)
      this.submitLoading[columnIndex] = true

      try {
        // 清理fileList，确保只有有效的文件被提交
        const cleanedFileList =
          columnData.fileList && Array.isArray(columnData.fileList)
            ? columnData.fileList.filter(file => file && file.fileNames)
            : []

        // 构建提交参数
        const params = {
          setTime: this.selectDate,
          data: {
            order: columnIndex.toString(),
            problem: columnData.content,
            fileList: cleanedFileList,
            page: this.currentPage,
            size: this.pageSize
          }
        }

        // 如果有ID，添加到参数中（更新模式）
        if (columnData.id) {
          params.data.id = columnData.id
        }

        const res = await post(typicalQualityProblemSaveAll, params)
        this.submitLoading[columnIndex] = false

        if (res.status === 1 || res.success || res.code === 200) {
          this.$message.success('提交成功！')
          // 刷新数据
          this.getTypicalQualityProblem()
        } else {
          this.$message.error('提交失败：' + (res.message || '未知错误'))
        }
      } catch (error) {
        console.error('提交出错：', error)
        this.$message.error('提交数据时发生错误')
        this.submitLoading[columnIndex] = false
      }
    },

    // 上传图片
    uploadImage(column, position) {
      this.currentUploadColumn = column
      this.currentUploadPosition = position

      // 创建临时input元素进行文件选择
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/*'
      input.onchange = e => {
        const file = e.target.files[0]
        if (!file) return

        const isImage = file.type.indexOf('image/') !== -1
        const isLt10M = file.size / 1024 / 1024 < 10

        if (!isImage) {
          this.$message.error('只能上传图片文件!')
          return
        }

        if (!isLt10M) {
          this.$message.error('图片大小不能超过 10MB!')
          return
        }

        const formData = new FormData()
        formData.append('files', file)
        this.uploadFileToServer(formData)
      }
      input.click()
    },

    // 发送文件到服务器
    async uploadFileToServer(formData) {
      try {
        const res = await post(uploadFile, formData, false, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (res.success && res.data && res.data.length > 0) {
          const fileInfo = res.data[0]
          const fileId = fileInfo.id
          const fileName = fileInfo.name || fileInfo.originalFilename
          const fileUrl = this.baseURL + fileId

          // 根据当前上传的列和位置设置图片
          const columnData = this.getCurrentColumnData(this.currentUploadColumn)

          // 确保fileList是一个有效的数组
          if (!columnData.fileList) {
            columnData.fileList = []
          }

          // 清理fileList中的无效项
          columnData.fileList = columnData.fileList.filter(
            file => file && file.fileId && file.fileNames
          )

          if (this.currentUploadPosition === 1) {
            // 设置第一张图片
            columnData.image1 = fileUrl
            columnData.rotation1 = 0 // 重置旋转角度

            // 更新或添加fileList中的第一项
            const newFileInfo = {
              fileId,
              fileName,
              fileNames: fileId
            }

            if (columnData.fileList.length > 0) {
              columnData.fileList[0] = newFileInfo
            } else {
              columnData.fileList.push(newFileInfo)
            }
          } else {
            // 设置第二张图片
            columnData.image2 = fileUrl
            columnData.rotation2 = 0 // 重置旋转角度

            // 更新或添加fileList中的第二项
            const newFileInfo = {
              fileId,
              fileName,
              fileNames: fileId
            }

            if (columnData.fileList.length > 1) {
              // 如果已经有两个项，更新第二个
              columnData.fileList[1] = newFileInfo
            } else if (columnData.fileList.length === 1) {
              // 如果只有一个项，添加第二个
              columnData.fileList.push(newFileInfo)
            } else {
              // 如果没有项，先添加一个空项，再添加第二个
              columnData.fileList.push({
                fileId: '',
                fileName: '',
                fileNames: ''
              })
              columnData.fileList.push(newFileInfo)
            }
          }

          this.$message.success('图片上传成功！')

          //自动保存
          this.submitColumn(this.currentUploadColumn)
        } else {
          this.$message.error('图片上传失败!')
        }
      } catch (error) {
        console.error('上传失败：', error)
        this.$message.error('图片上传失败')
      }
    },

    // 图片预览
    handleImgPreview(imgUrl) {
      // 找出当前预览的图片所在的列和位置
      for (let i = 1; i <= 4; i++) {
        const columnData = this.getCurrentColumnData(i)

        if (columnData.image1 === imgUrl) {
          this.currentPreviewColumn = i
          this.currentPreviewPosition = 1
          this.previewRotation = columnData.rotation1
          this.originalRotation = columnData.rotation1 // 保存原始旋转角度
          break
        } else if (columnData.image2 === imgUrl) {
          this.currentPreviewColumn = i
          this.currentPreviewPosition = 2
          this.previewRotation = columnData.rotation2
          this.originalRotation = columnData.rotation2 // 保存原始旋转角度
          break
        }
      }

      // 创建一个新的预览图片URL，添加时间戳参数以确保每次预览都是唯一的
      // 这样可以防止浏览器缓存导致的旋转问题
      const timestamp = new Date().getTime()
      const separator = imgUrl.includes('?') ? '&' : '?'
      this.previewImage = `${imgUrl}${separator}_t=${timestamp}`
      this.previewVisible = true
    },

    // 旋转预览图片
    async rotatePreviewImage(degree) {
      try {
        // 计算新的旋转角度
        this.previewRotation = (this.previewRotation + degree) % 360

        // 如果是负数，转换为正数角度
        if (this.previewRotation < 0) {
          this.previewRotation = 360 + this.previewRotation
        }

        // 只更新预览图片的旋转角度，不影响原图片
        // 这样可以防止同一张图片在不同位置的旋转互相影响

        // 当用户关闭预览窗口时，才将旋转角度应用到原图片
        // 这部分逻辑将在关闭预览窗口的处理中实现
      } catch (error) {
        console.error('旋转预览图片出错:', error)
        this.$message.error('旋转预览图片失败，请重试')
      }
    },

    // 旋转图片
    async handleRotateImage(columnIndex, position) {
      try {
        console.log(`旋转图片: 列=${columnIndex}, 位置=${position}`)

        // 获取当前页的数据
        const columnData = this.getCurrentColumnData(columnIndex)
        const rotationProperty = `rotation${position}`
        const currentRotation = columnData[rotationProperty] || 0

        // 每次旋转90度
        const newRotation = (currentRotation + 90) % 360
        console.log(`旋转角度: ${currentRotation} -> ${newRotation}`)

        // 使用Vue的响应式更新方式确保视图更新
        this.$set(columnData, rotationProperty, newRotation)

        // 强制DOM更新
        this.$nextTick(() => {
          // 查找对应的图片元素 - 改进选择器逻辑
          // 获取当前列的DOM元素
          const columnElement = document.querySelector(
            `.el-row .el-col:nth-child(${columnIndex})`
          )

          if (!columnElement) {
            console.error(
              `未找到列元素: .el-row .el-col:nth-child(${columnIndex})`
            )
            return
          }

          // 在当前列中查找图片容器
          const imagesContainer = columnElement.querySelector(
            '.images-container'
          )

          if (!imagesContainer) {
            console.error('未找到图片容器: .images-container')
            return
          }

          // 获取所有图片div元素
          const imgDivs = imagesContainer.querySelectorAll('.problem-img')
          console.log(`找到 ${imgDivs.length} 个图片div元素`)

          // 根据position找到对应的图片元素
          let imgElement = null

          if (position === 1) {
            // 第一个位置：找到第一个有图片的div（非no-image）
            for (let i = 0; i < imgDivs.length; i++) {
              if (!imgDivs[i].classList.contains('no-image')) {
                imgElement = imgDivs[i].querySelector('img')
                break
              }
            }
          } else if (position === 2) {
            // 第二个位置：找到第二个有图片的div（非no-image）
            let foundCount = 0
            for (let i = 0; i < imgDivs.length; i++) {
              if (!imgDivs[i].classList.contains('no-image')) {
                foundCount++
                if (foundCount === 2) {
                  imgElement = imgDivs[i].querySelector('img')
                  break
                }
              }
            }
          }

          if (imgElement) {
            // 直接设置transform样式以强制重绘
            imgElement.style.transform = `rotate(${newRotation}deg)`
            console.log(`已设置DOM元素旋转: ${newRotation}度`)
          } else {
            console.error('未找到图片元素，请确认图片已正确加载')
            // 尝试使用备用方法更新
            this.$forceUpdate()
          }
        })
      } catch (error) {
        console.error('旋转图片出错:', error)
        this.$message.error('旋转图片失败，请重试')
      }
    },

    // 删除图片
    async handleDeleteImage(columnIndex, position, event) {
      // 阻止事件冒泡，防止点击事件被其他元素捕获
      if (event) {
        event.stopPropagation()
        event.preventDefault()
      }

      // 显示加载状态
      this.$set(this.submitLoading, columnIndex, true)

      const columnData = this.getCurrentColumnData(columnIndex)

      try {
        // 找出对应位置的文件
        const fileInfo =
          position === 1 &&
          columnData.fileList &&
          columnData.fileList.length > 0
            ? columnData.fileList[0]
            : position === 2 &&
              columnData.fileList &&
              columnData.fileList.length > 1
              ? columnData.fileList[1]
              : null

        if (fileInfo && fileInfo.fileNames) {
          // 调用删除文件接口
          const del = await post(deleteFileByIds, { ids: [fileInfo.fileNames] })

          if (!del || !del.success) {
            this.$message.error('文件删除失败')
            this.$set(this.submitLoading, columnIndex, false)
            return
          }

          this.$message.success('图片删除成功')
        }

        // 无论文件是否存在或删除成功，都要清除本地数据
        // 这确保视图中的图片被移除
        if (position === 1) {
          // 清除第一张图片
          this.$set(columnData, 'image1', '')
          this.$set(columnData, 'rotation1', 0)

          // 如果有第二张图片，将其移到第一个位置
          if (columnData.image2) {
            this.$set(columnData, 'image1', columnData.image2)
            this.$set(columnData, 'image2', '')
            this.$set(columnData, 'rotation1', columnData.rotation2)
            this.$set(columnData, 'rotation2', 0)

            // 更新fileList
            if (columnData.fileList && columnData.fileList.length > 1) {
              const newFileList = [columnData.fileList[1]]
              this.$set(columnData, 'fileList', newFileList)
            } else {
              this.$set(columnData, 'fileList', [])
            }
          } else {
            // 没有第二张图片，直接清空fileList
            this.$set(columnData, 'fileList', [])
          }
        } else if (position === 2) {
          // 清除第二张图片
          this.$set(columnData, 'image2', '')
          this.$set(columnData, 'rotation2', 0)

          // 更新fileList，只保留第一项
          if (columnData.fileList && columnData.fileList.length > 0) {
            const newFileList = columnData.fileList.slice(0, 1)
            this.$set(columnData, 'fileList', newFileList)
          }
        }

        // 强制刷新视图
        this.$forceUpdate()

        // 自动提交更改，确保后端数据同步
        await this.submitColumn(columnIndex)

        // 重置加载状态
        this.$set(this.submitLoading, columnIndex, false)
      } catch (error) {
        console.error('删除文件错误:', error)
        this.$message.error('删除文件时发生错误')
        this.$set(this.submitLoading, columnIndex, false)
      }
    },

    // 处理拖拽开始
    handleDragStart(e, columnIndex, position) {
      // 保存拖拽源信息
      this.dragInfo = {
        sourceColumn: columnIndex,
        sourcePosition: position
      }

      // 设置拖拽效果
      e.dataTransfer.effectAllowed = 'move'

      // 为拖拽过程中添加视觉效果
      e.target.classList.add('dragging')

      // 设置拖拽数据
      e.dataTransfer.setData(
        'text/plain',
        JSON.stringify({
          columnIndex,
          position
        })
      )
    },

    // 处理拖拽结束
    handleDragEnd(e) {
      // 清除所有拖拽相关的类
      document.querySelectorAll('.problem-img').forEach(el => {
        el.classList.remove('dragging', 'drag-over')
      })

      // 清除拖拽信息
      this.dragInfo = null
    },

    // 处理拖拽进入目标元素
    handleDragEnter(e) {
      e.preventDefault()
      e.target.closest('.problem-img').classList.add('drag-over')
    },

    // 处理拖拽离开目标元素
    handleDragLeave(e) {
      e.preventDefault()
      e.target.closest('.problem-img').classList.remove('drag-over')
    },

    /*
     * 处理拖拽放置
     * 实现了以下功能：
     * 1. 同一列内图片移动：保留目标图片，不进行交换而是真正的移动
     * 2. 不同列间图片移动：将源图片移动到目标位置，并清除源位置
     * 3. 处理fileList数据，确保提交到后端的数据保持一致
     * 4. 视觉反馈：图片拖动时有清晰的状态指示
     */
    handleDrop(e, targetColumnIndex, targetPosition) {
      e.preventDefault()

      const sourceInfo = this.dragInfo

      // 如果没有源信息或源和目标相同，则不处理
      if (
        !sourceInfo ||
        (sourceInfo.sourceColumn === targetColumnIndex &&
          sourceInfo.sourcePosition === targetPosition)
      ) {
        return
      }

      // 获取源和目标的列数据
      const sourceColumn = this.getCurrentColumnData(sourceInfo.sourceColumn)
      const targetColumn = this.getCurrentColumnData(targetColumnIndex)

      // 获取源图片信息
      const sourceImg =
        sourceInfo.sourcePosition === 1
          ? sourceColumn.image1
          : sourceColumn.image2
      const sourceFileInfo =
        sourceColumn.fileList[sourceInfo.sourcePosition - 1]

      // 获取目标图片信息
      const targetImg =
        targetPosition === 1 ? targetColumn.image1 : targetColumn.image2
      const targetFileInfo =
        targetPosition - 1 < targetColumn.fileList.length
          ? targetColumn.fileList[targetPosition - 1]
          : null

      // 保存图片信息用于移动
      const sourceImgTemp = sourceImg
      const sourceFileInfoTemp = sourceFileInfo ? { ...sourceFileInfo } : null

      // 处理图片移动
      if (sourceInfo.sourceColumn === targetColumnIndex) {
        // 同一列内的图片移动，与不同列移动保持一致
        if (sourceInfo.sourcePosition === 1 && targetPosition === 2) {
          // 从第一个位置移到第二个位置

          // 如果目标位置已有图片，对调位置
          if (sourceColumn.image2) {
            // 保存目标位置原有图片
            const targetImgTemp = sourceColumn.image2
            const targetFileInfoTemp =
              sourceColumn.fileList.length > 1
                ? { ...sourceColumn.fileList[1] }
                : null
            const targetRotationTemp = sourceColumn.rotation2

            // 清空源位置
            sourceColumn.image1 = ''

            // 设置目标位置为源图片
            sourceColumn.image2 = sourceImgTemp
            sourceColumn.rotation2 = sourceColumn.rotation1

            // 设置源位置为目标原有图片
            sourceColumn.image1 = targetImgTemp
            sourceColumn.rotation1 = targetRotationTemp

            // 更新fileList
            if (sourceColumn.fileList.length > 0) {
              if (sourceColumn.fileList.length > 1) {
                const temp = { ...sourceColumn.fileList[1] }
                sourceColumn.fileList[1] = sourceFileInfoTemp
                sourceColumn.fileList[0] = temp
              } else {
                sourceColumn.fileList.push(sourceFileInfoTemp)
                sourceColumn.fileList[0] = {
                  fileId: '',
                  fileName: '',
                  fileNames: ''
                }
              }
            }
          } else {
            // 目标位置没有图片，直接移动
            // 清空源位置
            sourceColumn.image1 = ''

            // 设置目标位置
            sourceColumn.image2 = sourceImgTemp
            sourceColumn.rotation2 = sourceColumn.rotation1
            sourceColumn.rotation1 = 0

            // 更新fileList
            if (sourceColumn.fileList.length > 0) {
              if (sourceColumn.fileList.length > 1) {
                sourceColumn.fileList[1] = sourceFileInfoTemp
                sourceColumn.fileList.splice(0, 1)
              } else {
                sourceColumn.fileList[0] = null
                sourceColumn.fileList.push(sourceFileInfoTemp)
                sourceColumn.fileList.shift() // 移除第一个null元素
              }
            }
          }
        } else if (sourceInfo.sourcePosition === 2 && targetPosition === 1) {
          // 从第二个位置移到第一个位置

          // 如果目标位置已有图片，对调位置
          if (sourceColumn.image1) {
            // 保存目标位置原有图片
            const targetImgTemp = sourceColumn.image1
            const targetFileInfoTemp =
              sourceColumn.fileList.length > 0
                ? { ...sourceColumn.fileList[0] }
                : null
            const targetRotationTemp = sourceColumn.rotation1

            // 清空源位置
            sourceColumn.image2 = ''

            // 设置目标位置为源图片
            sourceColumn.image1 = sourceImgTemp
            sourceColumn.rotation1 = sourceColumn.rotation2

            // 设置源位置为目标原有图片
            sourceColumn.image2 = targetImgTemp
            sourceColumn.rotation2 = targetRotationTemp

            // 更新fileList
            if (sourceColumn.fileList.length > 0) {
              if (sourceColumn.fileList.length > 1) {
                const temp = { ...sourceColumn.fileList[0] }
                sourceColumn.fileList[0] = sourceFileInfoTemp
                sourceColumn.fileList[1] = temp
              } else {
                sourceColumn.fileList[0] = sourceFileInfoTemp
              }
            }
          } else {
            // 目标位置没有图片，直接移动
            // 清空源位置
            sourceColumn.image2 = ''

            // 设置目标位置
            sourceColumn.image1 = sourceImgTemp
            sourceColumn.rotation1 = sourceColumn.rotation2
            sourceColumn.rotation2 = 0

            // 更新fileList
            if (sourceColumn.fileList.length > 1) {
              sourceColumn.fileList[0] = sourceFileInfoTemp
              sourceColumn.fileList.splice(1, 1)
            }
          }
        }
      } else {
        // 不同列之间的图片移动
        if (sourceInfo.sourcePosition === 1) {
          // 源图片在第一个位置
          // 保存源图片信息
          const sourceImgTemp = sourceColumn.image1
          const sourceFileInfoTemp = sourceColumn.fileList[0]
            ? { ...sourceColumn.fileList[0] }
            : null
          const sourceRotationTemp = sourceColumn.rotation1

          // 清除源位置
          sourceColumn.image1 = ''

          // 如果源有第二张图，将它移到第一个位置
          if (sourceColumn.image2) {
            sourceColumn.image1 = sourceColumn.image2
            sourceColumn.image2 = ''

            // 同时调整fileList
            if (sourceColumn.fileList.length > 1) {
              sourceColumn.fileList[0] = sourceColumn.fileList[1]
              sourceColumn.fileList.splice(1, 1)
            }
          } else {
            // 如果没有第二张图，直接清除第一个fileList项
            if (sourceColumn.fileList.length > 0) {
              sourceColumn.fileList.splice(0, 1)
            }
          }

          // 设置目标位置
          if (targetPosition === 1) {
            // 如果目标有图，需要移到第二个位置
            if (targetColumn.image1) {
              targetColumn.image2 = targetColumn.image1

              // 同时调整fileList
              if (targetColumn.fileList.length > 0) {
                if (targetColumn.fileList.length > 1) {
                  // 如果已经有两个，则第二个被覆盖
                  targetColumn.fileList[1] = targetColumn.fileList[0]
                } else {
                  // 如果只有一个，则添加到第二个位置
                  targetColumn.fileList.push(targetColumn.fileList[0])
                }
              }
            }

            // 然后设置源图片到目标第一个位置
            targetColumn.image1 = sourceImgTemp
            targetColumn.rotation1 = sourceRotationTemp

            // 设置fileList
            if (sourceFileInfoTemp) {
              targetColumn.fileList[0] = sourceFileInfoTemp
            }
          } else {
            // 源图片设置到目标第二个位置
            targetColumn.image2 = sourceImgTemp
            targetColumn.rotation2 = sourceRotationTemp

            // 设置fileList
            if (sourceFileInfoTemp) {
              if (targetColumn.fileList.length === 0) {
                // 如果目标fileList为空，需要先添加一个空项
                targetColumn.fileList.push({
                  fileId: '',
                  fileName: '',
                  fileNames: ''
                })
              }

              if (targetColumn.fileList.length > 1) {
                targetColumn.fileList[1] = sourceFileInfoTemp
              } else {
                targetColumn.fileList.push(sourceFileInfoTemp)
              }
            }
          }
        } else {
          // 源图片在第二个位置
          // 保存源图片信息
          const sourceImgTemp = sourceColumn.image2
          const sourceFileInfoTemp =
            sourceColumn.fileList.length > 1
              ? { ...sourceColumn.fileList[1] }
              : null
          const sourceRotationTemp = sourceColumn.rotation2

          // 清除源位置
          sourceColumn.image2 = ''

          // 同时清除fileList第二项
          if (sourceColumn.fileList.length > 1) {
            sourceColumn.fileList.splice(1, 1)
          }

          // 设置目标位置
          if (targetPosition === 1) {
            // 如果目标有图，需要移到第二个位置
            if (targetColumn.image1) {
              targetColumn.image2 = targetColumn.image1

              // 调整fileList
              if (targetColumn.fileList.length > 0) {
                if (targetColumn.fileList.length > 1) {
                  targetColumn.fileList[1] = targetColumn.fileList[0]
                } else {
                  targetColumn.fileList.push(targetColumn.fileList[0])
                }
              }
            }

            // 设置源图片到目标第一个位置
            targetColumn.image1 = sourceImgTemp
            targetColumn.rotation1 = sourceRotationTemp

            // 设置fileList
            if (sourceFileInfoTemp) {
              targetColumn.fileList[0] = sourceFileInfoTemp
            }
          } else {
            // 源图片设置到目标第二个位置
            targetColumn.image2 = sourceImgTemp
            targetColumn.rotation2 = sourceRotationTemp

            // 设置fileList
            if (sourceFileInfoTemp) {
              if (targetColumn.fileList.length === 0) {
                targetColumn.fileList.push({
                  fileId: '',
                  fileName: '',
                  fileNames: ''
                })
              }

              if (targetColumn.fileList.length > 1) {
                targetColumn.fileList[1] = sourceFileInfoTemp
              } else {
                targetColumn.fileList.push(sourceFileInfoTemp)
              }
            }
          }
        }
      }

      // 确保清除空的文件记录
      for (let i = 1; i <= 4; i++) {
        const columnData = this.getCurrentColumnData(i)
        if (columnData.fileList) {
          columnData.fileList = columnData.fileList.filter(
            file => file && file.fileNames
          )

          // 确保图片和fileList一致
          if (!columnData.image1 && columnData.fileList.length > 0) {
            columnData.image1 = this.baseURL + columnData.fileList[0].fileNames
          }
          if (!columnData.image2 && columnData.fileList.length > 1) {
            columnData.image2 = this.baseURL + columnData.fileList[1].fileNames
          }
          if (columnData.image1 && columnData.fileList.length === 0) {
            columnData.image1 = ''
          }
          if (columnData.image2 && columnData.fileList.length <= 1) {
            columnData.image2 = ''
          }
        }
      }
    },

    // 自动保存列数据（拖拽后）
    async autoSaveColumns(...columnIndexes) {
      // 去重，确保每个列只保存一次
      const uniqueColumns = [...new Set(columnIndexes)]

      // 遍历每个受影响的列并提交
      const savePromises = uniqueColumns.map(async columnIndex => {
        const columnData = this.getCurrentColumnData(columnIndex)
        this.submitLoading[columnIndex] = true

        try {
          // 清理fileList，确保只有有效的文件被提交
          const cleanedFileList = columnData.fileList
            ? columnData.fileList.filter(file => file && file.fileNames)
            : []

          // 构建提交参数
          const params = {
            setTime: this.selectDate,
            data: {
              order: columnIndex.toString(),
              problem: columnData.content,
              fileList: cleanedFileList,
              page: this.currentPage,
              size: this.pageSize
            }
          }

          // 如果有ID，添加到参数中（更新模式）
          if (columnData.id) {
            params.data.id = columnData.id
          }

          const res = await post(typicalQualityProblemSaveAll, params)
          this.submitLoading[columnIndex] = false

          return res.status === 1
        } catch (error) {
          console.error(`第${columnIndex}列保存出错:`, error)
          this.submitLoading[columnIndex] = false
          return false
        }
      })

      // 等待所有保存完成
      const results = await Promise.all(savePromises)

      // 检查结果
      if (results.every(result => result === true)) {
        // 刷新数据
        this.getTypicalQualityProblem()
      } else {
        this.$message.error('自动保存时发生错误，请手动点击提交按钮')
      }
    },

    // 处理图片加载错误
    handleImgError(event) {
      event.target.src = require('@/assets/images/img-error.png')
    },

    // 更新列内容
    updateColumnContent(columnIndex, newContent) {
      const key = `${this.currentPage}_${columnIndex}`
      if (this.columnData[key]) {
        this.columnData[key].content = newContent
      }
    },

    // 处理预览窗口关闭
    handlePreviewClose() {
      // 默认取消更改
      this.cancelPreviewChanges()
    },

    // 取消预览中的更改
    cancelPreviewChanges() {
      // 关闭预览窗口
      this.previewVisible = false

      // 不应用任何更改，保持原始旋转角度
      this.previewRotation = this.originalRotation
    },

    // 应用预览中的更改
    async applyPreviewChanges() {
      try {
        if (this.currentPreviewColumn > 0 && this.currentPreviewPosition > 0) {
          const columnData = this.getCurrentColumnData(
            this.currentPreviewColumn
          )
          const rotationProperty = `rotation${this.currentPreviewPosition}`

          // 只有当旋转角度发生变化时才更新
          if (columnData[rotationProperty] !== this.previewRotation) {
            // 使用Vue的响应式更新方式
            this.$set(columnData, rotationProperty, this.previewRotation)
          }
        }

        // 关闭预览窗口
        this.previewVisible = false
      } catch (error) {
        console.error('应用预览更改出错:', error)
        this.$message.error('应用预览更改失败，请重试')
      }
    }
  }
}
</script>

<style scoped lang="less">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: hidden;

  .chart-row {
    margin-bottom: 10px;
    height: 100%;
    flex-direction: column;
  }

  .chart-row,
  .table-row {
    display: flex;
    gap: 10px;
    width: 100%;
  }

  .chart-box,
  .table-box {
    flex: 1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .chart-footer {
    margin: 0px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    padding: 5px 8px;
    background: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);
    width: 100%;
  }

  .chart-input {
    height: 100%;
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      height: 100%;
      padding: 0;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
        border-color: transparent !important;
      }
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      border-color: transparent !important;
    }
  }

  /deep/ .el-table {
    background-color: transparent;
    width: 100% !important;
    table-layout: fixed;

    &::before {
      display: none;
    }

    th {
      background-color: rgba(31, 198, 255, 0.3);
      color: #fff;
      border-color: #1fc6ff;
    }

    td {
      background-color: transparent;
      color: #fff;
      border-color: #2e4262;
    }

    tr {
      background-color: transparent;
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-content {
    padding: 0 10px 10px 10px;
    box-sizing: border-box;
    height: 100%;
    width: 100%;
  }

  .content-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
  }

  .common-pagination-container {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 8px;

    /deep/ .el-pagination {
      background-color: transparent;

      .el-pager li {
        background-color: rgba(31, 198, 255, 0.1);
        color: #fff;
        border: 1px solid rgba(31, 198, 255, 0.3);

        &.active {
          background-color: rgba(31, 198, 255, 0.5);
          color: #fff;
          border-color: #1fc6ff;
        }

        &:hover {
          color: #1fc6ff;
        }
      }

      .btn-prev,
      .btn-next {
        background-color: rgba(31, 198, 255, 0.1);
        color: #fff;

        &:hover {
          color: #1fc6ff;
        }
      }
    }
  }

  .problem-item {
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    padding: 0px 10px;
  }

  .images-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 10px;
    width: 100%;

    .problem-img {
      position: relative;
      width: 100%;
      height: 22vh;
      max-height: 30vh;
      border: 1px solid rgba(31, 198, 255, 0.3);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      cursor: grab;
      transition: all 0.3s ease;

      &.dragging {
        opacity: 0.6;
        transform: scale(0.98);
        border: 2px dashed #1fc6ff;
      }

      &.drag-over {
        border: 2px solid #1fc6ff;
        background-color: rgba(31, 198, 255, 0.1);
        box-shadow: 0 0 10px rgba(31, 198, 255, 0.5);
      }

      .img-actions {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s;
        z-index: 10;

        .img-action-item {
          margin: 0 10px;
          width: 40px;
          height: 40px;
          background-color: rgba(31, 198, 255, 0.3);
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          z-index: 11;

          i {
            color: white;
            font-size: 20px;
          }

          &:hover {
            background-color: rgba(31, 198, 255, 0.6);
          }
        }
      }

      &:hover .img-actions {
        opacity: 1;
      }

      &:hover {
        border-color: #1fc6ff;
        box-shadow: 0 0 8px rgba(31, 198, 255, 0.5);
      }

      &.no-image {
        color: #8c939d;
        cursor: pointer;

        i {
          font-size: 36px;
          margin-bottom: 10px;
        }

        p {
          margin: 0;
          font-size: 14px;
        }

        &:hover {
          background-color: rgba(31, 198, 255, 0.1);
        }
      }

      .full-img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    /* 屏幕高度小于等于826px时设置高度为23vh */
    @media (max-height: 826px) {
      .problem-img {
        height: 23vh;
      }
    }

    /* 屏幕高度大于826px且小于等于911px时设置高度为22vh */
    @media (min-height: 827px) and (max-height: 911px) {
      .problem-img {
        height: 25vh;
      }
    }

    /* 屏幕高度大于911px且小于等于985px时设置高度为27vh */
    @media (min-height: 912px) and (max-height: 985px) {
      .problem-img {
        height: 27vh;
      }
    }

    /* 屏幕高度大于985px时设置高度为29vh */
    @media (min-height: 986px) {
      .problem-img {
        height: 29vh;
      }
    }
  }

  .form-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;

    .problem-textarea {
      width: 100%;

      /deep/ .el-textarea__inner {
        // height: 18vh;
        font-size: 16px;
        line-height: 1.5;
        white-space: pre-wrap;
        background: transparent;
        border: 1px solid rgba(31, 198, 255, 0.3);
        color: #fff;

        &:focus {
          border-color: #1fc6ff;
        }
      }
    }
  }

  .submit-btn {
    width: 68px !important;
    height: 28px !important;
    padding: 0 !important;
    line-height: 26px !important;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    color: #fff;
    font-size: 14px;
    margin: 10px 0;
    align-self: flex-end;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
    }
  }

  /deep/ .el-textarea__inner,
  /deep/ .el-input__inner {
    background: transparent;
    border: 1px solid rgba(31, 198, 255, 0.3);
    color: #fff;

    &:focus {
      border-color: #1fc6ff;
    }
  }

  .preview-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    img {
      transition: transform 0.3s ease;
      max-height: 60vh;
      object-fit: contain;
    }

    .preview-actions {
      margin-top: 20px;
      display: flex;
      justify-content: center;
      gap: 20px;
    }
  }
}
</style>
