<template>
  <div class="content">
   
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <custom-table
                :title="'在制品统计（月度）'"
                :setting="tableObj"
                :url-list="tableUrl.list"
                :url-save="tableUrl.save"
                :params="{type: 'B'}"
                :select-date="selectDate"
                :show-table="false"
                @change="getChartZzp">
                <template v-slot:content>
                  <div class="chart-wrapper">
                    <div
                      class="cards">
                      <div class="card">
                        <span class="name">累计产量</span>
                        <span class="num"><em>{{ zzpMonth.output }}</em> t</span>
                      </div>
                      <div class="card">
                        <span class="name">超欠</span>
                        <span class="num"><em :class="{'red': zzpMonth.targetSchedule < 0, 'green': zzpMonth.targetSchedule >= 0}">{{ zzpMonth.targetSchedule }}</em> t</span>
                      </div>
                      <div class="card">
                        <span class="name">超欠进度</span>
                        <span class="num"><em>{{ zzpMonth.percent }}</em> %</span>
                      </div>
                    </div>
                    <div style="height: 85%">
                      <!-- <single-bars-chart1
                        :show-legend="false"
                        :bar-width="20"
                        :chart-data="zzpMonth.bar1"
                        :x-data="zzpMonth.barx"/> -->
                      <stock-line-chart
                        :last-month-data="stock1.blankLastMonth || '0'"
                        :month-plan-data="stock1.blankMonthPlan || '0'"
                        :show-legend="true"
                        :chart-data="stock1.bar1"
                        :x-data="stock1.barX"/>
                    </div>
                  </div>
                </template>
              </custom-table>
            </div>
            <div class="content-hold"/>
            <div class="content-item">
              <!-- <screen-border title="400°热装热送率（月度）"> -->
              <custom-table3
                :title="'400°热装热送率（月度）'"
                :setting="tableObj2"
                :url-list="tableUrl2.list"
                :url-save="tableUrl2.save"
                :select-date="selectDate"
                :show-table="false"
                :get-other="getOther"
                @change="getChartZzp">
                <template v-slot:content>
                  <div class="chart-wrapper">
                    <div
                      class="cards">
                      <div class="card">
                        <span class="name">总计</span>
                        <span class="num"><em>{{ other.bar3Total || '-' }}</em>%</span>
                      </div>
                    </div>
                    <div style="height: 85%">
                      <!-- <bars-chart
                      :show-legend="false"
                      :bar-width="20"
                      :chart-data="other.bar3"
                      :x-data="['板卷厂', '宽厚板厂', '中板厂']"
                      unit="%"/> -->
                      <el-row 
                        :gutter="20" 
                        class="full-height">
                        <el-col 
                          :span="6" 
                          class="full-height">  <div style="height: 90%">
                            <gauge-chart :chart-data="chartData"/>
                        </div><div style="text-align:center">总计</div></el-col>
                        <el-col 
                          :span="6" 
                          class="full-height">  <div style="height: 90%">
                            <gauge-chart :chart-data="chartData1"/>
                          </div>
                        <div style="text-align:center">中板厂</div></el-col>
                        <el-col 
                          :span="6" 
                          class="full-height">  <div style="height: 90%">
                            <gauge-chart :chart-data="chartData2"/>
                          </div>
                        <div style="text-align:center">宽厚板厂</div></el-col>
                        <el-col 
                          :span="6" 
                          class="full-height">  <div style="height: 90%">
                            <gauge-chart :chart-data="chartData3"/>
                        </div> <div style="text-align:center">板卷厂</div></el-col>
                      </el-row>
                    </div>
                  </div>
                </template>
              </custom-table3>
              <!-- </screen-border> -->
              <!-- <screen-border :title="'坯库存 ' + searchTime.format('yyyy年MM月')">
                <template v-slot:headerRight/>
                <div class="chart-wrapper">
                  <div
                    class="chart">
                    <stock-line-chart
                      :last-month-data="stock1.blankLastMonth || '0'"
                      :month-plan-data="stock1.blankMonthPlan || '0'"
                      :show-legend="true"
                      :chart-data="stock1.bar1"
                      :x-data="stock1.barX"/>
                  </div>
                </div>
              </screen-border> -->
            </div>
          </div>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border title="综合非计划（月度）">
                <template v-slot:headerRight>
                  <slot name="headerRight"/>
                  <template >
                    <span
                      v-command="'/screen/productionKpiScreen/tripleOrderTrack'"
                      class="screen-btn"
                      @click="dialogVisible = true">
                      <el-icon class="el-icon-edit-outline"/>
                      操作
                    </span>
                  </template>
  
                </template>
                <div class="chart-wrapper">
                  <div
                    class="cards">
                    <div class="card">
                      <span class="name">总计</span>
                      <span class="num"><em>{{ other.bar2Total || '-' }}</em>%</span>
                    </div>
                  </div>
                  <div style="height: 85%">
                    <bars-chart
                      :show-legend="false"
                      :bar-width="20"
                      :chart-data="other.bar2"
                      :x-data="['板卷厂', '宽厚板厂', '中板厂']"
                      unit="%"/>
                  </div>
                </div>
              </screen-border>
         
            </div>
            <div class="content-hold"/>
            <div class="content-item">
              <screen-border title="合同兑现率（月度）">
                <div class="chart-wrapper">
                  <div
                    class="cards">
                    <div class="card">
                      <span class="name">总计</span>
                      <span class="num"><em>{{ other.bar1Total || '-' }}</em>%</span>
                    </div>
                  </div>
                  <el-row 
                    :gutter="20" 
                    class="full-height">
                    <el-col 
                      :span="6" 
                      class="full-height">  <div style="height: 90%">
                        <gauge-chart :chart-data="chartData4"/>
                    </div><div style="text-align:center">总计</div></el-col>
                    <el-col 
                      :span="6" 
                      class="full-height">  <div style="height: 90%">
                        <gauge-chart :chart-data="chartData5"/>
                      </div>
                    <div style="text-align:center">中板厂</div></el-col>
                    <el-col 
                      :span="6" 
                      class="full-height">  <div style="height: 90%">
                        <gauge-chart :chart-data="chartData6"/>
                      </div>
                    <div style="text-align:center">宽厚板厂</div></el-col>
                    <el-col 
                      :span="6" 
                      class="full-height">  <div style="height: 90%">
                        <gauge-chart :chart-data="chartData7"/>
                    </div> <div style="text-align:center">板卷厂</div></el-col>
                  </el-row>
                
                </div>
              </screen-border>
          
            </div>
          
          </div>
        </el-col>
        
      </el-row>
    </div>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
 
 <script>
import ScreenBorder from '@/pages/screen/productionKpiScreen/component/screen-border'
import SingleBarsChart from '@/pages/screen/productionKpiScreen/component/single-bars-chart'
import SingleBarsChart2 from '@/pages/screen/productionKpiScreen/component/single-bars-chart2'
import SingleBarsChart1 from '@/pages/screen/productionKpiScreen/component/single-bars-chart1'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import BarsChart from '@/pages/screen/productionKpiScreen/component/bars-chart'
import CircleBarChart from '@/pages/screen/productionKpiScreen/component/circle-bar-chart'
import StockLineChart from '@/pages/screen/morningMeeting/component/stock-line-chart'
import CustomTable from '@/pages/screen/productionKpiScreen/component/custom-table'
import CustomTable3 from '@/pages/screen/productionKpiScreen/component/custom-table3'
import GaugeChart from '@/pages/screen/technologyMeeting/component/gauge-chart'
import { post } from '@/lib/Util'
import * as _ from 'lodash'
import {
  findBlankDetail,
  findBlankStock,
  findBlankStockPltZj,
  findBoardParameterByDateAndPara,
  findHeatTreatmentYieldByDate,
  findKeyIndic,
  findKeyResultByDate1,
  findSteelOutputByDate,
  findThreeFactoryOrderByDate,
  saveBoardParameter,
  saveKeyResul,
  saveSteelOutput,
  saveKeyIndic,
  steelOutputTask
} from '@/api/screen'
import { math } from '@/lib/Math'
import moment from 'moment'
import lodash from 'lodash'
export default {
  name: 'Output',
  components: {
    CustomTable,
    BarsChart,
    StockLineChart,
    SingleBarsChart,
    SingleBarsChart2,
    SingleBarsChart1,
    SingleBarsChart2,
    ScreenBorder,
    CircleBarChart,
    GaugeChart,
    CustomTable3
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      steelOutputTask: steelOutputTask,
      loading: false,
      steelYesterday: {
        bar1: [
          {
            value: 0,
            plan: 0,
            unit: '吨'
          }
        ],
        bar2: [
          {
            value: 0,
            plan: 0,
            unit: '炉'
          },
          {
            value: 0,
            plan: 0,
            unit: '炉'
          }
        ],
        failReason: '',
        notice: '',
        noticeEdit: '',
        editType: '',
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      stock1: {
        bar1: [],
        barX: [],
        gridData: [],
        dialogVisible: false,
        detailVisible: false,
        blankMonthPlan: '',
        blankLastMonth: ''
      },
      chartData: [
        {
          value: 70,
          name: '计划',
          title: {
            offsetCenter: ['-33px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 72,
          name: '实际',
          title: {
            offsetCenter: ['-33px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        }
      ],
      chartData1: [
        {
          value: 70,
          name: '计划',
          title: {
            offsetCenter: ['-33px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 72,
          name: '实际',
          title: {
            offsetCenter: ['-33px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        }
      ],
      chartData2: [
        {
          value: 70,
          name: '计划',
          title: {
            offsetCenter: ['-33px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 72,
          name: '实际',
          title: {
            offsetCenter: ['-33px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        }
      ],
      chartData3: [
        {
          value: 70,
          name: '计划',
          title: {
            offsetCenter: ['-33px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 72,
          name: '实际',
          title: {
            offsetCenter: ['-33px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        }
      ],
      chartData4: [
        {
          value: 70,
          name: '计划',
          title: {
            offsetCenter: ['-33px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 72,
          name: '实际',
          title: {
            offsetCenter: ['-33px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        }
      ],
      chartData5: [
        {
          value: 70,
          name: '计划',
          title: {
            offsetCenter: ['-33px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 72,
          name: '实际',
          title: {
            offsetCenter: ['-33px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        }
      ],
      chartData6: [
        {
          value: 70,
          name: '计划',
          title: {
            offsetCenter: ['-33px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 72,
          name: '实际',
          title: {
            offsetCenter: ['-33px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        }
      ],
      chartData7: [
        {
          value: 70,
          name: '计划',
          title: {
            offsetCenter: ['-33px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 72,
          name: '实际',
          title: {
            offsetCenter: ['-33px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        }
      ],
      factoryOrder: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      other: {
        bar1: [],
        bar1Total: null,
        bar2: [],
        bar2Total: null,
        bar3: [],
        bar3Total: null
      },
      tableUrl: {
        save: saveKeyResul,
        list: findKeyResultByDate1
      },
      tableUrl2: {
        save: saveKeyIndic,
        list: findKeyIndic
      },
      tableObj: [
        {
          keyQuery: 'plt',
          keySave: 'plt',
          label: '轧钢厂'
        },
        {
          keyQuery: 'unit',
          keySave: 'unit',
          label: '单位'
        },
        {
          keyQuery: 'cumulativeOutput',
          keySave: 'cumulativeOutput',
          label: '累计产量'
        },
        {
          keyQuery: 'targetProduction',
          keySave: 'targetProduction',
          label: '目标'
        },
        {
          keyQuery: 'targetSchedule',
          keySave: 'targetSchedule',
          label: '超欠目标进度'
        },
        {
          keyQuery: 'avgDailyProduction',
          keySave: 'avgDailyProduction',
          label: '日需均产'
        },
        {
          keyQuery: 'type',
          keySave: 'type',
          label: '类型', //  A 综判 B 在制品
          show: false
        }
      ],
      tableObj2: [
        {
          keyQuery: '热装热送率事业部计划',
          keySave: '热装热送率事业部计划',
          label: '热装热送率事业部计划'
        },
        {
          keyQuery: '热装热送率中板厂计划',
          keySave: '热装热送率中板厂计划',
          label: '热装热送率中板厂计划'
        },
        {
          keyQuery: '热装热送率宽厚板厂计划',
          keySave: '热装热送率宽厚板厂计划',
          label: '热装热送率宽厚板厂计划'
        },
        {
          keyQuery: '热装热送率板卷厂计划',
          keySave: '热装热送率板卷厂计划',
          label: '热装热送率板卷厂计划'
        }
      ],
      zzpMonth: {
        bar1: [],
        failReason: '',
        barx: [],
        output: 0,
        targetSchedule: 0,
        percent: 0
      }
    }
  },
  computed: {
    searchTime: function() {
      return moment(this.cDate).subtract(2, 'day')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.$nextTick(() => {
        this.loadData()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.loadData()
    //  this.calculateHeight()
    //  window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    async loadData() {
      this.$nextTick(() => {
        //   this.getSteal()
        this.getZZP()
        //   this.getHeat()
        this.getOther()
        this.getfactoryOrder()
      })
    },
    handlePreview(file) {
      try {
        LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
          data = LAY_EXCEL.filterImportData(data, {
            num: 'A',
            plt: 'B',
            unit: 'C',
            plan: 'D',
            reality: 'E',
            complete: 'F',
            unfinishedCause: 'G',
            responsibleUnit: 'H',
            monplan: 'I',
            targetProduction: 'J',
            cumulativeOutput: 'K',
            mtcPfdTime: 'L',
            rmMtTime: 'M',
            targetSchedule: 'N',
            avgdailyproduction: 'O'
          })
          // 去除第一行
          const sheet = data[0].Sheet1 || data[0].sheet1
          sheet.shift()
          // 表格信息
          this.steelYesterday.showGridData = sheet.map(item => {
            item.unit = item.unit.trim()
            item.targetSchedule = item.targetSchedule.toString()
            item.type = this.steelYesterday.editType
            if (item.targetSchedule.includes('%')) {
              item.targetSchedule = item.targetSchedule.replace('%', '')
            } else {
              item.targetSchedule = Number(
                math.multiply(Number(item.targetSchedule), 100).toFixed(2)
              )
            }
            return item
          })
          this.$message.success('解析成功！')
        })
      } catch (e) {
        this.$message.warning('解析失败！')
      }
    },
    async saveSteel() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        type: this.steelYesterday.editType,
        data: this.steelYesterday.showGridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(saveSteelOutput, params).then(res => {
        //
        if (res.status === 1) {
          this.getSteal()
        } else {
          this.$message.warning('保存失败！')
        }
        const params2 = {
          data: [
            {
              parameter: 'outputNotice',
              content: this.steelYesterday.noticeEdit,
              setDate: this.cDate
            }
          ]
        }
        post(saveBoardParameter, params2).then(res => {
          this.loading = false
          if (res.status === 1) {
            this.steelYesterday.dialogVisible = false
            this.$message.success('保存成功！')
          }
        })
      })
    },
    exportSteel() {
      const data = [
        {
          num: '序号',
          plt: '产线',
          unit: '单位',
          plan: '计划',
          reality: '实际',
          complete: '是否完成',
          unfinishedCause: '未完成原因',
          responsibleUnit: '责任单位',
          monplan: '月计划',
          targetProduction: '月目标',
          cumulativeOutput: '累计产量',
          mtcPfdTime: '已执行检修时间',
          rmMtTime: '剩余检修时间',
          targetSchedule: '超欠目标进度',
          avgDailyProduction: '日需均产'
        }
      ].concat(
        _.cloneDeep(this.steelYesterday.showGridData).map(item => {
          item.targetSchedule = item.targetSchedule + '%'
          delete item.type
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `钢产量详情（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    importSteelData(date) {
      post(findSteelOutputByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.steelYesterday.showGridData = res.data
          .filter(item => item.type === this.steelYesterday.editType)
          .map(item => {
            return {
              num: item.num,
              plt: item.plt,
              unit: item.unit,
              plan: item.plan,
              reality: item.reality,
              complete: item.complete,
              unfinishedCause: item.unfinishedcause,
              responsibleUnit: item.responsibleunit,
              monplan: item.monplan,
              targetProduction: item.targetproduction,
              cumulativeOutput: item.cumulativeoutput,
              mtcPfdTime: item.mtcpfdtime,
              rmMtTime: item.rmmttime,
              targetSchedule: Number(item.targetschedule || 0).toFixed(2),
              avgDailyProduction: item.avgdailyproduction,
              type: item.type
            }
          })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 数据管理
    clearShowGridData(name) {
      this[name].showGridData = []
    },
    addShowGridData(name) {
      this[name].showGridData.push({})
    },
    delShowGridData(index, name) {
      this[name].showGridData.splice(index, 1)
    },
    async getZZP() {
      const monthStart = moment(this.cDate).format('D') == 2
      // 参数
      const parameters = await post(findKeyResultByDate1, {
        setDate: this.cDate,
        type: 'B'
      })
      // alert('aaa')
      // const C1 = parameters.find(item => item.plt.trim() === 'C1')
      // const C2 = parameters.find(item => item.plt.trim() === 'C2')
      // const C3 = parameters.find(item => item.plt.trim() === 'C3')
      // console.log('C1111111111111', C1)
      // this.stock1.blankMonthPlan = this.getParam(
      //   'blankMonthPlan',
      //   parameters.data
      // )
      // this.stock1.blankLastMonth = this.getParam(
      //   'blankLastMonth',
      //   parameters.data
      // )
      // 坯料
      // const zrStock = await post(findBlankStock, {
      //   date: this.searchTime.format('yyyyMM')
      // })
      // const zrStock2 = monthStart
      //   ? await post(findBlankStock, {
      //       date: this.$moment(this.cDate).format('yyyyMM')
      //     })
      //   : { data: [] }
      // const stockZj = await post(findBlankStockPltZj, {
      //   date: this.searchTime.format('yyyyMM')
      // })
      // const stockZj2 = monthStart
      //   ? await post(findBlankStockPltZj, {
      //       date: this.$moment(this.cDate).format('yyyyMM')
      //     })
      //   : { data: [] }
      this.stock1.barX = parameters.data
        .filter(item => item.plt === 'C1')
        .map(item => item.setDate.substr(5, 6))
      let all = []
      for (let i = 0; i < this.stock1.barX.length; i++) {
        all.push(
          parameters.data
            .filter(item => item.plt === 'C1')
            .map(item => parseInt(item.cumulativeOutput))[i] +
            parameters.data
              .filter(item => item.plt === 'C2')
              .map(item => parseInt(item.cumulativeOutput))[i] +
            parameters.data
              .filter(item => item.plt === 'C3')
              .map(item => parseInt(item.cumulativeOutput))[i]
        )
      }
      this.stock1.bar1 = [
        {
          name: '累计',
          data: all
        },
        {
          name: '中板厂',
          data: parameters.data
            .filter(item => item.plt === 'C1')
            .map(item => parseInt(item.cumulativeOutput))
        },
        {
          name: '宽厚板厂',
          data: parameters.data
            .filter(item => item.plt === 'C2')
            .map(item => parseInt(item.cumulativeOutput))
        },
        {
          name: '板卷厂',
          data: parameters.data
            .filter(item => item.plt === 'C3')
            .map(item => parseInt(item.cumulativeOutput))
        }
      ]
    },
    getOther() {
      post(findKeyIndic, {
        setDate: this.$moment(this.prevDate).format('YYYYMM'),
        time: this.cDate
      }).then(res => {
        // this.other.bar1 = [
        //   {
        //     name: '合同兑现率',
        //     data: [
        //       Number(res['合同兑现率板卷厂'] * 100).toFixed(2),
        //       Number(res['合同兑现率宽厚板厂'] * 100).toFixed(2),
        //       Number(res['合同兑现率中板厂'] * 100).toFixed(2)
        //     ]
        //   }
        // ]
        this.chartData4[1].value = Number(
          res['合同兑现率事业部'] * 100
        ).toFixed(2)
        this.chartData5[1].value = Number(
          res['合同兑现率中板厂'] * 100
        ).toFixed(2)
        this.chartData6[1].value = Number(
          res['合同兑现率宽厚板厂'] * 100
        ).toFixed(2)
        this.chartData7[1].value = Number(
          res['合同兑现率板卷厂'] * 100
        ).toFixed(2)
        this.other.bar1Total = Number(res['合同兑现率事业部'] * 100).toFixed(2)
        //   this.setTopData('合同兑现率', this.other.bar1Total)
        this.other.bar2 = [
          {
            name: '综合非计划',
            data: [
              Number(res['综合非计划板卷厂'] * 100).toFixed(2),
              Number(res['综合非计划宽厚板厂'] * 100).toFixed(2),
              Number(res['综合非计划中板厂'] * 100).toFixed(2)
            ]
          }
        ]
        this.other.bar2Total = Number(res['综合非计划事业部'] * 100).toFixed(2)
        //   this.setTopData('综合非计划', this.other.bar2Total)
        this.chartData[1].value = Number(res['热装热送率事业部'] * 100).toFixed(
          2
        )
        this.chartData[0].value = Number(res['热装热送率事业部计划']).toFixed(2)
        this.chartData1[1].value = Number(
          res['热装热送率中板厂'] * 100
        ).toFixed(2)
        this.chartData1[0].value = Number(res['热装热送率中板厂计划']).toFixed(
          2
        )
        this.chartData2[1].value = Number(
          res['热装热送率宽厚板厂'] * 100
        ).toFixed(2)
        this.chartData2[0].value = Number(
          res['热装热送率宽厚板厂计划']
        ).toFixed(2)
        this.chartData3[1].value = Number(
          res['热装热送率板卷厂'] * 100
        ).toFixed(2)
        this.chartData3[0].value = Number(res['热装热送率板卷厂计划']).toFixed(
          2
        )
        this.other.bar3Total = Number(res['热装热送率事业部'] * 100).toFixed(2)
        //   this.setTopData('热装热送率', this.other.bar3Total)
      })
    },
    getfactoryOrder() {
      post(findThreeFactoryOrderByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.factoryOrder.showGridData = res.data.map(item => {
          return {
            thFactoryOrder: item.thfactoryorder,
            waitSteelmaking: item.waitsteelmaking,
            waitSteelrolling: item.waitsteelrolling,
            postponeSteelmaking: item.postponesteelmaking,
            postponeSteelrolling: item.postponesteelrolling,
            unsoldOrder: item.unsoldorder,
            thFactoryOrdTotal: item.thfactoryordtotal,
            ordeNotch: item.ordenotch,
            vacuumSteelConfirm: item.vacuumsteelconfirm,
            commonSteelConfirm: item.commonsteelconfirm,
            confirmTotal: item.confirmtotal
          }
        })
        this.factoryOrder.gridData = lodash.cloneDeep(
          this.factoryOrder.showGridData
        )
      })
    },
    tableTotalClass(row) {
      // console.log(row)
      if (row.row.thFactoryOrder && row.row.thFactoryOrder.trim() === '总计') {
        return 'table-total'
      }
      return ''
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
      if (row.thFactoryOrder === '总计') {
        if (columnIndex === 1) {
          return [1, 2]
        } else if (columnIndex === 3) {
          return [1, 3]
        } else if (columnIndex === 6) {
          return [1, 1]
        } else if (columnIndex < 6 && columnIndex > 1) {
          return [0, 0]
        } else {
          return [1, 1]
        }
      }
      if (row.thFactoryOrder === '备注') {
        if (columnIndex === 1) {
          return [1, 10]
        }
        if (columnIndex < 11 && columnIndex > 1) {
          return [0, 0]
        } else {
          return [1, 1]
        }
      }
    },
    calculateHeight() {
      this.factoryOrder.maxHeight = this.$refs.table1.offsetHeight
    },
    // 在制品统计 （月度）
    getChartZzp(data) {
      try {
        const C1 = data.find(item => item.plt.trim() === 'C1')
        const C2 = data.find(item => item.plt.trim() === 'C2')
        const C3 = data.find(item => item.plt.trim() === 'C3')
        //   console.log('C1', C1)
        //   console.log('C2', C2)
        //   console.log('C3', C3)
        Object.assign(this.zzpMonth, {
          bar1: [
            {
              value: C1 ? C1.cumulativeOutput : 0,
              plan: C1 ? C1.targetProduction : 0,
              finished: C1.targetSchedule >= 0,
              totalText: C1.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C1.targetSchedule ? C1.targetSchedule : '',
              schedule: C1.targetSchedule
                ? math.divide(
                    math.multiply(C1.targetSchedule || 0, C1.targetProduction),
                    100
                  )
                : '',
              avgDaily: C1.avgDailyProduction ? C1.avgDailyProduction : ''
            },
            {
              value: C2 ? C2.cumulativeOutput : 0,
              plan: C2 ? C2.targetProduction : 0,
              finished: C2.targetSchedule >= 0,
              totalText: C2.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C2.targetSchedule ? C2.targetSchedule : '',
              schedule: C2.targetSchedule
                ? math.divide(
                    math.multiply(C2.targetSchedule || 0, C2.targetProduction),
                    100
                  )
                : '',
              avgDaily: C2.avgDailyProduction ? C2.avgDailyProduction : ''
            },
            {
              value: C3 ? C3.cumulativeOutput : 0,
              plan: C3 ? C3.targetProduction : 0,
              finished: C3.targetSchedule >= 0,
              totalText: C3.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C3.targetSchedule ? C3.targetSchedule : '',
              schedule: C3.targetSchedule
                ? math.divide(
                    math.multiply(C3.targetSchedule || 0, C3.targetProduction),
                    100
                  )
                : '',
              avgDaily: C3.avgDailyProduction ? C3.avgDailyProduction : ''
            }
          ],
          targetSchedule: math.add(
            math.multiply(C1.targetSchedule, C1.targetProduction) / 100,
            math.multiply(C2.targetSchedule, C2.targetProduction) / 100,
            math.multiply(C3.targetSchedule, C3.targetProduction) / 100
          ),
          output: math.add(
            C1.cumulativeOutput,
            C2.cumulativeOutput,
            C3.cumulativeOutput
          ),
          barx: ['09-26', '09-27']
        })
        //   this.setTopData(
        //     '在制品',
        //     math
        //       .add(C1.cumulativeOutput, C2.cumulativeOutput, C3.cumulativeOutput)
        //       .toFixed(0)
        //   )
        // 计算进度
        this.rollMonth.percent = math.multiply(
          math
            .divide(
              this.rollMonth.targetSchedule,
              math.add(
                C1.targetProduction,
                C2.targetProduction,
                C3.targetProduction
              )
            )
            .toFixed(4),
          100
        )
      } catch (e) {}
    }
    // 宗判 （月度）
    //  getChartZp(data) {
    //    try {
    //      const C1 = data.find(item => item.plt.trim() === 'C1')
    //      const C2 = data.find(item => item.plt.trim() === 'C2')
    //      const C3 = data.find(item => item.plt.trim() === 'C3')
    //      Object.assign(this.zpMonth, {
    //        bar1: [
    //          {
    //            value: C1 ? C1.cumulativeOutput : 0,
    //            plan: C1 ? C1.targetProduction : 0,
    //            finished: C1.targetSchedule >= 0,
    //            totalText: C1.targetProduction ? '目标' : '计划',
    //            unit: '吨',
    //            targetSchedule: C1.targetSchedule ? C1.targetSchedule : '',
    //            schedule: C1.targetSchedule
    //              ? math.divide(
    //                  math.multiply(C1.targetSchedule || 0, C1.targetProduction),
    //                  100
    //                )
    //              : '',
    //            avgDaily: C1.avgDailyProduction ? C1.avgDailyProduction : ''
    //          },
    //          {
    //            value: C2 ? C2.cumulativeOutput : 0,
    //            plan: C2 ? C2.targetProduction : 0,
    //            finished: C2.targetSchedule >= 0,
    //            totalText: C2.targetProduction ? '目标' : '计划',
    //            unit: '吨',
    //            targetSchedule: C2.targetSchedule ? C2.targetSchedule : '',
    //            schedule: C2.targetSchedule
    //              ? math.divide(
    //                  math.multiply(C2.targetSchedule || 0, C2.targetProduction),
    //                  100
    //                )
    //              : '',
    //            avgDaily: C2.avgDailyProduction ? C2.avgDailyProduction : ''
    //          },
    //          {
    //            value: C3 ? C3.cumulativeOutput : 0,
    //            plan: C3 ? C3.targetProduction : 0,
    //            finished: C3.targetSchedule >= 0,
    //            totalText: C3.targetProduction ? '目标' : '计划',
    //            unit: '吨',
    //            targetSchedule: C3.targetSchedule ? C3.targetSchedule : '',
    //            schedule: C3.targetSchedule
    //              ? math.divide(
    //                  math.multiply(C3.targetSchedule || 0, C3.targetProduction),
    //                  100
    //                )
    //              : '',
    //            avgDaily: C3.avgDailyProduction ? C3.avgDailyProduction : ''
    //          }
    //        ],
    //        targetSchedule: math.add(
    //          math.multiply(C1.targetSchedule, C1.targetProduction) / 100,
    //          math.multiply(C2.targetSchedule, C2.targetProduction) / 100,
    //          math.multiply(C3.targetSchedule, C3.targetProduction) / 100
    //        ),
    //        output: math.add(
    //          C1.cumulativeOutput,
    //          C2.cumulativeOutput,
    //          C3.cumulativeOutput
    //        )
    //      })

    //      //   this.setTopData(
    //      //     '综判总产量',
    //      //     math
    //      //       .add(C1.cumulativeOutput, C2.cumulativeOutput, C3.cumulativeOutput)
    //      //       .toFixed(0)
    //      //   )
    //      // 计算进度
    //      this.rollMonth.percent = math.multiply(
    //        math
    //          .divide(
    //            this.rollMonth.targetSchedule,
    //            math.add(
    //              C1.targetProduction,
    //              C2.targetProduction,
    //              C3.targetProduction
    //            )
    //          )
    //          .toFixed(4),
    //        100
    //      )
    //    } catch (e) {}
    //  }
  }
}
</script>
 
 <style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 20px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .slick {
    height: 65px;
    position: relative;
    //top: -15px;
    margin-bottom: 15px;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    width: 90px;
    line-height: 24px;
    font-size: 16px;
    white-space: nowrap;
    color: #ffffff;
  }
  span:last-child {
    flex: 1;
    overflow: auto;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    font-size: 0;
    margin-right: 10px;
    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.green {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.scroll-wrapper {
  height: 100%;
}
.kpi-list {
  font-size: 0;
  .item {
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12.5%;
    img {
      width: 48px;
      margin-right: 10px;
    }
    .name {
      font-size: 18px;
      font-weight: 700;
      line-height: 18px;
      margin-bottom: 10px;
      letter-spacing: 0px;
      text-align: left;
    }
    .num {
      font-size: 32px;
      font-weight: 700;
      line-height: 32px;
      letter-spacing: 0px;
      text-align: left;
    }
    .unit {
      font-size: 20px;
      font-weight: 350;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
    }
  }
}
</style>
