<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <screen-border title="热处理检验批次一次合格率">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/morningMeeting/edit'"
                class="screen-btn"
                @click="HeatRate.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div
              ref="table3"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="HeatRate.showGridData"
                :row-class-name="tableRowClassName"
                :summary-method="getSummaries"
                :max-height="HeatRate.maxHeight"
                show-summary
                border>
                <el-table-column
                  align="center"
                  property="inspectionDate"
                  label="检验日期"
                  width="80"/>
                <el-table-column
                  align="center"
                  property="department"
                  label="科室"/>
                <el-table-column
                  align="center"
                  property="stdspec"
                  label="标准号"/>
                <el-table-column
                  align="center"
                  property="nonconformingLot"
                  label="不合格批次"
                  width="90"/>
                <el-table-column
                  align="center"
                  property="inspectionLot"
                  label="检验批次"
                  width="80"/>
                <el-table-column
                  align="center"
                  property="FPY"
                  label="一次合格率(%)"
                  width="120"/>
                <el-table-column
                  align="center"
                  property="Description"
                  label="情况说明"/>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border title="主要缺陷降级">
                <template v-slot:headerRight>
                  <span
                    v-command="'/screen/morningMeeting/edit'"
                    class="screen-btn"
                    @click="unPlaned.dialogVisible = true">
                    <el-icon class="el-icon-edit-outline"/>
                    操作
                  </span>
                </template>
                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="loading"
                    :data="unPlaned.showGridData"
                    :span-method="arraySpanMethod"
                    :max-height="unPlaned.maxHeight"
                    border>
                    <el-table-column
                      align="center"
                      prop="factory"
                      label="工厂"
                      width="80"/>
                    <el-table-column
                      align="center"
                      property="reason"
                      show-overflow-tooltip
                      label="降级原因"/>
                    <el-table-column
                      align="center"
                      property="pieceNum"
                      label="块数"
                      width="80"/>
                    <el-table-column
                      align="center"
                      property="weight"
                      label="重量"
                      width="65"/>
                    <el-table-column
                      align="center"
                      property="scale"
                      label="比例(%)"
                      width="80"/>
                  </el-table>
                </div>
              </screen-border>
            </div>
            <div class="content-hold"/>
            <div class="content-item">
              <screen-border title="昨日钢板降级分析">
                <template v-slot:headerRight>
                  <span
                    v-command="'/screen/morningMeeting/edit'"
                    class="screen-btn"
                    @click="unPlanedTotal.dialogVisible = true">
                    <el-icon class="el-icon-edit-outline"/>
                    操作
                  </span>
                </template>
                <div
                  ref="table2"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="loading"
                    :data="unPlanedTotal.showGridData"
                    :row-class-name="unPlanedTotalClass"
                    :max-height="unPlanedTotal.maxHeight"
                    border>
                    <el-table-column
                      align="center"
                      prop="rollingPlant"
                      label="工厂"
                      width="100"/>
                    <el-table-column
                      align="center"
                      prop="classify"
                      label="分类"
                      width="90"/>
                    <el-table-column
                      align="center"
                      property="blockNum"
                      label="块数"
                      width="90"/>
                    <el-table-column
                      align="center"
                      property="weight"
                      label="重量"/>
                    <el-table-column
                      align="center"
                      property="scale"
                      label="比例（%）"/>
                    <el-table-column
                      align="center"
                      property="plan"
                      label="计划"/>
                    <el-table-column
                      align="center"
                      property="complete"
                      label="是否完成"/>
                    <el-table-column
                      align="center"
                      property="remarks"
                      label="备注"/>
                  </el-table>
                </div>
              </screen-border>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--热处理详情-->
    <el-dialog
      :visible.sync="HeatRate.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="热处理检验批次一次合格率详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('HeatRate')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importHeatRateData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportHeatRate">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveHeatRate">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          热处理检验性能一次合格率详情
        </div>
      </template>
      <el-form :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="HeatRate.gridData"
          border>
          <el-table-column
            property="inspectionDate"
            label="检验日期">
            <template v-slot="{ row }">
              <el-input v-model="row.inspectionDate" />
            </template>
          </el-table-column>
          <el-table-column
            property="department"
            label="部门">
            <template v-slot="{ row }">
              <el-input v-model="row.department" />
            </template>
          </el-table-column>
          <el-table-column
            property="stdspec"
            label="标准号">
            <template v-slot="{ row }">
              <el-input v-model="row.stdspec" />
            </template>
          </el-table-column>
          <el-table-column
            property="nonconformingLot"
            label="不合格批次">
            <template v-slot="{ row }">
              <el-input v-model="row.nonconformingLot" />
            </template>
          </el-table-column>
          <el-table-column
            property="inspectionLot"
            label="检验批次">
            <template v-slot="{ row }">
              <el-input v-model="row.inspectionLot" />
            </template>
          </el-table-column>
          <el-table-column
            property="FPY"
            label="一次合格率(%)">
            <template v-slot="{ row }">
              <el-input v-model="row.FPY" />
            </template>
          </el-table-column>
          <el-table-column
            property="Description"
            label="情况说明">
            <template v-slot="{ row }">
              <el-input v-model="row.Description" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index, 'HeatRate')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEdit"
          class="screen-btn"
          @click="addGridData('HeatRate')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--非计划改判-->
    <el-dialog
      :visible.sync="unPlaned.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="非计划改判">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="SyncData(correctionMajorTask)">
              手动同步数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importUnPlanedData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handleUnPlanedPreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon 
                    clas 
                    s="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportUnplan">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveUnPlaned">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          主要缺陷改判详情
        </div>
      </template>
      <el-form
        v-loading="syncLoading"
        :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="unPlaned.gridData"
          :span-method="arraySpanMethod"
          border>
          <el-table-column
            align="center"
            prop="factory"
            label="工厂">
            <template v-slot="{ row }">
              <el-input v-model="row.factory" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="reason"
            label="降级原因">
            <template v-slot="{ row }">
              <el-input v-model="row.reason" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="pieceNum"
            label="块数">
            <template v-slot="{ row }">
              <el-input v-model="row.pieceNum" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="weight"
            label="重量">
            <template v-slot="{ row }">
              <el-input v-model="row.weight" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="scale"
            label="比例（%）">
            <template v-slot="{ row }">
              <el-input v-model="row.scale" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <!--非计划汇总-->
    <el-dialog
      :visible.sync="unPlanedTotal.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="SyncData(correctionGatherTask)">
              手动同步数据
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('unPlanedTotal')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importUnPlanedTotalData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handleUnPlanedTotalPreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportUnplanedTotal">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveUnPlanedTotal">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          昨日钢板降级分析
        </div>
      </template>
      <el-form
        v-loading="syncLoading"
        :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="unPlanedTotal.gridData"
          border>
          <el-table-column
            align="center"
            prop="rollingPlant"
            label="轧制工厂">
            <template v-slot="{ row }">
              <el-input v-model="row.rollingPlant" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="classify"
            label="分类">
            <template v-slot="{ row }">
              <el-input v-model="row.classify" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="blockNum"
            label="块数">
            <template v-slot="{ row }">
              <el-input v-model="row.blockNum" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="weight"
            label="重量">
            <template v-slot="{ row }">
              <el-input v-model="row.weight" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="scale"
            label="比例（%）">
            <template v-slot="{ row }">
              <el-input v-model="row.scale" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="plan"
            label="计划">
            <template v-slot="{ row }">
              <el-input v-model="row.plan" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="complete"
            label="是否完成">
            <template v-slot="{ row }">
              <el-select
                :popper-append-to-body="false"
                v-model="row.complete">
                <el-option
                  v-for="(item, index) in finishList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="remarks"
            label="备注">
            <template v-slot="{ row }">
              <el-input v-model="row.remarks" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index, 'unPlanedTotal')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEdit"
          class="screen-btn"
          @click="addGridData('unPlanedTotal')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="上月导入日期库存">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { batchUpdateResource } from '@/api/system'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import {
  correctionGatherTask,
  correctionMajorTask,
  findCorrectionGatherByDate,
  findCorrectionMajorByDate,
  findHeatTreatmentYieldByDate,
  findHtFpylestPerformanceByDate,
  findKeyTrackingItemsByDate,
  findOrderCorrectionByDate,
  resourceConfirmTask,
  saveCorrectionGather,
  saveCorrectionMajor,
  savehtFpylestPerformance,
  saveKeyTrackingItems,
  saveOrderCorrection,
  saveSteelOutput
} from '@/api/screen'
import { math } from '@/lib/Math'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
export default {
  name: 'Kpi',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      correctionGatherTask: correctionGatherTask,
      correctionMajorTask: correctionMajorTask,
      HeatRate: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      unPlaned: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      unPlanedTotal: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      unitList: ['吨数', '炉数'],
      finishList: ['是', '否']
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.loadData()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    loadData() {
      this.getHeatRate()
      this.getUnPlaned()
      this.getUnPlanedTotal()
    },
    calculateHeight() {
      this.unPlaned.maxHeight = this.$refs.table1.offsetHeight
      this.unPlanedTotal.maxHeight = this.$refs.table2.offsetHeight
      this.HeatRate.maxHeight = this.$refs.table3.offsetHeight
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        if (![3, 4, 5].includes(index)) return (sums[index] = '')
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          sums[index] = ''
        }
      })
      if (sums[4]) {
        sums[5] = (100 - math.divide(sums[3], sums[4]) * 100).toFixed(2)
      }
      return sums
    },
    tableRowClassName() {
      return ''
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          inspectionDate: 'A',
          department: 'B',
          stdspec: 'C',
          nonconformingLot: 'D',
          inspectionLot: 'E',
          FPY: 'F',
          Description: 'G'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.HeatRate.gridData = sheet
          .filter(
            item => item.inspectionDate && item.inspectionDate !== '检验日期'
          )
          .map(item => {
            item.stdspec = item.stdspec.trim()
            item.FPY = item.FPY.toString()
            if (item.FPY.includes('%')) {
              item.FPY = item.FPY.replace('%', '')
            }
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportHeatRate() {
      const data = [
        {
          inspectionDate: '检验日期',
          department: '科室',
          stdspec: '标准号',
          nonconformingLot: '不合格批次',
          inspectionLot: '检验批次',
          FPY: '一次合格率',
          Description: '情况说明'
        }
      ].concat(
        _.cloneDeep(this.HeatRate.gridData).map(item => {
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `热处理检验批次一次合格率（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    getHeatRate() {
      post(findHtFpylestPerformanceByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.HeatRate.showGridData = res.data.map(item => {
          return {
            inspectionDate: item.inspectiondate,
            department: item.department,
            stdspec: item.stdspec,
            nonconformingLot: item.nonconforminglot,
            inspectionLot: item.inspectionlot,
            FPY: item.fpy,
            Description: item.description
          }
        })
        this.HeatRate.gridData = lodash.cloneDeep(this.HeatRate.showGridData)
      })
    },
    saveHeatRate() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.HeatRate.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(savehtFpylestPerformance, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.HeatRate.dialogVisible = false
          this.getHeatRate()
        }
      })
    },
    handleUnPlanedPreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          factory: 'A',
          reason: 'B',
          pieceNum: 'C',
          weight: 'D',
          scale: 'E'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unPlaned.gridData = sheet.map(item => {
          item.weight = Number(item.weight).toFixed(1)
          item.scale = item.scale.toString()
          if (item.scale.includes('%')) {
            item.scale = item.scale.replace('%', '')
          } else {
            item.scale = Number(
              math.multiply(Number(item.scale), 100).toFixed(2)
            )
          }
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    exportUnplan() {
      const data = [
        {
          factory: '工厂',
          reason: '降级原因',
          pieceNum: '块数',
          weight: '重量',
          scale: '比例'
        }
      ].concat(
        _.cloneDeep(this.unPlaned.gridData).map(item => {
          item.scale = item.scale ? item.scale + '%' : ''
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `改判（${this.cDate}晨会）.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },
    getUnPlaned() {
      post(findCorrectionMajorByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        if (res.data.length) {
          this.unPlaned.showGridData = res.data.map(item => {
            return {
              factory: item.factory,
              reason: item.reason,
              pieceNum: item.piecenum,
              weight: Number(item.weight).toFixed(1),
              scale: item.scale
            }
          })
        } else {
          this.unPlaned.showGridData = [
            'B1',
            'B1',
            'C1-平',
            'C1-平',
            'C1-卷',
            'C1-卷',
            'C2',
            'C2',
            'C3',
            'C3'
          ].map(item => {
            return {
              factory: item,
              reason: '',
              pieceNum: '',
              weight: '',
              scale: ''
            }
          })
        }
        this.unPlaned.gridData = lodash.cloneDeep(this.unPlaned.showGridData)
      })
    },
    saveUnPlaned() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.unPlaned.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(saveCorrectionMajor, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.unPlaned.dialogVisible = false
          this.getUnPlaned()
        }
      })
    },
    handleUnPlanedTotalPreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          rollingPlant: 'A',
          classify: 'B',
          blockNum: 'C',
          weight: 'D',
          scale: 'E',
          plan: 'F',
          complete: 'G',
          remarks: 'H'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unPlanedTotal.gridData = sheet.map(item => {
          item.scale = item.scale.toString()
          if (item.scale.includes('%')) {
            item.scale = item.scale.replace('%', '')
          } else {
            item.scale = Number(
              math.multiply(Number(item.scale), 100).toFixed(2)
            )
          }
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    exportUnplanedTotal() {
      const data = [
        {
          rollingPlant: '轧制工厂',
          classify: '分类',
          blockNum: '块数',
          weight: '重量',
          scale: '比例',
          plan: '计划',
          complete: '是否完成',
          remarks: '备注'
        }
      ].concat(
        _.cloneDeep(this.unPlanedTotal.gridData).map(item => {
          item.scale = item.scale + '%'
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `主要缺陷改判（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    getUnPlanedTotal() {
      post(findCorrectionGatherByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.unPlanedTotal.showGridData = res.data.map(item => {
          return {
            rollingPlant: item.rollingplant,
            classify: item.classify,
            blockNum: item.blocknum,
            weight: item.weight,
            scale: Number(item.scale).toFixed(2),
            plan: item.plan,
            complete: item.complete,
            remarks: item.remarks
          }
        })
        this.unPlanedTotal.gridData = lodash.cloneDeep(
          this.unPlanedTotal.showGridData
        )
      })
    },
    saveUnPlanedTotal() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.unPlanedTotal.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(saveCorrectionGather, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.unPlanedTotal.dialogVisible = false
          this.getUnPlanedTotal()
        }
      })
    },
    unPlanedTotalClass(row) {
      console.log(row)
      let str = ''
      if (row.row.rollingPlant && row.row.rollingPlant.indexOf('汇总') !== -1) {
        str += ' table-total'
      }
      if (row.row.complete && row.row.complete == '否') {
        str += ' red-color'
      }
      console.log(str)
      return str
    },
    importHeatRateData(date) {
      post(findHtFpylestPerformanceByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.HeatRate.gridData = res.data.map(item => {
          return {
            inspectionDate: item.inspectiondate,
            department: item.department,
            stdspec: item.stdspec,
            nonconformingLot: item.nonconforminglot,
            inspectionLot: item.inspectionlot,
            FPY: item.fpy,
            Description: item.description
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
      if (row.factory === '备注') {
        if (columnIndex === 1) {
          return [1, 9]
        } else if (columnIndex > 1 && columnIndex < 10) {
          return [0, 0]
        }
      }
    },
    importUnPlanedData(date) {
      post(findOrderCorrectionByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        if (res.data.length) {
          this.unPlaned.gridData = res.data.map(item => {
            return {
              factory: item.factory,
              reason: item.reason,
              pieceNum: item.piecenum,
              weight: Number(item.weight).toFixed(1),
              scale: item.scale
            }
          })
        } else {
          this.unPlaned.gridData = [
            'B1',
            'B1',
            'C1-平',
            'C1-平',
            'C1-卷',
            'C1-卷',
            'C2',
            'C2',
            'C3',
            'C3'
          ].map(item => {
            return {
              factory: item,
              reason: '',
              pieceNum: '',
              weight: '',
              scale: ''
            }
          })
        }
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    importUnPlanedTotalData(date) {
      post(findCorrectionGatherByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.unPlanedTotal.gridData = res.data.map(item => {
          return {
            rollingPlant: item.rollingplant,
            classify: item.classify,
            blockNum: item.blocknum,
            weight: item.weight,
            scale: item.scale,
            remarks: item.remarks
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
/deep/ .red-color {
  color: #ff2855;
}
</style>
