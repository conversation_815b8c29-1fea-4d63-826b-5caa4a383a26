<template>
  <div 
    :id="containerId" 
    :style="{ height: '100%' }"/>
</template>

<script>
export default {
  name: 'bars-chart',
  props: {
    height: {
      type: Number,
      default: 180
    },
    unit: {
      type: String,
      default: ''
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 0
    },
    yAxisMax: {
      type: Number,
      default: 0
    },
    hideAxisName: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
    window.addEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      const container = document.querySelector('#' + this.containerId)
      if (container) {
        container.style.height = `${this.height}px`
      }

      if (this.myChart) {
        this.myChart.dispose()
      }

      this.myChart = this.$echarts.init(container)
      this.myChart.on('selectchanged', params => {
        this.$emit('selected', params)
      })

      const yMax =
        this.yAxisMax ||
        Math.max(
          ...this.chartData.flatMap(series =>
            series.data.map(
              item => (typeof item === 'object' ? item.value : item)
            )
          )
        ) * 1.2

      // 找到合计值并发出事件
      let totalValue = null
      if (this.chartData && this.chartData[0] && this.chartData[0].data) {
        const totalData = this.chartData[0].data.find(d => d.name === '合计')
        if (totalData && totalData.value !== undefined) {
          totalValue = totalData.value
          this.$emit('barDataUpdated', totalValue)
        }
      }

      const options = {
        tooltip: {
          show: this.showToolbox,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 10
          },
          formatter: function(params) {
            // 获取系列名称（即卡片标题如"电单耗"）
            const seriesName = params[0].seriesName

            return (
              `<div style="color: #1fc6ff; font-weight: bold; margin-bottom: 5px;">${seriesName}</div>` +
              params
                .map(
                  item =>
                    `<div style="display: flex; justify-content: space-between; margin: 5px 0;">
                      <span style="margin-right: 10px;">
                        ${item.axisValue}
                      </span>
                      <span style="font-weight: bold; color: #1fc6ff;">
                        ${
                          item.value === null || item.value === undefined
                            ? '-'
                            : item.value
                        }
                      </span>
                    </div>`
                )
                .join('')
            )
          }
        },
        color: this.color,
        legend: {
          show: this.showLegend,
          align: 'left',
          top: 5,
          right: 2,
          padding: 0,
          icon: 'circle',
          textStyle: {
            color: '#C9E1FDF2',
            fontSize: 10
          },
          itemHeight: 10,
          itemWidth: 10,
          itemGap: 10,
          itemStyle: {
            borderWidth: 0,
            padding: 0
          }
        },
        grid: {
          top: this.showLegend ? '15%' : '18%',
          left: '2%',
          right: '2%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: this.xData,
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              interval: 0,
              rotate: this.labelRotate || 0,
              formatter: function(value) {
                return value.replace(/(.{4})/g, '$1\n')
              }
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            }
          }
        ],
        yAxis: [
          {
            name: this.hideAxisName ? '' : this.unit,
            type: 'value',
            minInterval: 1,
            max: this.yAxisMax || 100,
            nameTextStyle: {
              color: '#fff'
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'right'
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        series: this.chartData.map(item => {
          return {
            name: item.name,
            type: 'bar',
            barGap: 0,
            barWidth: this.barWidth || 60 / this.chartData.length + '%',
            barMaxWidth: this.barWidth || 12,
            showBackground: this.barBackground,
            backgroundStyle: {
              color: 'rgba(31, 198, 255, 0.1)',
              borderColor: 'rgba(31, 198, 255, 0.3)',
              borderWidth: 1,
              borderRadius: 1
            },
            markPoint: {
              symbolSize: 5
            },
            label: {
              show: this.showLabel,
              color: '#fff',
              position: 'top',
              fontSize: 12,
              offset: [0, 2],
              formatter: function(params) {
                if (params.value === null || params.value === undefined) {
                  return '-'
                }
                // 对于合计值，保留3位小数
                if (params.name === '合计') {
                  return params.value.toFixed(3)
                }
                // 其他值保留2位小数
                return params.value.toFixed(2)
              }
            },
            data: item.data.map(d => ({
              ...d,
              value:
                d.value === null || d.value === undefined
                  ? null
                  : Number(d.value)
            })),
            itemStyle: {
              color: '#2afa97',
              borderColor: 'rgba(31, 198, 255, 0.6)',
              borderWidth: 1,
              borderRadius: 1
            }
          }
        })
      }
      this.myChart.setOption(options, true)

      setTimeout(() => {
        if (this.myChart) {
          this.myChart.resize()
        }
      }, 50)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
