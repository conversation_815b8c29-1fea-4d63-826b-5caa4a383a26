<template>
  <div>
    <div class="page-content">
      <el-row
        :gutter="30"
        class="row-bg"
        justify="start"
        type="flex"
      >
        <el-col
          :span="24"
        >
          <div class="page-operate">

            <div class="search-wrapper">
              <!-- <el-form
              ref="searchForm"
              :label-width="'80px'"
              :model="searchForm"
              size="mini"
              inline
              @keyup.enter.native="handleSearch(true)"
            >
              <el-form-item
                label="目录映射"
                prop="describe"
              >
                <el-input
                  v-model="searchForm.describe"
                  clearable
                  size="small"
                  placeholder="请输入指标描述"
                  style="width: 200px"
                  type="text"
                />
              </el-form-item>
              <el-form-item
                class="br"
              >
                <el-button
                  icon="el-icon-search"
                  type="primary"
                  size="small"
                  @click="handleSearch"
                >搜索
                </el-button>
              </el-form-item>
            </el-form> -->
            </div>
            <div>
              <el-button
                v-command="'/kpi/indicators/add'"
                icon="el-icon-circle-plus-outline"
                size="small"
                type="success"
                @click="handleAdd"
              >新增
              </el-button>
            </div>
          </div>
          <div class="page-card">
            <el-table
              v-loading="loading"
              :data="tableData"
              :size="size"
              border
              style="width: 100%"
            >
              <el-table-column
                label="序号"
                type="index"
                width="60"
              />
              <!-- <el-table-column
                label="目录ID编号"
                prop="id"
                min-width="120"
              /> -->
              <el-table-column
                label="映射名称"
                prop="directoryName"
              />
              <el-table-column
                label="报表页面ID编号"
                prop="themeDirectoryId"
                min-width="100"
              />
              <el-table-column
                label="wyn目录ID编号"
                prop="wynDirectoryId"
                min-width="100"
              />
              <el-table-column
                fixed="right"
                label="操作"
                width="120"
              >
                <template v-slot="{row}">
                  <el-button
                    v-command="'/kpi/indicators/delete'"
                    slot="reference"
                    type="text"
                    @click="handleDelete(row)"
                  >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-row
              align="middle"
              class="table-pagination"
              justify="end"
              type="flex"
            >
              <el-pagination
                :current-page="page.page"
                :page-size="page.size"
                :page-sizes="[10, 20, 30, 40]"
                :total="page.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </el-row>
          </div>
        </el-col>
      </el-row>
    </div>
    <Add
      ref="modalForm"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import Add from './component/add'
import listMixins from '@/mixins/ListMixins'
import { findListAll, saveItem, deleteItem } from '@/api/setting'

export default {
  name: 'User',
  components: {
    Add
  },
  mixins: [listMixins],
  data: () => {
    return {
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: findListAll, //分页接口地址
        add: saveItem, //添加接口地址
        delete: deleteItem //删除接口地址
      },
      rightMenuVisible: false,
      rightMenuLeft: 0,
      rightMenuTop: 0,
      rightMenuData: null
    }
  },
  watch: {
    rightMenuVisible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  created() {},
  methods: {
    getValue: function(list = [], value) {
      return list.find(item => item.value == value)
    },
    async handleSearch(reset = false) {
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.page = 1
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          page: this.page.page,
          size: this.page.size
        })
      )
      // console.log(data)
      this.tableData = data.results ? data.results : []
      // this.page.page = data.results.pageable.pageNumber + 1
      // this.page.size = data.results.pageable.pageSize
      this.page.total = data.totalElements
      this.afterHandleSearch(this.tableData)
      this.loading = false
    },
    handleDelete: function(data) {
      if (!this.url.delete) {
        this.$message('请设置url.delete属性!')
        return
      }
      this.$confirm('是否确认删除此数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除操作
        post(this.url.delete, data).then(res => {
          this.handleSearch()
        })
      })
    },
    async handleNodeClick(data) {
      this.closeMenu()
    },
    rightMod() {
      //
      this.handleEdit(this.rightMenuData.data)
    },
    rightDel() {
      //
      this.handleDelete(this.rightMenuData.data)
    },
    oncontextmenu(e, data, node) {
      //
      this.rightMenuTop = e.clientY
      this.rightMenuLeft = e.clientX
      this.rightMenuVisible = true
      this.rightMenuData = node
    },
    closeMenu(e) {
      this.rightMenuVisible = false
      this.rightMenuData = null
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.page-title {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  margin-bottom: 15px;
}

.page-content {
  font-size: 18px;
}

.page-operate {
  .operate-icon {
    margin-left: 8px;
  }
}

.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}
.tree-wrapper {
  height: 75vh;
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.first-node {
  font-size: 18px;
}
/deep/ .el-tree-node {
  margin: 5px 0;
}
/deep/ .el-tree > .el-tree-node {
  margin: 15px 0 12px;
}
.contextmenu {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  background: #fff;
  font-size: 14px;
  color: #666;
  border: 1px solid #e9e9e9;
  min-width: 250px;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
  li {
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #f4f4f5;
    }
  }
  li:last-child {
    border-top: 1px solid #e9e9e9;
  }
}
</style>
