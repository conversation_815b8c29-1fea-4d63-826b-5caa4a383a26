<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="6"
          class="full-height">
          <quality-indexs
            :select-date="cDate"
            @dateChange="changeDate"/>
        </el-col>
        <el-col
          :span="9"
          class="full-height">
          <screen-border title="炼钢现货非计划">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="showDiaigo"
              >
                <el-icon class="el-icon-edit-outline" />
                操作
              </span>
            </template>
            <div
              class="operate-box">
              <el-radio-group
                v-model="defectChartDateType"
                size="mini"
                class="screen-input">
                <el-radio-button :label="0">日</el-radio-button>
                <el-radio-button :label="1">月</el-radio-button>
              </el-radio-group>
            </div>
            <bars-chart
              :bar-width="30"
              :unit="'%'"
              :color="[
                '#2772f0',
                '#f5b544',
                '#51df81',
                '#edf173',
                '#e91e63',
                '#00bcd4',
                '#2196f3',
                '#3f51b5',
                '#9c27b0',
                '#009688',
              ]"
              :chart-data="spots.bar1"
              :x-data="spots.barX1"/>
          </screen-border>
        </el-col>
        <el-col
          :span="9"
          class="full-height">
          <screen-border title="炼钢原始非计划">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="()=>{
                  variety2.save = false;
                  variety2.dialogVisible = true
                  getvariety2()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明录入
              </span>
              <span
                class="screen-btn"
                @click="()=>{
                  variety2.save = true;
                  variety2.dialogVisible = true
                  getvariety2()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明
              </span>
            </template>
            <div
              class="operate-box">
              <el-radio-group
                v-model="defectChartDateType"
                size="mini"
                class="screen-input">
                <el-radio-button :label="0">日</el-radio-button>
                <el-radio-button :label="1">月</el-radio-button>
              </el-radio-group>
            </div>
            <bars-chart
              :bar-width="30"
              :unit="'%'"
              :color="[
                '#2772f0',
                '#f5b544',
                '#51df81',
                '#edf173',
                '#e91e63',
                '#00bcd4',
                '#2196f3',
                '#3f51b5',
                '#9c27b0',
                '#009688',
              ]"
              :chart-data="original.bar1"
              :x-data="original.barX1"/>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="8"
          class="full-height">
          <custom-table
            ref="table1"
            :title="'裂纹发生率'"
            :setting="flaw"
            :url-list="flawUrl.list"
            :url-save="flawUrl.save"
            :select-date="selectDate"
            :show-table="false"
            :steelmaking-show="true"
            @change="getPocessed">
            <template v-slot:topRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="()=>{
                  crackRemarks.save = false;
                  crackRemarks.dialogVisible = true
                  getCrackRemarks()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明录入
              </span>
              <span
                class="screen-btn"
                @click="()=>{
                  crackRemarks.save = true;
                  crackRemarks.dialogVisible = true
                  getCrackRemarks()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明
              </span>
            </template>
            <template v-slot:content>
              <div
                class="chart-wrapper">
                <div
                  class="chart"
                  style="padding-top: 12px">
                  <div
                    class="operate-box">
                    <el-radio-group
                      v-model="processed.dateType1"
                      size="mini"
                      class="screen-input"
                      @input="changeProcessed($event)">
                      <el-radio-button :label="0">日</el-radio-button>
                      <el-radio-button :label="1">月</el-radio-button>
                    </el-radio-group>
                  </div>
                  <single-bars-chart
                    :show-legend="false"
                    :chart-data="processed.bar1"
                    :x-data="processed.barX1"
                    :unit="'%'"
                    @selected="getIncidenceRate($event)"/>
                </div>
              </div>
            </template>
          </custom-table>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <screen-border
            :title="'裂纹改判率'">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="()=>{
                  crackRemarks2.save = false;
                  crackRemarks2.dialogVisible = true
                  getCrackRemarks2()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明录入
              </span>
              <span
                class="screen-btn"
                @click="()=>{
                  crackRemarks2.save = true;
                  crackRemarks2.dialogVisible = true
                  getCrackRemarks2()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明
              </span>
            </template>
            <div
              class="chart-wrapper">
              <div
                class="chart"
                style="padding-top: 12px">
                <div class="operate-box">
                  <el-radio-group
                    v-model="processed.dateType1"
                    size="mini"
                    class="screen-input"
                    @input="changeProcessed($event)">
                    <el-radio-button :label="0">日</el-radio-button>
                    <el-radio-button :label="1">月</el-radio-button>
                  </el-radio-group>
                </div>
                <single-bars-chart
                  :show-legend="false"
                  :chart-data="processed.bar11"
                  :x-data="processed.barX1"
                  :unit="'%'"
                  @selected="getCorrectionRate($event)"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <custom-table
            ref="table2"
            :title="'探伤合格率'"
            :setting="detection"
            :url-list="detectionUrl.list"
            :url-save="detectionUrl.save"
            :select-date="selectDate"
            :show-table="false"
            :steelmaking-show="true"
            @change="getPocessed1">
            <template v-slot:topRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="()=>{
                  inspectionRemarks.save = false;
                  inspectionRemarks.dialogVisible = true
                  getinspectionRemarks()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明录入
              </span>
              <span
                class="screen-btn"
                @click="()=>{
                  inspectionRemarks.save = true;
                  inspectionRemarks.dialogVisible = true
                  getinspectionRemarks()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明
              </span>
            </template>
            <template v-slot:content>
              <div class="chart-wrapper">
                <div
                  class="chart">
                  <div
                    class="operate-box">
                    <el-radio-group
                      v-model="processed.dateType2"
                      size="mini"
                      class="screen-input"
                      @input="changeProcessed1($event)">
                      <el-radio-button :label="0">日</el-radio-button>
                      <el-radio-button :label="1">月</el-radio-button>
                    </el-radio-group>
                  </div>
                  <single-bars-chart
                    :show-legend="false"
                    :chart-data="processed.bar2"
                    :unit="'%'"
                    :x-data="processed.barX2"
                    @selected="getDetectionDetailed($event)"/>
                </div>
              </div>
            </template>
          </custom-table>
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible.sync="crackRemarks.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="裂纹发生情况说明">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality&&!crackRemarks.save"
              class="screen-btn"
              @click="saveCrackRemarks">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          裂纹发生情况说明
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="crackRemarks.gridData"
          class="font-table"
          border>
          <el-table-column
            property="A_LIST"
            label="内容">
            <template
              v-slot="{ row }"
              v-if="!crackRemarks.save">
              <el-input
                v-model="row.A_LIST"
                :rows="8"
                type="textarea"/>
            </template>
            <template
              v-slot="{ row }"
              v-else>
              <div
                slot="content"
                v-html="formatText(row.A_LIST)"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            width="210px"
            label="图片说明">
            <template v-slot="{ row, $index }">
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.B_LIST"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item.id"
                    :id="item.id"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !crackRemarks.save"
                    @img-delete="handlePasteImgDeleteID($event, index, 'crackRemarks')"
                  />
                </li>
              </ul>
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.showPic == unfinished ? [] : row.showPic"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item"
                    :src="item"
                    :is-id="false"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !crackRemarks.save"
                    @img-delete="handlePasteImgDelete($event, index, 'crackRemarks')"
                  />
                </li>
              </ul>
              <el-upload
                v-if="!crackRemarks.save"
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file)=>{
                  handleChange(file, 'crackRemarks', $index)
                }"
                :show-file-list="false"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = $index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="crackRemarks2.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="裂纹改判情况说明">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality&&!crackRemarks2.save"
              class="screen-btn"
              @click="saveCrackRemarks2">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          裂纹改判情况说明
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="crackRemarks2.gridData"
          class="font-table"
          border>
          <el-table-column
            property="A_LIST"
            label="内容">
            <template
              v-slot="{ row }"
              v-if="!crackRemarks2.save">
              <el-input
                v-model="row.A_LIST"
                :rows="8"
                type="textarea"/>
            </template>
            <template
              v-slot="{ row }"
              v-else>
              <div
                slot="content"
                v-html="formatText(row.A_LIST)"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            width="210px"
            label="图片说明">
            <template v-slot="{ row, $index }">
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.B_LIST"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item.id"
                    :id="item.id"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !crackRemarks2.save"
                    @img-delete="handlePasteImgDeleteID($event, index, 'crackRemarks2')"
                  />
                </li>
              </ul>
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.showPic == unfinished ? [] : row.showPic"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item"
                    :src="item"
                    :is-id="false"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !crackRemarks2.save"
                    @img-delete="handlePasteImgDelete($event, index, 'crackRemarks2')"
                  />
                </li>
              </ul>
              <el-upload
                v-if="!crackRemarks2.save"
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file)=>{
                  handleChange(file, 'crackRemarks2', $index)
                }"
                :show-file-list="false"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = $index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="inspectionRemarks.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="探伤情况说明">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality&&!inspectionRemarks.save"
              class="screen-btn"
              @click="saveinspectionRemarks">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          探伤情况说明
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="inspectionRemarks.gridData"
          class="font-table"
          border>
          <el-table-column
            property="A_LIST"
            label="内容">
            <template
              v-slot="{ row }"
              v-if="!inspectionRemarks.save">
              <el-input
                v-model="row.A_LIST"
                :rows="8"
                type="textarea"/>
            </template>
            <template
              v-slot="{ row }"
              v-else>
              <div
                slot="content"
                v-html="formatText(row.A_LIST)"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            width="210px"
            label="图片说明">
            <template v-slot="{ row, $index }">
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.B_LIST"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item.id"
                    :id="item.id"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !inspectionRemarks.save"
                    @img-delete="handlePasteImgDeleteID($event, index, 'inspectionRemarks')"
                  />
                </li>
              </ul>
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.showPic == unfinished ? [] : row.showPic"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item"
                    :src="item"
                    :is-id="false"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !inspectionRemarks.save"
                    @img-delete="handlePasteImgDelete($event, index, 'inspectionRemarks')"
                  />
                </li>
              </ul>
              <el-upload
                v-if="!inspectionRemarks.save"
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file)=>{
                  handleChange(file, 'inspectionRemarks', $index)
                }"
                :show-file-list="false"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = $index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
    <!-- 率弹窗-->
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'1300px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="裂纹发生详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          裂纹发生详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList"
        class="center-table"
        border>
        <el-table-column
          property="产线"
          label="产线">
          <template v-slot="{ row }">
            {{ codePlt[row['产线']] }}
          </template>
        </el-table-column>
        <el-table-column
          property="钢板号"
          label="钢板号"/>
        <el-table-column
          property="板坯钢种"
          label="板坯钢种"/>
        <el-table-column
          property="轧制标准"
          label="轧制标准" />
        <el-table-column
          property="厚度"
          label="厚度"
          width="70"/>
        <el-table-column
          property="宽度"
          label="宽度"
          width="60"/>
        <el-table-column
          property="长度"
          label="长度"
          width="70"/>
        <el-table-column
          property="重量"
          label="重量"
          width="70"/>
        <el-table-column
          property="表面等级"
          label="表面等级"
          width="75"/>
        <el-table-column
          property="缺陷"
          label="缺陷"
          width="110"/>
        <el-table-column
          property="改判缺陷"
          label="改判缺陷"
          width="110"/>
        <el-table-column
          property="铸机号"
          label="铸机号"
          width="60"/>
        <el-table-column
          property="铸坯厚度"
          label="铸坯厚度"
          width="75"/>
        <el-table-column
          property="铸坯宽度"
          label="铸坯宽度"
          width="75"/>
        <el-table-column
          property="铸坯长度"
          label="铸坯长度"
          width="75"/>
        <el-table-column
          property="装炉温度"
          label="装炉温度"
          width="75"/>
      </el-table>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogVisible1"
      :width="'1300px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          裂纹改判详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList1"
        class="center-table"
        border>
        <el-table-column
          property="钢板号"
          label="钢板号"/>
        <el-table-column
          property="板坯钢种"
          label="板坯钢种"/>
        <el-table-column
          property="轧制标准"
          label="轧制标准" />
        <el-table-column
          property="厚度"
          label="厚度"
          width="70"/>
        <el-table-column
          property="宽度"
          label="宽度"
          width="70"/>
        <el-table-column
          property="长度"
          label="长度"
          width="70"/>
        <el-table-column
          property="重量"
          label="重量"
          width="70"/>
        <el-table-column
          property="表面等级"
          label="表面等级"
          width="80"/>
        <el-table-column
          property="缺陷"
          label="缺陷"
          width="110"/>
        <el-table-column
          property="改判缺陷"
          label="改判缺陷"
          width="110"/>
        <el-table-column
          property="铸机号"
          label="铸机号"
          width="70"/>
        <el-table-column
          property="铸坯厚度"
          label="铸坯厚度"
          width="80"/>
        <el-table-column
          property="铸坯宽度"
          label="铸坯宽度"
          width="80"/>
        <el-table-column
          property="铸坯长度"
          label="铸坯长度"
          width="80"/>
        <el-table-column
          property="装炉温度"
          label="装炉温度"
          width="80"/>
      </el-table>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogVisible2"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          探伤不合格详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList2"
        class="center-table"
        border>
        <el-table-column
          property="产线"
          label="产线"
          width="90">
          <template v-slot="{ row }">
            {{ codePlt[row['产线']] }}
          </template>
        </el-table-column>
        <el-table-column
          property="钢板号"
          label="钢板号"/>
        <el-table-column
          property="板坯钢种"
          label="板坯钢种"/>
        <el-table-column
          property="标准号"
          label="标准号"/>
        <el-table-column
          property="检查标准"
          label="检查标准"/>
        <el-table-column
          property="结论"
          label="结论"
          width="80">
          <template v-slot="{ row }">
            {{ resultList[row['结论']] }}
          </template>
        </el-table-column>
        <el-table-column
          property="改判原因"
          label="改判原因"
          width="80"/>
        <el-table-column
          property="厚度"
          label="厚度"
          width="80"/>
        <el-table-column
          property="宽度"
          label="宽度"
          width="80"/>
        <el-table-column
          property="长度"
          label="长度"
          width="80"/>
        <el-table-column
          property="重量"
          label="重量"
          width="80"/>
      </el-table>
    </el-dialog>
    <!-- 非计划 -->
    <el-dialog
      :visible.sync="unplanned.show"
      :width="'90%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="炼钢非计划"
    >
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"
            />
            <span
              v-loading="loading"
              v-if="canEditQuality"
              :v-loading="unplanned.loading"
              class="screen-btn"
              @click="saveUnplanned"
            >
              <el-icon class="el-icon-document-checked" />
              保存
            </span>
          </div>
          炼钢非计划
        </div>
      </template>
      <el-form 
        :disabled="!canEditQuality" 
        :v-loading="unplanned.loading">

        <el-row
          :style="{ height: 'calc(100vh - 355px)' }"
          :gutter="24"
          class="dialog-body"
        >
          <el-col :span="12">
            <div class="dialog-cell">
              <div class="dialog-cell-title">炼钢现货非计划</div>
            </div>
            <el-row
              :gutter="24"
              class="dialog-body"
            >
              <el-col :span="12">
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C1计划指标(日)</div>
                  <el-input
                    v-model="unplanned.editData[0].PLAN_TARGET_C1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C1实际指标(包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[0].REALITY_TARGET_C1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C1实际指标(不包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[0].REALITY_TARGETINCLUDE_C1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C2计划指标(日)</div>
                  <el-input
                    v-model="unplanned.editData[0].PLAN_TARGET_C2"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C2实际指标(包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[0].REALITY_TARGET_C2"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C2实际指标(不包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[0].REALITY_TARGETINCLUDE_C2"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C3计划指标(日)</div>
                  <el-input
                    v-model="unplanned.editData[0].PLAN_TARGET_C3"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C3实际指标(包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[0].REALITY_TARGET_C3"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C3实际指标(不包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[0].REALITY_TARGETINCLUDE_C3"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">综合计划指标(日)</div>
                  <el-input
                    v-model="unplanned.editData[0].PLAN_TARGET_B1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">综合实际指标(包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[0].REALITY_TARGET_B1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">综合实际指标(不包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[0].REALITY_TARGETINCLUDE_B1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
              </el-col>

              <el-col :span="12">
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C1计划指标(月)</div>
                  <el-input
                    v-model="unplanned.editData[1].PLAN_TARGET_C1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C1实际指标(包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[1].REALITY_TARGET_C1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C1实际指标(不包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[1].REALITY_TARGETINCLUDE_C1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C2计划指标(月)</div>
                  <el-input
                    v-model="unplanned.editData[1].PLAN_TARGET_C2"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C2实际指标(包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[1].REALITY_TARGET_C2"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C2实际指标(不包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[1].REALITY_TARGETINCLUDE_C2"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C3计划指标(月)</div>
                  <el-input
                    v-model="unplanned.editData[1].PLAN_TARGET_C3"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C3实际指标(包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[1].REALITY_TARGET_C3"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C3实际指标(不包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[1].REALITY_TARGETINCLUDE_C3"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">综合计划指标(月)</div>
                  <el-input
                    v-model="unplanned.editData[1].PLAN_TARGET_B1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">综合实际指标(包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[1].REALITY_TARGET_B1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">综合实际指标(不包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[1].REALITY_TARGETINCLUDE_B1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="12">
            <div class="dialog-cell">
              <div class="dialog-cell-title">炼钢原始非计划</div>
            </div>
            <el-row
              :gutter="24"
              class="dialog-body"
            >
              <el-col :span="12">
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C1计划指标(日)</div>
                  <el-input
                    v-model="unplanned.editData[2].PLAN_TARGET_C1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C1实际指标(包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[2].REALITY_TARGET_C1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C1实际指标(不包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[2].REALITY_TARGETINCLUDE_C1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C2计划指标(日)</div>
                  <el-input
                    v-model="unplanned.editData[2].PLAN_TARGET_C2"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C2实际指标(包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[2].REALITY_TARGET_C2"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C2实际指标(不包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[2].REALITY_TARGETINCLUDE_C2"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C3计划指标(日)</div>
                  <el-input
                    v-model="unplanned.editData[2].PLAN_TARGET_C3"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C3实际指标(包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[2].REALITY_TARGET_C3"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C3实际指标(不包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[2].REALITY_TARGETINCLUDE_C3"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">综合计划指标(日)</div>
                  <el-input
                    v-model="unplanned.editData[2].PLAN_TARGET_B1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">综合实际指标(包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[2].REALITY_TARGET_B1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">综合实际指标(不包含头尾坯)(日)</div>
                  <el-input
                    v-model="unplanned.editData[2].REALITY_TARGETINCLUDE_B1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
              </el-col>

              <el-col :span="12">
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C1计划指标(月)</div>
                  <el-input
                    v-model="unplanned.editData[3].PLAN_TARGET_C1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C1实际指标(包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[3].REALITY_TARGET_C1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C1实际指标(不包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[3].REALITY_TARGETINCLUDE_C1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C2计划指标(月)</div>
                  <el-input
                    v-model="unplanned.editData[3].PLAN_TARGET_C2"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C2实际指标(包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[3].REALITY_TARGET_C2"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C2实际指标(不包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[3].REALITY_TARGETINCLUDE_C2"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C3计划指标(月)</div>
                  <el-input
                    v-model="unplanned.editData[3].PLAN_TARGET_C3"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C3实际指标(包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[3].REALITY_TARGET_C3"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">C3实际指标(不包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[3].REALITY_TARGETINCLUDE_C3"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">综合计划指标(月)</div>
                  <el-input
                    v-model="unplanned.editData[3].PLAN_TARGET_B1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">综合实际指标(包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[3].REALITY_TARGET_B1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
                <div class="dialog-cell">
                  <div class="dialog-cell-title">综合实际指标(不包含头尾坯)(月)</div>
                  <el-input
                    v-model="unplanned.editData[3].REALITY_TARGETINCLUDE_B1"
                    :disabled="!canEditQuality"
                    type="number"
                  />
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <!-- 非计划情况说明 -->
    <el-dialog
      :visible.sync="variety2.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="炼钢非计划情况说明">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality&&!variety2.save"
              class="screen-btn"
              @click="savevariety2">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          炼钢非计划情况说明
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="variety2.gridData"
          class="font-table"
          border>
          <el-table-column
            property="A_LIST"
            label="内容">
            <template
              v-slot="{ row }"
              v-if="!variety2.save">
              <el-input
                v-model="row.A_LIST"
                :rows="4"
                type="textarea"/>
            </template>
            <template
              v-slot="{ row }"
              v-else>
              <div
                slot="content"
                v-html="formatText(row.A_LIST)"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            width="210px"
            label="图片说明">
            <template v-slot="{ row, $index }">
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.B_LIST"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item.id"
                    :id="item.id"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !variety2.save"
                    @img-delete="handlePasteImgDeleteID($event, index, 'variety2')"
                  />
                </li>
              </ul>
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.showPic == unfinished ? [] : row.showPic"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item"
                    :src="item"
                    :is-id="false"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !variety2.save"
                    @img-delete="handlePasteImgDelete($event, index, 'variety2')"
                  />
                </li>
              </ul>
              <el-upload
                v-if="!variety2.save"
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file)=>{
                  handleChange(file, 'variety2', $index)
                }"
                :show-file-list="false"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = $index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import lodash from 'lodash'
import * as _ from 'lodash'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import QualityIndexs from '../component/quality-index.vue'

import { post } from '@/lib/Util'
import {
  correctionRateDetailed,
  detectionDetailed,
  findInspectionPassRateByDate,
  firstMorningMeeting,
  incidenceRateDetailed,
  qmsQualityQueryPlateFlaw,
  qmsQualitySavePlateFlaw,
  saveInspectionPassRate
} from '@/api/screen'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart.vue'
import CustomTable from '@/pages/screen/qualityMeeting/component/custom-table.vue'
import ImgView from '@/components/ImgView.vue'
// 柱状图
import BarsChart from '@/pages/screen/firstSteelmakingPlantTest/component/bars-chart'
import { deleteFileByIds, uploadFile } from '@/api/system'

export default {
  name: 'qualityIndex',
  components: {
    ImgView,
    CustomTable,
    SingleBarsChart,
    ScreenBorder,
    BarsChart,
    QualityIndexs
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      tableHeight: null,
      editIndex: 0,
      spanArr2: {},
      flawList: [],
      tableList: [],
      dialogVisible: false,
      flawList1: [],
      tableList1: [],
      dialogVisible1: false,
      flawList2: [],
      tableList2: [],
      dialogVisible2: false,
      resultList: {
        Y: '合格',
        N: '不合格'
      },
      crackRemarks: {
        gridData: [],
        save: true,
        dialogVisible: false
      },
      crackRemarks2: {
        gridData: [],
        save: true,
        dialogVisible: false
      },
      inspectionRemarks: {
        gridData: [],
        save: true,
        dialogVisible: false
      },
      flawUrl: {
        save: qmsQualitySavePlateFlaw,
        list: qmsQualityQueryPlateFlaw
      },
      processed: {
        bar1: [],
        barX1: [],
        barLoading: false,
        dateType1: 0,
        failReason1: '',
        bar11: [],
        dateType11: 0,
        failReason11: '',
        bar2: [],
        barX2: [],
        dateType2: 0,
        failReason2: ''
      },
      flaw: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'incidencetargetvalue',
          keySave: 'incidenceTargetValue',
          label: '发生率目标值'
        },
        {
          keyQuery: 'incidencevalue',
          keySave: 'incidenceValue',
          label: '发生率实际值'
        },
        {
          keyQuery: 'reasonnotcompl',
          keySave: 'reasonNotCompl',
          label: '发生率未完成原因'
        },
        {
          keyQuery: 'changetargetvalue',
          keySave: 'changeTargetValue',
          label: '改判率目标值'
        },
        {
          keyQuery: 'changevalue',
          keySave: 'changeValue',
          label: '改判率实际值'
        },
        {
          keyQuery: 'changenotcompl',
          keySave: 'ChangeNotCompl',
          label: '改判率未完成原因'
        },
        {
          keyQuery: 'lwmonthdata',
          keySave: 'lwMonthData',
          label: '裂纹月数据',
          show: false
        },
        {
          keyQuery: 'gpmonthdata',
          keySave: 'gpMonthData',
          label: '改判月数据',
          show: false
        }
      ],
      detectionUrl: {
        save: saveInspectionPassRate,
        list: findInspectionPassRateByDate
      },
      detection: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'targetvalue',
          keySave: 'targetValue',
          label: '目标值'
        },
        {
          keyQuery: 'value',
          keySave: 'Value',
          label: '实际值'
        },
        {
          keyQuery: 'reasonnotcompl',
          keySave: 'reasonNotCompl',
          label: '未完成原因'
        },
        {
          keyQuery: 'monthdata',
          keySave: 'monthData',
          label: '月数据',
          show: false
        }
      ],
      // 切换radio
      defectChartDateType: 0,
      // 非计划原始数据
      unplanned: {
        data: [{}, {}, {}, {}],
        editData: [{}, {}, {}, {}],
        show: false,
        loading: false
      },
      // 炼钢现货非计划
      spots: {
        bar1: [
          {
            name: '计划指标',
            data: [],
            barGap: '0.5'
          },
          {
            name: '实际指标(包含头尾坯)',
            data: [],
            barGap: '0.5'
          },
          {
            name: '实际指标(不包含头尾坯)',
            data: [],
            barGap: '0.5'
          }
        ],
        barX1: ['C1', 'C2', 'C3', '综合']
      },
      // 炼钢原始非计划
      original: {
        bar1: [
          {
            name: '计划指标',
            data: [],
            barGap: '0.5'
          },
          {
            name: '实际指标(包含头尾坯)',
            data: [],
            barGap: '0.5'
          },
          {
            name: '实际指标(不包含头尾坯)',
            data: [],
            barGap: '0.5'
          }
        ],
        barX1: ['C1', 'C2', 'C3', '综合']
      },
      // 原始现货数据
      barData: {
        spots: {
          monthData: { 计划: [], 实际包: [], 实际不包: [] },
          dayData: { 计划: [], 实际包: [], 实际不包: [] }
        },
        original: {
          monthData: { 计划: [], 实际包: [], 实际不包: [] },
          dayData: { 计划: [], 实际包: [], 实际不包: [] }
        }
      },
      variety2: {
        gridData: [],
        save: true,
        dialogVisible: false
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getCrackRemarks()
      this.getCrackRemarks2()
      this.getinspectionRemarks()
      this.getUnplanned()
    },
    defectChartDateType: function() {
      if (this.defectChartDateType == 0) {
        this.watchdefectChart(
          this.barData.spots.dayData,
          this.barData.original.dayData
        )
      } else {
        this.watchdefectChart(
          this.barData.spots.monthData,
          this.barData.original.monthData
        )
      }
    },
    barData: function() {
      if (this.defectChartDateType == 0) {
        this.watchdefectChart(
          this.barData.spots.dayData,
          this.barData.original.dayData
        )
      } else {
        this.watchdefectChart(
          this.barData.spots.monthData,
          this.barData.original.monthData
        )
      }
    }
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['PROD_PROCESSES']
    // this.spanArr['PROD_PROCESSES'] = [2, 0, 1]
  },
  mounted() {
    // this.tableHeight = this.$refs.tableHeight.offsetHeight
  },
  methods: {
    // 探伤合格率详情
    getDetectionDetailed(data) {
      const plt = this.pltCode[
        this.processed.barX2[data.fromActionPayload.dataIndexInside]
      ]
      if (!plt) return
      post(detectionDetailed, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        plt
      }).then(res => {
        this.flawList2 = res.data
        this.dialogVisible2 = true
      })
    },
    // 裂纹发生率详情
    getIncidenceRate(data) {
      const plt = this.pltCode[
        this.processed.barX1[data.fromActionPayload.dataIndexInside]
      ]
      if (!plt) return
      post(incidenceRateDetailed, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        plt
      }).then(res => {
        this.flawList = res.data
        this.dialogVisible = true
      })
    },
    // 探伤获取数据
    getPocessed1(data) {
      this.tableList2 = data
      Object.assign(this.processed, {
        bar2: data.map(item => {
          return {
            value: Number(item['Value']),
            plan: Number(item.targetValue),
            finished: Number(item['Value']) >= Number(item.targetValue)
          }
        }),
        barX2: data.map(item => item.rollingMill)
      })
      this.processed.failReason2 = data
        .filter(item => item.reasonNotCompl)
        .map(item => item.rollingMill + '：' + item.reasonNotCompl)
        .join('；')
    },
    changeProcessed($event) {
      const data = this.tableList
      console.log(this.tableList)
      Object.assign(this.processed, {
        bar1: data.map(item => {
          const value =
            $event === 0
              ? Number(item.incidenceValue)
              : Number(item.lwMonthData || 0)
          return {
            value: value,
            plan: Number(item.incidenceTargetValue),
            finished: value < Number(item.incidenceTargetValue)
          }
        }),
        bar11: data.map(item => {
          const value =
            $event === 0
              ? Number(item.changeValue)
              : Number(item.gpMonthData || 0)
          return {
            value: value,
            plan: Number(item.changeTargetValue),
            finished: value < Number(item.changeTargetValue)
          }
        }),
        barX1: data.map(item => item.rollingMill)
      })
      this.processed.failReason1 =
        $event === 0
          ? data
              .filter(item => item.reasonNotCompl)
              .map(item => item.rollingMill + '：' + item.reasonNotCompl)
              .join('；')
          : ''
      this.processed.failReason11 =
        $event === 0
          ? data
              .filter(item => item.ChangeNotCompl)
              .map(item => item.rollingMill + '：' + item.ChangeNotCompl)
              .join('；')
          : 0
    },
    // 裂纹获取数据
    getPocessed(data) {
      this.tableList = data
      this.changeProcessed(0)
    },
    // 裂纹发生率详情
    getCorrectionRate(data) {
      const plt = this.pltCode[
        this.processed.barX2[data.fromActionPayload.dataIndexInside]
      ]
      if (!plt) return
      post(correctionRateDetailed, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        plt
      }).then(res => {
        this.flawList1 = res.data
        this.dialogVisible1 = true
      })
    },
    changeProcessed1($event) {
      const data = this.tableList2
      Object.assign(this.processed, {
        bar2: data.map(item => {
          const value =
            $event === 0
              ? Number(item['Value'])
              : Number(item['monthData'] || 0)
          return {
            value: value,
            plan: Number(item.targetValue),
            finished: value >= Number(item.targetValue)
          }
        }),
        barX2: data.map(item => item.rollingMill)
      })
      this.processed.failReason2 =
        $event === 0
          ? data
              .filter(item => item.reasonNotCompl)
              .map(item => item.rollingMill + '：' + item.reasonNotCompl)
              .join('；')
          : ''
    },
    getCrackRemarks() {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '裂纹备注'
      }).then(res => {
        this.crackRemarks.gridData = _.cloneDeep(
          lodash.map(res.data.length ? res.data : [{}], item => {
            return {
              ...item,
              B_LIST: item.B_LIST ? JSON.parse(item.B_LIST) : []
            }
          })
        )
      })
    },
    async saveCrackRemarks() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '裂纹备注',
        data: _.map(
          _.sortBy(this.crackRemarks.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              FLAG: '裂纹备注',
              SORT_NUM: index + 1
            }
          }
        )
      }
      let del = null
      if (
        this.crackRemarks.gridData[0].delImage &&
        this.crackRemarks.gridData[0].delImage.length
      ) {
        del = await post(deleteFileByIds, {
          ids: this.crackRemarks.gridData[0].delImage
        })
      }
      if (del == null || del.success) {
        if (
          this.crackRemarks.gridData[0].file &&
          this.crackRemarks.gridData[0].file.length
        ) {
          const formData = new FormData()
          this.crackRemarks.gridData[0].file.forEach(item => {
            formData.append('files', item)
          })
          post(uploadFile, formData, false, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.success) {
              params.data[0].B_LIST = JSON.stringify([
                ...params.data[0].B_LIST,
                ...res.data
              ])
              post(firstMorningMeeting.furnaceCondition, params).then(res => {
                this.getCrackRemarks()
                this.$message.success('保存成功')
                this.loading = false
              })
            } else {
              this.$message.warning('图片上传失败！')
              this.loading = false
            }
          })
        } else {
          params.data[0].B_LIST = JSON.stringify(params.data[0].B_LIST)
          post(firstMorningMeeting.furnaceCondition, params).then(res => {
            this.getCrackRemarks()
            this.$message.success('保存成功')
            this.loading = false
          })
        }
      } else {
        this.$message.error('图片删除失败！！！')
      }
    },
    getCrackRemarks2() {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '裂纹备注2'
      }).then(res => {
        this.crackRemarks2.gridData = _.cloneDeep(
          lodash.map(res.data.length ? res.data : [{}], item => {
            return {
              ...item,
              B_LIST: item.B_LIST ? JSON.parse(item.B_LIST) : []
            }
          })
        )
      })
    },
    async saveCrackRemarks2() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '裂纹备注2',
        data: _.map(
          _.sortBy(this.crackRemarks2.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              FLAG: '裂纹备注2',
              SORT_NUM: index + 1
            }
          }
        )
      }
      let del = null
      if (
        this.crackRemarks2.gridData[0].delImage &&
        this.crackRemarks2.gridData[0].delImage.length
      ) {
        del = await post(deleteFileByIds, {
          ids: this.crackRemarks2.gridData[0].delImage
        })
      }
      if (del == null || del.success) {
        if (
          this.crackRemarks2.gridData[0].file &&
          this.crackRemarks2.gridData[0].file.length
        ) {
          const formData = new FormData()
          this.crackRemarks2.gridData[0].file.forEach(item => {
            formData.append('files', item)
          })
          post(uploadFile, formData, false, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.success) {
              params.data[0].B_LIST = JSON.stringify([
                ...params.data[0].B_LIST,
                ...res.data
              ])
              post(firstMorningMeeting.furnaceCondition, params).then(res => {
                this.getCrackRemarks2()
                this.$message.success('保存成功')
                this.loading = false
              })
            } else {
              this.$message.warning('图片上传失败！')
              this.loading = false
            }
          })
        } else {
          params.data[0].B_LIST = JSON.stringify(params.data[0].B_LIST)
          post(firstMorningMeeting.furnaceCondition, params).then(res => {
            this.getCrackRemarks2()
            this.$message.success('保存成功')
            this.loading = false
          })
        }
      } else {
        this.$message.error('图片删除失败！！！')
      }
    },
    getinspectionRemarks() {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '探伤备注'
      }).then(res => {
        this.inspectionRemarks.gridData = _.cloneDeep(
          lodash.map(res.data.length ? res.data : [{}], item => {
            return {
              ...item,
              B_LIST: item.B_LIST ? JSON.parse(item.B_LIST) : []
            }
          })
        )
      })
    },
    async saveinspectionRemarks() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '探伤备注',
        data: _.map(
          _.sortBy(this.inspectionRemarks.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              FLAG: '探伤备注',
              SORT_NUM: index + 1
            }
          }
        )
      }
      let del = null
      if (
        this.inspectionRemarks.gridData[0].delImage &&
        this.inspectionRemarks.gridData[0].delImage.length
      ) {
        del = await post(deleteFileByIds, {
          ids: this.inspectionRemarks.gridData[0].delImage
        })
      }
      if (del == null || del.success) {
        if (
          this.inspectionRemarks.gridData[0].file &&
          this.inspectionRemarks.gridData[0].file.length
        ) {
          const formData = new FormData()
          this.inspectionRemarks.gridData[0].file.forEach(item => {
            formData.append('files', item)
          })
          post(uploadFile, formData, false, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.success) {
              params.data[0].B_LIST = JSON.stringify([
                ...params.data[0].B_LIST,
                ...res.data
              ])

              post(firstMorningMeeting.furnaceCondition, params).then(res => {
                this.getinspectionRemarks()
                this.$message.success('保存成功')
                this.loading = false
              })
            } else {
              this.$message.warning('图片上传失败！')
              this.loading = false
            }
          })
        } else {
          params.data[0].B_LIST = JSON.stringify(params.data[0].B_LIST)
          post(firstMorningMeeting.furnaceCondition, params).then(res => {
            this.getinspectionRemarks()
            this.$message.success('保存成功')
            this.loading = false
          })
        }
      } else {
        this.$message.error('图片删除失败！！！')
      }
    },
    httpRequest(params) {},
    handleChange(file, row, index) {
      if (this[row].gridData[index].file == undefined) {
        this[row].gridData[index].file = [file.raw]
      } else {
        this[row].gridData[index].file.push(file.raw)
      }
      if (this[row].gridData[index].showPic == undefined) {
        this[row].gridData[index].showPic = [file.url]
      } else {
        this[row].gridData[index].showPic.push(file.url)
      }
      this[row] = lodash.cloneDeep(this[row])
    },
    handlePasteImgDelete(file, index, row) {
      this[row].gridData[0].file.splice(index, 1)
      this[row].gridData[0].showPic.splice(index, 1)
      this[row] = lodash.cloneDeep(this[row])
    },
    handlePasteImgDeleteID(file, index, row) {
      if (this[row].gridData[0].delImage == undefined) {
        this[row].gridData[0].delImage = [file.id]
      } else {
        this[row].gridData[0].delImage.push(file.id)
      }
      this[row].gridData[0].B_LIST.splice(index, 1)
      this[row] = lodash.cloneDeep(this[row])
    },
    // 非计划
    showDiaigo() {
      // 编辑数据初始化
      this.unplanned.editData = _.cloneDeep(this.unplanned.data)
      this.unplanned.show = true
    },
    // 非计划数据保存
    saveUnplanned() {
      this.unplanned.loading = true
      const data = _.cloneDeep(this.unplanned.editData)
      _.map(['1', '2', '3', '4'], (item, index) => {
        data[index].FLAG = item
        data[index].PROD_TIME = this.cDate
      })
      post(firstMorningMeeting.spotsPlan(this.cDate), data)
        .then(res => {
          if (res.code == 200) {
            this.$message.success('保存成功')
            this.unplanned.show = false
            this.getUnplanned()
          } else {
            this.$message.warning('保存失败')
          }
          this.unplanned.loading = false
        })
        .catch(err => {
          this.$message.warning('保存失败')
          this.unplanned.loading = false
        })
    },
    getUnplanned() {
      post(firstMorningMeeting.spotsPlanInit, {
        startTime: this.cDate,
        endTime: this.cDate
      })
        .then(res => {
          if (res.code == 200) {
            const data = _.cloneDeep(res.data)
            for (let i = 0; i < 4 - res.data.length; i++) {
              data.push({})
            }
            this.getdefectChart(
              _.map(data, item => {
                if (item.FLAG == undefined || item.FLAG == null) {
                  item.FLAG = ''
                }
                return item
              })
            )
            this.unplanned.data = _.cloneDeep(_.sortBy(data, item => item.FLAG))
          } else {
            this.getdefectChart([{}, {}, {}, {}])
            this.unplanned.data = _.cloneDeep([{}, {}, {}, {}])
          }
        })
        .catch(err => {
          this.getdefectChart([{}, {}, {}, {}])
          this.unplanned.data = _.cloneDeep([{}, {}, {}, {}])
        })
    },
    //非计划获取数据
    getdefectChart(data) {
      const month1Data = { 计划: [], 实际包: [], 实际不包: [] }
      const day1Data = { 计划: [], 实际包: [], 实际不包: [] }
      const month2Data = { 计划: [], 实际包: [], 实际不包: [] }
      const day2Data = { 计划: [], 实际包: [], 实际不包: [] }
      const saveDataName = {
        PLAN_TARGET_: '计划',
        REALITY_TARGET_: '实际包',
        REALITY_TARGETINCLUDE_: '实际不包'
      }
      if (data.length) {
        const month1 = _.filter(data, item => item.FLAG.includes('2'))
        const day1 = _.filter(data, item => item.FLAG.includes('1'))
        const month2 = _.filter(data, item => item.FLAG.includes('4'))
        const day2 = _.filter(data, item => item.FLAG.includes('3'))
        const newData = [month1Data, day1Data, month2Data, day2Data]
        _.forEach([month1, day1, month2, day2], (item, index) => {
          if (item.length) {
            _.forEach(Object.keys(saveDataName), key => {
              const name = saveDataName[key]
              _.forEach(['C1', 'C2', 'C3', 'B1'], items => {
                newData[index][name].push(item[0][key + items])
              })
            })
          }
        })
      }
      this.barData = {
        spots: {
          monthData: month1Data,
          dayData: day1Data
        },
        original: {
          monthData: month2Data,
          dayData: day2Data
        }
      }
    },
    // 监听数据
    watchdefectChart(data, data2) {
      console.log(data, data2)
      this.spots.bar1[0].data = data.计划
      this.spots.bar1[1].data = data.实际包
      this.spots.bar1[2].data = data.实际不包
      this.original.bar1[0].data = data2.计划
      this.original.bar1[1].data = data2.实际包
      this.original.bar1[2].data = data2.实际不包
      console.log(this.original)
    },
    // 非计划情况说明
    getvariety2() {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'variety2'
      }).then(res => {
        this.variety2.gridData = _.cloneDeep(
          _.map(res.data.length ? res.data : [{}], item => {
            return {
              ...item,
              B_LIST: item.B_LIST ? JSON.parse(item.B_LIST) : []
            }
          })
        )
      })
    },
    async savevariety2() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 'variety2',
        data: _.map(
          _.sortBy(this.variety2.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              FLAG: 'variety2',
              SORT_NUM: index + 1
            }
          }
        )
      }

      let del = null
      if (
        this.variety2.gridData[0].delImage &&
        this.variety2.gridData[0].delImage.length
      ) {
        del = await post(deleteFileByIds, {
          ids: this.variety2.gridData[0].delImage
        })
      }
      if (del == null || del.success) {
        if (
          this.variety2.gridData[0].file &&
          this.variety2.gridData[0].file.length
        ) {
          const formData = new FormData()
          this.variety2.gridData[0].file.forEach(item => {
            formData.append('files', item)
          })
          post(uploadFile, formData, false, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.success) {
              params.data[0].B_LIST = JSON.stringify([
                ...params.data[0].B_LIST,
                ...res.data
              ])
              post(firstMorningMeeting.furnaceCondition, params).then(res => {
                this.getvariety2()
                this.$message.success('保存成功')
                this.loading = false
              })
            } else {
              this.$message.warning('图片上传失败！')
              this.loading = false
            }
          })
        } else {
          params.data[0].B_LIST = JSON.stringify(params.data[0].B_LIST)
          post(firstMorningMeeting.furnaceCondition, params).then(res => {
            this.getvariety2()
            this.$message.success('保存成功')
            this.loading = false
          })
        }
      } else {
        this.$message.error('图片删除失败！！！')
      }
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
    position: relative;
    .operate-box {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}

.operate-box {
  position: absolute;
  right: 20px;
  top: 10px;
  z-index: 5;
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
  }
}
</style>
