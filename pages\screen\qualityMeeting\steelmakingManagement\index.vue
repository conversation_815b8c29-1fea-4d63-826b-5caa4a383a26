<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="4"
          class="full-height">
          <screen-border :title="'成分合格率'">
            <div class="chart-wrapper">
              <div
                class="operate-box text-right">
                <el-radio-group
                  v-model="pieChart.dateType1"
                  size="mini"
                  class="screen-input"
                  @input="changePieChart($event)">
                  <el-radio-button :label="0">日</el-radio-button>
                  <el-radio-button :label="1">月</el-radio-button>
                </el-radio-group>
              </div>
              <div
                class="chart"
                @click="showRateDetail()">
                <pie-rate-chart
                  :chart-data="Number(pieChart.RATE)"
                  :unit="'%'"
                  :title="'成分合格率'"
                  :title-num="pieChart.RATE + '%'"
                  :title-text="'不合格炉数：' + pieChart.NUM"
                  :label-width="22"
                  :color="pieChart.color"
                  :vertical="false"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="4"
          class="full-height">
          <custom-table-6
            :title="'坯原钢种一次合格率'"
            :setting="detection1"
            :url-list="blankUrl.list"
            :url-save="blankUrl.save"
            :select-date="selectDate"
            :show-table="false"
            @change="getBlank">
            <template v-slot:content>
              <div class="chart-wrapper">
                <div
                  class="operate-box text-right">
                  <el-radio-group
                    v-model="pieChart2.dateType1"
                    size="mini"
                    class="screen-input"
                    @input="changeBlankPieChart($event)">
                    <el-radio-button :label="0">日</el-radio-button>
                    <el-radio-button :label="1">月</el-radio-button>
                  </el-radio-group>
                </div>
                <div
                  class="chart"
                  @click="showBlankRateDetail()">
                  <pie-rate-chart
                    :chart-data="Number(pieChart2.RATE)"
                    :unit="'%'"
                    :title="'合格率'"
                    :title-num="pieChart2.RATE + '%'"
                    :label-width="22"
                    :color="pieChart2.color"
                    :vertical="false"/>
                </div>
              </div>
            </template>
          </custom-table-6>
        </el-col>
        <el-col
          :span="4"
          class="full-height">
          <screen-border :title="'铸坯一次收得率'">
            <div class="chart-wrapper">
              <div
                class="operate-box text-right">
                <el-radio-group
                  v-model="pieChartFirst.dateType1"
                  size="mini"
                  class="screen-input"
                  @input="changePieChart2($event)">
                  <el-radio-button :label="0">日</el-radio-button>
                  <el-radio-button :label="1">月</el-radio-button>
                </el-radio-group>
              </div>
              <div
                class="chart"
                @click="getDataByToday(1)">
                <pie-chart
                  v-loading="loadingdetail"
                  :chart-data="oneGet.bar1"
                  :color="['#19BE6B', '#FFB243', '#FF2855']"
                  :unit="'%'" />
                  <!-- <pie-rate-chart
                   :chart-data="Number(pieChart.RATE)"
                   :unit="'%'"
                   :title="'一次收得率'"
                   :title-num="pieChart.RATE + '%'"
                   :label-width="22"
                   :vertical="false"/> -->
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="4"
          class="full-height">
          <screen-border :title="'铸坯收得率'">
            <div class="chart-wrapper">
              <div
                class="operate-box text-right">
                <el-radio-group
                  v-model="pieChartFirst.dateType1"
                  size="mini"
                  class="screen-input"
                  @input="changePieChart2($event)">
                  <el-radio-button :label="0">日</el-radio-button>
                  <el-radio-button :label="1">月</el-radio-button>
                </el-radio-group>
              </div>
              <div
                class="chart"
                @click="getDataByToday2(1)">
                <pie-chart
                  v-loading="loadingdetail"
                  :chart-data="oneGet2.bar1"
                  :color="['#19BE6B', '#FFB243', '#FF2855']"
                  :unit="'%'" />
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <custom-table5
            :title="'炼钢质量信息'"
            :setting="steelMaking"
            :url-list="steelMakingUrl.list"
            :url-save="steelMakingUrl.save"
            :select-date="selectDate"/>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">


        <el-col
          :span="8"
          class="full-height"
          style="float:left;position:absolute;"
        >
          <custom-table2
            :title="'裂纹发生率/改判率'"
            :pop-title="'月数据调整值编辑'"
            :setting="flawMonth"
            :url-list="flawMonthUrl.list"
            :url-save="flawMonthUrl.save"
            :select-date="selectDate"
            :show-table="false"
            @change="getMonthData">
            <span
              v-command="'/screen/qualityMeeting/edit'"
              class="screen-btn"
              @click="dialogVisible = true">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
            <template v-slot:content>
              <div
                class="chart-wrapper">
                <div
                  class="chart"
                  style="padding-top: 12px">
                  <div class="operate-box">
                    <el-radio-group
                      v-model="processed.dateType1"
                      size="mini"
                      class="screen-input"
                      @input="changeProcessed($event)">
                      <el-radio-button :label="0">日</el-radio-button>
                      <el-radio-button :label="1">月</el-radio-button>
                    </el-radio-group>
                  </div>
                  <bars-chart
                    :bar-width="46"
                    :chart-data="processed.barAll"
                    :x-data="processed.barX1"
                    :tooltipbg="true"
                    :unit="'%'"
                    @selected="getIncidenceRate($event)"/>
                </div>
                <div
                  class="fail-reason"
                  style="height: 15%">
                  <template v-if="processed.failReason11">
                    <span>未完成原因：</span><span>{{ processed.failReason11 || '无' }}</span>
                  </template>
                </div>
              </div>
            </template>
          </custom-table2>
        </el-col>
        <el-col
          :span="8"
          sty
          class="full-height">
          <custom-table3
            ref="table1"
            :url-list="flawUrl.list"
            :url-save="flawUrl.save"
            :select-date="selectDate"
            :show-table="false"
            :setting="flaw"
            :pop-title="'发生率详情'"
            @change="getPocessed">
            <template v-slot:content>
              <div
                class="chart-wrapper">
                <div
                  class="chart"
                  style="padding-top: 12px">
                  <div
                    class="operate-box">
                    <el-radio-group
                      v-model="processed.dateType1"
                      size="mini"
                      class="screen-input"
                      @input="changeProcessed($event)">
                      <el-radio-button :label="0">日</el-radio-button>
                      <el-radio-button :label="1">月</el-radio-button>
                    </el-radio-group>
                  </div>
                  <bars-chart
                    :bar-width="46"
                    :chart-data="processed.barAll"
                    :x-data="processed.barX1"
                    :tooltipbg="true"
                    :unit="'%'"
                    @selected="getIncidenceRate($event)"/>
                </div>
                <div
                  class="fail-reason"
                  style="height: 15%">
                  <template v-if="processed.failReason1">
                    <span>未完成原因：</span><span>{{ processed.failReason1 || '无' }}</span>
                  </template>
                </div>
              </div>
            </template>
          </custom-table3>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <custom-table
            ref="table2"
            :title="'探伤合格率'"
            :setting="detection"
            :url-list="detectionUrl.list"
            :url-save="detectionUrl.save"
            :select-date="selectDate"
            :show-table="false"
            @change="getPocessed1">
            <template v-slot:content>
              <div class="chart-wrapper">
                <div
                  class="chart">
                  <div
                    class="operate-box">
                    <el-radio-group
                      v-model="processed.dateType2"
                      size="mini"
                      class="screen-input"
                      @input="changeProcessed1($event)">
                      <el-radio-button :label="0">日</el-radio-button>
                      <el-radio-button :label="1">月</el-radio-button>
                    </el-radio-group>
                  </div>
                  <single-bars-chart
                    :show-legend="false"
                    :chart-data="processed.bar2"
                    :unit="'%'"
                    :x-data="processed.barX2"
                    @selected="getDetectionDetailed($event)"/>
                </div>
                <div
                  class="fail-reason"
                  style="height: 15%">
                  <template v-if="processed.failReason2">
                    <span>未完成原因：</span><span>{{ processed.failReason2 || '无' }}</span>
                  </template>
                </div>
              </div>
            </template>
          </custom-table>
        </el-col>
        <el-col
          :span="8"
          style="float:left;"
          class="full-height"
        >
          <custom-table4
            :title="'缺陷发生量/改判量（t）'"
            :setting="flawNum"
            :url-list="flawNumUrl.list"
            :url-save="flawNumUrl.save"
            :select-date="selectDate"/>
        </el-col>
      </el-row>
    </div>
    <!--    裂纹发生详情-->
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'95%'"
      :top="'10%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="裂纹发生详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          裂纹发生详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList"
        max-height="400"
        class="center-table"
        border>
        <el-table-column
          property="产线"
          label="产线">
          <template v-slot="{ row }">
            {{ codePlt[row['产线']] }}
          </template>
        </el-table-column>
        <el-table-column
          property="钢板号"
          label="钢板号"/>
        <el-table-column
          property="板坯钢种"
          label="板坯钢种"/>
        <el-table-column
          property="轧制标准"
          label="轧制标准" />
        <el-table-column
          property="厚度"
          label="厚度"
          width="70"/>
        <el-table-column
          property="宽度"
          label="宽度"
          width="60"/>
        <el-table-column
          property="长度"
          label="长度"
          width="70"/>
        <el-table-column
          property="重量"
          label="重量"
          width="70"/>
        <el-table-column
          property="表面等级"
          label="表面等级"
          width="75"/>
        <el-table-column
          property="缺陷"
          label="缺陷"/>
        <el-table-column
          property="改判缺陷"
          label="改判缺陷"
          width="110"/>
        <el-table-column
          property="铸机号"
          label="铸机号"
          width="60"/>
        <el-table-column
          property="铸坯厚度"
          label="铸坯厚度"
          width="75"/>
        <el-table-column
          property="铸坯宽度"
          label="铸坯宽度"
          width="75"/>
        <el-table-column
          property="铸坯长度"
          label="铸坯长度"
          width="75"/>
        <el-table-column
          property="异常坯"
          label="异常坯"
          width="75"/>
        <el-table-column
          property="铸坯生产时间"
          label="铸坯生产时间"
          width="100"/>
        <el-table-column
          property="装炉温度"
          label="装炉温度"
          width="75"/>
      </el-table>
    </el-dialog>
    <!--    裂纹改判详情-->
    <el-dialog
      :visible.sync="dialogVisible1"
      :width="'95%'"
      :top="'10%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          裂纹改判详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList1"
        max-height="400"
        class="center-table"
        border>
        <el-table-column
          property="钢板号"
          label="钢板号"/>
        <el-table-column
          property="板坯钢种"
          label="板坯钢种"/>
        <el-table-column
          property="轧制标准"
          label="轧制标准" />
        <el-table-column
          property="厚度"
          label="厚度"
          width="70"/>
        <el-table-column
          property="宽度"
          label="宽度"
          width="70"/>
        <el-table-column
          property="长度"
          label="长度"
          width="70"/>
        <el-table-column
          property="重量"
          label="重量"
          width="70"/>
        <el-table-column
          property="表面等级"
          label="表面等级"
          width="80"/>
        <el-table-column
          property="缺陷"
          label="缺陷"/>
        <el-table-column
          property="改判缺陷"
          label="改判缺陷"
          width="110"/>
        <el-table-column
          property="铸机号"
          label="铸机号"
          width="70"/>
        <el-table-column
          property="铸坯厚度"
          label="铸坯厚度"
          width="80"/>
        <el-table-column
          property="铸坯宽度"
          label="铸坯宽度"
          width="80"/>
        <el-table-column
          property="铸坯长度"
          label="铸坯长度"
          width="80"/>
        <el-table-column
          property="铸坯生产时间"
          label="铸坯生产时间"
          width="100"/>
        <el-table-column
          property="装炉温度"
          label="装炉温度"
          width="80"/>
      </el-table>
    </el-dialog>
    <!--    探伤不合格详情-->
    <el-dialog
      :visible.sync="dialogVisible2"
      :width="'1320px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          探伤不合格详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList2"
        class="center-table"
        border>
        <el-table-column
          property="产线"
          label="产线"
          width="80">
          <template v-slot="{ row }">
            {{ codePlt[row['产线']] }}
          </template>
        </el-table-column>
        <el-table-column
          property="钢板号"
          label="钢板号"/>
        <el-table-column
          property="板坯钢种"
          label="板坯钢种"/>
        <el-table-column
          property="标准号"
          label="标准号"/>
        <el-table-column
          property="检查标准"
          label="检查标准"/>
        <el-table-column
          property="结论"
          label="结论"
          width="60">
          <template v-slot="{ row }">
            <!-- {{ resultList[row['结论']] }} -->
            {{ row['结论'] }}
          </template>
        </el-table-column>
        <el-table-column
          property="改判原因"
          label="改判原因"
          width="80"/>
        <el-table-column
          property="厚度"
          label="厚度"
          width="80"/>
        <el-table-column
          property="宽度"
          label="宽度"
          width="80"/>
        <el-table-column
          property="长度"
          label="长度"
          width="80"/>
        <el-table-column
          property="重量"
          label="重量"
          width="80"/>
        <el-table-column
          property="铸坯堆冷时间"
          label="铸坯堆冷时间"
          width="85">
          <template v-slot="{ row }">
            {{ row['铸坯堆冷时间'] }}
          </template>
        </el-table-column>
        <el-table-column
          property="经RH"
          label="经RH"
          width="60"/>
        <el-table-column
          property="异常坯"
          label="异常坯"
          width="75"/>
        <el-table-column
          v-if="flawList2.length>0&&flawList2[0]['产线'] == 'C2'"
          label="操作"
          min-width="120">
          <template v-slot="{ row }">
            <span
              style="cursor: pointer;"
              @click="getRow(row,1)">
              数据查询
            </span>
            <span
              style="cursor: pointer;"
              @click="getRow(row,2)">
              主题追溯
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!--    成分不合格详情-->
    <el-dialog
      :visible.sync="dialogVisible3"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          成分不合格详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList3"
        class="center-table"
        border>
        <el-table-column
          label="物料号"
          property="matId"/>
        <el-table-column
          label="钢种"
          property="steelGrade"/>
        <el-table-column
          label="不合格元素及含量"
          property="chemCd"/>
        <el-table-column
          label="规格"
          property="thk"/>
        <el-table-column
          label="处置"
          property="opinion"/>
      </el-table>
    </el-dialog>
    <!--    坯钢种一次合格率-->
    <el-dialog
      :visible.sync="dialogVisibleBlankRate"
      :width="'96%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          铸坯原钢种一次合格率详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :summary-method="getSummaries"
        :data="blankRateList"
        show-summary
        class="center-table"
        border>
        <el-table-column
          label="连铸机"
          property="prcLine"/>
        <el-table-column
          label="铸坯">
          <el-table-column
            label="连铸产量"
            property="ccmWgt"/>
          <el-table-column
            label="表面裂纹修磨量"
            property="faceWgt"/>
          <el-table-column
            label="角裂修磨量"
            property="filletWgt"/>
          <el-table-column
            label="板面裂纹修磨量"
            property="slabFaceWgt"/>
          <el-table-column
            label="端部缺陷修磨量"
            property="selosWgt"/>
          <el-table-column
            label="其他缺陷修磨量"
            property="otherWgt"/>
          <el-table-column
            label="轧前判废量"
            property="scrapWgt"/>
          <el-table-column
            label="化学成分改判量"
            property="slabDeliWgt"/>
            <!-- <el-table-column
            label="工艺异常改判量"
            property="processUpsetsWgt"/> -->
        </el-table-column>
        <el-table-column
          label="钢板">
          <el-table-column
            label="裂纹发生量"
            property="lwOccurWgt"/>
          <el-table-column
            label="夹杂发生量"
            property="afbOccurWgt"/>
          <el-table-column
            label="结疤和重皮发生量"
            property="jcOccurWgt"/>
          <el-table-column
            label="探伤不合量"
            property="ustUnqualifiedWgt"/>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!--    一次收得率-->
    <el-dialog
      :visible.sync="dialogVisibleFirstget"
      :width="'96%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          一次收得率
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="FirstGetList"
        class="center-table"
        border
        @header-click="handleHeaderClick">
        <el-table-column
          label="连铸机"
          property="prcLine"/>
        <el-table-column
          label="产量"
          property="CCM_WGT"/>
        <el-table-column
          label="一次收得率"
          property="oneGet">
          <template slot-scope="scope">
            {{ fun((scope.row.CCM_WGT-scope.row.lianjiao-scope.row.touweipi-scope.row.duijiepi-scope.row.gongyishiyan-scope.row.qita-scope.row.chengfenchao-scope.row.gongyiyichang-scope.row.swzlyc-scope.row.gongyixiumo-scope.row.sizeNoplan-scope.row.lw_wgt-scope.row.ts_wgt-scope.row.jz_wgt-scope.row.jb_wgt)*100/scope.row.CCM_WGT) }}%
          </template>
        </el-table-column>
        <el-table-column
          label="生产计划内">
          <el-table-column
            label="连浇"
            property="lianjiao">
            <template slot-scope="scope">
              {{ fun(scope.row.lianjiao) }}
            </template>
          </el-table-column>
          <el-table-column
            label="头尾坯"
            property="touweipi">
            <template slot-scope="scope">
              {{ fun(scope.row.touweipi) }}
            </template>
          </el-table-column>
          <el-table-column
            label="对接坯"
            property="duijiepi">
            <template slot-scope="scope">
              {{ fun(scope.row.duijiepi) }}
            </template>
          </el-table-column>
          <el-table-column
            label="工艺试验"
            property="gongyishiyan">
            <template slot-scope="scope">
              {{ fun(scope.row.gongyishiyan) }}
            </template>
          </el-table-column>
          <el-table-column
            label="其他"
            property="qita">
            <template slot-scope="scope">
              {{ fun(scope.row.qita) }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="生产计划外"
        >
          <el-table-column
            label="成分超"
            property="chengfenchao">
            <template slot-scope="scope">
              {{ scope.row.chengfenchao }}
            </template>
          </el-table-column>
          <el-table-column
            label="工艺异常"
            property="gongyiyichang">
            <template slot-scope="scope">
              {{ fun(scope.row.gongyiyichang) }}
            </template>
          </el-table-column>
          <el-table-column
            label="实物质量异常"
            property="swzlyc">
            <template slot-scope="scope">
              {{ fun(scope.row.swzlyc) }}
            </template>
          </el-table-column>
          <el-table-column
            label="工艺修磨"
            property="gongyixiumo">
            <template slot-scope="scope">
              {{ fun(scope.row.gongyixiumo) }}
            </template>
          </el-table-column>
          <el-table-column
            label="尺寸(非计划)"
            property="sizeNoplan">
            <template slot-scope="scope">
              {{ fun(scope.row.sizeNoplan) }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="钢板(钢板实绩)"
        >
          <el-table-column
            label="裂纹(纵向裂纹、碎裂纹、气泡状裂纹)"
            property="lw_wgt">
            <template slot-scope="scope">
              {{ fun(scope.row.lw_wgt) }}
            </template>
          </el-table-column>
          <el-table-column
            label="探伤不合"
            property="ts_wgt">
            <template slot-scope="scope">
              {{ fun(scope.row.ts_wgt) }}
            </template>
          </el-table-column>
          <el-table-column
            label="夹杂"
            property="jz_wgt">
            <template slot-scope="scope">
              {{ fun(scope.row.jz_wgt) }}
            </template>
          </el-table-column>
          <el-table-column
            label="结疤"
            property="jb_wgt">
            <template slot-scope="scope">
              {{ fun(scope.row.jb_wgt) }}
            </template>
          </el-table-column>
        </el-table-column>
        <!-- <el-table-column
           label="钢板(钢板实际)">
           <el-table-column
             label="裂纹（纵向裂纹、碎裂纹、气泡状裂纹）"
             property="chengfenchao"/>
           <el-table-column
             label="探伤不合"
             property="gongyiyichang"/>
           <el-table-column
             label="夹杂"
             property="swzlyc"/>
           <el-table-column
             label="结疤"
             property="gongyixiumo"/>
         </el-table-column> -->
      </el-table>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogVisibleInplan"
      :width="'1200px'"
      :height="'800px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          生产计划内
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="inPlanList"
        :height="'643px'"
        class="center-table"
        border>
        <el-table-column
          label="锁定代码"
          property="cadManaNo"/>
        <el-table-column
          label="锁定详称"
          property="cadComment"/>
        <el-table-column
          label="一级弹窗表抬头"
          property="titleName"/>
        <el-table-column
          label="重量"
          property="wgt">
          <el-table-column
            label="1号机"
            property="prcLine1"/>
          <el-table-column
            label="2号机"
            property="prcLine2"/>
          <el-table-column
            label="3号机"
            property="prcLine3"/>
          <el-table-column
            label="0号机"
            property="prcLine0"/>
          <el-table-column
            label="汇总"
            property="prcLinez"/>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogVisibleNpPlan"
      :width="'1200px'"
      :height="'800px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          生产计划外
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="noPlanList"
        :height="'643px'"
        class="center-table"
        border>
        <el-table-column
          label="锁定代码"
          property="cadManaNo"/>
        <el-table-column
          label="锁定详称"
          property="cadComment"/>
        <el-table-column
          label="一级弹窗表抬头"
          property="titleName"/>
        <el-table-column
          label="重量"
          property="wgt">
          <el-table-column
            label="1号机"
            property="prcLine1"/>
          <el-table-column
            label="2号机"
            property="prcLine2"/>
          <el-table-column
            label="3号机"
            property="prcLine3"/>
          <el-table-column
            label="0号机"
            property="prcLine0"/>
          <el-table-column
            label="汇总"
            property="prcLinez"/>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogVisibleFirstget2"
      :width="'96%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          铸坯收得率
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="FirstGetList2"
        class="center-table"
        border
        @header-click="handleHeaderClick2">
        <el-table-column
          label="连铸机"
          property="prcLine"/>
        <el-table-column
          label="产量"
          property="CCM_WGT"/>
        <el-table-column
          label="生产计划内(改判，协议，判废)">
          <el-table-column
            label="消化积压坯轧XYB"
            property="xiaohuajiya">
            <template slot-scope="scope">
              {{ fun(scope.row.xiaohuajiya) }}
            </template>
          </el-table-column>
          <el-table-column
            label="头尾坯"
            property="touweipi">
            <template slot-scope="scope">
              {{ fun(scope.row.touweipi) }}
            </template>
          </el-table-column>
          <el-table-column
            label="对接坯"
            property="duijiepi">
            <template slot-scope="scope">
              {{ fun(scope.row.duijiepi) }}
            </template>
          </el-table-column>
          <el-table-column
            label="调宽坯"
            property="tiaokuanpi">
            <template slot-scope="scope">
              {{ fun(scope.row.tiaokuanpi) }}
            </template>
          </el-table-column>
          <el-table-column
            label="连浇段"
            property="lianjiao">
            <template slot-scope="scope">
              {{ fun(scope.row.lianjiao) }}
            </template>
          </el-table-column>
          <el-table-column
            label="其他"
            property="qita">
            <template slot-scope="scope">
              {{ fun(scope.row.qita) }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="生产计划外(改判，协议，判废)"
        >

          <el-table-column
            label="钢种改判"
            property="gangzhonggp">
            <template slot-scope="scope">
              {{ fun(scope.row.gangzhonggp) }}
            </template>
          </el-table-column>
          <el-table-column
            label="工艺异常"
            property="gongyiyc">
            <template slot-scope="scope">
              {{ fun(scope.row.gongyiyc) }}
            </template>
          </el-table-column>
          <el-table-column
            label="实物质量异常"
            property="swzlyc">
            <template slot-scope="scope">
              {{ fun(scope.row.swzlyc) }}
            </template>
          </el-table-column>
          <el-table-column
            label="成分超"
            property="chengfenchao">
            <template slot-scope="scope">
              {{ scope.row.chengfenchao }}
            </template>
          </el-table-column>
          <!-- <el-table-column
             label="工艺修磨"
             property="gongyixiumo">
             <template slot-scope="scope">
               {{ fun(scope.row.gongyixiumo) }}
             </template>
           </el-table-column> -->
          <!-- <el-table-column
             label="尺寸(非计划)"
             property="sizeNoplan">
             <template slot-scope="scope">
               {{ fun(scope.row.sizeNoplan) }}
             </template>
           </el-table-column> -->
        </el-table-column>
        <el-table-column
          label="钢板改判"
        >
          <el-table-column
            label="裂纹"
            property="lw_wgt">
            <template slot-scope="scope">
              {{ fun(scope.row.lw_wgt) }}
            </template>
          </el-table-column>
          <el-table-column
            label="探伤不合"
            property="ts_wgt">
            <template slot-scope="scope">
              {{ fun(scope.row.ts_wgt) }}
            </template>
          </el-table-column>
          <el-table-column
            label="夹杂"
            property="jz_wgt">
            <template slot-scope="scope">
              {{ fun(scope.row.jz_wgt) }}
            </template>
          </el-table-column>
          <el-table-column
            label="结疤"
            property="jb_wgt">
            <template slot-scope="scope">
              {{ fun(scope.row.jb_wgt) }}
            </template>
          </el-table-column>
        </el-table-column>
        <!-- <el-table-column
           label="钢板(钢板实际)">
           <el-table-column
             label="裂纹（纵向裂纹、碎裂纹、气泡状裂纹）"
             property="chengfenchao"/>
           <el-table-column
             label="探伤不合"
             property="gongyiyichang"/>
           <el-table-column
             label="夹杂"
             property="swzlyc"/>
           <el-table-column
             label="结疤"
             property="gongyixiumo"/>
         </el-table-column> -->
      </el-table>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogVisibleInplan2"
      :width="'1200px'"
      :height="'800px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          生产计划内
        </div>
      </template>
      <el-table
        v-loading="loading2"
        :data="inPlanList2"
        :height="'643px'"
        class="center-table"
        border>
        <el-table-column
          label="解锁代码"
          property="estCd"/>
        <el-table-column
          label="代码简称"
          property="cdShortName"/>
        <el-table-column
          label="一级弹窗表抬头"
          property="titleName"/>
        <el-table-column
          label="重量"
          property="wgt">
          <el-table-column
            label="1号机"
            property="prcLine1"/>
          <el-table-column
            label="2号机"
            property="prcLine2"/>
          <el-table-column
            label="3号机"
            property="prcLine3"/>
          <el-table-column
            label="0号机"
            property="prcLine0"/>
          <el-table-column
            label="汇总"
            property="prcLinez"/>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogVisibleNpPlan2"
      :width="'1200px'"
      :height="'800px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          生产计划外
        </div>
      </template>
      <el-table
        v-loading="loading2"
        :data="noPlanList2"
        :height="'643px'"
        class="center-table"
        border>
        <el-table-column
          label="解锁代码"
          property="estCd"/>
        <el-table-column
          label="代码简称"
          property="cdShortName"/>
        <el-table-column
          label="一级弹窗表抬头"
          property="titleName"/>
        <el-table-column
          label="重量"
          property="wgt">
          <el-table-column
            label="1号机"
            property="prcLine1"/>
          <el-table-column
            label="2号机"
            property="prcLine2"/>
          <el-table-column
            label="3号机"
            property="prcLine3"/>
          <el-table-column
            label="0号机"
            property="prcLine0"/>
          <el-table-column
            label="汇总"
            property="prcLinez"/>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>
 <script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border.vue'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import CustomTable from '@/pages/screen/qualityMeeting/component/custom-table'
import CustomTable2 from '@/pages/screen/qualityMeeting/component/custom-table2'
import CustomTable3 from '@/pages/screen/qualityMeeting/component/custom-table3'
import CustomTable4 from '@/pages/screen/qualityMeeting/component/custom-table4'
import CustomTable5 from '@/pages/screen/qualityMeeting/component/custom-table5'
import CustomTable6 from '@/pages/screen/qualityMeeting/component/custom-table6'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import {
  qmsQualityQuery,
  qmsQualitySave,
  qmsQualitySavePlateFlaw,
  qmsQualityQueryPlateFlaw,
  saveFlawSituation,
  findFlawSituationByDate,
  findInspectionPassRateByDate,
  saveInspectionPassRate,
  processAlarm,
  incidenceRateDetailed,
  correctionRateDetailed,
  detectionDetailed,
  getChemQualified,
  getChemQualifiedDetail,
  incidenceRate,
  QualityQuery,
  QualitySave,
  rsgfbRate,
  rsgfbRateDetails,
  rsgfbRateDetails2,
  saveSlabYgzFirstPassRate,
  findSlabYgzFirstPassRate,
  saveFlawSituationConfig,
  findFlawSituationConfigByDate,
  getDataByToday,
  getDataByToday2,
  qmsPygzFirstpassrateNew,
  qmsPygzFirstpassrateSaveNew
} from '@/api/screen'
import PolarChart from '@/pages/screen/energyMeeting/component/polar-chart'
import PieChart from '@/pages/screen/qualityMeeting/component/pie-chart2'
import PieRateChart from '@/pages/screen/qualityMeeting/component/pie-rate-chart'
import BarsChart from '@/pages/screen/diviceMeeting/component/bars-chart2'
export default {
  name: 'steelMaking',
  components: {
    PieRateChart,
    PolarChart,
    CustomTable,
    CustomTable2,
    SingleBarsChart,
    ScreenBorder,
    CustomTable3,
    CustomTable4,
    CustomTable5,
    CustomTable6,
    PieChart,
    BarsChart
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loadingdetail: true,
      loading: false,
      loading2: false,
      flawList3: [], // 成分不合格详情
      dialogVisible3: false,
      pieChart: {
        total: '',
        RATE: 0, //
        NUM: 0, //
        color: '#19be6b',
        dateType1: 0
      },
      pieChartFirst: {
        total: '',
        RATE: 0, //
        NUM: 0, //
        color: '#19be6b',
        dateType1: 0
      },
      pieChart2: {
        total: '',
        RATE: 0, //
        NUM: 0, //
        color: '#19be6b',
        dateType1: 0,
        dataList: []
      },
      blankRateList: [],
      dialogVisibleBlankRate: false,
      FirstGetList: [],
      dialogVisibleFirstget: false,
      inPlanList: [],
      dialogVisibleInplan: false,
      noPlanList: [],
      dialogVisibleNpPlan: false,
      FirstGetList2: [],
      dialogVisibleFirstget2: false,
      inPlanList2: [],
      dialogVisibleInplan2: false,
      noPlanList2: [],
      dialogVisibleNpPlan2: false,
      arr: [],
      arr2: [],
      arr3: [],
      oneGet: {
        bar1: []
      },
      oneGet2: {
        bar1: []
      },
      steelMakingUrl: {
        save: QualitySave,
        list: QualityQuery
      },
      steelMaking: [
        // {
        //   type: 'index',
        //   label: '序号'
        // },
        {
          keyQuery: 'castingnum',
          keySave: 'castingNum',
          label: '铸机号',
          width: '100'
        },
        {
          keyQuery: 'qualitycondition',
          keySave: 'qualityCondition',
          label: '质量情况',
          inputType: 'textarea',
          align: 'left'
        },
        {
          keyQuery: 'qualitycondition2',
          keySave: 'qualitycondition2',
          label: '铸坯改判情况'
        },
        {
          keyQuery: 'responsibilityunit',
          keySave: 'responsibilityUnit',
          label: '责任单位',
          align: 'left',
          onlyEdit: true
        },
        {
          keyQuery: 'situationdescription',
          keySave: 'situationDescription',
          label: '情况说明',
          inputType: 'textarea',
          align: 'left',
          onlyEdit: true
        }
      ],
      flawNumUrl: {
        save: saveFlawSituation,
        list: findFlawSituationByDate
      },
      flawNum: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'crackgeneration',
          keySave: 'crackGeneration',
          label: '裂纹发生量'
        },
        {
          keyQuery: 'crackcorrection',
          keySave: 'crackCorrection',
          label: '裂纹改判量'
        },
        {
          keyQuery: 'dfdquantity',
          keySave: 'dfdQuantity',
          label: '探伤不合格量'
        },
        {
          keyQuery: 'twpjtbhl',
          keySave: 'twpjtbhl',
          label: '头尾坯探伤不合格量'
        },
        {
          keyQuery: 'jbfs',
          keySave: 'jbfs',
          label: '结疤发生量'
        },
        {
          keyQuery: 'jbgp',
          keySave: 'jbgp',
          label: '结疤改判量'
        }
      ],
      // String   rollingMill            轧钢厂
      // String   changeValue            改判率实际值
      // String   changeTargetValue      改判率目标值
      // String   incidenceValue         发生率实际值
      // String   incidenceTargetValue   发生率目标值
      // String   isItDone               是否完成
      // String   reasonNotCompl         未完成原因
      // String   comments               备注
      flawUrl: {
        save: qmsQualitySavePlateFlaw,
        list: qmsQualityQueryPlateFlaw
      },
      flaw: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'incidencetargetvalue',
          keySave: 'incidenceTargetValue',
          label: '发生率目标值'
        },
        {
          keyQuery: 'incidencevalue',
          keySave: 'incidenceValue',
          label: '发生率实际值'
        },
        {
          keyQuery: 'reasonnotcompl',
          keySave: 'reasonNotCompl',
          label: '发生率未完成原因'
        },
        {
          keyQuery: 'changetargetvalue',
          keySave: 'changeTargetValue',
          label: '改判率目标值'
        },
        {
          keyQuery: 'changevalue',
          keySave: 'changeValue',
          label: '改判率实际值'
        },
        {
          keyQuery: 'changenotcompl',
          keySave: 'ChangeNotCompl',
          label: '改判率未完成原因'
        },
        {
          keyQuery: 'lwmonthdata',
          keySave: 'lwMonthData',
          label: '裂纹月数据',
          show: false
        },
        {
          keyQuery: 'gpmonthdata',
          keySave: 'gpMonthData',
          label: '改判月数据',
          show: false
        }
      ],
      // 缺陷月数据修改
      flawMonthUrl: {
        save: saveFlawSituationConfig,
        list: findFlawSituationConfigByDate
      },
      flawMonth: [
        {
          keyQuery: 'rollingMill',
          keySave: 'rollingMill',
          label: '轧钢厂',
          disabled: true
        },
        {
          keyQuery: 'production',
          keySave: 'production',
          label: '产量',
          disabled: true
        },
        {
          keyQuery: 'detectionValue',
          keySave: 'detectionValue',
          label: '探伤量',
          disabled: true
        },
        {
          keyQuery: 'crackGenerationValue',
          keySave: 'crackGenerationValue',
          label: '裂纹发生量',
          disabled: true
        },
        {
          keyQuery: 'crackCorrectionValue',
          keySave: 'crackCorrectionValue',
          label: '裂纹改判量',
          disabled: true
        },
        {
          keyQuery: 'dfdQuantityValue',
          keySave: 'dfdQuantityValue',
          label: '探伤不合格量',
          disabled: true
        },
        {
          keyQuery: 'crackGeneration',
          keySave: 'crackGeneration',
          label: '裂纹发生量调整值值'
        },
        {
          keyQuery: 'crackCorrection',
          keySave: 'crackCorrection',
          label: '裂纹改判量调整值'
        },
        {
          keyQuery: 'dfdQuantity',
          keySave: 'dfdQuantity',
          label: '探伤不合格量调整值'
        },
        {
          keyQuery: 'crackCorrectionRate',
          keySave: 'crackCorrectionRate',
          label: '裂纹改判率',
          disabled: true
        },
        {
          keyQuery: 'crackGenerationRate',
          keySave: 'crackGenerationRate',
          label: '裂纹发生率',
          disabled: true
        },
        {
          keyQuery: 'dfdQuantityRate',
          keySave: 'dfdQuantityRate',
          label: '探伤合格率',
          disabled: true
        }
      ],
      flowMonthData: [], // 指标月数据
      flawList: [], // 裂纹详情
      tableList: [], // 裂纹数据
      dialogVisible: false,
      flawList1: [], // 裂纹改判详情
      dialogVisible1: false,
      flawList2: [], // 探伤详情
      tableList2: [], // 探伤数据
      dialogVisible2: false,
      blankUrl: {
        // 坯原钢种一次合格率
        save: qmsPygzFirstpassrateSaveNew,
        list: qmsPygzFirstpassrateNew
      },
      detectionUrl: {
        save: saveInspectionPassRate,
        list: findInspectionPassRateByDate
      },
      detection1: [],
      detectionDay: [
        {
          keyQuery: 'rollingMill',
          keySave: 'rollingMill',
          label: '轧钢厂',
          show: true
        },
        {
          keyQuery: 'dayTargetValue',
          keySave: 'dayTargetValue',
          label: '目标值',
          show: true
        },
        {
          keyQuery: 'dayActualValue',
          keySave: 'dayActualValue',
          label: '实际值',
          show: true
        },
        {
          keyQuery: 'monthTargetValue',
          keySave: 'monthTargetValue',
          label: '目标值',
          show: false
        },
        {
          keyQuery: 'monthActualValue',
          keySave: 'monthActualValue',
          label: '实际值',
          show: false
        },
        {
          keyQuery: 'reasonnotcompl',
          keySave: 'reasonnotcompl',
          label: '未完成原因',
          show: true
        }
      ],
      detectionMonth: [
        {
          keyQuery: 'rollingMill',
          keySave: 'rollingMill',
          label: '轧钢厂',
          show: true
        },
        {
          keyQuery: 'dayTargetValue',
          keySave: 'dayTargetValue',
          label: '目标值',
          show: false
        },
        {
          keyQuery: 'dayActualValue',
          keySave: 'dayActualValue',
          label: '实际值',
          show: false
        },
        {
          keyQuery: 'monthTargetValue',
          keySave: 'monthTargetValue',
          label: '目标值',
          show: true
        },
        {
          keyQuery: 'monthActualValue',
          keySave: 'monthActualValue',
          label: '实际值',
          show: true
        },
        {
          keyQuery: 'reasonnotcompl',
          keySave: 'reasonnotcompl',
          label: '未完成原因',
          show: true
        }
      ],
      detection: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'targetvalue',
          keySave: 'targetValue',
          label: '目标值'
        },
        {
          keyQuery: 'value',
          keySave: 'Value',
          label: '实际值'
        },
        {
          keyQuery: 'monthdata',
          keySave: 'monthData',
          label: '月数据',
          show: false
        },
        {
          keyQuery: 'reasonnotcompl',
          keySave: 'reasonNotCompl',
          label: '未完成原因'
        }
      ],
      processed: {
        bar1: [],
        barAll: [],
        barX1: [],
        barLoading: false,
        dateType1: 0,
        failReason1: '',
        bar11: [],
        failReason11: '',
        bar2: [],
        barX2: [],
        dateType2: 0,
        failReason2: ''
      },
      resultList: {
        Y: '合格',
        N: '不合格'
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.loadData()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.detection1 = this.detectionDay
    this.loadData()
  },
  methods: {
    loadData() {
      this.getPartRate(
        this.$moment(this.cDate).format('yyyyMMDD'),
        this.$moment(this.cDate).format('yyyyMMDD')
      )
      this.rsgfbRateDetails()
    },
    // 成分合格率详情
    showRateDetail() {
      const date = {
        startTime: null,
        endTime: null
      }
      if (this.pieChart.dateType1 === 1) {
        Object.assign(date, this.getResentMonth())
      } else {
        date.startTime = this.$moment(this.cDate).format('yyyyMMDD')
        date.endTime = this.$moment(this.cDate).format('yyyyMMDD')
      }
      post(
        getChemQualifiedDetail +
          `?startTime=${date.startTime}&endTime=${date.endTime}`,
        {}
      ).then(res => {
        this.flawList3 = res
        this.dialogVisible3 = true
      })
    },
    // 成分合格率
    getPartRate(date1, date2) {
      post(getChemQualified + `?startTime=${date1}&endTime=${date2}`, {}).then(
        res => {
          this.pieChart.NUM = res.NUM
          this.pieChart.color = res.PASS_RATE >= 0.96 ? '#19be6b' : '#f32651'
          this.pieChart.RATE = (res.PASS_RATE * 100).toFixed(2)
        }
      )
    },
    // 成分合格率 月度
    changePieChart(type) {
      if (type === 1) {
        const dateObj = this.getResentMonth()
        this.getPartRate(dateObj.startTime, dateObj.endTime)
      } else {
        this.getPartRate(
          this.$moment(this.cDate).format('yyyyMMDD'),
          this.$moment(this.cDate).format('yyyyMMDD')
        )
      }
    },
    // 铸坯一次收得率 月度
    changePieChart2(type) {
      this.rsgfbRateDetails()
      // if (type === 1) {
      //   const dateObj = this.getResentMonth()
      //   this.getPartRate(dateObj.startTime, dateObj.endTime)
      // } else {
      //   this.getPartRate(
      //     this.$moment(this.cDate).format('yyyyMMDD'),
      //     this.$moment(this.cDate).format('yyyyMMDD')
      //   )
      // }
    },
    /* // 成分合格率
     getBlankRate(date1, date2) {
       post(rsgfbRate, {
         startTime: date1,
         endTime: date2
       }).then(res => {
         this.pieChart2.color = res.B1_WGT >= 94.95 ? '#19be6b' : '#f32651'
         this.pieChart2.RATE = res.B1_WGT
       })
     },
     // 成分合格率 月度
     changeBlankPieChart(type) {
       if (type === 1) {
         const dateObj = this.getResentMonth()
         this.getBlankRate(dateObj.startTime, dateObj.endTime)
       } else {
         this.getBlankRate(
           this.$moment(this.cDate).format('yyyyMMDD'),
           this.$moment(this.cDate).format('yyyyMMDD')
         )
       }
     },*/

    getBlank(data) {
      this.pieChart2.dataList = data
      this.changeBlankPieChart(this.pieChart2.dateType1)
    },

    // 铸坯一次收得率 - 新
    async changeBlankPieChart(type) {
      console.log('%c type', 'color: red; font-size: 16px;', type)
      const resData = await post(qmsPygzFirstpassrateNew, {
        setTime: this.$moment(this.cDate).format('yyyy-MM-DD')
      })
      const data = resData ? resData.data : []
      console.log('%c 铸坯一次收得率-新', 'color: red; font-size: 16px;', data)
      if (!data.length) return (this.pieChart2.RATE = 0)
      const value =
        type === 0 ? data[0].dayActualValue : data[0].monthActualValue
      const targetValue =
        type === 0 ? data[0].dayTargetValue : data[0].monthTargetValue
      this.pieChart2.color = value >= targetValue ? '#19be6b' : '#f32651'
      this.pieChart2.RATE = value
      if (type === 0) {
        this.detection1 = this.detectionDay
        this.blankRateList = resData.dayInfoData
      } else {
        this.detection1 = this.detectionMonth
        this.blankRateList = resData.monthInfoData
      }
    },

    // 铸坯一次收得率 - esm
    // changeBlankPieChart($event) {
    //   const data = this.pieChart2.dataList
    //   console.log(data)
    //   if (!data.length) return (this.pieChart2.RATE = 0)
    //   const value = $event === 0 ? data[0].Value : data[0].monthData
    //   this.pieChart2.color =
    //     value >= data[0].targetValue ? '#19be6b' : '#f32651'
    //   this.pieChart2.RATE = value
    // },

    // 坯原钢种一次合格率详情
    showBlankRateDetail() {
      this.dialogVisibleBlankRate = true
    },
    sum(arr) {
      return arr.reduce(function(prev, curr) {
        return prev + curr
      })
    },
    rsgfbRateDetails() {
      const date = {
        setDate: null
      }
      const date2 = {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        dateFlag: '0'
      }
      if (this.pieChartFirst.dateType1 === 1) {
        Object.assign(date, this.getResentMonth2())
        date2.dateFlag = '1'
      } else {
        date.setDate = this.$moment(this.cDate).format('yyyy-MM-DD')
      }
      post(rsgfbRateDetails, date).then(res => {
        //   res.map(item => this.arr2.push(item.CCM_WGT))
        this.arr2[3] =
          this.pieChartFirst.dateType1 === 0
            ? Number(res.data.length > 0 ? res.data[0].zero_wgt : 0)
            : Number(res.data.length > 0 ? res.data[0].mzero_wgt : 0)
        this.arr2[0] =
          this.pieChartFirst.dateType1 === 0
            ? Number(res.data.length > 0 ? res.data[0].one_wgt : 0)
            : Number(res.data.length > 0 ? res.data[0].mone_wgt : 0)
        this.arr2[1] =
          this.pieChartFirst.dateType1 === 0
            ? Number(res.data.length > 0 ? res.data[0].two_wgt : 0)
            : Number(res.data.length > 0 ? res.data[0].mtwo_wgt : 0)
        this.arr2[2] =
          this.pieChartFirst.dateType1 === 0
            ? Number(res.data.length > 0 ? res.data[0].three_wgt : 0)
            : Number(res.data.length > 0 ? res.data[0].mthree_wgt : 0)
        this.arr2[4] =
          this.pieChartFirst.dateType1 === 0
            ? Number(res.data.length > 0 ? res.data[0].all_wgt : 0)
            : Number(res.data.length > 0 ? res.data[0].mall_wgt : 0)
        // this.arr2[4] = (
        //   this.arr2[0] +
        //   this.arr2[1] +
        //   this.arr2[2] +
        //   this.arr2[3]
        // ).toFixed(3)
        this.arr3 = this.arr2
        post(getDataByToday, date2).then(res => {
          this.loadingdetail = false
          let totalProduce = res.firstDialogData[res.firstDialogData.length - 1]
          let allProduce = this.arr2[4]
          let planProduce = (
            Number(totalProduce.lianjiao) +
            Number(totalProduce.touweipi) +
            Number(totalProduce.duijiepi) +
            Number(totalProduce.gongyishiyan) +
            Number(totalProduce.qita)
          ).toFixed(2)
          let noplanProduce = (
            Number(totalProduce.chengfenchao) +
            Number(totalProduce.gongyiyichang) +
            Number(totalProduce.swzlyc) +
            Number(totalProduce.gongyixiumo) +
            Number(totalProduce.sizeNoplan)
          ).toFixed(2)
          let steelPlate = (
            Number(totalProduce.jz_wgt) +
            Number(totalProduce.lw_wgt) +
            Number(totalProduce.jb_wgt) +
            Number(totalProduce.ts_wgt)
          ).toFixed(2)
          // console.log(
          //   'totalProduce',
          //   res.firstDialogData[res.firstDialogData.length - 1]
          // )
          // console.log('planProduce', planProduce)
          // console.log('noplanProduce', noplanProduce)
          this.oneGet.bar1 = [
            {
              name: '一次收得',
              value: (allProduce - planProduce - noplanProduce).toFixed(2) || 0,
              //  percent: ((lessthan013 / total) * 100).toFixed(2)
              percent:
                (
                  ((allProduce - planProduce - noplanProduce - steelPlate) *
                    100) /
                  allProduce
                ).toFixed(2) || 0
            },
            {
              name: '生产计划内',
              value: planProduce,
              percent: ((planProduce * 100) / allProduce).toFixed(2) || 0
            },
            {
              name: '生产计划外',
              value: noplanProduce,
              percent:
                ((noplanProduce * 100 + steelPlate * 100) / allProduce).toFixed(
                  2
                ) || 0
            }
            // {
            //   name: '钢板',
            //   value: steelPlate,
            //   percent: ((steelPlate * 100) / allProduce).toFixed(2)
            // }
          ]
        })
        post(getDataByToday2, date2).then(res => {
          let totalProduce = res.firstDialogData[res.firstDialogData.length - 1]
          let allProduce = this.arr3[4]
          let planProduce = (
            Number(totalProduce.lianjiao) +
            Number(totalProduce.touweipi) +
            Number(totalProduce.duijiepi) +
            Number(totalProduce.tiaokuanpi) +
            Number(totalProduce.xiaohuajiya)
          ).toFixed(2)
          let noplanProduce = (
            Number(totalProduce.gangzhonggp) +
            Number(totalProduce.gongyiyc) +
            Number(totalProduce.swzlyc) +
            Number(totalProduce.chengfenchao)
          ).toFixed(2)
          let steelPlate = (
            Number(totalProduce.jz_wgt) +
            Number(totalProduce.lw_wgt) +
            Number(totalProduce.jb_wgt) +
            Number(totalProduce.ts_wgt)
          ).toFixed(2)
          //  console.log('totalProduce', allProduce)
          console.log('totalProduce', totalProduce)
          console.log('noplanProduce', noplanProduce)
          this.oneGet2.bar1 = [
            {
              name: '铸坯收得',
              value: (allProduce - planProduce - noplanProduce).toFixed(2) || 0,
              //  percent: ((lessthan013 / total) * 100).toFixed(2)
              percent:
                (
                  ((allProduce - planProduce - noplanProduce - steelPlate) *
                    100) /
                  allProduce
                ).toFixed(2) || 0
            },
            {
              name: '生产计划内',
              value: planProduce || 0,
              percent: ((planProduce * 100) / allProduce).toFixed(2) || 0
            },
            {
              name: '生产计划外',
              value: noplanProduce || 0,
              percent:
                ((noplanProduce * 100 + steelPlate * 100) / allProduce).toFixed(
                  2
                ) || 0
            }
            // {
            //   name: '钢板',
            //   value: steelPlate,
            //   percent: ((steelPlate * 100) / allProduce).toFixed(2)
            // }
          ]
        })
      })
      this.loadingdetail = true
    },
    fun(val) {
      return Number(val).toFixed(2)
    },
    // 计划/非计划/一次收得率
    getDataByToday(data) {
      const date = {
        startTime: null,
        endTime: null,
        dateFlag: '0'
      }
      if (this.pieChart2.dateType1 === 1) {
        Object.assign(date, this.getResentMonth())
      } else {
        date.startTime = this.$moment(this.cDate).format('yyyyMMDD')
        date.endTime = this.$moment(this.cDate).format('yyyyMMDD')
      }
      if (this.pieChartFirst.dateType1 === 1) {
        date.dateFlag = '1'
      }
      this.arr = []
      post(getDataByToday, date).then(res => {
        if (data == 1) {
          console.log('arr', this.arr2)

          this.FirstGetList = res.firstDialogData.map((obj, index) => {
            obj.CCM_WGT = this.arr2[index]
            return obj
          })
          //  this.FirstGetList = res.firstDialogData
          this.dialogVisibleFirstget = true
        } else if (data === 2) {
          this.inPlanList = res.planData
          this.dialogVisibleInplan = true
        } else if (data === 3) {
          this.noPlanList = res.noPlanData
          this.dialogVisibleNpPlan = true
        }
      })
      // console.log('aaaa', all)
    },
    // 铸坯收得率
    getDataByToday2(data) {
      const date = {
        startTime: null,
        endTime: null,
        dateFlag: '0'
      }
      if (this.pieChart.dateType1 === 1) {
        Object.assign(date, this.getResentMonth())
      } else {
        date.startTime = this.$moment(this.cDate).format('yyyyMMDD')
        date.endTime = this.$moment(this.cDate).format('yyyyMMDD')
      }
      if (this.pieChartFirst.dateType1 === 1) {
        date.dateFlag = '1'
      }
      this.arr = []
      post(getDataByToday2, date).then(res => {
        if (data == 1) {
          //  console.log('arr', this.arr2)
          //  this.arr2.push(this.sum(this.arr2).toFixed(3))
          this.FirstGetList2 = res.firstDialogData.map((obj, index) => {
            obj.CCM_WGT = this.arr3[index]
            return obj
          })
          //  this.FirstGetList = res.firstDialogData
          this.dialogVisibleFirstget2 = true
        } else if (data === 2) {
          this.inPlanList2 = res.planData
          this.dialogVisibleInplan2 = true
        } else if (data === 3) {
          this.noPlanList2 = res.noPlanData
          this.dialogVisibleNpPlan2 = true
        }
      })
      // console.log('aaaa', all)
    },
    handleHeaderClick(column, event) {
      console.log('label', column)
      if (column.label === '非计划') {
        this.getDataByToday(3)
      } else if (column.label === '计划内') {
        this.getDataByToday(2)
      }
    },
    handleHeaderClick2(column, event) {
      // console.log('label', column)
      // if (column.label === '非计划') {
      //   this.getDataByToday2(3)
      // } else if (column.label === '计划内(改判，协议，判废)') {
      //   this.getDataByToday2(2)
      // }
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '事业部'
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[index] = Number(sums[index]).toFixed(3)
        } else {
        }
      })

      return sums
    },
    // 获取指标月数据
    getMonthData(data) {
      console.log('data', data)

      this.flowMonthData = data
    },
    // 裂纹获取数据
    getPocessed(data) {
      this.tableList = data
      this.changeProcessed(this.processed.dateType1)
    },
    // 匹配指标月数据
    matchMonthData(factory) {
      return this.flowMonthData.find(item => item.rollingMill === factory) || {}
    },
    changeProcessed($event) {
      const data = this.tableList
      Object.assign(this.processed, {
        bar1: data.map(item => {
          const value =
            $event === 0
              ? Number(item.incidenceValue)
              : this.matchMonthData(item.rollingMill).crackGenerationRate
          return {
            value: value,
            plan: Number(item.incidenceTargetValue),
            finished: value < Number(item.incidenceTargetValue)
          }
        }),
        bar11: data.map(item => {
          const value =
            $event === 0
              ? Number(item.changeValue || 0)
              : this.matchMonthData(item.rollingMill).crackCorrectionRate
          return {
            value: value,
            plan: Number(item.changeTargetValue),
            finished: value < Number(item.changeTargetValue)
          }
        }),
        barX1: data.map(item => item.rollingMill)
      })
      this.processed.barX1 = [
        //   'C1发生',
        //   'C1改判',
        //   'C2发生',
        //   'C2改判',
        //   'C3发生',
        //   'C3改判',
        //   '事业部发生',
        //   '事业部改判'
        '中厚板卷厂',
        '宽厚板厂',
        '中板厂',
        '事业部'
      ]
      let barData = this.processed.bar1.concat(this.processed.bar11)
      this.processed.barAll = [
        {
          name: '发生率',
          data: this.processed.bar1.map(item => item.value),
          extra: this.processed.bar1.map(item => item.plan),
          barGap: '0.1',
          finished: this.processed.bar1.map(item => item.finished)
        },
        {
          name: '改判率',
          data: this.processed.bar11.map(item => item.value),
          extra: this.processed.bar11.map(item => item.plan),
          barGap: '0.1',
          finished: this.processed.bar11.map(item => item.finished)
        }
        //   {
        //     name: '发生率',
        //     data: this.processed.bar1.map(item => item.plan),
        //     barGap: '0',
        //     finished: this.processed.bar1.map(item => item.finished)
        //   },
        //   {
        //     name: '改判率',
        //     data: this.processed.bar11.map(item => item.plan),
        //     barGap: '0',
        //     finished: this.processed.bar11.map(item => item.finished)
        //   }
      ]

      // console.log('bar1', barData)

      // this.processed.barAll = [
      //   barData[0],
      //   barData[4],
      //   barData[1],
      //   barData[5],
      //   barData[2],
      //   barData[6],
      //   barData[3],
      //   barData[7]
      // ]
      console.log('barData', this.processed.barAll)
      this.processed.failReason1 =
        $event === 0
          ? data
              .filter(item => item.reasonNotCompl)
              .map(item => item.rollingMill + '：' + item.reasonNotCompl)
              .join('；')
          : ''
      this.processed.failReason11 =
        $event === 0
          ? data
              .filter(item => item.ChangeNotCompl)
              .map(item => item.rollingMill + '：' + item.ChangeNotCompl)
              .join('；')
          : 0
    },

    // 裂纹发生率详情
    getIncidenceRate(data) {
      let plt
      //    const plt = this.processed.barX1[
      //      data.fromActionPayload.dataIndexInside
      //    ].slice(0, 2)
      let barData = JSON.parse(JSON.stringify(data))
      console.log('dat2222222a', data.fromActionPayload.dataIndexInside)
      switch (data.fromActionPayload.dataIndexInside) {
        case 0:
          plt = 'C1'
          break
        case 1:
          plt = 'C2'
          break
        case 2:
          plt = 'C3'
          break
      }
      if (!plt) return
      if (
        data.fromActionPayload.dataIndexInside < 3 &&
        barData.fromActionPayload.seriesIndex == 0
      ) {
        this.loading = true
        this.dialogVisible = true
        post(incidenceRateDetailed, {
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          plt
        }).then(res => {
          this.flawList = res.data
          this.loading = false
        })
      } else if (
        data.fromActionPayload.dataIndexInside < 3 &&
        barData.fromActionPayload.seriesIndex == 1
      ) {
        this.loading = true
        this.dialogVisible1 = true
        post(correctionRateDetailed, {
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          plt
        }).then(res => {
          if (res) {
            this.flawList1 = res.data
            this.loading = false
          }
        })
      } else {
        return null
      }
    },
    getResentMonth() {
      return {
        startTime:
          this.$moment(this.cDate)
            .subtract(
              this.$moment(this.cDate).format('DD') >= 26 ? 0 : 1,
              'month'
            )
            .format('yyyyMM') + '26',
        endTime: this.$moment(this.cDate).format('yyyyMMDD')
      }
    },
    getResentMonth2() {
      return {
        setDate: this.$moment(this.cDate).format('yyyy-MM-DD')
      }
    },
    // 裂纹发生率详情
    getCorrectionRate(data) {
      const plt = this.pltCode[
        this.processed.barX2[data.fromActionPayload.dataIndexInside]
      ]
      if (!plt) return
      post(correctionRateDetailed, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        plt
      }).then(res => {
        this.flawList1 = res.data.rows
        this.dialogVisible1 = true
      })
    },
    // 探伤合格率详情
    getDetectionDetailed(data) {
      this.dialogVisible2 = true
      const plt = this.pltCode[
        this.processed.barX2[data.fromActionPayload.dataIndexInside]
      ]
      if (!plt) return
      this.loading = true
      post(detectionDetailed, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        plt
      })
        .then(res => {
          this.flawList2 = res.data
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },

    //获取钢板号跳转
    getRow(row, num) {
      if (row['产线'] == 'C2') {
        let steelId = row['钢板号']
        let userId = localStorage.getItem('userId')
        let token = localStorage.getItem('token')
        window.open(
          `http://172.25.63.188:9700/homepage?mode=app&mid=${
            num == 1
              ? '512166b9-977e-61e8-39fa-bd74fe0c07db'
              : '2c64683b-e1a9-3556-fbb5-b1c30b7d6ce0'
          }&desktop=qms&org=redirect&userId=` +
            userId +
            '&token=' +
            token +
            '&steelId=' +
            steelId
        )
      }
    },

    // 探伤获取数据
    getPocessed1(data) {
      this.tableList2 = data
      this.changeProcessed1(this.processed.dateType2)
    },
    changeProcessed1($event) {
      const data = this.tableList2
      Object.assign(this.processed, {
        bar2: data.map(item => {
          const value =
            this.processed.dateType2 === 0
              ? Number(item['Value'] || 0)
              : Number(item['monthData'] || 0)
          return {
            value: value,
            plan: Number(item.targetValue),
            finished: value >= Number(item.targetValue)
          }
        }),
        barX2: data.map(item => item.rollingMill)
      })
      this.processed.failReason2 =
        $event === 0
          ? data
              .filter(item => item.reasonNotCompl)
              .map(item => item.rollingMill + '：' + item.reasonNotCompl)
              .join('；')
          : ''
    }
  }
}
</script>

 <style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }

  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  overflow: auto;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}
/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
    position: relative;
    .operate-box {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}
</style>
