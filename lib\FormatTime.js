/*
 * @Author: 
 * @Date: 2022-08-23 14:03:26
 * @LastEditTime: 2022-08-23 14:37:26
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @Description: 
 * @FilePath: \iet-kpi-web\lib\FormatTime.js
 * 可以输入预定的版权声明、个性签名、空行等
 */
/**
 * 时间戳转时间
 * @param {String} timestamp 时间戳
 * @return {Object} 时间
 *
 * 例如：
 * timestampToDate('1484222693'); // Thu Jan 12 2017 20:04:53 GMT+0800 (中国标准时间)
 */
export function timestampToDate(timestamp) {
  let date = new Date()

  date.setTime(timestamp)

  console.debug('时间戳', timestamp)

  return date
}

/**
 * 获取特定格式时间
 * @param {Object} date 时间
 * @param {String} format 格式
 * @return {String} 特定格式的时间
 *
 * 例如：
 * var now = new Date(); // Mon Jan 16 2019 14:32:22 GMT+0800 (中国标准时间)
 * formatDate(now, 'yyyy-MM-dd h:m:s'); // 2019-01-16 14:32:22
 */
export function formatDate(value) {
  let date = new Date(value)
  let y = date.getFullYear()
  let MM = date.getMonth() + 1
  MM = MM < 10 ? '0' + MM : MM
  let d = date.getDate()
  d = d < 10 ? '0' + d : d
  let h = date.getHours()
  h = h < 10 ? '0' + h : h
  let m = date.getMinutes()
  m = m < 10 ? '0' + m : m
  let s = date.getSeconds()
  s = s < 10 ? '0' + s : s
  return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s
}

/**
 * 时间戳转特点格式时间
 * @param {String} timestamp 时间戳
 * @return {String} 特点格式时间
 *
 * 例如：
 * timestampFormatDate('1575957943713', 'yyyy-MM-dd'); // 2019-12-10
 */
export function timestampFormatDate(timestamp, format) {
  let date = timestampToDate(timestamp)
  console.debug('时间函数', date)
  let result = formatDate(date, format)

  return result
}

/**
 * @description: 获取本月的最后一天
 * @param {*} date: 选中得当前日期
 * @return {String} '2020-02-29 23:59:59'
 */
export function lastDayFormat(date) {
  let nowDate = new Date()
  let noeMonth = nowDate.getMonth()
  let lastDate = new Date(date) // 'Wed May 01 2019 00:00:00 GMT+0800 (中国标准时间)' // 2019年5月
  let lastMonth = lastDate.getMonth()

  if (lastMonth < noeMonth) {
    var lastDay = new Date(lastDate.getFullYear(), lastMonth + 1, 0) //是0而不是-1
    let data = formatDate(lastDay, 'yyyy-MM-dd')
    return data
  }

  if (lastMonth === noeMonth) {
    let data = formatDate(nowDate, 'yyyy-MM-dd')
    return data
  }
}
