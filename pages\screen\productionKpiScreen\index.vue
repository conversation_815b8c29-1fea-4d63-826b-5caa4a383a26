<template>
  <div class="screen-wrapper">
    <div class="screen-header">
      <div class="screen-header-inner">
        <div class="header-left header-side">
          <div>
            <screen-switch/>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'small'"
              class="screen-input"
              style="width: 105px; margin-left: 5px"
              @input="$forceUpdate()"/>
          </div>
          <div class="tab-box">
            <span
              v-for="(item, index) in leftList"
              :key="index"
              :class="{active: active === item.value, disabled: item.disabled}"
              class="header-btn"
              @click="active = item.value">{{ item.name }}</span>
          </div>
        </div>
        <div class="header-title">
          <span class="header-arrow"/>
          <span class="header-arrow header-arrow-right"/>
          <img
            src="../../../assets/images/screen/header-bg.png"
            alt=""
            @drag.prevent
          >
          <div class="header-text">板材生产关键指标看板</div>
        </div>
        <div class="header-right header-side">
          <div class="tab-box">
            <span
              v-for="(item, index) in rightList"
              :key="index"
              :class="{active: active === item.value, disabled: item.disabled}"
              class="header-btn"
              @click="active = item.value">{{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="screen-content">
      <!-- <Output
        :select-date="selectDate"
        @dateChange="changeDate"/> -->
      <production
        v-if="active === 1"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <heat-treatment
        v-if="active === 2"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <kpi-index
        v-if="active === 3"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <kpi-indexpage
        v-if="active === 4"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <tripleOrderTrack
        v-if="active === 5"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <thermalAssembly
        v-if="active === 6"
        :select-date="selectDate"
        @dateChange="changeDate"/>
    </div>
  </div>
</template>

<script>
import Output from './output'
import Production from './production'
import HeatTreatment from './heatTreatment'
import KpiIndexpage from './kpiIndexpage'
import KpiIndex from './kpiIndex'
import tripleOrderTrack from './tripleOrderTrack'
import thermalAssembly from './thermalAssembly'
import moment from 'moment'
import ScreenSwitch from '@/pages/screen/morningMeeting/component/screen-switch'
export default {
  components: {
    ScreenSwitch,
    Output,
    HeatTreatment,
    Production,
    KpiIndex,
    KpiIndexpage,
    tripleOrderTrack,
    thermalAssembly
  },
  layout: 'screenLayout',
  data: () => {
    return {
      active: 4,
      cDate: '',
      leftList: [
        {
          name: '三重订单',
          value: 5,
          disabled: false
        }
      ],
      rightList: [
        {
          name: '首页',
          value: 4,
          disabled: false
        },
        {
          name: '产量',
          value: 1,
          disabled: false
        },
        {
          name: '热处理',
          value: 2,
          disabled: false
        },
        {
          name: '指标',
          value: 3,
          disabled: false
        },
        {
          name: '热装分析',
          value: 6,
          disabled: false
        }
      ]
    }
  },
  computed: {
    selectDate: function() {
      return moment(this.cDate || '').format('yyyy-MM-DD')
    }
  },
  created() {
    this.cDate = new Date()
  },
  mounted() {
    if (!window.LAY_EXCEL) {
      const a = require('lay-excel')
    }
  },
  methods: {
    changeDate($event) {
      this.cDate = $event
    },
    changeStatus(status, type) {
      console.log(status, type)
      this.leftList.forEach(item => {
        if (item.value === type) {
          item.disabled = status
        }
      })
      this.rightList.forEach(item => {
        if (item.value === type) {
          item.disabled = status
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.screen-wrapper {
  position: relative;
  display: flex;
  height: 100vh;
  width: 100vw;
  flex-direction: column;
  background: #041a21 url('../../../assets/images/screen/screen-bg.png') repeat
    center;
  &:after {
    content: '';
    position: absolute;
    left: 25px;
    right: 25px;
    height: 4px;
    bottom: 8px;
    background: url('../../../assets/images/screen/footer-line.png') no-repeat;
    background-size: 100% 100%;
  }
  .screen-header {
    height: 71px;
    margin: 0 25px;
    &-inner {
      position: relative;
      display: flex;
      width: 100%;
    }
    .header-side {
      position: relative;
      flex: 1;
      margin: 7px 0;
      white-space: nowrap;
      border-top: 1px solid #136480;
      align-items: center;
      display: flex;
      overflow: hidden;
      .tab-box {
        flex: 1;
        overflow-y: hidden;
        overflow-x: auto;
      }
      &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 1px;
        left: 0;
        bottom: 0;
        background: url(../../../assets/images/screen/header-line.png) repeat
          left;
      }
    }
    .header-left {
      margin-right: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      overflow: hidden;
      &:after {
        content: '';
        position: absolute;
        left: -16px;
        top: 0;
        bottom: 0;
        padding: 0;
        margin: auto;
        display: block;
        width: 12px;
        height: 40px;
        box-sizing: border-box;
        background: #1fc6ff;
        clip-path: polygon(
          5px 0,
          calc(100%) 0,
          calc(100%) 1px,
          5px 1px,
          1px 5px,
          1px calc(100% - 5px),
          5px calc(100% - 1px),
          100% calc(100% - 1px),
          100% 100%,
          calc(100% - 5px) 100%,
          5px 100%,
          0 calc(100% - 5px),
          0 5px
        );
      }
      .tab-box {
        flex: 1;
        text-align: right;
        overflow-y: hidden;
        overflow-x: auto;
      }
    }
    .header-right {
      margin-left: 8px;
      text-align: right;
      &:after {
        content: '';
        position: absolute;
        right: -16px;
        top: 0;
        bottom: 0;
        padding: 0;
        margin: auto;
        display: block;
        width: 12px;
        height: 40px;
        box-sizing: border-box;
        background: #1fc6ff;
        transform: rotate(180deg);
        clip-path: polygon(
          5px 0,
          calc(100%) 0,
          calc(100%) 1px,
          5px 1px,
          1px 5px,
          1px calc(100% - 5px),
          5px calc(100% - 1px),
          100% calc(100% - 1px),
          100% 100%,
          calc(100% - 5px) 100%,
          5px 100%,
          0 calc(100% - 5px),
          0 5px
        );
      }
      &:before {
        background: url(../../../assets/images/screen/header-line2.png) repeat
          right;
      }
    }
    .header-btn {
      position: relative;
      display: inline-block;
      height: 35px;
      margin: 0 5px;
      padding: 0 8px;
      min-width: 60px;
      background: rgba(31, 198, 255, 0.12);
      border: 1px solid rgba(31, 198, 255, 0.2);
      font-weight: 400;
      font-size: 15px;
      line-height: 35px;
      text-align: center;
      color: #1fc6ff;
      cursor: pointer;
      &.active {
        color: #fff;
        font-weight: bold;
        background: rgba(31, 198, 255, 0.3);
      }
      &.disabled {
        color: #999;
        font-weight: bold;
        background: rgba(70, 77, 79, 0.2);
      }
      &:after {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        border: 1px solid rgba(31, 198, 255, 0.2);
      }
    }
    .header-title {
      position: relative;
      height: 71px;
      padding: 3px 0;
      img {
        display: block;
        height: 100%;
        margin: auto;
        user-select: none;
        -webkit-user-drag: none;
      }
      .header-text {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        padding-left: 65px;
        background: url(../../../assets/images/screen/header-logo.png) no-repeat
          left;
        background-size: 50px;
        height: 71px;
        line-height: 71px;
        font-size: 30px;
        color: #fff;
        font-weight: bold;
        letter-spacing: 2px;
        white-space: nowrap;
      }
    }
  }
  .screen-content {
    flex: 1;
    margin: 15px 25px 32px;
    position: relative;
    overflow: hidden;
  }
  .header-arrow {
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 80px;
    height: 20px;
    background-image: linear-gradient(100grad, #1ec5fe, #106685);
    animation: move-arrows 1s linear infinite;
    transform-origin: center;
    mask-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAUCAYAAAAa2LrXAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAH8SURBVHgB7ZhLSgNBEIb/6uBbURcKgo/ZuHChCLoxopgjeAO9gd4gnkBvoDfwCBFFoxDxBSq4iUFwIfhANCYhKXvEQMg0JulpSC38FiGpRfFRNVXpHsCAd8jTXpITXoL74ID+FPeOnPDCTIpb4ABJfgoGOQYSzFgqtWITIfHlerJYVAUMPGUxhZBI81OGyCYIP50lwsroIW8jBN1fmNJ5Wn7zjY0e8AzCIMwvWMAvLOvPdPlnWMnBdhwT47MiX7giCvMjU1DvFo/bkPC/lmN6ZHYy87QKC4ZS3Nmmx4QJnRX57jMLdAoLJPkpUzAdozTlEENVp70jtto5j7P0mevAfnWnh494EhZI8lNoQFIv7zX9eMfhSDJSwvhYkidggRQ/Qg2M41LEhn6847DANC6I4OZ+jm5gQbP9ahZQgqRkv7oK2GxJyX6q7ozthpjy14478hwiX5P86hvhpO4uB44NcX1s2IAFpu7mFa4fo3QLC5rpV/tP5L94f/qRZLlaSPCjhuSArUyU1uFIrqhw9xClK1ggxY/qlpN0lRPkF6kOeGfcxwUkXcn579jyWcScFU+YX+AYw1nsupLzef7AnKviSfQLFFDfBdf1Mnl1Iefz2oULnadgI2dCml+ggOl5OteL0b+k76k8rBZyJS+z9PauL+kl4GmgA5cIiTS/b5T7QUH8z5WLAAAAAElFTkSuQmCC');
    mask-size: contain;
  }
  .header-arrow-right {
    left: auto;
    right: 10px;
    transform: rotate(180deg);
  }
  @keyframes move-arrows {
    0% {
      mask-position: 0 0;
    }
    100% {
      mask-position: 40px 0;
    }
  }
}
/deep/ * {
  *::-webkit-scrollbar {
    width: 10px; /*对垂直流动条有效*/
    height: 10px; /*对水平流动条有效*/
    margin-left: 5px;
  }
  /*定义滑块颜色、内阴影及圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 7px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.15);
    background: #1fc6ff;
    opacity: 0.5;
  }
}
/deep/ .screen-dialog {
  .el-dialog {
    position: relative;
    border: 1px solid #0c4d63;
    background: #041a21;
    box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.12),
      0px 4px 8px rgba(0, 0, 0, 0.08), 0px 4px 16px 4px rgba(0, 0, 0, 0.04);
    &:after {
      content: '';
      position: absolute;
      top: -6px;
      left: 0;
      width: 59px;
      height: 13px;
      background: url(../../../assets/images/screen/dialog-top.png) no-repeat
        center;
    }
    .el-dialog__headerbtn {
      top: -19px;
      right: -19px;
      width: 36px;
      height: 36px;
      background: #0b3f67;
      text-align: center;
      line-height: 20px;
      /* 亮色 */
      border: 1px solid #1fc6ff;
      box-shadow: inset 0px 0px 10px #0e9cff;
      border-radius: 6px;
      transform: rotate(-45deg);
      .el-icon {
        transform: rotate(-45deg);
        font-size: 26px;
        color: #fff;
      }
      &:hover {
        box-shadow: inset 0px 0px 15px #54e1fa;
      }
    }
    .el-dialog__header {
      padding: 24px;
    }
    .custom-dialog-title {
      position: relative;
      padding-left: 66px;
      padding-right: 10px;
      height: 44px;
      font-style: normal;
      font-weight: 1000;
      font-size: 22px;
      line-height: 44px;
      color: #1fc6ff;
      background: #0f2b3f url(../../../assets/images/screen/dialog-title-bg.png)
        no-repeat left bottom;
      .btn-box {
        float: right;
        line-height: 40px;
        white-space: nowrap;
        color: #fff;
      }
    }
    .el-dialog__body {
      padding-top: 0;
    }
  }
  .el-upload:focus {
    color: #fff;
  }
  .input-green .el-input__inner {
    background: rgba(31, 255, 154, 0.3);
  }
  .el-textarea__inner,
  .el-input__inner {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
  }
  .el-select-dropdown {
    color: #fff;
    background: rgba(9, 60, 77);
    border-color: rgba(31, 198, 255, 0.6);
  }
  .el-select-dropdown__item {
    color: #fff;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background: rgba(4, 26, 33);
  }
  .el-popper[x-placement^='bottom'] .popper__arrow:after {
    border-bottom-color: rgba(9, 60, 77);
  }
  .el-divider__text {
    background-color: #041a21;
    color: #1fc6ff;
  }
  .el-divider {
    background-color: #58627a;
  }
  .el-form-item__label {
    color: #e0f9fc;
  }
}
/deep/ .el-table {
  .table-total td.el-table__cell {
    background: rgba(31, 198, 255, 0.2);
  }
}
/deep/ .el-table {
  background: #041a21;
  color: #def0ff;
  &.font-table {
    font-size: 16px;
    td.big-font {
      font-size: 20px;
      .cell {
        line-height: 1.3;
      }
    }
  }
  &.center-table .el-table__cell {
    text-align: center;
  }
  &.center-table td.is-left {
    text-align: left;
  }
  .red-row {
    color: #ff0000;
  }
  tr {
    background: transparent;
  }
  .el-table__fixed,
  .el-table__fixed-right {
    tr {
      background: #041a21;
    }
  }
  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background: transparent;
  }
  th.el-table__cell {
    background: transparent;
  }
  &.el-table--border th.el-table__cell,
  td.el-table__cell,
  th.el-table__cell.is-leaf {
    border-bottom: 4px solid #081f27;
  }
  &.el-table--border .el-table__cell {
    border-right: 4px solid #081f27;
  }
  thead.is-group th.el-table__cell,
  .el-table__footer-wrapper tbody td.el-table__cell,
  .el-table__header-wrapper tbody td.el-table__cell,
  th.el-table__cell {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-top: none;
  }
  &.el-table--border {
    border-width: 4px;
    border-color: #081f27;
  }
  &:before {
    background: transparent;
  }
  &:after {
    background: transparent;
  }
  &.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell,
  .el-table__body tr.hover-row > td.el-table__cell {
    background: rgba(31, 198, 255, 0.1);
  }
  .el-table__fixed-right-patch {
    background: #041a21;
    border: none;
  }
  .el-table__fixed,
  .el-table__fixed-right {
    box-shadow: 0 0 14px rgba(127, 191, 255, 0.15);
    margin-right: -7px;
  }
  .el-loading-mask {
    background: rgba(175, 223, 239, 0.6);
  }
  .el-table__empty-text {
    color: #fff6ee;
  }
  /*全局滚动条样式 chrome内核*/
  /*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/

  *::-webkit-scrollbar {
    width: 6px; /*对垂直流动条有效*/
    height: 6px; /*对水平流动条有效*/
  }

  /*定义滚动条的轨道颜色、内阴影及圆角*/
  *::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.15);
    border-radius: 3px;
  }

  /*定义滑块颜色、内阴影及圆角*/
  *::-webkit-scrollbar-thumb {
    border-radius: 7px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.15);
    background: #1fc6ff;
    opacity: 0.5;
  }

  /*定义两端按钮的样式*/
  *::-webkit-scrollbar-button {
    display: none;
  }

  /*定义右下角汇合处的样式*/
  *::-webkit-scrollbar-corner {
    display: none;
  }
}
/deep/ .screen-input {
  .el-textarea__inner,
  .el-input__inner {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
    padding-right: 5px;
  }
  .el-input__prefix {
    color: #fff;
  }
}
/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}
/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
</style>
