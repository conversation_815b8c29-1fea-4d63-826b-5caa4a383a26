<template>
  <div class="container">
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="综合非计划率">
          <template v-slot:headerRight>
            <span 
              v-command="'/screen/qualityDailyReportScreen/edit'"
              class="screen-btn"
              @click="handleDialogOperationVisible('comprehensive')"
            >
              <el-icon class="el-icon-edit-outline" />
              操作
            </span>
          </template>
          <line-chart 
            :chart-data="lineChartData.chartData" 
            :chart-data2="lineChartData.chartData2"
            :chart-data3="lineChartData.chartData3" 
            :x-data="lineChartData.xData" 
            :show-legend="true"
            :height="200" 
            :show-label="true"
            :bar-width="20" 
            unit="%" />
          <template v-slot:bottom>
            <div class="chart-footer">
              <el-input 
                v-model="processAppealDesc" 
                :rows="2" 
                type="textarea" 
                class="chart-input" 
                resize="none" />
            </div>
          </template>
        </screen-border>
      </div>

      <div class="chart-box">
        <screen-border title="原钢种一次合格率">
          <template v-slot:headerRight>
            <span 
              v-command="'/screen/qualityDailyReportScreen/edit'"
              class="screen-btn"
              @click="handleDialogOperationVisible('original')"
            >
              <el-icon class="el-icon-edit-outline" />
              操作
            </span>
          </template>
          <line-chart 
            :chart-data="lineChartDataOriginal.chartData"
            :chart-data2="lineChartDataOriginal.chartData2"
            :chart-data3="lineChartDataOriginal.chartData3"
            :x-data="lineChartDataOriginal.xData"
            :show-legend="true"
            :height="200" 
            :show-label="true"
            :bar-width="20" 
            unit="%" />
          <template v-slot:bottom>
            <div class="chart-footer">
              <el-input 
                v-model="processAppealDesc" 
                :rows="2" 
                type="textarea" 
                class="chart-input" 
                resize="none" />
            </div>
          </template>
        </screen-border>
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="单周废品分析">
          <template v-slot:headerRight>
            <span 
              v-command="'/screen/qualityDailyReportScreen/edit'"
              class="screen-btn"
              @click="handleDialogOperationVisible('single')"
            >
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <bars-chart
            :chart-data="erpAppealData"
            :x-data="erpAppealXData"
            :show-label="true"
            :show-legend="false"
            :bar-width="20"
            :height="200"
            unit="吨"/>
          <template v-slot:bottom>
            <div class="chart-footer">
              <el-input
                v-model="processAppealDesc"
                :rows="2"
                type="textarea"
                class="chart-input"
                resize="none"
              />
            </div>
          </template>
        </screen-border>
      </div>

      <div class="chart-box">
        <screen-border title="周退判统计">
          <template v-slot:headerRight>
            <span 
              v-command="'/screen/qualityDailyReportScreen/edit'"
              class="screen-btn"
              @click="handleDialogOperationVisible('judge')"
            >
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <custom-table
            ref="judgeRef" 
            :show-table="true"
            :show-edit="false"
            :key="'judge'"
            :title="'周退判统计'"
            :setting="tableObjFour.setting"
            :url-list="tableObjFour.url.list"
            :url-save="tableObjFour.url.save"
            :select-date="selectDate"
            @close="tableVisible.judge = false"
          />
        </screen-border>
      </div>
    </div>
    <custom-table
      v-if="tableVisible.comprehensive"
      ref="comprehensiveRef"
      :show-table="false"
      :key="'comprehensive'"
      :title="'综合非计划率'"
      :setting="tableObj.setting"
      :url-list="tableObj.url.list"
      :url-save="tableObj.url.save"
      :select-date="selectDate"
      @close="tableVisible.comprehensive = false"
      @save-success="fetchAllData"
    />
    <custom-table
      v-if="tableVisible.original"
      ref="originalRef"
      :show-table="false"
      :key="'original'"
      :title="'原钢种一次合格率'"
      :setting="tableObjTwo.setting"
      :url-list="tableObjTwo.url.list"
      :url-save="tableObjTwo.url.save"
      :select-date="selectDate"
      @close="tableVisible.original = false"
      @save-success="fetchAllData"
    />
    <custom-table
      v-if="tableVisible.single"
      ref="singleRef"
      :show-table="false"
      :key="'single'"
      :title="'单周废品分析'"
      :setting="tableObjThree.setting"
      :url-list="tableObjThree.url.list"
      :url-save="tableObjThree.url.save"
      :select-date="selectDate"
      @close="tableVisible.single = false"
      @save-success="fetchAllData"
    />
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityDailyReportScreen/components/screen-border.vue'
import BarsChart from '@/pages/screen/qualityDailyReportScreen/components/bars-chart.vue'
import PieChart from '@/pages/screen/qualityDailyReportScreen/components/pie-chart.vue'
import LineChart from '@/pages/screen/qualityDailyReportScreen/components/line-chart.vue'
import pieImg from '@/assets/images/screen/quality-daily-report-screen-pie.png'
import productionImg from '@/assets/images/screen/quality-daily-report-screen-production.png'
import rightImg from '@/assets/images/screen/quality-daily-report-screen-right.png'
import CustomTable from '@/pages/screen/qualityDailyReportScreen/components/custom-table.vue'

import {
  comprehensiveNonPlan,
  originalFirstPassRate,
  singleWeekNonPlan,
  weekJudgeNonPlan,
  comprehensiveNonPlanSave,
  originalFirstPassRateSave,
  singleWeekNonPlanSave,
  weekJudgeNonPlanSave
} from '@/api/screen'
import { post } from '@/lib/Util'

export default {
  name: 'WeeklyReport',
  components: {
    ScreenBorder,
    LineChart,
    PieChart,
    BarsChart,
    CustomTable
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableVisible: {
        comprehensive: false,
        original: false,
        single: false,
        judge: false
      },
      // 字段映射关系表
      fieldMap: {
        jztwmmhs: '卷轧头尾麻面划伤',
        abnormalThickness: '厚度异常',
        buckles: '瓢曲',
        thicknessDefect: '厚度1/2点状缺陷',
        processAttached: '工艺附带',
        rollingDamage: '轧损'
        // remark: '备注'
      },
      tableObj: {
        url: {
          list: comprehensiveNonPlan,
          save: comprehensiveNonPlanSave
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'setTime',
            keySave: 'setTime',
            label: '日期（格式：yyyy-MM-dd）',
            width: '200'
          },
          {
            keyQuery: 'bestInHistoryValue',
            keySave: 'bestInHistoryValue',
            label: '历史最好',
            width: '300'
          },
          {
            keyQuery: 'targetValue',
            keySave: 'targetValue',
            label: '计划目标',
            width: '300'
          },
          {
            keyQuery: 'actualValue',
            keySave: 'actualValue',
            label: '实际完成',
            width: '300'
          },
          {
            keyQuery: 'remark',
            keySave: 'remark',
            label: '备注',
            width: '400'
          }
        ]
      },
      tableObjTwo: {
        url: {
          list: originalFirstPassRate,
          save: originalFirstPassRateSave
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'setTime',
            keySave: 'setTime',
            label: '日期（格式：yyyy-MM-dd）',
            width: '200'
          },
          {
            keyQuery: 'bestInHistoryValue',
            keySave: 'bestInHistoryValue',
            label: '历史最好',
            width: '300'
          },
          {
            keyQuery: 'targetValue',
            keySave: 'targetValue',
            label: '计划目标',
            width: '300'
          },
          {
            keyQuery: 'actualValue',
            keySave: 'actualValue',
            label: '实际完成',
            width: '300'
          },
          {
            keyQuery: 'remark',
            keySave: 'remark',
            label: '备注',
            width: '400'
          }
        ]
      },
      tableObjThree: {
        url: {
          list: singleWeekNonPlan,
          save: singleWeekNonPlanSave
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'jztwmmhs',
            keySave: 'jztwmmhs',
            label: '卷轧头尾麻面划伤',
            width: '200'
          },
          {
            keyQuery: 'abnormalThickness',
            keySave: 'abnormalThickness',
            label: '厚度异常',
            width: '200'
          },
          {
            keyQuery: 'buckles',
            keySave: 'buckles',
            label: '瓤曲',
            width: '200'
          },
          {
            keyQuery: 'thicknessDefect',
            keySave: 'thicknessDefect',
            label: '厚度1/2点状缺陷',
            width: '200'
          },
          {
            keyQuery: 'processAttached',
            keySave: 'processAttached',
            label: '工艺附带',
            width: '200'
          },
          {
            keyQuery: 'rollingDamage',
            keySave: 'rollingDamage',
            label: '轧损',
            width: '200'
          },
          {
            keyQuery: 'remark',
            keySave: 'remark',
            label: '备注',
            width: '200'
          }
        ]
      },
      tableObjFour: {
        url: {
          list: weekJudgeNonPlan,
          save: weekJudgeNonPlanSave
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'mainSteelGrades',
            keySave: 'mainSteelGrades',
            label: '主要钢种',
            width: '200'
          },
          {
            keyQuery: 'businessUnit',
            keySave: 'businessUnit',
            label: '责任单位',
            width: '200'
          },
          {
            keyQuery: 'reason',
            keySave: 'reason',
            label: '判退原因',
            width: '200'
          },
          {
            keyQuery: 'numberOfBlocks',
            keySave: 'numberOfBlocks',
            label: '判退块数',
            width: '200'
          },
          {
            keyQuery: 'tonnage',
            keySave: 'tonnage',
            label: '判退吨位',
            width: '200'
          },
          {
            keyQuery: 'remark',
            keySave: 'remark',
            label: '备注',
            width: '300'
          }
        ]
      },
      rawMaterialPlanData: [
        // 示例数据
      ],
      lineChartData: {
        chartData: [],
        chartData2: [],
        chartData3: [],
        xData: []
      },
      textImageData: [],
      economicIndicatorsData: [],
      judgmentData: [
        // 示例数据
      ],
      pieChartData: [],
      processAppealDesc: '',
      processAppealDesc02: '',
      erpAppealData: [],
      erpAppealXData: [],
      erpAppealDesc: '',
      lineChartDataOriginal: {
        chartData: [{ name: '计划目标', data: [] }],
        chartData2: [{ name: '实际完成', data: [] }],
        chartData3: [{ name: '历史最好', data: [] }],
        xData: []
      }
    }
  },
  watch: {
    selectDate: {
      handler() {
        this.fetchAllData()
      },
      immediate: true
    }
  },
  created() {
    this.fetchAllData()
  },
  methods: {
    fetchAllData(type = '') {
      console.log('%c type', 'color: red', type)
      if (type === '综合非计划率') {
        this.fetchComprehensiveData()
      } else if (type === '原钢种一次合格率') {
        this.fetchOriginalData()
      } else if (type === '单周废品分析') {
        this.fetchSingleData()
      } else if (type === '周退判统计') {
        this.fetchJudgeData()
      } else {
        this.fetchComprehensiveData()
        this.fetchOriginalData()
        this.fetchSingleData()
      }
    },
    // 独立请求方法 - 综合非计划率情况
    async fetchComprehensiveData() {
      try {
        const res = await post(comprehensiveNonPlan, {
          setTime: this.selectDate
        })

        // 新增数据处理逻辑
        const rawData = res.data || []
        this.processLineChartData(rawData, 'comprehensive')
      } catch (error) {
        console.error('综合非计划率情况:', error)
      }
    },
    // 独立请求方法 - 原钢种一次合格率情况
    async fetchOriginalData() {
      try {
        const res = await post(originalFirstPassRate, {
          setTime: this.selectDate
        })

        // 新增数据处理逻辑
        const rawData = res.data || []
        this.processLineChartData(rawData, 'original')
      } catch (error) {
        console.error('原钢种一次合格率情况:', error)
      }
    },
    // 新增公共数据处理方法
    processLineChartData(rawData, type) {
      // 按时间排序
      const sortedData = [...rawData].sort(
        (a, b) => new Date(a.setTime) - new Date(b.setTime)
      )

      // 提取数据系列
      const targetValues = []
      const actualValues = []
      const bestValues = []
      const xData = []

      sortedData.forEach(item => {
        xData.push(item.setTime)
        targetValues.push(Number(item.targetValue) || 0)
        actualValues.push(Number(item.actualValue) || 0)
        bestValues.push(Number(item.bestInHistoryValue) || 0)
      })

      // 更新对应图表的数据
      if (type === 'comprehensive') {
        this.lineChartData = {
          chartData: [{ name: '计划目标', data: targetValues }],
          chartData2: [{ name: '实际完成', data: actualValues }],
          chartData3: [{ name: '历史最好', data: bestValues }],
          xData: xData
        }
      } else if (type === 'original') {
        // 如果需要单独维护原钢种的数据，可以创建新的数据对象如lineChartDataOriginal
        this.lineChartDataOriginal = {
          chartData: [{ name: '计划目标', data: targetValues }],
          chartData2: [{ name: '实际完成', data: actualValues }],
          chartData3: [{ name: '历史最好', data: bestValues }],
          xData: xData
        }
      }
    },
    // 独立请求方法 - 单周废品分析情况
    async fetchSingleData() {
      try {
        const res = await post(singleWeekNonPlan, {
          setTime: this.selectDate
        })
        const resData = res.data.length > 0 ? res.data[0] : {} // 取第一条数据
        const chartData = Object.keys(this.fieldMap).map(key => ({
          name: this.fieldMap[key],
          value: resData[key] || '' // 处理空值情况
        }))
        // erpAppealData、erpAppealXData
        // 更新图表数据
        this.erpAppealData = [
          {
            // 使用全新对象
            name: '单周废品分析',
            data: [...chartData.map(item => item.value)]
          }
        ]
        this.erpAppealXData = [...chartData.map(item => item.name)]
      } catch (error) {
        console.error('单周废品分析情况:', error)
        return null
      }
    },
    //操作表单弹窗
    handleDialogOperationVisible(type) {
      console.log('%c type', 'color', type)
      // 统一控制弹窗显示状态
      this.tableVisible = {
        comprehensive: false,
        original: false,
        single: false,
        judge: false
      }
      this.$set(this.tableVisible, type, true)

      this.$nextTick(() => {
        // 根据类型映射对应的ref名称
        const refMap = {
          comprehensive: 'comprehensiveRef',
          original: 'originalRef',
          single: 'singleRef',
          judge: 'judgeRef'
        }
        const refName = refMap[type]
        this.$refs[refName] && this.$refs[refName].openDialog()
      })
    }
  }
}
</script>

<style scoped lang="less">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: hidden;

  .chart-row {
    margin-bottom: 10px;
  }

  .chart-row,
  .table-row {
    display: flex;
    gap: 10px;
    height: 50%;
    width: 100%;
  }

  .chart-box,
  .table-box {
    flex: 1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .chart-footer {
    margin: 0px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    padding: 5px 8px;
    background: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);
    width: 100%;
  }

  .chart-input {
    height: 100%;
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      height: 100%;
      padding: 0;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
        border-color: transparent !important;
      }
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      border-color: transparent !important;
    }
  }

  /deep/ .el-table {
    background-color: transparent;
    width: 100% !important;
    table-layout: fixed;

    &::before {
      display: none;
    }

    th {
      background-color: rgba(31, 198, 255, 0.3);
      color: #fff;
      border-color: #1fc6ff;
    }

    td {
      background-color: transparent;
      color: #fff;
      border-color: #2e4262;
    }

    tr {
      background-color: transparent;
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-content {
    padding: 10px;
    box-sizing: border-box;
  }
}
</style>
