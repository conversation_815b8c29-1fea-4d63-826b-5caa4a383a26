<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <screen-border title="炼钢现货非计划">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="varietyQuality.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div
              ref="table1"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="varietyQuality.showGridData"
                :max-height="varietyQuality.maxHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  label="时间">
                  <el-table-column
                    property="A_LIST"
                    label="项目"/>
                  <el-table-column
                    property="B_LIST"
                    label="轧钢厂"/>
                </el-table-column>
                <el-table-column
                  :label="timeDate">
                  <el-table-column
                    property="C_C1"
                    label="C1">
                    <template v-slot="{ row }">
                      {{ percentage(row, "C_C1") }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    property="C_C2"
                    label="C2">
                    <template v-slot="{ row }">
                      {{ percentage(row, "C_C2") }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    property="C_C3"
                    label="C3">
                    <template v-slot="{ row }">
                      {{ percentage(row, "C_C3") }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    property="C_综合"
                    label="综合">
                    <template v-slot="{ row }">
                      {{ percentage(row, "C_综合") }}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column
                  :label="timeDate2 + '累计'">
                  <el-table-column
                    property="D_C1"
                    label="C1">
                    <template v-slot="{ row }">
                      {{ percentage(row, "D_C1") }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    property="D_C2"
                    label="C2">
                    <template v-slot="{ row }">
                      {{ percentage(row, "D_C2") }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    property="D_C3"
                    label="C3">
                    <template v-slot="{ row }">
                      {{ percentage(row, "D_C3") }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    property="D_综合"
                    label="综合">
                    <template v-slot="{ row }">
                      {{ percentage(row, "D_综合") }}
                    </template>
                  </el-table-column>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <screen-border title="炼钢原始非计划">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="()=>{
                  variety2.save = false;
                  variety2.dialogVisible = true
                  getvariety2()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明录入
              </span>
              <span
                class="screen-btn"
                @click="()=>{
                  variety2.save = true;
                  variety2.dialogVisible = true
                  getvariety2()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明
              </span>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="varietyQuality2.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="varietyQuality2.showGridData"
                :max-height="varietyQuality.maxHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  label="时间">
                  <el-table-column
                    property="A_LIST"
                    label="项目"/>
                  <el-table-column
                    property="B_LIST"
                    label="轧钢厂"/>
                </el-table-column>
                <el-table-column
                  :label="timeDate">
                  <el-table-column
                    property="C_C1"
                    label="C1">
                    <template v-slot="{ row }">
                      {{ percentage(row, "C_C1") }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    property="C_C2"
                    label="C2">
                    <template v-slot="{ row }">
                      {{ percentage(row, "C_C2") }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    property="C_C3"
                    label="C3">
                    <template v-slot="{ row }">
                      {{ percentage(row, "C_C3") }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    property="C_综合"
                    label="综合">
                    <template v-slot="{ row }">
                      {{ percentage(row, "C_综合") }}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column
                  :label="timeDate2 + '累计'">
                  <el-table-column
                    property="D_C1"
                    label="C1">
                    <template v-slot="{ row }">
                      {{ percentage(row, "D_C1") }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    property="D_C2"
                    label="C2">
                    <template v-slot="{ row }">
                      {{ percentage(row, "D_C2") }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    property="D_C3"
                    label="C3">
                    <template v-slot="{ row }">
                      {{ percentage(row, "D_C3") }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    property="D_综合"
                    label="综合">
                    <template v-slot="{ row }">
                      {{ percentage(row, "D_综合") }}
                    </template>
                  </el-table-column>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="10"
          class="full-height">
          <screen-border title="协议坯统计分析报表">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="protocolBlankReportForms.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="protocolBlankReportForms.showGridData"
                :max-height="varietyQuality.maxHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  property="A_LIST"
                  label="协议名称"/>
                <el-table-column
                  property="B_LIST"
                  label="内容"/>
                <el-table-column
                  property="C_LIST"
                  label="责任单位"/>
                <el-table-column
                  property="D_LIST"
                  label="重量（t）"/>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="14"
          class="full-height">
          <screen-border title="协议坯统计情况">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="protocolBlank.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="protocolBlank.showGridData"
                :max-height="varietyQuality.maxHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  property="A_LIST"
                  label="板坯号"/>
                <el-table-column
                  property="B_LIST"
                  label="钢种描述"/>
                <el-table-column
                  property="k1"
                  label="厚度"/>
                <el-table-column
                  property="k2"
                  label="宽度"/>
                <el-table-column
                  property="k3"
                  label="长度"/>
                <el-table-column
                  property="k4"
                  label="重量"/>
                <el-table-column
                  property="D_LIST"
                  label="原因详细"/>
                <el-table-column
                  property="E_LIST"
                  label="处理名称"/>
                <el-table-column
                  property="F_LIST"
                  label="解锁日期"/>
                <el-table-column
                  property="G_LIST"
                  label="处理人"/>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible.sync="varietyQuality.dialogVisible"
      :width="'1500px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="炼钢现货非计划">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('varietyQuality')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importUnfinishedData')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportunfinished">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveVarietyQuality">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          炼钢现货非计划
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="varietyQuality.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            label="时间">
            <el-table-column
              property="A_LIST"
              label="项目">
              <template v-slot="{ row }">
                <el-input v-model="row.A_LIST"/>
              </template>
            </el-table-column>
            <el-table-column
              property="B_LIST"
              label="轧钢厂">
              <template v-slot="{ row }">
                <el-input v-model="row.B_LIST"/>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            :label="timeDate">
            <el-table-column
              property="C_C1"
              label="C1">
              <template v-slot="{ row }">
                <el-input v-model="row.C_C1"/>
              </template>
            </el-table-column>
            <el-table-column
              property="C_C2"
              label="C2">
              <template v-slot="{ row }">
                <el-input v-model="row.C_C2"/>
              </template>
            </el-table-column>
            <el-table-column
              property="C_C3"
              label="C3">
              <template v-slot="{ row }">
                <el-input v-model="row.C_C3"/>
              </template>
            </el-table-column>
            <el-table-column
              property="C_综合"
              label="综合">
              <template v-slot="{ row }">
                <el-input v-model="row.C_综合"/>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            :label="timeDate2 + '累计'">
            <el-table-column
              property="D_C1"
              label="C1">
              <template v-slot="{ row }">
                <el-input v-model="row.D_C1"/>
              </template>
            </el-table-column>
            <el-table-column
              property="D_C2"
              label="C2">
              <template v-slot="{ row }">
                <el-input v-model="row.D_C2"/>
              </template>
            </el-table-column>
            <el-table-column
              property="D_C3"
              label="C3">
              <template v-slot="{ row }">
                <el-input v-model="row.D_C3"/>
              </template>
            </el-table-column>
            <el-table-column
              property="D_综合"
              label="综合">
              <template v-slot="{ row }">
                <el-input v-model="row.D_综合"/>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'varietyQuality')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('varietyQuality')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="varietyQuality2.dialogVisible"
      :width="'1500px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="炼钢原始非计划">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('varietyQuality2')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importUnfinishedData2')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview2"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportunfinished2">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveVarietyQuality2">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          炼钢原始非计划
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="varietyQuality2.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            label="时间">
            <el-table-column
              property="A_LIST"
              label="项目">
              <template v-slot="{ row }">
                <el-input v-model="row.A_LIST"/>
              </template>
            </el-table-column>
            <el-table-column
              property="B_LIST"
              label="轧钢厂">
              <template v-slot="{ row }">
                <el-input v-model="row.B_LIST"/>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            :label="timeDate">
            <el-table-column
              property="C_C1"
              label="C1">
              <template v-slot="{ row }">
                <el-input v-model="row.C_C1"/>
              </template>
            </el-table-column>
            <el-table-column
              property="C_C2"
              label="C2">
              <template v-slot="{ row }">
                <el-input v-model="row.C_C2"/>
              </template>
            </el-table-column>
            <el-table-column
              property="C_C3"
              label="C3">
              <template v-slot="{ row }">
                <el-input v-model="row.C_C3"/>
              </template>
            </el-table-column>
            <el-table-column
              property="C_综合"
              label="综合">
              <template v-slot="{ row }">
                <el-input v-model="row.C_综合"/>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            :label="timeDate2 + '累计'">
            <el-table-column
              property="D_C1"
              label="C1">
              <template v-slot="{ row }">
                <el-input v-model="row.D_C1"/>
              </template>
            </el-table-column>
            <el-table-column
              property="D_C2"
              label="C2">
              <template v-slot="{ row }">
                <el-input v-model="row.D_C2"/>
              </template>
            </el-table-column>
            <el-table-column
              property="D_C3"
              label="C3">
              <template v-slot="{ row }">
                <el-input v-model="row.D_C3"/>
              </template>
            </el-table-column>
            <el-table-column
              property="D_综合"
              label="综合">
              <template v-slot="{ row }">
                <el-input v-model="row.D_综合"/>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'varietyQuality2')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('varietyQuality2')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--protocolBlank情况-->
    <el-dialog
      :visible.sync="protocolBlank.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="protocolBlank情况">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('protocolBlank')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importProtocolBlank')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreviewProtocolBlank"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportProtocolBlank">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveprotocolBlank">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          协议坯统计情况
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="protocolBlank.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            property="A_LIST"
            label="板坯号">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            label="钢种描述">
            <template v-slot="{ row }">
              <el-input v-model="row.B_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="k1"
            label="厚度">
            <template v-slot="{ row }">
              <el-input v-model="row.k1"/>
            </template>
          </el-table-column>
          <el-table-column
            property="k2"
            label="宽度">
            <template v-slot="{ row }">
              <el-input v-model="row.k2"/>
            </template>
          </el-table-column>
          <el-table-column
            property="k3"
            label="长度">
            <template v-slot="{ row }">
              <el-input v-model="row.k3"/>
            </template>
          </el-table-column>
          <el-table-column
            property="k4"
            label="重量">
            <template v-slot="{ row }">
              <el-input v-model="row.k4"/>
            </template>
          </el-table-column>
          <el-table-column
            property=""
            label="原因详细">
            <template v-slot="{ row }">
              <el-input
                v-model="row.D_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="E_LIST"
            label="处理名称">
            <template v-slot="{ row }">
              <el-input
                v-model="row.E_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="F_LIST"
            label="解锁日期">
            <template v-slot="{ row }">
              <el-input v-model="row.F_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="G_LIST"
            label="处理人">
            <template v-slot="{ row }">
              <el-input
                v-model="row.G_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'protocolBlank')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('protocolBlank')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="protocolBlankReportForms.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="protocolBlankReportForms情况">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('protocolBlankReportForms')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importProtocolBlankReportForms')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreviewProtocolBlankReportForms"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportProtocolBlankReportForms">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveProtocolBlankReportForms">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          协议坯统计分析报表
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="protocolBlankReportForms.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            property="A_LIST"
            label="协议名称">
            <template v-slot="{ row }">
              <el-input
                v-model="row.A_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            label="内容">
            <template v-slot="{ row }">
              <el-input
                v-model="row.B_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="C_LIST"
            label="责任单位">
            <template v-slot="{ row }">
              <el-input
                v-model="row.C_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="D_LIST"
            label="重量">
            <template v-slot="{ row }">
              <el-input
                v-model="row.D_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'protocolBlankReportForms')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('protocolBlankReportForms')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>

    <el-dialog
      :visible.sync="variety2.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="炼钢非计划情况说明">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality&&!variety2.save"
              class="screen-btn"
              @click="savevariety2">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          炼钢非计划情况说明
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="variety2.gridData"
          class="font-table"
          border>
          <el-table-column
            property="A_LIST"
            label="内容">
            <template
              v-slot="{ row }"
              v-if="!variety2.save">
              <el-input
                v-model="row.A_LIST"
                :rows="4"
                type="textarea"/>
            </template>
            <template
              v-slot="{ row }"
              v-else>
              <div
                slot="content"
                v-html="formatText(row.A_LIST)"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            width="210px"
            label="图片说明">
            <template v-slot="{ row, $index }">
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.B_LIST"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item.id"
                    :id="item.id"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !variety2.save"
                    @img-delete="handlePasteImgDeleteID($event, index, 'variety2')"
                  />
                </li>
              </ul>
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.showPic == unfinished ? [] : row.showPic"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item"
                    :src="item"
                    :is-id="false"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !variety2.save"
                    @img-delete="handlePasteImgDelete($event, index, 'variety2')"
                  />
                </li>
              </ul>
              <el-upload
                v-if="!variety2.save"
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file)=>{
                  handleChange(file, 'variety2', $index)
                }"
                :show-file-list="false"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = $index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import { firstMorningMeeting } from '@/api/screen'
import ImgView from '@/components/ImgView.vue'
import { deleteFileByIds, uploadFile } from '@/api/system'

export default {
  name: 'massLoss',
  components: { ImgView, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      timeDate: '',
      timeDate2: '',
      editIndex: 0,
      varietyQuality: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      varietyQuality2: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      variety2: {
        gridData: [],
        save: true,
        dialogVisible: false
      },
      protocolBlank: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      protocolBlankReportForms: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getVarietyQuality({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'varietyQuality'
      })
      this.getVarietyQuality2({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'varietyQuality2'
      })
      this.getprotocolBlank({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'protocolBlank'
      })
      this.getProtocolBlankReportForms({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'protocolBlankReportForms'
      })
      this.timeDate = this.$moment(this.cDate).format('MM月DD日')
      this.timeDate2 = this.$moment(this.cDate).format('MM月')
      this.getvariety2()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.varietyQuality.maxHeight = this.$refs.table1.offsetHeight
  },
  methods: {
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          B_LIST: 'D',
          C_C1: 'E',
          C_C2: 'F',
          C_C3: 'G',
          C_综合: 'H',
          D_C1: 'I',
          D_C2: 'J',
          D_C3: 'K',
          D_综合: 'L'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.varietyQuality.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportunfinished() {
      const data = [
        {
          SORT_NUM: '序号',
          A_LIST: '项目',
          B_LIST: '轧钢厂',
          C_C1: 'C1',
          C_C2: 'C2',
          C_C3: 'C3',
          C_综合: '综合',
          D_C1: 'C1',
          D_C2: 'C2',
          D_C3: 'C3',
          D_综合: '综合'
        }
      ].concat(
        _.map(_.cloneDeep(this.varietyQuality.gridData), item => {
          let datas = {}
          _.forEach(
            [
              'SORT_NUM',
              'A_LIST',
              'B_LIST',
              'C_C1',
              'C_C2',
              'C_C3',
              'C_综合',
              'D_C1',
              'D_C2',
              'D_C3',
              'D_综合'
            ],
            items => {
              datas[items] = item[items]
            }
          )
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `炼钢现货非计划（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    importUnfinishedData(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: 'varietyQuality'
      }).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        let resData2 = _.map(resData, item => {
          return {
            ...item,
            ...JSON.parse(item.C_LIST),
            ...JSON.parse(item.D_LIST)
          }
        })
        this.varietyQuality.gridData = _.cloneDeep(resData2)
      })
    },
    handlePreview2(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          B_LIST: 'D',
          C_C1: 'E',
          C_C2: 'F',
          C_C3: 'G',
          C_综合: 'H',
          D_C1: 'I',
          D_C2: 'J',
          D_C3: 'K',
          D_综合: 'L'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.varietyQuality2.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportunfinished2() {
      const data = [
        {
          SORT_NUM: '序号',
          A_LIST: '项目',
          B_LIST: '轧钢厂',
          C_C1: 'C1',
          C_C2: 'C2',
          C_C3: 'C3',
          C_综合: '综合',
          D_C1: 'C1',
          D_C2: 'C2',
          D_C3: 'C3',
          D_综合: '综合'
        }
      ].concat(
        _.map(_.cloneDeep(this.varietyQuality2.gridData), item => {
          let datas = {}
          _.forEach(
            [
              'SORT_NUM',
              'A_LIST',
              'B_LIST',
              'C_C1',
              'C_C2',
              'C_C3',
              'C_综合',
              'D_C1',
              'D_C2',
              'D_C3',
              'D_综合'
            ],
            items => {
              datas[items] = item[items]
            }
          )
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `炼钢原始非计划（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    importUnfinishedData2(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: 'varietyQuality2'
      }).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        let resData2 = _.map(resData, item => {
          return {
            ...item,
            ...JSON.parse(item.C_LIST),
            ...JSON.parse(item.D_LIST)
          }
        })
        this.varietyQuality2.gridData = _.cloneDeep(resData2)
      })
    },

    getVarietyQuality(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        let resData2 = _.map(resData, item => {
          return {
            ...item,
            ...JSON.parse(item.C_LIST),
            ...JSON.parse(item.D_LIST)
          }
        })
        this.varietyQuality.gridData = _.cloneDeep(resData2)
        this.varietyQuality.showGridData = _.cloneDeep(resData2)
      })
    },
    saveVarietyQuality() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 'varietyQuality',
        data: _.map(
          _.sortBy(this.varietyQuality.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              FLAG: 'varietyQuality',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              SORT_NUM: index + 1,
              C_LIST: JSON.stringify({
                C_C1: item.C_C1,
                C_C2: item.C_C2,
                C_C3: item.C_C3,
                C_综合: item.C_综合
              }),
              D_LIST: JSON.stringify({
                D_C1: item.D_C1,
                D_C2: item.D_C2,
                D_C3: item.D_C3,
                D_综合: item.D_综合
              })
            }
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.varietyQuality.dialogVisible = false
        this.getVarietyQuality({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: 'varietyQuality'
        })
        this.loading = false
      })
    },

    getVarietyQuality2(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        let resData2 = _.map(resData, item => {
          return {
            ...item,
            ...JSON.parse(item.C_LIST),
            ...JSON.parse(item.D_LIST)
          }
        })
        this.varietyQuality2.gridData = _.cloneDeep(resData2)
        this.varietyQuality2.showGridData = _.cloneDeep(resData2)
      })
    },
    saveVarietyQuality2() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 'varietyQuality2',
        data: _.map(
          _.sortBy(this.varietyQuality2.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              FLAG: 'varietyQuality2',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              SORT_NUM: index + 1,
              C_LIST: JSON.stringify({
                C_C1: item.C_C1,
                C_C2: item.C_C2,
                C_C3: item.C_C3,
                C_综合: item.C_综合
              }),
              D_LIST: JSON.stringify({
                D_C1: item.D_C1,
                D_C2: item.D_C2,
                D_C3: item.D_C3,
                D_综合: item.D_综合
              })
            }
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.varietyQuality2.dialogVisible = false
        this.getVarietyQuality2({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: 'varietyQuality2'
        })
        this.loading = false
      })
    },

    getprotocolBlank(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        let resData2 = _.map(resData, item => {
          return {
            ...item,
            ...JSON.parse(item.C_LIST)
          }
        })
        this.protocolBlank.gridData = _.cloneDeep(resData2)
        this.protocolBlank.showGridData = _.cloneDeep(resData2)
      })
    },
    saveprotocolBlank() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 'protocolBlank',
        data: _.map(
          _.sortBy(this.protocolBlank.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              FLAG: 'protocolBlank',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              SORT_NUM: index + 1,
              C_LIST: JSON.stringify({
                k1: item.k1,
                k2: item.k2,
                k3: item.k3,
                k4: item.k4,
                k5: item.k5,
                k6: item.k6,
                k7: item.k7,
                k8: item.k8,
                k9: item.k9
              })
            }
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.protocolBlank.dialogVisible = false
        this.getprotocolBlank({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: 'protocolBlank'
        })
        this.loading = false
      })
    },
    importProtocolBlank(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: 'protocolBlank'
      }).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        let resData2 = _.map(resData, item => {
          return {
            ...item,
            ...JSON.parse(item.C_LIST)
          }
        })
        this.protocolBlank.gridData = _.cloneDeep(resData2)
        this.$message.success('导入成功！')
      })
    },
    handlePreviewProtocolBlank(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          B_LIST: 'D',
          k1: 'E',
          k2: 'F',
          k3: 'G',
          k4: 'H',
          D_LIST: 'I',
          E_LIST: 'J',
          F_LIST: 'K',
          G_LIST: 'L'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.protocolBlank.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportProtocolBlank() {
      const data = [
        {
          SORT_NUM: '序号',
          A_LIST: '板坯号',
          B_LIST: '钢种描述',
          k1: '厚度',
          k2: '宽度',
          k3: '长度',
          k4: '重量',
          D_LIST: '原因详细',
          E_LIST: '处理名称',
          F_LIST: '解锁日期',
          G_LIST: '处理人'
        }
      ].concat(
        _.map(_.cloneDeep(this.protocolBlank.gridData), item => {
          let datas = {}
          _.forEach(
            [
              'SORT_NUM',
              'A_LIST',
              'B_LIST',
              'k1',
              'k2',
              'k3',
              'k4',
              'D_LIST',
              'E_LIST',
              'F_LIST',
              'G_LIST'
            ],
            items => {
              datas[items] = item[items]
            }
          )
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `协议坯统计情况（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },

    getProtocolBlankReportForms(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.protocolBlankReportForms.gridData = _.cloneDeep(resData)
        this.protocolBlankReportForms.showGridData = _.cloneDeep(resData)
      })
    },
    saveProtocolBlankReportForms() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 'protocolBlankReportForms',
        data: _.map(
          _.sortBy(
            this.protocolBlankReportForms.gridData,
            item => item.SORT_NUM
          ),
          (item, index) => {
            return {
              ...item,
              FLAG: 'protocolBlankReportForms',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              SORT_NUM: index + 1
            }
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.protocolBlankReportForms.dialogVisible = false
        this.getProtocolBlankReportForms({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: 'protocolBlankReportForms'
        })
        this.loading = false
      })
    },
    importProtocolBlankReportForms(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: 'protocolBlankReportForms'
      }).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.protocolBlank.gridData = _.cloneDeep(resData)
        this.$message.success('导入成功！')
      })
    },
    handlePreviewProtocolBlankReportForms(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          B_LIST: 'D',
          C_LIST: 'E',
          D_LIST: 'F'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.protocolBlankReportForms.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportProtocolBlankReportForms() {
      const data = [
        {
          SORT_NUM: '序号',
          A_LIST: '协议名称',
          B_LIST: '内容',
          C_LIST: '责任单位',
          D_LIST: '重量（t）'
        }
      ].concat(
        _.map(_.cloneDeep(this.protocolBlankReportForms.gridData), item => {
          let datas = {}
          _.forEach(
            ['SORT_NUM', 'A_LIST', 'B_LIST', 'C_LIST', 'D_LIST'],
            items => {
              datas[items] = item[items]
            }
          )
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `协议坯统计报表（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },

    getvariety2() {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'variety2'
      }).then(res => {
        this.variety2.gridData = _.cloneDeep(
          _.map(res.data.length ? res.data : [{}], item => {
            return {
              ...item,
              B_LIST: item.B_LIST ? JSON.parse(item.B_LIST) : []
            }
          })
        )
      })
    },
    async savevariety2() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 'variety2',
        data: _.map(
          _.sortBy(this.variety2.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              FLAG: 'variety2',
              SORT_NUM: index + 1
            }
          }
        )
      }

      let del = null
      if (
        this.variety2.gridData[0].delImage &&
        this.variety2.gridData[0].delImage.length
      ) {
        del = await post(deleteFileByIds, {
          ids: this.variety2.gridData[0].delImage
        })
      }
      if (del == null || del.success) {
        if (
          this.variety2.gridData[0].file &&
          this.variety2.gridData[0].file.length
        ) {
          const formData = new FormData()
          this.variety2.gridData[0].file.forEach(item => {
            formData.append('files', item)
          })
          post(uploadFile, formData, false, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.success) {
              params.data[0].B_LIST = JSON.stringify([
                ...params.data[0].B_LIST,
                ...res.data
              ])
              post(firstMorningMeeting.furnaceCondition, params).then(res => {
                this.getvariety2()
                this.$message.success('保存成功')
                this.loading = false
              })
            } else {
              this.$message.warning('图片上传失败！')
              this.loading = false
            }
          })
        } else {
          params.data[0].B_LIST = JSON.stringify(params.data[0].B_LIST)
          post(firstMorningMeeting.furnaceCondition, params).then(res => {
            this.getvariety2()
            this.$message.success('保存成功')
            this.loading = false
          })
        }
      } else {
        this.$message.error('图片删除失败！！！')
      }
    },
    httpRequest(params) {},
    handleChange(file, row, index) {
      if (this[row].gridData[index].file == undefined) {
        this[row].gridData[index].file = [file.raw]
      } else {
        this[row].gridData[index].file.push(file.raw)
      }
      if (this[row].gridData[index].showPic == undefined) {
        this[row].gridData[index].showPic = [file.url]
      } else {
        this[row].gridData[index].showPic.push(file.url)
      }
      this[row] = _.cloneDeep(this[row])
    },
    handlePasteImgDelete(file, index, row) {
      this[row].gridData[0].file.splice(index, 1)
      this[row].gridData[0].showPic.splice(index, 1)
      this[row] = _.cloneDeep(this[row])
    },
    handlePasteImgDeleteID(file, index, row) {
      if (this[row].gridData[0].delImage == undefined) {
        this[row].gridData[0].delImage = [file.id]
      } else {
        this[row].gridData[0].delImage.push(file.id)
      }
      this[row].gridData[0].B_LIST.splice(index, 1)
      this[row] = _.cloneDeep(this[row])
    },
    //转%
    percentage(row, name) {
      return row.B_LIST.includes('指标') &&
        row[name] != undefined &&
        row[name] != null
        ? row[name].toString().includes('%')
          ? row[name]
          : (row[name] * 100).toFixed(2) + '%'
        : row[name]
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
