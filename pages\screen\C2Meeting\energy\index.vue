<template>
  <div class="content">
    <div class="content-item">
      <screen-border-multi>
        <template v-slot:title>
          <div class="tabs-class">
            <div
              v-for="(item) in tabList"
              :key="item.id"
              :class="{'tab-pane-active': active === item.id}"
              class="tab-pane"
              @click="active = item.id">
              <div class="tab-pane-title-class">
                <div>{{ item.title }}</div>
                <div
                  v-if="active === item.id"
                  class="tab-pane-img">
                  <img
                    class="tab-pane-img2"
                    src="@/assets/images/screen/tab-pane-active-line2.png"
                    alt="">
                  <img
                    class="tab-pane-img1"
                    src="@/assets/images/screen/tab-pane-active-line.png"
                    alt="">
                </div>
              </div>
            </div>
          </div>
        </template>
        <energy-table
          v-if="active === '1'"
          :key="'energy1'"
          :title="'昨日主线电耗情况汇总'"
          :setting="tableObj1.setting"
          :merge-set="tableObj1.mergeObj"
          :url-list="tableObj1.url.list"
          :url-save="tableObj1.url.save"
          :show-edit="tableObj1.showEdit"
          :select-date="selectDate"
          :active="active"
        />
        <energy-table
          v-if="active === '2'"
          :key="'energy2'"
          :title="'昨日主线能源消耗情况汇总'"
          :setting="tableObj2.setting"
          :merge-set="tableObj2.mergeObj"
          :url-list="tableObj2.url.list"
          :url-save="tableObj2.url.save"
          :show-edit="tableObj2.showEdit"
          :select-date="selectDate"
          :active="active"
        />
        <energy-table
          v-if="active === '3'"
          :key="'energy3'"
          :title="'昨日热处理能源消耗情况汇总'"
          :setting="tableObj3.setting"
          :merge-set="tableObj3.mergeObj"
          :url-list="tableObj3.url.list"
          :url-save="tableObj3.url.save"
          :show-edit="tableObj3.showEdit"
          :select-date="selectDate"
          :active="active"
        />
        <div
          v-if="active === '4'"
          style="flex: 2"
          class="content-item">
          <el-row
            :gutter="32"
            class="full-height">
            <el-col
              :span="24"
              class="full-height">
              <screen-border-multi :title="'各车间燃动成本报表 '">
                <template v-slot:default>
                  <div
                    ref="table1"
                    class="chart-wrapper">
                    <el-table
                      v-loading="fuelLoading"
                      :data="fuelList"
                      height="450"
                      border>
                      <el-table-column
                        align="center"
                        show-overflow-tooltip
                        prop="name"
                        label="项目"
                        width="110" />
                      <el-table-column
                        align="center"
                        show-overflow-tooltip
                        prop="unit"
                        label="计量单位" />
                      <el-table-column
                        align="center"
                        show-overflow-tooltip
                        prop="price"
                        label="单价" />
                      <el-table-column
                        align="center"
                        show-overflow-tooltip
                        prop=""
                        label="热轧车间">
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="rzPlan"
                          label="计划" />
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="rzActual"
                          label="实际" />
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="rzCost"
                          label="降本" />
                      </el-table-column>
                      <el-table-column
                        align="center"
                        show-overflow-tooltip
                        prop=""
                        label="电修车间">
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="stePlan"
                          label="计划" />
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="steActual"
                          label="实际">
                          <template v-slot="scope">
                            <span
                              v-if="scope.row.name==='蒸汽回收'"
                              style="cursor: pointer;color: #1FC6FF;text-decoration: underline;"
                              @click="clickSteamDialog">{{ scope.row.steActual }}</span>
                            <span
                              v-else-if="scope.row.name==='煤气回收'"
                              style="cursor: pointer;color: #1FC6FF;text-decoration: underline;"
                              @click="clickGasDialog">{{ scope.row.steActual }}</span>
                            <span v-else>{{ scope.row.steActual }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="steCost"
                          label="降本" />
                      </el-table-column>
                      <el-table-column
                        align="center"
                        show-overflow-tooltip
                        prop=""
                        label="机修车间">
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="refPlan"
                          label="计划" />
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="refActual"
                          label="实际" />
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="refCost"
                          label="降本" />
                      </el-table-column>
                      <el-table-column
                        align="center"
                        show-overflow-tooltip
                        prop=""
                        label="精整车间">
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="conPlan"
                          label="计划" />
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="conActual"
                          label="实际" />
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="conCost"
                          label="降本" />
                      </el-table-column>
                      <el-table-column
                        align="center"
                        show-overflow-tooltip
                        prop=""
                        label="热处理车间">
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="compPlan"
                          label="计划" />
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="compActual"
                          label="实际" />
                        <el-table-column
                          align="center"
                          show-overflow-tooltip
                          prop="compCost"
                          label="降本" />
                      </el-table-column>
                    </el-table>
                  </div>
                </template>

              </screen-border-multi>
            </el-col>
          </el-row>
        </div>

      </screen-border-multi>
    </div>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/C2Meeting/component/custom-table'
import {
  costReportC2,
  eleConsume,
  htpYiedConsume,
  htrConsume
} from '@/api/screenC2'
import EnergyTable from '@/pages/screen/C2Meeting/component/energy-table'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import { post } from '@/lib/Util'
import moment from 'moment'

export default {
  name: 'energyC1',
  components: { ScreenBorderMulti, EnergyTable, CustomTable, SingleBarsChart },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      fuelList: [],
      cDate: '',
      active: '1',
      tabList: [
        {
          id: '1',
          active: true,
          title: '昨日主线电耗情况汇总'
        },
        {
          id: '2',
          active: false,
          title: '昨日主线能源消耗情况汇总'
        },
        {
          id: '3',
          active: false,
          title: '昨日热处理能源消耗情况汇总'
        }
        // {
        //   id: '4',
        //   active: false,
        //   title: '燃动成本'
        // }
      ],
      tableObj1: {
        url: {
          save: '',
          list: eleConsume
        },
        showEdit: false,
        mergeObj: {
          '0-1': [2, 1],
          '1-1': false,
          '2-1': [2, 1],
          '3-1': false,
          '4-1': [2, 1],
          '5-1': false,
          '0-2': [2, 1],
          '1-2': false,
          '2-2': [2, 1],
          '3-2': false,
          '4-2': [2, 1],
          '5-2': false,
          '6-2': [2, 1],
          '7-2': false,
          '0-0': [2, 1],
          '1-0': false,
          '2-0': [2, 1],
          '3-0': false,
          '4-0': [2, 1],
          '5-0': false,
          '6-0': [2, 2],
          '6-1': false,
          '7-0': false,
          '7-1': false
        },
        setting: [
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次',
            width: '80'
          },
          {
            keyQuery: 'team',
            keySave: 'team',
            label: '班别',
            width: '80'
          },
          {
            keyQuery: 'htrYield',
            keySave: 'htrYield',
            label: '实绩产量（吨）'
          },
          {
            keyQuery: 'name',
            keySave: 'name',
            label: '班/日',
            width: '90'
          },
          {
            label: '电（kWh/t）',
            children: [
              {
                keyQuery: 'S_10',
                keySave: 'S_10',
                label: '主线',
                max: 59.999
              },
              {
                keyQuery: 'S_1602479925753810946',
                keySave: 'S_1602479925753810946',
                label: '板加',
                max: 6.392
              },
              {
                keyQuery: 'S_7',
                keySave: 'S_7',
                label: '热轧',
                max: 56.0878
              },
              {
                keyQuery: 'maindrive',
                keySave: 'maindrive',
                label: '主电机',
                max: 6.583
              },
              {
                keyQuery: 'descaling',
                keySave: 'descaling',
                label: '高压水除鳞',
                max: 10.486
              },
              {
                keyQuery: 'S_9',
                keySave: 'S_9',
                label: '精整',
                max: 9.935
              },
              {
                keyQuery: 'dustremoval',
                keySave: 'dustremoval',
                label: '轧机除尘',
                width: '110'
              }
            ]
          }
        ]
      },
      tableObj2: {
        url: {
          save: '',
          list: htrConsume
        },
        showEdit: false,
        mergeObj: {
          '0-1': [2, 1],
          '1-1': false,
          '2-1': [2, 1],
          '3-1': false,
          '4-1': [2, 1],
          '5-1': false,
          '0-2': [2, 1],
          '1-2': false,
          '2-2': [2, 1],
          '3-2': false,
          '4-2': [2, 1],
          '5-2': false,
          '6-2': [2, 1],
          '7-2': false,
          '0-0': [2, 1],
          '1-0': false,
          '2-0': [2, 1],
          '3-0': false,
          '4-0': [2, 1],
          '5-0': false,
          '6-0': [2, 2],
          '6-1': false,
          '7-0': false,
          '7-1': false,
          '8-0': [5, 2],
          '9-0': false,
          '8-2': false,
          '9-2': false,
          '8-3': [1, 4],
          '9-3': [1, 4],
          '10-0': false,
          '10-2': false,
          '10-3': [1, 4],
          '11-0': false,
          '11-2': false,
          '11-3': [1, 4],
          '12-0': false,
          '12-2': false,
          '12-3': [1, 4]
        },
        setting: [
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次',
            width: '80'
          },
          {
            keyQuery: 'team',
            keySave: 'team',
            label: '班别',
            width: '70'
          },
          {
            keyQuery: 'htrYield',
            keySave: 'htrYield',
            label: '实绩产量T'
          },
          {
            keyQuery: 'name',
            keySave: 'name',
            label: '班/日'
          },
          {
            keyQuery: 'gasGt',
            keySave: 'gasGt',
            label: '煤气单耗（GJ/t）',
            max: 1.71
          },
          {
            keyQuery: 'watGt',
            keySave: 'watGt',
            label: '轧区循环水单耗（m3/t）',
            max: '24.59'
          }
        ]
      },
      tableObj3: {
        url: {
          save: '',
          list: htpYiedConsume
        },
        showEdit: false,
        mergeObj: {
          '0-0': [2, 1],
          '1-0': false,
          '0-1': [2, 1],
          '1-1': false,
          '2-1': [2, 1],
          '3-1': false,
          '4-1': [2, 1],
          '5-1': false,
          '0-2': [2, 1],
          '1-2': false,
          '2-2': [2, 1],
          '3-2': false,
          '4-2': [2, 1],
          '5-2': false,
          '6-2': [2, 1],
          '7-2': false,
          '2-0': [2, 1],
          '3-0': false,
          '4-0': [2, 1],
          '5-0': false,
          '6-0': [2, 2],
          '6-1': false,
          '7-0': false,
          '7-1': false
        },
        setting: [
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次',
            width: '100'
          },
          {
            keyQuery: 'team',
            keySave: 'team',
            label: '班别',
            width: '80'
          },
          {
            keyQuery: 'htpYield',
            keySave: 'htpYield',
            label: '实绩产量T'
          },
          {
            keyQuery: 'name',
            keySave: 'name',
            label: '班/日',
            width: '100'
          },
          {
            keyQuery: 'eleGt',
            keySave: 'eleGt',
            label: '电（KWh/t）',
            max: '34'
          },
          {
            keyQuery: 'gasGt',
            keySave: 'gasGt',
            label: '煤气单耗（GJ/t）',
            max: '1.23'
          },
          {
            keyQuery: 'watGt',
            keySave: 'watGt',
            label: '热处理循环水单耗（m3/t）',
            max: '75'
          }
        ]
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    }
  },
  created() {
    this.cDate = this.selectDate
    this.fetchMaxValues()
    this.getFuelData()
  },
  methods: {
    async fetchMaxValues() {
      try {
        const response = await post('/ems/statement/max-values')
        const response2 = await post('/ems/statement/max-values2')
        const response3 = await post('/ems/statement/max-values3')
        const maxValues = response.data
        const maxValues2 = response2.data
        const maxValues3 = response3.data
        this.updateMaxValues(maxValues)
        this.updateMaxValues2(maxValues2)
        this.updateMaxValues3(maxValues3)
      } catch (error) {
        console.error('Error fetching max values:', error)
      }
    },
    updateMaxValues(maxValues) {
      this.tableObj1.setting.forEach(setting => {
        if (setting.children) {
          setting.children.forEach(child => {
            if (maxValues[child.keyQuery]) {
              child.max = maxValues[child.keyQuery]
            }
          })
        }
      })
    },
    updateMaxValues2(maxValues) {
      this.tableObj2.setting.forEach(setting => {
        if (maxValues[setting.keyQuery]) {
          setting.max = maxValues[setting.keyQuery]
        }
      })
    },
    updateMaxValues3(maxValues) {
      this.tableObj3.setting.forEach(setting => {
        if (maxValues[setting.keyQuery]) {
          setting.max = maxValues[setting.keyQuery]
        }
      })
    },
    getFuelData() {
      const previousDate = moment(this.cDate)
        .subtract(1, 'days')
        .format('YYYY-MM-DD')

      const params = {
        date: previousDate
      }
      this.fuelLoading = true
      post(costReportC2, params)
        .then(res => {
          this.fuelList = res.data
        })
        .finally(_ => {
          this.fuelLoading = false
        })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
