<template>
  <div class="full-height">
    <screen-border
      :title="title"
      :content-class="contentClass">
      <template v-slot:headerRight>
        <template v-if="steelmakingShow">
          <slot name="topRight"/>
        </template>
        <template v-else>
          <el-button
            :loading="exportLoading"
            class="screen-btn"
            @click="backendExportTable">
            导出
          </el-button>
          <button
            v-if="currentTime"
            class="screen-btn"
            @click="getData()">
            <el-icon class="el-icon-search"/>
            查找
          </button>
          <el-popconfirm
            v-else
            title="该时间段数据不稳定，请确认是否查找"
            @confirm="confirmSearch()"
            @cancel="cancelDelete()"
          >
            <el-button
              slot="reference"
              class="screen-btn">查找</el-button>
          </el-popconfirm>
          <!-- <button
            class="screen-btn"
            @click="getData()">
            <el-icon class="el-icon-search"/>
            查找
          </button> -->
          <!-- <el-button
            class="screen-btn"
            type="text"
          >查找</el-button>
          <el-popconfirm
            title="确定要删除这项内容吗？"
            @confirm="confirmSearch()"
            @cancel="cancelDelete()"/> -->

        </template>
        <el-select
          v-model="steelType"
          style="width:200px;margin-left: 5px;"
          filterable
          clearable
          multiple
          collapse-tags
          placeholder="请选择钢种"
        >
          <el-option
            v-for="item in steelTypeAll"
            :key="item"
            :label="item"
            :value="item"/>
        </el-select>
        <el-select
          v-model="orderNo"
          style="width:140px;margin-left: 5px;"
          filterable
          clearable
          placeholder="请选择订单号">
          <el-option
            v-for="item in orderNoAll"
            :key="item"
            :label="item"
            :value="item"/>
        </el-select>
        <!--        <el-input
          v-model="steelType"
          style="width:140px;margin-left: 5px;"
          placeholder="请输入钢种"
        />-->

        <el-date-picker
          v-model="cDate2"
          :clearable="orderNo ? true : false"
          :size="'small'"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="margin-left: 5px;background-color:#093d4d;color:red"

          @input="$forceUpdate()"/>
          <!-- <template v-else>
          <span
            v-command="'/screen/productionKpiScreen/edit'"
            class="screen-btn"
            @click="dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            操作
          </span>
        </template> -->
      </template>
      <slot name="content"/>
      <div
        v-if="showTable"
        ref="table1"
        class="scroll-wrapper">
        <el-table
          v-loading="loading"
          :data="showGridData"
          :max-height="maxHeight"
          :span-method="objectSpanMethod"
          style="overflow: visible;"
          empty-text="无数据"
          class="font-table center-table"
          border>
          <template #empty>
            <div class="custom-empty">
              <i class="el-icon-warning"/>
              <p>暂无数据</p>
            </div>
          </template>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false && !item.onlyEdit">
              <el-table-column
                v-if="item.type === 'index'"
                :fixed="item.fixed"
                :key="index"
                :label="item.label"
                type="index"
                width="100"
              />
              <template v-else>
                <template v-if="item.inputType === 'textarea'">
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :fixed="item.fixed"
                    :label="item.label"
                    :align="item.align">
                    <template v-slot="{ row }">
                      <div
                        slot="content"
                        v-html="formatText(row[item.keySave], item.split)"
                      />
                    </template>
                  </el-table-column>
                </template>
                <template v-else>
                  <el-table-column
                    :key="index"
                    :fixed="item.fixed"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label"
                    :align="item.align"/>
                </template>
              </template>
            </template>
          </template>
        </el-table>
        <el-pagination
          :total="pagination.total"
          :pager-count="5"
          :page-size="pagination.pageSize"
          :current-page="pagination.pageIndex"
          :page-sizes="pagination.pageSizes"
          :align="pagination.align"
          background
          class="foot"
          layout="total,prev, pager, next,sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </div>
    </screen-border>
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span> -->
            <!-- <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/> -->
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event)">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <!-- <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span> -->
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportTable">
              导出
            </span>
            <!--<span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span> -->
          </div>
          {{ popTitle || title }}
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="gridData"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.type === 'index'"
                :key="index"
                :label="item.label"
                :align="item.align"
                type="index"
                width="100"
              />
              <template v-else>
                <el-table-column
                  :key="index"
                  :width="item.width || ''"
                  :property="item.keySave"
                  :align="item.align"
                  :label="item.label">
                  <template v-slot="{ row }">
                    <template v-if="item.inputType === 'textarea'">
                      <el-input
                        v-model="row[item.keySave]"
                        :disabled="item.disabled"
                        :rows="4"
                        type="textarea"
                      />
                    </template>
                    <template v-else>
                      <el-input
                        v-model="row[item.keySave]"
                        :disabled="item.disabled"/>
                    </template>
                  </template>
                </el-table-column>
              </template>
            </template>
          </template>
          <!-- <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index)">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column> -->
        </el-table>
      </el-form>
      <!-- <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData()">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div> -->
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import {
  qmsQualityQuery,
  qmsQualitySave,
  FullProcessYieldFindDetailsDateByOrder,
  FindSteelOrder,
  findDetailsDateExcel
} from '@/api/screen'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorder from '@/pages/screen/overallYieldrate/component/screen-border'
import { outExcel } from '@/utils/outExcel'

export default {
  name: 'custom-table',
  components: { ScreenBorder },
  props: {
    title: {
      type: String,
      default: ''
    },
    popTitle: {
      type: String,
      default: ''
    },
    setting: {
      type: Array,
      default: function() {
        return []
      }
    },
    selectDate: {
      type: String,
      default: ''
    },
    urlList: {
      type: String,
      default: ''
    },
    urlSave: {
      type: String,
      default: ''
    },
    urlSteel: {
      type: String,
      default: ''
    },
    showTable: {
      type: Boolean,
      default: true
    },
    heightAuto: {
      type: Boolean,
      default: true
    },
    heightSet: {
      type: Number,
      default: 0
    },
    steelmakingShow: {
      type: Boolean,
      default: false
    },
    contentClass: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data: function() {
    return {
      exportLoading: false, //导出按钮提示
      cDate: '',
      cDate2: [
        moment(new Date().getTime() - 7 * 24 * 1000 * 60 * 60).format(
          'YYYY-MM-DD'
        ),
        moment(new Date().getTime()).format('YYYY-MM-DD')
      ],
      loading: false,
      showPopconfirm: false,
      currentTime: false,
      dialogVisible: false,
      showGridData: [],
      gridData: [],
      importDate: null,
      importDateVisible: false,
      importFunName: '',
      mergeArr: [],
      spanArr: {},
      position: 0,
      maxHeight: null,
      steelType: [],
      steelTypeAll: '',
      orderNo: '',
      orderNoAll: [],
      pagination: {
        align: 'right',
        pageSizes: [500, 1000, 5000, 10000],
        pageSize: 500,
        pageIndex: 1,
        total: 0
      },
      ABC: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'K'
      ],
      spanArr1: [],
      spanArr2: [],
      pos1: {},
      pos2: {}
    }
  },
  computed: {
    canEditQuality: function() {
      // console.log(moment().format('yyyy-MM-DD'), this.cDate)
      return (
        moment().format('yyyy-MM-DD') <=
        moment(this.cDate)
          .subtract(-1, 'day')
          .format('yyyy-MM-DD')
      )
    }
  },
  watch: {
    // selectDate: function() {
    //   this.cDate = this.selectDate
    // },
    // cDate: function() {
    //   // 初始化数据
    //   this.getData()
    // },
    heightSet: function() {
      this.maxHeight = this.heightSet ? this.heightSet : null
    }
  },
  destroyed() {
    // window.removeEventListener('resize', this.calculate)
  },
  created() {
    // this.cDate = this.selectDate
    // this.getData()
    this.getSteelData()
    this.getOrderNoAll()
  },
  mounted() {
    this.calculate()
    this.checkAnyTimeRange()
    // window.addEventListener('resize', this.calculate)
  },
  methods: {
    getOrderNoAll() {
      console.log('%c 获取订单号', 'color:red;')
      post(FindSteelOrder).then(res => {
        if (Array.isArray(res.data) && res.data.length > 0) {
          const orderNumbers = res.data.map(item => {
            return Object.values(item)[0]
          })
          this.orderNoAll = orderNumbers
        } else {
          this.orderNoAll = []
        }
      })
    },
    queryTableData(val) {
      this.pagination.pageIndex = val
      this.queryTableDataAll()
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // if (!Object.keys(this.mergeSet).length) return [1, 1]
      // if (this.mergeSet[rowIndex + '-' + columnIndex] !== undefined) {
      //   return !this.mergeSet[rowIndex + '-' + columnIndex]
      //     ? [0, 0]
      //     : this.mergeSet[rowIndex + '-' + columnIndex]
      // }
      // return [1, 1]
      if (columnIndex === 0 || columnIndex === 1) {
        const currentValue = row[column.property]
        const preRow = this.showGridData[rowIndex - 1]
        //上一行这一列的数据
        const preValue = preRow ? preRow[column.property] : null
        // 如果当前值和上一行的值相同，则将当前单元格隐藏
        if (currentValue === preValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let rowspan = 1
          // 计算应该合并的行数
          for (let i = rowIndex + 1; i < this.showGridData.length; i++) {
            const nextRow = this.showGridData[i]
            const nextValue = nextRow[column.property]
            if (nextValue === currentValue) {
              rowspan++
            } else {
              break
            }
          }
          return { rowspan, colspan: 1 }
        }
      }
    },
    confirmSearch() {
      // 用户点击确认按钮时执行的逻辑
      this.getData()
    },
    cancelDelete() {
      console.log('删除操作已取消')
    },
    // 导入文件
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = this.ABC[index]
      })
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, obj)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.gridData = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    // 后端导出
    backendExportTable() {
      console.log('后端导出')
      // post(findDetailsDateExcel, {
      //   startDate: this.cDate2 ? this.cDate2[0] || '' : '',
      //   endDate: this.cDate2 ? this.cDate2[1] || '' : '',
      //   ordNo: this.orderNo,
      //   steelType: this.steelType
      // }).then(res => {
      //   console.log(res, 'res')
      // })
      this.$confirm('此操作为导出excel, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.exportLoading = true
          //导出表格 传入url,参数,名称
          return outExcel(
            'api/mesAPI/FullProcessYield/findDetailsDateExcel',
            {
              startDate: this.cDate2 ? this.cDate2[0] || '' : '',
              endDate: this.cDate2 ? this.cDate2[1] || '' : '',
              ordNo: this.orderNo,
              steelType: this.steelType
            },
            `${this.title}（${
              this.cDate2 ? this.cDate2[0] + '~' + this.cDate2[1] : ''
            }）.xls`
          )
        })
        .catch(error => {
          console.error('导出失败:', error)
          this.$message.error('导出失败，请稍后重试')
        })
        .finally(() => {
          this.exportLoading = false
        })
    },
    // 导出表格
    exportTable() {
      // 准备导出数据
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = item.label
      })

      const data = [obj].concat(
        _.cloneDeep(
          this.showGridData.map(item => {
            const objRow = {}
            this.setting.forEach(set => {
              objRow[set.keySave] = item[set.keySave]
            })
            return objRow
          })
        )
      )

      // 设置基础样式
      const len = data.length
      LAY_EXCEL.setExportCellStyle(data, 'A1:T' + len, {
        s: {
          border: {
            top: { style: 'thin' },
            bottom: { style: 'thin' },
            left: { style: 'thin' },
            right: { style: 'thin' }
          },
          alignment: {
            horizontal: 'center',
            vertical: 'center'
          }
        }
      })

      // 计算需要合并的单元格
      const merges = []

      // 处理第一组合并(炉号、坯料钢种、坯料重量等前4列)
      this.spanArr1.forEach((span, index) => {
        if (span > 1) {
          // 只处理需要合并的行(span>1)
          // 合并前4列的单元格
          for (let col = 0; col < 3; col++) {
            merges.push({
              s: { r: index + 1, c: col }, // 开始位置(r:行,c:列),加1是因为第一行是表头
              e: { r: index + span, c: col } // 结束位置(r:行,c:列)
            })
          }
        }
      })

      // 处理第二组合并(子坯料号等字段,4-19列)
      this.spanArr2.forEach((span, index) => {
        if (span > 1) {
          // 只处理需要合并的行(span>1)
          // 合并3-19列的单元格
          for (let col = 3; col < 20; col++) {
            merges.push({
              s: { r: index + 1, c: col }, // 开始位置(r:行,c:列),加1是因为第一行是表头
              e: { r: index + span, c: col } // 结束位置(r:行,c:列)
            })
          }
        }
      })

      // 导出Excel时使用merges配置合并单元格
      LAY_EXCEL.exportExcel(
        data,
        `${this.title}（${
          this.cDate2 ? this.cDate2[0] + '~' + this.cDate2[1] : ''
        }）.xlsx`,
        'xlsx',
        {
          extend: {
            '!merges': merges // 设置合并单元格配置
          }
        }
      )
    },
    getSteelData() {
      post(this.urlSteel, {
        startDate: this.cDate2 ? this.cDate2[0] || '' : '',
        endDate: this.cDate2 ? this.cDate2[1] || '' : ''
      }).then(res => {
        let obj = []
        for (const item of res.data) {
          for (let key in item) {
            obj.push(key)
          }
        }
        this.steelTypeAll = obj
        //console.log(this.steelTypeAll, 'this.steelTypeAll')
      })
    },
    isCurrentTimeInAnyRange(timeRanges) {
      // 获取当前时间
      const currentTime = moment()

      // 遍历所有时间范围
      for (let range of timeRanges) {
        const start = moment(range.start)
        const end = moment(range.end)

        // 判断当前时间是否在任何一个范围内（包括边界）
        if (currentTime.isBetween(start, end, null, '[]')) {
          return true // 如果在范围内，立即返回 true
        }
      }

      // 如果遍历完所有范围都不在内，则返回 false
      return false
    },
    checkAnyTimeRange() {
      // 定义多个时间范围
      const timeRanges = [
        {
          start: moment()
            .hour(0)
            .minute(50)
            .second(0)
            .millisecond(0),
          end: moment()
            .hour(2)
            .minute(10)
            .second(0)
            .millisecond(0)
            .subtract(1, 'second')
        },
        {
          start: moment()
            .hour(8)
            .minute(30)
            .second(0)
            .millisecond(0),
          end: moment()
            .hour(10)
            .minute(10)
            .second(0)
            .millisecond(0)
            .subtract(1, 'second')
        },
        {
          start: moment()
            .hour(16)
            .minute(50)
            .second(0)
            .millisecond(0),
          end: moment()
            .hour(18)
            .minute(10)
            .second(0)
            .millisecond(0)
            .subtract(1, 'second')
        }
        // {
        //   start: moment()
        //     .hour(12)
        //     .minute(0)
        //     .second(0)
        //     .millisecond(0),
        //   end: moment()
        //     .hour(13)
        //     .minute(10)
        //     .second(0)
        //     .millisecond(0)
        //     .subtract(1, 'second')
        // }
        // ... 可以添加更多时间范围
      ]

      const isInAnyRange = this.isCurrentTimeInAnyRange(timeRanges)

      if (isInAnyRange) {
        console.log('当前时间在某个指定范围内')
        this.currentTime = false
      } else {
        console.log('当前时间不在任何指定范围内')
        this.currentTime = true
      }
    },

    // 获取数据
    getData() {
      this.loading = true
      post(
        this.urlList,
        Object.assign(
          {
            startDate: this.cDate2 ? this.cDate2[0] || '' : '',
            endDate: this.cDate2 ? this.cDate2[1] || '' : '',
            ordNo: this.orderNo,
            steelType: this.steelType,
            pageIndex: this.pagination.pageIndex,
            pageSize: this.pagination.pageSize
          },
          this.params
        )
      ).then(res => {
        this.pagination.total = res.data.totalElements
        this.loading = false
        this.showGridData = res.data.content.map(item => {
          const obj = {}
          this.setting.forEach(set => {
            obj[set.keySave] = item[set.keyQuery]
          })
          return obj
        })
        this.gridData = _.cloneDeep(this.showGridData)
        this.$nextTick(() => {
          this.getSpanArr(this.showGridData)
          this.$emit('change', this.showGridData)
        })
      })
    },
    handleCurrentChange(newPage) {
      this.pagination.pageIndex = newPage
      this.getData()
    },
    handleSizeChange(newSize) {
      this.pagination.pageSize = newSize
      this.getData()
    },
    handleDateChange(value) {
      this.getData()
    },
    // 更新数据
    saveData() {
      this.loading = true
      // 数据信息
      const params = {
        setDate: this.cDate,
        data: this.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(this.urlSave, params).then(res => {
        //
        this.loading = false
        if (res.status == 1) {
          this.$message.success('保存成功！')
          this.dialogVisible = false
          this.getData()
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    // 导入日期数据
    importData(date) {
      post(this.urlList, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.gridData = res.data.content.map(item => {
          const obj = {}
          this.setting.forEach(set => {
            obj[set.keySave] = item[set.keyQuery]
          })
          return obj
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 执行导入
    importHistoryData() {
      this.importData(this.importDate)
      this.importDateVisible = false
    },
    // 下拉菜单指令
    handleProcessedCommand(command) {
      if (command === 'yesterday') {
        this.importData(
          this.$moment(this.cDate)
            .subtract(1, 'day')
            .format('yyyy-MM-DD')
        )
      } else {
        this.importDate = this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
        this.importDateVisible = true
      }
    },
    // 数据管理
    clearGridData() {
      this.gridData = []
    },
    addGridData() {
      this.gridData.push({})
    },
    delGridData(index) {
      this.gridData.splice(index, 1)
    },
    // 日期改变推送
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    // 计算需要合并的单元格
    formatSpanData(data) {
      this.mergeArr.forEach(keyName => {
        this.spanArr[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
    },
    // 生成带换行数据
    formatText(text, split) {
      if (!text) {
        return ''
      }
      if (split) text = text.split(split).join('\n')
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },
    // 计算高度
    calculate() {
      this.showTable &&
        this.heightAuto &&
        (this.maxHeight = this.$refs.table1.offsetHeight)
    },
    // 计算合并单元格的数组和位置信息
    getSpanArr(data) {
      // 初始化合并数组和位置对象
      this.spanArr1 = [] // 存储第一组(前4列)的合并信息
      this.spanArr2 = [] // 存储第二组(后续列)的合并信息
      this.pos1 = {} // 记录第一组当前合并位置
      this.pos2 = {} // 记录第二组当前合并位置

      // 计算第一组合并信息：炉号、坯料钢种、坯料重量
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          // 第一行默认占一行
          this.spanArr1.push(1)
          this.pos1[i] = 0
        } else {
          const row = data[i]
          const prevRow = data[i - 1]
          // 判断当前行与上一行的关键字段是否相同
          //依据炉号、坯料钢种、坯料重量合并
          if (
            row.heatNo === prevRow.heatNo &&
            row.stlgrd === prevRow.stlgrd &&
            row.wgtSum === prevRow.wgtSum
          ) {
            // 相同则合并：当前位置计数+1，新位置标记为0
            this.spanArr1[this.pos1[i - 1]] += 1
            this.spanArr1.push(0)
            this.pos1[i] = this.pos1[i - 1]
          } else {
            // 不同则新开一行
            this.spanArr1.push(1)
            this.pos1[i] = i
          }
        }
      }

      // 计算第二组合并信息：子坯料号等字段
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          // 第一行默认占一行
          this.spanArr2.push(1)
          this.pos2[i] = 0
        } else {
          const row = data[i]
          const prevRow = data[i - 1]
          // 判断当前行与上一行的关键字段是否相同
          //依据子坯料号合并
          if (row.slabNo === prevRow.slabNo) {
            // 相同则合并：当前位置计数+1，新位置标记为0
            this.spanArr2[this.pos2[i - 1]] += 1
            this.spanArr2.push(0)
            this.pos2[i] = this.pos2[i - 1]
          } else {
            // 不同则新开一行
            this.spanArr2.push(1)
            this.pos2[i] = i
          }
        }
      }
    },

    // 表格合并单元格的具体实现方法
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 确保合并数组已经计算
      if (this.showGridData.length > 0 && this.spanArr1.length === 0) {
        this.getSpanArr(this.showGridData)
      }

      // 第一组列合并逻辑(0-2列)
      if (columnIndex >= 0 && columnIndex <= 2) {
        const _row = this.spanArr1[rowIndex] // 获取当前行的合并数
        const _col = _row > 0 ? 1 : 0 // 大于0显示，等于0隐藏
        return {
          rowspan: _row,
          colspan: _col
        }
      }

      // 第二组列合并逻辑(3-19列)
      if (columnIndex >= 3 && columnIndex <= 19) {
        const _row = this.spanArr2[rowIndex] // 获取当前行的合并数
        const _col = _row > 0 ? 1 : 0 // 大于0显示，等于0隐藏
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
// 大屏按钮
.screen-btn {
  display: inline-block;
  min-width: 68px;
  height: 28px;
  padding: 0 5px;
  background: rgba(31, 198, 255, 0.3);
  border: 1px solid #1fc6ff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  color: #fff;
  &:hover {
    background: rgba(31, 198, 255, 0.6);
    border: 1px solid #1fc6ff;
  }
}
.scroll-wrapper {
  height: 100%;
}
/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}
/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
/deep/.el-input--small .el-input__inner {
  height: 32px;
  line-height: 32px;
  background-color: #093d4d;
  color: #fff;
}
/deep/.el-range-editor--small .el-range-input {
  font-size: 13px;
  color: #fff;
}
/deep/.el-table__body-wrapper::-webkit-scrollbar {
  width: 26px; /* 设置滚动条的宽度 */
  height: 26px; /* 如果需要垂直滚动条也变粗，可以设置这个属性 */
}
.custom-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #909399;
}
.custom-empty i {
  font-size: 24px;
  margin-right: 10px;
}

/* 添加下面的样式使表格单元格文本左对齐 */
/deep/ .el-table .cell {
  text-align: left;
}
</style>
