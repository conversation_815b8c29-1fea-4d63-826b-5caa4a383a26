<template>
  <div
    class="full-height"
    style="overflow: hidden">
    <el-row
      :gutter="32"
      class="full-height">
      <el-col
        :span="14"
        class="full-height">
        <div class="content">
          <div class="content-item">
            <el-row
              :gutter="32"
              class="full-height">
              <el-col
                :span="12"
                class="full-height">
                <custom-table
                  :title="'麻面麻坑水波纹发生率'"
                  :setting="shape"
                  :url-list="shapeUrl.list"
                  :url-save="shapeUrl.save"
                  :select-date="selectDate"
                  :show-table="false"
                  @change="getShape">
                  <template v-slot:content>
                    <div class="chart-wrapper">
                      <div
                        class="operate-box text-right">
                        <el-radio-group
                          v-model="msChart.dateType"
                          size="mini"
                          class="screen-input"
                          @input="changeShape($event)">
                          <el-radio-button :label="0">日</el-radio-button>
                          <el-radio-button :label="1">月</el-radio-button>
                        </el-radio-group>
                      </div>
                      <div class="chart">
                        <single-bars-chart
                          ref="chart1"
                          :bar-width="35"
                          :show-label="true"
                          :show-legend="false"
                          :unit="'%'"
                          :chart-data="msChart.bar1"
                          :x-data="msChart.barX1"/>
                      </div>
                    </div>
                  </template>
                </custom-table>
              </el-col>
              <el-col
                :span="12"
                class="full-height">
                <custom-table
                  :title="'板形合格率'"
                  :setting="shapePass"
                  :url-list="shapePassUrl.list"
                  :url-save="shapePassUrl.save"
                  :select-date="prevDate"
                  :show-table="false"
                  :show-edit="false"
                  @change="getShapePass">
                  <template v-slot:content>
                    <div class="chart-wrapper">
                      <div
                        class="operate-box text-right">
                        <el-radio-group
                          v-model="shapeChart.dateType"
                          size="mini"
                          class="screen-input"
                          @input="changeShapePass($event)">
                          <el-radio-button :label="0">日</el-radio-button>
                          <el-radio-button :label="1">月</el-radio-button>
                        </el-radio-group>
                      </div>
                      <single-bars-chart
                        ref="chart3"
                        :show-legend="false"
                        :bar-width="35"
                        :chart-data="shapeChart.bar1"
                        :unit="'%'"
                        :x-data="shapeChart.barX1"/>
                    </div>
                  </template>
                </custom-table>
              </el-col>
            </el-row>
          </div>
          <div class="content-hold"/>
          <div class="content-item">
            <screen-border :title="'轧钢质量'">
              <template v-slot:headerRight>
                <el-radio-group
                  v-model="tableObj.dateType"
                  size="mini"
                  class="screen-input"
                  @input="getQuality($event)">
                  <el-radio-button :label="0">日</el-radio-button>
                  <el-radio-button :label="1">月</el-radio-button>
                </el-radio-group>
              </template>
              <div 
                ref="table1"
                class="scroll-wrapper">
                <el-table
                  v-loading="loading"
                  :data="tableObj.list"
                  :max-height="tableObj.maxHeight"
                  class="font-table center-table"
                  border>
                  <template
                    v-for="(item, index) in tableObj.setting">
                    <el-table-column
                      v-if="item.type === 'index'"
                      :key="index"
                      :label="item.label"
                      type="index"
                      width="100"
                    />
                    <template v-else>
                      <template v-if="item.inputType === 'textarea'">
                        <el-table-column
                          :key="index"
                          :width="item.width || ''"
                          :property="item.keySave"
                          :label="item.label"
                          :align="item.align">
                          <template v-slot="{ row }">
                            <div
                              slot="content"
                              v-html="formatText(row[item.keySave])"
                            />
                          </template>
                        </el-table-column>
                      </template>
                      <template v-else>
                        <el-table-column
                          :key="index"
                          :width="item.width || ''"
                          :property="item.keySave"
                          :label="item.label"
                          :align="item.align"/>
                      </template>
                    </template>
                  </template>
                </el-table>
              </div>
            </screen-border>
          </div>
        </div>
      </el-col>
      <el-col
        :span="10"
        class="full-height">
        <screen-border :title="'目标命中率'">
          <template v-slot:headerRight>
            <el-radio-group
              v-model="chartStackData.dateType"
              size="mini"
              class="screen-input"
              @input="getSteelRollingTarget($event)">
              <el-radio-button :label="0">日</el-radio-button>
              <el-radio-button :label="1">月</el-radio-button>
            </el-radio-group>
          </template>
          <div class="content">
            <div
              class="content-item">
              <div class="chart-wrapper">
                <div class="chart-tit">
                  目标命中率
                  <small>（实际平均温度在目标±10℃以内，≥目标正向命中，&lt;目标负向命中）</small>
                </div>
                <div
                  class="chart">
                  <bars-stack-chart
                    ref="chart1"
                    :bar-width="30"
                    :max="100"
                    :show-label="true"
                    :show-legend="true"
                    :unit="'%'"
                    :chart-data="chartStackData.bar1"
                    :x-data="chartStackData.bar1X"/>
                </div>
              </div>
            </div>
            <div
              style="height: 10px"
              class="content-hold"/>
            <div
              class="content-item">
              <div class="chart-wrapper">
                <div class="chart-tit">
                  最小值目标命中率
                  <small>
                    （单点温度最小值：终轧±40℃、返红±30℃以内）
                  </small>
                </div>
                <div
                  class="chart">
                  <bars-chart
                    ref="chart1"
                    :bar-width="30"
                    :max="100"
                    :show-label="true"
                    :show-legend="true"
                    :unit="'%'"
                    :chart-data="chartStackData.barMin"
                    :x-data="chartStackData.bar1X"/>
                </div>
              </div>
            </div>
            <div
              style="height: 10px"
              class="content-hold"/>
            <div
              class="content-item">
              <div class="chart-wrapper">
                <div class="chart-tit">
                  最大值目标命中率
                  <small>
                    （单点温度最大值：终轧±40℃、返红±30℃以内）
                  </small>
                </div>
                <div
                  class="chart">
                  <bars-chart
                    ref="chart1"
                    :bar-width="30"
                    :max="100"
                    :show-label="true"
                    :show-legend="true"
                    :unit="'%'"
                    :chart-data="chartStackData.barMax"
                    :x-data="chartStackData.bar1X"/>
                </div>
              </div>
            </div>
          </div>
        </screen-border>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post, replaceAll } from '@/lib/Util'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  findSteelRollingBySetDate,
  passRateFind,
  passRateSave,
  peakTargetHitRate
} from '@/api/screenTechnolagy'
import moment from 'moment'
import BarsChart from '@/pages/screen/technologyMeeting/component/bars-chart'
import PieRateChart from '@/pages/screen/qualityMeeting/component/pie-rate-chart'
import BarsStackChart from '@/pages/screen/technologyMeeting/component/bars-stack-chart'
import GaugeChart from '@/pages/screen/technologyMeeting/component/gauge-chart'
import LineChart from '@/pages/screen/technologyMeeting/component/line-chart'
import { math } from '@/lib/Math'
import CustomTable from '@/pages/screen/technologyMeeting/component/custom-table'
import SingleBarsChart from '@/pages/screen/technologyMeeting/component/single-bars-chart'
import { findShapePassByDate, saveShapePass } from '@/api/screen'

export default {
  name: 'CraftHitRoll',
  components: {
    SingleBarsChart,
    CustomTable,
    LineChart,
    GaugeChart,
    BarsStackChart,
    PieRateChart,
    BarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      shapeUrl: {
        save: passRateSave,
        list: passRateFind
      },
      shape: [
        {
          keyQuery: 'type',
          keySave: 'type',
          label: '类型'
        },
        {
          keyQuery: 'rollingMill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'value',
          keySave: 'value',
          label: '实际值'
        },
        {
          keyQuery: 'targetValue',
          keySave: 'targetValue',
          label: '目标值'
        },
        {
          keyQuery: 'month',
          keySave: 'month',
          label: '月数据',
          show: false
        },
        {
          keyQuery: 'description',
          keySave: 'description',
          label: '未完成原因'
        }
      ],
      shapeChart: {
        dataList: [],
        bar1: [],
        barX1: [],
        failReason: '',
        dateType: 0
      },
      shapePassUrl: {
        save: saveShapePass,
        list: findShapePassByDate
      },
      shapePass: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'value',
          keySave: 'Value',
          label: '实际值'
        },
        {
          keyQuery: 'targetvalue',
          keySave: 'targetValue',
          label: '目标值'
        },
        {
          keyQuery: 'description',
          keySave: 'description',
          label: '未完成原因'
        },
        {
          keyQuery: 'monthdata',
          keySave: 'monthData',
          label: '月数据',
          show: false
        }
      ],
      msChart: {
        bar1: [],
        barX1: ['C1', 'C2', 'C3'],
        failReason: '',
        dateType: 0
      },
      bxChart: {
        bar1: [],
        barX1: ['C1', 'C2', 'C3'],
        failReason: '',
        dateType: 0
      },
      chartStackData: {
        dateType: 0,
        bar1: [],
        barMin: [
          {
            name: 'C1',
            data: []
          },
          {
            name: 'C2',
            data: []
          },
          {
            name: 'C3',
            data: []
          }
        ],
        barMax: [
          {
            name: 'C1',
            data: []
          },
          {
            name: 'C2',
            data: []
          },
          {
            name: 'C3',
            data: []
          }
        ],
        bar1X: ['终轧', '返红']
      },
      chartData5: {
        bar1: [
          {
            name: 'C1',
            data: []
          },
          {
            name: 'C2',
            data: []
          },
          {
            name: 'C3',
            data: []
          }
        ],
        bar1X: ['二阶段', '终轧', '返红']
      },
      shapeList: [],
      tableObj: {
        list: [],
        dateType: 0,
        maxHeight: null,
        url: {
          save: findSteelRollingBySetDate,
          list: findSteelRollingBySetDate
        },
        setting: [
          {
            keyQuery: 'plt',
            keySave: 'plt',
            label: '产线',
            width: 80
          },
          {
            keyQuery: 'dayBuckLes',
            keySave: 'dayBuckLes',
            label: '瓢曲发生量',
            inputType: 'textarea'
          },
          {
            keyQuery: 'dayHempWater',
            keySave: 'dayHempWater',
            label: '麻水发生量',
            inputType: 'textarea'
          }
        ]
      }
    }
  },
  computed: {
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'day')
        .format('yyyy-MM-DD')
    },
    nextDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(-1, 'day')
        .format('yyyy-MM-dd')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.loadData()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.loadData()
  },
  mounted() {
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    loadData() {
      this.getSteelRollingTarget(this.chartStackData.dateType)
      this.getQuality(0)
    },

    // 轧钢质量
    getQuality(type) {
      post(this.tableObj.url.list, {
        setDate: this.cDate
      }).then(res => {
        this.tableObj.list = res.data.map(item => {
          item.monthHempWater = replaceAll(item.monthHempWater, ',', '\n')
          item.monthBuckLes = replaceAll(item.monthBuckLes, ',', '\n')
          item.dayHempWater = replaceAll(item.dayHempWater, ',', '\n')
          item.dayBuckLes = replaceAll(item.dayBuckLes, ',', '\n')
          return {
            dayHempWater: type === 0 ? item.dayHempWater : item.monthHempWater,
            dayBuckLes: type === 0 ? item.dayBuckLes : item.monthBuckLes,
            plt: item.plt
          }
        })
      })
    },

    // 目标命中
    getSteelRollingTarget(type) {
      const startTime =
        type == 0
          ? moment(this.prevDate).format('yyyyMMDD')
          : this.getResentMonth(this.prevDate).startTime
      const endTime = moment(this.prevDate).format('yyyyMMDD')
      // 极值目标命中率
      post(peakTargetHitRate, {
        startTime,
        endTime
      }).then(res => {
        // 目标命中率
        this.chartStackData.bar1 = [
          {
            name: 'C1 ≥目标值',
            color: '#2772f0',
            label: 'top',
            stack: 'C1',
            data: [res['终轧目标命中率C1上'], res['返红目标命中率C1上']]
          },
          {
            name: 'C1＜目标值',
            color: '#96bdff',
            label: 'bottom',
            stack: 'C1',
            data: [-res['终轧目标命中率C1下'], -res['返红目标命中率C1下']]
          },
          {
            name: 'C2 ≥目标值',
            color: '#f6cb6b',
            label: 'top',
            stack: 'C2',
            data: [res['终轧目标命中率C2上'], res['返红目标命中率C2上']]
          },
          {
            name: 'C2＜目标值',
            color: '#ffe9b9',
            label: 'bottom',
            stack: 'C2',
            data: [-res['终轧目标命中率C2下'], -res['返红目标命中率C2下']]
          },
          {
            name: 'C3 ≥目标值',
            color: '#71f0a1',
            label: 'top',
            stack: 'C3',
            data: [res['终轧目标命中率C3上'], res['返红目标命中率C3上']]
          },
          {
            name: 'C3＜目标值',
            color: '#b8ffd2',
            label: 'bottom',
            stack: 'C3',
            data: [-res['终轧目标命中率C3下'], -res['返红目标命中率C3下']]
          }
        ]

        // '二阶段', '终轧', '返红'
        this.chartStackData.barMin[0].data = [
          res['终轧最小值目标命中率C1'] || 0,
          res['返红最小值目标命中率C1'] || 0
        ]
        this.chartStackData.barMin[1].data = [
          res['终轧最小值目标命中率C2'] || 0,
          res['返红最小值目标命中率C2'] || 0
        ]
        this.chartStackData.barMin[2].data = [
          res['终轧最小值目标命中率C3'] || 0,
          res['返红最小值目标命中率C3'] || 0
        ]
        // '二阶段', '终轧', '返红'
        this.chartStackData.barMax[0].data = [
          res['终轧最大值目标命中率C1'] || 0,
          res['返红最大值目标命中率C1'] || 0
        ]
        this.chartStackData.barMax[1].data = [
          res['终轧最大值目标命中率C2'] || 0,
          res['返红最大值目标命中率C2'] || 0
        ]
        this.chartStackData.barMax[2].data = [
          res['终轧最大值目标命中率C3'] || 0,
          res['返红最大值目标命中率C3'] || 0
        ]
      })
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        if (![3, 4, 5].includes(index)) return (sums[index] = '')
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          sums[index] = ''
        }
      })
      if (sums[4]) {
        sums[5] = (100 - math.divide(sums[3], sums[4]) * 100).toFixed(2)
      }
      return sums
    },

    // 生成图表
    getShape(data) {
      this.shapeList = data
      this.changeShape(this.msChart.dateType)
    },
    calculate() {
      this.tableObj.maxHeight = this.$refs.table1.offsetHeight
    },
    changeShape(type) {
      const filterList = this.shapeList.filter(item => item.type === '麻水')
      this.msChart.barX1 = filterList.map(item => item.rollingMill)
      this.msChart.bar1 = filterList.map(item => {
        return {
          value:
            type === 0 ? Number(item['value']) : Number(item['month'] || 0),
          plan: Number(item.targetValue),
          finished:
            (type === 0 ? Number(item['value']) : Number(item['month'] || 0)) <=
            Number(item.targetValue)
        }
      })

      const filterList2 = this.shapeList.filter(item => item.type === '板形')
      this.bxChart.barX1 = filterList2.map(item => item.rollingMill)
      this.bxChart.bar1 = filterList2.map(item => {
        return {
          value:
            type === 0 ? Number(item['value']) : Number(item['month'] || 0),
          plan: Number(item.targetValue),
          finished:
            (type === 0 ? Number(item['value']) : Number(item['month'] || 0)) >=
            Number(item.targetValue)
        }
      })
    },
    getShapePass(data) {
      this.shapeChart.dataList = data
      this.changeShapePass(0)
    },
    changeShapePass($event) {
      const data = this.shapeChart.dataList
      this.shapeChart.bar1 = data.map(item => {
        return {
          value:
            $event === 0
              ? Number(item['Value'])
              : Number(item['monthData'] || 0),
          plan: Number(item.targetValue),
          finished:
            ($event === 0
              ? Number(item['Value'])
              : Number(item['monthData'] || 0)) >= Number(item.targetValue)
        }
      })
      this.shapeChart.barX1 = data.map(item => item.rollingMill)
      this.shapeChart.failReason =
        $event === 0
          ? data
              .filter(item => item.reasonNotCompl)
              .map(item => item.rollingMill + '：' + item.reasonNotCompl)
              .join('；')
          : ''
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.edit-btn {
  margin: 0 3px;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .chart-tit {
    font-size: 16px;
    font-weight: bolder;
    color: #ffffff;
    line-height: 20px;
    margin: 10px 0 5px;
    &:before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 6px;
      height: 100%;
      margin-right: 4px;
    }
  }
  .chart {
    flex: 1;
    height: 0;
  }
}
.dialog-item-tit {
  font-size: 18px;
  color: #dcdcdc;
  margin-bottom: 8px;
  margin-top: 15px;
}
</style>
