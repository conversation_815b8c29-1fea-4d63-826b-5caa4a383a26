<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="11"
          class="full-height">
          <quality-equipment
            :select-date="cDate"
            @dateChange="changeDate"/>
        </el-col>
        <el-col
          :span="7"
          class="full-height"
        >
          <screen-border title="跟班情况">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="attendant.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div
              ref="tableHeight"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="attendant.showGridData"
                :height="tableHeight"
                :row-class-name="rowClassName"
                class="font-table center-table"
                border>
                <el-table-column
                  property="A_LIST"
                  label="工序"
                  width="80"/>
                <el-table-column
                  property="D_LIST"
                  label="跟班钢种"
                  width="120">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.D_LIST)"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  property="B_LIST"
                  label="跟班人员履职情况"
                  width="160">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.B_LIST)"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  property="C_LIST"
                  label="工序异常"
                  width="150">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.C_LIST)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="6"
          class="full-height"
        >
          <screen-border title="炼钢生产控制">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="steelmaking.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="steelmaking.showGridData"
                :height="tableHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  property="A_LIST"
                  label="炼钢生产控制"/>
                <el-table-column
                  property="B_LIST"
                  label="品种钢">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.B_LIST)"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  property="C_LIST"
                  label="普通钢">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.C_LIST)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height"
        >
          <screen-border title="品种钢钢板质量">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="()=>{
                  varietySteelQuality.save = false;
                  varietySteelQuality.dialogVisible = true
                  getvarietySteelQuality()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明录入
              </span>
              <span
                class="screen-btn"
                @click="()=>{
                  varietySteelQuality.save = true;
                  varietySteelQuality.dialogVisible = true
                  getvarietySteelQuality()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明
              </span>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="varietyQuality.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>

            <div
              class="operate-box">
              <el-radio-group
                v-model="defectChartDateType"
                size="mini"
                class="screen-input">
                <el-radio-button :label="0">日</el-radio-button>
                <el-radio-button :label="1">月</el-radio-button>
              </el-radio-group>
            </div>
            <bars-chart
              :bar-width="30"
              :unit="'%'"
              :color="[
                '#2772f0',
                '#f5b544',
                '#51df81',
                '#edf173',
                '#e91e63',
                '#00bcd4',
                '#2196f3',
                '#3f51b5',
                '#9c27b0',
                '#009688',
              ]"
              :chart-data="defectChart.bar1"
              :x-data="defectChart.barX1"/>
          </screen-border>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <custom-table
            :title="'炼钢质量信息'"
            :setting="steelMaking"
            :url-list="steelMakingUrl.list"
            :url-save="steelMakingUrl.save"
            :select-date="selectDate"/>
        </el-col>
      </el-row>
    </div>
    <!--跟班情况-->
    <el-dialog
      :visible.sync="attendant.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="跟班情况">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('attendant')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importAttendant')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreviewAttendant"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportAttendant">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveAttendant">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          跟班情况
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="attendant.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            property="A_LIST"
            label="工序"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="D_LIST"
            label="跟班钢种"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.D_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            label="跟班人员履职情况">
            <template v-slot="{ row }">
              <el-input
                v-model="row.B_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="C_LIST"
            label="工序异常">
            <template v-slot="{ row }">
              <el-input
                v-model="row.C_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'attendant')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('attendant')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--品种钢质量-->
    <el-dialog
      :visible.sync="varietyQuality.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="品种钢钢板质量">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('varietyQuality')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importVarietyQuality')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreviewVarietyQuality"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportVarietyQuality">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveVarietyQuality">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          品种钢钢板质量
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="varietyQuality.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            property="A_LIST"
            label="项目">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            label="管线钢探伤情况"
          >
            <el-table-column
              property="k1"
              label="探伤不合">
              <template v-slot="{ row }">
                <el-input v-model="row.k1"/>
              </template>
            </el-table-column>
            <el-table-column
              property="k2"
              label="探伤量">
              <template v-slot="{ row }">
                <el-input v-model="row.k2"/>
              </template>
            </el-table-column>
            <el-table-column
              property="k3"
              label="探伤合格率">
              <template v-slot="{ row }">
                <el-input v-model="row.k3"/>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="抗酸管线钢探伤情况"
          >
            <el-table-column
              property="k4"
              label="探伤不合">
              <template v-slot="{ row }">
                <el-input v-model="row.k4"/>
              </template>
            </el-table-column>
            <el-table-column
              property="k5"
              label="探伤量">
              <template v-slot="{ row }">
                <el-input v-model="row.k5"/>
              </template>
            </el-table-column>
            <el-table-column
              property="k6"
              label="探伤合格率">
              <template v-slot="{ row }">
                <el-input v-model="row.k6"/>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="镍系钢探伤情况"
          >
            <el-table-column
              property="k7"
              label="探伤不合">
              <template v-slot="{ row }">
                <el-input v-model="row.k7"/>
              </template>
            </el-table-column>
            <el-table-column
              property="k8"
              label="探伤量">
              <template v-slot="{ row }">
                <el-input v-model="row.k8"/>
              </template>
            </el-table-column>
            <el-table-column
              property="k9"
              label="探伤合格率">
              <template v-slot="{ row }">
                <el-input v-model="row.k9"/>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'varietyQuality')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('varietyQuality')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--炼钢生产控制-->
    <el-dialog
      :visible.sync="steelmaking.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="炼钢生产控制">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('steelmaking')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importSteelmaking')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreviewSteelmaking"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportSteelmaking">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveSteelmaking">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          炼钢生产控制
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="steelmaking.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            property="A_LIST"
            label="炼钢生产控制"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            label="品种钢">
            <template v-slot="{ row }">
              <el-input
                v-model="row.B_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="C_LIST"
            label="普通钢">
            <template v-slot="{ row }">
              <el-input
                v-model="row.C_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'steelmaking')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('steelmaking')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
    <!--品种钢备注-->
    <el-dialog
      :visible.sync="varietySteelQuality.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="品种钢情况说明">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality&&!varietySteelQuality.save"
              class="screen-btn"
              @click="savevarietySteelQuality">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          品种钢情况说明
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="varietySteelQuality.gridData"
          class="font-table"
          border>
          <el-table-column
            property="A_LIST"
            label="内容">
            <template
              v-slot="{ row }"
              v-if="!varietySteelQuality.save">
              <el-input
                v-model="row.A_LIST"
                :rows="4"
                type="textarea"/>
            </template>
            <template
              v-slot="{ row }"
              v-else>
              <div
                slot="content"
                v-html="formatText(row.A_LIST)"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            width="210px"
            label="图片说明">
            <template v-slot="{ row, $index }">
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.B_LIST"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item.id"
                    :id="item.id"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !varietySteelQuality.save"
                    @img-delete="handlePasteImgDeleteID($event, index, 'varietySteelQuality')"
                  />
                </li>
              </ul>
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.showPic == unfinished ? [] : row.showPic"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item"
                    :src="item"
                    :is-id="false"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !varietySteelQuality.save"
                    @img-delete="handlePasteImgDelete($event, index, 'varietySteelQuality')"
                  />
                </li>
              </ul>
              <el-upload
                v-if="!varietySteelQuality.save"
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file)=>{
                  handleChange(file, 'varietySteelQuality', $index)
                }"
                :show-file-list="false"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = $index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import * as _ from 'lodash'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
// 质量设备
import QualityEquipment from '@/pages/screen/firstSteelmakingPlantTest/qualityEquipment'
// 柱状图
import BarsChart from '@/pages/screen/firstSteelmakingPlantTest/component/bars-chart'
// 表
import CustomTable from '../component/custom-table.vue'
import { post } from '@/lib/Util'
import { firstMorningMeeting, QualityQuery, QualitySave } from '@/api/screen'
// 上传、删除图片api
import { deleteFileByIds, uploadFile } from '@/api/system'
// 图片
import ImgView from '@/components/ImgView.vue'

export default {
  name: 'qualityProduction',
  components: {
    ImgView,
    ScreenBorder,
    QualityEquipment,
    BarsChart,
    CustomTable
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      tableHeight: null,
      editIndex: 0,
      unfinished: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      attendant: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      // 品种钢情况
      varietyQuality: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      steelmaking: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      // 品种钢情况说明
      varietySteelQuality: {
        gridData: [],
        showGridData: [],
        save: true,
        dialogVisible: false
      },
      // 初始化情况说明
      varietySteelQualityList: {
        C1日: {
          k1: 0,
          k2: 0,
          k3: 0,
          k4: 0,
          k5: 0,
          k6: 0,
          k7: 0,
          k8: 0,
          k9: 0
        },
        C2日: {
          k1: 0,
          k2: 0,
          k3: 0,
          k4: 0,
          k5: 0,
          k6: 0,
          k7: 0,
          k8: 0,
          k9: 0
        },
        C3日: {
          k1: 0,
          k2: 0,
          k3: 0,
          k4: 0,
          k5: 0,
          k6: 0,
          k7: 0,
          k8: 0,
          k9: 0
        },
        合计日: {
          k1: 0,
          k2: 0,
          k3: 0,
          k4: 0,
          k5: 0,
          k6: 0,
          k7: 0,
          k8: 0,
          k9: 0
        },
        C1月: {
          k1: 0,
          k2: 0,
          k3: 0,
          k4: 0,
          k5: 0,
          k6: 0,
          k7: 0,
          k8: 0,
          k9: 0
        },
        C2月: {
          k1: 0,
          k2: 0,
          k3: 0,
          k4: 0,
          k5: 0,
          k6: 0,
          k7: 0,
          k8: 0,
          k9: 0
        },
        C3月: {
          k1: 0,
          k2: 0,
          k3: 0,
          k4: 0,
          k5: 0,
          k6: 0,
          k7: 0,
          k8: 0,
          k9: 0
        },
        合计月: {
          k1: 0,
          k2: 0,
          k3: 0,
          k4: 0,
          k5: 0,
          k6: 0,
          k7: 0,
          k8: 0,
          k9: 0
        }
      },
      // 品种钢质量柱状图
      defectChart: {
        bar1: [
          {
            name: 'C1',
            data: [],
            barGap: '0.5'
          },
          {
            name: 'C2',
            data: [],
            barGap: '0.5'
          },
          {
            name: 'C3',
            data: [],
            barGap: '0.5'
          },
          {
            name: '合计',
            data: [],
            barGap: '0.5'
          }
        ],
        barX1: ['管线', '抗酸', '镍系']
      },
      // 切换radio
      defectChartDateType: 0,
      steelMakingUrl: {
        save: QualitySave,
        list: QualityQuery
      },
      steelMaking: [
        {
          keyQuery: 'castingnum',
          keySave: 'castingNum',
          label: '铸机号',
          width: '100',
          disabled: true
        },
        {
          keyQuery: 'qualitycondition',
          keySave: 'qualityCondition',
          label: '质量情况',
          inputType: 'textarea',
          align: 'left',
          disabled: true
        },
        {
          keyQuery: 'responsibilityunit',
          keySave: 'responsibilityUnit',
          label: '责任单位',
          inputType: 'textarea',
          align: 'left'
        },
        {
          keyQuery: 'situationdescription',
          keySave: 'situationDescription',
          label: '情况说明',
          inputType: 'textarea',
          align: 'left'
        }
      ],
      // 数据
      barData: {
        monthData: { C1: {}, C2: {}, C3: {}, 合计: {} },
        dayData: { C1: {}, C2: {}, C3: {}, 合计: {} }
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getAttendant({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '跟班情况'
      })
      this.getVarietyQuality({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '品种钢质量'
      })
      this.getSteelmaking({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '炼钢生产控制'
      })
      this.getvarietySteelQuality()
    },
    defectChartDateType: function() {
      this.defectChartDateType == 0
        ? this.watchdefectChart(this.barData.dayData)
        : this.watchdefectChart(this.barData.monthData)
    },
    barData: function() {
      this.defectChartDateType == 0
        ? this.watchdefectChart(this.barData.dayData)
        : this.watchdefectChart(this.barData.monthData)
    }
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.tableHeight = this.$refs.tableHeight.offsetHeight
  },
  methods: {
    // 跟班
    getAttendant(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.attendant.gridData = _.cloneDeep(resData)
        this.attendant.showGridData = _.cloneDeep(resData)
      })
    },
    saveAttendant() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '跟班情况',
        data: _.map(
          _.sortBy(this.attendant.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              FLAG: '跟班情况',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              SORT_NUM: index + 1
            }
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.attendant.dialogVisible = false
        this.getAttendant({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '跟班情况'
        })
        this.loading = false
      })
    },
    importAttendant(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: '跟班情况'
      }).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.attendant.gridData = _.cloneDeep(resData)
        this.$message.success('导入成功！')
      })
    },
    exportAttendant() {
      const data = [
        {
          SORT_NUM: '序号',
          D_LIST: '钢种',
          A_LIST: '工序',
          B_LIST: '跟班人员履职情况',
          C_LIST: '工序异常'
        }
      ].concat(
        _.map(_.cloneDeep(this.attendant.gridData), item => {
          let datas = {}
          _.forEach(['SORT_NUM', 'A_LIST', 'B_LIST', 'C_LIST'], items => {
            datas[items] = item[items]
          })
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `质量生产-跟班情况（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    handlePreviewAttendant(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          D_LIST: 'D',
          B_LIST: 'E',
          C_LIST: 'F'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.attendant.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    // 品种钢
    getVarietyQuality(data) {
      post(firstMorningMeeting.varietyStlqlyInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        let resData2 = _.map(resData, item => {
          return {
            ...item,
            ...JSON.parse(item.B_LIST)
          }
        })
        this.varietyQuality.gridData = _.cloneDeep(resData2)
        this.varietyQuality.showGridData = _.cloneDeep(resData2)
        this.getdefectChart(resData2)
      })
    },
    // 获取数据
    getdefectChart(data) {
      const monthData = { C1: {}, C2: {}, C3: {}, 合计: {} }
      const dayData = { C1: {}, C2: {}, C3: {}, 合计: {} }
      const bptData = [
        'C1日',
        'C2日',
        'C3日',
        '合计日',
        'C1月',
        'C2月',
        'C3月',
        '合计月'
      ]
      const dataObj1 = {}
      const dataObj2 = {}
      data.forEach(item => {
        dataObj1[item.A_LIST] = item
      })
      bptData.forEach(item => {
        dataObj2[item] =
          dataObj1[item] == undefined
            ? {
                k1: 0,
                k2: 0,
                k3: 0,
                k4: 0,
                k5: 0,
                k6: 0,
                k7: 0,
                k8: 0,
                k9: 0
              }
            : dataObj1[item]
      })
      console.log(dataObj2)
      this.varietySteelQualityList = _.cloneDeep(dataObj2)
      if (data.length) {
        const month = _.filter(data, item => item.A_LIST.includes('月'))
        const day = _.filter(data, item => item.A_LIST.includes('日'))
        _.forEach(['C1', 'C2', 'C3', '合计'], (items, indexs) => {
          _.forEach(month, item => {
            if (item.A_LIST.includes(items)) {
              monthData[items]['管线'] = item.k3 == undefined ? 0 : item.k3
              monthData[items]['抗酸'] = item.k6 == undefined ? 0 : item.k6
              monthData[items]['镍系'] = item.k9 == undefined ? 0 : item.k9
            }
          })
          _.forEach(day, item => {
            if (item.A_LIST.includes(items)) {
              dayData[items]['管线'] = item.k3 == undefined ? 0 : item.k3
              dayData[items]['抗酸'] = item.k6 == undefined ? 0 : item.k6
              dayData[items]['镍系'] = item.k9 == undefined ? 0 : item.k9
            }
          })
        })
      }
      this.barData = {
        monthData,
        dayData
      }
    },
    // 监听数据
    watchdefectChart(data) {
      _.forEach(this.defectChart.bar1, (item, index) => {
        item.data = _.map(['管线', '抗酸', '镍系'], items =>
          (data[item.name][items] * 100).toFixed(2)
        )
      })
    },
    saveVarietyQuality() {
      this.loading = true
      const params = _.map(
        _.sortBy(this.varietyQuality.gridData, item => item.SORT_NUM),
        (item, index) => {
          return {
            ...item,
            PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
            SORT_NUM: index + 1,
            B_LIST: JSON.stringify({
              k1: item.k1,
              k2: item.k2,
              k3: item.k3,
              k4: item.k4,
              k5: item.k5,
              k6: item.k6,
              k7: item.k7,
              k8: item.k8,
              k9: item.k9
            })
          }
        }
      )
      post(firstMorningMeeting.varietyStlqly, params).then(res => {
        this.varietyQuality.dialogVisible = false
        this.getVarietyQuality({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '品种钢质量'
        })
        this.loading = false
      })
    },
    importVarietyQuality(date) {
      post(firstMorningMeeting.varietyStlqlyInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: '品种钢质量'
      }).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.varietyQuality.gridData = _.cloneDeep(resData)
        this.$message.success('导入成功！')
      })
    },
    exportVarietyQuality() {
      const data = [
        {
          SORT_NUM: '序号',
          A_LIST: '项目',
          k1: '管线钢探伤不和',
          k2: '管线钢探伤量',
          k3: '管线钢钢探伤合格率',
          k4: '抗酸管线钢探伤不和',
          k5: '抗酸管线钢探伤量',
          k6: '抗酸管线钢探伤合格率',
          k7: '镍系钢探伤不和',
          k8: '镍系钢探伤量',
          k9: '镍系钢探伤合格率'
        }
      ].concat(
        _.map(_.cloneDeep(this.varietyQuality.gridData), item => {
          let datas = {}
          _.forEach(
            [
              'SORT_NUM',
              'A_LIST',
              'k1',
              'k2',
              'k3',
              'k4',
              'k5',
              'k6',
              'k7',
              'k8',
              'k9'
            ],
            items => {
              datas[items] = item[items]
            }
          )
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `品种钢（${this.cDate}晨会）.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },
    handlePreviewVarietyQuality(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          k1: 'D',
          k2: 'E',
          k3: 'F',
          k4: 'G',
          k5: 'H',
          k6: 'I',
          k7: 'J',
          k8: 'K',
          k9: 'L'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.varietyQuality.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    // 品种钢备注
    async savevarietySteelQuality() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 'varietySteelQuality',
        data: _.map(
          _.sortBy(this.varietySteelQuality.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              FLAG: 'varietySteelQuality',
              SORT_NUM: index + 1
            }
          }
        )
      }
      let del = null
      if (
        this.varietySteelQuality.gridData[0].delImage &&
        this.varietySteelQuality.gridData[0].delImage.length
      ) {
        del = await post(deleteFileByIds, {
          ids: this.varietySteelQuality.gridData[0].delImage
        })
      }
      if (del == null || del.success) {
        if (
          this.varietySteelQuality.gridData[0].file &&
          this.varietySteelQuality.gridData[0].file.length
        ) {
          const formData = new FormData()
          this.varietySteelQuality.gridData[0].file.forEach(item => {
            formData.append('files', item)
          })
          post(uploadFile, formData, false, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.success) {
              params.data[0].B_LIST = JSON.stringify([
                ...params.data[0].B_LIST,
                ...res.data
              ])

              post(firstMorningMeeting.furnaceCondition, params).then(res => {
                this.getvarietySteelQuality()
                this.$message.success('保存成功')
                this.loading = false
              })
            } else {
              this.$message.warning('图片上传失败！')
              this.loading = false
            }
          })
        } else {
          params.data[0].B_LIST = JSON.stringify(params.data[0].B_LIST)
          post(firstMorningMeeting.furnaceCondition, params).then(res => {
            this.getvarietySteelQuality()
            this.$message.success('保存成功')
            this.loading = false
          })
        }
      } else {
        this.$message.error('图片删除失败！！！')
      }
    },
    getvarietySteelQuality() {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'varietySteelQuality'
      }).then(res => {
        this.varietySteelQuality.gridData = _.cloneDeep(
          _.map(
            res.data.length
              ? res.data
              : [
                  Object.keys(this.varietySteelQualityList).length
                    ? {
                        A_LIST: `${this.$moment(this.cDate).format(
                          'MM月DD日'
                        )}，重点品种钢板探伤${(
                          this.varietySteelQualityList['合计日'].k2 * 1 +
                          this.varietySteelQualityList['合计日'].k5 * 1 +
                          this.varietySteelQualityList['合计日'].k8 * 1
                        ).toFixed(3)}吨，探伤不合${(
                          this.varietySteelQualityList['合计日'].k1 * 1 +
                          this.varietySteelQualityList['合计日'].k4 * 1 +
                          this.varietySteelQualityList['合计日'].k7 * 1
                        ).toFixed(3)}吨，探伤合格率${(
                          ((this.varietySteelQualityList['合计日'].k2 * 1 +
                            this.varietySteelQualityList['合计日'].k5 * 1 +
                            this.varietySteelQualityList['合计日'].k8 * 1 -
                            (this.varietySteelQualityList['合计日'].k1 * 1 +
                              this.varietySteelQualityList['合计日'].k4 * 1 +
                              this.varietySteelQualityList['合计日'].k7 * 1)) /
                            (this.varietySteelQualityList['合计日'].k2 * 1 +
                              this.varietySteelQualityList['合计日'].k5 * 1 +
                              this.varietySteelQualityList['合计日'].k8 * 1)) *
                          100
                        ).toFixed(3)}%。其中
                        1、管线钢探伤${
                          this.varietySteelQualityList['合计日'].k2
                        }吨，探伤不合${
                          this.varietySteelQualityList['合计日'].k1
                        }吨，探伤合格率${(
                          this.varietySteelQualityList['合计日'].k3 * 100
                        ).toFixed(3)}%，月累计探伤合格率${(
                          this.varietySteelQualityList['合计月'].k3 * 100
                        ).toFixed(3)}%；
                        2、抗酸管线钢探伤${
                          this.varietySteelQualityList['合计日'].k5
                        }吨，探伤不合${
                          this.varietySteelQualityList['合计日'].k4
                        }吨，探伤合格率${(
                          this.varietySteelQualityList['合计日'].k6 * 100
                        ).toFixed(3)}%，月累计探伤合格率${(
                          this.varietySteelQualityList['合计月'].k6 * 100
                        ).toFixed(3)}%；
                        3、镍系钢钢板探伤${
                          this.varietySteelQualityList['合计日'].k8
                        }吨，探伤不合${
                          this.varietySteelQualityList['合计日'].k7
                        }吨，探伤合格率${(
                          this.varietySteelQualityList['合计日'].k9 * 100
                        ).toFixed(3)}%，月累计探伤合格率${(
                          this.varietySteelQualityList['合计月'].k9 * 100
                        ).toFixed(3)}%；
                      `
                      }
                    : {}
                ],
            item => {
              return {
                ...item,
                B_LIST: item.B_LIST ? JSON.parse(item.B_LIST) : []
              }
            }
          )
        )
      })
    },
    // 炼钢
    getSteelmaking(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.steelmaking.gridData = _.cloneDeep(resData)
        this.steelmaking.showGridData = _.cloneDeep(resData)
      })
    },
    saveSteelmaking() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '炼钢生产控制',
        data: _.map(
          _.sortBy(this.steelmaking.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              FLAG: '炼钢生产控制',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              SORT_NUM: index + 1
            }
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.steelmaking.dialogVisible = false
        this.getSteelmaking({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '炼钢生产控制'
        })
        this.loading = false
      })
    },
    importSteelmaking(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: '炼钢生产控制'
      }).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.steelmaking.gridData = _.cloneDeep(resData)
        this.$message.success('导入成功！')
      })
    },
    exportSteelmaking() {
      const data = [
        {
          SORT_NUM: '序号',
          A_LIST: '炼钢生产控制',
          B_LIST: '品种钢',
          C_LIST: '普通钢'
        }
      ].concat(
        _.map(_.cloneDeep(this.steelmaking.gridData), item => {
          let datas = {}
          _.forEach(['SORT_NUM', 'A_LIST', 'B_LIST', 'C_LIST'], items => {
            datas[items] = item[items]
          })
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `质量生产-炼钢生产控制（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    handlePreviewSteelmaking(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          B_LIST: 'D',
          C_LIST: 'E'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.steelmaking.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    httpRequest(params) {},
    handleChange(file, row, index) {
      if (this[row].gridData[index].file == undefined) {
        this[row].gridData[index].file = [file.raw]
      } else {
        this[row].gridData[index].file.push(file.raw)
      }
      if (this[row].gridData[index].showPic == undefined) {
        this[row].gridData[index].showPic = [file.url]
      } else {
        this[row].gridData[index].showPic.push(file.url)
      }
      this[row] = _.cloneDeep(this[row])
    },
    handlePasteImgDelete(file, index, row) {
      this[row].gridData[0].file.splice(index, 1)
      this[row].gridData[0].showPic.splice(index, 1)
      this[row] = _.cloneDeep(this[row])
    },
    handlePasteImgDeleteID(file, index, row) {
      if (this[row].gridData[0].delImage == undefined) {
        this[row].gridData[0].delImage = [file.id]
      } else {
        this[row].gridData[0].delImage.push(file.id)
      }
      this[row].gridData[0].B_LIST.splice(index, 1)
      this[row] = _.cloneDeep(this[row])
    },
    //转%
    percentage(row, name) {
      return row[name] != undefined && row[name] != null
        ? row[name].toString().includes('%')
          ? row[name]
          : (row[name] * 100).toFixed(2) + '%'
        : row[name]
    },
    rowClassName({ row, rowIndex }) {
      if (
        row.C_LIST != '无' &&
        row.C_LIST != '' &&
        row.C_LIST != undefined &&
        row.C_LIST != null
      ) {
        return 'class_org'
      } else {
        return ''
      }
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
/deep/ .el-table .class_org {
  background: #f99f04;
  color: #000;
}
.operate-box {
  position: absolute;
  right: 20px;
  top: 10px;
  z-index: 5;
}
</style>
