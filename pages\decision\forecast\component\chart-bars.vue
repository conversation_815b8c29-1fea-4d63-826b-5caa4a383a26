<template>
  <div 
    :id="containerId"
    :style="{ height: '100%' }"/>
</template>

<script>
export default {
  name: 'chart-bars',
  props: {
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
      }
    },
    showLegend: {
      type: Boolean,
      default: false
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    transverse: {
      // 是否横向
      type: Boolean,
      default: false
    },
    barWidth: {
      type: Number,
      default: 0
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
      }
      const options = {
        tooltip: {
          show: this.showToolbox,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            console.log(params)
            const note = params[0].data.note
              ? params[0].data.note
                  .map(
                    item =>
                      `<span style="display: inline-block; width: 8px; height: 8px; vertical-align: middle; border-radius: 50%;"></span>
                        ${item.label}
                        &emsp;
                        ${item.value}`
                  )
                  .join('<br/>')
              : ''
            const str =
              params[0].name +
              '<br/>' +
              params
                .map(
                  item =>
                    `<span style="display: inline-block; width: 8px; height: 8px; vertical-align: middle; border-radius: 50%;background: ${
                      item.color
                    }"></span>
                        ${item.seriesName}
                        &emsp;
                        ${item.value}`
                )
                .join('<br/>') +
              '<br/>' +
              note
            return str
          }
        },
        color: this.color,
        legend: {
          show: this.showLegend,
          align: 'left',
          right: 0,
          padding: 0,
          icon: 'circle',
          textStyle: {
            color: '#000',
            fontSize: 12
          },
          itemHeight: 10, // 修改icon图形大小
          itemWidth: 10, // 修改icon图形大小
          itemGap: 3, // 修改间距
          itemStyle: {
            borderWidth: 0,
            padding: 0
          }
        },
        grid: {
          top: this.showLegend ? '18%' : '8%',
          left: '0%',
          right: '1%',
          bottom: '8%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'value',
            axisTick: { show: false },
            data: this.xData,
            axisLabel: {
              show: false,
              color: '#8590B3',
              fontSize: 12,
              interval: 0,
              rotate: this.labelRotate || 0
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0',
                width: 4
              }
            },
            splitLine: {
              show: false,
              color: ['#19be6b'],
              lineStyle: {
                width: 1
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'category',
            data: this.xData,
            axisTick: { show: false },
            axisLine: {
              show: true,
              color: ['#19be6b'],
              lineStyle: {
                width: 8,
                color: '#19be6b'
              }
            },
            axisLabel: {
              color: '#8590B3',
              fontSize: 12
            },
            splitLine: {
              color: ['#EAEBF0'],
              lineStyle: {
                show: false,
                type: 'dashed'
              }
            }
          }
        ],
        series: this.chartData.map(item => {
          return {
            name: item.name,
            type: 'bar',
            barGap: 0,
            barWidth: this.barWidth || 60 / this.chartData.length + '%',
            barMaxWidth: this.barWidth || 12,
            showBackground: this.barBackground,
            backgroundStyle: {
              color: 'rgba(232, 236, 239, 0.3)'
            },
            markPoint: {
              symbolSize: 5
            },
            label: {
              show: this.showLabel,
              color: '#fff',
              position: 'inside',
              fontSize: 12
            },
            data: item.data
          }
        })
      }
      this.myChart.setOption(options)
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;

  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;

    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }

    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
