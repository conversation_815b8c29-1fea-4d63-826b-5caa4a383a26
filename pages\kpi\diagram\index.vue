<template>

  <div
    class="page-content"
    style="height: 100%;position: relative">
    <el-row
      :gutter="30"
      class="row-bg"
      justify="start"
      type="flex"
      style="height: 100%;"
    >
      <el-col
        :span="6"
        style="height: 100%;"
      >
        <div class="tree-wrapper">
          <div>
            <el-select
              v-model="factory"
              :style="{width: '100%'}"
              size="small"
              clearable
              placeholder="请选择厂区"
              @change="loadData();getDiagram()"
            >
              <el-option
                v-for="(item, index) in factoryList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <el-tree
            ref="tree"
            :data="data"
            :props="defaultProps"
            highlight-current
            node-key="id"
            @node-click="handleNodeClick"
            @node-contextmenu="oncontextmenu"
          >
            <template
              v-slot="{node, data}">
              <span
                :class="{'first-node': node.level === 1}"
                class="custom-tree-node">
                <span>{{ node.label }}</span>
                <span
                  v-if="getShowStatus(data.parentId) && node.level !== 1">
                  <el-switch
                    :value="data.isShow"
                    @click.native.stop
                    @change="handleDelete($event, data)"/>
                </span>
              </span>

            </template>
          </el-tree>
        </div>

      </el-col>
      <el-col
        :span="18"
        style="height: 100%;">
        <div
          id="diagram"
          style="height: 100%;"
          @contextmenu.prevent.stop/>
      </el-col>
    </el-row>
    <div style="position: absolute; top: 0; right: 0; background: #fff; padding: 15px; text-align: right;border: 1px solid #eee">
      <el-radio-group v-model="layout">
        <el-radio-button label="force">网状</el-radio-button>
        <el-radio-button label="circular">环形</el-radio-button>
      </el-radio-group>
      <br>
      <div
        v-if="selected"
        style="line-height: 2; margin-top: 10px">
        已选节点：{{ selected ? selected.name : '' }}
        <br>
        <el-button
          size="mini"
          @click="cancelSelect()">取消选择</el-button>
        <el-button
          size="mini"
          type="primary"
          @click="showTree=true">查看详情</el-button>
      </div>

    </div>
    <Edit
      ref="modalForm"
      @success="updateNode"
    />
    <kpi-tree
      v-if="showTree"
      ref="modalKpi"
      v-model="showTree"
      :node="selected || {}"
    />
    <!-- 右键菜单 -->
    <ul
      v-show="rightMenuVisible"
      ref="rightMenu"
      :style="{ left: rightMenuLeft + 'px', top: rightMenuTop + 'px' }"
      class="contextmenu"
    >
      <li @click="rightAddNext()">添加下级KPI</li>
    </ul>
  </div>
    
</template>

<script>
import { post } from '@/lib/Util'
import {
  deleteDiagram,
  deleteKpiIndex,
  findKpiDiagram,
  findKpiTree,
  saveDiagram,
  updateIsShow
} from '@/api/kpi'
import Edit from './component/edit'
import { ENUM } from '@/lib/Constant'
import { findBySpecification } from '@/api/kpi'
import KpiTree from '@/pages/kpi/diagram/component/kpiTree'
export default {
  name: 'kpi-diagram',
  components: {
    KpiTree,
    Edit
  },
  data: () => {
    return {
      selected: null,
      chart: null,
      showData: {
        nodes: [],
        links: [],
        categories: []
      },
      chartOption: null,
      layout: 'force',
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: findBySpecification, //分页接口地址
        delete: deleteKpiIndex //删除接口地址
      },
      editUserId: null,
      data: [], // 树状数据
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      factory: 1,
      factoryList: ENUM.factoryListForecast,
      kpiFunction: ENUM.kpiFunction,
      levelList: ENUM.levelList,
      statusList: [
        {
          value: 0,
          label: '正常',
          type: 'success'
        },
        {
          value: 1,
          label: '废弃',
          type: 'warning'
        }
      ],
      rightMenuVisible: false,
      rightMenuLeft: 0,
      rightMenuTop: 0,
      rightMenuData: null,
      showTree: false,
      warningColor: '#ee6666'
    }
  },
  watch: {
    layout: function(value) {
      this.chart.setOption({
        series: [
          {
            layout: value,
            data: this.showData.nodes.map(item => {
              if (item.warningStatus === '1') {
                item.itemStyle = {
                  color: this.warningColor
                }
              }
              return Object.assign({}, item, {
                symbolSize: (
                  item.symbolSize / (this.layout === 'circular' ? 3 : 1)
                ).toFixed(0)
              })
            })
          }
        ]
      })
    },
    showData: {
      deep: true,
      handler() {
        this.showData.nodes.forEach(function(node) {
          node.symbolSize = 65 / Math.sqrt(node.rank)
          node.label = {
            show: node.symbolSize > 30
          }
        })
        this.chart.setOption({
          legend: [
            {
              // selectedMode: 'single',
              data: this.showData.categories.map(function(a) {
                return a.name
              })
            }
          ],
          series: [
            {
              data: this.showData.nodes.map(item => {
                if (item.warningStatus === '1') {
                  item.itemStyle = {
                    color: this.warningColor
                  }
                }
                return item
              }),
              links: this.showData.links,
              categories: this.showData.categories
            }
          ]
        })
      }
    },
    rightMenuVisible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.init()
      this.loadData()
    })
  },
  methods: {
    // 页面初始化
    async init() {
      //
      const chartDom = document.getElementById('diagram')
      this.chart = this.$echarts.init(chartDom)
      this.showData.nodes.forEach(function(node) {
        node.label = {
          show: node.symbolSize > 30
        }
      })
      this.chartOption = {
        title: {
          text: '指标关系图',
          subtext: 'Default layout',
          top: 'bottom',
          left: 'right'
        },
        color: [
          '#5470c6',
          '#91cc75',
          '#fac858',
          '#73c0de',
          '#3ba272',
          '#fc8452',
          '#9a60b4',
          '#ea7ccc',
          '#67d2ac'
        ],
        tooltip: {},
        legend: [
          {
            data: this.showData.categories.map(function(a) {
              return a.name
            })
          }
        ],
        animationDuration: 0,
        animationEasingUpdate: 'quinticInOut',
        series: [
          {
            name: '',
            type: 'graph',
            layout: this.layout,
            force: {
              repulsion: 280,
              edgeLength: 100
            },
            data: [],
            links: [],
            categories: [],
            roam: true,
            label: {
              position: 'right',
              formatter: '{b}'
            },
            lineStyle: {
              color: 'source',
              curveness: 0.3
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 10
              }
            }
          }
        ]
      }
      this.chart.setOption(this.chartOption)
      this.chart.on('click', async params => {
        if (params.dataType === 'node') {
          if (this.selected && this.selected.id !== params.data.id) {
            await this.generateRelation(this.selected, params.data)
          } else {
            this.selected = params.data
            this.selected.kid = params.data.id
          }
        }
      })
      this.chart.on('dblclick', async params => {
        params.data.source &&
          params.data.target &&
          this.removeRelation(params.data.source, params.data.target)
      })
      this.chart.on('contextmenu', async params => {
        console.log(params)
        params.event.event.preventDefault()
        if (params.dataType === 'node') {
          // params.data 显示详情
          //
          this.selected = params.data
          this.selected.kid = params.data.id
          this.showTree = true
        }
      })
      window.onresize = () => {
        this.chart.resize()
      }
      // 获取关系图数据
      await this.getDiagram()
    },
    async getDiagram() {
      this.selected = null
      this.chart.showLoading()
      const { data } = await post(findKpiDiagram, { factory: this.factory })
      this.showData = data
      this.chart.hideLoading()
    },
    // 生成联系
    async generateRelation(source, target) {
      this.$confirm(
        `是否确认建立 ${source.name} → ${target.name} 的链接?`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        // 建立链接
        const data = await post(saveDiagram, {
          sourceId: source.id,
          targetId: target.id
        })
        if (data.success) {
          this.showData.links.push({
            source: source.id,
            target: target.id
          })
          this.selected = null
        }
      })
    },
    // 删除联系
    removeRelation(source, target) {
      this.$confirm(`是否确认删除该链接?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除连线
        post(deleteDiagram, {
          targetId: target,
          sourceId: source
        }).then(res => {
          if (res.success) {
            const index = this.showData.links.findIndex(
              item => item.target === target && item.source === source
            )
            this.showData.links.splice(index, 1)
            this.selected = null
          }
        })
      })
    },

    async loadData() {
      const { data } = await post(findKpiTree, { factory: this.factory })
      // this.data = data
      const list = this.kpiFunction.map(item => {
        item.id = 'kpi' + item.value
        item.name = item.label
        return item
      })
      if (data) {
        this.data = list
          .map(item => {
            item.children = data.filter(kpi => kpi.feature == item.value)
            return item
          })
          .filter(item => item.children.length)
      }
    },
    async handleNodeClick(data) {
      this.closeMenu()
      this.highLightChart(data)
    },

    // 增加子节点
    handleAddChild(node, data) {
      console.log(node, data)
      this.$refs.modalForm.add()
      this.$nextTick(() => {
        this.$refs.modalForm.formData.pid = node.level === 0 ? '0' : data.id
        this.$refs.modalForm.formData.rank = data.rank ? data.rank + 1 : 1
        this.$refs.modalForm.formData.category =
          node.level === 0 ? null : data.category
        this.$refs.modalForm.formData.parentName = data.name
      })
      this.$refs.modalForm.visible = true
    },
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$nextTick(() => {
        this.$refs.modalForm.formData.parentName = row.name
      })
      this.$refs.modalForm.visible = true
      this.selected = null
    },
    rightAddNext() {
      //
      console.log(this.rightMenuData)
      this.handleAddChild(this.rightMenuData, this.rightMenuData.data)
    },
    rightAddBrother() {
      //
      this.handleAddChild(
        this.rightMenuData.parent,
        this.rightMenuData.parent.data
      )
    },
    rightMod() {
      //
      this.handleEdit(this.rightMenuData.data)
    },
    rightDel() {
      //
      this.handleDelete(this.rightMenuData.data)
    },
    oncontextmenu(e, data, node) {
      return
      this.rightMenuTop = e.clientY
      this.rightMenuLeft = e.clientX
      this.rightMenuVisible = true
      this.rightMenuData = node
    },
    closeMenu(e) {
      this.rightMenuVisible = false
      this.rightMenuData = null
    },

    // 更新节点
    updateNode(type, data) {
      console.log(data)
      // 根据编辑情况更新页面信息
      if (type === 'add') {
        if (data.pid === '0') {
          // 添加根节点
          this.data.push(data)
        } else {
          // 添加子节点
          this.$refs.tree.append(data, data.pid)
          this.showData.nodes.push(data)
          this.generateRelation(data.pid, data.id)
        }
      } else if (type === 'edit') {
        this.$refs.tree.getNode(data.id).data = data // 更新树数据
      }
    },

    // 删除节点
    handleDelete: function(e, data) {
      console.log(e, data)
      // 删除操作
      post(updateIsShow, { id: data.id, isShow: !data.isShow }).then(res => {
        if (res.success) {
          // this.$message.info('删除成功')
          // this.$refs.tree.remove(data)
          // this.showData.nodes = this.showData.nodes.filter(
          //   res => res.id !== data.id
          // )
          this.updateShowStatus(data, !data.isShow)
          this.$refs.tree.getNode(data.id).data = data // 更新树数据
          this.getDiagram()
        }
      })
    },

    // 更新显示状态
    updateShowStatus(data, status) {
      data.isShow = status
      const list = data.children
      list &&
        list.length &&
        list.forEach(item => {
          this.updateShowStatus(item, status)
        })
    },

    // 取消选择
    cancelSelect() {
      this.selected = null
    },

    getShowStatus(id) {
      const node = this.$refs.tree.getNode(id)
      if (node) {
        return node.data.isShow
      } else {
        return true
      }
    },
    highLightChart(data) {
      this.selected &&
        this.chart.dispatchAction({
          type: 'downplay',
          // 用 index 或 id 或 name 来指定系列。
          // 可以使用数组指定多个系列。
          seriesIndex: 0,
          name: [data.name]
        })
      if (
        this.showData.nodes.findIndex(item => item.name === data.name) !== -1
      ) {
        this.selected = data
        this.selected.kid = data.id
        this.chart.dispatchAction({
          type: 'highlight',
          // 用 index 或 id 或 name 来指定系列。
          // 可以使用数组指定多个系列。
          seriesIndex: 0,
          name: [data.name]
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
.page-title {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  margin-bottom: 15px;
}

.page-content {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  box-shadow: 0 0 10px rgba(117, 116, 116, 0.1);
  height: 100%;
}

.page-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .operate-icon {
    margin-left: 8px;
  }
}

.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}
.tree-wrapper {
  height: 100%;
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.first-node {
  font-size: 18px;
}
/deep/ .el-tree-node {
  margin: 5px 0;
}
/deep/ .el-tree > .el-tree-node {
  margin: 15px 0 12px;
}
.contextmenu {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  background: #fff;
  font-size: 14px;
  color: #666;
  border: 1px solid #e9e9e9;
  min-width: 250px;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
  li {
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #f4f4f5;
    }
  }
  li:last-child {
    border-top: 1px solid #e9e9e9;
  }
}
</style>
