<template>
  <div class="trackinfo">
    <div
      v-tag:tag="item"
      v-for="(item,index) in variable"
      :key="index"
      data-page="heatTrackInfo"/>
    <div class="five-box fiveorsix-box">
      <div class="fiveorsix-title">
        <div class="one">
          <p>5#热处理炉</p>
        </div>
        <div class="two bg-b">
          出钢时间预报：<i class="time-b">{{ forecastCurrentFiveOutShow[0] }}</i>min <i class="time-b">{{ forecastCurrentFiveOutShow[1] }}</i>s
        </div>
        <div class="three bg-b">
          装钢时间预报：<i class="time-b">{{ forecastCurrentFiveEnterShow[0] }}</i>min <i class="time-b">{{ forecastCurrentFiveEnterShow[1] }}</i>s
        </div>
      </div>
      <div class="bgbox five-bgbox">
        <div class="img_"/>
        <div class="pos-box">
          <div
            :class="[luStatusFive == 0?'lu-normal':'lu-stop']"
            class="lu-status "/>
          <div class="burner">
            <span
              v-for="(item, key) in burnerAllFive"
              v-if="item.num != 0"
              :key="key">
              <i>{{ item.num }}</i>
            </span>
          </div>
          <div class="steel-box">
            <el-tooltip
              v-for="(item, key) in trackDataFive"
              :key="key"
              effect="light"
              placement="top-start">
              <div
                slot="content"
                style="font-size: 14px">
                <p class="tooltip-p">板坯详情</p>
                <span class="tooltip-span">钢板号 <i class="tooltip-i">{{ item.MATID }}</i> </span>
                <span class="tooltip-span">钢种<i class="tooltip-i">{{ item.STEELGRADE }}</i> </span>
                <span class="tooltip-span">厚度 <i class="tooltip-i">{{ Number(item.THK).toFixed(3) }}</i> </span>
                <span class="tooltip-span">宽度 <i class="tooltip-i">{{ Number(item.WID).toFixed(3) }}</i> </span>
                <span class="tooltip-span">长度 <i class="tooltip-i">{{ Number(item.LEN).toFixed(3) }}</i> </span>
                <span class="tooltip-span">重量<i class="tooltip-i">{{ Number(item.WEIGHT).toFixed(3) }}</i> </span>
                <span class="tooltip-span">装钢时间 <i class="tooltip-i">{{ item.CHARGE_TIME }}</i> </span>
                <span class="tooltip-span"> 在炉时间（设定）<i class="tooltip-i">{{ item.SET_INFCE_TIME }}</i> </span>
                <span class="tooltip-span">在炉时间（实际） <i class="tooltip-i">{{ item.ACT_INFCE_TIME }}</i> </span>
                <span class="tooltip-span">装钢间距 <i class="tooltip-i">{{ Number(item.CHARGE_SPACE).toFixed(3) }}</i> </span>
                <span class="tooltip-span">保温时间（设定） <i class="tooltip-i">{{ item.SET_HEAT_TIME }}</i> </span>
                <span class="tooltip-span">进入保温区时长 <i class="tooltip-i">{{ item.ACT_HEAT_TIME }}</i> </span>
                <span class="tooltip-span">升温速率（设定） <i class="tooltip-i">{{ Number(item.SET_HEAT_RATE).toFixed(3) }}</i> </span>
              </div>
              <div
                :style="{
                  width: item.width+'px',
                  left:item.left +'px',
                }"
                class="steel-item">
                <span>
                  {{ item.MATID }}
                </span>
              </div>
            </el-tooltip>
          </div>

        </div>
      </div>
      <div class="table-box">
        <el-table
          :data="tableDataFive"
          style="width: 100%">
          <el-table-column
            prop="name"
            label="指标项"
            width="65px"
          />
          <el-table-column
            prop="key31"
            label="Z32"
            width="58px"
          />
          <el-table-column
            prop="key30"
            label="Z31"
            width="58px"
          />
          <el-table-column
            prop="key29"
            label="Z30"
            width="58px"
          />
          <el-table-column
            prop="key28"
            label="Z29"
            width="58px"
          />
          <el-table-column
            prop="key27"
            label="Z28"
            width="58px"
          />
          <el-table-column
            prop="key26"
            label="Z27"
            width="58px"
          />
          <el-table-column
            prop="key25"
            label="Z26"
            width="58px"
          />
          <el-table-column
            prop="key24"
            label="Z25"
            width="58px"
          />
          <el-table-column
            prop="key23"
            label="Z24"
            width="58px"
          />
          <el-table-column
            prop="key22"
            label="Z23"
            width="58px"
          />
          <el-table-column
            prop="key21"
            label="Z22"
            width="58px"
          />
          <el-table-column
            prop="key20"
            label="Z21"
            width="58px"
          />
          <el-table-column
            prop="key19"
            label="Z20"
            width="58px"
          />
          <el-table-column
            prop="key18"
            label="Z19"
            width="58px"
          />
          <el-table-column
            prop="key17"
            label="Z18"
            width="58px"
          />
          <el-table-column
            prop="key16"
            label="Z17"
            width="58px"
          />
          <el-table-column
            prop="key15"
            label="Z16"
            width="58px"
          />
          <el-table-column
            prop="key14"
            label="Z15"
            width="58px"
          />
          <el-table-column
            prop="key13"
            label="Z14"
            width="58px"
          />
          <el-table-column
            prop="key12"
            label="Z13"
            width="58px"
          />
          <el-table-column
            prop="key11"
            label="Z12"
            width="58px"
          />
          <el-table-column
            prop="key10"
            label="Z11"
            width="58px"
          />
          <el-table-column
            prop="key9"
            label="Z10"
            width="58px"
          />
          <el-table-column
            prop="key8"
            label="Z9"
            width="58px"
          />
          <el-table-column
            prop="key7"
            label="Z8"
            width="58px"
          />
          <el-table-column
            prop="key6"
            label="Z7"
            width="58px"
          />
          <el-table-column
            prop="key5"
            label="Z6"
            width="58px"
          />
          <el-table-column
            prop="key4"
            label="Z5"
            width="58px"
          />
          <el-table-column
            prop="key3"
            label="Z4"
            width="58px"
          />
          <el-table-column
            prop="key2"
            label="Z3"
            width="58px"
          />
          <el-table-column
            prop="key1"
            label="Z2"
            width="58px"
          />
          <el-table-column
            prop="key0"
            label="Z1"
            width="58px"
          />
        </el-table>
      </div>
    </div>

    <div class="six-box fiveorsix-box">
      <div class="fiveorsix-title">
        <div class="one">
          <p>6#热处理炉</p>
        </div>
        <div class="two bg-b">
          出钢时间预报：<i class="time-b">{{ forecastCurrentSixOutShow[0] }}</i>min <i class="time-b">{{ forecastCurrentSixOutShow[1] }}</i>s
        </div>
        <div class="three bg-b">
          装钢时间预报：<i class="time-b">{{ forecastCurrentSixEnterShow[0] }}</i>min <i class="time-b">{{ forecastCurrentSixEnterShow[1] }}</i>s
        </div>
      </div>
      <div class="bgbox six-bgbox">
        <div class="img_"/>
        <div class="pos-box">
          <div
            :class="[luStatusSix==0?'lu-normal':'lu-stop']"
            class="lu-status"
          />
          <div class="burner">
            <span
              v-for="(item, key) in burnerAllSix"
              v-if="item.num != 0"
              :key="key">
              <i>{{ item.num }}</i>
            </span>
          </div>
          <div class="steel-box">
            <el-tooltip
              v-for="(item, key) in trackDataSix"
              :key="key"
              effect="light"
              placement="top-start">
              <div
                slot="content"
                style="font-size: 14px">
                <p class="tooltip-p">板坯详情</p>
                <span class="tooltip-span">钢板号 <i class="tooltip-i">{{ item.MATID }}</i> </span>
                <span class="tooltip-span">钢种<i class="tooltip-i">{{ item.STEELGRADE }}</i> </span>
                <span class="tooltip-span">厚度 <i class="tooltip-i">{{ Number(item.THK).toFixed(3) }}</i> </span>
                <span class="tooltip-span">宽度 <i class="tooltip-i">{{ Number(item.WID).toFixed(3) }}</i> </span>
                <span class="tooltip-span">长度 <i class="tooltip-i">{{ Number(item.LEN).toFixed(3) }}</i> </span>
                <span class="tooltip-span">重量<i class="tooltip-i">{{ Number(item.WEIGHT).toFixed(3) }}</i> </span>
                <span class="tooltip-span">装钢时间 <i class="tooltip-i">{{ item.CHARGE_TIME }}</i> </span>
                <span class="tooltip-span"> 在炉时间（设定）<i class="tooltip-i">{{ item.SET_INFCE_TIME }}</i> </span>
                <span class="tooltip-span">在炉时间（实际） <i class="tooltip-i">{{ item.ACT_INFCE_TIME }}</i> </span>
                <span class="tooltip-span">装钢间距 <i class="tooltip-i">{{ Number(item.CHARGE_SPACE).toFixed(3) }}</i> </span>
                <span class="tooltip-span">保温时间（设定） <i class="tooltip-i">{{ item.SET_HEAT_TIME }}</i> </span>
                <span class="tooltip-span">进入保温区时长 <i class="tooltip-i">{{ item.ACT_HEAT_TIME }}</i> </span>
                <span class="tooltip-span">升温速率（设定） <i class="tooltip-i">{{ Number(item.SET_HEAT_RATE).toFixed(3) }}</i> </span>
              </div>
              <div
                :style="{
                  width: item.width+'px',
                  left:item.left +'px',
                }"
                class="steel-item">
                <span>
                  {{ item.MATID }}
                </span>
              </div>
            </el-tooltip>
          </div>

        </div>
      </div>
      <div class="table-box">
        <el-table
          :data="tableDataSix"
          style="width: 100%">
          <el-table-column
            prop="name"
            label="指标项"
            width="65px"
          />
          <el-table-column
            prop="key35"
            label="Z36"
            width="58px"
          />
          <el-table-column
            prop="key34"
            label="Z35"
            width="58px"
          />
          <el-table-column
            prop="key33"
            label="Z34"
            width="58px"
          />
          <el-table-column
            prop="key32"
            label="Z33"
            width="58px"
          />
          <el-table-column
            prop="key31"
            label="Z32"
            width="58px"
          />
          <el-table-column
            prop="key30"
            label="Z31"
            width="58px"
          />
          <el-table-column
            prop="key29"
            label="Z30"
            width="58px"
          />
          <el-table-column
            prop="key28"
            label="Z29"
            width="58px"
          />
          <el-table-column
            prop="key27"
            label="Z28"
            width="58px"
          />
          <el-table-column
            prop="key26"
            label="Z27"
            width="58px"
          />
          <el-table-column
            prop="key25"
            label="Z26"
            width="58px"
          />
          <el-table-column
            prop="key24"
            label="Z25"
            width="58px"
          />
          <el-table-column
            prop="key23"
            label="Z24"
            width="58px"
          />
          <el-table-column
            prop="key22"
            label="Z23"
            width="58px"
          />
          <el-table-column
            prop="key21"
            label="Z22"
            width="58px"
          />
          <el-table-column
            prop="key20"
            label="Z21"
            width="58px"
          />
          <el-table-column
            prop="key19"
            label="Z20"
            width="58px"
          />
          <el-table-column
            prop="key18"
            label="Z19"
            width="58px"
          />
          <el-table-column
            prop="key17"
            label="Z18"
            width="58px"
          />
          <el-table-column
            prop="key16"
            label="Z17"
            width="58px"
          />
          <el-table-column
            prop="key15"
            label="Z16"
            width="58px"
          />
          <el-table-column
            prop="key14"
            label="Z15"
            width="58px"
          />
          <el-table-column
            prop="key13"
            label="Z14"
            width="58px"
          />
          <el-table-column
            prop="key12"
            label="Z13"
            width="58px"
          />
          <el-table-column
            prop="key11"
            label="Z12"
            width="58px"
          />
          <el-table-column
            prop="key10"
            label="Z11"
            width="58px"
          />
          <el-table-column
            prop="key9"
            label="Z10"
            width="58px"
          />
          <el-table-column
            prop="key8"
            label="Z9"
            width="58px"
          />
          <el-table-column
            prop="key7"
            label="Z8"
            width="58px"
          />
          <el-table-column
            prop="key6"
            label="Z7"
            width="58px"
          />
          <el-table-column
            prop="key5"
            label="Z6"
            width="58px"
          />
          <el-table-column
            prop="key4"
            label="Z5"
            width="58px"
          />
          <el-table-column
            prop="key3"
            label="Z4"
            width="58px"
          />
          <el-table-column
            prop="key2"
            label="Z3"
            width="58px"
          />
          <el-table-column
            prop="key1"
            label="Z2"
            width="58px"
          />
          <el-table-column
            prop="key0"
            label="Z1"
            width="58px"
          />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getHmiData, stopGetData, sendMsg } from '@/lib/GetData'
import { post } from '@/lib/Util'
import moment from 'moment'
export default {
  name: 'trackinfo',

  filters: {
    forTimeG(val) {
      return moment(val, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')
    },
    formatSecondsToMMSS(seconds) {
      // 计算分钟
      const m = Math.floor(seconds / 60)
      // 计算剩余的秒数
      const s = seconds % 60

      // 格式化为两位数的字符串
      const mm = String(m).padStart(2, '0')
      const ss = String(s).padStart(2, '0')

      let arr = [mm, ss]
      // 拼接为 "mm:ss" 格式
      return arr
    }
  },
  data() {
    return {
      variable: [
        'rec_hlfce5_temp',
        'rec_hlfce6_temp',
        'rec_burner5_warm',
        'rec_burner6_warm',
        'rec_fce5_panel',
        'rec_fce6_panel',
        'HL_FCE_5_condition',
        'HL_FCE_6_condition'
      ],
      tableDataFive: [
        {
          name: '设定温度',
          key0: '1800℃',
          key1: '1800℃',
          key2: '800℃',
          key3: '800℃',
          key4: '800℃',
          key5: '800℃',
          key6: '800℃',
          key7: '800℃',
          key8: '800℃',
          key9: '800℃',
          key10: '800℃',
          key11: '800℃',
          key12: '800℃',
          key13: '800℃',
          key14: '800℃',
          key15: '800℃',
          key16: '800℃',
          key17: '800℃',
          key18: '800℃',
          key19: '800℃',
          key20: '800℃',
          key21: '800℃',
          key22: '800℃',
          key23: '800℃',
          key24: '800℃',
          key25: '800℃',
          key26: '800℃',
          key27: '800℃',
          key28: '800℃',
          key29: '800℃',
          key30: '800℃',
          key31: '800℃'
        },
        {
          name: '实际温度',
          key0: '800℃',
          key1: '800℃',
          key2: '800℃',
          key3: '800℃',
          key4: '800℃',
          key5: '800℃',
          key6: '800℃',
          key7: '800℃',
          key8: '800℃',
          key9: '800℃',
          key10: '800℃',
          key11: '800℃',
          key12: '800℃',
          key13: '800℃',
          key14: '800℃',
          key15: '800℃',
          key16: '800℃',
          key17: '800℃',
          key18: '800℃',
          key19: '800℃',
          key20: '800℃',
          key21: '800℃',
          key22: '800℃',
          key23: '800℃',
          key24: '800℃',
          key25: '800℃',
          key26: '800℃',
          key27: '800℃',
          key28: '800℃',
          key29: '800℃',
          key30: '800℃',
          key31: '800℃'
        }
      ],
      tableDataSix: [
        {
          name: '设定温度',
          key0: '1800℃',
          key1: '1800℃',
          key2: '800℃',
          key3: '800℃',
          key4: '800℃',
          key5: '800℃',
          key6: '800℃',
          key7: '800℃',
          key8: '800℃',
          key9: '800℃',
          key10: '800℃',
          key11: '800℃',
          key12: '800℃',
          key13: '800℃',
          key14: '800℃',
          key15: '800℃',
          key16: '800℃',
          key17: '800℃',
          key18: '800℃',
          key19: '800℃',
          key20: '800℃',
          key21: '800℃',
          key22: '800℃',
          key23: '800℃',
          key24: '800℃',
          key25: '800℃',
          key26: '800℃',
          key27: '800℃',
          key28: '800℃',
          key29: '800℃',
          key30: '800℃',
          key31: '800℃',
          key32: '1800℃',
          key33: '1800℃',
          key34: '1800℃',
          key35: '1800℃'
        },
        {
          name: '实际温度',
          key0: '800℃',
          key1: '800℃',
          key2: '800℃',
          key3: '800℃',
          key4: '800℃',
          key5: '800℃',
          key6: '800℃',
          key7: '800℃',
          key8: '800℃',
          key9: '800℃',
          key10: '800℃',
          key11: '800℃',
          key12: '800℃',
          key13: '800℃',
          key14: '800℃',
          key15: '800℃',
          key16: '800℃',
          key17: '800℃',
          key18: '800℃',
          key19: '800℃',
          key20: '800℃',
          key21: '800℃',
          key22: '800℃',
          key23: '800℃',
          key24: '800℃',
          key25: '800℃',
          key26: '800℃',
          key27: '800℃',
          key28: '800℃',
          key29: '800℃',
          key30: '800℃',
          key31: '800℃',
          key32: '1800℃',
          key33: '1800℃',
          key34: '1800℃',
          key35: '1800℃'
        }
      ],
      burnerAllFive: [],
      burnerAllSix: [],
      trackDataFive: [],
      trackDataSix: [],
      luStatusFive: 0,
      luStatusSix: 0,
      forecastCurrentFiveEnter: 0,
      forecastCurrentFiveOut: 0,
      forecastCurrentSixEnter: 0,
      forecastCurrentSixOut: 0,

      forecastCurrentFiveEnterShow: 0,
      forecastCurrentFiveOutShow: 0,
      forecastCurrentSixEnterShow: 0,
      forecastCurrentSixOutShow: 0,
      forecastTimer5: null,
      forecastTimer6: null
    }
  },
  mounted() {
    this.intBList()
    this.getTrackInfo()
    this.getForecastTime()
  },
  //钩子死亡清除定时器
  beforeDestroy() {
    stopGetData()
    clearInterval(this.forecastTimer5)
    clearInterval(this.forecastTimer6)
    this.forecastTimer5 = null
    this.forecastTimer6 = null
  },
  methods: {
    getForecastTime() {
      post('ht/HeatBasicData/findTimes', {
        fceId: '5'
      }).then(res => {
        if (res) {
          this.forecastCurrentFiveEnter = res.data.loadingForecast
          this.forecastCurrentFiveOut = res.data.tappingForecast
          if (
            this.forecastCurrentFiveEnter != '' &&
            this.forecastCurrentFiveOut != ''
          ) {
            this.countDown5()
          }
        }
      })
      post('ht/HeatBasicData/findTimes', {
        fceId: '6'
      }).then(res => {
        this.forecastCurrentSixEnter = res.data.loadingForecast
        this.forecastCurrentSixOut = res.data.tappingForecast
        if (
          this.forecastCurrentSixEnter != '' &&
          this.forecastCurrentSixOut != ''
        ) {
          this.countDown6()
        }
      })
    },
    //倒计时
    countDown5() {
      this.forecastTimer5 = setInterval(() => {
        if (
          this.forecastCurrentFiveEnter <= 0 &&
          this.forecastCurrentFiveOut <= 0
        ) {
          clearInterval(this.forecastTimer5)
        }
        if (this.forecastCurrentFiveEnter) {
          this.forecastCurrentFiveEnter--
        }
        if (this.forecastCurrentFiveOut) {
          this.forecastCurrentFiveOut--
        }
        //转换成min s
        this.forecastCurrentFiveEnterShow = this.formatSecondsToMMSS(
          this.forecastCurrentFiveEnter
        )
        this.forecastCurrentFiveOutShow = this.formatSecondsToMMSS(
          this.forecastCurrentFiveOut
        )
      }, 1000)
    },
    countDown6() {
      this.forecastTimer6 = setInterval(() => {
        if (
          this.forecastCurrentSixEnter <= 0 &&
          this.forecastCurrentSixOut <= 0
        ) {
          clearInterval(this.forecastTimer5)
        }
        if (this.forecastCurrentSixEnter) {
          this.forecastCurrentSixEnter--
        }
        if (this.forecastCurrentSixOut) {
          this.forecastCurrentSixOut--
        }
        //转换成min s
        this.forecastCurrentSixEnterShow = this.formatSecondsToMMSS(
          this.forecastCurrentSixEnter
        )
        this.forecastCurrentSixOutShow = this.formatSecondsToMMSS(
          this.forecastCurrentSixOut
        )
      }, 1000)
    },
    formatSecondsToMMSS(seconds) {
      // 计算分钟
      const m = Math.floor(seconds / 60)
      // 计算剩余的秒数
      const s = seconds % 60

      // 格式化为两位数的字符串
      const mm = String(m).padStart(2, '0')
      const ss = String(s).padStart(2, '0')

      let arr = [mm, ss]
      // 拼接为 "mm:ss" 格式
      return arr
    },
    getTrackInfo() {
      getHmiData(
        'heatTrackInfo',
        data => {
          this.getTemp(data['rec_hlfce5_temp'], data['rec_hlfce6_temp'])
          this.getBurner(data['rec_burner5_warm'], data['rec_burner6_warm'])
          this.trackSteel(data['rec_fce5_panel'], data['rec_fce6_panel'])
          this.getLuStatus(
            data['HL_FCE_5_condition'],
            data['HL_FCE_6_condition']
          )
          // stopGetData()
        },
        error => {
          console.log(error)
        },
        2000
      )
    },
    getLuStatus(fiveStatus, sixStatus) {
      this.luStatusFive = fiveStatus
      this.luStatusSix = sixStatus
    },
    getTemp(fiveTemp, sixTemp) {
      for (let i = 0; i < fiveTemp.length; i++) {
        this.tableDataFive[0]['key' + i] = fiveTemp[i].set_temp + '℃'
        this.tableDataFive[1]['key' + i] = fiveTemp[i].act_temp + '℃'
      }
      for (let i = 0; i < sixTemp.length; i++) {
        this.tableDataSix[0]['key' + i] = sixTemp[i].set_temp + '℃'
        this.tableDataSix[1]['key' + i] = sixTemp[i].act_temp + '℃'
      }
    },
    intBList() {
      for (let i = 0; i < 16; i++) {
        this.burnerAllFive.push({
          name: 'Z' + (2 * i + 1) + (2 * i + 2) + '',
          list: [],
          num: 0
        })
      }

      for (let i = 0; i < 18; i++) {
        this.burnerAllSix.push({
          name: 'Z' + (2 * i + 1) + (2 * i + 2) + '',
          list: [],
          num: 0
        })
      }
    },
    getBurner(fiveBurner, sixBurner) {
      //5#炉
      this.burnerAllFive[0].list = []
      this.burnerAllFive[1].list = []
      this.burnerAllFive[2].list = []
      this.burnerAllFive[3].list = []
      this.burnerAllFive[4].list = []
      this.burnerAllFive[5].list = []
      this.burnerAllFive[6].list = []
      this.burnerAllFive[7].list = []
      this.burnerAllFive[8].list = []
      this.burnerAllFive[9].list = []
      this.burnerAllFive[10].list = []
      this.burnerAllFive[11].list = []
      this.burnerAllFive[12].list = []
      this.burnerAllFive[13].list = []
      this.burnerAllFive[14].list = []
      this.burnerAllFive[15].list = []
      this.burnerAllFive[0].num = 0
      this.burnerAllFive[1].num = 0
      this.burnerAllFive[2].num = 0
      this.burnerAllFive[3].num = 0
      this.burnerAllFive[4].num = 0
      this.burnerAllFive[5].num = 0
      this.burnerAllFive[6].num = 0
      this.burnerAllFive[7].num = 0
      this.burnerAllFive[8].num = 0
      this.burnerAllFive[9].num = 0
      this.burnerAllFive[10].num = 0
      this.burnerAllFive[11].num = 0
      this.burnerAllFive[12].num = 0
      this.burnerAllFive[13].num = 0
      this.burnerAllFive[14].num = 0
      this.burnerAllFive[15].num = 0
      //组装数据
      for (let i = 0; i < fiveBurner.length; i++) {
        this.burnerAllFive[0].list.push(fiveBurner[i].burner1)
        this.burnerAllFive[0].list.push(fiveBurner[i].burner2)
        this.burnerAllFive[1].list.push(fiveBurner[i].burner3)
        this.burnerAllFive[1].list.push(fiveBurner[i].burner4)
        this.burnerAllFive[2].list.push(fiveBurner[i].burner5)
        this.burnerAllFive[2].list.push(fiveBurner[i].burner6)
        this.burnerAllFive[3].list.push(fiveBurner[i].burner7)
        this.burnerAllFive[3].list.push(fiveBurner[i].burner8)
        this.burnerAllFive[4].list.push(fiveBurner[i].burner9)
        this.burnerAllFive[4].list.push(fiveBurner[i].burner10)
        this.burnerAllFive[5].list.push(fiveBurner[i].burner11)
        this.burnerAllFive[5].list.push(fiveBurner[i].burner12)
        this.burnerAllFive[6].list.push(fiveBurner[i].burner13)
        this.burnerAllFive[6].list.push(fiveBurner[i].burner14)
        this.burnerAllFive[7].list.push(fiveBurner[i].burner15)
        this.burnerAllFive[7].list.push(fiveBurner[i].burner16)
        this.burnerAllFive[8].list.push(fiveBurner[i].burner17)
        this.burnerAllFive[8].list.push(fiveBurner[i].burner18)
        this.burnerAllFive[9].list.push(fiveBurner[i].burner19)
        this.burnerAllFive[9].list.push(fiveBurner[i].burner20)
        this.burnerAllFive[10].list.push(fiveBurner[i].burner21)
        this.burnerAllFive[10].list.push(fiveBurner[i].burner22)
        this.burnerAllFive[11].list.push(fiveBurner[i].burner23)
        this.burnerAllFive[11].list.push(fiveBurner[i].burner24)
        this.burnerAllFive[12].list.push(fiveBurner[i].burner25)
        this.burnerAllFive[12].list.push(fiveBurner[i].burner26)
        this.burnerAllFive[13].list.push(fiveBurner[i].burner27)
        this.burnerAllFive[13].list.push(fiveBurner[i].burner28)
        this.burnerAllFive[14].list.push(fiveBurner[i].burner29)
        this.burnerAllFive[14].list.push(fiveBurner[i].burner30)
        this.burnerAllFive[15].list.push(fiveBurner[i].burner31)
        this.burnerAllFive[15].list.push(fiveBurner[i].burner32)
      }
      //判断有几个故障烧嘴
      for (let i = 0; i < this.burnerAllFive.length; i++) {
        this.burnerAllFive[i].num = this.burnerAllFive[i].list.filter(
          item => item === 1
        ).length
      }

      //6#炉
      this.burnerAllSix[0].list = []
      this.burnerAllSix[1].list = []
      this.burnerAllSix[2].list = []
      this.burnerAllSix[3].list = []
      this.burnerAllSix[4].list = []
      this.burnerAllSix[5].list = []
      this.burnerAllSix[6].list = []
      this.burnerAllSix[7].list = []
      this.burnerAllSix[8].list = []
      this.burnerAllSix[9].list = []
      this.burnerAllSix[10].list = []
      this.burnerAllSix[11].list = []
      this.burnerAllSix[12].list = []
      this.burnerAllSix[13].list = []
      this.burnerAllSix[14].list = []
      this.burnerAllSix[15].list = []
      this.burnerAllSix[16].list = []
      this.burnerAllSix[17].list = []
      this.burnerAllSix[0].num = 0
      this.burnerAllSix[1].num = 0
      this.burnerAllSix[2].num = 0
      this.burnerAllSix[3].num = 0
      this.burnerAllSix[4].num = 0
      this.burnerAllSix[5].num = 0
      this.burnerAllSix[6].num = 0
      this.burnerAllSix[7].num = 0
      this.burnerAllSix[8].num = 0
      this.burnerAllSix[9].num = 0
      this.burnerAllSix[10].num = 0
      this.burnerAllSix[11].num = 0
      this.burnerAllSix[12].num = 0
      this.burnerAllSix[13].num = 0
      this.burnerAllSix[14].num = 0
      this.burnerAllSix[15].num = 0
      this.burnerAllSix[16].num = 0
      this.burnerAllSix[17].num = 0

      //组装数据
      for (let i = 0; i < sixBurner.length; i++) {
        this.burnerAllSix[0].list.push(sixBurner[i].burner1)
        this.burnerAllSix[0].list.push(sixBurner[i].burner2)
        this.burnerAllSix[1].list.push(sixBurner[i].burner3)
        this.burnerAllSix[1].list.push(sixBurner[i].burner4)
        this.burnerAllSix[2].list.push(sixBurner[i].burner5)
        this.burnerAllSix[2].list.push(sixBurner[i].burner6)
        this.burnerAllSix[3].list.push(sixBurner[i].burner7)
        this.burnerAllSix[3].list.push(sixBurner[i].burner8)
        this.burnerAllSix[4].list.push(sixBurner[i].burner9)
        this.burnerAllSix[4].list.push(sixBurner[i].burner10)
        this.burnerAllSix[5].list.push(sixBurner[i].burner11)
        this.burnerAllSix[5].list.push(sixBurner[i].burner12)
        this.burnerAllSix[6].list.push(sixBurner[i].burner13)
        this.burnerAllSix[6].list.push(sixBurner[i].burner14)
        this.burnerAllSix[7].list.push(sixBurner[i].burner15)
        this.burnerAllSix[7].list.push(sixBurner[i].burner16)
        this.burnerAllSix[8].list.push(sixBurner[i].burner17)
        this.burnerAllSix[8].list.push(sixBurner[i].burner18)
        this.burnerAllSix[9].list.push(sixBurner[i].burner19)
        this.burnerAllSix[9].list.push(sixBurner[i].burner20)
        this.burnerAllSix[10].list.push(sixBurner[i].burner21)
        this.burnerAllSix[10].list.push(sixBurner[i].burner22)
        this.burnerAllSix[11].list.push(sixBurner[i].burner23)
        this.burnerAllSix[11].list.push(sixBurner[i].burner24)
        this.burnerAllSix[12].list.push(sixBurner[i].burner25)
        this.burnerAllSix[12].list.push(sixBurner[i].burner26)
        this.burnerAllSix[13].list.push(sixBurner[i].burner27)
        this.burnerAllSix[13].list.push(sixBurner[i].burner28)
        this.burnerAllSix[14].list.push(sixBurner[i].burner29)
        this.burnerAllSix[14].list.push(sixBurner[i].burner30)
        this.burnerAllSix[15].list.push(sixBurner[i].burner31)
        this.burnerAllSix[15].list.push(sixBurner[i].burner32)
        this.burnerAllSix[16].list.push(sixBurner[i].burner33)
        this.burnerAllSix[16].list.push(sixBurner[i].burner34)
        this.burnerAllSix[17].list.push(sixBurner[i].burner35)
        this.burnerAllSix[17].list.push(sixBurner[i].burner36)
      }
      //判断有几个故障烧嘴
      for (let i = 0; i < this.burnerAllSix.length; i++) {
        this.burnerAllSix[i].num = this.burnerAllSix[i].list.filter(
          item => item === 1
        ).length
      }
    },

    trackSteel(fiveTrackInfo, sixTrackInfo) {
      /*
      * actLen: 辊道从头到尾实际长度
      * pageLen: 辊道从头到尾页面长度
      * */
      /*-------------------------------5#------------------------------------*/
      let [actLenFive, pageLenFive] = [170020, 1708]

      this.trackDataFive = []
      for (let i = 0; i < fiveTrackInfo.length; i++) {
        this.trackDataFive.push({
          width: this.countSteelLeng(
            actLenFive,
            pageLenFive,
            fiveTrackInfo[i].LEN
          ),
          left:
            pageLenFive -
            this.countSteelHead(
              actLenFive,
              pageLenFive,
              fiveTrackInfo[i].head_pos
            ), //从右向左 需要用pageLenFive减去
          ACT_HEAT_RATE: fiveTrackInfo[i].ACT_HEAT_RATE,
          ACT_INFCE_TIME: fiveTrackInfo[i].ACT_INFCE_TIME,
          CHARGE_INTER: fiveTrackInfo[i].CHARGE_INTER,
          CHARGE_TIME: fiveTrackInfo[i].CHARGE_TIME,
          DIS_PRED: fiveTrackInfo[i].DIS_PRED,
          LEN: fiveTrackInfo[i].LEN,
          MATID: fiveTrackInfo[i].MATID,
          SET_HEAT_RATE: fiveTrackInfo[i].SET_HEAT_RATE,
          SET_HEAT_TIME: fiveTrackInfo[i].SET_HEAT_TIME,
          SET_INFCE_TIME: fiveTrackInfo[i].SET_INFCE_TIME,
          THK: fiveTrackInfo[i].THK,
          WID: fiveTrackInfo[i].WID,
          head_pos: fiveTrackInfo[i].head_pos,
          CHARGE_SPACE: fiveTrackInfo[i].CHARGE_SPACE,
          STEELGRADE: fiveTrackInfo[i].STEELGRADE,
          WEIGHT: fiveTrackInfo[i].WEIGHT
        })
      }
      /*---------------------------6#---------------------------------------*/
      let [actLenSix, pageLenSix] = [175000, 1768]

      this.trackDataSix = []
      for (let i = 0; i < sixTrackInfo.length; i++) {
        this.trackDataSix.push({
          width: this.countSteelLeng(
            actLenSix,
            pageLenSix,
            sixTrackInfo[i].LEN
          ),
          left:
            pageLenSix -
            this.countSteelHead(
              actLenSix,
              pageLenSix,
              sixTrackInfo[i].head_pos
            ),
          ACT_HEAT_RATE: sixTrackInfo[i].ACT_HEAT_RATE,
          ACT_INFCE_TIME: sixTrackInfo[i].ACT_INFCE_TIME,
          CHARGE_INTER: sixTrackInfo[i].CHARGE_INTER,
          CHARGE_TIME: sixTrackInfo[i].CHARGE_TIME,
          DIS_PRED: sixTrackInfo[i].DIS_PRED,
          LEN: sixTrackInfo[i].LEN,
          MATID: sixTrackInfo[i].MATID,
          SET_HEAT_RATE: sixTrackInfo[i].SET_HEAT_RATE,
          SET_HEAT_TIME: sixTrackInfo[i].SET_HEAT_TIME,
          SET_INFCE_TIME: sixTrackInfo[i].SET_INFCE_TIME,
          THK: sixTrackInfo[i].THK,
          WID: sixTrackInfo[i].WID,
          head_pos: sixTrackInfo[i].head_pos,
          CHARGE_SPACE: sixTrackInfo[i].CHARGE_SPACE,
          STEELGRADE: sixTrackInfo[i].STEELGRADE,
          WEIGHT: sixTrackInfo[i].WEIGHT
        })
      }
    },
    //计算钢板长度 既是 在页面上用于div的width
    countSteelLeng(actLen, pageLen, mat_len) {
      return (pageLen * mat_len) / actLen
    },
    //计算钢板尾位置  既是 在页面上用于div的定位的left值
    countSteelHead(actLen, pageLen, head_x) {
      return (pageLen * head_x) / actLen
    }
  }
}
</script>

<style scoped lang="less">
.trackinfo /deep/.el-table td.el-table__cell div {
  padding-left: 4px !important;
  padding-right: 4px !important;
}
.trackinfo {
  color: #ffffff;
  margin-top: 0px;
  .five-box {
    margin-top: 4px;
    .img_ {
      width: 1721px;
      height: 88px;
      background: url('../../../../assets/images/screen/fiveBg.png') no-repeat;
    }
  }
  .six-box {
    margin-top: 14px;
    .img_ {
      width: 1838px;
      height: 88px;
      background: url('../../../../assets/images/screen/sixBg.png') no-repeat;
    }
  }
  .fiveorsix-box {
    width: 100%;
    //margin-top: 16px;
    .fiveorsix-title {
      padding: 0 20px;
      > div {
        float: left;
      }
      .one {
        //margin-left: 20px;
      }
      .two {
        margin-left: 20px;
      }
      .three {
        margin-left: 810px;
      }
      width: 100%;
      height: 32px;
      line-height: 32px;
      background-color: rgba(31, 199, 255, 0.2);
      .bg-b {
        padding: 0 2px;
        height: 32px;
        line-height: 32px;
        background-color: rgba(4, 26, 33, 1);
        border: 1px solid rgba(31, 198, 255, 1);
      }
      p {
        font-size: 20px;
        color: #ffffff;
        font-weight: bold;
      }
      .time-b {
        font-size: 24px;
        color: #ffb243;
        font-weight: bold;
      }
    }
    .bgbox {
      width: 100%;
      margin-top: 6px;
      /* .img_ {
        width: 1721px;
        height: 88px;
        background: url('@/assets/images/screen/fiveBg.png') no-repeat;
      }*/
    }
    .table-box {
      margin-top: 6px;
      /deep/ .el-table__row:nth-child(1) {
        color: rgba(85, 198, 212, 1);
      }
      /deep/ .el-table__row:nth-child(2) {
        color: rgba(51, 145, 255, 1);
      }
    }
  }

  .five-bgbox {
    position: relative;
    .lu-status {
      top: 23px;
      left: 257px;
      width: 875px;
      height: 48px;
      background-color: rgba(255, 178, 67, 1);
      opacity: 0.5;
      position: absolute;
    }
    .lu-stop {
      background-color: rgba(255, 178, 67, 1);
    }
    .lu-normal {
      background-color: rgba(31, 198, 255, 1);
    }
    .burner {
      > span {
        position: absolute;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        color: rgba(237, 45, 31, 1);
        display: inline-block;
        border: 1px solid rgba(237, 45, 31, 1);
        border-radius: 50%;
        cursor: pointer;
      }
      :nth-child(16) {
        top: 0px;
        left: 286px;
      }
      :nth-child(15) {
        top: 0px;
        left: 334px;
      }
      :nth-child(14) {
        top: 0px;
        left: 373px;
      }
      :nth-child(13) {
        top: 0px;
        left: 419px;
      }
      :nth-child(12) {
        top: 0px;
        left: 479px;
      }
      :nth-child(11) {
        top: 0px;
        left: 521px;
      }
      :nth-child(10) {
        top: 0px;
        left: 565px;
      }
      :nth-child(9) {
        top: 0px;
        left: 635px;
      }
      :nth-child(8) {
        top: 0px;
        left: 666px;
      }

      :nth-child(7) {
        top: 0px;
        left: 717px;
      }
      :nth-child(6) {
        top: 0px;
        left: 763px;
      }
      :nth-child(5) {
        top: 0px;
        left: 833px;
      }

      :nth-child(4) {
        top: 0px;
        left: 890px;
      }
      :nth-child(3) {
        top: 0px;
        left: 964px;
      }
      :nth-child(2) {
        top: 0px;
        left: 1007px;
      }
      :nth-child(1) {
        top: 0px;
        left: 1063px;
      }
    }
    .steel-item {
      position: absolute;
      left: 100px;
      top: 35px;
      width: 100px;
      height: 20px;
      line-height: 20px;
      font-size: 14px;
      background-color: rgba(237, 45, 31, 1);
      padding: 0 3px;
      text-align: center;
    }
  }
  .six-bgbox {
    position: relative;
    .lu-status {
      top: 23px;
      left: 82px;
      width: 1179px;
      height: 48px;
      background-color: rgba(255, 178, 67, 1);
      opacity: 0.5;
      position: absolute;
    }
    .lu-stop {
      background-color: rgba(255, 178, 67, 1);
    }
    .lu-normal {
      background-color: rgba(31, 198, 255, 1);
    }
    .burner {
      > span {
        position: absolute;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        color: rgba(237, 45, 31, 1);
        display: inline-block;
        border: 1px solid rgba(237, 45, 31, 1);
        border-radius: 50%;
        cursor: pointer;
      }
      :nth-child(18) {
        top: 0px;
        left: 111px;
      }
      :nth-child(17) {
        top: 0px;
        left: 171px;
      }
      :nth-child(16) {
        top: 0px;
        left: 243px;
      }
      :nth-child(15) {
        top: 0px;
        left: 296px;
      }
      :nth-child(14) {
        top: 0px;
        left: 341px;
      }
      :nth-child(13) {
        top: 0px;
        left: 410px;
      }
      :nth-child(12) {
        top: 0px;
        left: 475px;
      }
      :nth-child(11) {
        top: 0px;
        left: 536px;
      }
      :nth-child(10) {
        top: 0px;
        left: 594px;
      }

      :nth-child(9) {
        top: 0px;
        left: 678px;
      }
      :nth-child(8) {
        top: 0px;
        left: 718px;
      }
      :nth-child(7) {
        top: 0px;
        left: 773px;
      }

      :nth-child(6) {
        top: 0px;
        left: 841px;
      }
      :nth-child(5) {
        top: 0px;
        left: 896px;
      }
      :nth-child(4) {
        top: 0px;
        left: 956px;
      }
      :nth-child(3) {
        top: 0px;
        left: 1025px;
      }
      :nth-child(2) {
        top: 0px;
        left: 1085px;
      }
      :nth-child(1) {
        top: 0px;
        left: 1145px;
      }
    }
    .steel-item {
      position: absolute;
      left: 100px;
      top: 35px;
      width: 100px;
      height: 20px;
      line-height: 20px;
      font-size: 14px;
      background-color: rgba(237, 45, 31, 1);
      padding: 0 3px;
      text-align: center;
    }
  }
}
</style>
<style lang="less">
.el-tooltip__popper {
  background: #041a21 !important;
  border: 4px solid #0c4d63 !important;
  color: #ffffff;
  border-radius: 0;
  overflow: hidden;
  font-size: 12px;
  .tooltip-p {
    color: rgba(31, 198, 255, 1);
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 8px;
  }
  .tooltip-span {
    padding-bottom: 4px;
    display: block;
  }
  .tooltip-i {
    color: rgba(31, 198, 255, 1);
    float: right;
  }
}
</style>
