<template>
  <el-popover
    v-model="visible"
    placement="bottom"
    width="400"
    trigger="click">
    <div class="tree-box">
      <div class="tree-mode">
        选择模式
        <el-select
          v-model="mode"
          size="small"
          placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"/>
        </el-select>
        <el-button
          type="text"
          @click="handleReset"
        >重置</el-button>
      </div>
      <el-tree
        ref="orgTree"
        :data="data"
        :props="defaultProps"
        :check-strictly="mode"
        show-checkbox
        node-key="orgCode"
        @check-change="checkChange"
      />
    </div>

    <el-input
      slot="reference"
      v-model="form.orgName"
      :title="form.orgName"
      readonly
      placeholder="请选择机构"
    />
  </el-popover>
</template>

<script>
import { post } from '@/lib/Util'
import { allOrgList, allOrgListByCode } from '@/api/system'
import { ENUM } from '@/lib/Constant'

export default {
  name: 'SelectOrg',
  data() {
    return {
      visible: false,
      mode: false,
      options: [
        {
          value: false,
          label: '联动选择'
        },
        {
          value: true,
          label: '独立选择'
        }
      ],
      data: [], // 树状数据
      defaultProps: {
        children: 'children',
        label: 'orgAllName',
        isLeaf: 'leaf'
      },
      form: {
        orgCode: null,
        orgName: null
      }
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      const data = await post(allOrgList, {})
      this.data = this.getOrgData(data.data, 'X').filter(
        item => ENUM.orgTop.indexOf(item.orgCode) !== -1
      )
    },
    // async loadNode(node, resolve) {
    //   const data = await this.loadData(node.data.orgCode || 'X50000000')
    //   resolve(data)
    // },
    checkChange(selected) {
      console.log(this.$refs.orgTree.getCheckedNodes())
      const checked = this.$refs.orgTree.getCheckedNodes()
      this.form.orgCode = checked.map(item => item.orgCode)
      this.form.orgName = checked.map(item => item.orgAllName).join('、')
      // 推送变化
      this.$emit('on-change', this.form.orgCode)
    },
    /**
     * 根据后端全量列表生成树状菜单结构数据
     * @param orgList 需处理菜单列表
     * @param pid  父级id
     * @returns {*[]}
     */
    getOrgData(orgList = [], pid = '') {
      const data = []
      // 本次递归第一级菜单
      for (let item of orgList) {
        if (!item.parentOrgCode) item.parentOrgCode = ''
        if (item.parentOrgCode === pid) {
          data.push(item)
        }
      }
      // 本次递归二级菜单
      for (let item of data) {
        item.children = this.getOrgData(orgList, item.orgCode)
        if (!item.children.length) {
          delete item.children
        }
      }
      // console.log(data)
      return data
    },
    handleReset() {
      this.$refs.orgTree.setCheckedKeys([])
    }
  }
}
</script>

<style scoped>
.tree-box {
  max-height: 300px;
  overflow: auto;
}
.tree-mode {
  margin-bottom: 8px;
}
</style>
