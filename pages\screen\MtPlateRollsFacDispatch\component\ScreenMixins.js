/**
 * 看板 mixins
 */
import { post } from '@/lib/Util'
import moment from 'moment'
import { progressReportingSave } from '@/api/screenTechnolagy'

export default {
  data() {
    return {
      cDate: '',
      syncLoading: false,
      importDate: null,
      importDateVisible: false,
      importFunName: '',
      mergeArr: [],
      spanArr: {},
      position: 0,
      pltCode: {
        中厚板卷厂: 'C1',
        宽厚板厂: 'C2',
        中板厂: 'C3',
        第一炼钢厂: 'B1'
      },
      codePlt: {
        C1: '中厚板卷厂',
        C2: '宽厚板厂',
        C3: '中板厂',
        B1: '第一炼钢厂'
      }
    }
  },
  computed: {
    canEdit: function() {
      // console.log(moment().format('yyyy-MM-DD'), this.cDate)
      return (
        moment()
          .subtract(2, 'day')
          .format('yyyy-MM-DD') <= this.cDate
      )
    },
    canEditMonth: function() {
      // console.log(moment().format('yyyy-MM-DD'), this.cDate)
      return (
        moment().format('yyyy-MM') <= this.cDate ||
        moment()
          .subtract(1, 'days')
          .format('yyyy-MM') === this.cDate
      )
    },
    canEditQuality: function() {
      // console.log(moment().format('yyyy-MM-DD'), this.cDate)
      return (
        moment().format('yyyy-MM-DD') <=
        moment(this.cDate)
          .subtract(-1, 'day')
          .format('yyyy-MM-DD')
      )
    },
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'day')
        .format('yyyy-MM-DD')
    },
    nextDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(-1, 'day')
        .format('yyyy-MM-dd')
    }
  },
  created() {},
  methods: {
    // 执行导入
    importHistoryData() {
      this[this.importFunName](this.importDate)
      this.importDateVisible = false
    },
    // 下拉菜单指令
    handleProcessedCommand(command, funName) {
      if (command === 'yesterday') {
        this[funName](
          this.$moment(this.cDate)
            .subtract(1, 'day')
            .format('yyyy-MM-DD')
        )
      } else {
        this.importDate = this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
        this.importFunName = funName
        this.importDateVisible = true
      }
    },
    loadData() {},
    getResentMonth(date) {
      return {
        startTime:
          this.$moment(date)
            .subtract(this.$moment(date).format('DD') >= 26 ? 0 : 1, 'month')
            .format('yyyyMM') + '26',
        endTime: this.$moment(date).format('yyyyMMDD'),
        endMonthTime:
          this.$moment(date)
            .add(this.$moment(date).format('DD') >= 26 ? 1 : 0, 'month')
            .format('yyyyMM') + '25'
      }
    },
    // 同步数据
    SyncData(url) {
      console.log(url)
      this.$confirm(`同步数据后将会覆盖当前数据，是否确认同步?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.syncLoading = true
        post(url, {})
          .then(res => {
            if (res && res.status == 1) {
              this.$message.success('同步成功！')
              // this.getpilotPlan(type)
              // this.editIndex = null
              this.loadData()
            }
          })
          .catch(err => {
            console.log('err', err)
          })
      })
    },
    // 数据管理
    clearGridData(name) {
      this[name].gridData = []
    },
    addGridData(name) {
      this[name].gridData.push({})
    },
    delGridData(index, name) {
      console.log(name)
      this[name].gridData.splice(index, 1)
    },
    // 日期改变推送
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    // 计算需要合并的单元格
    formatSpanData(data) {
      this.mergeArr.forEach(keyName => {
        this.spanArr[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
    },
    // 合并单元格
    handleObjectSpan({ row, column, rowIndex, columnIndex }) {
      // 需要合并的列
      // [0, 1, 2].includes(columnIndex ), 表示合并前三列
      if (this.mergeArr.includes(column.property)) {
        const _row = this.spanArr[column.property][rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 生成带换行数据
    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },
    getFinishColorByName(name) {
      return name.indexOf('回收') !== -1 ? '#19BE6B' : '#FF2855'
    },
    getUnfinishColorByName(name) {
      return name.indexOf('回收') === -1 ? '#19BE6B' : '#FF2855'
    },
    getParam(name, list) {
      const match = list.find(item => item.parameter === name)
      return match ? match.content : null
    }
  }
}
