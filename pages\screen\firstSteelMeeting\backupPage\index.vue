<!--备件库存费用-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="9"
          class="full-height">
          <screen-border-multi :title="'库存动态储位统计'">
            <template v-slot:headerRight>
              <div style="display: flex;align-items: center">
                <div style="display: flex;">
                  <div style="margin: 0 10px">
                    <span>实际库存:</span>
                    <span style="color: red">{{ libData.fact }}万元</span>
                  </div>
                  <div style="margin: 0 10px">
                    <span>计划库存:</span>
                    <span>{{ libData.plan }}万元</span>
                  </div>
                </div>
                <span
                  class="screen-btn"
                  @click="LibData.dialogVisible = true">
                  <el-icon class="el-icon-edit-outline"/>
                  操作
                </span>
              </div>
            </template>
            <template v-slot:default>
              <div style="display: flex;flex-direction: column;width: 100%;height: 100%">
                <div class="chart-wrapper">
                  <div class="chart">
                    <first-steel-chart
                      v-loading="LibData.loading"
                      :chart-data="option1.series"
                      :color="['#3391FF','#66CC6A']"
                      :unit="'万元'"
                      :x-data="option1.xData"
                    />
                  </div>
                </div>
              </div>
            </template>
          </screen-border-multi>
        </el-col>
        <el-col
          :span="3"
          class="full-height">
          <screen-border-multi :title="'制造费用'">
            <template v-slot:default>
              <div style="display: flex;flex-direction: column;width: 100%;height: 100%">
                <div class="chart-wrapper">
                  <div class="chart">
                    <first-steel-chart
                      v-loading="LibData.loading"
                      :chart-data="option6.series"
                      :color="['#3391FF','#66CC6A']"
                      :unit="'元/吨'"
                      :x-data="option6.xData"
                    />
                  </div>
                </div>
              </div>
            </template>
          </screen-border-multi>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <screen-border-multi>
            <template v-slot:title>
              <div class="tabs-class">
                <div
                  v-for="(item, index) in tabList"
                  :key="item.id"
                  :class="{'tab-pane-active': item.active}"
                  class="tab-pane"
                  @click="clickTabPane(item, index)">
                  <div class="tab-pane-title-class">
                    <div>{{ item.title }}</div>
                    <div
                      v-if="item.active"
                      class="tab-pane-img">
                      <img
                        class="tab-pane-img2"
                        src="@/assets/images/screen/tab-pane-active-line2.png"
                        alt="">
                      <img
                        class="tab-pane-img1"
                        src="@/assets/images/screen/tab-pane-active-line.png"
                        alt="">
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-slot:headerRight>
              <div
                v-if="active === 1"
                style="display: flex;align-items: center">
                <div style="display: flex;">
                  <div style="margin: 0 10px">
                    <span>昨日转运总量：</span>
                    <span style="color: red">{{ Trans.yesT }}吨</span>
                  </div>
                  <div style="margin: 0 10px">
                    <span>截至昨日转运总量：</span>
                    <span>{{ Trans.curM }}吨</span>
                  </div>
                </div>
              </div>
              <div
                v-if="active === 2"
                style="display: flex;align-items: center">
                <div style="display: flex;">
                  <div style="margin: 0 10px">
                    <span>昨日切割总量：</span>
                    <span style="color: red">{{ lastAmount }}吨</span>
                  </div>
                  <div style="margin: 0 10px">
                    <span>截至昨日切割总量：</span>
                    <span :style="{color:(allAmount>5000?'red':(allAmount>3000&&allAmount<5000?'yellow':'white'))}">{{ allAmount }}吨</span>
                  </div>
                </div>
              </div>
            </template>
            <template v-slot:default>
              <el-row
                v-if="active === 0"
                :gutter="32"
                class="full-height">
                <el-col
                  :span="12"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div
                      class="chart">
                      <first-steel-chart
                        :title="'昨日'"
                        :chart-data="Billet.series1"
                        :color="['#3391FF','#66CC6A']"
                        :bar-width="30"
                        :unit="'吨'"
                        :x-data="Billet.xData"
                      />
                    </div>
                  </div>
                </el-col>
                <el-col
                  :span="12"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div
                      class="chart">
                      <first-steel-chart
                        :title="'本月'"
                        :chart-data="Billet.series2"
                        :color="['#3391FF','#66CC6A']"
                        :bar-width="30"
                        :unit="'吨'"
                        :x-data="Billet.xData"
                      />
                    </div>
                  </div>
                </el-col>
              </el-row>
              <template
                v-if="active === 1">
                <div class="chart-wrapper">
                  <div
                    class="chart">
                    <first-steel-chart
                      :chart-data="Trans.series1"
                      :color="['#3391FF','#66CC6A']"
                      :bar-width="30"
                      :unit="'吨'"
                      :x-data="Trans.xData"
                    />
                  </div>
                </div>
              </template>

              <!--氢氧切割图表-->
              <template
                v-if="active === 2">
                <div class="chart-wrapper">
                  <div
                    class="chart">
                    <first-steel-chart
                      v-loading="LibData.loading"
                      :chart-data="option5.series"
                      :color="['#3391FF','#66CC6A']"
                      :unit="'吨'"
                      :x-data="option5.xData"
                    />
                  </div>
                </div>
              </template>
            </template>

          </screen-border-multi>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <screen-border-multi :title="'吨钢费用趋势分析'">
            <template v-slot:headerRight>
              <div style="display: flex;align-items: center">
                <div style="display: flex;">
                  <div style="margin: 0 10px">
                    <span>实际吨钢:</span>
                    <span style="color: red">{{ costData.factAmt }}元/吨</span>
                  </div>
                  <div style="margin: 0 10px">
                    <span>挑战指标:</span>
                    <span>{{ costData.target }}元/吨</span>
                  </div>
                  <div style="margin: 0 10px">
                    <span>事业部指标:</span>
                    <span>{{ costData.ssbTarget }}元/吨</span>
                  </div>
                </div>
                <span
                  class="screen-btn"
                  @click="TunCostData.dialogVisible = true">
                  <el-icon class="el-icon-edit-outline"/>
                  操作
                </span>
              </div>

            </template>
            <template v-slot:default>
              <div class="chart-wrapper">
                <div class="chart">
                  <first-steel-chart
                    v-loading="TunCostData.loading"
                    :chart-data="option4.series"
                    :color="['#3391FF','#66CC6A','#FF9800']"
                    :unit="'元/吨'"
                    :x-data="option4.xData"
                  />
                </div>
              </div>
            </template>

          </screen-border-multi>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <screen-border-multi :title="'维修及生产费用完成情况'">
            <template v-slot:headerRight>
              <div style="display: flex;align-items: center">
                <div style="display: flex;">
                  <div style="margin: 0 10px">
                    <span>备材领用费用:</span>
                    <span style="color: red">{{ repairData.cost2 }}万元</span>
                  </div>
                  <div style="margin: 0 10px">
                    <span>预提费用:</span>
                    <span style="color: red">{{ repairData.cost }}万元</span>
                  </div>
                  <div style="margin: 0 10px">
                    <span>产量:</span>
                    <span>{{ repairData.planOutPut }}吨</span>
                  </div>
                </div>
                <span
                  class="screen-btn"
                  @click="RepairCostData.dialogVisible = true">
                  <el-icon class="el-icon-edit-outline"/>
                  操作
                </span>
              </div>
            </template>
            <div class="chart-wrapper">
              <div class="chart">
                <first-steel-chart
                  v-loading="RepairCostData.loading"
                  :chart-data="option2.series"
                  :color="['#3391FF','#66CC6A']"
                  :bar-width="20"
                  :unit="'万元'"
                  :x-data="option2.xData"
                />
              </div>
            </div>
          </screen-border-multi>
        </el-col>
      </el-row>
    </div>

    <!--库存动态储位统计 修改-->
    <el-dialog
      :visible.sync="LibData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="库存动态储位统计">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <span
              class="screen-btn"
              @click="exportLibData">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveLibData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          库存动态储位统计
        </div>
      </template>
      <el-form
        :disabled="!canEdit">
        <el-table
          v-loading="LibData.loading"
          :data="LibData.gridData"
          :height="'calc(100vh - 315px)'"
          border>
          <el-table-column
            property="setDate"
            label="日期">
            <template v-slot="{ row }">
              <el-input
                v-model="row.setDate"
                disabled />
            </template>
          </el-table-column>
          <el-table-column
            property="fact"
            label="实际">
            <template v-slot="{ row }">
              <el-input
                v-model="row.fact"
                disabled />
            </template>
          </el-table-column>
          <el-table-column
            property="plan"
            label="计划">
            <template v-slot="{ row }">
              <el-input v-model="row.plan" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <!--吨钢费用趋势分析 修改-->
    <el-dialog
      :visible.sync="TunCostData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="吨钢费用趋势分析">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <span
              class="screen-btn"
              @click="exportTunCostData">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveTunCostData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          吨钢费用趋势分析
        </div>
      </template>
      <el-form
        :model="costData"
        inline>
        <el-row>
          <el-col :span="8">
            <el-form-item label="日期:">
              <el-date-picker
                v-model="costData.date"
                :clearable="false"
                :size="'mini'"
                :value-format="'yyyy-MM-dd'"
                class="screen-input"
                @change="changeCostDate"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挑战指标:">
              <el-input v-model="costData.target"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="事业部指标:">
              <el-input v-model="costData.ssbTarget"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form
        :disabled="!canEdit">
        <el-table
          v-loading="TunCostData.loading"
          :data="TunCostData.gridData"
          :height="'calc(100vh - 315px)'"
          border>
          <el-table-column
            property="setDate"
            label="日期">
            <template v-slot="{ row }">
              <el-input
                v-model="row.date"
                disabled />
            </template>
          </el-table-column>
          <el-table-column
            property="fact"
            label="实际">
            <template v-slot="{ row }">
              <el-input
                v-model="row.factAmt"
                disabled />
            </template>
          </el-table-column>
          <el-table-column
            property="plan"
            label="挑战指标">
            <template v-slot="{ row }">
              <el-input
                v-model="row.target"
                disabled/>
            </template>
          </el-table-column>
          <el-table-column
            property="plan"
            label="事业部指标">
            <template v-slot="{ row }">
              <el-input
                v-model="row.ssbTarget"
                disabled/>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <!--维修及生产费用完成情况 修改-->
    <el-dialog
      :visible.sync="RepairCostData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="维修及生产费用完成情况">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <span
              class="screen-btn"
              @click="exportRepairCostData">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveRepairCostData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          维修及生产费用完成情况
        </div>
      </template>
      <el-form
        :disabled="!canEdit">
        <el-table
          v-loading="RepairCostData.loading"
          :data="RepairCostData.gridData"
          :height="'calc(100vh - 315px)'"
          border>
          <el-table-column
            property="workShop"
            label="车间">
            <template v-slot="{ row }">
              <el-input
                v-model="row.workShop"
                disabled />
            </template>
          </el-table-column>
          <el-table-column
            property="fact"
            label="实际">
            <template v-slot="{ row }">
              <el-input
                v-model="row.factCost"
                disabled />
            </template>
          </el-table-column>
          <el-table-column
            property="plan"
            label="计划">
            <template v-slot="{ row }">
              <el-input
                v-model="row.planCost"
                disabled/>
            </template>
          </el-table-column>
          <el-table-column
            property="target"
            label="指标">
            <template v-slot="{ row }">
              <el-input v-model="row.target" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { sumArr, getYesterday } from '@/lib/sum.js'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import FirstSteelChart from '@/pages/screen/firstSteelMeeting/component/first-steel-chart'
import FirstSteelPie from '@/pages/screen/firstSteelMeeting/component/first-steel-pie.vue'
import { post } from '@/lib/Util'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import {
  billetGrindingAmount,
  firstMeetingBackup2,
  firstMeetingBackup3,
  firstMeetingBackup4,
  firstMeetingBackup5,
  firstMeetingBackup6,
  firstMeetingBackup7,
  HOcut,
  transVolume,
  transVolumeTotali
} from '@/api/firstMeeting'
import lodash from 'lodash'
import moment from 'moment'
export default {
  name: 'backupPage',
  components: {
    ScreenBorder,
    ScreenBorderMulti,
    FirstSteelChart,
    FirstSteelPie
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      active: 0,
      lastAmount: '',
      allAmount: '',
      tabList: [
        {
          id: '1',
          active: true,
          title: '坯料修磨量'
        },
        {
          id: '2',
          active: false,
          title: '汽车转运量'
        },
        {
          id: '3',
          active: false,
          title: '氢氧切割量'
        }
      ],
      Billet: {
        xData: ['中厚板卷厂', '宽厚板厂', '宽厚板厂(剥皮)', '机械修磨'],
        series1: [
          {
            name: '坯料修磨量',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            label: {
              show: true,
              color: '#fff',
              position: 'top',
              fontSize: 16,
              offset: [0, 2]
            },
            data: []
          }
        ],
        series2: [
          {
            name: '坯料修磨量',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            label: {
              show: true,
              color: '#fff',
              position: 'top',
              fontSize: 16,
              offset: [0, 2]
            },
            data: []
          }
        ]
      },

      Trans: {
        yesT: null,
        curM: null,
        xData: [],
        series1: [
          {
            name: '转运量',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            label: {
              show: true,
              color: '#fff',
              position: 'top',
              fontSize: 16,
              offset: [0, 2]
            },
            data: []
          }
        ]
      },
      option1: {
        unit: '万元',
        xData: [
          '1',
          '2',
          '3',
          '4',
          '5',
          '6',
          '7',
          '8',
          '9',
          '10',
          '11',
          '12',
          '13',
          '14',
          '15',
          '16',
          '17',
          '18',
          '19',
          '20',
          '21',
          '22',
          '23',
          '24',
          '25',
          '26',
          '27',
          '28',
          '29',
          '30'
        ],
        series: [
          {
            name: '计划',
            type: 'line',
            yAxisIndex: 0,
            barGap: 0,
            data: []
          },
          {
            name: '实际完成',
            type: 'line',
            yAxisIndex: 0,
            barGap: 0,
            smooth: true,
            data: []
          }
        ]
      },
      option2: {
        xData: [
          '运行车间',
          '炼钢车间',
          '原料车间',
          '连铸车间',
          '精炼车间',
          '管理中心',
          '预提费用'
        ],
        series: [
          {
            name: '计划',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: []
          },
          {
            name: '实际',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            smooth: true,
            data: []
          }
        ]
      },
      option4: {
        unit: '元/吨',
        xData: [
          '1',
          '2',
          '3',
          '4',
          '5',
          '6',
          '7',
          '8',
          '9',
          '10',
          '11',
          '12',
          '13',
          '14',
          '15',
          '16',
          '17',
          '18',
          '19',
          '20',
          '21',
          '22',
          '23',
          '24',
          '25',
          '26',
          '27',
          '28',
          '29',
          '30'
        ],
        series: [
          {
            name: '实际',
            type: 'line',
            lineStyle: {
              type: 'solid'
            },
            smooth: true,
            data: []
          },
          {
            name: '挑战指标',
            type: 'line',
            lineStyle: {
              type: 'dashed'
            },
            smooth: true,
            data: []
          },
          {
            name: '事业部指标',
            type: 'line',
            lineStyle: {
              type: 'dashed'
            },
            smooth: true,
            data: []
          }
        ]
      },
      option5: {
        unit: '吨',
        xData: [
          '1',
          '2',
          '3',
          '4',
          '5',
          '6',
          '7',
          '8',
          '9',
          '10',
          '11',
          '12',
          '13',
          '14',
          '15',
          '16',
          '17',
          '18',
          '19',
          '20',
          '21',
          '22',
          '23',
          '24',
          '25',
          '26',
          '27',
          '28',
          '29',
          '30'
        ],
        series: [
          {
            name: '切割量',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            label: {
              show: true,
              color: '#fff',
              position: 'top',
              fontSize: 16,
              offset: [0, 2]
            },
            data: []
          }
        ]
      },
      option6: {
        unit: '元/吨',
        xData: ['实际', '计划'],
        series: [
          {
            name: '',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            label: {
              show: true,
              color: '#fff',
              position: 'top',
              fontSize: 16,
              offset: [0, 2]
            },
            data: [91, 93]
          }
        ]
      },
      LibData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      TunCostData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      RepairCostData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      libData: {
        setDate: '',
        plan: '',
        fact: ''
      },
      costData: {
        date: '',
        target: '',
        ssbTarget: ''
      },
      repairData: {
        date: '',
        cost: '',
        cost2: '',
        planOutPut: ''
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.$nextTick(() => {
        this.init()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getLibData()
      this.getRepairCostData()
      this.getTunCostData()
      this.getBilletGrindingAmount()
      this.getTransVolumeTotali()
      this.getHOcut()
    },
    clickTabPane(item, index) {
      this.tabList.forEach(item => {
        item.active = false
      })
      item.active = true
      this.active = index
    },
    getBilletGrindingAmount() {
      const recentMonth = this.getResentMonth(this.cDate)
      Promise.all([
        // 板卷 日
        post(billetGrindingAmount, {
          startTime: recentMonth.endTime + '000000',
          endTime: recentMonth.endTime + '235959',
          empCd: '0009366'
        }),
        // 板卷 月
        post(billetGrindingAmount, {
          startTime: recentMonth.startTime + '000000',
          endTime: recentMonth.endTime + '235959',
          empCd: '0009366'
        }),
        // 宽板 日
        post(billetGrindingAmount, {
          startTime: recentMonth.endTime + '000000',
          endTime: recentMonth.endTime + '235959',
          empCd: '0002964'
        }),
        // 宽板 月
        post(billetGrindingAmount, {
          startTime: recentMonth.startTime + '000000',
          endTime: recentMonth.endTime + '235959',
          empCd: '0002964'
        }),
        // 宽板(剥皮) 日
        post(billetGrindingAmount, {
          startTime: recentMonth.endTime + '000000',
          endTime: recentMonth.endTime + '235959',
          empCd: '0002964',
          xmType: '04'
        }),
        // 宽板（剥皮） 月
        post(billetGrindingAmount, {
          startTime: recentMonth.startTime + '000000',
          endTime: recentMonth.endTime + '235959',
          empCd: '0002964',
          xmType: '04'
        }),
        // 机械修磨 日
        post(billetGrindingAmount, {
          startTime: recentMonth.endTime + '000000',
          endTime: recentMonth.endTime + '235959',
          empCd: '0000543'
        }),
        // 机械修磨 月
        post(billetGrindingAmount, {
          startTime: recentMonth.startTime + '000000',
          endTime: recentMonth.endTime + '235959',
          empCd: '0000543'
        })
      ]).then(res => {
        this.Billet.series1[0].data = [
          res[0].data.rows[0].WGT.toFixed(3),
          (res[2].data.rows[0].WGT - res[4].data.rows[0].WGT).toFixed(3),
          res[4].data.rows[0].WGT.toFixed(3),
          res[6].data.rows[0].WGT.toFixed(3)
        ]
        this.Billet.series2[0].data = [
          res[1].data.rows[0].WGT.toFixed(3),
          (res[3].data.rows[0].WGT - res[5].data.rows[0].WGT).toFixed(3),
          res[5].data.rows[0].WGT.toFixed(3),
          res[7].data.rows[0].WGT.toFixed(3)
        ]
      })
    },
    getTransVolumeTotali() {
      const recentMonth = this.getResentMonth(this.cDate)
      Promise.all([
        // 昨日总量
        post(transVolume, {
          startTime: recentMonth.endTime,
          endTime: recentMonth.endTime
        }),
        // 截至昨日总量
        post(transVolume, {
          startTime: recentMonth.startTime,
          endTime: recentMonth.endTime
        })
      ]).then(res => {
        this.Trans.yesT = res[0].data.rows[0].WGT
        this.Trans.curM = res[1].data.rows[0].WGT
      })

      const TotaliArr = [
        '过跨车故障',
        '行车故障',
        '双机生产',
        '异常超重(长坯料)',
        '裂纹修磨',
        {
          frInv: 'ZB',
          toInv: '00'
        },
        {
          frInv: 'ZB',
          toInv: 'HB'
        },
        {
          frInv: 'HB',
          toInv: '00'
        }
      ]
      this.Trans.xData = TotaliArr.map(item => {
        return item.frInv ? item.frInv + '-' + item.toInv : item
      })
      const promiseArr = TotaliArr.map(item => {
        return post(transVolume, {
          startTime: recentMonth.endTime,
          endTime: recentMonth.endTime,
          remark: item.frInv ? '' : item,
          frInv: item.frInv ? item.frInv : '',
          toInv: item.toInv ? item.toInv : ''
        })
      })
      Promise.all(promiseArr).then(res => {
        this.Trans.series1[0].data = res.map(item => item.data.rows[0].WGT)
      })
    },
    //库存数据查询
    getLibData() {
      const params = {
        setDate: this.cDate
      }
      this.LibData.loading = true
      post(firstMeetingBackup3, params)
        .then(res => {
          if (res.success) {
            let planList = [] //计划
            let factList = [] //实际
            let xData = []
            let list = []
            res.data.plan.forEach(item => {
              planList.push(
                item.endAmt === null || item.endAmt.length === 0
                  ? ''
                  : (parseFloat(item.endAmt) / 10000).toFixed(2)
              )
              xData.push(item.setDate.substring(8, item.setDate.length))
              list.push({
                setDate: item.setDate,
                planId: item.id,
                plan: item.endAmt === null ? '' : item.endAmt,
                fact: '',
                factId: ''
              })
            })
            res.data.fact.forEach(item => {
              factList.push(
                item.endAmt === null || item.endAmt.length === 0
                  ? ''
                  : (parseFloat(item.endAmt) / 10000).toFixed(2)
              )
              const lItem = list.find(lItem => lItem.setDate === item.setDate)
              if (lItem) {
                lItem.fact = item.endAmt === null ? '' : item.endAmt
                lItem.factId = item.id
              }
            })
            list.forEach(item => {
              if (item.setDate === this.cDate) {
                let fact =
                  item.fact == null || item.fact.length == 0
                    ? ''
                    : (parseFloat(item.fact) / 10000).toFixed(2)
                let plan =
                  item.plan === null || item.plan.length === 0
                    ? ''
                    : (parseFloat(item.plan) / 10000).toFixed(2)
                this.libData.setDate = item.setDate
                this.libData.fact = fact
                this.libData.plan = plan
              }
            })
            this.option1.xData = xData
            this.option1.series[0].data = planList
            this.option1.series[1].data = factList
            this.LibData.showGridData = list
            this.LibData.gridData = lodash.cloneDeep(this.LibData.showGridData)
          }
        })
        .finally(_ => {
          this.LibData.loading = false
        })
    },
    //库存数据保存
    saveLibData() {
      this.LibData.loading = true
      let params = []
      this.LibData.gridData.forEach(item => {
        params.push({
          type: '1',
          id: item.planId,
          endAmt: item.plan,
          setDate: item.setDate
        })
      })
      post(firstMeetingBackup6, params).then(res => {
        //
        this.LibData.loading = false
        if (res.success) {
          this.$message.success('保存成功！')
          this.LibData.dialogVisible = false
          this.getLibData()
        }
      })
    },
    //导出库存数据
    exportLibData() {
      const data = [
        {
          setDate: '日期',
          fact: '实际',
          plan: '计划'
        }
      ].concat(
        _.cloneDeep(this.LibData.gridData).map(item => {
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `库存动态储位统计（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },

    changeCostDate(val) {
      const item = this.TunCostData.showGridData.find(item => item.date === val)
      if (item) {
        this.costData.target = item.target
        this.costData.ssbTarget = item.ssbTarget
      } else {
        this.costData.target = ''
        this.costData.ssbTarget = ''
      }
    },
    //吨钢费用数据查询
    getTunCostData() {
      const params = {
        setDate: this.cDate
      }
      this.TunCostData.loading = true
      post(firstMeetingBackup4, params)
        .then(res => {
          if (res.success) {
            let factList = [] //实际
            let planList = [] //挑战
            let bizList = [] //事业部
            let xData = []
            let list = res.data
            res.data.forEach(item => {
              factList.push(item.factAmt)
              planList.push(item.target)
              bizList.push(item.ssbTarget)
              xData.push(item.date.substring(8, item.date.length))
              if (item.date === this.cDate) {
                this.costData.date = item.date
                this.costData.factAmt = item.factAmt
                this.costData.target = item.target
                this.costData.ssbTarget = item.ssbTarget
                this.option6.series[0].data = [
                  {
                    value: (Number(item.factAmt) + 64.94).toFixed(2),
                    itemStyle: {
                      color:
                        Number(item.factAmt) + 64.94 < 93 ? '#30934f' : 'red'
                    }
                  },
                  93
                ]
              }
            })
            this.option4.xData = xData
            this.option4.series[0].data = factList
            this.option4.series[1].data = planList
            this.option4.series[2].data = bizList
            this.TunCostData.showGridData = list
            this.TunCostData.gridData = lodash.cloneDeep(
              this.TunCostData.showGridData
            )
          }
        })
        .finally(_ => {
          this.TunCostData.loading = false
        })
    },
    //吨钢费用数据保存
    saveTunCostData() {
      this.TunCostData.loading = true
      let params = [
        {
          target: this.costData.target,
          ssbTarget: this.costData.ssbTarget,
          date: this.costData.date
        }
      ]
      post(firstMeetingBackup7, params).then(res => {
        //
        this.TunCostData.loading = false
        if (res.success) {
          this.$message.success('保存成功！')
          this.TunCostData.dialogVisible = false
          this.getTunCostData()
        }
      })
    },
    //导出吨钢费用数据
    exportTunCostData() {
      const data = [
        {
          date: '日期',
          factCost: '实际',
          target: '挑战指标',
          ssbTarget: '事业部指标'
        }
      ].concat(
        _.cloneDeep(this.TunCostData.gridData).map(item => {
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `吨钢费用趋势分析（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },

    //维修费用数据查询
    getRepairCostData() {
      const params = {
        setDate: this.cDate
      }
      this.RepairCostData.loading = true
      post(firstMeetingBackup2, params)
        .then(res => {
          if (res.success) {
            let xData = []
            let planList = [] //计划
            let factList = [] //实际
            let list = []
            res.data.plan.forEach(item => {
              planList.push(
                item.cost === null || item.cost.length === 0
                  ? ''
                  : (parseFloat(item.cost) / 10000).toFixed(2)
              )
              xData.push(item.workShop)
              list.push({
                setDate: item.setDate,
                workShop: item.workShop,
                target: item.target,
                planId: item.id,
                planCost: item.cost === null ? '' : item.cost,
                factId: '',
                factCost: ''
              })
            })
            let total = 0
            res.data.fact.forEach(item => {
              let cost =
                item.cost === null || item.cost.length === 0
                  ? ''
                  : (parseFloat(item.cost) / 10000).toFixed(2)
              factList.push(cost)
              if (item.workShop === '预提费用') {
                this.repairData.cost = cost
                this.repairData.planOutPut = item.planOutPut
              } else {
                total +=
                  item.cost === null || item.cost.length === 0
                    ? 0
                    : parseFloat(item.cost)
              }
              const lItem = list.find(lItem => lItem.workShop === item.workShop)
              if (lItem) {
                lItem.factCost = item.cost === null ? '' : item.cost
                lItem.factId = item.id
              }
            })
            this.repairData.cost2 = (total / 10000).toFixed(2)
            this.option2.xData = xData
            this.option2.series[0].data = planList
            this.option2.series[1].data = factList
            this.RepairCostData.showGridData = list
            this.RepairCostData.gridData = lodash.cloneDeep(
              this.RepairCostData.showGridData
            )
          }
        })
        .finally(_ => {
          this.RepairCostData.loading = false
        })
    },
    //维修费用数据保存
    saveRepairCostData() {
      this.RepairCostData.loading = true
      let params = []
      this.RepairCostData.gridData.forEach(item => {
        params.push({
          id: item.planId,
          workShop: item.workShop,
          cost: item.planCost,
          target: item.target,
          type: '1',
          setDate: item.setDate
        })
      })
      post(firstMeetingBackup5, params).then(res => {
        //
        this.RepairCostData.loading = false
        if (res.success) {
          this.$message.success('保存成功！')
          this.RepairCostData.dialogVisible = false
          this.getRepairCostData()
        }
      })
    },
    //导出维修费用数据
    exportRepairCostData() {
      const data = [
        {
          item: '项次',
          content: '内容',
          handlePlan: '处理计划',
          remarks: '备注'
        }
      ].concat(
        _.cloneDeep(this.RepairCostData.gridData).map(item => {
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `维修及生产费用完成情况（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },

    //氢氧切割量数据查询
    async getHOcut() {
      const recentMonth = this.getResentMonth(this.cDate)

      let res = await post(HOcut, {
        // start_date: '2024-03-01',
        // end_date: '2024-03-31'
        start_date: moment(recentMonth.startTime).format('YYYY-MM-DD'),
        end_date: moment(recentMonth.endTime).format('YYYY-MM-DD')
      })
      if (res.stateCode == '0000') {
        let xData = []
        let yData = []
        let num = 0
        let HOdata = res.data.rows.sort(function(a, b) {
          return a.time > b.time ? 1 : -1
        })
        HOdata.forEach(item => {
          if (item.time) {
            num = num + 1
            xData.push(num)
          }
          yData.push(item.sum_netwgt)
        })
        const array = HOdata.map(item => item.time)
        const lastTwoElements = array.map(element => {
          const lastTwo = element.slice(-2)
          return lastTwo
        })
        this.option5.xData = lastTwoElements
        this.option5.series[0].data = yData
        const specifiedDate = new Date(this.cDate)
        const specifyDate = getYesterday(specifiedDate)
        let arrNew = HOdata.map(item => item.time)
        if (arrNew.indexOf(specifyDate) == -1) {
          this.lastAmount = 0
        } else {
          this.lastAmount = HOdata.filter(
            item => item.time == specifyDate
          )[0].sum_netwgt
        }
        this.allAmount = sumArr(HOdata.map(item => item.sum_netwgt))
      }
    }
  }
}
</script>

<style scoped lang="less">
.dialog-body {
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}

.tabs-class {
  display: flex;
  flex-direction: row;
  .tab-pane {
    color: #ffffffbf;
    margin-right: 21px;
  }
  .tab-pane-active {
    color: #ffffff;
  }
  .tab-pane-title-class {
    display: flex;
    flex-direction: column;
    position: relative;
    .tab-pane-img {
      .tab-pane-img2 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
      .tab-pane-img1 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        margin-bottom: 7px;
      }
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }

  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  overflow: auto;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
