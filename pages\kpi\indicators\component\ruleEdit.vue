<template>
  <div>
    <!-- 日/月数据取值 -->
    <operation-data-obtain 
      v-if="operationDataObtainVisible"
      ref="operationDataObtain"
      @aliasName="handleAliasName" />
    <el-dialog
      :title="title + '规则'"
      :visible.sync="visible"
      :width="'600px'"
      :close-on-click-modal="false"
      v-bind="$attrs"
      :append-to-body="true"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="150px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="规则类型"
          prop="ruleSign"
        >
          <el-select
            v-model="formData.ruleSign"
            :style="{width: '100%'}"
            size="small"
            clearable
            placeholder="请选择规则类型"
            @change="getRemarks"
          >
            <el-option
              v-for="(item, index) in ruleType"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="月数据取值"
          prop="code">
          <el-select
            v-model="formData.code"
            :loading="loading"
            :remote-method="remoteMethod"
            filterable
            remote
            clearable
            reserve-keyword
            placeholder="请输入关键词搜索"
            @input="codeChange">
            <el-option
              v-for="(item, index) in codeList"
              :key="item.name + index"
              :label="item.desc"
              :value="item.aliasName"/>
          </el-select>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            @click="handleAddDate">
            新增
          </el-button>
        </el-form-item>
        <el-form-item
          label="日数据取值"
          prop="dayCode">
          <el-select
            v-model="formData.dayCode"
            :loading="loading"
            :remote-method="remoteMethod"
            filterable
            remote
            clearable
            reserve-keyword
            placeholder="请输入关键词搜索">
            <el-option
              v-for="(item, index) in codeList"
              :key="item.name + index"
              :label="item.desc"
              :value="item.aliasName"/>
          </el-select>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            @click="handleAddDate">
            新增
          </el-button>
        </el-form-item>
        <el-form-item
          label="规则名称"
          prop="ruleName"
        >
          <el-input
            v-model="formData.ruleName"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入规则名称"
          />
        </el-form-item>
        <el-form-item
          label="预警规则"
          prop="rule"
        >
          <el-select
            v-model="formData.rule"
            :style="{width: '100%'}"
            size="small"
            clearable
            placeholder="请选择预警规则"
            @change="getRemarks"
          >
            <el-option
              v-for="(item, index) in earlyWarningRule"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="预警逻辑"
          prop="logic"
        >
          <el-select
            v-model="formData.logic"
            :style="{width: '100%'}"
            size="small"
            clearable
            placeholder="请选择预警逻辑"
            @change="getRemarks"
          >
            <el-option
              v-for="(item, index) in earlyWarningLogic"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="预警参数"
          prop="warningParam"
        >
          <el-input-number
            v-model="formData.warningParam"
            :precision="0"
            :step="1"
            :min="0"
            controls-position="right"
            placeholder="请输入"
            class="center"
            @change="getRemarks"/>
          <div v-if="formData.rule === 2">
            <i class="el-icon-warning-outline" />
            预警参数为" 0 "时，表示每月最后一天
          </div>
        </el-form-item>
        <el-form-item
          v-if="![3].includes(formData.rule)"
          label="目标值读取方式"
          prop="targetGetType"
        >
          <el-select
            v-model="formData.targetGetType"
            :style="{width: '100%'}"
            :disabled="[1, 4, ].includes(formData.rule) "
            size="small"
            clearable
            placeholder="请选择目标值读取方式"
          >
            <el-option
              v-for="(item, index) in targetGetType"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="formData.targetGetType === 0"
          label="关联目标ID"
          prop="linkedKpiId"
        >
          <select-kpi
            v-model="formData.linkedKpiId"
            :parent-name="formData.parentName"/>
        </el-form-item>
        <el-form-item
          v-if="[2, 3].includes(formData.targetGetType) && ![3].includes(formData.rule)"
          label="目标值"
          prop="basicValue"
        >
          <el-input-number
            v-model="formData.basicValue"
            :precision="2"
            :step="0.1"
            :min="0"
            controls-position="right"
            placeholder="请输入"
            class="center"
            @change="getRemarks"/>
          <template v-if="lastMonth !== null">
            上月目标值：{{ lastMonth }}
          </template>
        </el-form-item>
        <el-form-item
          label="目标值倍数"
          prop="multiple"
        >
          <el-input-number
            v-model="formData.multiple"
            :precision="2"
            :step="0.1"
            :min="0"
            controls-position="right"
            placeholder="请输入"
            class="center"
            @change="getRemarks"/>
        </el-form-item>
        <el-form-item
          label="单位"
          prop="unit"
        >
          <el-input
            v-model="formData.unit"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入单位"
          />
        </el-form-item>
        <el-form-item
          v-if="![1, 4].includes(formData.rule)"
          label="目标值管理"
          prop="isTargetMark"
        >
          <el-radio-group
            v-model="formData.isTargetMark"
            size="medium"
          >
            <el-radio
              v-for="(item, index) in statusList"
              :label="item.value"
              :key="index">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="是否核心指标"
          prop="isCoreRule"
        >
          <el-radio-group
            v-model="formData.isCoreRule"
            size="medium"
          >
            <el-radio
              v-for="(item, index) in statusList"
              :label="item.value"
              :key="index">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="比较值说明"
          prop="remarks"
        >
          <el-input
            v-model="formData.remarks"
            :style="{width: '100%'}"
            :rows="2"
            type="textarea"
            clearable
            placeholder="请输入比较值说明"
          />
        </el-form-item>

      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { roleAdd, roleEdit, userAdd, userEdit } from '@/api/system'
import {
  findCode,
  findTargetValueByRid,
  updateKpiWarningRules
} from '@/api/kpi'
import { ENUM } from '@/lib/Constant'
import SelectKpi from '@/components/SelectKpi'
import { post } from '@/lib/Util'
import OperationDataObtain from './operationDataObtain'

export default {
  components: { SelectKpi, OperationDataObtain },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    kid: {
      type: Number,
      require: true,
      default: null
    },
    rank: {
      type: Number,
      require: true,
      default: null
    },
    kpiName: {
      type: String,
      require: true,
      default: ''
    },
    hasDayCode: {
      type: Boolean,
      default: false
    }
  },
  // eslint-disable-next-line vue/require-prop-types
  data() {
    return {
      operationDataObtainVisible: false,
      loading: false,
      codeList: [],
      visible: false,
      url: {
        edit: updateKpiWarningRules,
        add: updateKpiWarningRules
      },
      kpiFunction: ENUM.kpiFunction,
      factoryList: ENUM.factoryList,
      levelList: ENUM.levelList,
      ruleType: ENUM.ruleType,
      earlyWarningLogic: ENUM.earlyWarningLogic,
      earlyWarningRule: ENUM.earlyWarningRule,
      targetGetType: ENUM.targetGetType,
      lastMonth: null, // 上月目标值
      statusList: [
        {
          value: false,
          label: '否',
          type: 'success'
        },
        {
          value: true,
          label: '是',
          type: 'warning'
        }
      ],
      formData: {
        kid: this.kid,
        ruleSign: null,
        logic: null,
        rule: null,
        warningParam: null,
        remarks: null,
        targetGetType: 2,
        linkedKpiId: null,
        basicValue: 0,
        multiple: 1,
        unit: null,
        isCoreRule: false,
        isTargetMark: false,
        code: null,
        dayCode: null,
        ruleName: null
      },
      isConfirmTarget: false, // 是否添加目标值管理确定的规则
      rules: {
        ruleSign: [
          {
            required: true,
            message: '请选择规则类型',
            type: 'number',
            trigger: 'change'
          }
        ],
        logic: [
          {
            required: true,
            message: '请选择预警逻辑',
            type: 'number',
            trigger: 'change'
          }
        ],
        rule: [
          {
            required: true,
            message: '请选择预警规则',
            type: 'number',
            trigger: 'change'
          }
        ],
        warningParam: [
          {
            required: true,
            message: '请填写预警参数',
            trigger: 'change'
          }
        ],
        targetGetType: [
          {
            required: true,
            message: '请选择目标值读取方式',
            type: 'number'
          }
        ],
        linkedKpiId: [
          {
            required: false,
            message: '请选择关联目标ID',
            type: 'number',
            trigger: 'change'
          }
        ],
        basicValue: [
          {
            required: true,
            message: '请输入目标值',
            trigger: 'change'
          }
        ],
        multiple: [
          {
            required: true,
            message: '请输入目标值倍数',
            trigger: 'change'
          }
        ]
      }
    }
  },
  computed: {
    logic: function() {
      if (!this.formData.logic && this.formData.logic !== 0)
        return '【预警逻辑】'
      return this.earlyWarningLogic.find(
        item => item.value === this.formData.logic
      ).label
    },
    ruleListFilter: function() {
      if (this.isConfirmTarget) {
        return this.earlyWarningRule.filter(
          item => ![1, 4].includes(item.value)
        )
      }
      return this.earlyWarningRule
    }
  },
  watch: {
    // 预警规则1.2.3必须填写预警参数
    'formData.rule': function(value) {
      this.rules.warningParam = [
        {
          required: [1, 2, 3, 4].includes(value),
          message: '请填写预警参数',
          trigger: 'change'
        }
      ]
      if ([1, 4].includes(value)) {
        this.formData.targetGetType = this.formData.rule
        this.formData.isTargetMark = false
      }
    },
    // 目标值读取方式为1时系统自动导入上月的该值
    //目标值读取方式为0时关联目标id必填，弹出指标搜索框
    'formData.targetGetType': async function(value) {
      this.rules.linkedKpiId = [
        {
          required: value === 0,
          message: '请选择关联目标ID',
          type: 'number',
          trigger: 'change'
        }
      ]

      // 置空
      // this.formData.basicValue = 0
      this.lastMonth = null
      if (!this.formData.id) return
      const { data } = post(findTargetValueByRid, { rid: this.formData.id })
      if (value === 2) {
        //
        this.formData.basicValue = data
      }
      if (value === 3) {
        // this.formData.basicValue = null
        this.lastMonth = data
      }
    }
  },
  created() {
    console.log('编辑页面')
  },
  methods: {
    handleAliasName(aliasName) {
      this.remoteMethod(aliasName) //显示新增的那条数据
    },
    handleAddDate() {
      this.operationDataObtainVisible = false
      this.$nextTick(() => {
        this.operationDataObtainVisible = true
      })
    },
    getRemarks() {
      if (!this.formData.rule && this.formData.rule !== 0) return
      if (this.formData.rule === 0) {
        this.formData.remarks = `如果实际值${this.logic}目标值*${
          this.formData.multiple
        }时触发`
      } else if (this.formData.rule === 2) {
        this.formData.remarks = `仅每月的${
          this.formData.warningParam === 0
            ? '最后一'
            : '第' + this.formData.warningParam
        }天校验，如果实际值${this.logic}目标值*${this.formData.multiple}时触发`
      } else if (this.formData.rule === 3) {
        this.formData.remarks = `实际有${this.logic}${this.formData
          .warningParam || '【预警参数】'}*${
          this.formData.multiple
        }个次级指标报警时触发`
      } else if (this.formData.rule === 1) {
        this.formData.remarks = `每月的第${
          this.formData.warningParam
        }天开始往后每天都校验，如果实际进度${this.logic}月时序进度值*${
          this.formData.multiple
        }时触发`
      } else if (this.formData.rule === 4) {
        this.formData.remarks = `每年的第${this.formData.warningParam ||
          '【预警参数】'}天开始往后每天都校验，如果实际进度${
          this.logic
        }月时序进度值*${this.formData.multiple}时触发`
      }
    },
    submitBefore() {
      this.formData.kid = this.kid
      this.formData.kpiName = this.kpiName
      this.formData.rank = this.rank
      this.formData.targetManagerId = '123456'
      //次级指标个数 预警参数和目标值一致
      if (this.formData.rule === 3) {
        this.formData.basicValue = this.formData.warningParam
      }
    },
    clearForm() {
      this.formData = {
        kid: this.kid,
        ruleSign: null,
        logic: null,
        rule: null,
        warningParam: null,
        remarks: null,
        targetGetType: 2,
        linkedKpiId: null,
        basicValue: 0,
        multiple: 1,
        unit: null,
        isCoreRule: false,
        isTargetMark: false
      }
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].clearValidate == 'function'
      ) {
        this.$nextTick(() => {
          this.$refs['form'].clearValidate()
        })
      }
    },
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true
        post(findCode, { param: query }).then(res => {
          this.codeList = res.data.content
          this.loading = false
        })
      } else {
        this.options = []
      }
    },
    codeChange() {
      const foundItem = this.codeList.find(
        item => item.aliasName === this.formData.code
      )
      this.formData.ruleName = foundItem ? foundItem.desc : ''
    },
    handleSubmit() {
      if (this.hasDayCode && this.formData.dayCode) {
        this.$confirm('该指标已有日数据，是否将其设置为新值?', '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        })
          .then(() => {
            //
            this.handelConfirm()
          })
          .catch(() => {
            this.formData.dayCode = null
            this.handelConfirm()
          })
      } else {
        this.handelConfirm()
      }
    },

    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          this.submitBefore()
          post(this.url.edit, this.formData).then(res => {
            if (!res.success) return this.$message.warning('保存失败！')
            if (res.data == 0) {
              this.$alert(
                '保存成功，您目前尚未配置目标值管理的规则，点击确认现在配置！',
                '重要提示',
                {
                  confirmButtonText: '确定',
                  callback: action => {
                    this.add()
                    this.clearForm()
                    this.isConfirmTarget = true
                    this.formData.isTargetMark = true
                  }
                }
              )
            } else {
              this.isConfirmTarget = false
              this.submitAfter(res)
              this.close()
            }
          })
        })
      }
    }
  }
}
</script>
<style scoped>
</style>
