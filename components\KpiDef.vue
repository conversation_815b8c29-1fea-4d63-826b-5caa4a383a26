<template>
  <div class="kpi-def">
    <div class="def-main">
      <div
        v-for="(item) in list"
        :class="{
          'warning': item.warningStatus,
          'trend': item.trendWarningStatus
        }"
        :title="getTitle(item)"
        :key="item.id"
        class="item">
        <img
          :src="getIcon(item.feature)"
          alt="">
        <div class="node-describe">
          <div class="tit">{{ item.name }}</div>
          <span>{{ item.coreResultValue }} <em>{{ item.unit }}</em></span>
        </div>
      </div>
    </div>
    <el-popover
      v-model="popVisible"
      placement="left"
      width="400"
      trigger="click">
      <div class="tree-wrapper">
        <el-tree
          ref="kpiTree"
          :data="data"
          :props="defaultProps"
          :check-strictly="true"
          highlight-current
          show-checkbox
          node-key="id"
        />
      </div>
      <div class="tree-footer text-right">
        <el-button
          size="small"
          @click="saveConfig">
          保存
        </el-button>
      </div>
      <div
        slot="reference"
        class="def-setting">
        <el-icon class="el-icon-setting" />
        指标
        配置
      </div>
    </el-popover>

  </div>
</template>

<script>
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import { math } from '@/lib/Math.js'
import {
  findDefKpiByUserNo,
  findKpiDataTree,
  findKpiTree,
  findNextRank,
  saveDefKpiByUserNo
} from '@/api/kpi'

export default {
  name: 'KpiDef',
  data() {
    return {
      list: [],
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'leaf'
      },
      kpiFunction: ENUM.kpiFunction,
      popVisible: false
    }
  },
  created() {
    this.loadData()
    this.loadTree()
  },
  methods: {
    getIcon(feature) {
      const icon = ENUM.kpiFunction.find(item => item.value == feature)
      return icon ? icon.icon : ''
    },
    loadTree() {
      post(findKpiTree, { type: 'settings' }).then(res => {
        this.data = res.data
      })
    },
    async loadData() {
      const data = await post(findDefKpiByUserNo, {
        userNo: localStorage.getItem('userId')
      })
      if (data.success) {
        this.list = data.data.coreKpi.map(item => {
          item.coreResultValue =
            item.unit === '%'
              ? math.multiply(item.coreResultValue, 100)
              : item.coreResultValue
          item.coreResultValue =
            item.coreResultValue >= 100
              ? Math.round(item.coreResultValue)
              : item.coreResultValue
          return item
        })
      }
    },
    async loadRootData() {
      //
      const list = this.kpiFunction.map(item => {
        item.id = 'kpi' + item.value
        item.name = item.label
        item.disabled = true
        return item
      })
      return Promise.resolve(list)
    },
    async loadKpiData(parentId) {
      const { data } = await post(findNextRank, { parentId: parentId })
      return Promise.resolve(data)
    },
    async loadNode(node, resolve) {
      let data = null
      if (node.level === 0) {
        data = await this.loadRootData()
      } else {
        const parentId = node.level === 1 ? 0 : node.data.id
        data = await this.loadKpiData(parentId)
        console.log(data)
        if (node.level === 1) {
          data = data.filter(kpi => kpi.feature == node.data.value)
          console.log(data, node.data.value)
        }
      }
      resolve(data)
    },
    async saveConfig() {
      const selectedList = this.$refs.kpiTree.getCheckedNodes()
      console.log(selectedList)
      const data = await post(saveDefKpiByUserNo, {
        userNo: localStorage.getItem('userId'),
        coreDefKpi: JSON.stringify(selectedList.map(item => item.id))
      })
      if (data.success) {
        // 保存成功
        this.popVisible = false
        this.loadData()
      }
    },
    getTitle(item) {
      return `${item.name}：${
        !item.coreResultValue && item.coreResultValue !== 0
          ? ''
          : item.coreResultValue
      } ${item.unit || ''}`
    }
  }
}
</script>

<style scoped lang="less">
.kpi-def {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  .def-setting {
    width: 70px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    padding: 20px;
    height: 88px;
    margin-left: 20px;
    font-size: 12px;
    color: #606266;
    background: #ffffff;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    .el-icon-setting {
      font-size: 30px;
    }
  }
  .def-main {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    .item {
      width: 15.6%;
      display: flex;
      justify-content: space-between;
      color: #fff;
      background: #5e93ed;
      padding: 12px 15px;
      border-radius: 6px;
      box-shadow: 0 0 10px rgba(34, 35, 35, 0.5);
      gap: 4px;
      span {
        display: block;
        font-size: 24px;
        line-height: 38px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        em {
          font-size: 14px;
        }
      }
      img {
        margin-right: 10px;
        width: 20%;
      }
      .tit {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      &.trend {
        background: #ffa958;
      }
      &.warning {
        background: #f56c6c;
      }
      .node-describe {
        //margin-right: 15px;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        span {
          display: block;
          min-height: 40px;
        }
      }
      .node-arrow {
        cursor: pointer;
      }
    }
  }
}
.tree-wrapper {
  max-height: 400px;
  overflow: auto;
}

@media (max-width: 1500px) {
  .kpi-def {
    .def-setting {
      height: 84px;
    }
    .def-main {
      .item {
        padding: 10px 15px;
        span {
          font-size: 20px;
        }
      }
    }
  }
}
</style>
