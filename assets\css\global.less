
/*菜单彩色图标*/
.icon-svg {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

/*常用样式*/
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.full-height {
    height: 100%;
}
.overflow-auto {
    overflow: auto;
}

.one-line {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/*全局滚动条样式 chrome内核*/
/*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/

*::-webkit-scrollbar {
    width: 6px; /*对垂直流动条有效*/
    height: 6px; /*对水平流动条有效*/
    &:hover {
        height: 12px; /*对水平流动条有效*/
    }
}

/*定义滚动条的轨道颜色、内阴影及圆角*/
*::-webkit-scrollbar-track{
    -webkit-box-shadow: inset 0 0 4px rgba(0,0,0,.15);
    border-radius: 3px;
}


/*定义滑块颜色、内阴影及圆角*/
*::-webkit-scrollbar-thumb{
    border-radius: 7px;
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.15);
    background-color: #C1CBDB;
    &:hover {
        background: #a9b4c5;
    }
}
/*定义两端按钮的样式*/
*::-webkit-scrollbar-button {
    display: none;
}

/*定义右下角汇合处的样式*/
*::-webkit-scrollbar-corner {
    display: none;
}

/*table不对齐问题*/
.el-table--border th.gutter:last-of-type {
    display: block!important;
}

/*路由切换渐变动画*/
.page-enter-active, page-leave-active{
    transition: all 0.5s;
}

.page-enter, .page-leave, .page-leave-active{
    opacity: 0;
    transform: translateX(-5px);
}

.el-autocomplete-suggestion {
    min-width: 240px;
}

.search-wrapper {
    /deep/ .el-form-item--mini.el-form-item {
        margin-bottom: 12px !important;
    }
}
.screen-text-pop {
    background: #1f657a;
}
.page-operate .el-button--small{
    margin-bottom: 12px !important;
}

.shadow-base {
    box-shadow:
            0px 2px 4px -2px rgba(0, 0, 0, 0.12),
            0px 4px 8px rgba(0, 0, 0, 0.08),
            0px 4px 16px 4px rgba(0, 0, 0, 0.04);
    border-radius: 6px
}

.shadow-light {
    box-shadow:
            0px 0px 4px rgba(0, 0, 0, 0.08),
            0px 2px 6px rgba(0, 0, 0, 0.06),
            0px 4px 8px 2px rgba(0, 0, 0, 0.04);
    border-radius: 6px
}

.page-content {
    font-size: 18px;
    height: 100%;
    .page-card {
        background: #fff;
        padding: 24px;
        position: relative;
    }
    .page-operate {
        display: flex;
        justify-content: space-between;
        .operate-icon {
            margin-left: 8px;
        }
    }
    .page-title {
        font-size: 18px;
        padding: 20px;
        background: #fff;
        margin-bottom: 10px;
    }
    .table-title {
        font-size: 16px;
        font-weight: bold;
        color: #606266;
        line-height: 33px;
        margin-bottom: 12px;
    }
}

.el-main {
    padding: 24px;
}
.forecast-order /deep/ .el-date-editor .el-range-separator {
    box-sizing: content-box !important;
}
.el-button--text {
    padding-left: 0!important;
    padding-right: 0!important;
}

// 大屏按钮
.screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1FC6FF;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;
    &:hover {
        background: rgba(31, 198, 255, 0.6);
        border: 1px solid #1FC6FF;
    }
}