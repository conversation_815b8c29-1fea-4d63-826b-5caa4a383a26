<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="8"
          class="full-height">
          <screen-border title="质量体系">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="showUnfinished2">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div
              ref="tableHeight"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="unfinished2.showGridData"
                :span-method="handleObjectSpan"
                :max-height="tableHeight"
                :format-span-data="unfinished2.showGridData"
                class="font-table center-table"
                border>
                <el-table-column
                  property="QUALITY_TYPES"
                  label="体系审核">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.QUALITY_TYPES)"/>
                  </template>
                </el-table-column>
                <el-table-column
                  property="VALIDATION_EX"
                  label="二方备注">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.VALIDATION_EX)"/>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="16"
          class="full-height">
          <screen-border title="铸坯质量">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="showUnfinished">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="unfinished.showGridData"
                :span-method="handleObjectSpan"
                :max-height="tableHeight"
                :format-span-data="unfinished.showGridData"
                class="font-table center-table"
                border>
                <el-table-column
                  property="PROD_PROCESSES"
                  label="工序"
                  width="120"/>
                <el-table-column
                  property="QUALITY_TYPES"
                  label="铸坯质量"
                  width="120"/>
                <el-table-column
                  property="A_LIST"
                  label="1#机"/>
                <el-table-column
                  property="B_LIST"
                  label="2#机"/>
                <el-table-column
                  property="C_LIST"
                  label="3#机"/>
                <el-table-column
                  property="D_LIST"
                  label="0#机"/>
                <el-table-column
                  property="NOTES"
                  label="备注"/>
                <el-table-column
                  property="VALIDATION_EX"
                  label="异常解释"/>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="8"
          class="full-height">
          <custom-table
            ref="table1"
            :title="'裂纹发生率'"
            :setting="flaw"
            :url-list="flawUrl.list"
            :url-save="flawUrl.save"
            :select-date="selectDate"
            :show-table="false"
            :steelmaking-show="true"
            @change="getPocessed">
            <template v-slot:topRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="()=>{
                  crackRemarks.save = false;
                  crackRemarks.dialogVisible = true
                  getCrackRemarks()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明录入
              </span>
              <span
                class="screen-btn"
                @click="()=>{
                  crackRemarks.save = true;
                  crackRemarks.dialogVisible = true
                  getCrackRemarks()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明
              </span>
            </template>
            <template v-slot:content>
              <div
                class="chart-wrapper">
                <div
                  class="chart"
                  style="padding-top: 12px">
                  <div
                    class="operate-box">
                    <el-radio-group
                      v-model="processed.dateType1"
                      size="mini"
                      class="screen-input"
                      @input="changeProcessed($event)">
                      <el-radio-button :label="0">日</el-radio-button>
                      <el-radio-button :label="1">月</el-radio-button>
                    </el-radio-group>
                  </div>
                  <single-bars-chart
                    :show-legend="false"
                    :chart-data="processed.bar1"
                    :x-data="processed.barX1"
                    :unit="'%'"
                    @selected="getIncidenceRate($event)"/>
                </div>
              </div>
            </template>
          </custom-table>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <screen-border
            :title="'裂纹改判率'">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="()=>{
                  crackRemarks2.save = false;
                  crackRemarks2.dialogVisible = true
                  getCrackRemarks2()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明录入
              </span>
              <span
                class="screen-btn"
                @click="()=>{
                  crackRemarks2.save = true;
                  crackRemarks2.dialogVisible = true
                  getCrackRemarks2()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明
              </span>
            </template>
            <div
              class="chart-wrapper">
              <div
                class="chart"
                style="padding-top: 12px">
                <div class="operate-box">
                  <el-radio-group
                    v-model="processed.dateType1"
                    size="mini"
                    class="screen-input"
                    @input="changeProcessed($event)">
                    <el-radio-button :label="0">日</el-radio-button>
                    <el-radio-button :label="1">月</el-radio-button>
                  </el-radio-group>
                </div>
                <single-bars-chart
                  :show-legend="false"
                  :chart-data="processed.bar11"
                  :x-data="processed.barX1"
                  :unit="'%'"
                  @selected="getCorrectionRate($event)"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <custom-table
            ref="table2"
            :title="'探伤合格率'"
            :setting="detection"
            :url-list="detectionUrl.list"
            :url-save="detectionUrl.save"
            :select-date="selectDate"
            :show-table="false"
            :steelmaking-show="true"
            @change="getPocessed1">
            <template v-slot:topRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="()=>{
                  inspectionRemarks.save = false;
                  inspectionRemarks.dialogVisible = true
                  getinspectionRemarks()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明录入
              </span>
              <span
                class="screen-btn"
                @click="()=>{
                  inspectionRemarks.save = true;
                  inspectionRemarks.dialogVisible = true
                  getinspectionRemarks()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明
              </span>
            </template>
            <template v-slot:content>
              <div class="chart-wrapper">
                <div
                  class="chart">
                  <div
                    class="operate-box">
                    <el-radio-group
                      v-model="processed.dateType2"
                      size="mini"
                      class="screen-input"
                      @input="changeProcessed1($event)">
                      <el-radio-button :label="0">日</el-radio-button>
                      <el-radio-button :label="1">月</el-radio-button>
                    </el-radio-group>
                  </div>
                  <single-bars-chart
                    :show-legend="false"
                    :chart-data="processed.bar2"
                    :unit="'%'"
                    :x-data="processed.barX2"
                    @selected="getDetectionDetailed($event)"/>
                </div>
              </div>
            </template>
          </custom-table>
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible.sync="unfinished.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="铸坯质量">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importUnfinishedData')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportunfinished">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveUnfinished">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          质量指标铸坯质量
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="unfinished.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="80">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            property="PROD_PROCESSES"
            label="工序"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.PROD_PROCESSES"/>
            </template>
          </el-table-column>
          <el-table-column
            property="QUALITY_TYPES"
            label="铸坯质量"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.QUALITY_TYPES"/>
            </template>
          </el-table-column>
          <el-table-column
            property="A_LIST"
            label="1#机"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            label="2#机"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.B_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="C_LIST"
            label="3#机"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.C_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="D_LIST"
            label="0#机"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.D_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="NOTES"
            label="备注">
            <template v-slot="{ row }">
              <el-input
                v-model="row.NOTES"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="VALIDATION_EX"
            label="异常解释">
            <template v-slot="{ row }">
              <el-input
                v-model="row.VALIDATION_EX"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                class="screen-btn"
                @click="delGridData($index, 'unfinished')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('unfinished')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="unfinished2.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="质量体系">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished2')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importUnfinishedData2')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview2"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportunfinished2">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveUnfinished2">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          质量体系
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="unfinished2.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="80">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM" />
            </template>
          </el-table-column>
          <el-table-column
            property="QUALITY_TYPES"
            label="体系审核">
            <template v-slot="{ row }">
              <el-input
                v-model="row.QUALITY_TYPES"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="VALIDATION_EX"
            label="二方备注">
            <template v-slot="{ row }">
              <el-input
                v-model="row.VALIDATION_EX"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                class="screen-btn"
                @click="delGridData($index, 'unfinished2')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          class="screen-btn"
          @click="addGridData('unfinished2')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="crackRemarks.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="裂纹发生情况说明">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality&&!crackRemarks.save"
              class="screen-btn"
              @click="saveCrackRemarks">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          裂纹发生情况说明
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="crackRemarks.gridData"
          class="font-table"
          border>
          <el-table-column
            property="A_LIST"
            label="内容">
            <template
              v-slot="{ row }"
              v-if="!crackRemarks.save">
              <el-input
                v-model="row.A_LIST"
                :rows="8"
                type="textarea"/>
            </template>
            <template
              v-slot="{ row }"
              v-else>
              <div
                slot="content"
                v-html="formatText(row.A_LIST)"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            width="210px"
            label="图片说明">
            <template v-slot="{ row, $index }">
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.B_LIST"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item.id"
                    :id="item.id"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !crackRemarks.save"
                    @img-delete="handlePasteImgDeleteID($event, index, 'crackRemarks')"
                  />
                </li>
              </ul>
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.showPic == unfinished ? [] : row.showPic"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item"
                    :src="item"
                    :is-id="false"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !crackRemarks.save"
                    @img-delete="handlePasteImgDelete($event, index, 'crackRemarks')"
                  />
                </li>
              </ul>
              <el-upload
                v-if="!crackRemarks.save"
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file)=>{
                  handleChange(file, 'crackRemarks', $index)
                }"
                :show-file-list="false"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = $index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="crackRemarks2.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="裂纹改判情况说明">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality&&!crackRemarks2.save"
              class="screen-btn"
              @click="saveCrackRemarks2">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          裂纹改判情况说明
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="crackRemarks2.gridData"
          class="font-table"
          border>
          <el-table-column
            property="A_LIST"
            label="内容">
            <template
              v-slot="{ row }"
              v-if="!crackRemarks2.save">
              <el-input
                v-model="row.A_LIST"
                :rows="8"
                type="textarea"/>
            </template>
            <template
              v-slot="{ row }"
              v-else>
              <div
                slot="content"
                v-html="formatText(row.A_LIST)"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            width="210px"
            label="图片说明">
            <template v-slot="{ row, $index }">
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.B_LIST"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item.id"
                    :id="item.id"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !crackRemarks2.save"
                    @img-delete="handlePasteImgDeleteID($event, index, 'crackRemarks2')"
                  />
                </li>
              </ul>
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.showPic == unfinished ? [] : row.showPic"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item"
                    :src="item"
                    :is-id="false"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !crackRemarks2.save"
                    @img-delete="handlePasteImgDelete($event, index, 'crackRemarks2')"
                  />
                </li>
              </ul>
              <el-upload
                v-if="!crackRemarks2.save"
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file)=>{
                  handleChange(file, 'crackRemarks2', $index)
                }"
                :show-file-list="false"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = $index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="inspectionRemarks.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="探伤情况说明">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality&&!inspectionRemarks.save"
              class="screen-btn"
              @click="saveinspectionRemarks">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          探伤情况说明
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="inspectionRemarks.gridData"
          class="font-table"
          border>
          <el-table-column
            property="A_LIST"
            label="内容">
            <template
              v-slot="{ row }"
              v-if="!inspectionRemarks.save">
              <el-input
                v-model="row.A_LIST"
                :rows="8"
                type="textarea"/>
            </template>
            <template
              v-slot="{ row }"
              v-else>
              <div
                slot="content"
                v-html="formatText(row.A_LIST)"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            width="210px"
            label="图片说明">
            <template v-slot="{ row, $index }">
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.B_LIST"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item.id"
                    :id="item.id"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !inspectionRemarks.save"
                    @img-delete="handlePasteImgDeleteID($event, index, 'inspectionRemarks')"
                  />
                </li>
              </ul>
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.showPic == unfinished ? [] : row.showPic"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item"
                    :src="item"
                    :is-id="false"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !inspectionRemarks.save"
                    @img-delete="handlePasteImgDelete($event, index, 'inspectionRemarks')"
                  />
                </li>
              </ul>
              <el-upload
                v-if="!inspectionRemarks.save"
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file)=>{
                  handleChange(file, 'inspectionRemarks', $index)
                }"
                :show-file-list="false"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = $index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
    <!-- 率弹窗-->
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'1300px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="裂纹发生详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          裂纹发生详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList"
        class="center-table"
        border>
        <el-table-column
          property="产线"
          label="产线">
          <template v-slot="{ row }">
            {{ codePlt[row['产线']] }}
          </template>
        </el-table-column>
        <el-table-column
          property="钢板号"
          label="钢板号"/>
        <el-table-column
          property="板坯钢种"
          label="板坯钢种"/>
        <el-table-column
          property="轧制标准"
          label="轧制标准" />
        <el-table-column
          property="厚度"
          label="厚度"
          width="70"/>
        <el-table-column
          property="宽度"
          label="宽度"
          width="60"/>
        <el-table-column
          property="长度"
          label="长度"
          width="70"/>
        <el-table-column
          property="重量"
          label="重量"
          width="70"/>
        <el-table-column
          property="表面等级"
          label="表面等级"
          width="75"/>
        <el-table-column
          property="缺陷"
          label="缺陷"
          width="110"/>
        <el-table-column
          property="改判缺陷"
          label="改判缺陷"
          width="110"/>
        <el-table-column
          property="铸机号"
          label="铸机号"
          width="60"/>
        <el-table-column
          property="铸坯厚度"
          label="铸坯厚度"
          width="75"/>
        <el-table-column
          property="铸坯宽度"
          label="铸坯宽度"
          width="75"/>
        <el-table-column
          property="铸坯长度"
          label="铸坯长度"
          width="75"/>
        <el-table-column
          property="装炉温度"
          label="装炉温度"
          width="75"/>
      </el-table>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogVisible1"
      :width="'1300px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          裂纹改判详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList1"
        class="center-table"
        border>
        <el-table-column
          property="钢板号"
          label="钢板号"/>
        <el-table-column
          property="板坯钢种"
          label="板坯钢种"/>
        <el-table-column
          property="轧制标准"
          label="轧制标准" />
        <el-table-column
          property="厚度"
          label="厚度"
          width="70"/>
        <el-table-column
          property="宽度"
          label="宽度"
          width="70"/>
        <el-table-column
          property="长度"
          label="长度"
          width="70"/>
        <el-table-column
          property="重量"
          label="重量"
          width="70"/>
        <el-table-column
          property="表面等级"
          label="表面等级"
          width="80"/>
        <el-table-column
          property="缺陷"
          label="缺陷"
          width="110"/>
        <el-table-column
          property="改判缺陷"
          label="改判缺陷"
          width="110"/>
        <el-table-column
          property="铸机号"
          label="铸机号"
          width="70"/>
        <el-table-column
          property="铸坯厚度"
          label="铸坯厚度"
          width="80"/>
        <el-table-column
          property="铸坯宽度"
          label="铸坯宽度"
          width="80"/>
        <el-table-column
          property="铸坯长度"
          label="铸坯长度"
          width="80"/>
        <el-table-column
          property="装炉温度"
          label="装炉温度"
          width="80"/>
      </el-table>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogVisible2"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          探伤不合格详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList2"
        class="center-table"
        border>
        <el-table-column
          property="产线"
          label="产线"
          width="90">
          <template v-slot="{ row }">
            {{ codePlt[row['产线']] }}
          </template>
        </el-table-column>
        <el-table-column
          property="钢板号"
          label="钢板号"/>
        <el-table-column
          property="板坯钢种"
          label="板坯钢种"/>
        <el-table-column
          property="标准号"
          label="标准号"/>
        <el-table-column
          property="检查标准"
          label="检查标准"/>
        <el-table-column
          property="结论"
          label="结论"
          width="80">
          <template v-slot="{ row }">
            {{ resultList[row['结论']] }}
          </template>
        </el-table-column>
        <el-table-column
          property="改判原因"
          label="改判原因"
          width="80"/>
        <el-table-column
          property="厚度"
          label="厚度"
          width="80"/>
        <el-table-column
          property="宽度"
          label="宽度"
          width="80"/>
        <el-table-column
          property="长度"
          label="长度"
          width="80"/>
        <el-table-column
          property="重量"
          label="重量"
          width="80"/>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import lodash from 'lodash'
import * as _ from 'lodash'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'

import { post } from '@/lib/Util'
import {
  correctionRateDetailed,
  detectionDetailed,
  findInspectionPassRateByDate,
  firstMorningMeeting,
  incidenceRateDetailed,
  qmsQualityQueryPlateFlaw,
  qmsQualitySavePlateFlaw,
  saveInspectionPassRate
} from '@/api/screen'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart.vue'
import CustomTable from '@/pages/screen/qualityMeeting/component/custom-table.vue'
import ImgView from '@/components/ImgView.vue'
import { deleteFileByIds, uploadFile } from '@/api/system'

export default {
  name: 'qualityIndex',
  components: { ImgView, CustomTable, SingleBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      tableHeight: null,
      editIndex: 0,
      spanArr2: {},
      flawList: [],
      tableList: [],
      dialogVisible: false,
      flawList1: [],
      tableList1: [],
      dialogVisible1: false,
      flawList2: [],
      tableList2: [],
      dialogVisible2: false,
      resultList: {
        Y: '合格',
        N: '不合格'
      },
      unfinished: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      unfinished2: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      crackRemarks: {
        gridData: [],
        save: true,
        dialogVisible: false
      },
      crackRemarks2: {
        gridData: [],
        save: true,
        dialogVisible: false
      },
      inspectionRemarks: {
        gridData: [],
        save: true,
        dialogVisible: false
      },
      flawUrl: {
        save: qmsQualitySavePlateFlaw,
        list: qmsQualityQueryPlateFlaw
      },
      processed: {
        bar1: [],
        barX1: [],
        barLoading: false,
        dateType1: 0,
        failReason1: '',
        bar11: [],
        dateType11: 0,
        failReason11: '',
        bar2: [],
        barX2: [],
        dateType2: 0,
        failReason2: ''
      },
      flaw: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'incidencetargetvalue',
          keySave: 'incidenceTargetValue',
          label: '发生率目标值'
        },
        {
          keyQuery: 'incidencevalue',
          keySave: 'incidenceValue',
          label: '发生率实际值'
        },
        {
          keyQuery: 'reasonnotcompl',
          keySave: 'reasonNotCompl',
          label: '发生率未完成原因'
        },
        {
          keyQuery: 'changetargetvalue',
          keySave: 'changeTargetValue',
          label: '改判率目标值'
        },
        {
          keyQuery: 'changevalue',
          keySave: 'changeValue',
          label: '改判率实际值'
        },
        {
          keyQuery: 'changenotcompl',
          keySave: 'ChangeNotCompl',
          label: '改判率未完成原因'
        },
        {
          keyQuery: 'lwmonthdata',
          keySave: 'lwMonthData',
          label: '裂纹月数据',
          show: false
        },
        {
          keyQuery: 'gpmonthdata',
          keySave: 'gpMonthData',
          label: '改判月数据',
          show: false
        }
      ],
      detectionUrl: {
        save: saveInspectionPassRate,
        list: findInspectionPassRateByDate
      },
      detection: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'targetvalue',
          keySave: 'targetValue',
          label: '目标值'
        },
        {
          keyQuery: 'value',
          keySave: 'Value',
          label: '实际值'
        },
        {
          keyQuery: 'reasonnotcompl',
          keySave: 'reasonNotCompl',
          label: '未完成原因'
        },
        {
          keyQuery: 'monthdata',
          keySave: 'monthData',
          label: '月数据',
          show: false
        }
      ]
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getUnfinished({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD')
      })
      this.getCrackRemarks()
      this.getCrackRemarks2()
      this.getinspectionRemarks()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['PROD_PROCESSES']
    // this.spanArr['PROD_PROCESSES'] = [2, 0, 1]
  },
  mounted() {
    this.tableHeight = this.$refs.tableHeight.offsetHeight
  },
  methods: {
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          PROD_PROCESSES: 'C',
          QUALITY_TYPES: 'D',
          A_LIST: 'E',
          B_LIST: 'F',
          C_LIST: 'G',
          D_LIST: 'H',
          NOTES: 'I',
          VALIDATION_EX: 'J'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unfinished.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportunfinished() {
      const dataName = [
        'SORT_NUM',
        'PROD_PROCESSES',
        'QUALITY_TYPES',
        'A_LIST',
        'B_LIST',
        'C_LIST',
        'D_LIST',
        'NOTES',
        'VALIDATION_EX'
      ]
      const data = [
        {
          SORT_NUM: '序号',
          PROD_PROCESSES: '工序',
          QUALITY_TYPES: '铸坯质量',
          A_LIST: '1#机',
          B_LIST: '2#机',
          C_LIST: '3#机',
          D_LIST: '0#机',
          NOTES: '备注',
          VALIDATION_EX: '异常解释'
        }
      ].concat(
        _.map(_.cloneDeep(this.unfinished.gridData), item => {
          let datas = {}
          _.forEach(dataName, items => {
            datas[items] = item[items]
          })
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `质量指标—铸坯质量（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 质量体系导出
    exportunfinished2() {
      const dataName = ['SORT_NUM', 'QUALITY_TYPES', 'VALIDATION_EX']
      const data = [
        {
          SORT_NUM: '序号',
          QUALITY_TYPES: '项目',
          VALIDATION_EX: '内容'
        }
      ].concat(
        _.map(_.cloneDeep(this.unfinished2.gridData), item => {
          let datas = {}
          _.forEach(dataName, items => {
            datas[items] = item[items]
          })
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `质量指标—质量体系（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 获取数据
    getUnfinished(data) {
      post(firstMorningMeeting.qualityFirstInit, data).then(res => {
        this.unfinished.showGridData = lodash.cloneDeep(res.data[0])
        this.formatSpanData(lodash.cloneDeep(res.data[0]))
        this.unfinished.gridData = lodash.cloneDeep(res.data[0])
        this.unfinished2.showGridData = lodash.cloneDeep(res.data[1])
        this.unfinished2.gridData = lodash.cloneDeep(res.data[1])
        this.formatSpanData2(lodash.cloneDeep(res.data[1]))
      })
    },
    // 铸坯质量保存数据
    saveUnfinished() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        prodTime: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 0,
        data: lodash.map(
          lodash.sortBy(this.unfinished.gridData, item => item.SORT_NUM),
          (item, index) => {
            item.PROD_DATE = this.$moment(this.cDate).format('yyyyMMDD')
            item.FLAG = 0
            item.SORT_NUM = index + 1
            return item
          }
        )
      }
      post(firstMorningMeeting.qualityFirst, params).then(res => {
        this.getUnfinished({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD')
        })
        this.unfinished.dialogVisible = false
        this.loading = false
      })
    },
    // 铸坯质量日期导入
    importUnfinishedData(date) {
      post(firstMorningMeeting.qualityFirstInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD')
      }).then(res => {
        //
        this.loading = false
        this.unfinished.gridData = lodash.cloneDeep(res.data[0])
        if (!res.data[0].length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    showUnfinished() {
      this.unfinished.gridData = lodash.cloneDeep(this.unfinished.showGridData)
      this.unfinished.dialogVisible = true
    },
    // 保存质量体系数据
    saveUnfinished2() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        prodTime: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 1,
        data: lodash.map(
          lodash.sortBy(this.unfinished2.gridData, item => item.SORT_NUM),
          (item, index) => {
            item.PROD_DATE = this.$moment(this.cDate).format('yyyyMMDD')
            item.FLAG = 1
            item.SORT_NUM = index + 1
            return item
          }
        )
      }
      post(firstMorningMeeting.qualityFirst, params).then(res => {
        this.getUnfinished({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD')
        })
        this.unfinished2.dialogVisible = false
        this.loading = false
      })
    },
    // 展示质量体系
    showUnfinished2() {
      this.unfinished2.gridData = lodash.cloneDeep(
        this.unfinished2.showGridData
      )
      this.unfinished2.dialogVisible = true
    },
    // 质量体系导入文件
    handlePreview2(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          QUALITY_TYPES: 'C',
          VALIDATION_EX: 'D'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unfinished2.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    importUnfinishedData2(date) {
      post(firstMorningMeeting.qualityFirstInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD')
      }).then(res => {
        //
        this.loading = false
        this.unfinished2.gridData = lodash.cloneDeep(res.data[1])
        if (!res.data[0].length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 计算需要合并的单元格
    formatSpanData2(data) {
      this.mergeArr.forEach(keyName => {
        this.spanArr2[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr2[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr2[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr2[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr2[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
    },
    // 合并单元格
    handleObjectSpan2({ row, column, rowIndex, columnIndex }) {
      // 需要合并的列
      // [0, 1, 2].includes(columnIndex ), 表示合并前三列
      if (this.mergeArr.includes(column.property)) {
        const _row = this.spanArr2[column.property][rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 探伤合格率详情
    getDetectionDetailed(data) {
      const plt = this.pltCode[
        this.processed.barX2[data.fromActionPayload.dataIndexInside]
      ]
      if (!plt) return
      post(detectionDetailed, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        plt
      }).then(res => {
        this.flawList2 = res.data
        this.dialogVisible2 = true
      })
    },
    // 裂纹发生率详情
    getIncidenceRate(data) {
      const plt = this.pltCode[
        this.processed.barX1[data.fromActionPayload.dataIndexInside]
      ]
      if (!plt) return
      post(incidenceRateDetailed, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        plt
      }).then(res => {
        this.flawList = res.data
        this.dialogVisible = true
      })
    },
    // 探伤获取数据
    getPocessed1(data) {
      this.tableList2 = data
      Object.assign(this.processed, {
        bar2: data.map(item => {
          return {
            value: Number(item['Value']),
            plan: Number(item.targetValue),
            finished: Number(item['Value']) >= Number(item.targetValue)
          }
        }),
        barX2: data.map(item => item.rollingMill)
      })
      this.processed.failReason2 = data
        .filter(item => item.reasonNotCompl)
        .map(item => item.rollingMill + '：' + item.reasonNotCompl)
        .join('；')
    },
    changeProcessed($event) {
      const data = this.tableList
      console.log(this.tableList)
      Object.assign(this.processed, {
        bar1: data.map(item => {
          const value =
            $event === 0
              ? Number(item.incidenceValue)
              : Number(item.lwMonthData || 0)
          return {
            value: value,
            plan: Number(item.incidenceTargetValue),
            finished: value < Number(item.incidenceTargetValue)
          }
        }),
        bar11: data.map(item => {
          const value =
            $event === 0
              ? Number(item.changeValue)
              : Number(item.gpMonthData || 0)
          return {
            value: value,
            plan: Number(item.changeTargetValue),
            finished: value < Number(item.changeTargetValue)
          }
        }),
        barX1: data.map(item => item.rollingMill)
      })
      this.processed.failReason1 =
        $event === 0
          ? data
              .filter(item => item.reasonNotCompl)
              .map(item => item.rollingMill + '：' + item.reasonNotCompl)
              .join('；')
          : ''
      this.processed.failReason11 =
        $event === 0
          ? data
              .filter(item => item.ChangeNotCompl)
              .map(item => item.rollingMill + '：' + item.ChangeNotCompl)
              .join('；')
          : 0
    },
    // 裂纹获取数据
    getPocessed(data) {
      this.tableList = data
      this.changeProcessed(0)
    },
    // 裂纹发生率详情
    getCorrectionRate(data) {
      const plt = this.pltCode[
        this.processed.barX2[data.fromActionPayload.dataIndexInside]
      ]
      if (!plt) return
      post(correctionRateDetailed, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        plt
      }).then(res => {
        this.flawList1 = res.data
        this.dialogVisible1 = true
      })
    },
    changeProcessed1($event) {
      const data = this.tableList2
      Object.assign(this.processed, {
        bar2: data.map(item => {
          const value =
            $event === 0
              ? Number(item['Value'])
              : Number(item['monthData'] || 0)
          return {
            value: value,
            plan: Number(item.targetValue),
            finished: value >= Number(item.targetValue)
          }
        }),
        barX2: data.map(item => item.rollingMill)
      })
      this.processed.failReason2 =
        $event === 0
          ? data
              .filter(item => item.reasonNotCompl)
              .map(item => item.rollingMill + '：' + item.reasonNotCompl)
              .join('；')
          : ''
    },
    getCrackRemarks() {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '裂纹备注'
      }).then(res => {
        this.crackRemarks.gridData = _.cloneDeep(
          lodash.map(res.data.length ? res.data : [{}], item => {
            return {
              ...item,
              B_LIST: item.B_LIST ? JSON.parse(item.B_LIST) : []
            }
          })
        )
      })
    },
    async saveCrackRemarks() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '裂纹备注',
        data: _.map(
          _.sortBy(this.crackRemarks.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              FLAG: '裂纹备注',
              SORT_NUM: index + 1
            }
          }
        )
      }
      let del = null
      if (
        this.crackRemarks.gridData[0].delImage &&
        this.crackRemarks.gridData[0].delImage.length
      ) {
        del = await post(deleteFileByIds, {
          ids: this.crackRemarks.gridData[0].delImage
        })
      }
      if (del == null || del.success) {
        if (
          this.crackRemarks.gridData[0].file &&
          this.crackRemarks.gridData[0].file.length
        ) {
          const formData = new FormData()
          this.crackRemarks.gridData[0].file.forEach(item => {
            formData.append('files', item)
          })
          post(uploadFile, formData, false, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.success) {
              params.data[0].B_LIST = JSON.stringify([
                ...params.data[0].B_LIST,
                ...res.data
              ])
              post(firstMorningMeeting.furnaceCondition, params).then(res => {
                this.getCrackRemarks()
                this.$message.success('保存成功')
                this.loading = false
              })
            } else {
              this.$message.warning('图片上传失败！')
              this.loading = false
            }
          })
        } else {
          params.data[0].B_LIST = JSON.stringify(params.data[0].B_LIST)
          post(firstMorningMeeting.furnaceCondition, params).then(res => {
            this.getCrackRemarks()
            this.$message.success('保存成功')
            this.loading = false
          })
        }
      } else {
        this.$message.error('图片删除失败！！！')
      }
    },
    getCrackRemarks2() {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '裂纹备注2'
      }).then(res => {
        this.crackRemarks2.gridData = _.cloneDeep(
          lodash.map(res.data.length ? res.data : [{}], item => {
            return {
              ...item,
              B_LIST: item.B_LIST ? JSON.parse(item.B_LIST) : []
            }
          })
        )
      })
    },
    async saveCrackRemarks2() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '裂纹备注2',
        data: _.map(
          _.sortBy(this.crackRemarks2.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              FLAG: '裂纹备注2',
              SORT_NUM: index + 1
            }
          }
        )
      }
      let del = null
      if (
        this.crackRemarks2.gridData[0].delImage &&
        this.crackRemarks2.gridData[0].delImage.length
      ) {
        del = await post(deleteFileByIds, {
          ids: this.crackRemarks2.gridData[0].delImage
        })
      }
      if (del == null || del.success) {
        if (
          this.crackRemarks2.gridData[0].file &&
          this.crackRemarks2.gridData[0].file.length
        ) {
          const formData = new FormData()
          this.crackRemarks2.gridData[0].file.forEach(item => {
            formData.append('files', item)
          })
          post(uploadFile, formData, false, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.success) {
              params.data[0].B_LIST = JSON.stringify([
                ...params.data[0].B_LIST,
                ...res.data
              ])
              post(firstMorningMeeting.furnaceCondition, params).then(res => {
                this.getCrackRemarks2()
                this.$message.success('保存成功')
                this.loading = false
              })
            } else {
              this.$message.warning('图片上传失败！')
              this.loading = false
            }
          })
        } else {
          params.data[0].B_LIST = JSON.stringify(params.data[0].B_LIST)
          post(firstMorningMeeting.furnaceCondition, params).then(res => {
            this.getCrackRemarks2()
            this.$message.success('保存成功')
            this.loading = false
          })
        }
      } else {
        this.$message.error('图片删除失败！！！')
      }
    },
    getinspectionRemarks() {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '探伤备注'
      }).then(res => {
        this.inspectionRemarks.gridData = _.cloneDeep(
          lodash.map(res.data.length ? res.data : [{}], item => {
            return {
              ...item,
              B_LIST: item.B_LIST ? JSON.parse(item.B_LIST) : []
            }
          })
        )
      })
    },
    async saveinspectionRemarks() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '探伤备注',
        data: _.map(
          _.sortBy(this.inspectionRemarks.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              FLAG: '探伤备注',
              SORT_NUM: index + 1
            }
          }
        )
      }
      let del = null
      if (
        this.inspectionRemarks.gridData[0].delImage &&
        this.inspectionRemarks.gridData[0].delImage.length
      ) {
        del = await post(deleteFileByIds, {
          ids: this.inspectionRemarks.gridData[0].delImage
        })
      }
      if (del == null || del.success) {
        if (
          this.inspectionRemarks.gridData[0].file &&
          this.inspectionRemarks.gridData[0].file.length
        ) {
          const formData = new FormData()
          this.inspectionRemarks.gridData[0].file.forEach(item => {
            formData.append('files', item)
          })
          post(uploadFile, formData, false, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.success) {
              params.data[0].B_LIST = JSON.stringify([
                ...params.data[0].B_LIST,
                ...res.data
              ])

              post(firstMorningMeeting.furnaceCondition, params).then(res => {
                this.getinspectionRemarks()
                this.$message.success('保存成功')
                this.loading = false
              })
            } else {
              this.$message.warning('图片上传失败！')
              this.loading = false
            }
          })
        } else {
          params.data[0].B_LIST = JSON.stringify(params.data[0].B_LIST)
          post(firstMorningMeeting.furnaceCondition, params).then(res => {
            this.getinspectionRemarks()
            this.$message.success('保存成功')
            this.loading = false
          })
        }
      } else {
        this.$message.error('图片删除失败！！！')
      }
    },
    httpRequest(params) {},
    handleChange(file, row, index) {
      if (this[row].gridData[index].file == undefined) {
        this[row].gridData[index].file = [file.raw]
      } else {
        this[row].gridData[index].file.push(file.raw)
      }
      if (this[row].gridData[index].showPic == undefined) {
        this[row].gridData[index].showPic = [file.url]
      } else {
        this[row].gridData[index].showPic.push(file.url)
      }
      this[row] = lodash.cloneDeep(this[row])
    },
    handlePasteImgDelete(file, index, row) {
      this[row].gridData[0].file.splice(index, 1)
      this[row].gridData[0].showPic.splice(index, 1)
      this[row] = lodash.cloneDeep(this[row])
    },
    handlePasteImgDeleteID(file, index, row) {
      if (this[row].gridData[0].delImage == undefined) {
        this[row].gridData[0].delImage = [file.id]
      } else {
        this[row].gridData[0].delImage.push(file.id)
      }
      this[row].gridData[0].B_LIST.splice(index, 1)
      this[row] = lodash.cloneDeep(this[row])
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
    position: relative;
    .operate-box {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}
</style>
