<template>
  <div class="content">
    <div class="content-item">
      <screen-border :title="cDate + '月份产品试制/试验计划总量'">
        <template v-slot:headerRight>
          <span
            class="screen-btn"
            @click="changeTab(2)">
            关闭
          </span>
        </template>
        <div
          ref="table1"
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            :data="summaryData.showGridData"
            :span-method="handleObjectSpan"
            :max-height="summaryData.maxHeight"
            :row-class-name="totalClass"
            size="mini"
            class="center-table font-table"
            border>
            <el-table-column
              property="DEPARTMENT_PREFIX"
              label="部门"/>
            <el-table-column
              property="TYPE"
              label="">
              <template v-slot="{ row }">
                {{ monthStr }}月{{ row.TYPE }}
              </template>
            </el-table-column>
            <el-table-column
              property="TPQUANTITY"
              label="试制量"/>
            <el-table-column
              property="STEELMAKINGCAPACITY"
              label="炼钢量"/>
            <el-table-column
              :label="'1#150mm(t)'"
              property="ONEHUNDREDFIFTY"/>
            <el-table-column
              :label="'2#220mm(t)'"
              property="TWOHUNDREDTWENTY"/>
            <el-table-column
              :label="'3#260mm(t)'"
              property="TWOHUNDREDSIXTY"/>
            <el-table-column
              :label="'3#320mm(t)'"
              property="THREEHUNDREDTWENTY"/>
            <el-table-column
              :label="'0#370mm(t)'"
              property="THREEHUNDREDSEVENTY"/>
            <el-table-column
              :label="'0#460mm(t)'"
              property="FOURHUNDREDSIXTY"/>
          </el-table>
        </div>
      </screen-border>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { findTppTotal } from '@/api/screenTechnolagy'
import moment from 'moment'

export default {
  name: 'pilotPlanSummary',
  components: { ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      editIndex: null,
      summaryData: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      departmentList: ['技术研发处-能源用钢研发室', '研究院-结构材料研究所'],
      varietyList: ['新产品研发'],
      statusList: ['已完成', '未完成'],
      reasonList: [
        '技术原因',
        '订单交付原因',
        '设备原因',
        '生产原因',
        '检试验原因',
        '外委原因'
      ]
    }
  },
  computed: {
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'month')
        .format('yyyy-MM')
    },
    monthStr: function() {
      // 初始化数据
      return Number(moment(this.cDate).format('MM'))
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = moment(this.selectDate).format('YYYY-MM')
    },
    cDate: function() {
      // 初始化数据
      this.getpilotPlan()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = moment(this.selectDate).format('YYYY-MM')
    this.mergeArr = ['DEPARTMENT_PREFIX']
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    // 通知改变tab
    changeTab(tab) {
      this.$emit('jumpTab', tab)
    },
    // 获取数据
    getpilotPlan() {
      post(findTppTotal, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.$emit('statusChange', !res.length)
        this.summaryData.showGridData = res
        this.formatSpanData(res)
      })
    },
    calculate() {
      console.log(this.$refs.table1.offsetHeight)
      this.summaryData.maxHeight = this.$refs.table1.offsetHeight
    },

    // 计算需要合并的单元格
    formatSpanData(data) {
      this.mergeArr.forEach(keyName => {
        this.spanArr[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
    },
    // 合并单元格
    handleObjectSpan({ row, column, rowIndex, columnIndex }) {
      // 需要合并的列
      // [0, 1, 2].includes(columnIndex ), 表示合并前三列
      if (this.mergeArr.includes(column.property)) {
        const _row = this.spanArr[column.property][rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    totalClass(row) {
      console.log(row)
      if (row.row.TYPE && row.row.TYPE === '总计') {
        return 'table-total'
      }
      return ''
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.edit-btn {
  margin: 0 3px;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
