<template>
  <div
    :id="containerId"
    style="height: 100%"/>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return [
          '#0C75FF',
          '#FF7D00',
          '#00B42A',
          '#91cc75',
          '#fac858',
          '#ee6666',
          '#73c0de',
          '#3ba272',
          '#fc8452',
          '#9a60b4',
          '#ea7ccc'
        ]
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    unit: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    labelWidth: {
      type: Number,
      default: 160
    },
    total: {
      type: Number,
      default: 0
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
        window.addEventListener('resize', this.resizeChart)
      }
      const options = {
        title: {
          show: !!this.title,
          text: '{tit|' + this.title + '}',
          left: '28%',
          top: '45%',
          textAlign: 'center',
          padding: 0,
          textStyle: {
            fontSize: 12,
            rich: {
              tit: {
                borderColor: '#F0F0F0',
                borderWidth: 12,
                borderRadius: 4,
                fontWeight: 600,
                fontSize: 18
              }
            }
          }
        },
        tooltip: {
          show: true,
          trigger: 'item',
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          left: '1%',
          top: '75%',
          y: 'center',
          itemGap: 5,
          itemHeight: 5,
          icon: 'circle',
          data: this.chartData.map((item, index) => {
            return {
              name: item.name,
              textStyle: {
                color: this.color[index]
              }
            }
          }),
          formatter: name => {
            let arr =
              '{name|' +
              name +
              '}{value|' +
              this.getNum(name).value +
              '}{percentage|' +
              this.getNum(name).percent +
              (this.unit ? this.unit : '') +
              '}'
            return arr
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 12,
                align: 'left',
                lineHeight: 50,
                width: this.labelWidth - 90,
                color: '#f1f1f1'
              },
              value: {
                fontSize: 12,
                align: 'left',
                width: 70
              },
              percentage: {
                fontSize: 12,
                align: 'center'
              }
            }
          }
        },
        color: this.color,
        series: [
          {
            type: 'pie',
            //selectedMode: 'single',
            radius: ['50%', '75%'],
            center: ['50%', '40%'],
            label: {
              normal: {
                show: false,
                formatter: '{b}：{d}%',
                color: '#f2f2f2'
              }
            },
            data: this.chartData
            // labelLine: {
            //   normal: {
            //     show: true, //开启提示线展示
            //     length: 4, //设置第一条提示线长度
            //     length2: 4, //设置第二条提示线长度
            //     length3: 4, //设置第二条提示线长度
            //     lineStyle: {
            //       color: 'white'
            //     }
            //   }
            // }
          }
        ]
      }
      this.myChart.setOption(options)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    getNum(name) {
      const match = this.chartData.find(item => item.name === name)
      return match ? match : {}
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style scoped>
</style>
