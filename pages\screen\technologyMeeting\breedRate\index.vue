<template>
  <div class="content">
    <div 
      class="content-item"
      style="flex: 1">
      <custom-table
        ref="customTable1"
        :title="'品种合格率'"
        :setting="tableObj1.setting"
        :url-list="tableObj1.url.list"
        :url-save="tableObj1.url.save"
        :select-date="selectDate"
        :table-cell-class="tableCellClass"
        :table-class="'big-table'">
        <template v-slot:headerRight>
          <span
            v-command="'/screen/technologyMeeting/edit'"
            class="screen-btn"
            @click="tableObj1.dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            指标维护
          </span>
        </template>
      </custom-table>
    </div>
    <el-dialog
      :visible.sync="tableObj1.dialogVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          合格率指标维护
        </div>
      </template>
      <el-form>
        <el-table
          :loading="loading"
          :data="tableObj1.gridData"
          border>
          <template
            v-for="(item, index) in tableObj1.setting1">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.children"
                :key="index"
                :width="item.width || ''"
                :label="item.label">
                <template
                  v-for="(cItem, cIndex) in item.children">
                  <el-table-column
                    :key="cIndex"
                    :width="cItem.width || ''"
                    :property="cItem.keySave"
                    :label="cItem.label">
                    <template v-slot="{ row }">
                      <template v-if="cItem.inputType === 'textarea'">
                        <el-input
                          v-model="row[cItem.keySave]"
                          :rows="4"
                          type="textarea"
                        />
                      </template>
                      <template v-else>
                        <el-input v-model="row[cItem.keySave]"/>
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </el-table-column>
              <template v-else>
                <el-table-column
                  v-if="item.type === 'index'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                />
                <template v-else>
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label">
                    <template v-slot="{ row }">
                      <template v-if="item.inputType === 'textarea'">
                        <el-input
                          v-model="row[item.keySave]"
                          :rows="4"
                          type="textarea"
                        />
                      </template>
                      <template v-else-if="item.inputType === 'date'">
                        <el-date-picker
                          v-model="row[item.keySave]"
                          :size="'mini'"
                          :value-format="'yyyy-MM-dd'"
                          type="date"
                          class="screen-input"/>
                      </template>
                      <template v-else>
                        <el-input v-model="row[item.keySave]"/>
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </template>
            </template>
          </template>
        </el-table>
      </el-form>
    </el-dialog>
    <div class="content-hold"/>
    <div 
      class="content-item"
      style="height: 260px; flex: none">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <custom-table
            :title="'当日非计划'"
            :setting="tableObj2.setting"
            :url-list="tableObj2.url.list"
            :url-save="tableObj2.url.save"
            :select-date="selectDate"
            :table-class="'big-table'"/>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <custom-table
            ref="customTable3"
            :title="'性能一次合格率'"
            :setting="tableObj3.setting"
            :url-list="tableObj3.url.list"
            :url-save="tableObj3.url.save"
            :select-date="selectDate"
            :table-cell-class="tableCellClass"
            :table-class="'big-table'"/>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/technologyMeeting/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/technologyMeeting/component/custom-table'
import {
  breedTargetFind,
  breedTargetFindConfig,
  breedTargetSave,
  breedTargetSaveAllConfig,
  NiSteelSaveAll,
  pfmcFirstPassRateFind,
  pfmcFirstPassRateSave,
  unplannedDayFind,
  unplannedDaySave
} from '@/api/screenTechnolagy'
import moment from 'moment'
import { post } from '@/lib/Util'
export default {
  name: 'BreedRate',
  components: { CustomTable, SingleBarsChart },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      loading: false,
      cDate: '',
      tableObj1: {
        url: {
          save: breedTargetSave,
          list: breedTargetFind
        },
        dialogVisible: false,
        setting: [
          {
            keyQuery: 'plt',
            keySave: 'plt',
            label: '产线',
            width: 80
          },
          {
            keyQuery: 'department',
            keySave: 'department',
            label: '科室'
          },
          {
            keyQuery: 'classification',
            keySave: 'classification',
            label: '分类'
          },
          {
            keyQuery: 'production',
            keySave: 'production',
            label: '产量'
          },
          {
            keyQuery: 'ycPassRate',
            keySave: 'ycPassRate',
            label: '一次合格率'
          },
          {
            keyQuery: 'bxPassRate',
            keySave: 'bxPassRate',
            label: '板形合格率'
          },
          {
            keyQuery: 'xnfjhPassRate',
            keySave: 'xnfjhPassRate',
            label: '性能非计划'
          },
          {
            keyQuery: 'zhPassRate',
            keySave: 'zhPassRate',
            label: '综合合格率'
          },
          {
            label: '指标',
            align: 'center',
            show: false,
            children: [
              {
                keyQuery: 'conYcPassRate',
                keySave: 'conYcPassRate',
                label: '一次合格率指标'
              },
              {
                keyQuery: 'conBxPassRate',
                keySave: 'conBxPassRate',
                label: '板形合格率指标'
              },
              {
                keyQuery: 'conXnfjhPassRate',
                keySave: 'conXnfjhPassRate',
                label: '性能非计划指标'
              },
              {
                keyQuery: 'conZhPassRate',
                keySave: 'conZhPassRate',
                label: '综合合格率指标'
              }
            ]
          },
          {
            keyQuery: 'reason',
            keySave: 'reason',
            label: '原因分析'
          }
        ],
        gridData: [],
        setting1: [
          {
            keyQuery: 'department',
            keySave: 'department',
            label: '科室'
          },
          {
            keyQuery: 'classification',
            keySave: 'classification',
            label: '分类'
          },
          {
            keyQuery: 'conYcPassRate',
            keySave: 'conYcPassRate',
            label: '一次合格率指标'
          },
          {
            keyQuery: 'conBxPassRate',
            keySave: 'conBxPassRate',
            label: '板形合格率指标'
          },
          {
            keyQuery: 'conXnfjhPassRate',
            keySave: 'conXnfjhPassRate',
            label: '性能非计划指标'
          },
          {
            keyQuery: 'conZhPassRate',
            keySave: 'conZhPassRate',
            label: '综合合格率指标'
          },
          {
            keyQuery: 'conXnycPassRate',
            keySave: 'conXnycPassRate',
            label: '性能一次合格率指标'
          }
        ]
      },
      tableObj3: {
        url: {
          save: pfmcFirstPassRateSave,
          list: pfmcFirstPassRateFind
        },
        setting: [
          {
            keyQuery: 'plt',
            keySave: 'plt',
            label: '产线',
            width: 80
          },
          {
            keyQuery: 'department',
            keySave: 'department',
            label: '科室'
          },
          {
            keyQuery: 'classification',
            keySave: 'classification',
            label: '分类'
          },
          {
            keyQuery: 'xnycPassRate',
            keySave: 'xnycPassRate',
            label: '性能一次合格率'
          },
          {
            keyQuery: 'conXnycPassRate',
            keySave: 'conXnycPassRate',
            label: '性能一次合格率指标',
            show: false
          },
          {
            keyQuery: 'determine',
            keySave: 'determine',
            label: '原因分析'
          }
        ]
      },
      tableObj2: {
        url: {
          save: unplannedDaySave,
          list: unplannedDayFind
        },
        setting: [
          {
            keyQuery: 'stlgrd',
            keySave: 'stlgrd',
            label: '钢种',
            width: 200
          },
          {
            keyQuery: 'plt',
            keySave: 'plt',
            label: '产线',
            width: 80
          },
          {
            keyQuery: 'changeJudgmentReason',
            keySave: 'changeJudgmentReason',
            label: '改判原因'
          },
          {
            keyQuery: 'changeJudgmentQuantity',
            keySave: 'changeJudgmentQuantity',
            label: '改判量',
            width: 120
          },
          {
            keyQuery: 'reason',
            keySave: 'reason',
            label: '原因分析'
          },
          {
            keyQuery: 'measure',
            keySave: 'measure',
            label: '措施'
          }
        ]
      }
    }
  },
  computed: {
    selectedMonth: function() {
      return moment(this.selectDate).format('YYYY-MM')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.getIndexData()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.getIndexData()
  },
  methods: {
    tableCellClass(obj) {
      if (
        obj.column.property === 'ycPassRate' &&
        obj.row[obj.column.property] - obj.row['conYcPassRate'] < 0
      ) {
        return 'red'
      }
      if (
        obj.column.property === 'bxPassRate' &&
        obj.row[obj.column.property] - obj.row['conBxPassRate'] < 0
      ) {
        return 'red'
      }
      if (
        obj.column.property === 'xnfjhPassRate' &&
        obj.row[obj.column.property] - obj.row['conXnfjhPassRate'] > 0
      ) {
        return 'red'
      }
      if (
        obj.column.property === 'zhPassRate' &&
        obj.row[obj.column.property] - obj.row['conZhPassRate'] < 0
      ) {
        return 'red'
      }
      if (
        obj.column.property === 'xnycPassRate' &&
        obj.row[obj.column.property] - obj.row['conXnycPassRate'] < 0
      ) {
        return 'red'
      }
      return ''
    },
    getIndexData() {
      post(breedTargetFindConfig, {}).then(res => {
        this.tableObj1.gridData = res.data
      })
    },
    saveData() {
      this.loading = true
      // 数据信息
      const params = {
        data: this.tableObj1.gridData.map(item => {
          return item
        })
      }
      post(breedTargetSaveAllConfig, params).then(res => {
        //
        this.loading = false
        if (res.status == 1) {
          this.$message.success('保存成功！')
          this.$refs.customTable1.getData()
          this.$refs.customTable3.getData()
          this.tableObj1.dialogVisible = false
          this.getData()
        } else {
          this.$message.warning('保存失败！')
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
