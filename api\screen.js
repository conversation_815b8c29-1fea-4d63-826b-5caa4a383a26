const path = 'mesAPI/'
const pathQuality = 'orgApi/'
const pathQuality2 = 'orgApi2/'
const ztkb = 'ztkb/'
const ems = 'ems/'

//钢产量
export const saveSteelOutput = path + 'steelOutput/saveSteelOutput'
export const findSteelOutputByDate = path + 'steelOutput/findSteelOutputByDate'
export const findSteelOutputByDate1 =
  path + 'steelOutput/findSteelOutputByDate1'
// 热处理产量
export const saveHeatTreatmentYield =
  path + 'HeatTreatmentYield/saveHeatTreatmentYield'
export const findHeatTreatmentYieldByDate =
  path + 'HeatTreatmentYield/findHeatTreatmentYieldByDate'

//收得率明细表 => 查询接口
export const FullProcessYieldFindDetailsDate =
  path + 'FullProcessYield/findDetailsDate'
//收得率明细表 => 订单号查询
export const FullProcessYieldFindDetailsDateByOrder =
  path + 'FullProcessYield/FindOrderNoBySteelOrder'

//综合-坯料
export const FindSlabFullProcess = path + 'slabFullProcess/findSlabFullProcess'
//钢板-原钢种
export const FindQltFullProcessy = path + 'slabFullProcess/findQltFullProcessY'
//钢板-综合
export const FindQltFullProcessz = path + 'slabFullProcess/findQltFullProcessZ'
//收得率明细表
export const FullProcessYieldFindSummaryDate =
  path + 'FullProcessYield/findSummaryDate'
//收得率分析表
export const AnalysisSlab = path + 'slabFullProcess/AnalysisSlab'
export const SlabPanFei = path + 'slabFullProcess/SlabPanFei'
export const findDetailsDateNoPage =
  path + 'FullProcessYield/findDetailsDateNoPage'
export const findDetailsDateQlt = path + 'FullProcessYield/findDetailsDateQlt'
export const AnalysisSlabQltPlt = path + 'slabFullProcess/AnalysisSlabQltPlt'
export const AnalysisSlabQlt = path + 'slabFullProcess/AnalysisSlabQlt'
export const SlabQltPanFei = path + 'slabFullProcess/SlabQltPanFei'
//收得率钢种输入框
export const FindSteelGrade = path + 'FullProcessYield/FindSteelGrade'
//明细表  -后端导出
export const findDetailsDateExcel =
  path + 'FullProcessYield/findDetailsDateExcel'

//钢原钢种收得率：
// mesAPI/FullProcessYield/FindYieldRateSteel
export const FindYieldRateSteel = path + 'FullProcessYield/FindYieldRateSteel'
//收得率-原钢种：
// mesAPI/FullProcessYield/FindYieldRateSteel1
export const FindYieldRateSteel1 = path + 'FullProcessYield/FindYieldRateSteel1'
//收得率-钢板综合：
// mesAPI/FullProcessYield/FindYieldRateSteel2
export const FindYieldRateSteel2 = path + 'FullProcessYield/FindYieldRateSteel2'
//收得率-项目订单
export const FullProcessYieldFindAll = path + 'FullProcessYield/findAllDate'
//收得率-项目订单
export const FullProcessYieldSave = path + 'FullProcessYield/saveOrderData'
//收得率-项目订单
export const FindOrderYieldRateSteel =
  path + 'FullProcessYield/FindOrderYieldRateSteel'
export const FindSteelOrder = path + 'FullProcessYield/FindSteelOrder'
//重点订单刷新
export const ScheduledTasks2 = path + 'FullProcessYield/ScheduledTasks2'
// 跟踪事项
export const findKeyTrackingItemsByDate =
  path + 'keyTrackingItems/findKeyTrackingItemsByDate'
export const saveKeyTrackingItems =
  path + 'keyTrackingItems/saveKeyTrackingItems'
export const KeyTrackingUpdateById = path + 'keyTrackingItems/updateById'
export const keyTrackingDeleteById = path + 'keyTrackingItems/deleteById'

export const savehtFpylestPerformance =
  path + 'htFpyTestPerformance/savehtFpyTestPerformance'
export const findHtFpylestPerformanceByDate =
  path + 'htFpyTestPerformance/findHtFpyTestPerformanceByDate'
// 在制品
export const saveWipInventoryTracking =
  path + 'wipInventoryTracking/saveWipInventoryTracking'
export const findWipInventoryTrackingByDate =
  path + '/wipInventoryTracking/findWipInventoryTrackingByDate'
export const ydayCutDisposeAmountQuery =
  'mesAPI/mesApi/YdayCutDisposeAmountQuery'
//工序处理跟着 性能待委托
export const PerformanceWait = path + 'PerformanceWait/findAllDate'

//规则说明
export const PageRuleDesSaveAll = 'mesAPI/PageRuleDes/saveAll'
export const PageRuleDesFindAllDate = 'mesAPI/PageRuleDes/findAllDate'
// 后道工序作业率
export const findDivDailyRate =
  '/ddm/followProcessActivityRateController/findDivDailyRate.ddm'
export const findEQStatus =
  '/ddm/followProcessActivityRateController/findEQStatus.ddm'
// 轧钢计划
export const saveSteelRollingPlan =
  path + 'steelRollingPlan/saveSteelRollingPlan'
export const findSteelRollingPlanByDate =
  path + 'steelRollingPlan/findSteelRollingPlanByDate'

// 炼钢计划
export const saveSteelmakingPlan = path + 'steelmakingPlan/saveSteelmakingPlan'
export const findSteelmakingPlanByDate =
  path + 'steelmakingPlan/findSteelmakingPlanByDate'

// 三个厂订单
export const saveThreeFactoryOrder =
  path + 'threeFactoryOrder/saveThreeFactoryOrder'
export const findThreeFactoryOrderByDate =
  path + 'threeFactoryOrder/findThreeFactoryOrderByDate'
//金润订单
export const JinrunFactoryOrderFind = path + 'JinrunFactoryOrder/findAllDate'
export const JinrunFactoryOrderSave = path + 'JinrunFactoryOrder/saveAll'
//金润机加工设备利用率
export const EquipmentUtilizationFind =
  path + 'EquipmentUtilization/findAllDate'
export const EquipmentUtilizationSave = path + 'EquipmentUtilization/saveAll'
// 资源确认
export const saveResourceConfirm = path + 'resourceConfirm/saveResourceConfirm'
export const findResourceConfirmByDate =
  path + 'resourceConfirm/findResourceConfirmByDate'

// 热处理炉检修计划表
export const saveHtFurnaceMantancePlan =
  path + 'htFurnaceMantancePlan/saveHtFurnaceMantancePlan'
export const findHtFurnaceMantancePlanByDate =
  path + 'htFurnaceMantancePlan/findHtFurnaceMantancePlanByDate'

// 非计划订单
export const saveOrderCorrection = path + 'orderCorrection/saveOrderCorrection'
export const findOrderCorrectionByDate =
  path + 'orderCorrection/findOrderCorrectionByDate'

// 非计划订单
export const saveCorrectionMajor = path + 'correctionMajor/saveCorrectionMajor'
export const findCorrectionMajorByDate =
  path + 'correctionMajor/findCorrectionMajorByDate'

// 非计划改判汇总
export const saveCorrectionGather =
  path + 'correctionGather/saveCorrectionGather'

export const findCorrectionGatherByDate =
  path + 'correctionGather/findCorrectionGatherByDate'

export const findWipInventoryByDate =
  path + 'wipInventory/findWipInventoryByDate'
// 在制品
export const findZrTotal = path + 'wipInventory/findZrTotal'
export const findWipInventoryPltZj = path + 'wipInventory/findWipInventoryPltZj'
export const findWipInventory = path + 'wipInventory/findWipInventory'
export const findWorkDetail = path + 'blankStock/findWorkDetail'
// 坯料
export const findBlankDetail = path + 'blankStock/findBlankDetail'
export const findBlankStock = path + 'blankStock/findBlankStock'
export const findBlankStockPltZj = path + 'blankStock/findBlankStockPltZj'
export const findBlankStockDetails = path + 'blankStock/findBlankStockDetails'

// 参数
export const findBoardParameterByDateAndPara =
  path + 'boardParameter/findBoardParameterByDateAndPara'
export const saveBoardParameter = path + 'boardParameter/saveBoardParameter'
export const findBlankSurplus = path + 'blankStock/findBlankSurplus'

//行车故障
//查询
export const DRIVINGFAILURE_DATA = path + '/DrivingMalfunction/findAllDate'
//保存
export const DRIVINGFAILURE_SAVE = path + '/DrivingMalfunction/saveAll'

// 协调事项
export const saveCoordinationMatters =
  path + 'coordinationMatters/saveCoordinationMatters'
export const findCoordinationMattersByDate =
  path + 'coordinationMatters/findCoordinationMattersByDate'

// 复盘
export const saveSteelmakingReplay =
  path + 'steelmakingReplay/saveSteelmakingReplay'
export const findSteelmakingReplayByDate =
  path + 'steelmakingReplay/findSteelmakingReplayByDate'
export const steelRollingReplaySave = path + 'steelRollingReplay/saveAll'
export const steelRollingReplayByDate =
  path + 'steelRollingReplay/findAllByDate'

export const eleSplit = '/ems/generator/indexELE/eleSplit'
export const eleSplitForPlatform = '/ems/generator/indexELE/eleSplitForPlatform'

// 延误
export const findDelayDataToBC = path + 'delay/findDelayDataToBC'
export const findDelayNumAndHourToBC = path + 'delay/findDelayNumAndHourToBC'

// 延误
export const finishProductOrderSave = path + 'finishProductOrder/saveAll'
export const finishProductOrderFind =
  path + 'finishProductOrder/findAllBySetDate'

// 看板同步任务接口
// 昨日钢板降级信息接口
// ************:9800/mesAPI/correctionGather/ScheduledTasks
export const correctionGatherTask = path + 'correctionGather/ScheduledTasks'

// 主要缺陷改判接口
// ************:9800/mesAPI/correctionMajor/ScheduledTasks
export const correctionMajorTask = path + 'correctionMajor/ScheduledTasks'
//
// 热处理产量接口
// ************:9800/mesAPI/HeatTreatmentYield/ScheduledTasks
export const HeatTreatmentYieldTask = path + 'HeatTreatmentYield/ScheduledTasks'
//
// 资源确认接口
// ************:9800/mesAPI/resourceConfirm/ScheduledTasks
export const resourceConfirmTask = path + 'resourceConfirm/ScheduledTasks'
//
// 炼钢计划接口
// ************:9800/mesAPI/steelmakingPlan/ScheduledTasks
export const steelmakingPlanTask = path + 'steelmakingPlan/ScheduledTasks'
//
// 钢产量接口
// ************:9800/mesAPI/steelOutput/ScheduledTasks
export const steelOutputTask = path + 'steelOutput/ScheduledTasks'
export const SteelRolligIntervalFindAllDate =
  path + 'SteelRolligInterval/findAllDate'
export const SteelRolligIntervalSaveAll = path + 'SteelRolligInterval/saveAll'
//
// 三个厂订单情况接口
// ************:9800/mesAPI/threeFactoryOrder/ScheduledTasks
export const threeFactoryOrderTask = path + 'threeFactoryOrder/ScheduledTasks'
//
// 在制品库存及日均处置跟踪
// ************:9800/mesAPI/wipInventoryTracking/ScheduledTasks
export const wipInventoryTrackingTask =
  path + 'wipInventoryTracking/ScheduledTasks'

// 质量晨会看板
// 质量体系
export const qmsQualitySystem = path + 'QualitySystem/findQualitySystemByDate'
export const qmsQualitySystemSaveNew = path + 'QualitySystem/saveQualitySystem'
// 炼钢质量管理
export const qmsRawAuxiliaryManageQuery =
  path + 'RawAuxiliaryManage/findRawAuxiliaryManageByDate'
export const qmsRawAuxiliaryManageSave =
  path + 'RawAuxiliaryManage/saveRawAuxiliaryManage'
// 炼钢质量管理
export const qmsQualityQuery =
  path + 'MakingsteelQualityControl/findMakingsteelQualityControlByDate'
export const qmsQualitySave =
  path + 'MakingsteelQualityControl/saveMakingsteelQualityControl'
export const QualityQuery = path + 'steelMakingQuality/findAllBySetDate'
export const QualitySave = path + 'steelMakingQuality/saveAll'
// 炼钢质量管理 - 裂纹量
export const findFlawSituationByDate =
  path + 'FlawSituation/findFlawSituationByDate'
export const saveFlawSituation = path + 'FlawSituation/saveFlawSituation'
// 炼钢质量管理 - 裂纹
export const qmsQualityQueryPlateFlaw =
  path + 'PlateCrackOccurDecide/findPlateCrackOccurDecideByDate'
export const qmsQualitySavePlateFlaw =
  path + 'PlateCrackOccurDecide/savePlateCrackOccurDecide'
// 炼钢质量管理 - 探伤
export const findInspectionPassRateByDate =
  path + 'InspectionPassRate/findInspectionPassRateByDate'
export const saveInspectionPassRate =
  path + 'InspectionPassRate/saveInspectionPassRate'
// 坯原钢种一次合格率
export const findSlabYgzFirstPassRate =
  path + 'SlabYgzFirstPassRate/findAllByDate'
export const saveSlabYgzFirstPassRate = path + 'SlabYgzFirstPassRate/saveAll'
// 铁水
export const qmsMoltenIronCompositionQuery =
  path + 'MoltenIronComposition/findMoltenIronCompositionByDate'
export const qmsMoltenIronCompositionSave =
  path + 'MoltenIronComposition/saveMoltenIronComposition'
// 铁水mes
export const castingMachine = path + 'MoltenIronComposition/castingMachine'
// P
export const qmsPPotencyScaleQuery =
  path + 'PPotencyScale/findPPotencyScaleByDate'
export const qmsPPotencyScaleSave = path + 'PPotencyScale/savePPotencyScale'
// P含量 mes
export const pproportionQuery = path + 'mesApi/pproportionQuery'
// 轧钢质量
export const qmsRollingsteelQualityControlQuery =
  path + 'RollingsteelQualityControl/findRollingsteelQualityControlByDate'
export const qmsRollingsteelQualityControlSave =
  path + 'RollingsteelQualityControl/saveRollingsteelQualityControl'
// 质量监督
export const qmsQualitySupervisionQuery =
  path + 'QualitySupervision/findQualitySupervisionByDate'
export const qmsQualitySupervisionSave =
  path + 'QualitySupervision/saveQualitySupervision'
// 质量监督 - 项目
export const findSuperviseProjectByDate =
  path + 'SuperviseProject/findSuperviseProjectByDate'
export const saveSuperviseProject =
  path + 'SuperviseProject/saveSuperviseProject'
// 质量异议
export const qmsQualityObjectionsQuery =
  path + 'QualityObjections/findQualityObjectionsByDate'
export const qmsQualityObjectionsSave =
  path + 'QualityObjections/saveQualityObjections'
// 工艺报警
// 炼钢厂
export const processAlarmB1 = path + 'ProcessAlarm/processAlarmB1'
// 轧钢厂
export const processAlarm = path + '/ProcessAlarm/processAlarm'
export const findProcessAlarmByDate =
  path + 'ProcessAlarm/findProcessAlarmByDate'
export const saveProcessAlarm = path + 'ProcessAlarm/saveProcessAlarm'
// 一次合格率
export const findPerformancePassByDate =
  path + 'PerformancePass/findPerformancePassByDate'
export const savePerformancePass = path + 'PerformancePass/savePerformancePass'
// 板形合格率
export const findShapePassByDate = path + 'ShapePass/findShapePassByDate'
export const saveShapePass = path + 'ShapePass/saveShapePass'
// 材一次合格率
export const findPlateYgzFirstPassRate =
  path + 'PlateYgzFirstPassRate/findAllByDate'
export const savePlateYgzFirstPassRate = path + 'PlateYgzFirstPassRate/saveAll'
// 缺陷改判排名前三
export const findDefectCorrectionByDate =
  path + 'DefectCorrection/findDefectCorrectionByDate'
export const saveDefectCorrection =
  path + 'DefectCorrection/saveDefectCorrection'
export const getDataByToday = path + 'CADReviewResume/getDataByToday1'
export const getDataByToday2 = path + 'CADReviewResume/getEstDataByToday'
export const surplusMaterials = path + 'DefectCorrection/surplusMaterials'
// 裂纹发生率
export const incidenceRateDetailed = path + 'qms/incidenceRateDetailed'
// 裂纹改判率
export const correctionRateDetailed = path + 'qms/correctionRateDetailed'
// 探伤合格率率
export const detectionDetailed = path + 'qms/detectionDetailed'
// 工艺报警
export const processAlarmDetailed = path + 'qms/processAlarmDetailed'
// 探伤合格率 、性能一次合格率
export const pfmcFirstPassRateDetailed = path + 'qms/pfmcFirstPassRateDetailed'
// 材原钢种一次合格率详情
export const plateYgzFirstPassRateDetails =
  path + 'PlateYgzFirstPassRate/plateYgzFirstPassRateDetails'
// 成分合格率
export const getChemQualified = pathQuality + 'monitor/sm-cc/get-chem-qualified'
// 成分合格率
export const getChemQualifiedDetail =
  pathQuality + 'monitor/sm-cc/get-detail-components'
// 坯原钢种一次合格率
export const rsgfbRate = path + '/qms/rsgfbRate'
// 坯原钢种一次合格率详情(esm)
export const rsgfbRateDetails2 = path + '/qms/rsgfbRateDetails'
// 坯原钢种一次合格率详情(新)
export const qmsPygzFirstpassrateNew = path + 'QmsPygzFirstpassrate/findAllDate'
export const qmsPygzFirstpassrateSaveNew = path + 'QmsPygzFirstpassrate/saveAll'

export const rsgfbRateDetails = path + 'SteelYieldLine/findAllByDate'
// 材原钢种一次合格率
export const findSubjectData = '/idm/api/findSubjectData.idm'
// 原钢种一次合格率
export const WidePlateRate = path + 'planProductionMonth/WidePlateRate'

// 探伤合格率 、性能一次合格率
export const targetDetailed = path + 'qms/target'
// 裂纹发生率、改判率 时间
export const incidenceRate = path + 'qms/incidenceRate'
// 板形合格率 时间
export const shapePass = path + 'qms/shapePass'

export const findFlawSituationConfigByDate =
  path + '/FlawSituationConfig/findFlawSituationByDate'
export const saveFlawSituationConfig =
  path + '/FlawSituationConfig/saveFlawSituation'

// 生产关键指标接口
export const findKeyIndic = path + '/keyIndic/findBySetDate'
export const saveKeyIndic = path + '/keyIndic/savePlan'
export const saveKeyResul = path + '/keyIndic/saveKeyResult'
export const findKeyResultByDate = path + '/keyIndic/findKeyResultByDate'
export const findKeyResultByDate1 = path + '/keyIndic/findKeyResultByDate1'
export const findKeyResultByDatezp = path + '/keyIndic/findKeyResultByDate'
export const TripleOrderTrack = path + '/TripleOrderTrack/findAllDate'
export const TripleOrderTrackSave = path + '/TripleOrderTrack/saveAll'

// 一炼钢晨会
export const firstMorningMeeting = {
  // 质量指标 数据修改
  qualityFirst: pathQuality2 + 'lgboard/quality/indication/update',
  // 质量指标 查询
  qualityFirstInit: pathQuality2 + 'lgboard/quality/indication/init',
  // 质量检化验 数据修改
  inspection: pathQuality2 + 'lgboard/quality/dailyNcontent/update',
  // 质量检化验 查询
  inspectionInit: pathQuality2 + 'lgboard/quality/dailyNcontent/init',
  // 备注
  notes: pathQuality2 + 'lgboard/quality/dailyNotes/update',
  //转炉炉况详情
  furnaceConditionInit: pathQuality2 + 'lgboard/quality/funcondition/init',
  //转炉炉况
  furnaceCondition: pathQuality2 + 'lgboard/quality/funcondition/update',
  // 文件上传
  fileUpdata: pathQuality2 + 'lgboard/quality/WriteIO',
  // 文件删除
  fileDelete: pathQuality2 + 'lgboard/quality/DeleteIO',
  // 新转炉存数据
  bofcdDataUpdate: pathQuality2 + 'lgboard/quality/bofcd/update',
  // 新转炉存数据查询
  bofcdDataInit: pathQuality2 + 'lgboard/quality/bofcd/init',
  // 工艺检查
  processInspectionInit:
    pathQuality2 + 'lgboard/quality/processInspection/init',
  processInspectionUpdate:
    pathQuality2 + 'lgboard/quality/processInspection/update',
  // 删除
  processInspectionDelete: id =>
    pathQuality2 + 'lgboard/quality/processInspection/delete?id=' + id,
  // 品种钢质量
  varietyStlqlyInit: pathQuality2 + 'lgboard/quality/varietyStlqly/init',
  varietyStlqly: pathQuality2 + 'lgboard/quality/varietyStlqly/update',
  // 工艺异常
  processUpsetsInit: pathQuality2 + 'lgboard/quality/processUpsets/init',
  processUpsets: id =>
    pathQuality2 + 'lgboard/quality/processUpsets/update?start=' + id,
  // 非计划
  spotsPlanInit: pathQuality2 + 'lgboard/quality/spotsPlan/init',
  spotsPlan: id =>
    pathQuality2 + 'lgboard/quality/spotsPlan/update?dateTime=' + id,
  //cad
  report: ztkb + 'cad/report',
  //cad更新
  reportUpData: ztkb + 'cad/comment',
  //cad更新
  reportUpDatas: ztkb + 'cad/comments',
  //车间明细
  statistics: ztkb + 'cad/statistics',
  //车间明细
  statisticsNew: ztkb + 'cad/statisticsNew',
  //cad月统计
  cadMonth: time => ztkb + 'cad/month/' + time,
  //cad 车间
  cadWorkshop: time => ztkb + 'cad/workshop/' + time,
  //cad 班组
  cadGroup: time => ztkb + 'cad/group/' + time
}
/**
 *  中厚板卷厂综合看板
 * */
export const coilScreen = {
  // 数据查询
  coilInit: pathQuality2 + 'bjboard/quality/supervision/init',
  // 更新
  coilUpdate: pathQuality2 + 'bjboard/quality/supervision/update',
  // 删除
  coilDelete: id => pathQuality2 + 'bjboard/quality/supervision/delete?id=' + id
}

//点击曲线在制品库存详情
export const LINEBYDATE = path + 'workProcessStock/findAllByDate'
//点击曲线在制品库存详情
export const SAVE_ALL = path + 'workProcessStock/SaveAll'

//中厚板卷厂调度看板-主生产线
//当月轧制/终判
export const ROLLDECIDE = path + 'RollingMonth/findAllDate'
export const ROLLDECIDE_SAVE = path + 'RollingMonth/saveAll'

//班组轧制节奏
export const ROLLINGRHYTHM = path + 'RollingRhythm/findAllDate'
export const ROLLINGRHYTHM_SAVE = path + 'RollingRhythm/saveAll'

//相关方考核
export const ASSESS = path + 'StakeholderEvaluation/findAllDate'
export const ASSESS_SAVE = path + 'StakeholderEvaluation/saveAll'

//原因说明
export const CAUSE_EXPLAIN = path + 'RollingReason/findAllDate'
export const CAUSE_EXPLAIN_SAVE = path + 'RollingReason/saveAll'

//中厚板卷厂调度看板-热处理
//当月热处理
export const MONTH_HEATTREATMENT = path + 'HeatTreatmentMonth/findAllDate'
export const MONTH_HEATTREATMENT_SAVE = path + 'HeatTreatmentMonth/saveAll'
//安全环保
export const HESIncidentSave = path + 'HESIncident/saveAll'
export const HESIncidentFindall = path + 'HESIncident/findAllDate'
//危险作业/施工公示
export const HazardNoticetSave = path + 'HazardNotice/saveAll'
export const HazardNoticeFindall = path + 'HazardNotice/findAllDate'
//班次热处理
export const SAIL_HEATTREATMENT = path + 'ShiftHeatTreatment/findAllDate'
export const SAIL_HEATTREATMENT_SAVE = path + 'ShiftHeatTreatment/saveAll'

//中板厂调度看板-安全
//安全检查情况查询
export const SECURITYCHECKS = path + '/MppSecurityCheck/findAllDate'
//安全检查情况保存
export const SECURITYCHECKSSAVE = path + '/MppSecurityCheck/saveAll'
//施工项目安全检查情况查询
export const PROJECTSECURITY =
  path + '/MppConstructionProSecurityCheck/findAllDate'
//施工项目安全检查情况保存
export const PROJECTSECURITYSAVE =
  path + '/MppConstructionProSecurityCheck/saveAll'

//中板厂调度看板
//-----------------------------------质量
//重点类别跟踪情况查询
export const FOCUSEDTYPEDATA = path + '/MppKeyCategoryTracking/findAllDate'
//重点类别跟踪情况保存
export const FOCUSEDTYPESAVE = path + '/MppKeyCategoryTracking/saveAll'
//胜代订单生产情况况查询
export const FOCUSEDORDERSDATA = path + '/MppKeyOrderTracking/findAllDate'
//胜代订单生产情况况保存
export const FOCUSEDORDERSSAVE = path + '/MppKeyOrderTracking/saveAll'
//胜代订单生产情况上传图片
export const FOCUSEDORDERSUPDATA = path + '/MppKeyOrderTracking/updateFileId'
//产品质量跟踪查询
export const PRODUCTQUALITYDATA =
  path + '/MppProductQualityTracking/findAllDate'
//产品质量跟踪保存
export const PRODUCTQUALITYSAVE = path + '/MppProductQualityTracking/saveAll'
//成材率查询
export const YIELDDATA = path + '/MppYield/findAllDate'
//成材率保存
export const YIELDSAVE = path + '/MppYield/saveAll'
//非计划数据统计查询
export const UNPLANNEDDATA = path + '/MppUnplanned/findAllDate'
//非计划数据统计保存
export const UNPLANNEDSAVE = path + '/MppUnplanned/saveAll'

//-----------------------------------本部生产
//生产报告
export const PRODUCEREPORT = path + 'MppProductionReport/findAllDate'
export const PRODUCEREPORT_SAVE = path + 'MppProductionReport/saveAll'

//剪切生产报告
export const CUT_PRODUCTION_DATA = path + 'MppCutProductionReport/findAllDate'
export const CUT_PRODUCTION_SAVE = path + 'MppCutProductionReport/saveAll'

//合同跟踪报告
export const CT_REPORTS = path + 'MppContractTrackingReport/findAllDate'
export const CT_REPORTS_SAVE = path + 'MppContractTrackingReport/saveAll'

//生产日报表
export const PRODUCEDAYTABLE = path + 'MppProductionDayReport/findAllDate'
export const PRODUCEDAYTABLE_SAVE = path + 'MppProductionDayReport/saveAll'

//-----------------------------------港池生产
//热处理炉
export const HP_DATA1 = path + 'MppHarborBasinHeatTreatment/findAllDate'
export const HP_SAVE1 = path + 'MppHarborBasinHeatTreatment/saveAll'
//台车炉
export const HP_DATA2 = path + 'MppTrolleyFurnace/findAllDate'
export const HP_SAVE2 = path + 'MppTrolleyFurnace/saveAll'
//生产报告
export const HP_DATA3 = path + 'MppHarborBasinProductionDayReport/findAllDate'
export const HP_SAVE3 = path + 'MppHarborBasinProductionDayReport/saveAll'

//港池生产报告-热处理炉
export const HP_HT_STOVE = path + 'RCLGCProductionReport/findAllDate'
export const HP_HT_STOVE_SAVE = path + 'RCLGCProductionReport/saveAll'

//生产计划
export const PRODUCEPLAN = path + 'ProductionPlan/findAllDate'
export const PRODUCEPLAN_SAVE = path + 'ProductionPlan/saveAll'

//-----------------------------------库存分析
//中板库坯料报表
export const INVENTORYANALYSIS_DATA1 =
  path + '/MppLibraryBlankReport/findAllDate'
export const INVENTORYANALYSIS_SAVE1 = path + '/MppOrderBlankReport/findAllDate'
//中板订单坯料报表
export const INVENTORYANALYSIS_DATA2 =
  path + '/MppLibraryBlankReport/findAllDate'
export const INVENTORYANALYSIS_SAVE2 = path + '/MppOrderBlankReport/saveAll'
//在制品统计
export const INVENTORYANALYSIS_DATA3 = path + '/MppWorkInProgress/findAllDate'
export const INVENTORYANALYSIS_SAVE3 = path + '/MppWorkInProgress/saveAll'
//中板半成品运转
export const INVENTORYANALYSIS_DATA4 =
  path + '/MppSemiFinishedProductOperation/findAllDate'
export const INVENTORYANALYSIS_SAVE4 =
  path + '/MppSemiFinishedProductOperation/saveAll'

//-----------------------------------计划
//炼钢计划
export const PLAN_DATA1 = path + '/MppSteelmakingPlan/findAllDate'
export const PLAN_SAVE1 = path + '/MppSteelmakingPlan/saveAll'
//生产计划
export const PLAN_DATA2 = path + '/MppProductionPlan/findAllDate'
export const PLAN_SAVE2 = path + '/MppProductionPlan/saveAll'

//-----------------------------------设备
//设备运行(本部)
export const DEVICE_DATA1 = path + '/MppDeviceRun/findAllSelfDate'
export const DEVICE_SAVE1 = path + '/MppDeviceRun/saveAllSelf'
//设备运行(港池)
export const DEVICE_DATA2 = path + '/MppDeviceRun/findAllBasinDate'
export const DEVICE_SAVE2 = path + '/MppDeviceRun/saveAllBasin'

//中厚板卷厂安全看板
//施工明细
//查询
export const CD_INQUIRE = path + 'QmsZhbjSafetyBoard/getConstructionDetails'
//下载
// export const CD_DOWNLOAD = path + 'QmsZhbjSafetyBoard/exportConstructionDetails'
//新增
export const CD_NEWS = path + 'QmsZhbjSafetyBoard/saveConstructionDetails'
//删除
export const CD_DEL = path + '/QmsZhbjSafetyBoard/deleteConstructionDetails'

//隐患随手拍
//查询
export const HT_INQUIRE = path + 'QmsZhbjSafetyBoard/getSecurityCheck'
//新增
export const HT_NEWS = path + 'QmsZhbjSafetyBoard/saveSecurityCheck'
/*文件上传*/
export const minio_upload = 'dsm/minio/upload.dsm'
//删除
export const HT_DEL = path + 'QmsZhbjSafetyBoard/deleteSecurityCheck'
//完成情况
export const HT_COMPLETION = path + 'QmsZhbjSafetyBoard/getSecurityCheckCount'

//文件宣贯
//查询
export const DP_INQUIRE = path + 'QmsZhbjSafetyBoard/getFileIssue'
//新增
export const DP_NEWS = path + 'QmsZhbjSafetyBoard/saveFileIssue'
//删除
export const DP_DEL = path + 'QmsZhbjSafetyBoard/deleteFileIssue'

//考核通报
//查询
export const AN_INQUIRE = path + 'QmsZhbjSafetyBoard/getAssessmentNotification'
//新增
export const AN_NEWS = path + 'QmsZhbjSafetyBoard/saveAssessmentNotification'
//删除
export const AN_DEL = path + 'QmsZhbjSafetyBoard/deleteAssessmentNotification'

//==============全流程收得率主题看板：因子分析===============
//炼钢因子分析 - 新接口
export const findSteelmakingAnalysis1 =
  path + 'slabFullProcess/SteelmakingAnalysis1'

//轧钢因子分析 - 新接口
export const findSteelmakingAnalysis2 =
  path + 'slabFullProcess/SteelmakingAnalysis2'

export const findFactorAnalysis = path + 'slabFullProcess/SteelmakingAnalysis'
//炼钢因子分析弹窗:slabFullProcess/SlabPanFei1
export const SlabPanFei1 = path + 'slabFullProcess/SlabPanFei1'
// 坯料替代、坯料判废、坯料库存弹窗：ullProcessYield/findDetailsDateNoPage1
export const findDetailsDateNoPage1 =
  path + 'FullProcessYield/findDetailsDateNoPage1'
//轧钢因子分析弹窗:slabFullProcess/SlabQltPanFei1
export const SlabQltPanFei1 = path + 'slabFullProcess/SlabQltPanFei1'
//材原钢种改判和协议、材现货、材判废弹窗：FullProcessYield/findDetailsDateQlt1
export const findDetailsDateQlt1 = path + 'FullProcessYield/findDetailsDateQlt1'

//全流程收得率主题看板：重点钢种

//============中厚板卷厂质量日报:缺陷汇总及重点品种===============
//新 - 缺陷汇总
export const findDefectSummaryByDate =
  path + 'MtDailyReport/findDefectSummaryByDate'

//在制品统计
export const findWorkInProgressByDate =
  path + 'MtDailyReport/findWorkInProgressByDate'

// 过程非计划
// 查询
export const processNonPlan = path + 'QmsProcessUnplanned/findAllDate'
// 保存
export const processNonPlanSave = path + 'QmsProcessUnplanned/saveAll'

// ERP非计划
// 查询
export const erpNonPlan = path + 'QmsErpUnplanned/findAllDate'
// 保存
export const erpNonPlanSave = path + 'QmsErpUnplanned/saveAll'

// 在线重点品种
// 查询
export const onlineKeyVariety = path + 'QmsOnlineKeyVarieties/findAllDate'
// 保存
export const onlineKeyVarietySave = path + 'QmsOnlineKeyVarieties/saveAll'

// 热处理重点品种
// 查询 - 旧
export const heatTreatmentKeyVariety =
  path + 'QmsHeatTreatmentKeyVarieties/findAllDate'

// 查询 - 新
export const findAllHeatTreatmentKeyVarietiesDate =
  path + 'MtDailyReport/findAllHeatTreatmentVarietiesDate'

// 保存
export const heatTreatmentKeyVarietySave =
  path + 'QmsHeatTreatmentKeyVarieties/saveAll'

//新 - 在线重点品种
// 查询
export const findAllOnlineKeyVarietiesDate =
  path + 'MtDailyReport/findAllOnlineKeyVarietiesDate'

//============中厚板卷厂质量日报:技经指标及非计划===============
// 原始非计划
// 查询 - 旧
export const originalNonPlan = path + 'QmsOriginalUnplanned/findAllDate'

// 查询 - 新
export const findAllOriginalUnplannedDate =
  path + 'MtDailyReport/findAllOriginalUnplannedDate'

// 保存
export const originalNonPlanSave = path + 'QmsOriginalUnplanned/saveAll'

// 终判产量及综合非计划率情况
// 查询
export const finalJudgeProduction =
  path + 'QmsFinalVerdictOrUnplannedRate/findAllDate'
// 保存
export const finalJudgeProductionSave =
  path + 'QmsFinalVerdictOrUnplannedRate/saveAll'

//========技经指标及非计划========
//过程非计划
export const findAllProcessUnplannedDate =
  path + 'MtDailyReport/findAllProcessUnplannedDate'

//erp非计划
export const findAllErpUnplannedDate =
  path + 'MtDailyReport/findAllErpUnplannedDate'

// 技经指标统计
// 查询
export const technicalEconomicIndicators =
  path + 'QmsJjIndicatorStatistics/findAllDate'
// 保存
export const technicalEconomicIndicatorsSave =
  path + 'QmsJjIndicatorStatistics/saveAll'

//新技经指标统计
export const newTechnicalEconomicIndicators =
  path + 'MtDailyReport/findAllJjIndicatorStatisticsDate'

// 判废情况
// 查询 - 旧
export const qmsInvalidationStatusFind =
  path + 'QmsInvalidationStatus/findAllDate'

// 查询 - 新
export const findAllInvalidationStatusDate =
  path + 'MtDailyReport/findAllWasteStatisticsByDate'

// 保存
export const qmsInvalidationStatusSave = path + 'QmsInvalidationStatus/saveAll'

//================中厚板卷厂质量日报:周报===================
// 综合非计划率
// 查询
export const comprehensiveNonPlan =
  path + 'QmsComprehensiveUnplannedRate/findAllDate'
// 保存
export const comprehensiveNonPlanSave =
  path + 'QmsComprehensiveUnplannedRate/saveAll'

// 原钢种一次合格率
// 查询
export const originalFirstPassRate = path + 'QmsYgzFirstPassRate/findAllDate'
// 保存
export const originalFirstPassRateSave = path + 'QmsYgzFirstPassRate/saveAll'

// 单周废品分析
// 查询
export const singleWeekNonPlan = path + 'QmsWeeklyWasteAnalysis/findAllDate'
// 保存
export const singleWeekNonPlanSave = path + 'QmsWeeklyWasteAnalysis/saveAll'

// 周退判统计
// 查询
export const weekJudgeNonPlan = path + 'QmsWeeklyReturnStatistics/findAllDate'
// 保存
export const weekJudgeNonPlanSave = path + 'QmsWeeklyReturnStatistics/saveAll'

//============中厚板卷厂质量日报:典型质量缺陷===============
// 查询
export const typicalQualityDefects = path + 'QmsSteelGradeDefects/findAllDate'
// 保存
export const typicalQualityDefectsSave = path + 'QmsSteelGradeDefects/saveAll'

//============中厚板卷厂质量日报:尺寸抽查记录===============
// 查询
export const dimensionSpotCheck = path + 'QmsSizeSpotCheckRecord/findAllDate'
// 保存
export const dimensionSpotCheckSave = path + 'QmsSizeSpotCheckRecord/saveAll'

//============中厚板卷厂生产晨会看板：在制品/合同===============
//在制品
export const mtpWorkInProgress = path + 'MtpWorkInProgress/findAllDate'
export const mtpWorkInProgressSave = path + 'MtpWorkInProgress/saveAll'
//跟踪事项
export const mtpTrackingMatters = path + 'MtpTrackingMatters/findAllDate'
export const mtpTrackingMattersSave = path + 'MtpTrackingMatters/saveAll'
//合同兑现
export const mtpOrder = path + 'MtpOrder/findAllDate'
export const mtpOrderSave = path + 'MtpOrder/saveAll'

//============中厚板卷厂生产晨会看板：后道生产===============
//切割
export const mtpPostWorkInProgress1 =
  path + 'MtpPostWorkInProgress1/findAllDate'
export const mtpPostWorkInProgress1Save =
  path + 'MtpPostWorkInProgress1/saveAll'
//强力冷矫
export const mtpPostWorkInProgress2 =
  path + 'MtpPostWorkInProgress2/findAllDate'
export const mtpPostWorkInProgress2Save =
  path + 'MtpPostWorkInProgress2/saveAll'
//1#,2#矫
export const mtpRectify1and2 = path + 'MtpRectify1and2/findAllDate'
export const mtpRectify1and2Save = path + 'MtpRectify1and2/saveAll'
//离线探伤
export const mtpOfflineFlawDetection =
  path + 'MtpOfflineFlawDetection/findAllDate'
export const mtpOfflineFlawDetectionSave =
  path + 'MtpOfflineFlawDetection/saveAll'

//============中厚板卷厂生产晨会看板：工序耗能===============
// 电耗消耗统计区域曲线导出
export const eleConsumptionCurveExportField =
  ems +
  '/generator/matsenergyConsumptionStatisticsExportCurve/eleConsumptionCurveExportField'

// 煤气消耗统计区域曲线导出
export const gasConsumptionCurveExportField =
  ems +
  '/generator/matsenergyConsumptionStatisticsExportCurve/gasConsumptionCurveExportField'

// 压缩空气消耗统计区域曲线导出
export const cpaConsumptionCurveExportField =
  ems +
  '/generator/matsenergyConsumptionStatisticsExportCurve/cpaConsumptionCurveExportField'

// 水消耗统计区域曲线导出
export const watConsumptionCurveExportField =
  ems +
  '/generator/matsenergyConsumptionStatisticsExportCurve/watConsumptionCurveExportField'

//单耗、热处理：电
export const eleConsumptionFieldPage =
  ems + 'generator/Matsenergyconsumptionstatistics/eleConsumptionFieldPage'

//单耗、热处理：煤气
export const gasConsumptionFieldPage =
  ems + 'generator/Matsenergyconsumptionstatistics/gasConsumptionFieldPage'

//单耗、热处理：压缩空气
export const cpaConsumptionFieldPage =
  ems + 'generator/Matsenergyconsumptionstatistics/cpaConsumptionFieldPage'

//单耗、热处理：水
export const watConsumptionFieldPage =
  ems + 'generator/Matsenergyconsumptionstatistics/watConsumptionFieldPage'

//=================================中板厂晨会看板 - 设备及能耗==========================================
//能介消耗
// { energyType(ele：电,gwat:工业清水,swat:生活水,cpa:压缩空气,ste:蒸汽),"date": "2023-08-17"}
//查询：能源组提供
export const filterDielectricConsumption =
  ems + 'generator/indexELE/MSROLLDAYTOS'

//查询
export const findDielectricConsumption =
  path + 'ReasonsForDeviceAndEnergyConsumption/MSROLLDAYTOS'

//保存
export const saveDielectricConsumption =
  path + 'ReasonsForDeviceAndEnergyConsumption/saveMSRollDAYTOS'

//热处理能耗
// { energyType(gas:煤气,ele：电,gwat:工业清水,swat:生活水,cpa:压缩空气,nit:氮气),"date": "2023-08-17"}
//查询：能源组提供
export const filterHeatTreatment = ems + 'generator/indexELE/MSHEATDAYTOS'

//查询
export const findHeatTreatment =
  path + 'ReasonsForDeviceAndEnergyConsumption/MSHEATDAYTOS'

//保存
export const saveHeatTreatment =
  path + 'ReasonsForDeviceAndEnergyConsumption/saveMSHeatDAYTOS'

//查询指定日期的加热炉单耗
//查询：能源组提供
export const findRhfConsumptionByDate =
  ems + 'heating-furnaces/unit-consumption'

//查询
export const findRhfConsumption =
  path + 'ReasonsForDeviceAndEnergyConsumption/unit-consumption'

//保存
export const saveRhfConsumption =
  path + 'ReasonsForDeviceAndEnergyConsumption/saveUnitConsumption'

//=====================================================================================================

// 全流程收得率主题看板：重点钢种
// 查询
export const slabFindGradeData = path + 'slabFullProcess/findGradeData'
// 保存
export const slabSaveGradeData = path + 'slabFullProcess/saveGradeData'
//重点钢种曲线
export const slabFindGradeCurve = path + 'slabFullProcess/FindKeySteelGrades'

// 技术研发处看板-->性能检验-->热处理检验批次一次合格率
export const findHtFpyTestByBetweenDate =
  path + 'htFpyTestPerformance/findHtFpyTestPerformanceByBetweenDate'

// 全流程收得率主题看板-重点订单
// 炼钢分析表
export const AnalysisSlabByOrd = path + 'slabFullProcess/AnalysisSlabByOrd'
// 轧钢分析表
export const AnalysisSlabQltByOrd =
  path + 'slabFullProcess/AnalysisSlabQltByOrd'

// 全流程收得率主题看板-分析表-子厂饼图
export const SlabQltPanFeiPlt = path + 'slabFullProcess/SlabQltPanFeiPlt'

//==========================================中厚板卷厂质量周报===========================================================
//1.质量通报 - 事业部稽查(月度)
// 保存
export const departmentAuditSaveAll =
  path + '/QmsZhbjWeeklyReportInspect/saveAll'

// 查询
export const departmentAuditFindAllDate =
  path + '/QmsZhbjWeeklyReportInspect/findAllDate'

//2.质量通报 - 受理质量异议(月度)
// 保存
export const qualityObjectionSaveAll =
  path + '/QmsZhbjWeeklyReportQualityObjection/saveAll'

// 查询
export const qualityObjectionFindAllDate =
  path + '/QmsZhbjWeeklyReportQualityObjection/findAllDate'

//3.本周工作计划 - 重点跟踪事项
// 保存
export const mtpTrackingMattersSaveAll =
  path + '/QmsZhbjWeeklyReportTrackingMatters/saveAll'

// 查询
export const mtpTrackingMattersFindAllDate =
  path + '/QmsZhbjWeeklyReportTrackingMatters/findAllDate'

//4.本周工作计划 - 本周工作计划
// 保存
export const weeklyWorkPlanSaveAll =
  path + '/QmsZhbjWeeklyReportWorkPlan/saveAll'

// 查询
export const weeklyWorkPlanFindAllDate =
  path + '/QmsZhbjWeeklyReportWorkPlan/findAllDate'

//5.非计划统计 - 班组非计划情况
// 查询
export const teamNoplanInfoFindAllDate = path + '/QmsTeamNoplanInfo/findAllDate'

//6.非计划统计 - 现货非计划情况
// 查询 - 旧
export const currentNoplanInfoFindAllDate =
  path + 'QmsXafNoplanInfo/findAllDate'

//保存 - 旧
export const currentNoplanInfoSaveAll = path + 'QmsXafNoplanInfo/saveAll'

// 查询 - 新
export const currentNoplanInfoFindAllDate1 =
  path + 'QmsXafNoplanInfo/findAllDate1'

// 保存 - 新
export const currentNoplanInfoSaveAll2 = path + 'QmsXafNoplanInfo/saveAll2'

//6.备注信息
// 查询
export const findAllDateRemark =
  path + 'QmsZhbjWeeklyReportRemarks/findAllDateAndType'

// 保存
export const saveAllRemark = path + 'QmsZhbjWeeklyReportRemarks/saveAll'

//7.典型质量问题
// 查询
export const typicalQualityProblemFindAllDate =
  path + 'QmsZhbjTypicalQualityProblem/findAllDate'

// 保存
export const typicalQualityProblemSaveAll =
  path + 'QmsZhbjTypicalQualityProblem/saveAll'

//8.内部质量管理
// 车间考核
//查询
export const workshopAssessmentFindAllDate =
  path + 'QmsZhbjWeeklyWorkshopAssessment/findAllDate'

// 保存
export const workshopAssessmentSaveAll =
  path + 'QmsZhbjWeeklyWorkshopAssessment/saveAll'

//车间质量履职情况
//查询
export const workshopQualityPerformanceFindAllDate =
  path + 'QmsZhbjWeeklyWorkshopQualityPerformance/findAllDate'

// 保存
export const workshopQualityPerformanceSaveAll =
  path + 'QmsZhbjWeeklyWorkshopQualityPerformance/saveAll'

//相关方考核
//查询
export const stakeholderAssessmentFindAllDate =
  path + 'QmsZhbjWeeklyStakeholderAssessment/findAllDate'

//保存
export const stakeholderAssessmentSaveAll =
  path + 'QmsZhbjWeeklyStakeholderAssessment/saveAll'

//离线探伤检查
//查询
export const offlineFlawDetectionFindAllDate =
  path + 'QmsZhbjWeeklyOfflineFlawDetection/findAllDate'

//保存
export const offlineFlawDetectionSaveAll =
  path + 'QmsZhbjWeeklyOfflineFlawDetection/saveAll'

//专利受理情况
//查询
export const patentAdmissibilityFindAllDate =
  path + 'QmsZhbjWeeklyReportPatentAcceptance/findAllDate'

//保存
export const patentAdmissibilitySaveAll =
  path + 'QmsZhbjWeeklyReportPatentAcceptance/saveAll'
