<template>
  <div class="content">
    <div class="content-item">
      <screen-border-multi>
        <template v-slot:title>
          <div class="tabs-class">
            <div
              v-for="(item) in tabList"
              :key="item.id"
              :class="{'tab-pane-active': active === item.id}"
              class="tab-pane"
              @click="active = item.id">
              <div class="tab-pane-title-class">
                <div>{{ item.title }}</div>
                <div
                  v-if="active === item.id"
                  class="tab-pane-img">
                  <img
                    class="tab-pane-img2"
                    src="@/assets/images/screen/tab-pane-active-line2.png"
                    alt="">
                  <img
                    class="tab-pane-img1"
                    src="@/assets/images/screen/tab-pane-active-line.png"
                    alt="">
                </div>
              </div>
            </div>
          </div>
        </template>
        <custom-table-security
          v-if="active === '1'"
          :title="'安消环事件'"
          :key="'productYes1'"
          :setting="tableObj1.setting"
          :url-list="tableObj1.url.list"
          :url-save="tableObj1.url.save"
          :select-date="cDate"/>
        <custom-table-security
          v-if="active === '2'"
          :key="'productYes2'"
          :title="'质量事件'"
          :setting="tableObj1.setting"
          :url-list="tableObj2.url.list"
          :url-save="tableObj2.url.save"
          :select-date="cDate"/>
        <custom-table-security
          v-if="active === '3'"
          :key="'productYes3'"
          :title="'停时明细'"
          :setting="tableObj3.setting"
          :url-list="tableObj3.url.list"
          :url-save="tableObj3.url.save"
          :select-date="cDate"/>
        <custom-table-security3
          v-if="active === '4'"
          :key="'productYes4'"
          :title="'管理人员履职评价'"
          :setting="tableObj4.setting"
          :url-list="tableObj4.url.list"
          :url-save="tableObj4.url.save"
          :select-date="cDate"/>
      </screen-border-multi>
    </div>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import {
  qmsQualitySupervisionQuery,
  qmsQualitySupervisionSave
} from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/C2Meeting/component/custom-table'
import {
  addQualityEvent,
  addSafety,
  addStopDetails,
  cutUnplannedRateFind,
  cutUnplannedRateSave,
  findAllQuality,
  findAllSafety,
  findAllStopDetail,
  findEquipmentOperation,
  finishingShearingFind,
  finishingShearingSave,
  hotRollingSituationFind,
  hotRollingSituationSave,
  productionSituationDayFind,
  productionSituationDaySave,
  PSCDayFind,
  PSCDaySave,
  RecordEvaluationFindAllBySetDate,
  RecordEvaluationsaveAll
} from '@/api/screenC2'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import CustomTableNoheader from '@/pages/screen/C2Meeting/component/custom-table-noheader'
import { post } from '@/lib/Util'
import { expenseDetail } from '@/api/device'
import CustomTableSecurity from '@/pages/screen/C2Meeting/component/custom-table-security'
import CustomTableSecurity3 from '@/pages/screen/C2Meeting/component/custom-table-security3'
import moment from 'moment'
export default {
  name: 'securityEnv',
  components: {
    CustomTableSecurity,
    CustomTableSecurity3,
    CustomTableNoheader,
    ScreenBorderMulti,
    CustomTable,
    SingleBarsChart
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      active: '1',
      loading: false,
      tabList: [
        {
          id: '1',
          active: true,
          title: '安消环事件'
        },
        {
          id: '2',
          active: false,
          title: '质量事件'
        },
        {
          id: '3',
          active: false,
          title: '停时明细'
        },
        {
          id: '4',
          active: false,
          title: '管理人员履职评价'
        }
      ],
      tableObj1: {
        url: {
          save: addSafety,
          list: findAllSafety
        },
        setting: [
          {
            keyQuery: 'enterDate',
            keySave: 'enterDate',
            label: '日期',
            type: 'date'
          },
          {
            keyQuery: 'shift',
            keySave: 'shift',
            label: '班次'
          },
          {
            keyQuery: 'area',
            keySave: 'area',
            label: '区域'
          },
          {
            keyQuery: 'content',
            keySave: 'content',
            label: '事情描述'
          },
          {
            keyQuery: 'responsibleUnit',
            keySave: 'responsibleUnit',
            label: '责任单位'
          },
          {
            keyQuery: 'isClosed',
            keySave: 'isClosed',
            label: '是否闭环'
          }
        ]
      },
      tableObj2: {
        url: {
          save: addQualityEvent,
          list: findAllQuality
        },
        setting: []
      },
      tableObj3: {
        url: {
          save: addStopDetails,
          list: findAllStopDetail
        },
        setting: [
          {
            keyQuery: 'enterDate',
            keySave: 'enterDate',
            label: '日期',
            type: 'date'
          },
          {
            keyQuery: 'shift',
            keySave: 'shift',
            label: '班次'
          },
          {
            keyQuery: 'area',
            keySave: 'area',
            label: '区域'
          },
          {
            keyQuery: 'content',
            keySave: 'content',
            label: '事情描述'
          },
          {
            keyQuery: 'responsibleUnit',
            keySave: 'responsibleUnit',
            label: '责任单位'
          },
          {
            keyQuery: 'isClosed',
            keySave: 'isClosed',
            label: '是否闭环'
          },
          {
            keyQuery: 'faultReason',
            keySave: 'faultReason',
            label: '故障原因'
          },
          {
            keyQuery: 'handlingMeasures',
            keySave: 'handlingMeasures',
            label: '处理措施'
          }
        ]
      },
      tableObj4: {
        url: {
          save: RecordEvaluationsaveAll,
          list: RecordEvaluationFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'NAME',
            keySave: 'NAME',
            label: '名字'
          },
          {
            keyQuery: 'JANUARYEVA',
            keySave: 'JANUARYEVA',
            label: '一月'
          },
          {
            keyQuery: 'FEBRUARYEVA',
            keySave: 'FEBRUARYEVA',
            label: '二月'
          },
          {
            keyQuery: 'MARCHEVA',
            keySave: 'MARCHEVA',
            label: '三月'
          },
          {
            keyQuery: 'APRILEVA',
            keySave: 'APRILEVA',
            label: '四月'
          },
          {
            keyQuery: 'MAYEVA',
            keySave: 'MAYEVA',
            label: '五月'
          },
          {
            keyQuery: 'JUNEEVA',
            keySave: 'JUNEEVA',
            label: '六月'
          },
          {
            keyQuery: 'JULYEVA',
            keySave: 'JULYEVA',
            label: '七月'
          },
          {
            keyQuery: 'AUGUSTEVA',
            keySave: 'AUGUSTEVA',
            label: '八月'
          },
          {
            keyQuery: 'SEPTEMBEREVA',
            keySave: 'SEPTEMBEREVA',
            label: '九月'
          },
          {
            keyQuery: 'OCTOBEREVA',
            keySave: 'OCTOBEREVA',
            label: '十月'
          },
          {
            keyQuery: 'NOVEMBEREVA',
            keySave: 'NOVEMBEREVA',
            label: '十一月'
          },
          {
            keyQuery: 'DECEMBEREVA',
            keySave: 'DECEMBEREVA',
            label: '十二月'
          }
          // {
          //   keyQuery: 'enterDate',
          //   keySave: 'enterDate',
          //   label: '红灯',
          //   type: 'date'
          // },
          // {
          //   keyQuery: 'enterDate',
          //   keySave: 'enterDate',
          //   label: '黄灯',
          //   type: 'date'
          // }
        ]
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = moment(this.selectDate).format('YYYYMMDD')
      this.loadData()
    }
  },
  created() {
    this.cDate = moment(this.selectDate).format('YYYYMMDD')
    this.loadData()
  },
  methods: {
    loadData() {}
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
