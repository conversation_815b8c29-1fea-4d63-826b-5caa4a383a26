import {
  findAllByTableId,
  queryUserAndOrgByUserNo,
  queryUserRoleInfo
} from '@/lib/RiskManageApi'
import { post } from '@/lib/Util'

export async function goToPage(to, next) {
  const deviceId = to.query.deviceId
  let form = parseCode(deviceId)
  switch (deviceId.length) {
    case 2:
      next({
        path:
          '/EquipConditionMonitor/WideThickPlateFactory/WTboard/WTBoardFacNew3',
        params: { form: form }
      })
      break
    case 5:
      switch (deviceId) {
        case '34002':
          next({
            path:
              '/EquipConditionMonitor/WideThickPlateFactory/WTboard/WTboardHeatFurnace',
            params: { form: form }
          })
          break
        case '34003':
          next({
            path:
              '/EquipConditionMonitor/WideThickPlateFactory/WTboard/WTboardZQNew',
            params: { form: form }
          })
          break
        case '34005':
          next({
            path:
              '/EquipConditionMonitor/WideThickPlateFactory/FinishingArea/FAOverview',
            params: { form: form }
          })
          break
        case '34006':
          next({
            path:
              '/EquipConditionMonitor/WideThickPlateFactory/HeatTreatmentArea/HTOverview',
            params: { form: form }
          })
          break
        default:
          next({
            path:
              '/EquipConditionMonitor/WideThickPlateFactory/WTboard/AreaOverview',
            query: { code: deviceId }
          })
          break
      }
      break
    case 7:
      next({
        path:
          '/EquipConditionMonitor/WideThickPlateFactory/WTboard/DeviceOverview',
        query: { code: deviceId }
      })
      break
    case 10:
      next({
        path:
          '/EquipConditionMonitor/WideThickPlateFactory/WTboard/DevicePartOverview',
        query: { code: deviceId }
      })
      break
    case 12:
    case 14:
    default:
      next({
        path:
          '/EquipConditionMonitor/WideThickPlateFactory/MonitorRoute/DeviceNotFound',
        params: { form: form },
        query: { code: deviceId }
      })
      break
  }
}
export function parseCode(code) {
  let form = {
    business: '', //事业部
    businessName: '板材事业部',
    production: '34', //厂级
    productionName: '宽厚板厂',
    area: '', //区域
    areaName: '',
    device: '', //设备
    deviceName: '',
    devicePartId: '',
    devicePart: '',
    devicePartName: '',
    field: 'EL', //专业
    fieldName: '电气',
    partType: 'motor', //专业类型
    part: '', //部件
    partName: '',
    zero: '', //零件
    zeroName: ''
  }
  let length = code.length
  if (length >= 2) {
    //厂级
    form.production = code.substring(0, 2)
  }
  if (length >= 5) {
    //区域
    form.area = code.substring(0, 5)
  }
  if (length >= 7) {
    //设备
    form.device = code.substring(0, 7)
  }
  if (length >= 10) {
    //分部设备
    form.devicePart = code.substring(0, 10)
  }
  if (length >= 12) {
    //零部件
    form.part = code.substring(0, 12)
  }
  if (length >= 14) {
    //零部件明细
    form.device = code.substring(0, 14)
  }
  return form
}
//查询产线
export async function findFactoryList() {
  //产线
  const { data: res } = await post(findAllByTableId, {
    tableId: 'FACTORY',
    col: '3',
    data: '3'
  })
  let newFactoryList = []
  let factoryListHeader = ''
  res.forEach(item => {
    const fl = this.factoryList.find(cell => cell.name === item.twoCol)
    if (fl) {
      fl.id = item.oneCol
      fl.orgCode = item.sixCol
    }
  })
  newFactoryList = JSON.parse(JSON.stringify(this.factoryList))
  newFactoryList.push({
    id: '3',
    name: '事业部',
    orgCode: ''
  })
  let newStr3 = ''
  this.factoryList.forEach(item => {
    newStr3 += item.name + ','
  })
  factoryListHeader = ['"' + newStr3.substring(0, newStr3.length - 1) + '"']
  this.$store.commit('factory/setFactoryList', this.factoryList)
  this.$store.commit('factory/setNewFactoryList', newFactoryList)
  this.$store.commit('factory/setFactoryListHeader', factoryListHeader)
}
// 查询用户角色
export async function queryUserRoleList() {
  const res = await post(queryUserRoleInfo, {
    userID: this.userInfo.userId
  })
  if (res && res.success) {
    let roleList = []
    res.data.forEach(item => {
      roleList.push({
        id: item.id,
        roleName: item.roleName,
        roleCode: item.roleCode,
        status: item.status,
        desc: item.desc
      })
    })
    this.userInfo.role = roleList
    this.$store.commit('user/setRole', roleList)
    this.$store.commit('user/setUserInfo', this.userInfo)
  }
}
//获取用户信息和组织信息
export async function getUserAndOrgInfo() {
  const res = await post(queryUserAndOrgByUserNo, {
    userNo: localStorage.getItem('userId')
  })
  if (res.success) {
    this.userNo = res.data.user.userNo
    this.userName = res.data.user.userName
    this.userInfo.userId = res.data.user.id
    this.userInfo.userNo = res.data.user.userNo
    this.userInfo.userName = res.data.user.userName
    this.userInfo.phone = res.data.user.mobPhone
    this.userInfo.factoryNo = ''
    this.userInfo.factoryCode = ''
    this.userInfo.factoryName = ''
    this.userInfo.workDeptNo = ''
    this.userInfo.workDeptName = ''
    res.data.aboveOrgGrades.forEach(item => {
      if (item.orgAllName.charAt(item.orgAllName.length - 1) === '厂') {
        //分厂
        this.userInfo.factoryCode = item.orgCode
        this.userInfo.factoryName = item.orgAllName
      } else if (
        item.orgAllName.substring(item.orgAllName.length - 2) === '车间'
      ) {
        //车间
        this.userInfo.workDeptNo = item.orgCode
        this.userInfo.workDeptName = item.orgAllName.replace(
          this.userInfo.factoryName,
          ''
        )
      }
      // if (res.data.aboveOrgGrades.length === 4) {
      //   if (item.grade === 3) {
      //     //事业部
      //     this.userInfo.businessUnitCode = item.orgCode
      //     this.userInfo.businessUnitName = item.orgAllName
      //   } else if (item.grade === 2) {
      //     //分厂
      //     this.userInfo.factoryCode = item.orgCode
      //     this.userInfo.factoryName = item.orgAllName
      //   } else if (item.grade === 1) {
      //     //车间
      //     this.userInfo.workDeptNo = item.orgCode
      //     this.userInfo.workDeptName = item.orgAllName.replace(
      //       this.userInfo.factoryName,
      //       ''
      //     )
      //   } else if (item.grade === 0) {
      //     //班
      //     this.userInfo.fullName = item.orgAllName
      //     this.userInfo.classNo = item.orgCode
      //     this.userInfo.className = item.orgAllName.replace
      //       .replace(this.userInfo.factoryName, '')
      //       .replace(this.userInfo.workDeptName, '')
      //   }
      // } else if (res.data.aboveOrgGrades.length === 3) {
      //   if (item.grade === 2) {
      //     //事业部
      //     this.userInfo.businessUnitCode = item.orgCode
      //     this.userInfo.businessUnitName = item.orgAllName
      //   } else if (item.grade === 1) {
      //     //部门
      //     this.userInfo.factoryCode = item.orgCode
      //     this.userInfo.factoryName = item.orgAllName
      //   } else if (item.grade === 0) {
      //     //科室
      //     this.userInfo.workDeptNo = item.orgCode
      //     this.userInfo.workDeptName = item.orgAllName.replace(
      //       this.userInfo.factoryName,
      //       ''
      //     )
      //   }
      // }
    })
    this.userInfo.workDeptName = this.userInfo.workDeptName.replace(
      this.userInfo.factoryName,
      ''
    )
    const fl = this.factoryList.find(
      item => item.name === this.userInfo.factoryName
    )
    if (fl) {
      this.userInfo.factoryNo = fl.id
    }
    this.$store.commit('user/setUserName', this.userName)
    this.$store.commit('user/setUserInfo', this.userInfo)
  }
}
