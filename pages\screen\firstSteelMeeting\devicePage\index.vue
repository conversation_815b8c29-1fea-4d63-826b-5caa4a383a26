<!--设备运行-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border title="故障">
                <template v-slot:headerRight>
                  <span
                    class="screen-btn"
                    @click="clickAddTrouble">
                    <el-icon class="el-icon-circle-plus-outline"/>
                    新增
                  </span>
                </template>
                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="TroubleData.loading"
                    :data="TroubleData.showGridData"
                    :row-class-name="tableRowClassName"
                    :max-height="TroubleData.maxHeight"
                    border>
                    <el-table-column
                      align="center"
                      label="序号"
                      width="50">
                      <template v-slot="{$index}">
                        {{ $index + 1 }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      property="faultAppearance"
                      label="故障描述"/>
                    <el-table-column
                      align="center"
                      property=""
                      width="150"
                      label="操作">
                      <template slot-scope="scope">
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickTroubleItem(scope.row)">查看详情</span>
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickTroubleDeleteItem(scope.row)">删除</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border>
            </div>
            <div class="content-hold"/>
            <div class="content-item">
              <screen-border :title="'连铸火切机'">
                <template v-slot:headerRight>
                  <span
                    class="screen-btn"
                    @click="fireCutting.dialogVisible = true">
                    <el-icon class="el-icon-edit-outline"/>
                    操作
                  </span>
                </template>
                <div
                  ref="table2"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="fireCutting.loading"
                    :data="fireCutting.showGridData"
                    :max-height="fireCutting.maxHeight"
                    border>
                    <el-table-column
                      show-overflow-tooltip
                      label="设备名称">
                      <template slot-scope="scope">
                        <div>{{ scope.row.eqName }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      label="温度℃">
                      <template slot-scope="scope">
                        <!-- <div :style="{color:scope.row.temp.includes('-')?'':'#FF2855'}">{{ scope.row.crystallizeHydrating }}</div> -->
                        <!-- <div>{{ scope.row.temp }}</div> -->
                        <div :style="{color:scope.row.temp-scope.row.temperatureWar<0?'':'red'}">{{ scope.row.temp }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      label="流量L/min">
                      <template slot-scope="scope">
                        <div :style="{color:scope.row.flow-scope.row.flowWar>=0?'':'red'}">{{ scope.row.flow }}</div>
                        <!-- <div :style="{color:scope.row.flow.includes('-')?'':'#FF2855'}">{{ scope.row.flow }}</div> -->
                      </template>
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      label="温度报警值">
                      <template slot-scope="scope">
                        <div>{{ scope.row.temperatureWar }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      label="流量报警值">
                      <template slot-scope="scope">
                        <!-- <div :style="{color:scope.row.examineTwo.includes('-')?'':'#FF2855'}">{{ scope.row.examineTwo }}</div> -->
                        <div>{{ scope.row.flowWar }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      label="状态">
                      <template slot-scope="scope">
                        <!-- <div :style="{color:scope.row.examineTwo.includes('-')?'':'#FF2855'}">{{ scope.row.examineTwo }}</div> -->
                        <div v-if="scope.row.flowDiff>0 && scope.row.tempDiff<=0">正常</div>
                        <div 
                          v-else 
                          style="color:red">异常</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      label="备注">
                      <template slot-scope="scope">
                        <!-- <div :style="{color:scope.row.examineTwo.includes('-')?'':'#FF2855'}">{{ scope.row.examineTwo }}</div> -->
                        <div>{{ scope.row.remark }}</div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border>
            </div>
          </div>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border :title="'主要水系统 '+ newDate">
                <template v-slot:headerRight>
                  <span
                    class="screen-btn"
                    @click="WaterData.dialogVisible = true">
                    <el-icon class="el-icon-edit-outline"/>
                    操作
                  </span>
                </template>
                <div
                  ref="table2"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="WaterData.loading"
                    :data="WaterData.showGridData"
                    :max-height="WaterData.maxHeight"
                    border>
                    <el-table-column
                      show-overflow-tooltip
                      label="设备名称">
                      <template slot-scope="scope">
                        <div>{{ scope.row.eqName }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      label="结晶器补水(吨)">
                      <template slot-scope="scope">
                        <div :style="{color:scope.row.crystallizeHydrating < 100?'':'#FF2855'}">{{ scope.row.crystallizeHydrating }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      label="考核情况(元)">
                      <template slot-scope="scope">
                        <div :style="{color:scope.row.examineOne>=0?'':'#FF2855'}">{{ scope.row.examineOne }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      label="设备水补水(吨)">
                      <template slot-scope="scope">
                        <div :style="{color:scope.row.equipmentWaterHydrating < 200?'':'#FF2855'}">{{ scope.row.equipmentWaterHydrating }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      label="考核情况(元)">
                      <template slot-scope="scope">
                        <div :style="{color:scope.row.examineTwo>=0?'':'#FF2855'}">{{ scope.row.examineTwo }}</div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border>
            </div>
            <div class="content-hold"/>
            <div class="content-item">
              <screen-border title="行车">
                <template v-slot:headerRight>
                  <span
                    class="screen-btn"
                    @click="clickAddHang">
                    <el-icon class="el-icon-edit-outline"/>
                    新增
                  </span>
                </template>
                <div
                  ref="table3"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="HangData.loading"
                    :data="HangData.showGridData"
                    :max-height="HangData.maxHeight"
                    border>
                    <el-table-column
                      label="项次">
                      <template slot-scope="scope">
                        <div>{{ scope.row.item }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="内容">
                      <template slot-scope="scope">
                        <div>{{ scope.row.content }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="处理计划">
                      <template slot-scope="scope">
                        <div>{{ scope.row.handlePlan }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      width="100"
                      label="是否完成">
                      <template slot-scope="scope">
                        <div>{{ scope.row.isFinish==='Y'?'是':'否' }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      width="140"
                      label="完成时间">
                      <template slot-scope="scope">
                        <div>{{ scope.row.finishTime }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="备注">
                      <template slot-scope="scope">
                        <div>{{ scope.row.remarks }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property=""
                      width="150"
                      label="操作">
                      <template slot-scope="scope">
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickHangItem(scope.row)">查看详情</span>
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickHangDeleteItem(scope.row)">删除</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--水系统修改-->
    <el-dialog
      :visible.sync="WaterData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="主要水系统">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="exportWaterData">
              导出
            </span>
          </div>
          主要水系统
        </div>
      </template>
      <el-form :disabled="!canEdit">
        <el-table
          v-loading="WaterData.loading"
          :data="WaterData.gridData"
          border>
          <el-table-column
            property="itemName"
            label="设备名称">
            <template v-slot="{ row }">
              <el-input
                v-model="row.eqName"
                disabled/>
            </template>
          </el-table-column>
          <el-table-column
            property="content"
            label="结晶器补水">
            <template v-slot="{ row }">
              <el-input
                v-model="row.crystallizeHydrating"
                disabled/>
            </template>
          </el-table-column>
          <el-table-column
            property="dealPlan"
            label="考核情况">
            <template v-slot="{ row }">
              <el-input
                v-model="row.examineOne"
                disabled/>
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="设备水补水">
            <template v-slot="{ row }">
              <el-input
                v-model="row.equipmentWaterHydrating"
                disabled/>
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="考核情况">
            <template v-slot="{ row }">
              <el-input
                v-model="row.examineTwo"
                disabled/>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="fireCutting.dialogVisible"
      :width="'1600px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="连铸火切机" >
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="savefireCutting">
              保存
            </span>
          </div>
          连铸火切机&nbsp;{{ newDate }}
        </div>
      </template>
      <el-form :disabled="!canEdit">
        <el-table
          v-loading="fireCutting.loading"
          :data="fireCutting.gridData"
          border>
          <!-- <el-table-column
             property="itemName"
             label="设备名称">
             <template v-slot="{ row }">
               <el-input
                 v-model="row.eqName"
                 disabled/>
             </template>
           </el-table-column> -->
          <el-table-column
            property="content"
            label="设备名称">
            <template v-slot="{ row }">
              <el-input
                v-model="row.eqName"
                disabled/>
            </template>
          </el-table-column>
          <el-table-column
            property="dealPlan"
            label="温度℃">
            <template v-slot="{ row }">
              <el-input
                v-model="row.temp"
                disabled/>
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="流量L/min">
            <template v-slot="{ row }">
              <el-input
                v-model="row.flow"
                disabled/>
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="温度最高值">
            <template v-slot="{ row }">
              <el-input
                v-model="row.tempMax"
                disabled/>
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="温度报警值">
            <template v-slot="{ row }">
              <el-input
                v-model="row.temperatureWar"
              />
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="温度偏差">
            <template v-slot="{ row }">
              <!-- <el-input
                 v-model="row.tempDiff"
                 disabled/> -->
              <div :style="{color:row.tempDiff<0?'':'red'}">{{ row.tempDiff }}</div>
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="流量最低值">
            <template v-slot="{ row }">
              <el-input
                v-model="row.flowMin"
                disabled/>
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="流量最大值">
            <template v-slot="{ row }">
              <el-input
                v-model="row.flowMax"
                disabled/>
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="流量报警值">
            <template v-slot="{ row }">
              <el-input
                v-model="row.flowWar"
              />
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="流量偏差">
            <template v-slot="{ row }">
              <!-- <el-input
                 v-model="row.flowDiff"
                 disabled
               /> -->
              <div :style="{color:row.flowDiff>=0?'':'red'}">{{ row.flowDiff }}</div>
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="状态">
            <template v-slot="{ row }">
              <!-- <el-input
                 v-model="row.state"
                 disabled
               /> -->
              <div v-if="row.flowDiff>0 && row.tempDiff<=0">正常</div>
              <div 
                v-else 
                style="color:red">异常</div>
            </template>
          </el-table-column>
          <el-table-column
            property="memo"
            label="备注">
            <template v-slot="{ row }">
              <el-input
                v-model="row.remark"
              />
            </template>
          </el-table-column>
           
        </el-table>
      </el-form>
    </el-dialog>
    <!--行车详情新增修改删除-->
    <!--    <el-dialog
       :visible.sync="HangData.dialogVisible"
       :width="'1200px'"
       :close-on-click-modal="false"
       class="screen-dialog"
       title="行车">
       <template v-slot:title>
         <div class="custom-dialog-title">
           <div class="btn-box">
             <span
               v-if="canEdit"
               class="screen-btn"
               @click="clearGridData('HeatRate')">
               清空数据
             </span>
             <el-date-picker
               v-model="cDate"
               :clearable="false"
               :size="'mini'"
               :value-format="'yyyy-MM-dd'"
               class="screen-input"
               @change="changeDate"/>
             <el-dropdown
               v-if="canEdit"
               @command="handleProcessedCommand($event, 'importHangData')">
               <el-upload
                 ref="upload"
                 :show-file-list="false"
                 :on-change="handleHangPreview"
                 :auto-upload="false"
                 :action="''"
                 style="display: inline-block">
                 <span
                   class="screen-btn">
                   <el-icon class="el-icon-edit-outline"/>
                   EXCEL导入
                 </span>
               </el-upload>
               <el-dropdown-menu slot="dropdown">
                 <el-dropdown-item
                   command="yesterday"
                   icon="el-icon-copy">
                   从上一日导入
                 </el-dropdown-item>
                 <el-dropdown-item
                   command="other"
                   icon="el-icon-copy">
                   从其他日期导入
                 </el-dropdown-item>
               </el-dropdown-menu>
             </el-dropdown>
             <span
               class="screen-btn"
               @click="exportHangData">
               导出
             </span>
             <span
               class="screen-btn"
               @click="saveHangData">
               <el-icon class="el-icon-document-checked"/>
               保存
             </span>
           </div>
           行车
         </div>
       </template>
       <el-form>
         <el-table
           v-loading="HangData.loading"
           :data="HangData.gridData"
           :height="'calc(100vh - 345px)'"
           border>
           <el-table-column
             property="itemName"
             label="项次">
             <template v-slot="{ row }">
               <el-input v-model="row.item" />
             </template>
           </el-table-column>
           <el-table-column
             property="content"
             label="内容">
             <template v-slot="{ row }">
               <el-input v-model="row.content" />
             </template>
           </el-table-column>
           <el-table-column
             property="dealPlan"
             label="处理计划">
             <template v-slot="{ row }">
               <el-input v-model="row.handlePlan" />
             </template>
           </el-table-column>
           <el-table-column
             property="memo"
             label="备注">
             <template v-slot="{ row }">
               <el-input v-model="row.remarks" />
             </template>
           </el-table-column>
           <el-table-column
             property="progress"
             label="操作"
             width="100">
             <template v-slot="{ row, $index }">
               <span
                 class="screen-btn"
                 @click="delGridData($index, 'HangData')">
                 <el-icon class="el-icon-delete"/>
                 删除
               </span>
             </template>
           </el-table-column>
         </el-table>
       </el-form>
       <div class="text-center">
         <span
           class="screen-btn"
           @click="addGridData('HangData')">
           <el-icon class="el-icon-circle-plus-outline"/>
           增加数据
         </span>
       </div>
     </el-dialog>-->
    <!--行车新增和修改-->
    <el-dialog
      :visible.sync="HangData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="行车详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          行车详情
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">项次</div>
          <el-input
            v-model="hangItem.item"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">内容</div>
          <el-input
            v-model="hangItem.content"
            :rows="3"
            clearable
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">处理计划</div>
          <el-input
            v-model="hangItem.handlePlan"
            :rows="3"
            clearable
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">是否完成</div>
          <el-select
            v-model="hangItem.isFinish"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in isFinishList"
              :key="item.id"
              :label="item.name"
              :value="item.id"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">备注 </div>
          <el-input
            v-model="hangItem.remarks"
            :rows="8"
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="addHangData()">
          确定
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="上月导入日期库存">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
 
    <!--故障详情-->
    <el-dialog
      :visible.sync="TroubleData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="故障详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          故障详情
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">故障时间</div>
          <!--          <el-input
             v-model="TroubleData.faultTime"
             :rows="3"
             type="textarea"
             placeholder="请输入内容"
             class="dialog-cell-input"/>-->
          <el-date-picker
            v-model="troubleItem.faultTime"
            :clearable="false"
            :size="'mini'"
            :value-format="'yyyy-MM-dd'"
            class="screen-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">故障描述</div>
          <el-input
            v-model="troubleItem.faultAppearance"
            :rows="3"
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">原因分析</div>
          <el-input
            v-model="troubleItem.reason"
            :rows="8"
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改措施</div>
          <el-table
            :data="troubleItem.faultDetailList"
            border>
            <el-table-column
              align="center"
              label="序号"
              width="50">
              <template v-slot="{ row, $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property="measure"
              label="整改措施">
              <template v-slot="{ row }">
                <el-input v-model="row.measure" />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property="responsiblePerson"
              width="200"
              label="责任人">
              <template v-slot="{ row }">
                <el-input v-model="row.responsiblePerson" />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property="responsiblePerson"
              width="200"
              label="完成时间">
              <template v-slot="{ row }">
                <el-date-picker
                  v-model="row.finishTime"
                  :clearable="false"
                  :size="'mini'"
                  :value-format="'yyyy-MM-dd'"
                  style="width: 160px"
                  class="screen-input"/>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property=""
              width="120"
              label="操作">
              <template slot-scope="scope">
                <span
                  class="screen-btn"
                  @click="delTroubleDetailData(scope.row, $index)">
                  <el-icon class="el-icon-delete"/>
                  删除
                </span>
              </template>
            </el-table-column>
          </el-table>
          <div class="text-center">
            <span
              class="screen-btn"
              @click="addTroubleDetailData()">
              <el-icon class="el-icon-circle-plus-outline"/>
              增加数据
            </span>
          </div>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">考核落实</div>
          <el-input
            v-model="troubleItem.examine"
            :rows="4"
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
 
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="addTroubleData()">
          确定
        </span>
      </div>
    </el-dialog>
  </div>
</template>
 
 <script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  firstMeetingDevice1,
  firstMeetingDevice2,
  firstMeetingDevice3,
  firstMeetingDevice4,
  firstMeetingDevice5,
  firstMeetingDevice6,
  firstMeetingDevice7,
  firstMeetingDevice8,
  firstMeetingDevice9,
  firstMeetingDevice10,
  firstMeetingDevice11,
  firstMeetingDevice12
} from '@/api/firstMeeting'
import moment from 'moment/moment'
export default {
  name: 'DevicePage',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      troubleItem: {
        faultAppearance: '',
        faultTime: '',
        reason: '',
        examine: '',
        faultDetailList: [
          {
            measure: '',
            responsiblePerson: '',
            finishTime: ''
          }
        ]
      },
      cDate: '',
      flowMin1: '',
      flowMin2: '',
      flowMin3: '',
      flowMin4: '',
      TroubleData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      WaterData: {
        loading: false,
        gridData: [],
        showGridData: [
          {
            id: 'LZ0',
            eqName: '0#连铸机',
            crystallizeHydrating: '',
            examineOne: '',
            equipmentWaterHydrating: '',
            examineTwo: ''
          },
          {
            id: 'LZ1',
            eqName: '1#连铸机',
            crystallizeHydrating: '',
            examineOne: '',
            equipmentWaterHydrating: '',
            examineTwo: ''
          },
          {
            id: 'LZ2',
            eqName: '2#连铸机',
            crystallizeHydrating: '',
            examineOne: '',
            equipmentWaterHydrating: '',
            examineTwo: ''
          },
          {
            id: 'LZ3',
            eqName: '3#连铸机',
            crystallizeHydrating: '',
            examineOne: '',
            equipmentWaterHydrating: '',
            examineTwo: ''
          }
        ],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      fireCurrentData: [],
      fireCurrentDataMax: [],
      fireCurrentDataMin: [],
      fireCutting: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      HangData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      newDate: '',
      //是否完成
      isFinishList: [
        {
          id: 'N',
          name: '否'
        },
        {
          id: 'Y',
          name: '是'
        }
      ],
      hangItem: {}
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.$nextTick(_ => {
        // this.newDate = moment(
        //   new Date(new Date(this.cDate).getTime() - 24 * 60 * 60 * 1000)
        // ).format('yyyy-MM-DD')
        this.newDate = this.cDate
        this.init()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
    this.getFireCutting()
    //  this.getFireCut()
  },
  methods: {
    //点击新增行车
    clickAddHang() {
      this.hangItem = {
        item: '',
        content: '',
        handlePlan: '',
        isFinish: 'N',
        remarks: '',
        setDate: this.cDate
      }
      this.HangData.dialogVisible = true
    },
    //行车点击查看详情
    clickHangItem(row) {
      this.hangItem = JSON.parse(JSON.stringify(row))
      this.HangData.dialogVisible = true
    },
    //行车点击查看详情
    clickHangDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteHang(row.id)
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    //新增/修改行车数据
    addHangData() {
      // const params = [this.hiddenItem]
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: [this.hangItem]
      }
      post(firstMeetingDevice6, params).then(res => {
        if (res.success) {
          this.HangData.dialogVisible = false
          this.getHangData()
        }
      })
    },
    //删除行车数据
    deleteHang(id) {
      const params = [
        {
          id: id
        }
      ]
      post(firstMeetingDevice7, params).then(res => {
        if (res.success) {
          this.getHangData()
        }
      })
    },
    init() {
      this.getTroubleData()
      this.getWaterData()
      this.getHangData()
      this.getFireCutting()
    },
    //故障点击新增
    clickAddTrouble() {
      this.troubleItem = {
        faultAppearance: '',
        faultTime: '',
        reason: '',
        examine: '',
        faultDetailList: [
          {
            measure: '',
            responsiblePerson: '',
            finishTime: ''
          }
        ]
      }
      this.TroubleData.dialogVisible = true
    },
    //故障点击查看详情
    clickTroubleItem(row) {
      this.troubleItem = JSON.parse(JSON.stringify(row))
      this.TroubleData.dialogVisible = true
    },
    //故障点击查看详情
    clickTroubleDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteTrouble(row.id)
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    //增加故障明细
    addTroubleDetailData() {
      this.troubleItem.faultDetailList.push({})
    },
    //删除故障明细
    delTroubleDetailData(row, index) {
      if (row.id) {
        this.deleteTroubleDetail(row.id)
      }
      this.troubleItem.faultDetailList.splice(index, 1)
    },
    //故障查询
    getTroubleData() {
      this.getFireCutting()
      const params = {
        time: this.cDate
        // time: ''
      }
      this.TroubleData.loading = true
      post(firstMeetingDevice1, params)
        .then(res => {
          this.TroubleData.showGridData = res.data.map(item => {
            return {
              id: item.id,
              faultTime: item.faultTime,
              faultAppearance: item.faultAppearance,
              reason: item.reason,
              examine: item.examine,
              faultDetailList: item.faultDetailList
            }
          })
          this.TroubleData.gridData = lodash.cloneDeep(
            this.TroubleData.showGridData
          )
        })
        .finally(_ => {
          this.TroubleData.loading = false
        })
    },
    //故障数据保存
    saveTroubleData() {
      this.TroubleData.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.TroubleData.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(firstMeetingDevice6, params).then(res => {
        //
        this.TroubleData.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.TroubleData.dialogVisible = false
          this.getTroubleData()
        }
      })
    },
    //新增修改故障
    addTroubleData() {
      const params = [this.troubleItem]
      post(firstMeetingDevice2, params).then(res => {
        if (res.success) {
          this.TroubleData.dialogVisible = false
          this.getTroubleData()
        }
      })
    },
    //删除故障
    deleteTrouble(id) {
      const params = [
        {
          id: id
        }
      ]
      post(firstMeetingDevice3, params).then(res => {
        if (res.success) {
          this.getTroubleData()
        }
      })
    },
    //删除故障明细
    deleteTroubleDetail(id) {
      const params = [
        {
          id: id
        }
      ]
      post(firstMeetingDevice8, params).then(res => {
        if (res.success) {
          this.$message.success('删除成功！')
        }
      })
    },

    //连铸火切机
    getFireCut() {
      // this.getFireCutMax()
      post('it/customApi/iot/currentValue', [
        {
          pointCode: '5307230792050218'
        },
        {
          pointCode: '5307231366539963'
        },
        {
          pointCode: '5307230853759544'
        },
        {
          pointCode: '5307230657048787'
        },
        {
          pointCode: '5307232110429037'
        },
        {
          pointCode: '5307230518764480'
        },
        {
          pointCode: '5307230646785785'
        },
        {
          pointCode: '5307152108477641'
        },
        {
          pointCode: '5308130196565053'
        },
        {
          pointCode: '5308021306055892'
        }
      ]).then(res => {
        if (res) {
          this.fireCurrentData = res.data
        }
      })
    },
    getFireCutFlowMinTime() {
      let start = Date.parse(this.cDate) - 8 * 60 * 60 * 1000
      let end = Date.parse(this.cDate) + 16 * 60 * 60 * 1000
      post(firstMeetingDevice12, {
        pointCode: '2803200869860808',
        startTs: start,
        endTs: end,
        cons: 0.75
      }).then(res => {
        if (res) {
          this.flowMin1 = res.data
        }
      })
      post(firstMeetingDevice12, {
        pointCode: '01030202_0009_0002',
        startTs: start,
        endTs: end,
        cons: 0.85
      }).then(res => {
        if (res) {
          this.flowMin2 = res.data
        }
      })
      post(firstMeetingDevice12, {
        pointCode: '01030209_0008_0002',
        startTs: start,
        endTs: end,
        cons: 0.7
      }).then(res => {
        if (res) {
          this.flowMin3 = res.data
        }
      })
      post(firstMeetingDevice12, {
        pointCode: '01030204_0008_0002',
        startTs: start,
        endTs: end,
        cons: 0.7
      }).then(res => {
        if (res) {
          this.flowMin4 = res.data
        }
      })
    },
    getFireCutMax() {
      let start = Date.parse(this.cDate) - 8 * 60 * 60 * 1000
      let end = Date.parse(this.cDate) + 16 * 60 * 60 * 1000
      post('it/customApi/iot/maxValue', [
        {
          pointCode: '5307230792050218',
          fromTs: start,
          toTs: end
        },
        {
          pointCode: '5307231366539963',
          fromTs: start,
          toTs: end
        },
        {
          pointCode: '5307230853759544',
          fromTs: start,
          toTs: end
        },
        {
          pointCode: '5307230657048787',
          fromTs: start,
          toTs: end
        },
        {
          pointCode: '5307232110429037',
          fromTs: start,
          toTs: end
        },
        {
          pointCode: '5307230518764480',
          fromTs: start,
          toTs: end
        },
        {
          pointCode: '5307230646785785',
          fromTs: start,
          toTs: end
        },
        {
          pointCode: '5307152108477641',
          fromTs: start,
          toTs: end
        },
        {
          pointCode: '5308130196565053',
          fromTs: start,
          toTs: end
        },
        {
          pointCode: '5308021306055892',
          fromTs: start,
          toTs: end
        }
      ]).then(res => {
        if (res.data[0].list[0].value) {
          this.fireCurrentDataMax = res.data
          //  this.fireCutting.showGridData[0].tempMax = res.data[0].list[0].value.toFixed(
          //    2
          //  )
          //  console.log('aabbbbccc', this.fireCurrentDataMax)
          //  console.log('this', this)
        }
      })
    },
    //连铸机查询
    getFireCutting() {
      this.fireCutting.showGridData = []
      this.fireCutting.gridData = []
      this.getFireCut()
      this.getFireCutMax()
      this.getFireCutFlowMinTime()
      this.fireCutting.loading = true
      post(firstMeetingDevice10)
        .then(res => {
          this.getFireCut()
          this.getFireCutMax()
          this.fireCutting.showGridData = []
          this.fireCutting.gridData = []
          if (
            this.fireCurrentDataMax[0].list[0].value ||
            this.fireCurrentDataMax[1].list[0].value
            // this.fireCurrentDataMax[6].list[0].value ||
            // this.fireCurrentDataMax[7].list[0].value
          ) {
            this.fireCutting.showGridData = [
              {
                id: res.data[0].id,
                eqName: '0#连铸机一切',
                temp: this.fireCurrentData[0].list[0].currentvalue.toFixed(2),
                flow: this.fireCurrentData[1].list[0].currentvalue.toFixed(2),
                temperatureWar: res.data[0].temperatureWar,
                flowWar: res.data[0].flowWar,
                remark: res.data[0].remark,
                tempMax: this.fireCurrentDataMax[0].list[0].value.toFixed(2),
                flowMin: this.flowMin1,
                flowMax: this.fireCurrentDataMax[1].list[0].value.toFixed(2),
                tempDiff: (
                  this.fireCurrentDataMax[0].list[0].value -
                  res.data[0].temperatureWar
                ).toFixed(2),
                flowDiff: (this.flowMin1 - res.data[0].flowWar).toFixed(2),
                state: '正常'
              },
              // {
              //   id: res.data[1].id,
              //   eqName: '0#连铸机二切',
              //   temp: this.fireCurrentData[2].list[0].currentvalue.toFixed(2),
              //   flow: this.fireCurrentData[3].list[0].currentvalue.toFixed(2),
              //   temperatureWar: res.data[1].temperatureWar,
              //   flowWar: res.data[1].flowWar,
              //   remark: res.data[1].remark,
              //   tempMax: this.fireCurrentDataMax[2].list[0].value.toFixed(2),
              //   flowMin: this.fireCurrentDataMax[3].list[0].value.toFixed(2),
              //   tempDiff: (
              //     this.fireCurrentDataMax[2].list[0].value -
              //     res.data[1].temperatureWar
              //   ).toFixed(2),
              //   flowDiff: (
              //     this.fireCurrentDataMax[3].list[0].value - res.data[1].flowWar
              //   ).toFixed(2),
              //   state: '正常'
              // },
              {
                id: res.data[1].id,
                eqName: '1#连铸机一切',
                temp: this.fireCurrentData[4].list[0].currentvalue.toFixed(2),
                flow: this.fireCurrentData[5].list[0].currentvalue.toFixed(2),
                temperatureWar: res.data[1].temperatureWar,
                flowWar: res.data[1].flowWar,
                remark: res.data[1].remark,
                tempMax: this.fireCurrentDataMax[4].list[0].value.toFixed(2),
                flowMin: this.flowMin2,
                flowMax: this.fireCurrentDataMax[5].list[0].value.toFixed(2),
                tempDiff: (
                  this.fireCurrentDataMax[4].list[0].value -
                  res.data[1].temperatureWar
                ).toFixed(2),
                flowDiff: (this.flowMin2 - res.data[1].flowWar).toFixed(2),
                state: '正常'
              },
              {
                id: res.data[3].id,
                eqName: '2#连铸机一切',
                temp: this.fireCurrentData[8].list[0].currentvalue.toFixed(2),
                flow: this.fireCurrentData[9].list[0].currentvalue.toFixed(2),
                temperatureWar: res.data[1].temperatureWar,
                flowWar: res.data[3].flowWar,
                remark: res.data[3].remark,
                tempMax: this.fireCurrentDataMax[8].list[0].value.toFixed(2),
                flowMin: this.flowMin4,
                flowMax: this.fireCurrentDataMax[9].list[0].value.toFixed(2),
                tempDiff: (
                  this.fireCurrentDataMax[8].list[0].value -
                  res.data[3].temperatureWar
                ).toFixed(2),
                flowDiff: (this.flowMin4 - res.data[3].flowWar).toFixed(2),
                state: '正常'
              },
              {
                id: res.data[2].id,
                eqName: '3#连铸机一切',
                temp: this.fireCurrentData[6].list[0].currentvalue.toFixed(2),
                flow: this.fireCurrentData[7].list[0].currentvalue.toFixed(2),
                temperatureWar: res.data[2].temperatureWar,
                flowWar: res.data[2].flowWar,
                remark: res.data[2].remark,
                tempMax: this.fireCurrentDataMax[6].list[0]
                  ? this.fireCurrentDataMax[6].list[0].value.toFixed(2)
                  : '无数据',
                flowMin: this.flowMin3,
                flowMax: this.fireCurrentDataMax[7].list[0]
                  ? this.fireCurrentDataMax[7].list[0].value.toFixed(2)
                  : '无数据',
                tempDiff: this.fireCurrentDataMax[6].list[0]
                  ? (
                      this.fireCurrentDataMax[6].list[0].value -
                      res.data[2].temperatureWar
                    ).toFixed(2)
                  : '无数据',
                flowDiff: (this.flowMin3 - res.data[2].flowWar).toFixed(2),
                state: '正常'
              }
            ]
          }

          this.fireCutting.gridData = lodash.cloneDeep(
            this.fireCutting.showGridData
          )
        })
        .finally(_ => {
          this.fireCutting.loading = false
        })
    },
    savefireCutting() {
      this.fireCutting.loading = true
      // 保存钢铁产量信息
      const params = this.fireCutting.gridData.map(item => {
        item.setDate = this.cDate
        //  item.updateTime = moment().format('YYYY-MM-DD HH:mm:ss')
        return item
      })

      post(firstMeetingDevice11, params).then(res => {
        //
        this.fireCutting.loading = false
        if (res.status) {
          this.$message.success('保存成功！')
          this.fireCutting.dialogVisible = false
          this.getFireCutting()
        }
      })
    },
    //主要水系统查询
    getWaterData() {
      this.getFireCutting()
      const params = {
        month: this.newDate.substring(0, 7)
      }
      this.WaterData.loading = true
      post(firstMeetingDevice4, params)
        .then(res => {
          if (res.code === 0) {
            let list = res.result
            if (list && list.length > 0) {
              const data = list.find(
                item =>
                  (item.day < 10 ? '0' + item.day : item.day + '') ===
                  this.newDate.substring(8)
              )
              console.log('水系统：', data)
              if (data) {
                this.WaterData.showGridData.forEach(item => {
                  switch (item.id) {
                    case 'LZ0':
                      item.crystallizeHydrating = data.LZ0_CRYS_ACT
                      item.examineOne = data.LZ0_CRYS_RP
                      item.equipmentWaterHydrating = data.LZ0_DEVICE_ACT
                      item.examineTwo = data.LZ0_DEVICE_RP
                      break
                    case 'LZ1':
                      item.crystallizeHydrating = data.LZ1_CRYS_ACT
                      item.examineOne = data.LZ1_CRYS_RP
                      item.equipmentWaterHydrating = data.LZ1_DEVICE_ACT
                      item.examineTwo = data.LZ1_DEVICE_RP
                      break
                    case 'LZ2':
                      item.crystallizeHydrating = data.LZ2_CRYS_ACT
                      item.examineOne = data.LZ2_CRYS_RP
                      item.equipmentWaterHydrating = data.LZ2_DEVICE_ACT
                      item.examineTwo = data.LZ2_DEVICE_RP
                      break
                    case 'LZ3':
                      item.crystallizeHydrating = data.LZ3_CRYS_ACT
                      item.examineOne = data.LZ3_CRYS_RP
                      item.equipmentWaterHydrating = data.LZ3_DEVICE_ACT
                      item.examineTwo = data.LZ3_DEVICE_RP
                      break
                  }
                })
              }
              this.WaterData.gridData = lodash.cloneDeep(
                this.WaterData.showGridData
              )
            }
          }
        })
        .finally(_ => {
          this.WaterData.loading = false
        })
    },
    //水系统数据保存
    saveWaterData() {
      this.WaterData.loading = true
      // 保存钢铁产量信息
      const params = this.WaterData.gridData.map(item => {
        item.setDate = this.cDate
        return item
      })
      post(firstMeetingDevice9, params).then(res => {
        //
        this.WaterData.loading = false
        if (res.success) {
          this.$message.success('保存成功！')
          this.WaterData.dialogVisible = false
          this.getWaterData()
        }
      })
    },
    //导出数据
    exportWaterData() {
      const data = [
        {
          eqName: '设备名称',
          crystallizeHydrating: '结晶器补水',
          examineOne: '考核情况',
          equipmentWaterHydrating: '设备水补水',
          examineTwo: '考核情况'
        }
      ].concat(
        _.cloneDeep(this.WaterData.gridData).map(item => {
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `水系统（${this.cDate}晨会）.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },
    //导出数据
    exportfireCutting() {
      const data = [
        {
          eqName: '设备名称',
          crystallizeHydrating: '结晶器补水',
          examineOne: '考核情况',
          equipmentWaterHydrating: '设备水补水',
          examineTwo: '考核情况'
        }
      ].concat(
        _.cloneDeep(this.WaterData.gridData).map(item => {
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `水系统（${this.cDate}晨会）.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //行车查询
    getHangData() {
      const params = {
        time: this.cDate
      }
      this.HangData.loading = true
      post(firstMeetingDevice5, params)
        .then(res => {
          this.HangData.showGridData = res.data.map(item => {
            return {
              id: item.id,
              item: item.item,
              content: item.content,
              handlePlan: item.handlePlan,
              isFinish: item.isFinish,
              isFinishName: item.isFinish === 'Y' ? '是' : '否',
              finishTime: item.finishTime,
              remarks: item.remarks,
              setDate: item.setDate
            }
          })
          this.HangData.gridData = lodash.cloneDeep(this.HangData.showGridData)
        })
        .finally(_ => {
          this.HangData.loading = false
        })
    },
    //行车数据保存
    saveHangData() {
      this.HangData.loading = true
      // 保存钢铁产量信息
      const params = {
        date: this.cDate,
        data: this.HangData.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(firstMeetingDevice6, params).then(res => {
        //
        this.HangData.loading = false
        if (res.success) {
          this.$message.success('保存成功！')
          this.HangData.dialogVisible = false
          this.getHangData()
        }
      })
    },
    //导入昨天数据或者选择的某一天导入数据
    importHangData(date) {
      post(firstMeetingDevice5, {
        time: date
      }).then(res => {
        //
        this.HangData.loading = false
        this.HangData.gridData = res.data.map(item => {
          return {
            item: item.item,
            content: item.content,
            handlePlan: item.handlePlan,
            isFinish: item.isFinish,
            finishTime: item.finishTime,
            remarks: item.remarks
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    //导出数据
    exportHangData() {
      const data = [
        {
          item: '项次',
          content: '内容',
          handlePlan: '处理计划',
          isFinish: '是否完成',
          finishTime: '完成时间',
          remarks: '备注'
        }
      ].concat(
        _.cloneDeep(this.HangData.gridData).map(item => {
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `行车（${this.cDate}晨会）.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },
    //文件夹导入
    handleHangPreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          item: 'A',
          content: 'B',
          handlePlan: 'C',
          isFinish: 'D',
          finishTime: 'E',
          remarks: 'F'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.HangData.gridData = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    //计算高度
    calculateHeight() {
      this.TroubleData.maxHeight = this.$refs.table1.offsetHeight
      this.WaterData.maxHeight = this.$refs.table2.offsetHeight
      this.HangData.maxHeight = this.$refs.table3.offsetHeight
    },
    //表格行样式名称
    tableRowClassName() {
      return ''
    }
  }
}
</script>
 
 <style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
