<template>
  <div 
    :id="containerId" 
    :style="{ height: height + 'px' }"/>
</template>

<script>
export default {
  name: 'liquidfill-chart',
  props: {
    height: {
      type: Number,
      default: 160
    },
    chartData: {
      type: Number,
      default: 0
    },
    showText: {
      type: Number,
      default: 0
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return []
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showSymbol: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  methods: {
    initChart() {
      const _this = this
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId),
          'custom'
        )
        this.myChart.getZr().on('click', params => {
          this.$emit('selected', params)
        })
      }
      const option = {
        series: [
          {
            type: 'liquidFill',
            radius: '90%',
            amplitude: 8, //圆形内 水滴角度
            color: ['#4097f2', '#4aa3ff'],
            data: [
              this.chartData,
              0.01 + this.chartData,
              0.02 + this.chartData
            ],
            label: {
              normal: {
                position: ['50%', '70%'],
                formatter: function(val) {
                  return '综合得分\n' + _this.showText
                },
                textStyle: {
                  color: 'rgba(14,156,255)',
                  lineHeight: 24,
                  fontSize: 18 //字体大小
                }
              }
            },
            backgroundStyle: {
              color: 'transparent'
            },
            itemStyle: {
              // shadowColor: 'transparent'
            },
            outline: {
              // 轮廓设置
              show: true,
              itemStyle: {
                borderWidth: 4, //轮廓大小
                borderColor: '#f5f5fa',
                shadowColor: '#c5c5c7',
                color: '#f5f5fa'
              },
              borderDistance: 0 // 轮廓间距
            }
          }
        ]
      }
      this.myChart.setOption(option)
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c === 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style scoped>
</style>
