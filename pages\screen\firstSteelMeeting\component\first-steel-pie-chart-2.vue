<template>
  <el-row :gutter="20">
    <el-col :span="3">
      <div class="e-chart">
        <div
          :id="containerId"
          style="height: 184px"
          class="e-chart-div"/>
      </div>
    </el-col>
    <el-col :span="13">
      <div class="fuel-div">
        <div class="fuel-top-div">
          <div
            v-for="item in fData"
            :key="item.name"
            class="fuel-item-div">
            <img
              :src="item.img"
              class="fuel-item-img"
              alt="">
            <div class="fuel-item-text-div">
              <div class="fuel-item-text">
                <span>{{ item.name }}</span>
              </div>
              <div class="fuel-item-text">
                <span
                  :style="{color:item.color}"
                  class="fuel-item-text-value">{{ item.value }}</span>
                <span class="fuel-item-text-unit">{{ item.unit }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="fuel-bottom-div">
          <div
            v-for="item in fLegendData"
            :key="item.name"
            class="fuel-item-2-div">
            <div class="fuel-item-2-text-div">
              <span
                :style="{backgroundColor:item.color}"
                class="fuel-item-2-view"/>
              <span class="fuel-item-2-title">{{ item.name }}</span>
            </div>
            <div class="fuel-item-2-text-div">
              <span class="fuel-item-2-value">{{ item.value }}</span>
              <span class="fuel-item-2-unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="8">
      <div class="remark-div">
        <div class="remark-title">备注</div>
        <el-input
          v-model="remark"
          :disabled="!isEdit"
          :rows="6"
          type="textarea"
          class="remark-input"
          placeholder="请输入描述"/>
      </div>
    </el-col>
  </el-row>

</template>

<script>
export default {
  name: 'first-steel-pie',
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    titleNum: {
      type: String,
      default: ''
    },
    vertical: {
      type: Boolean,
      default: true
    },
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    fData: {
      type: Array,
      default: () => {
        return []
      }
    },
    fLegendData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 0
    },
    unit: {
      type: String,
      default: '吨'
    },
    unit1: {
      type: String,
      default: '%'
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null,
      id: '',
      remark: ''
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
    window.addEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
      }
      const options = {
        color: this.color,
        tooltip: {
          show: this.showToolbox,
          trigger: 'item',
          position: 'right',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          padding: 10
        },
        legend: {
          show: this.showLegend,
          align: 'left',
          top: 5,
          right: 2,
          padding: 0,
          // icon: 'circle',
          textStyle: {
            color: '#C9E1FDF2',
            fontSize: 12
          },
          // itemHeight: 10, // 修改icon图形大小
          // itemWidth: 10, // 修改icon图形大小
          // itemGap: 10, // 修改间距
          itemStyle: {
            borderWidth: 0,
            padding: 0
          }
        },
        grid: {
          top: '16%',
          left: '0%',
          right: '1%',
          bottom: '1%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: this.xData,
            nameTextStyle: {
              color: '#d2d2d2'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              interval: 0,
              rotate: this.labelRotate || 0
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            minInterval: 1,
            axisLine: {
              show: false
            },
            nameTextStyle: {
              color: '#d2d2d2'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'right'
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        series: this.chartData.map(item => ({
          name: item.name,
          type: item.type,
          radius: item.radius,
          smooth: item.smooth,
          barWidth: this.barWidth || null,
          yAxisIndex: item.yAxisIndex || 0,
          data: item.data,
          labelLine: {
            length: 30
          },
          label: {
            show: false,
            formatter: '{b|{b}}\n  {c|{c}}  {per|万元}  ',
            // backgroundColor: '#F6F8FC',
            // borderColor: '#8C8D8E',
            // borderWidth: 1,
            borderRadius: 4,
            rich: {
              a: {
                color: '#6E7079',
                lineHeight: 22,
                align: 'center'
              },
              b: {
                color: '#ffffff',
                fontSize: 16,
                fontWeight: 'bold',
                lineHeight: 33
              },
              c: {
                color: '#FF2855',
                fontSize: '20px',
                fontWeight: 'bolder'
              },
              per: {
                color: '#ffffff',
                fontSize: '14px',
                padding: [3, 4],
                borderRadius: 4
              }
            }
          }
        }))
      }
      this.myChart.setOption(options)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.fuel-div {
  display: flex;
  flex-direction: column;
  .fuel-top-div {
    display: flex;
    justify-content: space-between;
    margin-bottom: 32px;
    .fuel-item-div {
      display: flex;
      .fuel-item-img {
        width: 80px;
        margin-right: 16px;
      }
      .fuel-item-text-div {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 4px 0;
        .fuel-item-text {
          color: #ffffff;
          font-size: 20px;
          line-height: 20px;
          font-weight: bold;
          .fuel-item-text-value {
            padding-top: 16px;
            font-weight: bolder;
            font-size: 36px;
            line-height: 36px;
          }
          .fuel-item-text-value-red {
            color: #ff2855;
          }
          .fuel-item-text-unit {
            font-weight: normal;
          }
        }
      }
    }
  }
  .fuel-bottom-div {
    display: flex;
    justify-content: space-between;
    .fuel-item-2-div {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .fuel-item-2-text-div {
        display: flex;
        margin-bottom: 8px;
        align-items: baseline;
        .fuel-item-2-view {
          width: 32px;
          height: 16px;
          background: #3391ff;
          margin-right: 8px;
        }
        .fuel-item-2-title {
          font-size: 20px;
          font-weight: bold;
          line-height: 20px;
          color: #ffffff;
        }
        .fuel-item-2-value {
          font-size: 32px;
          line-height: 32px;
          font-weight: bold;
          color: #ff2855;
        }
        .fuel-item-2-unit {
          font-size: 20px;
          line-height: 20px;
          color: #ffffff;
        }
      }
    }
  }
}
.remark-div {
  width: 100%;
  padding: 0 20px;
  .remark-title {
    font-size: 14px;
    line-height: 20px;
    font-weight: bolder;
    color: #ffffff;
    margin-bottom: 11px;
  }
  .remark-title::before {
    content: '1';
    width: 6px;
    height: 100%;
    color: #ffffff;
    background: #ffffff;
    margin-right: 4px;
  }
  .remark-input {
    font-size: 19px;
  }
}
.remark-div {
  /deep/ .el-textarea__inner,
  /deep/ .el-input__inner {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
    padding-right: 5px;
  }
  /deep/ .el-input__prefix {
    color: #fff;
  }
}
.e-chart {
  display: flex;
  width: 100%;
  height: 100%;
}
.e-chart-div {
  flex: 1;
  width: 100%;
  height: 100%;
}
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
