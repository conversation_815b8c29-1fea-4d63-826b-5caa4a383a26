<template>
  <div class="container">
    <div class="header-footer">
      <el-input
        v-model="monthlyReport"
        :rows="3"
        type="textarea"
        class="chart-input"
        resize="none"
      />
      <el-button
        :loading="submitLoading"
        type="primary"
        class="submit-btn"
        icon="el-icon-finished"
        @click="handleSave">提交</el-button>
    </div>
    <div class="table-section">
      <div class="table-left">
        <screen-border title="现货非计划情况">
          <template v-slot:headerRight>
            <el-date-picker
              v-model="cDate"
              :size="'mini'"
              clearable
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="date-picker"
              popper-class="date-picker-dropdown"
              @change="changeDate"/>
            <span
              class="screen-btn"
              @click="$refs.spotNoplanRef.openDialog()">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <custom-table-range
            ref="spotNoplanRef"
            :show-table="true"
            :show-edit="false"
            :key="'spotNoplan'"
            :title="'现货非计划情况'"
            :setting="tableObj.setting"
            :url-list="tableObj.url.list"
            :url-save="tableObj.url.save"
            :select-date="selectDate"
            :date-params="dateParams"
          />
        </screen-border>
      </div>
      <div class="table-right">
        <screen-border
          title="轧制非计划情况（敬请期待）"
          class="dairy-section">
          <template v-slot:headerRight>
            <span
              class="screen-btn"
            >
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <div class="dairy-tables">
            <div class="dairy-table-content">
              <div class="dairy-table">
                <div class="dairy-title">平轧非计划</div>
                <el-table
                  :data="dairyPlanData"
                  size="small">
                  <el-table-column
                    prop="item"
                    label="非计划细分项"
                    min-width="80"
                    align="center" />
                  <el-table-column
                    prop="planned"
                    label="计划"
                    width="80"
                    align="center" />
                  <el-table-column
                    prop="actual"
                    label="实际"
                    width="80"
                    align="center">
                    <template slot-scope="scope">
                      <span :class="{'positive-value': parseFloat(scope.row.actual) > 0}">{{ scope.row.actual }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="dairy-table">
                <div class="dairy-title">卷轧非计划</div>

                <el-table
                  :data="dairyPlanData2"
                  size="small">
                  <el-table-column
                    prop="item"
                    label="非计划细分项"
                    min-width="80"
                    align="center" />
                  <el-table-column
                    prop="planned"
                    label="计划"
                    width="80"
                    align="center" />
                  <el-table-column
                    prop="actual"
                    label="实际"
                    width="80"
                    align="center">
                    <template slot-scope="scope">
                      <span :class="{'positive-value': parseFloat(scope.row.actual) > 0}">{{ scope.row.actual }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="rate-section">
                <div class="rate-boxes">
                  <div class="rate-box">
                    <div class="rate-header">计划</div>
                    <div class="rate-value-container">
                      <span class="rate-value">4.11 %</span>
                    </div>
                  </div>
                  <div class="rate-box">
                    <div class="rate-header">实绩</div>
                    <div class="rate-value-container">
                      <span class="rate-value green-value">3.66 %</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </screen-border>
        <screen-border
          title="班组非计划情况"
          class="team-section">
          <!-- <template v-slot:headerRight>
            <span
              class="screen-btn"
            >
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template> -->
          <el-table
            :data="teamData"
            row-key="id"
          >
            <el-table-column
              type="expand"
              width="50">
              <template slot-scope="props">
                <el-table
                  :data="props.row.children"
                  size="small"
                  style="width: 100%; margin-bottom: 10px;">
                  <el-table-column
                    label="班组"
                    width="100"
                    align="center">
                    <template slot-scope="scope">
                      {{ scope.row.team }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="responsibleUnit"
                    label="责任单位"
                    width="90"
                    align="center" />
                  <el-table-column
                    prop="resAc"
                    label="余材原因"
                    min-width="120"
                    align="center" />
                  <el-table-column
                    prop="wgtResAc"
                    label="轧制非计划"
                    width="90"
                    align="center" />
                  <el-table-column
                    prop="ratioResAc"
                    label="占比"
                    width="80"
                    align="center" />
                </el-table>
              </template>
            </el-table-column>
            <el-table-column
              prop="team"
              label="班组"
              width="100"
              align="center" />
            <el-table-column
              prop="wgt"
              label="轧钢非计划"
              min-width="100"
              align="center" />
            <el-table-column
              prop="wgtSum"
              label="轧制产量"
              min-width="100"
              align="center" />
            <el-table-column
              prop="ratio"
              label="占比"
              width="80"
              align="center" />
          </el-table>
        </screen-border>
      </div>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityWeeklyReportScreen/components/screen-border.vue'
import CustomTable from '@/pages/screen/qualityWeeklyReportScreen/components/custom-table.vue'
import CustomTableRange from '@/pages/screen/qualityWeeklyReportScreen/components/custom-table-range.vue'
import {
  teamNoplanInfoFindAllDate,
  currentNoplanInfoFindAllDate,
  currentNoplanInfoSaveAll,
  currentNoplanInfoFindAllDate1,
  currentNoplanInfoSaveAll2,
  findAllDateRemark,
  saveAllRemark
} from '@/api/screen'
import { post } from '@/lib/Util'

export default {
  name: 'QualityNotification',
  components: {
    CustomTable,
    CustomTableRange,
    ScreenBorder
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      submitLoading: false,
      cDate: null,
      dateParams: {},
      monthlyReport: '',
      tableObj: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index',
            width: '60',
            canEdit: false
          },
          {
            keyQuery: 'responsibleUnit',
            keySave: 'responsibleUnit',
            label: '责任单位',
            width: '100',
            canEdit: false
          },
          {
            keyQuery: 'resAc',
            keySave: 'resAc',
            label: '余材原因',
            width: '100',
            canEdit: false
          },
          {
            keyQuery: 'wgt',
            keySave: 'wgt',
            label: '重量',
            width: '100',
            canEdit: false
          },
          {
            keyQuery: 'ratio',
            keySave: 'ratio',
            label: '占比',
            width: '100',
            canEdit: false
          },
          {
            keyQuery: 'indicator',
            keySave: 'indicator',
            label: '指标',
            width: '100'
          },
          {
            keyQuery: 'ratioSubtractIndicator',
            keySave: 'ratioSubtractIndicator',
            label: '差值',
            width: '100',
            canEdit: false
          },
          {
            keyQuery: 'finishInfo',
            keySave: 'finishInfo',
            label: '完成情况',
            width: '100',
            canEdit: false
          }
        ],
        url: {
          list: currentNoplanInfoFindAllDate1,
          save: currentNoplanInfoSaveAll2
        }
      },
      spotData: [
        {
          index: 1,
          type: '热轧车间',
          subtype: '卷轧浅焊圆钢',
          amount: '425.313',
          ratio1: '1.48%',
          ratio2: '1.80%',
          difference: '-0.32%',
          status: '完成'
        },
        {
          index: 2,
          type: '热轧车间',
          subtype: '浮废异常',
          amount: '10.203',
          ratio1: '1.48%',
          ratio2: '1.80%',
          difference: '0.02%',
          status: '未完成'
        },
        {
          index: 3,
          type: '热轧车间',
          subtype: '疵块',
          amount: '4.15',
          ratio1: '1.48%',
          ratio2: '1.80%',
          difference: '-0.03%',
          status: '完成'
        },
        {
          index: 4,
          type: '热轧车间',
          subtype: '单轧疵条冷塞',
          amount: '2.137',
          ratio1: '1.48%',
          ratio2: '1.80%',
          difference: '-0.03%',
          status: '完成'
        },
        {
          index: 5,
          type: '热轧车间',
          subtype: '黄疽',
          amount: '0.972',
          ratio1: '1.48%',
          ratio2: '1.80%',
          difference: '-0.04%',
          status: '完成'
        },
        {
          index: 6,
          type: '热轧车间',
          subtype: '镀力皱',
          amount: '1.983',
          ratio1: '1.48%',
          ratio2: '1.80%',
          difference: '-0.04%',
          status: '完成'
        },
        {
          index: 7,
          type: '电炉车间',
          subtype: '浮废异常',
          amount: '46.834',
          ratio1: '1.48%',
          ratio2: '1.80%',
          difference: '0.15%',
          status: '未完成'
        },
        {
          index: 8,
          type: '电炉车间',
          subtype: '单标超不合',
          amount: '14.08',
          ratio1: '1.48%',
          ratio2: '1.80%',
          difference: '0.15%',
          status: '未完成'
        },
        {
          index: 9,
          type: '轧钢车间',
          subtype: '浮废异常',
          amount: '8.102',
          ratio1: '1.48%',
          ratio2: '1.80%',
          difference: '0.00%',
          status: '完成'
        },
        {
          index: '合计',
          type: '',
          subtype: '',
          amount: '574.887',
          ratio1: '1.48%',
          ratio2: '1.80%',
          difference: '-1.81%',
          status: '完成'
        }
      ],
      dairyPlanData: [
        { item: '结疤', planned: '0.22%', actual: '0.92%' },
        { item: '轧制', planned: '0.60%', actual: '0.92%' },
        { item: '擦痕', planned: '0.22%', actual: '0.92%' },
        { item: '带带', planned: '0.42%', actual: '0.92%' },
        { item: '综合', planned: '2.16%', actual: '0.67%' }
      ],
      dairyPlanData2: [
        { item: '结疤', planned: '0.92%', actual: '0.92%' },
        { item: '轧制', planned: '0.92%', actual: '0.92%' },
        { item: '擦痕', planned: '0.92%', actual: '0.92%' },
        { item: '带带', planned: '0.92%', actual: '0.92%' },
        { item: '综合', planned: '0.92%', actual: '0.92%' }
      ],
      teamData: []
    }
  },
  watch: {
    selectDate: function() {
      this.getRemarkInfo()
      this.getTeamData()
    }
  },
  created() {
    this.getTeamData()
    this.getRemarkInfo()
    //设置cDate数组日期范围为最近的周六到今天，并赋值给dateParams
    const today = new Date()
    const todayCopy = new Date(today) // 创建今天日期的副本，避免修改原始today变量

    // 计算最近的周六（如果今天是周六，则为今天；否则为上一个周六）
    // getDay()返回0-6，0是周日，6是周六
    const daysToLastSaturday = today.getDay() === 6 ? 0 : today.getDay() + 1
    const lastSaturday = new Date(todayCopy)
    lastSaturday.setDate(lastSaturday.getDate() - daysToLastSaturday)

    this.cDate = [lastSaturday, today]
    // 使用整个对象替换的方式，确保响应式更新
    this.dateParams = {
      startDate: this.$moment(lastSaturday).format('yyyy-MM-DD'),
      endDate: this.$moment(today).format('yyyy-MM-DD')
    }
    console.log('%c dateParams', 'color:red;', this.dateParams)
  },
  methods: {
    changeDate(date) {
      if (Array.isArray(date) && date.length) {
        // 使用整个对象替换的方式，确保响应式更新
        this.dateParams = {
          startDate: this.$moment(date[0]).format('yyyy-MM-DD'),
          endDate: this.$moment(date[1]).format('yyyy-MM-DD')
        }
        console.log('dateParams updated:', this.dateParams)
      }
    },
    async getTeamData() {
      const res = await post(teamNoplanInfoFindAllDate, {
        setTime: this.selectDate
      })
      this.teamData = res.data.map((item, index) => {
        return {
          ...item,
          id: `team-${index}`,
          children: item.children
            ? item.children.map((child, childIndex) => {
                return {
                  ...child,
                  id: `team-${index}-child-${childIndex}`
                }
              })
            : []
        }
      })
      console.log('%c this.teamData', 'color: #fff', this.teamData)
    },
    async getRemarkInfo() {
      const params = {
        setTime: this.selectDate,
        type: 2 // 1:质量通报 2:非计划统计 3.成材率 4.专利受理情况 5.
      }
      const res = await post(findAllDateRemark, params)
      this.monthlyReport = res.data.length ? res.data[0].remark : ''
    },
    async handleSave() {
      this.submitLoading = true
      const params = {
        setTime: this.selectDate,
        type: 2,
        data: {
          remark: this.monthlyReport
        }
      }
      const res = await post(saveAllRemark, params)
      if (res.status === 1) {
        this.$message.success('已提交')
      } else {
        this.$message.error(res.data)
      }
      this.submitLoading = false
    }
  }
}
</script>

<style lang="less">
/* 日期选择器下拉面板科技风格 */
.date-picker-dropdown {
  background-color: rgba(4, 29, 53, 0.95);
  border: 1px solid rgba(0, 242, 254, 0.7);
  box-shadow: 0 0 15px rgba(0, 242, 254, 0.3);

  /* 标题和头部样式 */
  .el-date-range-picker__header {
    margin: 8px;
    color: #fff;
  }

  /* 日期表格样式 */
  .el-picker-panel__content {
    color: #fff;

    .el-date-table {
      th {
        color: rgba(0, 242, 254, 0.8);
        font-weight: normal;
        border-bottom: 1px solid rgba(0, 242, 254, 0.3);
      }

      td {
        &.available:hover {
          background-color: rgba(0, 242, 254, 0.2);
        }

        &.in-range div {
          background-color: rgba(0, 242, 254, 0.15);
          color: #fff;
        }

        &.end-date div,
        &.start-date div {
          background-color: rgba(0, 242, 254, 0.8);
          color: #000;
        }

        &.today span {
          color: #1fc6ff;
        }

        &.disabled div {
          background-color: transparent;
          color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  /* 按钮样式 */
  .el-picker-panel__footer {
    background-color: rgba(4, 29, 53, 0.8);
    border-top: 1px solid rgba(0, 242, 254, 0.3);

    .el-button {
      background: transparent;
      border: 1px solid rgba(0, 242, 254, 0.5);
      color: #fff;

      &:hover {
        background-color: rgba(0, 242, 254, 0.2);
        border-color: rgba(0, 242, 254, 0.8);
      }

      &--default {
        margin-right: 10px;
      }

      &--primary {
        background-color: rgba(0, 242, 254, 0.3);
        border-color: rgba(0, 242, 254, 0.7);

        &:hover {
          background-color: rgba(0, 242, 254, 0.5);
        }
      }
    }
  }

  /* 箭头样式 */
  .el-date-range-picker__header .el-icon-arrow-left,
  .el-date-range-picker__header .el-icon-arrow-right,
  .el-date-range-picker__header .el-icon-d-arrow-left,
  .el-date-range-picker__header .el-icon-d-arrow-right {
    color: rgba(0, 242, 254, 0.8);

    &:hover {
      color: rgba(0, 242, 254, 1);
    }
  }

  .el-date-table td.end-date span,
  .el-date-table td.start-date span {
    background-color: rgba(0, 242, 254, 0.6);
    color: #fff;
    font-weight: bold;
  }
}
</style>


<style scoped lang="less">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: hidden;
  padding: 10px;
  box-sizing: border-box;

  .header-footer {
    margin-bottom: 10px;
    // height: 80px;
    border-radius: 4px;
    border: 1px solid rgba(31, 198, 255, 0.3);
    display: flex;
    align-items: flex-end;
    gap: 20px;
    position: relative;

    .submit-btn {
      width: 68px !important;
      height: 28px !important;
      padding: 0 !important;
      line-height: 26px !important;
      background: rgba(31, 198, 255, 0.3);
      border: 1px solid #1fc6ff;
      color: #fff;
      font-size: 14px;
      position: absolute;
      right: 10px;
      bottom: 10px;

      &:hover {
        background: rgba(31, 198, 255, 0.6);
      }
    }
  }

  /deep/ .el-textarea__inner,
  /deep/ .el-input__inner {
    background: transparent;
    border: 1px solid rgba(31, 198, 255, 0.3);
    color: #fff;
    padding: 10px 80px 10px 10px !important;

    &:focus {
      border-color: #1fc6ff;
    }
  }

  .table-section {
    display: flex;
    gap: 10px;
    height: calc(100% - 90px);
    width: 100%;
    overflow: hidden;
  }

  .table-left,
  .table-right {
    height: 100%;
    overflow: hidden;
  }

  /deep/ .el-table__header,
  /deep/ .el-table__body {
    min-width: 100% !important;
  }

  .table-left {
    width: 48%;
    display: flex;
    flex-direction: column;

    /deep/ .border-content {
      padding: 5px;
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    /deep/ .el-table {
      flex: 1;
    }
  }

  .table-right {
    width: 52%;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .team-section {
      margin-top: 10px;
      flex: 1;
      display: flex;
      flex-direction: column;

      /deep/ .border-content {
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      /deep/ .border-content-wrapper {
        max-height: 46%;
      }

      /deep/ .el-table {
        flex: 1;
      }
    }

    .dairy-section {
      height: 60%;
      display: flex;
      flex-direction: column;

      /deep/ .border-content {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .edit-icon-wrapper {
    position: absolute;
    right: 10px;
    top: 8px;
    z-index: 10;
    i {
      color: #1fc6ff;
      font-size: 16px;
      cursor: pointer;
    }
  }

  .chart-input {
    height: 100%;
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      height: 100%;
      padding: 0;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
        border-color: transparent !important;
      }
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      border-color: transparent !important;
    }
  }

  /deep/ .el-table {
    background-color: transparent;
    width: 100% !important;
    border: none; /* 移除表格边框 */
    table-layout: auto;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &::before {
      display: none;
    }

    /* 确保表格没有任何边框 */
    &.el-table--border,
    &.el-table--group {
      border: none;
    }

    &::after {
      display: none;
    }

    .el-table__header-wrapper {
      th {
        padding: 5px 0;
      }
    }

    .el-table__body-wrapper {
      overflow-y: auto !important;
      overflow-x: auto !important;
      flex: 1;
      max-height: none !important;
    }

    th {
      background-color: rgba(0, 50, 75, 0.8);
      color: #fff;
      border: none; /* 移除表头边框 */
      padding: 5px 0;
      font-size: 12px;
    }

    td {
      background-color: rgba(0, 40, 60, 0.5);
      color: #fff;
      border: none; /* 移除单元格边框 */
      padding: 5px 0;
      font-size: 12px;
      white-space: nowrap;
    }

    tr {
      background-color: transparent;
      &:nth-child(even) {
        td {
          background-color: rgba(31, 198, 255, 0.1);
        }
      }
    }

    /* 移除所有表格边框线 */
    &.el-table--border th.el-table__cell,
    &.el-table--border td.el-table__cell,
    &.el-table--border th.el-table__cell.is-leaf {
      border-right: none;
      border-bottom: none;
    }

    .el-table__cell {
      border-bottom: none;
    }

    .el-table__inner-wrapper::after {
      display: none;
    }

    .el-table__row:hover td {
      background-color: rgba(31, 198, 255, 0.2) !important;
    }
  }

  .dairy-tables {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    .dairy-title {
      background: url('../../../../assets/images/screen/unplan-title.png')
        no-repeat center center;
      background-size: 100% 100%;
      color: #fff;
      font-size: 14px;
      text-align: center;
      position: relative;
      border-right: none;
      width: 150px;
      height: 28px;
      line-height: 28px;
      box-sizing: border-box;
      margin-bottom: 10px;
      overflow: visible;
    }

    .dairy-table-row {
      display: flex;
      margin-bottom: 5px;
    }

    .dairy-table-content {
      display: flex;
      gap: 15px;
      margin-bottom: 10px;
      flex: 1;
      overflow: hidden;

      .dairy-table {
        flex: 1;
        min-width: 0;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .el-table {
          margin-top: 0;
          border-top-left-radius: 0;
          flex: 1;
          display: flex;
          flex-direction: column;
        }
      }
    }
  }

  .rate-section {
    .rate-boxes {
      display: flex;
      justify-content: center;
      gap: 20px;
    }

    .rate-box {
      width: 100px;
      border: 1px solid rgba(31, 198, 255, 0.5);
    }

    .rate-header {
      background-color: #0a7a8e;
      color: #fff;
      text-align: center;
      padding: 4px 0;
      font-size: 14px;
    }

    .rate-value-container {
      background-color: #041a21;
      padding: 4px 0;
      text-align: center;
    }

    .rate-value {
      font-size: 14px;
      color: #fff;
      font-weight: bold;
    }

    .green-value {
      color: #52c41a;
    }
  }

  .negative-value {
    color: #ff4d4f;
    font-weight: bold;
  }

  .positive-value {
    color: #52c41a;
    font-weight: bold;
  }

  .status-completed {
    color: #fff;
    font-weight: bold;
  }

  .status-incomplete {
    color: #ff4d4f;
    font-weight: bold;
  }

  .highlight {
    color: #1fc6ff;
    font-weight: bold;
  }

  /deep/ .border-content {
    padding: 10px;
    box-sizing: border-box;
    position: relative;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  /deep/ .el-table__body {
    width: 100% !important;
  }

  /deep/ .el-table__header {
    width: 100% !important;
  }

  /deep/ .el-table__header-wrapper {
    overflow: hidden;
  }

  /deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    display: block !important;
  }

  /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background: rgba(31, 198, 255, 0.5);
    border-radius: 4px;
    visibility: visible !important;
  }

  /deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    visibility: visible !important;
  }

  /deep/ .el-table__body-wrapper::-webkit-scrollbar-corner {
    background: rgba(0, 0, 0, 0.1);
    visibility: visible !important;
  }

  /deep/ .el-table__fixed-right-patch {
    background-color: rgba(0, 50, 75, 0.8);
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }
}

/* 优化日期选择器内部文本样式 */
/deep/ .el-date-editor {
  background-color: rgba(4, 29, 53, 0.7);
  border: 1px solid rgba(0, 242, 254, 0.5);

  .el-range-input {
    background-color: transparent;
    color: #fff;

    &::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }

    &:focus {
      border: none !important;
    }
  }

  .el-range-separator,
  .el-input__icon {
    color: rgba(0, 242, 254, 0.8);
  }

  /* 调整输入框间距和对齐方式 */
  .el-range__icon {
    transform: translateY(-1px);
    color: rgba(0, 242, 254, 0.8);
  }

  .el-range-separator {
    padding: 0 5px;
    transform: translate(-10px, -1px);
  }

  .el-input__inner {
    vertical-align: middle;
  }

  /* 调整输入框高度和内边距 */
  &.el-input__inner,
  .el-range-input {
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    transform: translateY(4px);
  }

  /* 针对开始日期和结束日期文本的特定样式 */
  .el-range__close-icon {
    color: rgba(0, 242, 254, 0.8);

    &:hover {
      color: rgba(0, 242, 254, 1);
    }
  }
}

/* 确保开始日期和结束日期的placeholder文本颜色与控件保持一致 */
/deep/ .el-date-editor input::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
/deep/ .el-date-editor input::-moz-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
/deep/ .el-date-editor input:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* 优化日期选择器弹出面板的输入框样式 */
/deep/ .el-picker-panel {
  .el-date-range-picker__editors-wrap {
    input {
      background-color: rgba(4, 29, 53, 0.8);
      border-color: rgba(0, 242, 254, 0.5);
      color: #fff;

      &:hover,
      &:focus {
        border-color: rgba(0, 242, 254, 0.8);
      }

      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }

  .el-date-range-picker__time-header {
    border-bottom-color: rgba(0, 242, 254, 0.3);
  }
}
</style>
