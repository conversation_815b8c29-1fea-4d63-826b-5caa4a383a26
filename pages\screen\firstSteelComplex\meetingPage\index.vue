<!--会议纪要-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border-multi>
                <template v-slot:title>
                  <div class="tabs-class">
                    <div
                      v-for="(item, index) in tabList"
                      :key="item.id"
                      :class="{'tab-pane-active': item.active}"
                      class="tab-pane"
                      @click="clickTabPane(item, index)">
                      <div class="tab-pane-title-class">
                        <div>{{ item.title }}</div>
                        <div
                          v-if="item.active"
                          class="tab-pane-img">
                          <img
                            class="tab-pane-img2"
                            src="@/assets/images/screen/tab-pane-active-line2.png"
                            alt="">
                          <img
                            class="tab-pane-img1"
                            src="@/assets/images/screen/tab-pane-active-line.png"
                            alt="">
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-slot:headerRight>
                  <span
                    class="screen-btn"
                    @click="clickAddProject">
                    <el-icon class="el-icon-upload2"/>
                    上传
                  </span>
                </template>
                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="ProjectData.loading"
                    :data="ProjectData.showGridData"
                    border>
                    <el-table-column
                      show-overflow-tooltip
                      width="70"
                      label="序号">
                      <template slot-scope="scope">
                        <div>{{ scope.$index+1 }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="文件名">
                      <template slot-scope="scope">
                        <div
                          style="text-decoration: underline;cursor: pointer;"
                          @click="clickProjectPreviewItem(scope.row)">{{ scope.row.fileName }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="上传日期"
                      width="130">
                      <template slot-scope="scope">
                        <div >{{ scope.row.uploadTime }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property=""
                      width="150"
                      label="操作">
                      <template v-slot="scope">
                        <!--                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectItem(scope.row)">查看</span>-->
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectDownloadItem(scope.row)">下载</span>
                        <span
                          v-command="'/first/steel/complex/delete'"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectDeleteItem(scope.row)">删除</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border-multi>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--会议纪要新增修改-->
    <el-dialog
      v-loading="ProjectData.loading"
      :visible.sync="ProjectData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="会议纪要">
      <template v-slot:title>
        <div class="custom-dialog-title">
          会议纪要
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">文件名</div>
          <el-input
            v-model="projectItem.fileName"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">文件</div>
          <el-upload
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :before-upload="beforeUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
            :multiple="false"
            action=""
            class="upload-demo">
            <el-button
              size="small"
              type="primary">点击上传</el-button>
            <div
              slot="tip"
              class="el-upload__tip">请选择文件</div>
          </el-upload>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="clickAddProjectData()">
          确定
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post, get } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  minio_download,
  minio_upload,
  oneBulletinBoard_deleteMeetingSummary,
  oneBulletinBoard_getMeetingSummary,
  oneBulletinBoard_saveMeetingSummary
} from '@/api/firstMeeting'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi.vue'

export default {
  name: 'ProjectPage',
  components: {
    ScreenBorderMulti,
    SingleBarsChart,
    SteelBarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      dialogVisible: false,
      pdfUrl: '',
      active: 0,
      tabList: [
        {
          id: '1',
          type: 'A',
          active: true,
          title: '公司/事业部'
        },
        {
          id: '2',
          type: 'B',
          active: false,
          title: '厂部'
        },
        {
          id: '3',
          type: 'C',
          active: false,
          title: '安全环保室'
        },
        {
          id: '4',
          type: 'D',
          active: false,
          title: '品质室'
        },
        {
          id: '5',
          type: 'E',
          active: false,
          title: '设备室'
        },
        {
          id: '6',
          type: 'F',
          active: false,
          title: '生产管理室'
        }
      ],
      tabMap: {
        A: '公司/事业部',
        B: '厂部',
        C: '安全环保室',
        D: '品质室',
        E: '设备室',
        F: '生产管理室'
      },
      cDate: '',
      ProjectData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      projectItem: {},
      fileList: [],
      fileUrl: '',
      file: null
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getProjectData()
    },
    active: function() {
      this.getProjectData()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    beforeUpload(file) {
      this.file = file
      console.log('beforeUpload', file, this.file)
      if (this.projectItem.fileName.length === 0) {
        this.projectItem.fileName = file.name
      }
      console.log('beforeUpload', this.projectItem, file.name)
    },
    handleRemove(file, fileList) {
      console.log('handleRemove', file, fileList)
    },
    handlePreview(file) {
      console.log('handlePreview', file, this.fileList)
    },
    handleExceed(files, fileList) {
      console.log('handleExceed', files, this.fileList)
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },
    beforeRemove(file, fileList) {
      const isDel = this.$confirm(`确定移除 ${file.name}？`)
      if (isDel) {
        this.file = null
      }
      return isDel
    },
    //上传文件
    async uploadFile(file) {
      let formData = new FormData()
      formData.append('file', file)
      // formData.append('userNo', this.userNo)
      post(minio_upload, formData)
        .then(res => {
          console.log('上传文件：', res)
          let index = res.indexOf('?')
          if (index !== -1) {
            this.fileUrl = res.substring(0, index)
          } else {
            this.fileUrl = res
          }
          this.projectItem.fileUrl = this.fileUrl
          //清空文件
          this.fileList = []
          this.addProjectData()
        })
        .catch(err => {
          this.$message.error('上传失败！' + err)
          console.log(err)
        })
    },
    //tab切换
    clickTabPane(item, index) {
      this.tabList.forEach(item => {
        item.active = false
      })
      item.active = true
      this.active = index
    },
    //点击上传
    clickAddProject() {
      this.projectItem = {
        deptNo: this.tabList[this.active].type,
        fileName: '',
        fileUrl: ''
      }
      this.ProjectData.dialogVisible = true
    },
    //督办点击查看详情
    clickProjectItem(row) {
      this.projectItem = JSON.parse(JSON.stringify(row))
      this.ProjectData.dialogVisible = true
    },
    async clickProjectPreviewItem(row) {
      if (row.fileUrl) {
        let lastDotIndex = row.fileUrl.lastIndexOf('.')
        let format = row.fileUrl.substring(lastDotIndex + 1)
        console.log('format:', format)
        let data = await get(row.fileUrl)
        if (!data) {
          return
        }

        let type = ''
        if (format === 'pdf') {
          type = 'application/pdf'
        } else if (format === 'png') {
          type = 'image/png'
        } else if (format === 'jpeg') {
          type = 'image/jpeg'
        }
        const url = window.URL.createObjectURL(new Blob([data], { type: type }))

        //预览
        if (type.length !== 0) {
          window.open(url)
        } else {
          //下载
          this.$message.warning('该文件类型不支持预览只能下载之后打开！')
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', row.fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
        }
        window.URL.revokeObjectURL(url) //释放掉blob对象
      } else {
        this.$message.error('没有文件url无法下载！')
      }
    },
    clickProjectDownloadItem(row) {
      if (row.fileUrl) {
        window.open(row.fileUrl)
      } else {
        this.$message.error('没有文件url无法下载！')
      }
    },
    //督办点击删除
    clickProjectDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteProject(row.id)
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    clickAddProjectData() {
      if (this.file) {
        this.uploadFile(this.file)
      } else {
        this.addProjectData()
      }
    },
    //新增/修改
    addProjectData() {
      let deptNo = this.projectItem.deptNo
      let fileName = this.projectItem.fileName
      let fileUrl = this.projectItem.fileUrl
      if (deptNo === null || deptNo.length === 0) {
        this.$message.warning('请选择部门类别！')
        return
      }
      if (fileName === null || fileName.length === 0) {
        this.$message.warning('请输入文件名称！')
        return
      }
      if (fileUrl === null || fileUrl.length === 0) {
        this.$message.warning('请选择上传文件！')
        return
      }
      const params = [this.projectItem]
      this.ProjectData.loading = true
      post(oneBulletinBoard_saveMeetingSummary, params)
        .then(res => {
          if (res.success) {
            this.$notify.success('操作成功！')
            this.ProjectData.dialogVisible = false
            this.getProjectData()
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    //删除
    deleteProject(id) {
      const params = [
        {
          id: id
        }
      ]
      post(oneBulletinBoard_deleteMeetingSummary, params).then(res => {
        if (res.success) {
          this.$notify.success('删除成功！')
          this.getProjectData()
        }
      })
    },
    calculateHeight() {
      this.ProjectData.maxHeight = this.$refs.table1.offsetHeight
    },
    getProjectData() {
      this.ProjectData.loading = true
      post(oneBulletinBoard_getMeetingSummary, {
        setDate: this.cDate
      })
        .then(res => {
          if (res && res.success) {
            // this.$message.success('查询成功！')
            let list = []
            res.data.forEach(item => {
              if (item.deptNo === this.tabList[this.active].type) {
                list.push({
                  id: item.id,
                  deptNo: item.deptNo,
                  uploadTime: item.uploadTime,
                  fileName: item.fileName,
                  fileUrl: item.fileUrl
                })
              }
            })
            this.ProjectData.showGridData = list
            // this.ProjectData.showGridData = res.data.map(item => {
            //   return {
            //     id: item.id,
            //     deptNo: item.deptNo,
            //     uploadTime: item.uploadTime,
            //     fileName: item.fileName,
            //     fileUrl: item.fileUrl
            //   }
            // })
            this.ProjectData.gridData = lodash.cloneDeep(
              this.ProjectData.showGridData
            )
          } else {
            this.$message.error(res.message)
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
.tabs-class {
  display: flex;
  flex-direction: row;
  .tab-pane {
    color: #ffffffbf;
    margin-right: 21px;
  }
  .tab-pane-active {
    color: #ffffff;
  }
  .tab-pane-title-class {
    display: flex;
    flex-direction: column;
    position: relative;
    .tab-pane-img {
      .tab-pane-img2 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
      .tab-pane-img1 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        margin-bottom: 7px;
      }
    }
  }
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
