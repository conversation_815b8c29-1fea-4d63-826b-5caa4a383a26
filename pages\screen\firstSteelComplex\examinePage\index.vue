<!--考核通报-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border-multi :title="'考核通报'">
                <template v-slot:headerRight>
                  <span
                    class="screen-btn"
                    @click="clickAddProject">
                    <el-icon class="el-icon-upload2"/>
                    上传
                  </span>
                </template>
                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="ProjectData.loading"
                    :data="ProjectData.showGridData"
                    border>
                    <el-table-column
                      show-overflow-tooltip
                      width="70"
                      label="序号">
                      <template slot-scope="scope">
                        <div>{{ scope.$index+1 }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="文件名">
                      <template slot-scope="scope">
                        <div
                          style="text-decoration: underline;cursor: pointer;"
                          @click="clickProjectDownloadItem(scope.row)">{{ scope.row.fileName }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="上传日期"
                      width="130">
                      <template slot-scope="scope">
                        <div>{{ scope.row.uploadTime }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property=""
                      width="150"
                      label="操作">
                      <template v-slot="scope">
                        <!--<span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectItem(scope.row)">查看</span>-->
                        <span
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectDownloadItem(scope.row)">下载</span>
                        <span
                          v-command="'/first/steel/complex/delete'"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectDeleteItem(scope.row)">删除</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border-multi>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--考核通报新增修改-->
    <el-dialog
      v-loading="ProjectData.loading"
      :visible.sync="ProjectData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="考核通报">
      <template v-slot:title>
        <div class="custom-dialog-title">
          考核通报
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">文件名</div>
          <el-input
            v-model="projectItem.fileName"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">文件</div>
          <el-upload
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :before-upload="beforeUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
            :multiple="false"
            class="upload-demo">
            <el-button
              size="small"
              type="primary">点击上传</el-button>
            <div
              slot="tip"
              class="el-upload__tip">请选择文件</div>
          </el-upload>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="clickAddProjectData()">
          确定
        </span>
      </div>
    </el-dialog>

    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="上月导入日期库存">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { get, post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  minio_upload,
  oneBulletinBoard_deleteCheckReport,
  oneBulletinBoard_getCheckReport,
  oneBulletinBoard_saveCheckReport
} from '@/api/firstMeeting'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi.vue'
export default {
  name: 'ProjectPage',
  components: {
    ScreenBorderMulti,
    SingleBarsChart,
    SteelBarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      ProjectData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      projectItem: {},
      fileList: [],
      fileUrl: '',
      file: null
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getProjectData()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    beforeUpload(file) {
      this.file = file
      console.log('beforeUpload', file, this.file)
      if (this.projectItem.fileName.length === 0) {
        this.projectItem.fileName = file.name
      }
      console.log('beforeUpload', this.projectItem, file.name)
    },
    handleRemove(file, fileList) {
      console.log(file, fileList)
    },
    handlePreview(file) {
      console.log(file)
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },
    beforeRemove(file, fileList) {
      const isDel = this.$confirm(`确定移除 ${file.name}？`)
      if (isDel) {
        this.file = null
      }
      return isDel
    },
    //上传文件
    async uploadFile(file) {
      let formData = new FormData()
      formData.append('file', file)
      // formData.append('userNo', this.userNo)
      post(minio_upload, formData)
        .then(res => {
          console.log('上传文件：', res)
          let index = res.indexOf('?')
          if (index !== -1) {
            this.fileUrl = res.substring(0, index)
          } else {
            this.fileUrl = res
          }
          this.projectItem.fileUrl = this.fileUrl
          //清空文件
          this.fileList = []
          this.addProjectData()
        })
        .catch(err => {
          this.$message.error('上传失败！' + err)
          console.log(err)
        })
    },
    //督办点击删除
    clickAddProject() {
      this.projectItem = {
        fileName: '',
        fileUrl: ''
      }
      this.ProjectData.dialogVisible = true
    },
    //督办点击查看详情
    clickProjectItem(row) {
      this.projectItem = JSON.parse(JSON.stringify(row))
      this.ProjectData.dialogVisible = true
    },
    async clickProjectDownloadItem(row) {
      if (row.fileUrl) {
        let lastDotIndex = row.fileUrl.lastIndexOf('.')
        let format = row.fileUrl.substring(lastDotIndex + 1)
        console.log('format:', format)
        // let data = await get(
        //   minio_download + row.fileUrl.replace('http://172.25.63.72:9123/', '')
        // )
        let data = await get(row.fileUrl)
        // let data = await get(row.fileUrl)
        if (!data) {
          return
        }

        let type = ''
        if (format === 'pdf') {
          type = 'application/pdf'
        } else if (format === 'png') {
          type = 'image/png'
        } else if (format === 'jpeg') {
          type = 'image/jpeg'
        }
        const url = window.URL.createObjectURL(new Blob([data], { type: type }))

        //预览
        if (type.length !== 0) {
          window.open(url)
        } else {
          //下载
          this.$message.warning('该文件类型不支持预览只能下载之后打开！')
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', row.fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
        }
        window.URL.revokeObjectURL(url) //释放掉blob对象
      } else {
        this.$message.error('没有文件url无法下载！')
      }
    },
    //督办点击删除
    clickProjectDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteProject(row.id)
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    clickAddProjectData() {
      if (this.file) {
        this.uploadFile(this.file)
      } else {
        this.addProjectData()
      }
    },
    //新增/修改
    addProjectData() {
      let fileName = this.projectItem.fileName
      let fileUrl = this.projectItem.fileUrl
      if (fileName === null || fileName.length === 0) {
        this.$message.warning('请输入文件名称！')
        return
      }
      if (fileUrl === null || fileUrl.length === 0) {
        this.$message.warning('请选择上传文件！')
        return
      }
      const params = [this.projectItem]
      this.ProjectData.loading = true
      post(oneBulletinBoard_saveCheckReport, params)
        .then(res => {
          if (res.success) {
            this.$notify.success('操作成功！')
            this.ProjectData.dialogVisible = false
            this.getProjectData()
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    //删除
    deleteProject(id) {
      const params = [
        {
          id: id
        }
      ]
      post(oneBulletinBoard_deleteCheckReport, params).then(res => {
        if (res.success) {
          this.$notify.success('删除成功！')
          this.getProjectData()
        }
      })
    },
    calculateHeight() {
      this.ProjectData.maxHeight = this.$refs.table1.offsetHeight
    },
    getProjectData() {
      this.ProjectData.loading = true
      post(oneBulletinBoard_getCheckReport, {
        setDate: this.cDate
      })
        .then(res => {
          this.ProjectData.showGridData = res.data.map(item => {
            return {
              id: item.id,
              uploadTime: item.uploadTime,
              fileName: item.fileName,
              fileUrl: item.fileUrl
            }
          })
          this.ProjectData.gridData = lodash.cloneDeep(
            this.ProjectData.showGridData
          )
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
