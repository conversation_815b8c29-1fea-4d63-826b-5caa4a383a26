<template>
  <div class="quality-report">
    <div class="operation-buttons">
      <el-button 
        type="primary" 
        icon="el-icon-upload2" 
        @click="handleUpload">上传照片</el-button>
      <el-button 
        type="danger" 
        @click="clearImages">图片清空</el-button>
      <el-button 
        :loading="submitLoading" 
        type="success"
        @click="submitDefects">钢种缺陷提交</el-button>
    </div>
    
    <div class="image-grid-container">
      <div class="image-grid">
        <!-- 第一行4个图片 -->
        <div class="image-row">
          <div 
            class="image-item" 
            draggable="true" 
            @dragstart="dragStart($event, 0)" 
            @dragover.prevent 
            @drop="handleDrop($event, 0)">
            <img-view
              v-if="images[0]"
              :src="images[0]"
              :is-id="false"
              deleteable
              @img-preview="handleImgPreview"
              @img-delete="handleImgDeleteByUrl($event, 0)"/>
            <div 
              v-if="!images[0]" 
              class="image-number">1</div>
            <div 
              v-if="images[0]" 
              class="image-controls">
              <i 
                v-if="0 > 0" 
                class="el-icon-arrow-left" 
                @click="moveImage(0, -1)"/>
              <i 
                v-if="0 < 7" 
                class="el-icon-arrow-right" 
                @click="moveImage(0, 1)"/>
            </div>
          </div>
          <div 
            class="image-item" 
            draggable="true" 
            @dragstart="dragStart($event, 1)" 
            @dragover.prevent 
            @drop="handleDrop($event, 1)">
            <img-view
              v-if="images[1]"
              :src="images[1]"
              :is-id="false"
              deleteable
              @img-preview="handleImgPreview"
              @img-delete="handleImgDeleteByUrl($event, 1)"/>
            <div 
              v-if="!images[1]" 
              class="image-number">2</div>
            <div 
              v-if="images[1]" 
              class="image-controls">
              <i 
                v-if="1 > 0" 
                class="el-icon-arrow-left" 
                @click="moveImage(1, -1)"/>
              <i 
                v-if="1 < 7" 
                class="el-icon-arrow-right" 
                @click="moveImage(1, 1)"/>
            </div>
          </div>
          <div 
            class="image-item" 
            draggable="true" 
            @dragstart="dragStart($event, 2)" 
            @dragover.prevent 
            @drop="handleDrop($event, 2)">
            <img-view
              v-if="images[2]"
              :src="images[2]"
              :is-id="false"
              deleteable
              @img-preview="handleImgPreview"
              @img-delete="handleImgDeleteByUrl($event, 2)"/>
            <div 
              v-if="!images[2]" 
              class="image-number">3</div>
            <div 
              v-if="images[2]" 
              class="image-controls">
              <i 
                v-if="2 > 0" 
                class="el-icon-arrow-left" 
                @click="moveImage(2, -1)"/>
              <i 
                v-if="2 < 7" 
                class="el-icon-arrow-right" 
                @click="moveImage(2, 1)"/>
            </div>
          </div>
          <div 
            class="image-item" 
            draggable="true" 
            @dragstart="dragStart($event, 3)" 
            @dragover.prevent 
            @drop="handleDrop($event, 3)">
            <img-view
              v-if="images[3]"
              :src="images[3]"
              :is-id="false"
              deleteable
              @img-preview="handleImgPreview"
              @img-delete="handleImgDeleteByUrl($event, 3)"/>
            <div 
              v-if="!images[3]" 
              class="image-number">4</div>
            <div 
              v-if="images[3]" 
              class="image-controls">
              <i 
                v-if="3 > 0" 
                class="el-icon-arrow-left" 
                @click="moveImage(3, -1)"/>
              <i 
                v-if="3 < 7" 
                class="el-icon-arrow-right" 
                @click="moveImage(3, 1)"/>
            </div>
          </div>
        </div>
        
        <!-- 第二行4个图片 -->
        <div class="image-row">
          <div 
            class="image-item" 
            draggable="true" 
            @dragstart="dragStart($event, 4)" 
            @dragover.prevent 
            @drop="handleDrop($event, 4)">
            <img-view
              v-if="images[4]"
              :src="images[4]"
              :is-id="false"
              deleteable
              @img-preview="handleImgPreview"
              @img-delete="handleImgDeleteByUrl($event, 4)"/>
            <div 
              v-if="!images[4]" 
              class="image-number">5</div>
            <div 
              v-if="images[4]" 
              class="image-controls">
              <i 
                v-if="4 > 0" 
                class="el-icon-arrow-left" 
                @click="moveImage(4, -1)"/>
              <i 
                v-if="4 < 7" 
                class="el-icon-arrow-right" 
                @click="moveImage(4, 1)"/>
            </div>
          </div>
          <div 
            class="image-item" 
            draggable="true" 
            @dragstart="dragStart($event, 5)" 
            @dragover.prevent 
            @drop="handleDrop($event, 5)">
            <img-view
              v-if="images[5]"
              :src="images[5]"
              :is-id="false"
              deleteable
              @img-preview="handleImgPreview"
              @img-delete="handleImgDeleteByUrl($event, 5)"/>
            <div 
              v-if="!images[5]" 
              class="image-number">6</div>
            <div 
              v-if="images[5]" 
              class="image-controls">
              <i 
                v-if="5 > 0" 
                class="el-icon-arrow-left" 
                @click="moveImage(5, -1)"/>
              <i 
                v-if="5 < 7" 
                class="el-icon-arrow-right" 
                @click="moveImage(5, 1)"/>
            </div>
          </div>
          <div 
            class="image-item" 
            draggable="true" 
            @dragstart="dragStart($event, 6)" 
            @dragover.prevent 
            @drop="handleDrop($event, 6)">
            <img-view
              v-if="images[6]"
              :src="images[6]"
              :is-id="false"
              deleteable
              @img-preview="handleImgPreview"
              @img-delete="handleImgDeleteByUrl($event, 6)"/>
            <div 
              v-if="!images[6]" 
              class="image-number">7</div>
            <div 
              v-if="images[6]" 
              class="image-controls">
              <i 
                v-if="6 > 0" 
                class="el-icon-arrow-left" 
                @click="moveImage(6, -1)"/>
              <i 
                v-if="6 < 7" 
                class="el-icon-arrow-right" 
                @click="moveImage(6, 1)"/>
            </div>
          </div>
          <div 
            class="image-item" 
            draggable="true" 
            @dragstart="dragStart($event, 7)" 
            @dragover.prevent 
            @drop="handleDrop($event, 7)">
            <img-view
              v-if="images[7]"
              :src="images[7]"
              :is-id="false"
              deleteable
              @img-preview="handleImgPreview"
              @img-delete="handleImgDeleteByUrl($event, 7)"/>
            <div 
              v-if="!images[7]" 
              class="image-number">8</div>
            <div 
              v-if="images[7]" 
              class="image-controls">
              <i 
                v-if="7 > 0" 
                class="el-icon-arrow-left" 
                @click="moveImage(7, -1)"/>
              <i 
                v-if="7 < 7" 
                class="el-icon-arrow-right" 
                @click="moveImage(7, 1)"/>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="input-sections">
      <div class="input-section">
        <div class="label">钢种：</div>
        <el-input 
          :rows="3" 
          v-model="steelType" 
          type="textarea" 
          placeholder="请输入内容"/>
      </div>
      <div class="input-section">
        <div class="label">缺陷：</div>
        <el-input 
          :rows="3" 
          v-model="defect" 
          type="textarea" 
          placeholder="请输入内容"/>
      </div>
    </div>
    
    <!-- 隐藏的文件上传组件 -->
    <el-upload
      ref="fileInput"
      :auto-upload="false"
      :http-request="httpRequest"
      :on-change="handleChange"
      :show-file-list="false"
      :before-upload="beforeUpload"
      multiple
      action="#"
      style="display: inline">
      <el-button
        style="display: none"
        size="small"
        type="primary">点击上传</el-button>
    </el-upload>
  </div>
</template>

<script>
import {
  batchUpdateResource,
  deleteFileByIds,
  uploadFile,
  downloadFileById
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { typicalQualityDefects, typicalQualityDefectsSave } from '@/api/screen'

export default {
  name: 'TypicalQualityDefects',
  components: { ImgView },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      images: Array(8).fill(null),
      imageIds: Array(8).fill(null), // 存储图片ID，用于删除
      steelType: '',
      defect: '',
      baseURL: 'http://************:9800/' + downloadFileById,
      submitLoading: false
    }
  },
  watch: {
    selectDate: {
      handler() {
        this.fetchDefectsData()
      },
      immediate: true
    }
  },
  created() {
    this.fetchDefectsData()
  },
  methods: {
    // 初始化获取数据
    fetchDefectsData() {
      post(typicalQualityDefects, { setTime: this.selectDate })
        .then(res => {
          if (res.data) {
            // 处理返回的数据
            const { data } = res
            if (data && data.length > 0) {
              const item = data[0]

              // 如果有picture字段且不为空
              if (item.picture) {
                // 分割逗号分隔的图片ID字符串
                const pictureIds = item.picture.split(',')

                // 遍历所有图片ID，构建URL并设置到images数组
                pictureIds.forEach((id, index) => {
                  if (index < 8 && id) {
                    // 限制最多8张图片
                    // 保存图片ID
                    this.$set(this.imageIds, index, id)
                    // 构建完整URL
                    const imageUrl = this.baseURL + id
                    this.$set(this.images, index, imageUrl)
                  }
                })
              }

              // 如果有钢种和缺陷信息，设置到对应字段
              this.steelType = item.steelType || ''
              this.defect = item.defect || ''
            }
          }
        })
        .catch(error => {
          console.error('获取数据失败:', error)
          this.$message.error('获取钢种缺陷数据失败')
        })
    },

    httpRequest(params) {},

    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    },

    async handleChange(file, fileList) {
      // 在处理前清空el-upload组件的文件列表，以防止重复累加
      const currentFiles = fileList.map(f => f.raw).filter(Boolean)

      // 清空el-upload组件的文件列表
      this.$nextTick(() => {
        this.$refs.fileInput.clearFiles()
      })

      let currentIndex = this.images.findIndex(img => img === null)

      if (currentIndex === -1) {
        this.$message.warning('已经上传了8张图片，请先清空再上传')
        return
      }

      // 限制上传的数量，防止超出8张
      const availableSlots = 8 - currentIndex
      const filesToUpload = currentFiles.slice(0, availableSlots)

      // 判断用户选择的文件是否超过剩余可用空位
      if (currentFiles.length > availableSlots) {
        this.$message.warning(
          `最多只能上传8张图片，您还可以上传${availableSlots}张图片`
        )
      }

      if (filesToUpload.length === 0) return

      const formData = new FormData()
      filesToUpload.forEach(file => {
        formData.append('files', file)
      })

      post(uploadFile, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
        .then(res => {
          console.log('上传成功，后端返回数据：', res)
          if (res.success && res.data && res.data.length > 0) {
            this.$message.success(
              '文件上传成功！, 请点击"钢种缺陷提交"按钮进行保存'
            )

            // 将图片按顺序放入空位
            res.data.forEach((fileInfo, index) => {
              if (currentIndex + index < 8) {
                const fileId = fileInfo.id
                const imageUrl = this.baseURL + fileId
                this.$set(this.images, currentIndex + index, imageUrl)
                this.$set(this.imageIds, currentIndex + index, fileId)
              }
            })
          } else {
            this.$message.warning('文件上传失败！')
          }
        })
        .catch(error => {
          console.error('上传失败：', error)
          this.$message.error('图片上传失败')
        })
    },

    handleUpload() {
      // 先清空文件列表，再触发文件选择
      if (this.$refs.fileInput) {
        this.$refs.fileInput.clearFiles()
      }
      this.$refs.fileInput.$el.querySelector('input').click()
    },

    async handleImgDelete(fileId) {
      if (!fileId) return

      try {
        const del = await post(deleteFileByIds, { ids: [fileId] })
        if (del.success) {
          this.$message.success(
            '文件删除成功！，请点击"钢种缺陷提交"按钮进行保存'
          )
          return true
        } else {
          this.$message.error('文件删除失败')
          return false
        }
      } catch (error) {
        console.error('删除文件错误:', error)
        this.$message.error('删除文件时发生错误')
        return false
      }
    },

    getImageSrc(index) {
      return this.images[index - 1] || ''
    },

    clearImages() {
      // 先立即清空前端显示
      this.images = Array(8).fill(null)
      this.imageIds = Array(8).fill(null)

      // 再异步删除后端文件
      const filesToDelete = [...this.imageIds].filter(id => id !== null)

      console.log('%c filesToDelete', 'color: red', filesToDelete)

      if (filesToDelete.length > 0) {
        post(deleteFileByIds, { ids: filesToDelete })
          .then(res => {
            if (res.success) {
              this.$message.success(
                '页面图片已清空，请点击"钢种缺陷提交"按钮进行保存'
              )
            } else {
              this.$message.warning('图片清空可能不完全')
            }
          })
          .catch(error => {
            console.error('清空图片错误:', error)
            this.$message.error('清空图片时发生错误')
          })
      } else {
        this.$message.success(
          '所有图片已清空，请点击"钢种缺陷提交"按钮进行保存'
        )
      }
    },

    submitDefects() {
      // 验证是否有上传的图片
      // if (!this.imageIds.some(id => id !== null)) {
      //   this.$message.warning('请至少上传一张图片')
      //   return
      // }

      // 设置加载状态
      this.submitLoading = true

      // 构建提交参数
      const today = new Date()
      const setTime =
        today.getFullYear() +
        '-' +
        String(today.getMonth() + 1).padStart(2, '0') +
        '-' +
        String(today.getDate()).padStart(2, '0')

      // 将所有非空图片ID拼接成一个以逗号分隔的字符串
      const pictureIds = this.imageIds.filter(id => id !== null).join(',')

      const params = {
        setTime: setTime,
        data: [
          {
            picture: pictureIds,
            steelType: this.steelType,
            defect: this.defect
          }
        ]
      }

      post(typicalQualityDefectsSave, params)
        .then(res => {
          this.submitLoading = false
          if (res.data === '导入数据成功') {
            this.$message.success('钢种缺陷提交成功')
          } else {
            this.$message.error(
              '钢种缺陷提交失败: ' + (res.message || '未知错误')
            )
          }
        })
        .catch(error => {
          this.submitLoading = false
          console.error('提交错误:', error)
          this.$message.error('提交时发生错误')
        })
    },

    handleImgPreview(data) {
      // 可以添加额外的预览处理逻辑
      console.log('预览图片:', data)
    },

    handleImgDeleteByUrl(event, index) {
      // 获取当前图片的ID
      const fileId = this.imageIds[index]
      if (fileId) {
        this.handleImgDelete(fileId).then(success => {
          if (success) {
            // 从数组中移除
            this.$set(this.images, index, null)
            this.$set(this.imageIds, index, null)
          }
        })
      }
    },

    // 新增方法 - 拖拽开始
    dragStart(event, index) {
      event.dataTransfer.setData('sourceIndex', index)
    },

    // 新增方法 - 处理放置
    handleDrop(event, targetIndex) {
      const sourceIndex = event.dataTransfer.getData('sourceIndex')
      this.swapImages(parseInt(sourceIndex), targetIndex)
    },

    // 新增方法 - 交换图片
    swapImages(sourceIndex, targetIndex) {
      if (sourceIndex === targetIndex) return

      // 交换图片URL
      const tempImage = this.images[sourceIndex]
      this.$set(this.images, sourceIndex, this.images[targetIndex])
      this.$set(this.images, targetIndex, tempImage)

      // 交换图片ID
      const tempId = this.imageIds[sourceIndex]
      this.$set(this.imageIds, sourceIndex, this.imageIds[targetIndex])
      this.$set(this.imageIds, targetIndex, tempId)
    },

    // 新增方法 - 移动图片
    moveImage(index, direction) {
      const newIndex = index + direction
      if (newIndex >= 0 && newIndex < 8) {
        this.swapImages(index, newIndex)
      }
    }
  }
}
</script>

<style scoped lang="less">
.quality-report {
  min-height: 100vh;
  padding: 0 20px 20px 20px;
  color: white;

  .operation-buttons {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;

    .el-button {
      margin-left: 10px;
      background-color: #0c4e64;
      border: 1px solid #1db7ec;
      border-radius: 5px;
      font-size: 14px;
      font-weight: bold;
    }
  }

  .image-grid-container {
    border: 1px solid #0c4e64;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    height: calc(100vh - 410px);
    margin-top: 3px;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: flex-start;
    overflow: scroll;

    .image-grid {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;

      .image-row {
        width: 100%;
        height: 50%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .image-item {
    position: relative;
    background-color: #0a2a42;
    width: 24%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain; /* 修改为contain避免图片变形 */
    }

    .image-number {
      position: absolute;
      font-size: 48px;
      font-weight: bold;
    }

    // 添加图片控制按钮样式
    .image-controls {
      position: absolute;
      bottom: 5px;
      right: 5px;
      display: flex;
      gap: 8px;

      i {
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        border-radius: 50%;
        padding: 5px;
        font-size: 14px;
        cursor: pointer;

        &:hover {
          background-color: rgba(0, 0, 0, 0.8);
        }
      }
    }
  }

  .input-sections {
    display: flex;
    padding-top: 16px;
    gap: 20px;
    margin-bottom: 20px; /* 减少底部空白区域 */

    .input-section {
      flex: 1;

      .label {
        margin-bottom: 5px;
        font-weight: bold;
      }

      .el-textarea {
        width: 100%;
        margin-top: 4px;

        /deep/.el-textarea__inner {
          height: 120px; /* 减小文本框高度 */
          background-color: #041a21;
          color: white;
          border-color: #0c4e64;
        }
      }
    }
  }
}
</style>
