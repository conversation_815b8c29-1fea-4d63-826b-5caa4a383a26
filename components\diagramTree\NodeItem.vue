<template>
  <div
    :style="{
      '--lineBottom': lineBottom,
      '--display': list.length ? 'block': 'none'
    }"
    class="tree-item"
  >
    <template
      v-for="(child, index) in list">
      <div
        :key="child.id">
        <div
          ref="treeNode"
          :class="{
            'warning': child.warningStatus,
            'trend': child.trendWarningStatus
          }"
          class="tree-node tree-node-children">
          <Node
            :node="child"
            @nodeClick="getChildren(index)"
            @changeStatus="changeStatus($event, index)"/>
        </div>
        <template v-if="child.children && child.children.length && !child.hiddenChildren">
          <node-item
            :key="child.id + index"
            :node="child.children"
            :only-warning="onlyWarning"
            :rank="rank"
            :level="level + 1"
            @size-change="calculateHeight"/>
        </template>
        <div style="clear: both"/>
      </div>
    </template>
  </div>
</template>

<script>
import { getCoreResultValue, offSet, post } from '@/lib/Util'
import { math } from '@/lib/Math'
import Node from '@/components/diagramTree/Node'
import { findKpiRelTree } from '@/api/kpi'

export default {
  name: 'NodeItem',
  components: { Node },
  props: {
    node: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    },
    onlyWarning: {
      type: Boolean,
      default: false
    },
    rank: {
      type: Number,
      default: 3
    },
    level: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      lineBottom: '0px',
      list: []
    }
  },
  watch: {
    onlyWarning: function() {
      this.updateData()
      this.$nextTick(() => {
        this.calculateHeight()
      })
    },
    rank: function() {
      this.updateData()
      this.$nextTick(() => {
        this.calculateHeight()
      })
    },
    node: function() {
      this.updateData()
      this.$nextTick(() => {
        this.calculateHeight()
      })
    }
  },
  mounted() {
    this.updateData()
    this.$nextTick(() => {
      this.calculateHeight()
    })
  },
  methods: {
    updateData() {
      this.list = this.node
        .filter(item => {
          return !this.onlyWarning || item.warningStatus
        })
        .map(item => {
          item.hiddenChildren = false
          return item
        })
    },
    calculateHeight() {
      console.log(this.$refs.treeNode)
      this.lineBottom =
        this.$refs.treeNode && this.$refs.treeNode.length
          ? this.$refs.treeNode.slice(-1)[0].offsetTop + 'px'
          : '0px'
      this.$emit('size-change')
    },
    changeStatus(event, index) {
      const origin = this.list[index]
      this.list.splice(
        index,
        1,
        Object.assign({}, origin, {
          hiddenChildren: !origin.hiddenChildren
        })
      )
      this.$nextTick(() => {
        this.calculateHeight()
      })
    },
    getChildren(index) {
      console.log(index)
      post(findKpiRelTree, { kid: this.list[index].kid }).then(res => {
        this.list[index].children = res.data.children
        this.$nextTick(() => {
          this.calculateHeight()
        })
      })
    }
  }
}
</script>

<style scoped lang="less">
.tree-node {
  padding: 5px 15px;
  border-radius: 4px;
  font-size: 16px;
  background: #eff4fd;
  border-left: 3px solid #5e93ed;
  float: left;
  .node-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .node-describe {
      margin-right: 15px;
      small {
        color: #666;
        font-size: 10px;
      }
    }
    .node-arrow {
      cursor: pointer;
    }
  }
  span.result {
    display: block;
    color: #5e93ed;
    font-weight: 600;
    font-size: 20px;
    line-height: 28px;
    height: 28px;
  }
  &.trend {
    border-color: #ffa958;
    background: #fff6ee;
    /deep/ span.result {
      color: #ffa958;
    }
  }
  &.warning {
    border-color: #f56c6c;
    background: #fef0f0;
    /deep/ span.result {
      color: #f56c6c;
    }
  }
}
.tree-item {
  position: relative;
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  &:before {
    content: '';
    position: absolute;
    left: 70px;
    top: 30px;
    height: var(--lineBottom);
    width: 1px;
    background: #dcdfe6;
  }
  &:after {
    display: var(--display);
    content: '';
    position: absolute;
    left: 0;
    top: 30px;
    width: 70px;
    height: 1px;
    background: #dcdfe6;
  }
}
.tree-node-children {
  margin-left: 140px;
  margin-bottom: 20px;
  position: relative;
  &:before {
    content: '';
    position: absolute;
    right: 100%;
    top: 30px;
    width: 72px;
    height: 1px;
    background: #dcdfe6;
  }
}
</style>
