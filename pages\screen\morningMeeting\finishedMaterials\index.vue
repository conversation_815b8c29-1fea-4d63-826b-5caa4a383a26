<template>
  <div class="content">
    <div 
      class="content-item" 
      style="flex: 1.3">
      <materials-table
        v-if="material.length"
        :title="'成品订单材分析'"
        :setting="material"
        :url-list="materialUrl.list"
        :url-save="materialUrl.save"
        :can-edit="canEditMonth"
        :default-data="defaultData"
        :merge-arr="['setTime', 'entryName', 'subProjects']"
        :select-date="cDate"
        @dateChange="changeDate"
        @change="getMaterial"/>
    </div>
    <div class="content-hold"/>
    <div 
      class="content-item" 
      style="flex: 1; overflow: visible">
      <screen-border :title="'成品订单材分析趋势'">
        <div class="chart-wrapper">
          <div
            class="chart">
            <stock-line-chart
              :show-legend="true"
              :chart-data="materialUrl.data"
              :x-data="materialUrl.xData"/>
          </div>
        </div>
      </screen-border>
    </div>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { batchUpdateResource } from '@/api/system'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { finishProductOrderSave, finishProductOrderFind } from '@/api/screen'
import * as _ from 'lodash'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import StockLineChart from '@/pages/screen/morningMeeting/component/stock-line-chart'
import CustomTable from '@/pages/screen/morningMeeting/component/custom-table'
import MaterialsTable from '@/pages/screen/morningMeeting/finishedMaterials/materials-table'
export default {
  name: 'finishedMaterials',
  components: {
    MaterialsTable,
    CustomTable,
    StockLineChart,
    SingleBarsChart,
    SteelBarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      materialUrl: {
        save: finishProductOrderSave,
        list: finishProductOrderFind,
        data: [],
        xData: []
      },
      material: [],
      dayArr: [],
      defaultData: [
        {
          subprojects: '已入库钢板在途（不含码头）',
          entryname: '订单材不可开单',
          unit: '物流中心'
        },
        {
          subprojects: '每天XAA钢板未入库在交接货位',
          entryname: '订单材不可开单',
          unit: '中厚板卷厂-物流中心'
        },
        {
          subprojects: '每天XAA钢板未入库在交接货位',
          entryname: '订单材不可开单',
          unit: '宽板厂-物流中心'
        },
        {
          subprojects: '每天XAA钢板未入库在交接货位',
          entryname: '订单材不可开单',
          unit: '中板厂-物流中心'
        },
        {
          subprojects: '每天XAA钢板未入库在交接货位',
          entryname: '订单材不可开单',
          unit: '小计'
        },
        {
          subprojects: '每天XAA钢板未入库在分厂货位',
          entryname: '订单材不可开单',
          unit: '中厚板卷厂'
        },
        {
          subprojects: '每天XAA钢板未入库在分厂货位',
          entryname: '订单材不可开单',
          unit: '宽厚板厂'
        },
        {
          subprojects: '每天XAA钢板未入库在分厂货位',
          entryname: '订单材不可开单',
          unit: '中板厂'
        },
        {
          subprojects: '每天XAA钢板未入库在分厂货位',
          entryname: '订单材不可开单',
          unit: '小计'
        },
        {
          subprojects: 'XAA具备发货量',
          entryname: '订单材可开单',
          unit: '营销处-物流中心'
        },
        {
          subprojects: 'XAA具备发货量',
          entryname: '订单材可开单',
          unit: '国贸-物流中心'
        },
        {
          subprojects: 'XAA具备发货量',
          entryname: '订单材可开单',
          unit: '特用钢-物流中心'
        },
        {
          subprojects: 'XAA具备发货量',
          entryname: '订单材可开单',
          unit: '小计'
        },
        {
          subprojects: null,
          entryname: '每天出库量',
          unit: '物流中心-营销处'
        },
        {
          subprojects: null,
          entryname: '每天出库量',
          unit: '物流中心-出口'
        },
        {
          subprojects: null,
          entryname: '每天出库量',
          unit: '合计'
        },
        {
          subprojects: null,
          entryname: '成品余材库存',
          unit: '生产处'
        },
        {
          subprojects: null,
          entryname: '现货库存',
          unit: '物流中心'
        }
      ],
      arrSet: [
        {
          keyQuery: 'entryname',
          keySave: 'entryName',
          label: '项目名称',
          fixed: 'left',
          width: '140'
        },
        {
          keyQuery: 'subprojects',
          keySave: 'subProjects',
          label: '子项目',
          fixed: 'left',
          width: '140'
        },
        {
          keyQuery: 'unit',
          keySave: 'unit',
          label: '责任单位',
          fixed: 'left',
          width: '140'
        },
        {
          keyQuery: 'target',
          keySave: 'target',
          fixed: 'left',
          label: '目标'
        }
      ]
    }
  },
  watch: {
    selectDate: function() {
      console.log(this.selectDate)
      this.setArr()
      this.cDate = this.selectDate
    },
    cDate: function() {}
  },
  created() {
    this.setArr()
    this.cDate = this.selectDate
  },
  mounted() {
    this.$nextTick(() => {
      // this.calculateHeight()
    })
  },
  methods: {
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    setArr() {
      // 设置默认数据时间
      this.defaultData.forEach(item => {
        item.settime = this.$moment(this.selectDate)
          .subtract(1, 'day')
          .format('YYYY-MM')
      })
      const day = this.$moment(this.selectDate)
        .subtract(1, 'day')
        .format('DD')
      this.material = []
      this.dayArr = []
      for (let i = 1; i <= Number(day); i++) {
        this.dayArr.push(i)
      }
      this.material = this.arrSet.concat(
        this.dayArr.map((item, index) => {
          return {
            keyQuery: 'day' + item,
            keySave: 'day' + item,
            label: item + '号',
            show: index >= this.dayArr.length - 10
          }
        })
      )
    },
    getMaterial(data) {
      this.materialUrl.data = []
      const chartData = data.filter(
        item => item.unit === '小计' || item.unit === '合计'
      )
      const showArr = this.dayArr.filter(item => chartData[0]['day' + item])
      this.materialUrl.xData = showArr
      this.materialUrl.data = chartData.map(item => {
        return {
          name:
            (item.entryName || '') +
            (item.subProjects ? '-' + item.subProjects : ''),
          data: showArr.map(day => {
            return item['day' + day]
          })
        }
      })
      console.log(this.materialUrl.data)
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
      if ([0, 1, 2, 3, 4, 5, 6, 7].includes(columnIndex)) {
        if (rowIndex === 0) {
          return [3, 1]
        } else {
          return [0, 0]
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.scroll-wrapper {
  height: 100%;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: visible;
  }
}

/deep/ .el-table.el-table--border {
  border-width: 0 !important;
}
/deep/ table.el-table--border th.el-table__cell,
/deep/ .el-table td.el-table__cell,
/deep/ .el-table th.el-table__cell.is-leaf {
  border-bottom: 2px solid #081f27 !important;
}
</style>
