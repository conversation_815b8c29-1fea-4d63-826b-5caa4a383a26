<template>
  <div 
    :id="containerId" 
    :style="{ height: '100%' }"/>
</template>
 
 <script>
export default {
  name: '<PERSON><PERSON>ap<PERSON><PERSON>',
  props: {
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return [
          //  '#2772F0',
          //  '#F5B544',
          //  '#51DF81',
          //  '#edf173',
          //  '#e89a78',
          '#d24f14',
          //  '#b21111'
          '#19be6b'
        ]
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 0
    },
    unit: {
      type: String,
      default: '吨'
    },
    titles: {
      type: String,
      default: ''
    },
    getZr: {
      type: Boolean,
      default: false
    },
    tooltipbg: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
    window.addEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
      }
      const options = {
        // title: {
        //   text: this.titles, // 标题文本内容
        //   textStyle: {
        //     color: '#fff', // 标题颜色
        //     fontSize: 16 // 标题字体大小
        //   },
        //   left: 20
        // },
        // tooltip: {
        //   show: this.showToolbox,
        //   trigger: 'axis',
        //   confine: true,
        //   axisPointer: {
        //     type: 'shadow'
        //   },
        //   borderColor: '#1fc6ff',
        //   backgroundColor: '#041a21',
        //   textStyle: {
        //     color: '#fff',
        //     fontSize: 14
        //   },
        //   padding: 10,
        //   //  formatter: function(params) {
        //   //    console.log('params', this.chartData)

        //   //    // params 是一个数组，包含了多个对象，每个对象对应一个系列的数据信息
        //   //    let result = `厂区: ${params[0].axisValueLabel}<br/>`
        //   //    params.forEach(function(item) {
        //   //      result += `${item.seriesName}: ${item.value}<br/>`
        //   //    })
        //   //    return result
        //   //  }
        //   //  },
        //   formatter: this.tooltipbg
        //     ? params => {
        //         let extra = []
        //         extra.push(this.chartData.map(item => item.extra))
        //         //  console.log('params', extra)
        //         params[0].extra = extra[0][0][params[0].dataIndex]
        //         params[1].extra = extra[0][1][params[0].dataIndex]
        //         //  console.log('params2', params)
        //         return (
        //           params[0].axisValue +
        //           '<br/>' +
        //           params
        //             .map(
        //               item =>
        //                 `<div style="display: flex; justify-content: space-between"><span>
        //     <span style="display: inline-block; width: 8px; height: 8px; vertical-align: middle; border-radius: 50%;background: ${
        //       item.color
        //     }"></span>
        //                         ${item.data.name || item.seriesName}
        //     </span> <span> &emsp;
        //                         ${item.value}</span><span> &emsp; &emsp;目标
        //                         ${item.extra}</span></div>
        //                        `
        //             )
        //             .join('')
        //         )
        //       }
        //     : null
        //   //  formatter: function(params) {
        //   //    const data = params[0].data
        //   //    let note = `<p style="line-height: 24px">${data.name}
        //   //                &emsp;
        //   //                <b style="font-weight: bold">${data.value}</b></p>`
        //   //    return note
        //   //  }
        // },
        // color: this.color,
        // legend: {
        //   show: false,
        //   align: 'left',
        //   top: 5,
        //   right: 105,
        //   padding: 0,
        //   // icon: 'circle',
        //   textStyle: {
        //     color: '#C9E1FDF2',
        //     fontSize: 12
        //   },
        //   // itemHeight: 10, // 修改icon图形大小
        //   // itemWidth: 10, // 修改icon图形大小
        //   // itemGap: 10, // 修改间距
        //   itemStyle: {
        //     borderWidth: 0,
        //     padding: 0
        //   }
        // },
        // grid: {
        //   top: this.showLegend ? '18%' : '6%',
        //   left: '0%',
        //   right: '1%',
        //   bottom: '1%',
        //   containLabel: true
        // },
        // xAxis: [
        //   {
        //     type: 'category',
        //     axisTick: { show: false },
        //     data: this.xData,
        //     axisLabel: {
        //       color: '#fff',
        //       fontSize: 12,
        //       interval: 0,
        //       rotate: this.labelRotate || 0
        //     },
        //     axisLine: {
        //       show: false,
        //       lineStyle: {
        //         color: '#EAEBF0'
        //       }
        //     }
        //   }
        // ],
        // yAxis: [
        //   {
        //     name: this.unit,
        //     type: 'value',
        //     minInterval: 0.2,

        //     max: 1, // 设置 y 轴的最大值

        //     axisLine: {
        //       show: false
        //     },
        //     axisLabel: {
        //       color: '#fff',
        //       fontSize: 12,
        //       align: 'left'
        //     },
        //     splitLine: {
        //       lineStyle: {
        //         color: '#2e4262'
        //       }
        //     }
        //   }
        // ],
        // series: this.chartData.map(item => {
        //   return {
        //     name: item.name,
        //     type: 'bar',
        //     itemStyle: {
        //       color: function(params) {
        //         // params 是当前数据项的信息，包括 value, dataIndex, seriesIndex 等
        //         return item.finished[params.dataIndex] ? '#19BE6B' : '#FF2855'
        //       }
        //       //   color: item.finished ? '#19BE6B' : '#FF2855'
        //       // color: item.finished ? '#FF2855' : '#19BE6B'
        //     },
        //     barGap: item.barGap,
        //     barWidth: this.barWidth || 190 / this.chartData.length + '%',
        //     barMaxWidth: this.barWidth || 30,
        //     showBackground: this.barBackground,
        //     backgroundStyle: {
        //       color: 'rgba(232, 236, 239, 0.3)'
        //     },
        //     markPoint: {
        //       symbolSize: 5
        //     },
        //     label: {
        //       show: this.showLabel,
        //       color: '#fff',
        //       position: 'top',
        //       fontSize: 10,
        //       offset: [0, 5]
        //     },
        //     data: item.data
        //   }
        // })
        series: [
          {
            type: 'treemap',
            data: [
              {
                name: 'nodeA',
                value: 10,
                children: [
                  {
                    name: 'nodeAa',
                    value: 4
                  },
                  {
                    name: 'nodeAb',
                    value: 6
                  }
                ]
              },
              {
                name: 'nodeB',
                value: 20,
                children: [
                  {
                    name: 'nodeBa',
                    value: 20,
                    children: [
                      {
                        name: 'nodeBa1',
                        value: 20
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
      this.myChart.setOption(options)
      if (this.getZr) {
        this.myChart.off('click') //防止触发两次点击事件
        this.myChart.getZr().on('click', params => {
          let pointInPixel = [params.offsetX, params.offsetY]
          if (this.myChart.containPixel('grid', pointInPixel)) {
            let pointInGrid = this.myChart.convertFromPixel(
              {
                seriesIndex: 0
              },
              pointInPixel
            )
            let xIndex = pointInGrid[0] //索引
            let handleIndex = Number(xIndex)
            let seriesObj = this.myChart.getOption() //图表object对象
            var op = this.myChart.getOption()
            //获得图表中点击的列
            var month = op.xAxis[0].data[handleIndex] //获取点击的列名
            let data = { month, handleIndex, seriesObj }
            this.$emit('child', data)
          }
        })
      }
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>
 
 <style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
