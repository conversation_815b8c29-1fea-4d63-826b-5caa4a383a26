<template>
  <div class="content">
    <div class="content-item top">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <screen-border :title="'设备故障'">
            <el-radio-group
              v-model="radio"
              @change="handleChange">
              <el-radio :label="'中厚板卷厂'">中厚板卷厂</el-radio>
              <el-radio :label="'宽厚板厂'">宽厚板厂</el-radio>
              <el-radio :label="'中板厂'">中板厂</el-radio>
              <el-radio :label="'第一炼钢厂'">第一炼钢厂</el-radio>
            </el-radio-group>
            <div
              class="chart-wrapper"
              style="margin-top: -10px">
              <div
                class="chart">
                <FoldedColumnChart
                  :bar-width="15"
                  :unit="'停时(min)'"
                  :unit1="'次'"
                  :get-zr="true"
                  :color="['#61a4e4','#ffa958','#f56c6c']"
                  :chart-data="bar1"
                  :x-data="barX1" />
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <screen-border :title="'隐患数量'">
            <div class="chart-wrapper alarms">
              <el-row class="alarm-box">
                <el-col
                  :span="24"
                  class="alarm-box">
                  <div
                    class="chart">
                    <FoldedColumnChart
                      :show-legend="false"
                      :chart-data="hiddenDanger"
                      :color="['#61a4e4','#ffa958','#f56c6c']"
                      :unit="'数量'"
                      :bar-width="10"
                      :x-data="hiddenDangerX"
                    />
                    <!-- <bars-chart
                      :bar-width="30"
                      :unit="'次'"
                      :titles="'当月'"
                      :get-zr="true"
                      :color="['#D45454','#F5B544','#2772F0']"
                      :chart-data="callThePolice.bar1"
                      :x-data="callThePolice.barX1"
                      @child="reportHandlerChild"/> -->
                    <!-- <bars-chart
                      :bar-width="30"
                      :unit="'个'"
                      :get-zr="true"
                      :color="['#D45454','#F5B544','#2772F0']"
                      :chart-data="hiddenDanger.bar1"
                      :x-data="hiddenDanger.barX1"
                      @child="hideHandlerChild"/> -->
                    <FoldedColumnChart
                      :bar-width="15"
                      :unit="'停时(min)'"
                      :unit1="'次'"
                      :get-zr="true"
                      :color="['#61a4e4','#ffa958','#f56c6c']"
                      :chart-data="hiddenDanger"
                      :x-data="hiddenDangerX" />

                  </div>
                </el-col>
                <!-- <el-col
                  :span="12"
                  class="alarm-box">
                  <div
                    class="chart">
                    <bars-chart
                      :bar-width="30"
                      :unit="'次'"
                      :titles="'昨日'"
                      :get-zr="true"
                      :color="['#D45454','#F5B544','#2772F0']"
                      :chart-data="callThePolice_day.bar1"
                      :x-data="callThePolice_day.barX1"
                      @child="reportHandlerChild_day"/>
                  </div>
                </el-col> -->
              </el-row>
            </div>
          </screen-border>
        </el-col>
      </el-row>

    </div>
    <div class="content-hold" />
    <div class="content-item top">
      <el-row
        :gutter="32"
        class="full-height">

        <el-col
          :span="12"
          class="full-height">
          <screen-border :title="'报警数量'">
            <div class="chart-wrapper alarms">
              <el-row class="alarm-box">
                <el-col
                  :span="24"
                  class="alarm-box">
                  <div
                    class="chart">
                    <bars-chart
                      :bar-width="30"
                      :unit="'次'"
                      :get-zr="true"
                      :color="['#2772F0','#D45454','#F5B544']"
                      :chart-data="callThePolice.bar1"
                      :x-data="callThePolice.barX1"
                      @child="reportHandlerChild" />
                  </div>
                </el-col>
                <!-- <el-col
                  :span="12"
                  class="alarm-box">
                  <div
                    class="chart">
                    <bars-chart
                      :bar-width="30"
                      :unit="'次'"
                      :titles="'昨日'"
                      :get-zr="true"
                      :color="['#D45454','#F5B544','#2772F0']"
                      :chart-data="callThePolice_day.bar1"
                      :x-data="callThePolice_day.barX1"
                      @child="reportHandlerChild_day"/>
                  </div>
                </el-col> -->
              </el-row>
            </div>
          </screen-border>

        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <screen-border :title="'点检异常'">
            <div class="chart-wrapper alarms">
              <el-row class="alarm-box">
                <el-col
                  :span="24"
                  class="alarm-box">
                  <div
                    class="chart">
                    <bars-chart-3
                      :bar-width="30"
                      :unit="'异常数'"
                      :unit1="'%'"
                      :get-zr="true"
                      :color="['#F45549','#FFDA35']"
                      :chart-data="abnormalData"
                      :x-data="abnormalDataX" />
                  </div>
                </el-col>
              </el-row>
            </div>
          </screen-border>
        </el-col>
      </el-row>

    </div>
  </div>
</template>

<script>
import BarsChart from '@/pages/screen/diviceMeeting/component/bars-chart'
import BarsChart3 from '@/pages/screen/diviceMeeting/component/bars-chart3'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border.vue'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import FoldedColumnChart from '@/pages/screen/diviceMeeting/component/FoldedColumn-chart'
import { post } from '@/lib/Util'
import {
  dangerStatisticsFactory,
  dangerStatisticsFactoryDetail,
  findAllWithHandle,
  getAlarmLevelCount,
  getFactoryMainInfo,
  alarmManagement,
  alarmConfig,
  dangerQuantityStatisticsFactory,
  dangerStopReport,
  getFactoryFailureByMonth,
  queryNotDealDangerInfo,
  abnormalSpotCheck
} from '@/api/device'
import * as _ from 'lodash'
// import {
//   getMonthFirstDay,
//   getMonthLastDay
// } from '@/pages/screen/diviceMeeting/component/dateUtil/dateUtil'
import {
  getYearFirstDay,
  getYearLastDay,
  getNowDay,
  getMonthFirstDay,
  getLastFirstMonth,
  getYearFirstDayLast
} from '@/utils/dateUtil'
import moment from 'moment'

export default {
  name: 'spareParts',
  components: {
    BarsChart,
    BarsChart3,
    ScreenBorder,
    ScreenMixins,
    FoldedColumnChart
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      radio: '中厚板卷厂',
      //   hiddenDanger: {
      //     bar1: [],
      //     barX1: []
      //   },
      hiddenDanger: [
        {
          name: '未处理数',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        }
      ],
      hiddenDangerX: [],
      alarmData: [],
      alarmDataX: [],
      form: {
        businessDivision: '3',
        // beginDate: getMonthFirstDay(),
        beginDate: getYearFirstDayLast(),
        endDate: getNowDay()
      },
      bar1: [
        {
          name: '考核线',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        },
        {
          name: '停时',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        },
        {
          name: '频次',
          type: 'line',
          yAxisIndex: 1,
          barGap: 0,
          data: []
        }
      ],
      barX1: [],
      callThePolice: {
        bar1: [
          {
            name: '已处理',
            data: [],
            barGap: '0'
          },
          {
            name: '未处理',
            data: [],
            barGap: '0'
          },
          {
            name: '处理中',
            // data: [150, 230, 224, 218, 135, 147, 260],
            data: [],
            barGap: '0'
          }
        ],
        barX1: [
          '第一炼钢厂',
          '宽厚板厂',
          '中厚板卷厂',
          '中板厂',
          '金石材料厂',
          '金润智能工厂'
        ]
      },
      //   callThePolice_day: {
      //     bar1: [
      //       {
      //         name: '报警总数',
      //         // data: [150, 230, 224, 218, 135, 147, 260],
      //         data: [],
      //         barGap: '0'
      //       },
      //       {
      //         name: '已处理',
      //         data: [],
      //         barGap: '0'
      //       },
      //       {
      //         name: '未处理',
      //         data: [],
      //         barGap: '0'
      //       }
      //     ],
      //     barX1: ['第一炼钢厂', '宽厚板厂']
      //   },
      HazardDetails: [],
      AlarmDetails: [],
      factoryNo: '',
      defaultDisplay: false,
      processData: {
        alarmsNumber0: 0,
        wideHandle: 0,
        wideUnhandle: 0
      },
      processData_1: {
        alarmsNumber1: '0',
        oneHandle: '',
        oneUnhandle: ''
      },
      alertDetailsTime: [
        moment(new Date().getTime() - 30 * 24 * 1000 * 60 * 60).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        moment(new Date().getTime()).format('YYYY-MM-DD HH:mm:ss')
      ],
      dataList: [],
      dataList_1: [],
      startTime: '',
      endTime: '',
      treeCodes: '',
      title: '当月-所有',
      abnormalData: [],
      abnormalDataX: []
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.$nextTick(item => {
        if (this.checkIfAfter26(this.cDate) == 1) {
          this.beginTime = this.$moment()
            .subtract(1, 'month')
            .startOf('month')
            .date(26)
            .startOf('day')
            .format('yyyy-MM-DD')
          this.endTime = this.$moment(this.cDate).format('yyyy-MM-DD')
        } else if (this.checkIfAfter26(this.cDate) == 0) {
          //26号之后  开始时间为这个月26号 到选择时间
          this.beginTime = this.$moment()
            .date(26)
            .startOf('day')
            .format('yyyy-MM-DD')
          this.endTime = this.$moment(this.cDate).format('yyyy-MM-DD')
        } else if (this.checkIfAfter26(this.cDate) == 2) {
          //当前26号  开始丶结束都是26号

          this.beginTime = this.$moment(this.cDate).format('yyyy-MM-DD')
          this.endTime = this.$moment(this.cDate).format('yyyy-MM-DD')
        }
        /*this.beginTime = this.$moment(this.cDate)
          .subtract(1, 'month')
          .startOf('month')
          .date(26)
          .startOf('day')
          .format('YYYY-MM-DD 00:00:00')
        this.endTime = this.$moment(this.cDate)
          .date(26)
          .endOf('day')
          .format('YYYY-MM-DD 23:59:59')*/
        this.treeCodes = ''
        this.title = '当月-所有'
        this.getHiddenDanger()
        this.getAbnormalSpot()
        // this.getHiddenTableDefault()
        // this.getCallThePolice()
        // this.getCallThePolice_day()
        // this.defaultPostTime()
        this.getAlarmDetails()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
    /*this.beginTime =
      this.$moment(this.cDate)
        .startOf('month')
        .format('YYYY-MM-DD') + ' 00:00:00'
    this.endTime =
      this.$moment(this.cDate)
        .endOf('month')
        .format('YYYY-MM-DD') + ' 23:59:59'*/

    this.beginTime = this.$moment(this.cDate)
      .subtract(1, 'month')
      .startOf('month')
      .date(26)
      .startOf('day')
      .format('YYYY-MM-DD 00:00:00')
    this.endTime = this.$moment(this.cDate)
      .date(26)
      .endOf('day')
      .format('YYYY-MM-DD 23:59:59')
  },
  mounted() {
    this.getHiddenDanger()
    this.getFactoryFailureByMonth('中厚板卷厂')
    // this.getAbnormalSpot()
    // this.getHiddenTable()
    // this.getHiddenTableDefault()
    // this.getCallThePolice()
    // this.getCallThePolice_day()
    // this.defaultPostTime()
    this.getAlarmDetails()
  },
  methods: {
    handleChange(value) {
      //   console.log('选中的值:', value)
      this.getFactoryFailureByMonth(value)
    },
    //判断是否在26号之前还是之后
    //26号之前  开始时间为上个月26号 到选择时间
    //26号之后  开始时间为这个月26号 到选择时间
    checkIfAfter26(selectedDate) {
      // 将选定日期转换为 Moment 对象
      const selectedDateMoment = this.$moment(selectedDate)

      // 获取当前月份的 26 号的 Moment 对象
      const currentMonth26 = this.$moment()
        .date(26)
        .startOf('day')

      // 判断选择的日期是否在当前月份的 26 号之后
      if (selectedDateMoment.isAfter(currentMonth26)) {
        console.log('选择日期在当前月份的 26 号之后')
        return 0
      } else if (selectedDateMoment.isSame(currentMonth26, 'day')) {
        console.log('选择日期是当前月份的 26 号')
        return 2
      } else {
        console.log('选择日期在当前月份的 26 号之前')
        return 1
      }
    },
    //时间转义
    formatterTime(row, column, cellValue, index) {
      return moment(cellValue).format('YYYY-MM-DD HH:mm:ss')
    },
    // 默认传参时间
    // defaultPostTime() {
    //   // 宽厚板厂地图数据默认传今日时间
    //   this.mapWidePost = {
    //     startTime: this.$moment(this.cDate).format('yyyy-MM-DD') + ' 00:00:00',
    //     endTime: this.$moment(this.cDate).format('yyyy-MM-DD') + ' 23:59:59'
    //   }
    // },
    //隐患数量统计点击柱
    hideHandlerChild(msg) {
      const month = msg.month
      switch (month) {
        case '第一炼钢厂':
          this.factoryNo = '73'
          break
        case '中板厂':
          this.factoryNo = '66'
          break
        case '宽厚板厂':
          this.factoryNo = '38'
          break
        case '中厚板卷厂':
          this.factoryNo = '32'
          break
        case '金石材料厂':
          this.factoryNo = '84'
          break
        case '金润智能工厂':
          this.factoryNo = '87'
          break
      }
      this.getHiddenTable()
      // console.log('隐患', msg)
    },
    getFactorycode(name) {
      switch (name) {
        case '32':
          return '中厚板卷厂'
        case '38':
          return '宽厚板厂'
        case '66':
          return '中板厂'
        case '87':
          return '金润智能工厂'
        case '73':
          return '第一炼钢厂'
        case '84':
          return '金石材料厂'
        // case '宽厚板厂':
        //   return '宽厚板厂'
        // case '板材事业部机关':
        //   return '板材事业部机关'
        case '10':
          return '事业部'
        default:
          return name
      }
    },
    //隐患數量統計
    getHiddenDanger() {
      const params = this.form
      post(queryNotDealDangerInfo, params).then(res => {
        let allSum1 = res.data.reduce((sum, item) => sum + item.NOMAL, 0)
        let allSum2 = res.data.reduce((sum, item) => sum + item.BIGGER, 0)
        let allSum3 = res.data.reduce((sum, item) => sum + item.MAJOR, 0)
        res.data.push({
          FACTORY_NO: '10',
          NOMAL: allSum1,
          BIGGER: allSum2,
          MAJOR: allSum3
        })
        res.data.map(item => {
          return {
            value: this.getFactorycode(item.FACTORY_NO),
            code: item.FACTORY_NO
          }
        })
        console.log('hiddenDanger::::: %c', 'color:red', res.data)
        this.hiddenDangerX = res.data.map(item => {
          return {
            value: this.getFactorycode(item.FACTORY_NO),
            code: item.FACTORY_NO
          }
        })
        this.hiddenDanger = [
          //隐患
          {
            name: '一般隐患',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: res.data.map(item => item.NOMAL)
          },
          {
            name: '较大隐患',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: res.data.map(item => item.BIGGER)
          },
          {
            name: '重大隐患',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: res.data.map(item => item.MAJOR)
          }
        ]
      })
    },
    getFactoryFailureByMonth(factory) {
      const params = {
        businessDivision: '3',
        beginMonth: this.$moment(this.cDate)
          .subtract(11, 'month')
          .format('yyyy-MM'),
        endMonth: this.$moment(this.cDate).format('yyyy-MM')
      }
      let list = []
      post(getFactoryFailureByMonth, params).then(res => {
        if (res.success) {
          let data = res.data.find(item => item.FACTORYNAME === factory)
          this.barX1 = data.DATA.map(item => item.YEARMONTH)
          this.bar1[0].data = data.DATA.map(item => item.STDLINE)
          this.bar1[1].data = data.DATA.map(item => item.RATEMINUTE)
          this.bar1[2].data = data.DATA.map(item => item.FAILURETIMES)
        }
      })
    },
    getAbnormalSpot() {
      const params = {
        startTime: this.cDate
      }
      this.abnormalDataX = []
      this.abnormalData = []
      post(abnormalSpotCheck, params).then(res => {
        if (res.data) {
          this.abnormalDataX = res.data.map(item => {
            return {
              value: item.NAME,
              code: item.NAME
            }
          })
          this.abnormalData = [
            {
              name: '异常数',
              type: 'bar',
              yAxisIndex: 0,
              barGap: 0,
              data: res.data.map(item => item.ALARM)
            },
            {
              name: '任务漏检率',
              type: 'bar',
              yAxisIndex: 1,
              barGap: 0,
              data: res.data.map(item => parseFloat(item.ROUTENOTCHKEDBATE))
            }
          ]
        }
      })
    },
    //隐患明细---table ---默认
    async getHiddenTableDefault() {
      const params = {
        beginDate: this.$moment(this.cDate)
          .subtract(7, 'day')
          .format('yyyy-MM-DD'), //开始日期
        endDate: this.$moment(this.cDate).format('yyyy-MM-DD'), //结束日期
        status: 'A',
        statusName: '未处理',
        isAplay: 'Y',
        pageIndex: 1,
        pageSize: 16,
        isSelf: 0
      }
      post(findAllWithHandle, params).then(res => {
        this.defaultDisplay = true
        this.HazardDetails = res.data.tlist
        // console.log('隐患数量统计---table', this.HazardDetails)
      })
    },
    //报警数量统计---柱状图(当月)
    async getCallThePolice() {
      let params_Info = {
        startTime:
          this.$moment(this.cDate)
            .startOf('month')
            .format('YYYY-MM-DD') + ' 00:00:00',
        endTime:
          this.$moment(this.cDate)
            .endOf('month')
            .format('YYYY-MM-DD') + ' 23:59:59'
      }
      post(getFactoryMainInfo, params_Info).then(res => {
        const B1 = res.data.find(item => item.code == 31)
        const C2 = res.data.find(item => item.code == 34)
        // 报警总数
        this.callThePolice.bar1[0].data = [
          Number(B1.handledOneLevelAlarmNum) +
            Number(B1.twoLevelAlarmNum) +
            Number(B1.threeLevelAlarmNum),
          Number(C2.handledOneLevelAlarmNum) +
            Number(C2.twoLevelAlarmNum) +
            Number(C2.threeLevelAlarmNum)
        ]
        // 已处理数
        this.callThePolice.bar1[1].data = [
          Number(B1.handledOneLevelAlarmNum) +
            Number(B1.handledTwoLevelAlarmNum) +
            Number(B1.handledThreeLevelAlarmNum),
          Number(C2.handledOneLevelAlarmNum) +
            Number(C2.handledTwoLevelAlarmNum) +
            Number(C2.handledThreeLevelAlarmNum)
        ]
        // 未处理数
        this.callThePolice.bar1[2].data = [
          Number(B1.unhandledOneLevelAlarmNum) +
            Number(B1.unhandledTwoLevelAlarmNum) +
            Number(B1.unhandledThreeLevelAlarmNum),
          Number(C2.unhandledOneLevelAlarmNum) +
            Number(C2.unhandledTwoLevelAlarmNum) +
            Number(C2.unhandledThreeLevelAlarmNum)
        ]
      })
    },
    //报警数量统计---柱状图(昨日)
    async getCallThePolice_day() {
      let params_Info = {
        startTime:
          this.$moment(this.cDate)
            .subtract(1, 'day')
            .format('yyyy-MM-DD') + ' 00:00:00',
        endTime:
          this.$moment(this.cDate)
            .subtract(1, 'day')
            .format('yyyy-MM-DD') + ' 23:59:59'
      }
      post(getFactoryMainInfo, params_Info).then(res => {
        const B1 = res.data.find(item => item.code == 31)
        const C2 = res.data.find(item => item.code == 34)
        // 报警总数
        this.callThePolice_day.bar1[0].data = [
          Number(B1.handledOneLevelAlarmNum) +
            Number(B1.twoLevelAlarmNum) +
            Number(B1.threeLevelAlarmNum),
          Number(C2.handledOneLevelAlarmNum) +
            Number(C2.twoLevelAlarmNum) +
            Number(C2.threeLevelAlarmNum)
        ]
        // 已处理数
        this.callThePolice_day.bar1[1].data = [
          Number(B1.handledOneLevelAlarmNum) +
            Number(B1.handledTwoLevelAlarmNum) +
            Number(B1.handledThreeLevelAlarmNum),
          Number(C2.handledOneLevelAlarmNum) +
            Number(C2.handledTwoLevelAlarmNum) +
            Number(C2.handledThreeLevelAlarmNum)
        ]
        // 未处理数
        this.callThePolice_day.bar1[2].data = [
          Number(B1.unhandledOneLevelAlarmNum) +
            Number(B1.unhandledTwoLevelAlarmNum) +
            Number(B1.unhandledThreeLevelAlarmNum),
          Number(C2.unhandledOneLevelAlarmNum) +
            Number(C2.unhandledTwoLevelAlarmNum) +
            Number(C2.unhandledThreeLevelAlarmNum)
        ]
      })
    },
    //报警数量统计点击柱
    setStartTimeEndTime(unit, subtractValue = 0) {
      console.log('点击柱')
      const momentObj =
        subtractValue > 0
          ? this.$moment(this.cDate).subtract(subtractValue, unit)
          : this.$moment(this.cDate)
      this.beginTime = momentObj.startOf(unit).format('YYYY-MM-DD 00:00:00')
      this.endTime = momentObj.endOf(unit).format('YYYY-MM-DD 23:59:59')
    },
    // 当月
    reportHandlerChild(msg) {
      this.setStartTimeEndTime('month')
      this.treeCodes = msg.month === '第一炼钢厂' ? 31 : 34
      this.title =
        msg.month === '第一炼钢厂' ? '当月-第一炼钢厂' : '当月-宽厚板厂'
      this.getAlarmDetails()
    },
    // 昨日
    reportHandlerChild_day(msg) {
      this.setStartTimeEndTime('day', 1)
      this.treeCodes = msg.month === '第一炼钢厂' ? 31 : 34
      this.title =
        msg.month === '第一炼钢厂' ? '昨日-第一炼钢厂' : '昨日-宽厚板厂'
      this.getAlarmDetails()
    },

    //报警明细
    async getAlarmDetails() {
      console.log('---------------')
      const params1 = {
        startTime: '',
        endTime: '',
        // startTime: this.beginTime,
        // endTime: this.endTime,
        pageSize: 9999,
        state: [],
        treeCode: '31',
        deviceCode: '',
        pageIndex: 1,
        alarmInfo: ''
      }
      const params2 = {
        startTime: '',
        endTime: '',
        // startTime: this.beginTime,
        // endTime: this.endTime,
        pageSize: 9999,
        state: [],
        treeCode: '34',
        deviceCode: '',
        pageIndex: 1,
        alarmInfo: ''
      }
      const params3 = {
        startTime: '',
        endTime: '',
        // startTime: this.beginTime,
        // endTime: this.endTime,
        pageSize: 9999,
        state: [],
        treeCode: '32',
        deviceCode: '',
        pageIndex: 1,
        alarmInfo: ''
      }
      //中板厂
      const params4 = {
        startTime: '',
        endTime: '',
        // startTime: this.beginTime,
        // endTime: this.endTime,
        pageSize: 9999,
        state: [],
        treeCode: '33',
        deviceCode: '',
        pageIndex: 1,
        alarmInfo: ''
      }
      //金石材料
      const params5 = {
        startTime: '',
        endTime: '',
        // startTime: this.beginTime,
        // endTime: this.endTime,
        pageSize: 9999,
        state: [],
        treeCode: '37',
        deviceCode: '',
        pageIndex: 1,
        alarmInfo: ''
      }
      //金润
      const params6 = {
        startTime: '',
        endTime: '',
        // startTime: this.beginTime,
        // endTime: this.endTime,
        pageSize: 9999,
        state: [],
        treeCode: '38',
        deviceCode: '',
        pageIndex: 1,
        alarmInfo: ''
      }
      let data1 = []
      let data2 = []
      let data3 = []
      let data11 = []
      let data12 = []
      let data13 = []
      let data21 = []
      let data22 = []
      let data23 = []
      let data31 = []
      let data32 = []
      let data33 = []
      let data41 = []
      let data42 = []
      let data43 = []
      let data51 = []
      let data52 = []
      let data53 = []
      let res1 = await post(alarmManagement, { ...params1 })
      let res2 = await post(alarmManagement, { ...params2 })
      let res3 = await post(alarmManagement, { ...params3 })
      let res4 = await post(alarmManagement, { ...params4 })
      let res5 = await post(alarmManagement, { ...params5 })
      let res6 = await post(alarmManagement, { ...params6 })
      //1处理中  3已处理
      //   const mergedArray = [... res1.data.tlist,... res2.data.tlist,... res3.data.tlist,... res4.data.tlist,... res5.data.tlist,... res6.data.tlist]
      res1.data.tlist.map(item => {
        if (item.state == 1) {
          data1.push(item)
        } else if (item.state == 3) {
          data2.push(item)
        } else if (item.state == 0) {
          data3.push(item)
        }
      })
      res2.data.tlist.map(item => {
        if (item.state == 1) {
          data11.push(item)
        } else if (item.state == 3) {
          data12.push(item)
        } else if (item.state == 0) {
          data13.push(item)
        }
      })
      // console.log('aaaaa',mergedArray);
      res3.data.tlist.map(item => {
        if (item.state == 1) {
          data21.push(item)
        } else if (item.state == 3) {
          data22.push(item)
        } else if (item.state == 0) {
          data23.push(item)
        }
      })
      res4.data.tlist.map(item => {
        if (item.state == 1) {
          data31.push(item)
        } else if (item.state == 3) {
          data32.push(item)
        } else if (item.state == 0) {
          data33.push(item)
        }
      })
      res5.data.tlist.map(item => {
        if (item.state == 1) {
          data41.push(item)
        } else if (item.state == 3) {
          data42.push(item)
        } else if (item.state == 0) {
          data43.push(item)
        }
      })
      res6.data.tlist.map(item => {
        if (item.state == 1) {
          data51.push(item)
        } else if (item.state == 3) {
          data52.push(item)
        } else if (item.state == 0) {
          data53.push(item)
        }
      })
      this.callThePolice.bar1[2].data = [
        data1.length,
        data11.length,
        data21.length,
        data31.length,
        data41.length,
        data51.length
      ]
      this.callThePolice.bar1[1].data = [
        data3.length,
        data13.length,
        data23.length,
        data33.length,
        data43.length,
        data53.length
      ]
      this.callThePolice.bar1[0].data = [
        data2.length,
        data12.length,
        data22.length,
        data32.length,
        data42.length,
        data52.length
      ]
      //未处理
      //   console.log('处理中', data1.length)
      //处理
      //   console.log('已处理', data2.length)
      //   console.log('3333', data3.length)
      //已处理
      //   console.log('未处理', data3.length)
      //   post(alarmManagement, params).then(res => {
      //     let data1 = []
      //     let data2 = []
      //     let data3 = []
      //     let data4 = []
      //     this.dataList_1 = res.data.tlist
      //     this.dataList_1.map(item => {
      //       if (item.status == 3) {
      //         data4.push(item)
      //       } else if (item.status == 2) {
      //         data3.push(item)
      //       } else if (item.status == 1) {
      //         data2.push(item)
      //       } else if (item.status == 0) {
      //         data1.push(item)
      //       }
      //     })
      //     this.alarmDataX = []
      //     this.alarmData = [
      //       //报警
      //       {
      //         name: '未处理',
      //         type: 'bar',
      //         yAxisIndex: 0,
      //         barGap: 0,
      //         data: this.dataList_1.map(item => item.NOMAL)
      //       },
      //       {
      //         name: '已处理',
      //         type: 'bar',
      //         yAxisIndex: 0,
      //         barGap: 0,
      //         data: this.dataList_1.map(item => item.BIGGER)
      //       },
      //       {
      //         name: '处理中',
      //         type: 'bar',
      //         yAxisIndex: 0,
      //         barGap: 0,
      //         data: this.dataList_1.map(item => item.MAJOR)
      //       }
      //     ]
      //未处理
      // data1.push(res.data.tlist.find(item => item.status === 0))
      // //处理中
      // data2.push(res.data.tlist.find(item => item.status === 1))
      // //驳回
      // data3.push(res.data.tlist.find(item => item.status === 2))
      // //已处理
      // data4.push(res.data.tlist.find(item => item.status === 3))
      //     console.log('000000', res.data.tlist)
      //     console.log('111', data1.length)
      //     console.log('2222', data2.length)
      //     console.log('3333', data3.length)
      //     console.log('4444', data4.length)
      //   })
    },
    // 恢复默认
    handleSteel() {
      this.treeCodes = ''
      this.setStartTimeEndTime('month')
      this.title = '当月-所有'
      this.getAlarmDetails()
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.alarms {
  display: flex;
}

.alarm-box {
  height: 100%;
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;
    font-size: 0;

    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.green {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }

  .chart {
    flex: 1;
    height: 100%;
    overflow: hidden;
  }
}
</style>
