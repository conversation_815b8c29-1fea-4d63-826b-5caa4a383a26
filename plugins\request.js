import {
  minio_download,
  oneBulletinBoard_exportConstructionDetails,
  oneBulletinCost_downloadCostExcel,
  oneBulletinCost_exportCostDetails
} from '@/api/firstMeeting'

export default function({ $axios, redirect }) {
  $axios.onRequest(config => {
    if (
      config.url.indexOf(minio_download) !== -1 ||
      config.url.indexOf('http://172.25.63.72:9123/') !== -1 ||
      config.url.indexOf('minoApi') !== -1
    ) {
      console.log('blob:', config.url)
      config['responseType'] = 'blob'
    }
    if (config.url.indexOf(oneBulletinBoard_exportConstructionDetails) !== -1) {
      config['responseType'] = 'blob'
    }
    if (config.url.indexOf(oneBulletinCost_downloadCostExcel) !== -1) {
      config['responseType'] = 'blob'
    }
    if (config.url.indexOf(oneBulletinCost_exportCostDetails) !== -1) {
      config['responseType'] = 'blob'
    }
  })
  $axios.onError(error => {
    const code = parseInt(error.response && error.response.status)
    if (code === 400) {
      redirect('/400')
    }
  })
}
