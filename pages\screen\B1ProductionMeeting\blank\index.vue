<template>
  <div class="content">
    <div class="content-item">

      <el-row
        :gutter="32"
        class="full-height"
      >
        <el-col
          :span="12"
          class="full-height"
        >
          <screen-border :title="'库存情况（坯料） ' + searchTime.format('yyyy年MM月')">
            <template v-slot:headerRight>
              <span
                class="screen-btn"
                @click="stock1.detailVisible = true"
              >
                详情
              </span>
            </template>
            <div class="chart-wrapper">
              <div
                class="chart"
              >
                <stock-line-chart
                  :last-month-data="stock1.blankLastMonth || '0'"
                  :month-plan-data="stock1.blankMonthPlan || '0'"
                  :show-legend="true"
                  :chart-data="stock1.bar1"
                  :x-data="stock1.barX"
                />
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="12"
          class="full-height"
        >
          <screen-border :title="'锁定坯'">
            <template v-slot:headerRight />
            <div
              ref="table1"
              class="chart-wrapper"
            >
              <el-table
                v-loading="loading"
                ref="tableTag"
                :data="facTotalList"
                :max-height="facTotalMaxHeight"
                show-summary
                border
              >
                <el-table-column
                  align="center"
                  property="NAME"
                  label="CAD锁定详称"
                />
                <el-table-column
                  align="center"
                  property="DY"
                  label="第一炼钢厂"
                />
                <el-table-column
                  align="center"
                  property="DE"
                  label="第一炼钢厂、中板厂"
                />
                <el-table-column
                  align="center"
                  property="DS"
                  label="技术研发处"
                />
                <el-table-column
                  align="center"
                  property="DF"
                  label="轧钢厂"
                />
                <el-table-column
                  align="center"
                  property="total"
                  label="总计"
                />
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold" />
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height"
      >
        <el-col
          :span="12"
          class="full-height"
        >
          <custom-table
            :title="'CAD库存'"
            :setting="tableObj1.setting"
            :url-list="tableObj1.url.list"
            :url-save="tableObj1.url.save"
            :select-date="selectDate"
            :show-edit="false"
            :table-class="'big-table'"
          />
        </el-col>
        <el-col
          :span="12"
          class="full-height"
        >
          <screen-border title="可用坯料">
            <bars-chart
              :chart-data="blankData2.bar1"
              :x-data="blankData2.bar1X"
              :bar-width="40"
              :show-label="true"
              :show-legend="false"
              :unit="'吨'"
            />
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold" />
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height"
      >
        <el-col
          :span="24"
          class="full-height"
        >
          <screen-border :title="'每日余坯统计'">
            <template v-slot:headerRight />
            <div class="chart-wrapper">
              <div
                class="chart"
              >
                <stock-line-chart
                  :show-legend="true"
                  :chart-data="stock3.bar1"
                  :x-data="stock3.barX"
                />
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>

    <!--坯料详情-->
    <el-dialog
      :visible.sync="stock1.detailVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="坯料详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          坯料库存详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="stock1.gridData"
        show-summary
        border>
        <el-table-column
          align="center"
          property="PLT"
          label=""/>

        <el-table-column
          align="center"
          label="订单坯">
          <el-table-column
            align="center"
            property="DDC_CAN"
            label="CAA(N)"/>
          <el-table-column
            align="center"
            property="DDC_CAY"
            label="CAA(Y)"/>
          <el-table-column
            align="center"
            property="DDC_CAB"
            label="CAB"/>
          <el-table-column
            align="center"
            property="DDC_CAD"
            label="DDC_CAD"/>
          <el-table-column
            align="center"
            property="DDC_TOTAL"
            label="合计"/>
        </el-table-column>

        <el-table-column
          align="center"
          label="余坯">
          <el-table-column
            align="center"
            property="YC_CAC"
            label="CAC"/>
          <el-table-column
            align="center"
            property="YC_CAD"
            label="CAD"/>
          <el-table-column
            align="center"
            property="YC_TOTAL"
            label="合计"/>
        </el-table-column>
        <el-table-column
          align="center"
          label="备坯(CZ)">
          <el-table-column
            align="center"
            property="BP_CAC"
            label="CAC"/>
          <el-table-column
            align="center"
            property="BP_CAD"
            label="CAD"/>
        </el-table-column>
        <el-table-column
          align="center"
          property="ZJ"
          label="总计"/>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/B1ProductionMeeting/component/custom-table'
import {
  availableBillets,
  blankFindAllBySetDate,
  fireCuttingSave,
  totalInventory
} from '@/api/screenC2'
import BarsChart from '@/pages/screen/B1ProductionMeeting/component/bars-chart'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import moment from 'moment'
import { post } from '@/lib/Util'
import {
  findBlankDetail,
  findBlankStock,
  findBlankStockDetails,
  findBlankStockPltZj,
  findBlankSurplus,
  findBoardParameterByDateAndPara,
  findDivDailyRate,
  findEQStatus,
  findWipInventoryTrackingByDate,
  saveBoardParameter,
  saveWipInventoryTracking,
  wipInventoryTrackingTask
} from '@/api/screen'
import { math } from '@/lib/Math'
import StockLineChart from '@/pages/screen/morningMeeting/component/stock-line-chart'

export default {
  name: 'B1Blank',
  components: {
    StockLineChart,
    ScreenBorder,
    BarsChart,
    CustomTable,
    SingleBarsChart
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      totalNum: '',
      // blankData: {
      //   bar1: [
      //     {
      //       name: '实际',
      //       data: []
      //     },
      //     {
      //       name: '预测',
      //       data: []
      //     }
      //   ],
      //   bar1X: ['C1', 'C2', 'C3']
      // },
      // blankData1: {
      //   bar1: [
      //     {
      //       name: '实际',
      //       data: []
      //     },
      //     {
      //       name: '预测',
      //       data: []
      //     }
      //   ],
      //   bar1X: ['厂内', '厂外', '总计']
      // },
      blankData2: {
        bar1: [
          {
            name: '可用坯料',
            data: []
          }
        ],
        bar1X: ['C1', 'C2', 'C3', '总计']
      },
      tableObj1: {
        url: {
          save: fireCuttingSave,
          list: blankFindAllBySetDate
        },
        setting: [
          {
            keyQuery: 'classification',
            keySave: 'classification',
            label: 'CAD库存'
          },
          {
            keyQuery: 'day',
            keySave: 'day',
            label: '昨日（吨）'
          }
        ]
      },
      wipInventoryTrackingTask: wipInventoryTrackingTask,
      stock1: {
        bar1: [],
        barX: [],
        gridData: [],
        dialogVisible: false,
        detailVisible: false,
        blankMonthPlan: '',
        blankLastMonth: ''
      },
      stock2: {
        bar1: [],
        barX: [],
        gridData: [],
        detailVisible: false,
        processedMonthPlan: '',
        processedLastMonth: ''
      },
      stock3: {
        bar1: [],
        barX: []
      },
      facTotalList: [],
      facTotalMaxHeight: null,
      formInline: {
        processedMonthPlan: '',
        processedLastMonth: '',
        blankMonthPlan: '',
        blankLastMonth: ''
      },
      processed: {
        bar1: [],
        bar2: [],
        barX: [],
        gridData: [],
        gridMerge: [],
        dialogVisible: false,
        output: 0,
        targetSchedule: 0,
        percent: 0
      },
      divDaily: {
        bar1: [],
        bar2: [],
        barX: []
      }
    }
  },
  computed: {
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'day')
        .format('yyyyMMDD')
    },
    searchTime: function() {
      return this.$moment(this.cDate).subtract(2, 'day')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.loadData()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.loadData()
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    loadData() {
      this.getInventory()
      this.getDetail()
      this.getZZP()
      this.getBlankSurplus()
      this.getProcessed()
    },
    // 坯料库存
    getInventory(date1, date2) {
      post(totalInventory, {}).then(res => {
        // this.blankData.bar1[0].data = [res.C1.T2, res.C2.T2, res.C3.T2]
        // this.blankData.bar1[1].data = [res.C1.T3, res.C2.T3, res.C3.T3]
        // this.totalNum = (
        //   res.C1.T2 +
        //   res.C2.T2 +
        //   res.C3.T2 +
        //   res['厂外'].T2
        // ).toFixed(2)
        // this.blankData1.bar1[0].data = [
        //   (res.C1.T2 + res.C2.T2 + res.C3.T2).toFixed(2),
        //   res['厂外'].T2,
        //   this.totalNum
        // ]
        // this.blankData1.bar1[1].data = [
        //   (res.C1.T3 + res.C2.T3 + res.C3.T2).toFixed(2),
        //   res['厂外'].T3,
        //   (res.C1.T3 + res.C2.T3 + res.C3.T2 + res['厂外'].T3).toFixed(2)
        // ]
      })
      post(availableBillets, { date: this.prevDate }).then(res => {
        this.blankData2.bar1[0].data = [res.C1, res.C2, res.C3, res.ZJ]
      })
    },
    getDetail() {
      post(findBlankStockDetails, {
        date: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyyMMDD')
      }).then(res => {
        this.stock1.gridData = res.data
      })
    },
    calculateHeight() {
      this.facTotalMaxHeight = this.$refs.table1.offsetHeight
    },
    async getZZP() {
      const monthStart = this.$moment(this.cDate).format('D') == 2
      // 参数
      const parameters = await post(findBoardParameterByDateAndPara, {
        setDate: this.searchTime.format('yyyy-MM')
      })
      this.formInline.blankMonthPlan = this.stock1.blankMonthPlan = this.getParam(
        'blankMonthPlan',
        parameters.data
      )
      this.formInline.blankLastMonth = this.stock1.blankLastMonth = this.getParam(
        'blankLastMonth',
        parameters.data
      )
      // 坯料
      const zrStock = await post(findBlankStock, {
        date: this.searchTime.format('yyyyMM')
      })
      const zrStock2 = monthStart
        ? await post(findBlankStock, {
            date: this.$moment(this.cDate).format('yyyyMM')
          })
        : { data: [] }
      const stockZj = await post(findBlankStockPltZj, {
        date: this.searchTime.format('yyyyMM')
      })
      const stockZj2 = monthStart
        ? await post(findBlankStockPltZj, {
            date: this.$moment(this.cDate).format('yyyyMM')
          })
        : { data: [] }
      this.stock1.barX = zrStock.data
        .concat(zrStock2.data)
        .filter(item => item.PLT === 'C1')
        .map(item => item.PLCK_DATE.substr(6, 2))
      this.stock1.bar1 = [
        {
          name: '总计',
          data: zrStock.data
            .concat(zrStock2.data)
            .filter(item => item.PLT === 'C1')
            .map(item => {
              const match = stockZj.data
                .concat(stockZj2.data)
                .find(zj => zj.PLCK_DATE === item.PLCK_DATE)
              return match ? parseInt(match['总计']) : 0
            })
        },
        {
          name: 'C1',
          data: zrStock.data
            .concat(zrStock2.data)
            .filter(item => item.PLT === 'C1')
            .map(item => parseInt(item.ZJ))
        },
        {
          name: 'C2',
          data: zrStock.data
            .concat(zrStock2.data)
            .filter(item => item.PLT === 'C2')
            .map(item => parseInt(item.ZJ))
        },
        {
          name: 'C3',
          data: zrStock.data
            .concat(zrStock2.data)
            .filter(item => item.PLT === 'C3')
            .map(item => parseInt(item.ZJ))
        },
        {
          name: 'CAD',
          data: zrStock.data
            .concat(zrStock2.data)
            .filter(item => item.PLT === 'C1')
            .map(item => {
              const match = stockZj.data
                .concat(stockZj2.data)
                .find(zj => zj.PLCK_DATE === item.PLCK_DATE)
              return match ? parseInt(match['CAD']) : 0
            })
        },
        {
          name: 'CAC',
          data: zrStock.data
            .concat(zrStock2.data)
            .filter(item => item.PLT === 'C1')
            .map(item => {
              const match = stockZj.data
                .concat(stockZj2.data)
                .find(zj => zj.PLCK_DATE === item.PLCK_DATE)
              return match ? parseInt(match['CAC']) : 0
            })
        }
      ]
      const facTotal = await post(findBlankDetail, {})
      this.facTotalList = facTotal.data.map(item => {
        item.total = math.add(
          item.DF || 0,
          item.DE || 0,
          item.DS || 0,
          item.DY || 0
        )
        return item
      })
      this.$nextTick(() => {
        this.$refs.tableTag.doLayout()
      })
    },
    async getBlankSurplus() {
      const monthStart = this.$moment(this.cDate).format('D') == 2
      console.log('======', monthStart)
      const blankSurplus = monthStart
        ? await post(findBlankSurplus, {
            date: this.$moment(this.cDate).format('yyyyMM')
          })
        : { data: [] }
      post(findBlankSurplus, {
        date: this.searchTime.format('yyyyMM')
      }).then(res => {
        this.stock3.barX = res.data
          .concat(blankSurplus.data)
          .map(item => item.SETDATE.substr(6, 2))
        this.stock3.bar1 = [
          {
            name: '当前余坯量',
            data: res.data.concat(blankSurplus.data).map(item => item.SCWGT)
          },
          {
            name: '当日消化量',
            data: res.data.concat(blankSurplus.data).map(item => item.UPWGT)
          },
          {
            name: '当日余坯产生量',
            data: res.data.concat(blankSurplus.data).map(item => item.CCWGT)
          },
          {
            name: '当日非计划余坯产生量',
            data: res.data.concat(blankSurplus.data).map(item => item.LGWGT)
          }
        ]
      })
    },
    getProcessed() {
      post(findWipInventoryTrackingByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.processed.gridData = res.data.map(item => {
          return {
            factoryType: item.factorytype,
            productionProcess: item.productionprocess,
            processName: item.productionprocess
              ? item.productionprocess.split('/')[0]
              : '',
            unit: item.productionprocess
              ? item.productionprocess.split('/')[1]
              : '吨',
            targetLine: item.targetline,
            inventory: item.inventory,
            yesterdayDisposal: item.yesterdaydisposal,
            qualify: item.qualify,
            description: item.description,
            yesterdayBlocks: item.yesterdayblocks,
            setDate: item.setdate
          }
        })
        this.processed.barX = this.processed.gridData.map(
          item =>
            item.factoryType +
            item.productionProcess +
            '\n当前库存：' +
            item.inventory
        )
        this.processed.bar1 = this.processed.gridData.map(item => {
          if (item.unit === '吨') {
            return {
              value: item.yesterdayDisposal,
              plan: item.targetLine,
              unit: item.unit,
              piece: item.yesterdayBlocks, // 块数
              show: true,
              finished: item.qualify !== '否',
              description: item.description
            }
          } else {
            return {
              value: 0,
              plan: 0,
              pieceValue: item.yesterdayDisposal,
              piecePlan: item.targetLine,
              show: false,
              unit: item.unit,
              description: item.description
            }
          }
        })
        this.processed.bar2 = this.processed.gridData.map(item => {
          if (item.unit === '块') {
            return {
              value: item.yesterdayDisposal,
              plan: item.targetLine,
              unit: item.unit,
              show: true,
              finished: item.qualify !== '否',
              description: item.description
            }
          } else {
            return {
              value: 0,
              plan: 0,
              show: false,
              unit: item.unit
            }
          }
        })
      })
    },
    getParam(name, list) {
      const match = list.find(item => item.parameter === name)
      return match ? match.content : null
    },
    async getEquipStatus(list) {
      //柱状图横坐标
      let seriesDatum = []
      //顶部圆形图案个数
      let symbolArray = []
      //顶部圆形图案颜色
      let symbolColors = []
      let markPointDatum = []
      list.forEach(item => {
        seriesDatum.push(item.RATE.slice(0, -1))
      })
      console.log('seriesDatum', seriesDatum)
      this.divDaily.data = []
      let res = await post(findEQStatus, {})
      if (res.success && res.data !== null) {
        res.data.forEach((val, index) => {
          list.forEach((val2, index2) => {
            if (val.EQUIP_NO === val2.EQUIP_NO) {
              list[index2].status = res.data[index].STATUS
            }
          })
        })
        console.log('list', list)
      }
      list.forEach((item, ind) => {
        if (item.status !== null || item.status !== undefined) {
          if (item.status === true) {
            symbolColors[ind] = '#19BE6B'
          } else if (item.status === false) {
            symbolColors[ind] = '#FF2855'
          } else {
            symbolColors[ind] = '#8b8589' //透明色#ffffff00
          }
        }
      })
      console.log('颜色symbolColors', symbolColors)
      for (var i = 0; i < seriesDatum.length; i++) {
        symbolArray[i] = 'circle'
        let _obj = {
          symbol: symbolArray[i],
          symbolSize: [10, 10],
          symbolRotate: 0,
          symbolOffset: ['0', -28],
          coord: [i, seriesDatum[i] + 10],
          // value: seriesDatum[i] - 20,
          label: {
            show: true,
            color: '#000',
            fontSize: 12,
            position: 'right'
          },
          itemStyle: {
            borderWidth: 3,
            borderColor: symbolColors[i],
            color: symbolColors[i]
          }
        }
        markPointDatum.push(_obj)
        this.divDaily.bar2 = markPointDatum
      }
    }
  }
}
</script>

<style
  scoped
  lang="less"
>
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }

  .t-text {
    position: absolute;
    top: 0;
    left: 0;
  }

  .relative {
    position: relative;
    font-size: 22px;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
