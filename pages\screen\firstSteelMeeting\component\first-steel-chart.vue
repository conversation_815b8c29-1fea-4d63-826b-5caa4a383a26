<template>
  <div class="e-chart">
    <div
      v-if="title.length!==0"
      class="e-chart-title">{{ title }}</div>
    <div
      :id="containerId"
      class="e-chart-div"/>
  </div>

</template>

<script>
export default {
  name: 'first-steel-chart',
  props: {
    title: {
      type: String,
      default: ''
    },
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 0
    },
    unit: {
      type: String,
      default: ''
    },
    unit1: {
      type: String,
      default: '%'
    },
    tooltipFormatter: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
    window.addEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
      }
      const options = {
        color: this.color,
        tooltip: {
          show: this.showToolbox,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          padding: 10
        },
        legend: {
          // show: this.showLegend,
          align: 'left',
          top: 5,
          right: 2,
          padding: 0,
          // icon: 'circle',
          textStyle: {
            color: '#C9E1FDF2',
            fontSize: 12
          },
          // itemHeight: 10, // 修改icon图形大小
          // itemWidth: 10, // 修改icon图形大小
          // itemGap: 10, // 修改间距
          itemStyle: {
            borderWidth: 0,
            padding: 0
          }
        },
        grid: {
          top: '16%',
          left: '0%',
          right: '1%',
          bottom: '1%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: this.xData,
            nameTextStyle: {
              color: '#d2d2d2'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              interval: 0,
              rotate: this.labelRotate || 0
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            }
          }
        ],
        yAxis: [
          {
            name: this.unit.length !== 0 ? this.unit : '',
            type: 'value',
            // minInterval: 1,
            axisLine: {
              show: false
            },
            nameTextStyle: {
              color: '#d2d2d2'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'right'
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        series: this.chartData.map(item => ({
          name: item.name,
          type: item.type,
          lineStyle: item.lineStyle,
          barGap: item.barGap,
          label: item.label,
          smooth: item.smooth,
          barWidth: this.barWidth || null,
          yAxisIndex: item.yAxisIndex || 0,
          data: item.data,
          markLine: item.markLine
        }))
      }
      if (this.tooltipFormatter) {
        options.tooltip.formatter = params => {
          // console.log('params', params)
          const param = params[0]
          const data = param.data
          return (
            param.name +
            '<br/>' +
            param.marker +
            `${param.seriesName}\xa0\xa0\xa0\xa0${param.value}` +
            '<br/>' +
            `\xa0\xa0\xa0日期\xa0\xa0\xa0\xa0${data.time}` +
            '<br/>' +
            `\xa0\xa0\xa0总炉数\xa0\xa0\xa0\xa0${data.count}` +
            '<br/>' +
            `\xa0\xa0\xa0低于20000炉数\xa0\xa0\xa0\xa0${data.exCount}`
          )
        }
      }
      this.myChart.setOption(options)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.e-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.e-chart-title {
  font-size: 14px;
  font-weight: bolder;
  color: #ffffff;
  line-height: 20px;
}
.e-chart-title::before {
  content: '1';
  color: #ffffff;
  background: #ffffff;
  width: 6px;
  height: 100%;
  margin-right: 4px;
}
.e-chart-div {
  flex: 1;
}
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
