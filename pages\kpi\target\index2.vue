<template>
  <div class="page-content shadow-light">
    <div class="page-operate">
      <!--      <div>-->
      <!--        <el-button-->
      <!--          v-command="'/kpi/indicators/add'"-->
      <!--          icon="el-icon-circle-plus-outline"-->
      <!--          size="small"-->
      <!--          type="success"-->
      <!--          @click="handleAdd"-->
      <!--        >新增-->
      <!--        </el-button>-->
      <!--      </div>-->
      <div>
        <el-tooltip
          content="密度"
          placement="top"
        >
          <span class="operate-icon">
            <el-dropdown
              slot="刷新"
              placement="bottom"
              trigger="click"
              @command="changeTableSize"
            >
              <span class="el-dropdown-link">
                <i class="el-icon-menu" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="medium">宽松</el-dropdown-item>
                <el-dropdown-item command="small">默认</el-dropdown-item>
                <el-dropdown-item command="mini">紧凑</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span>
        </el-tooltip>
        <el-tooltip
          content="刷新"
          placement="top"
        >
          <span
            class="operate-icon"
            @click="handleSearch"
          >
            <i class="el-icon-refresh" />
          </span>
        </el-tooltip>
      </div>
    </div>
    <el-table
      v-loading="loading"
      :data="tableData"
      :size="size"
      border
      style="width: 100%"
    >
      <el-table-column
        label="序号"
        type="index"
        width="50"
      />
      <el-table-column
        label="指标ID"
        prop="kid"
        width="80"
      />
      <el-table-column
        label="指标名称"
        prop="kpiName"
        min-width="80"
      />
      <el-table-column
        label="规则类型"
        prop="ruleSign"
        min-width="100"
      >
        <template
          v-slot="{row}"
        >
          {{ getValue(ruleType, row.ruleSign).label }}
        </template>
      </el-table-column>
      <el-table-column
        label="预警逻辑"
        prop="logic"
        width="100"
      >
        <template
          v-slot="{row}"
        >
          {{ getValue(earlyWarningLogic, row.logic).label }}
        </template>
      </el-table-column>
      <el-table-column
        label="预警规则"
        prop="rule"
        width="120"
      >
        <template
          v-slot="{row}"
        >
          {{ getValue(earlyWarningRule, row.rule).label }}
        </template>
      </el-table-column>
      <el-table-column
        label="预警参数"
        prop="warningParam"
        width="100"
      >
        <template
          v-slot="{row}"
        >
          {{ row.warningParam }}
        </template>
      </el-table-column>
      <el-table-column
        label="比较值说明"
        prop="remarks"
        min-width="100"
      >
        <template
          v-slot="{row}"
        >
          {{ row.remarks }}
        </template>
      </el-table-column>
      <el-table-column
        label="目标值读取方式"
        prop="targetGetType"
        min-width="100"
      >
        <template
          v-slot="{row}"
        >
          {{ getValue(targetGetType, row.targetGetType).label }}
        </template>
      </el-table-column>
      <el-table-column
        label="关联目标ID"
        prop="linkedKpiId"
        width="100"
      >
        <template
          v-slot="{row}"
        >
          {{ row.linkedKpiId }}
        </template>
      </el-table-column>
      <el-table-column
        label="基础目标值"
        prop="targetValue"
        width="100"
      >
        <template
          v-slot="{row, $index}"
        >
          <div v-if="row.edit">
            <el-tooltip
              :content="`上月目标值：${ row.lastMonth }`"
              :value="row.lastMonth != null"
              class="item" 
              effect="dark"
              placement="top">
              <el-input
                v-model="row.targetValue"
                :style="{width: '100%'}"
                clearable
                placeholder="请输入目标值"
                @blur="updateRule($index)"
              />
            </el-tooltip>
          </div>
          <template v-else>
            <div
              class="num-con"
              @click="editTable(row, $index)">{{ row.targetValue }}</div>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="目标值倍数"
        prop="multiple"
        width="100"
      >
        <template
          v-slot="{row, $index}"
        >
          <div v-if="row.edit">
            <el-input
              v-model="row.multiple"
              :style="{width: '100%'}"
              clearable
              placeholder="请输入目标值"
              @blur="updateRule($index)"
            />
          </div>
          <template v-else>
            <div
              class="num-con"
              @click="editTable($index)">{{ row.multiple }}</div>
          </template>
        </template>
      </el-table-column>
      <!--      <el-table-column-->
      <!--        fixed="right"-->
      <!--        label="操作"-->
      <!--        width="120"-->
      <!--      >-->
      <!--        <template-->
      <!--          v-slot="{row}"-->
      <!--        >-->
      <!--          <span v-command="'/kpi/indicators/edit'">-->
      <!--            <el-button-->
      <!--              size="small"-->
      <!--              type="text"-->
      <!--              @click="handleEdit(row)"-->
      <!--            >编辑-->
      <!--            </el-button>-->
      <!--          </span>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
    <el-row
      align="middle"
      class="table-pagination"
      justify="end"
      type="flex"
    >
      <el-pagination
        :current-page="page.page"
        :page-size="page.size"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <Edit
        ref="modalForm"
        @success="handleSearch"
      />
    </el-row>
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import { post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'
import {
  findRulesByManagerId,
  findTargetValueByRid,
  updateKpiWarningRules
} from '@/api/kpi'
export default {
  name: 'kpi-target',
  components: {
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: findRulesByManagerId //分页接口地址
      },
      searchForm: {
        targetManagerId: '123456'
      },
      ruleType: ENUM.ruleType,
      earlyWarningLogic: ENUM.earlyWarningLogic,
      earlyWarningRule: ENUM.earlyWarningRule,
      targetGetType: ENUM.targetGetType,
      statusList: [
        {
          value: 0,
          label: '正常',
          type: 'success'
        },
        {
          value: 1,
          label: '废弃',
          type: 'warning'
        }
      ]
    }
  },
  watch: {},
  created() {},
  methods: {
    getValue: function(list = [], value) {
      return list.find(item => item.value == value)
        ? list.find(item => item.value == value)
        : {}
    },
    async editTable(row, index) {
      console.log(index)
      if (row.targetGetType !== 3) return
      const item = this.tableData[index]
      item.edit = true
      const { data } = await post(findTargetValueByRid, { rid: row.id })
      item.lastMonth = data || null
      this.tableData.splice(index, 1, item)
    },
    afterHandleSearch(data) {
      this.tableData = data.map(item => {
        return Object.assign({}, item, {
          edit: false
        })
      })
    },
    updateRule(index) {
      post(updateKpiWarningRules, this.tableData[index]).then(res => {
        if (!res.success) {
          console.warn(res)
        } else {
          this.tableData[index].edit = false
        }
      })
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.page-title {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  margin-bottom: 15px;
}

.page-content {
  height: 100%;
  overflow: auto;
  font-size: 18px;
  padding: 20px;
  background: #fff;
}
.page-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .operate-icon {
    margin-left: 8px;
  }
}

.table-pagination {
  margin-top: 20px;
  .el-form-item--small.el-form-item {
    margin-bottom: 12px;
  }
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}
.tree-wrapper {
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.first-node {
  font-size: 18px;
}
/deep/ .el-tree-node {
  margin: 5px 0;
}
/deep/ .el-tree > .el-tree-node {
  margin: 15px 0 12px;
}
.contextmenu {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  background: #fff;
  font-size: 14px;
  color: #666;
  border: 1px solid #e9e9e9;
  min-width: 250px;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
  li {
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #f4f4f5;
    }
  }
  li:last-child {
    border-top: 1px solid #e9e9e9;
  }
}
.num-con {
  min-height: 23px;
  cursor: text;
}
</style>
