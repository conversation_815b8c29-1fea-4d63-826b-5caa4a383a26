<template>
  <div class="e-chart">
    <div
      :id="containerId"
      class="e-chart-div"/>
  </div>

</template>

<script>
export default {
  name: 'first-steel-pie',
  props: {
    title: {
      type: String,
      default: ''
    },
    titleNum: {
      type: String,
      default: ''
    },
    vertical: {
      type: Boolean,
      default: true
    },
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: <PERSON>olean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 0
    },
    unit: {
      type: String,
      default: '吨'
    },
    unit1: {
      type: String,
      default: '%'
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
    window.addEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
        this.myChart.group = 'group1'
      }
      const options = {
        color: this.color,
        title: {
          show: !!this.title,
          text: '{tit|' + this.title + '}',
          subtext: '{stit|' + this.titleNum + '}\n\n {sunit|万元}',
          left: this.vertical ? '50%' : '28%',
          top: this.vertical ? '35%' : '40%',
          textAlign: 'center',
          padding: 0,
          textStyle: {
            fontSize: 23,
            rich: {
              tit: {
                borderColor: 'transparent',
                borderWidth: 12,
                borderRadius: 4,
                color: '#fff',
                fontSize: '16px'
              }
            }
          },
          subtextStyle: {
            rich: {
              stit: {
                borderColor: 'transparent',
                fontSize: '32px',
                lineHeight: 0,
                fontWeight: 'bolder',
                color: '#FF2855'
              },
              sunit: {
                color: '#fff',
                marginTop: '10px'
              }
            }
          }
        },
        tooltip: {
          show: this.showToolbox,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          padding: 10
        },
        legend: {
          show: this.showLegend,
          align: 'left',
          top: 5,
          right: 2,
          padding: 0,
          // icon: 'circle',
          textStyle: {
            color: '#C9E1FDF2',
            fontSize: 12
          },
          // itemHeight: 10, // 修改icon图形大小
          // itemWidth: 10, // 修改icon图形大小
          // itemGap: 10, // 修改间距
          itemStyle: {
            borderWidth: 0,
            padding: 0
          }
        },
        grid: {
          top: '16%',
          left: '0%',
          right: '1%',
          bottom: '1%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: this.xData,
            nameTextStyle: {
              color: '#d2d2d2'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              interval: 0,
              rotate: this.labelRotate || 0
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            minInterval: 1,
            axisLine: {
              show: false
            },
            nameTextStyle: {
              color: '#d2d2d2'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'right'
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        series: this.chartData.map(item => ({
          name: item.name,
          type: item.type,
          radius: item.radius,
          smooth: item.smooth,
          barWidth: this.barWidth || null,
          yAxisIndex: item.yAxisIndex || 0,
          data: item.data,
          labelLine: {
            length: 30
          },
          label: {
            formatter: '{b|{b}}\n  {c|{c}}  {per|万元}  ',
            // backgroundColor: '#F6F8FC',
            // borderColor: '#8C8D8E',
            // borderWidth: 1,
            borderRadius: 4,
            rich: {
              a: {
                color: '#6E7079',
                lineHeight: 22,
                align: 'center'
              },
              b: {
                color: '#ffffff',
                fontSize: 16,
                fontWeight: 'bold',
                lineHeight: 33
              },
              c: {
                color: '#FF2855',
                fontSize: '20px',
                fontWeight: 'bolder'
              },
              per: {
                color: '#ffffff',
                fontSize: '14px',
                padding: [3, 4],
                borderRadius: 4
              }
            }
          }
        }))
      }
      this.myChart.setOption(options)
      this.$echarts.connect('group1')
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.e-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.e-chart-title {
  font-size: 14px;
  font-weight: bolder;
  color: #ffffff;
  line-height: 20px;
}
.e-chart-title::before {
  content: '1';
  color: #ffffff;
  background: #ffffff;
  width: 6px;
  height: 100%;
  margin-right: 4px;
}
.e-chart-div {
  flex: 1;
}
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
