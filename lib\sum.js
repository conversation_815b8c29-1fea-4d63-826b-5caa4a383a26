export function sumArr(arr) {
  var s = 0
  arr.forEach(function(value) {
    s += value
  })
  return s.toFixed(3)
}
export function getYesterday(date) {
  const oneDay = 24 * 60 * 60 * 1000 // 一天的毫秒数
  const yesterdayMs = date.getTime() - oneDay
  const yesterdayDate = new Date(yesterdayMs)

  const year = String(yesterdayDate.getFullYear()) // 取年份的最后两位
  const month = String(yesterdayDate.getMonth() + 1).padStart(2, '0') // 月份加1，不足两位补0
  const day = String(yesterdayDate.getDate()).padStart(2, '0') // 日期不足两位补0

  return `${year}-${month}-${day}` // 返回格式化的日期字符串
}
