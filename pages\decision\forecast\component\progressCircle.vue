<template>
  <div
    ref="progress"
    :class="getClass"
    class="progress-box">
    <div class="relative">
      <div class="text">
        <span class="leave"><em>{{ progressData.percent || 0 }}</em>%</span>
        <span class="name">{{ name }}</span><br>
        <span class="compare">{{ progressData.resultValue ? progressData.resultValue.toFixed(2) : 0 }}/{{ progressData.targetValue || 0 }}</span><br>
        <span class="description">{{ subName }}</span>
      </div>
      <el-progress
        v-if="show"
        :stroke-width="progressWidth / 15"
        :percentage="(progressData.percent > 100 ? 100 : progressData.percent) || 0"
        :width="progressWidth"
        :format="() => ''"
        :color="getColor"
        :stroke-linecap="'square'"
        type="circle"/>
    </div>
  </div>
</template>

<script>
export default {
  name: 'progress-circle',
  props: {
    progressData: {
      default: function() {
        return {}
      },
      type: Object
    },
    percent: {
      default: 0,
      type: Number
    },
    name: {
      default: '',
      type: String
    },
    subName: {
      default: '',
      type: String
    }
  },
  data: () => {
    return {
      show: false,
      progressWidth: 0
    }
  },
  computed: {
    getColor: function() {
      if (this.progressData.warningStatus) {
        return '#ffb243'
      }
      if (this.progressData.trendStatus) {
        return '#ffb243'
      }
      return '#00b0f0'
    },
    getClass: function() {
      if (this.progressData.warningStatus) {
        return 'warning'
      }
      if (this.progressData.trendStatus) {
        return 'trend'
      }
      return ''
    }
  },
  mounted() {
    console.dir(this.$refs['progress'])
    this.$nextTick(() => {
      this.progressWidth = this.$refs['progress'].offsetWidth
      this.show = true
    })
  },
  methods: {
    getText(percent) {
      return this.text
    }
  }
}
</script>

<style scoped lang="less">
.progress-box {
  position: relative;
  text-align: center;
  .relative {
    display: inline-block;
    position: relative;
  }
  &.warning {
    //.name,
    //.text {
    //  background: linear-gradient(
    //    to right,
    //    rgba(255, 40, 85, 0.7),
    //    rgba(255, 40, 85, 1)
    //  );
    //  border-color: #bb1437;
    //}
    ///deep/ .el-progress--circle .el-progress__text {
    //  color: #bb1437;
    //}
  }
  &.trend {
    //.name,
    //.text {
    //  background: linear-gradient(
    //    to right,
    //    rgba(252, 182, 82, 0.7) 0,
    //    rgba(252, 182, 82, 1)
    //  );
    //  border-color: #ffb243;
    //}
    ///deep/ .el-progress--circle .el-progress__text {
    //  color: #b26f0e;
    //}
  }
  .text {
    position: absolute;
    white-space: nowrap;
    min-width: 140px;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    text-align: center;
    line-height: 22px;
    .leave {
      display: block;
      margin-bottom: 6%;
      font-weight: bold;
      font-size: 18px;
      line-height: 1;
      color: #999;
      em {
        font-size: 36px;
        color: #19be6b;
      }
    }
    .name {
      font-weight: 600;
      font-size: 18px;
      line-height: 1;
      color: #6a74a5;
    }
    .compare {
      font-size: 14px;
      line-height: 20px;
      text-align: center;
      color: #4458fe;
      font-weight: bold;
    }
    .description {
      font-size: 12px;
      line-height: 14px;
      text-align: center;
      color: #6a74a5;
    }
  }
}
/deep/ .el-progress--circle .el-progress__text {
  font-size: 36px !important;
  color: #19be6b;
  font-weight: bold;
  text-shadow: 1px 1px rgba(0, 0, 0, 0.2);
  top: 30%;
}
</style>
