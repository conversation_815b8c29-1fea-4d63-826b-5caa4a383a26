<template>
  <div
    :id="containerId"
    :style="{ height: '100%' }"/>
</template>

<script>
export default {
  name: 'line-chart',
  props: {
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    lastMonthData: {
      type: String,
      default: ''
    },
    monthPlanData: {
      type: String,
      default: ''
    },
    chartData2: {
      type: Array,
      default: () => {
        return []
      }
    },
    chartData3: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array | String,
      default: () => {
        return ['#FFDA35', '#F45549', '#55C6D4', '#3391FF']
      }
    },
    showLegend: {
      type: Boolean,
      default: false
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: <PERSON>olean,
      default: true
    },
    barWidth: {
      type: Number,
      default: 46
    },
    unit: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
    window.addEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
      }
      const options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#57617B'
            }
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        color: this.color,
        grid: {
          top: this.showLegend ? '15%' : '10%',
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        legend: {
          show: this.showLegend,
          right: 30,
          itemHeight: 8, // 修改icon图形大小
          itemWidth: 25, // 修改icon图形大小
          itemGap: 20,
          lineStyle: {
            join: 'bevel'
          },
          textStyle: {
            color: '#9facd5',
            fontSize: 12
          }
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisTick: { show: false },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            },
            data: this.xData
          }
        ],
        yAxis: [
          {
            name: this.unit,
            type: 'value',
            nameTextStyle: {
              color: '#fff'
            },
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#57617B'
              }
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'right'
            },
            splitLine: {
              lineStyle: {
                color: '#2E4262'
              }
            }
          },
          {
            name: '',
            nameTextStyle: {
              color: '#fff'
            },
            type: 'value',
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#57617B'
              }
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'left'
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: this.chartData
          .map(item => {
            // 根据能源类型选择对应的颜色
            let itemColor = this.color

            // 如果color是数组且item.name或value存在能源类型标识
            if (Array.isArray(this.color)) {
              if (
                item.name === '电' ||
                item.name === '电耗' ||
                item.value === 'electricity'
              ) {
                itemColor = '#FFDA35' // 黄色 - 电
              } else if (item.name === '煤气' || item.value === 'gas') {
                itemColor = '#F45549' // 红色 - 煤气
              } else if (
                item.name === '压缩空气' ||
                item.value === 'compressedAir'
              ) {
                itemColor = '#55C6D4' // 青色 - 压缩空气
              } else if (item.name === '水' || item.value === 'water') {
                itemColor = '#3391FF' // 蓝色 - 水
              }
            }

            return {
              name: item.name,
              type: 'line',
              smooth: true,
              lineStyle: {
                normal: {
                  width: 2,
                  color: itemColor
                }
              },
              symbol: 'emptyCircle',
              symbolSize: 6,
              itemStyle: {
                color: itemColor
              },
              data: item.data
            }
          })
          .concat(
            this.chartData2.map(item => {
              return {
                name: item.name,
                yAxisIndex: 0,
                type: 'line',
                smooth: true,
                lineStyle: {
                  normal: {
                    width: 2
                  }
                },
                symbol: 'emptyCircle',
                symbolSize: 6,
                data: item.data
              }
            })
          )
          .concat(
            this.chartData3 && this.chartData3.length >= 1
              ? this.chartData3.map(item => ({
                  name: item.name,
                  yAxisIndex: 0,
                  type: 'line',
                  smooth: true,
                  lineStyle: { normal: { width: 2 } },
                  symbol: 'emptyCircle',
                  symbolSize: 6,
                  data: item.data
                }))
              : []
          )
      }
      this.myChart.setOption(options)
    },
    getColor(item) {
      return !item.show
        ? 'transparent'
        : item.value < item.plan
          ? '#FF2855'
          : '#19BE6B'
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    /**
     * 将数字取整为10的倍数
     * @param {Number} num 需要取整的值
     * @param {Boolean} ceil 是否向上取整
     * @param {Number} prec 需要用0占位的数量
     */
    formatInt(num, prec = 2, ceil = true) {
      const len = String(num).length
      if (len <= prec) {
        return num
      }
      const mult = Math.pow(10, prec)
      return ceil ? Math.ceil(num / mult) * mult : Math.floor(num / mult) * mult
    },

    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
