<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <screen-border title="质量体系">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="showUnfinished2">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div
              ref="tableHeight"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="unfinished2.showGridData"
                :span-method="handleObjectSpan"
                :max-height="tableHeight"
                :format-span-data="unfinished2.showGridData"
                class="font-table center-table"
                border>
                <el-table-column
                  property="QUALITY_TYPES"
                  label="体系审核">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.QUALITY_TYPES)"/>
                  </template>
                </el-table-column>
                <el-table-column
                  property="VALIDATION_EX"
                  label="二方备注">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.VALIDATION_EX)"/>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible.sync="unfinished2.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="质量体系">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished2')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importUnfinishedData2')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview2"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportunfinished2">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveUnfinished2">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          质量体系
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="unfinished2.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="80">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM" />
            </template>
          </el-table-column>
          <el-table-column
            property="QUALITY_TYPES"
            label="体系审核">
            <template v-slot="{ row }">
              <el-input
                v-model="row.QUALITY_TYPES"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="VALIDATION_EX"
            label="二方备注">
            <template v-slot="{ row }">
              <el-input
                v-model="row.VALIDATION_EX"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                class="screen-btn"
                @click="delGridData($index, 'unfinished2')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          class="screen-btn"
          @click="addGridData('unfinished2')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
  
  <script>
import lodash from 'lodash'
import * as _ from 'lodash'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'

import { post } from '@/lib/Util'
import {
  findInspectionPassRateByDate,
  firstMorningMeeting,
  qmsQualityQueryPlateFlaw,
  qmsQualitySavePlateFlaw,
  saveInspectionPassRate
} from '@/api/screen'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart.vue'
import CustomTable from '@/pages/screen/qualityMeeting/component/custom-table.vue'
import ImgView from '@/components/ImgView.vue'

export default {
  name: 'qualityIndex',
  components: { ImgView, CustomTable, SingleBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      tableHeight: null,
      editIndex: 0,
      spanArr2: {},
      flawList: [],
      tableList: [],
      dialogVisible: false,
      flawList1: [],
      tableList1: [],
      dialogVisible1: false,
      flawList2: [],
      tableList2: [],
      dialogVisible2: false,
      resultList: {
        Y: '合格',
        N: '不合格'
      },
      unfinished: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      unfinished2: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      crackRemarks: {
        gridData: [],
        save: true,
        dialogVisible: false
      },
      crackRemarks2: {
        gridData: [],
        save: true,
        dialogVisible: false
      },
      inspectionRemarks: {
        gridData: [],
        save: true,
        dialogVisible: false
      },
      flawUrl: {
        save: qmsQualitySavePlateFlaw,
        list: qmsQualityQueryPlateFlaw
      },
      processed: {
        bar1: [],
        barX1: [],
        barLoading: false,
        dateType1: 0,
        failReason1: '',
        bar11: [],
        dateType11: 0,
        failReason11: '',
        bar2: [],
        barX2: [],
        dateType2: 0,
        failReason2: ''
      },
      flaw: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'incidencetargetvalue',
          keySave: 'incidenceTargetValue',
          label: '发生率目标值'
        },
        {
          keyQuery: 'incidencevalue',
          keySave: 'incidenceValue',
          label: '发生率实际值'
        },
        {
          keyQuery: 'reasonnotcompl',
          keySave: 'reasonNotCompl',
          label: '发生率未完成原因'
        },
        {
          keyQuery: 'changetargetvalue',
          keySave: 'changeTargetValue',
          label: '改判率目标值'
        },
        {
          keyQuery: 'changevalue',
          keySave: 'changeValue',
          label: '改判率实际值'
        },
        {
          keyQuery: 'changenotcompl',
          keySave: 'ChangeNotCompl',
          label: '改判率未完成原因'
        },
        {
          keyQuery: 'lwmonthdata',
          keySave: 'lwMonthData',
          label: '裂纹月数据',
          show: false
        },
        {
          keyQuery: 'gpmonthdata',
          keySave: 'gpMonthData',
          label: '改判月数据',
          show: false
        }
      ],
      detectionUrl: {
        save: saveInspectionPassRate,
        list: findInspectionPassRateByDate
      },
      detection: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'targetvalue',
          keySave: 'targetValue',
          label: '目标值'
        },
        {
          keyQuery: 'value',
          keySave: 'Value',
          label: '实际值'
        },
        {
          keyQuery: 'reasonnotcompl',
          keySave: 'reasonNotCompl',
          label: '未完成原因'
        },
        {
          keyQuery: 'monthdata',
          keySave: 'monthData',
          label: '月数据',
          show: false
        }
      ]
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getUnfinished({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD')
      })
    }
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['PROD_PROCESSES']
    // this.spanArr['PROD_PROCESSES'] = [2, 0, 1]
  },
  mounted() {
    this.tableHeight = this.$refs.tableHeight.offsetHeight
  },
  methods: {
    exportunfinished() {
      const dataName = [
        'SORT_NUM',
        'PROD_PROCESSES',
        'QUALITY_TYPES',
        'A_LIST',
        'B_LIST',
        'C_LIST',
        'D_LIST',
        'NOTES',
        'VALIDATION_EX'
      ]
      const data = [
        {
          SORT_NUM: '序号',
          PROD_PROCESSES: '工序',
          QUALITY_TYPES: '铸坯质量',
          A_LIST: '1#机',
          B_LIST: '2#机',
          C_LIST: '3#机',
          D_LIST: '0#机',
          NOTES: '备注',
          VALIDATION_EX: '异常解释'
        }
      ].concat(
        _.map(_.cloneDeep(this.unfinished.gridData), item => {
          let datas = {}
          _.forEach(dataName, items => {
            datas[items] = item[items]
          })
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `质量指标—铸坯质量（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 质量体系导出
    exportunfinished2() {
      const dataName = ['SORT_NUM', 'QUALITY_TYPES', 'VALIDATION_EX']
      const data = [
        {
          SORT_NUM: '序号',
          QUALITY_TYPES: '项目',
          VALIDATION_EX: '内容'
        }
      ].concat(
        _.map(_.cloneDeep(this.unfinished2.gridData), item => {
          let datas = {}
          _.forEach(dataName, items => {
            datas[items] = item[items]
          })
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `质量指标—质量体系（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 获取数据
    getUnfinished(data) {
      post(firstMorningMeeting.qualityFirstInit, data).then(res => {
        this.formatSpanData(lodash.cloneDeep(res.data[0]))
        this.unfinished2.showGridData = lodash.cloneDeep(res.data[1])
        this.unfinished2.gridData = lodash.cloneDeep(res.data[1])
        this.formatSpanData2(lodash.cloneDeep(res.data[1]))
      })
    },
    // 保存质量体系数据
    saveUnfinished2() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        prodTime: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 1,
        data: lodash.map(
          lodash.sortBy(this.unfinished2.gridData, item => item.SORT_NUM),
          (item, index) => {
            item.PROD_DATE = this.$moment(this.cDate).format('yyyyMMDD')
            item.FLAG = 1
            item.SORT_NUM = index + 1
            return item
          }
        )
      }
      post(firstMorningMeeting.qualityFirst, params).then(res => {
        this.getUnfinished({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD')
        })
        this.unfinished2.dialogVisible = false
        this.loading = false
      })
    },
    // 展示质量体系
    showUnfinished2() {
      this.unfinished2.gridData = lodash.cloneDeep(
        this.unfinished2.showGridData
      )
      this.unfinished2.dialogVisible = true
    },
    // 质量体系导入文件
    handlePreview2(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          QUALITY_TYPES: 'C',
          VALIDATION_EX: 'D'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unfinished2.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    importUnfinishedData2(date) {
      post(firstMorningMeeting.qualityFirstInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD')
      }).then(res => {
        //
        this.loading = false
        this.unfinished2.gridData = lodash.cloneDeep(res.data[1])
        if (!res.data[0].length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 计算需要合并的单元格
    formatSpanData2(data) {
      this.mergeArr.forEach(keyName => {
        this.spanArr2[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr2[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr2[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr2[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr2[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
    },
    httpRequest(params) {}
  }
}
</script>
  
  <style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
    position: relative;
    .operate-box {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}
</style>
