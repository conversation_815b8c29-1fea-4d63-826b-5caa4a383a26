<template>
  <div class="screen-wrapper">
    <div class="screen-header">
      <div class="screen-header-inner">
        <div class="header-left header-side">
          <div class="tab-box">
            <span
              v-for="(item, index) in leftList"
              :key="index"
              :class="{active: active === item.value, disabled: item.disabled}"
              class="header-btn"
              @click="active = item.value">{{ item.name }}</span>
          </div>
        </div>
        <div class="header-title">
          <span class="header-arrow"/>
          <span class="header-arrow header-arrow-right"/>
          <img
            src="../../../assets/images/screen/header-ht.png"
            alt=""
            @drag.prevent
          >
        </div>
        <div class="header-right header-side">
          <div class="tab-box">
            <span
              v-for="(item, index) in rightList"
              :key="index"
              :class="{active: active === item.value, disabled: item.disabled}"
              class="header-btn"
              @click="active = item.value">{{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="screen-content">
      <div v-if="active === 1">
        <contentTitle
          :current-carousel="currentCarousel"
          :content-title-data="contentTitleData"
          @checkLu="checkLu"
        />
        <el-carousel
          ref="carouse"
          :autoplay="carouselFlag"
          :initial-index="0"
          :interval="10000"
          trigger="click"
          height="409px"
          @change="carouselChange" >
          <el-carousel-item>
            <itemlistBox
              :current-carousel="currentCarousel"
              :all-data="topLeftData"
              :right-bottom-data="rightBottomData"
              @openDialog="openDialog"
            />
          </el-carousel-item>
          <el-carousel-item>
            <itemlistBox
              :current-carousel="currentCarousel"
              :all-data="topLeftData"
              :right-bottom-data="rightBottomData"
              @openDialog="openDialog"
            />
          </el-carousel-item>
        </el-carousel>
        <trackinfo/>
      </div>
      <div v-if="active === 2">
        <mesAct/>
      </div>
      <div v-if="active === 4">
        <productionAct/>
      </div>
    </div>
    <el-dialog
      :visible.sync="dialogEchart.dialogVisibleEchart"
      :before-close="handleClose"
      :width="'1100px'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          {{ currentLuNum }} #热处理炉 {{ dialogEchart.title }}
        </div>
      </template>
      <div
        class="dialogDiv">
        <el-row :gutter="20">
          <el-col :span="24">
            <div style="height: 260px;margin-top: 10px" >
              <heatEcharts
                :_height="'100%'"
                :x-axis="dialogEchart.option.xAxis"
                :y-axis="dialogEchart.option.yAxis"
                :legend="dialogEchart.option.legend"
                :tooltip="dialogEchart.option.tooltip"
                :series="dialogEchart.option.series"
                :grid="dialogEchart.option.grid"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import contentTitle from '@/pages/screen/heatMeeting/component/content-title'
import itemlistBox from '@/pages/screen/heatMeeting/component/itemlist-box'
import trackinfo from '@/pages/screen/heatMeeting/component/trackinfo'
import mesAct from '@/pages/screen/heatMeeting/component/mesAct'
import productionAct from '@/pages/screen/heatMeeting/component/productionAct'
import { post } from '@/lib/Util'
import moment from 'moment'
import heatEcharts from '@/pages/screen/heatMeeting/component/heatEcharts'
export default {
  name: 'index',
  layout: 'screenLayout',
  components: {
    contentTitle,
    itemlistBox,
    trackinfo,
    mesAct,
    productionAct,
    heatEcharts
  },
  data: () => {
    return {
      active: 1,
      // carouselFlag: true,
      carouselFlag: false,
      contentTitleData: {
        group: '', //班组
        shift: '' //班别
      },
      topLeftData: {
        weight: '', //班组实时产量
        avgWeight: '', //班组实时机时产量
        inHeatTime: '', //班组平均在炉时间
        temperatureRiseFall: '', //班组升降温比
        specificationConversion: '', //班组规格转换比
        group: '', //班组
        shift: '', //班别
        utilization: '', //利用率
        burnerMalfunctions: '', //烧嘴故障数
        heatTreatmentRatio: '', //有效作业率
        autoSteelLoadRatio: '', //自动装钢率
        furnaceRollerCurrent: '', //炉辊电流超限数

        preWeight: '', //前一班组实时产量
        avgPreWeight: '', //前一班组实时机时产量
        preInHeatTime: '', //前一班组平均在炉时间
        preTemperatureRiseFall: '', //前一班组升降温比
        preSpecificationConversion: '', //前一班组规格转换比
        preGroup: '', //前一班组
        preShift: '', //前一班别
        preFacid: '', //前一炉号
        YoYWeight: '', //产量同比
        YoYAvgWeight: '', //机时产量同比
        YoYInHeatTime: '', //平均在炉时间同比
        YoYTemperatureRiseFall: '', //班组升降温比
        YoYSpecificationConversion: '' //班组规格转换比同比
      },
      rightBottomData: {
        gasP: '',
        gasF: '',
        nitrogenP: '',
        allP: ''
      },
      currentCarousel: 0,
      currentLuNum: 5,
      leftList: [
        {
          name: '首页看板',
          value: 1,
          disabled: false
        },
        {
          name: 'MES实绩',
          value: 2,
          disabled: false
        },
        {
          name: '生产计划',
          value: 3,
          disabled: false
        },
        {
          name: '生产实绩',
          value: 4,
          disabled: false
        }
      ],
      rightList: [
        {
          name: '曲线分析',
          value: 7,
          disabled: false
        },
        {
          name: '报警明细',
          value: 6,
          disabled: false
        },
        {
          name: '数据分析',
          value: 5,
          disabled: false
        }
      ],
      dialogEchart: {
        dialogVisibleEchart: false,
        title: '',
        option: {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            /*right: '2%',
            top: '3%'*/
          },
          grid: [
            {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            }
          ],
          xAxis: [
            {
              type: 'category',
              data: [
                '1',
                '2',
                '3',
                '4',
                '5',
                '6',
                '7',
                '8',
                '9',
                '1',
                '2',
                '3',
                '4',
                '5',
                '6',
                '7',
                '8',
                '9',
                '1',
                '2',
                '3',
                '4',
                '5',
                '6',
                '7',
                '8',
                '9'
              ],
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dotted' //'dotted'虚线 'solid'实线
                }
              }
            }
          ],
          series: [
            {
              name: '甲',
              type: 'line',
              data: [
                320,
                332,
                301,
                334,
                320,
                332,
                301,
                334,
                567,
                320,
                332,
                301,
                334,
                320,
                332,
                301,
                334,
                567,
                320,
                332,
                301,
                334,
                320,
                332,
                301,
                334,
                567
              ],
              color: 'rgba(51, 145, 255, 1)'
            }
          ]
        }
      }
    }
  },
  mounted() {
    this.getTopLeftDataFive()
    this.getRightBottomFive()
  },
  methods: {
    openDialog(params, name) {
      this.$forceUpdate()
      console.log('fu', params, name)
      this.dialogEchart.dialogVisibleEchart = true
      this.dialogEchart.option.series[0].name = name
      this.dialogEchart.title = name
      const num = this.currentCarousel == 0 ? '5' : '6'
      const lastMonth26th = moment()
        .subtract(1, 'months')
        .date(26)
        .startOf('day')
        .format('YYYYMMDDHHmmss')
      const currentMonth25th = moment()
        .date(25)
        .startOf('day')
        .format('YYYYMMDDHHmmss')
      post('ht/HeatBasicData/findByTimeandShiftInTime', {
        startTime: lastMonth26th,
        endTime: currentMonth25th,
        name: params + num
      }).then(res => {
        if (res) {
          let xData = []
          let yData = []
          for (let key in res.data) {
            xData.push(key)
            yData.push(res.data[key])
          }
          this.dialogEchart.option.xAxis[0].data = xData
          this.dialogEchart.option.series[0].data = yData
        }
      })
    },
    handleClose() {
      this.dialogEchart.dialogVisibleEchart = false
    },
    carouselChange(num) {
      //0: 5#热处理炉   1: 6#热处理炉
      this.currentCarousel = num
      console.log('qiehuan', num)
      if (this.currentCarousel == 0) {
        this.currentLuNum = 5
        this.getTopLeftDataFive()
        this.getRightBottomFive()
      }
      if (this.currentCarousel == 1) {
        this.currentLuNum = 6
        this.getTopLeftDataSix()
        this.getRightBottomSix()
      }
    },
    checkLu(par1) {
      if (par1 == '1') {
        // 5#炉
        this.carouselFlag = false
        this.$refs.carouse.setActiveItem(0)
      } else if (par1 == '2') {
        // 6#炉
        this.carouselFlag = false
        this.$refs.carouse.setActiveItem(1)
      } else if (par1 == '0') {
        //默认 5/6#炉轮播
        this.carouselFlag = true
        this.$refs.carouse.setActiveItem(0)
      }
      console.log('fu', par1)
    },
    getTopLeftDataFive() {
      post('ht/HeatBasicData/findByPreGroup', {
        facid: '5'
      }).then(res => {
        if (res) {
          res.data.id = 'five'
          this.topLeftData = res.data
          this.contentTitleData.group = res.data.group
          this.contentTitleData.shift = res.data.shift
        }
      })
    },
    getTopLeftDataSix() {
      post('ht/HeatBasicData/findByPreGroup', {
        facid: '6'
      }).then(res => {
        if (res) {
          res.data.id = 'six'
          this.topLeftData = res.data
        }
      })
    },
    getRightBottomFive() {
      //5号炉
      /*煤气总管压力：01030107_0019_0001（单位：kPa）
        煤气总管流量：01030107_0018_0152（单位：m³/h）
        氮气总管压力：01030107_0001_0031（单位：kPa）
          炉体中区炉压：01030107_0019_0012（单位：Pa）*/
      post('it/customApi/iot/currentValue', [
        {
          pointCode: '01030107_0019_0001'
        },
        {
          pointCode: '01030107_0018_0152'
        },
        {
          pointCode: '01030107_0001_0031'
        },
        {
          pointCode: '01030107_0019_0012'
        }
      ]).then(res => {
        if (res) {
          /*let data = {
            statusCode: 200,
            message: '操作成功',
            data: [
              {
                index: '0',
                list: [
                  {
                    currentvalue: 0.12037
                  }
                ]
              },
              {
                index: '1',
                list: [
                  {
                    currentvalue: 0.0
                  }
                ]
              },
              {
                index: '2',
                list: [
                  {
                    currentvalue: 0.101273
                  }
                ]
              },
              {
                index: '3',
                list: [
                  {
                    currentvalue: 4.57176
                  }
                ]
              }
            ]
          }*/
          this.rightBottomData.gasP = res.data[0].list[0].currentvalue.toFixed(
            2
          )
          this.rightBottomData.gasF = res.data[1].list[0].currentvalue.toFixed(
            2
          )
          this.rightBottomData.nitrogenP = res.data[2].list[0].currentvalue.toFixed(
            2
          )
          this.rightBottomData.allP = res.data[3].list[0].currentvalue.toFixed(
            2
          )
        }
      })
    },
    getRightBottomSix() {
      /*
            * 6号炉
              煤气总管压力：2808311642205131（单位：kPa）
              煤气总管流量：5305240617547780 + 5305241861605809，两个流量相加（单位：Nm³/h）
              氮气总管压力：01030107_0002_0040（单位：MPa）
              炉体中区炉压：01030107_0002_0042（单位：Pa）
            *
            * */
      post('it/customApi/iot/currentValue', [
        {
          pointCode: '2808311642205131'
        },
        {
          pointCode: '5305240617547780'
        },
        {
          pointCode: '5305241861605809'
        },
        {
          pointCode: '01030107_0002_0040'
        },
        {
          pointCode: '01030107_0002_0042'
        }
      ]).then(res => {
        if (res) {
          /*let res = {
            statusCode: 200,
            message: '操作成功',
            data: [
              {
                index: '0',
                list: [
                  {
                    currentvalue: 0.0
                  }
                ]
              },
              {
                index: '1',
                list: [
                  {
                    currentvalue: 0.0
                  }
                ]
              },
              {
                index: '2',
                list: [
                  {
                    currentvalue: 101.768
                  }
                ]
              },
              {
                index: '3',
                list: [
                  {
                    currentvalue: -0.144676
                  }
                ]
              },
              {
                index: '4',
                list: [
                  {
                    currentvalue: 8.55035
                  }
                ]
              }
            ]
          }*/
          this.rightBottomData.gasP = res.data[0].list[0].currentvalue.toFixed(
            2
          )
          this.rightBottomData.gasF = (
            Number(res.data[1].list[0].currentvalue) +
            Number(res.data[2].list[0].currentvalue)
          ).toFixed(2)
          this.rightBottomData.nitrogenP = res.data[3].list[0].currentvalue.toFixed(
            2
          )
          this.rightBottomData.allP = res.data[4].list[0].currentvalue.toFixed(
            2
          )
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.screen-content {
  margin-top: 8px !important;
}
.screen-wrapper {
  position: relative;
  display: flex;
  height: 100vh;
  width: 100vw;
  flex-direction: column;
  background: #041a21 url('../../../assets/images/screen/screen-bg.png') repeat
    center;
  &:after {
    content: '';
    position: absolute;
    left: 25px;
    right: 25px;
    height: 4px;
    bottom: 8px;
    background: url('../../../assets/images/screen/footer-line.png') no-repeat;
    background-size: 100% 100%;
  }
  .screen-header {
    height: 71px;
    margin: 0 25px;
    &-inner {
      position: relative;
      display: flex;
      width: 100%;
    }
    .header-side {
      position: relative;
      flex: 1;
      margin: 7px 0;
      white-space: nowrap;
      border-top: 1px solid #136480;
      align-items: center;
      display: flex;
      .tab-box {
        flex: 1;
      }
      &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 1px;
        left: 0;
        bottom: 0;
        background: url(../../../assets/images/screen/header-line.png) repeat
          left;
      }
    }
    .header-left {
      margin-right: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:after {
        content: '';
        position: absolute;
        left: -16px;
        top: 0;
        bottom: 0;
        padding: 0;
        margin: auto;
        display: block;
        width: 12px;
        height: 40px;
        box-sizing: border-box;
        background: #1fc6ff;
        clip-path: polygon(
          5px 0,
          calc(100%) 0,
          calc(100%) 1px,
          5px 1px,
          1px 5px,
          1px calc(100% - 5px),
          5px calc(100% - 1px),
          100% calc(100% - 1px),
          100% 100%,
          calc(100% - 5px) 100%,
          5px 100%,
          0 calc(100% - 5px),
          0 5px
        );
      }
      .tab-box {
        flex: 0;
      }
    }
    .header-right {
      margin-left: 8px;
      text-align: right;
      &:after {
        content: '';
        position: absolute;
        right: -16px;
        top: 0;
        bottom: 0;
        padding: 0;
        margin: auto;
        display: block;
        width: 12px;
        height: 40px;
        box-sizing: border-box;
        background: #1fc6ff;
        transform: rotate(180deg);
        clip-path: polygon(
          5px 0,
          calc(100%) 0,
          calc(100%) 1px,
          5px 1px,
          1px 5px,
          1px calc(100% - 5px),
          5px calc(100% - 1px),
          100% calc(100% - 1px),
          100% 100%,
          calc(100% - 5px) 100%,
          5px 100%,
          0 calc(100% - 5px),
          0 5px
        );
      }
      &:before {
        background: url(../../../assets/images/screen/header-line2.png) repeat
          right;
      }
    }
    .header-btn {
      position: relative;
      display: inline-block;
      height: 35px;
      margin: 0 5px;
      padding: 0 10px;
      min-width: 60px;
      background: rgba(31, 198, 255, 0.12);
      border: 1px solid rgba(31, 198, 255, 0.2);
      font-weight: 400;
      font-size: 16px;
      line-height: 35px;
      text-align: center;
      color: #1fc6ff;
      cursor: pointer;
      &.active {
        color: #fff;
        font-weight: bold;
        background: rgba(31, 198, 255, 0.3);
      }
      &.disabled {
        color: #999;
        font-weight: bold;
        background: rgba(70, 77, 79, 0.2);
      }
      &:after {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        border: 1px solid rgba(31, 198, 255, 0.2);
      }
    }
    .header-title {
      position: relative;
      height: 71px;
      padding: 3px 0;
      img {
        display: block;
        height: 100%;
        margin: auto;
        user-select: none;
        -webkit-user-drag: none;
      }
    }
  }
  .screen-content {
    flex: 1;
    margin: 25px 25px 32px;
    overflow: hidden;
    position: relative;
  }
  .header-arrow {
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 80px;
    height: 20px;
    background-image: linear-gradient(100grad, #1ec5fe, #106685);
    animation: move-arrows 1s linear infinite;
    transform-origin: center;
    mask-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAUCAYAAAAa2LrXAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAH8SURBVHgB7ZhLSgNBEIb/6uBbURcKgo/ZuHChCLoxopgjeAO9gd4gnkBvoDfwCBFFoxDxBSq4iUFwIfhANCYhKXvEQMg0JulpSC38FiGpRfFRNVXpHsCAd8jTXpITXoL74ID+FPeOnPDCTIpb4ABJfgoGOQYSzFgqtWITIfHlerJYVAUMPGUxhZBI81OGyCYIP50lwsroIW8jBN1fmNJ5Wn7zjY0e8AzCIMwvWMAvLOvPdPlnWMnBdhwT47MiX7giCvMjU1DvFo/bkPC/lmN6ZHYy87QKC4ZS3Nmmx4QJnRX57jMLdAoLJPkpUzAdozTlEENVp70jtto5j7P0mevAfnWnh494EhZI8lNoQFIv7zX9eMfhSDJSwvhYkidggRQ/Qg2M41LEhn6847DANC6I4OZ+jm5gQbP9ahZQgqRkv7oK2GxJyX6q7ozthpjy14478hwiX5P86hvhpO4uB44NcX1s2IAFpu7mFa4fo3QLC5rpV/tP5L94f/qRZLlaSPCjhuSArUyU1uFIrqhw9xClK1ggxY/qlpN0lRPkF6kOeGfcxwUkXcn579jyWcScFU+YX+AYw1nsupLzef7AnKviSfQLFFDfBdf1Mnl1Iefz2oULnadgI2dCml+ggOl5OteL0b+k76k8rBZyJS+z9PauL+kl4GmgA5cIiTS/b5T7QUH8z5WLAAAAAElFTkSuQmCC');
    mask-size: contain;
  }
  .header-arrow-right {
    left: auto;
    right: 15px;
    transform: rotate(180deg);
  }
  @keyframes move-arrows {
    0% {
      mask-position: 0 0;
    }
    100% {
      mask-position: 40px 0;
    }
  }
}
/deep/ * {
  *::-webkit-scrollbar {
    width: 6px; /*对垂直流动条有效*/
    height: 6px; /*对水平流动条有效*/
    margin-left: 5px;
  }
  /*定义滑块颜色、内阴影及圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 7px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.15);
    background: #1fc6ff;
    opacity: 0.5;
  }
}
/deep/ .screen-dialog {
  .el-dialog {
    position: relative;
    border: 1px solid #0c4d63;
    background: #041a21;
    box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.12),
      0px 4px 8px rgba(0, 0, 0, 0.08), 0px 4px 16px 4px rgba(0, 0, 0, 0.04);
    &:after {
      content: '';
      position: absolute;
      top: -6px;
      left: 0;
      width: 59px;
      height: 13px;
      background: url(../../../assets/images/screen/dialog-top.png) no-repeat
        center;
    }
    .el-dialog__headerbtn {
      top: -19px;
      right: -19px;
      width: 36px;
      height: 36px;
      background: #0b3f67;
      text-align: center;
      line-height: 20px;
      /* 亮色 */
      border: 1px solid #1fc6ff;
      box-shadow: inset 0px 0px 10px #0e9cff;
      border-radius: 6px;
      transform: rotate(-45deg);
      .el-icon {
        transform: rotate(-45deg);
        font-size: 26px;
        color: #fff;
      }
      &:hover {
        box-shadow: inset 0px 0px 15px #54e1fa;
      }
    }
    .el-dialog__header {
      padding: 24px;
    }
    .custom-dialog-title {
      position: relative;
      padding-left: 66px;
      padding-right: 10px;
      height: 44px;
      font-style: normal;
      font-weight: 1000;
      font-size: 22px;
      line-height: 44px;
      color: #1fc6ff;
      background: #0f2b3f url(../../../assets/images/screen/dialog-title-bg.png)
        no-repeat left bottom;
      .btn-box {
        float: right;
        line-height: 40px;
        white-space: nowrap;
        color: #fff;
      }
    }
    .el-dialog__body {
      padding-top: 0;
    }
  }
  .el-upload:focus {
    color: #fff;
  }
  .el-textarea__inner,
  .el-input__inner {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
  }
  .el-select-dropdown {
    color: #fff;
    background: rgba(9, 60, 77);
    border-color: rgba(31, 198, 255, 0.6);
  }
  .el-select-dropdown__item {
    color: #fff;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background: rgba(4, 26, 33);
  }
  .el-popper[x-placement^='bottom'] .popper__arrow:after {
    border-bottom-color: rgba(9, 60, 77);
  }
  .el-divider__text {
    background-color: #041a21;
    color: #1fc6ff;
  }
  .el-divider {
    background-color: #58627a;
  }
  .el-form-item__label {
    color: #e0f9fc;
  }
}
/deep/ .el-table {
  .table-total td.el-table__cell {
    background: rgba(31, 198, 255, 0.2);
  }
}
/deep/ .el-table {
  background: #041a21;
  color: #def0ff;
  &.font-table {
    font-size: 16px;
    td.big-font {
      font-size: 20px;
      .cell {
        line-height: 1.3;
      }
    }
  }
  &.center-table .el-table__cell {
    text-align: center;
    &.is-left {
      text-align: left;
    }
  }
  .red-row {
    color: #ff0000;
  }
  .split-row {
    td.el-table__cell {
      border-bottom: 4px solid #255a6c;
    }
  }
  tr {
    background: transparent;
  }
  th.el-table__cell {
    background: transparent;
  }
  &.el-table--border th.el-table__cell,
  td.el-table__cell,
  th.el-table__cell.is-leaf {
    border-bottom: 4px solid #0b2a34;
  }
  &.el-table--border .el-table__cell {
    border-right: 4px solid #0b2a34;
  }
  thead.is-group th.el-table__cell,
  .el-table__footer-wrapper tbody td.el-table__cell,
  .el-table__header-wrapper tbody td.el-table__cell,
  th.el-table__cell {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-top: none;
  }
  &.el-table--border {
    border-width: 4px;
    border-color: #0b2a34;
  }
  &:before {
    background: transparent;
  }
  &:after {
    background: transparent;
  }
  &.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background: rgba(31, 198, 255, 0.1);
  }
  .el-loading-mask {
    background: rgba(175, 223, 239, 0.6);
  }
  .el-table__empty-text {
    color: #fff6ee;
  }
  /*全局滚动条样式 chrome内核*/
  /*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/

  *::-webkit-scrollbar {
    width: 6px; /*对垂直流动条有效*/
    height: 6px; /*对水平流动条有效*/
  }

  /*定义滚动条的轨道颜色、内阴影及圆角*/
  *::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.15);
    border-radius: 3px;
  }

  /*定义滑块颜色、内阴影及圆角*/
  *::-webkit-scrollbar-thumb {
    border-radius: 7px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.15);
    background: #1fc6ff;
    opacity: 0.5;
  }

  /*定义两端按钮的样式*/
  *::-webkit-scrollbar-button {
    display: none;
  }

  /*定义右下角汇合处的样式*/
  *::-webkit-scrollbar-corner {
    display: none;
  }
}
/deep/ .screen-input {
  .el-textarea__inner,
  .el-input__inner {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
    padding-right: 5px;
  }
  .el-input__prefix {
    color: #fff;
  }
}
</style>
