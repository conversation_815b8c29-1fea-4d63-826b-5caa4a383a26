<template>
  <div>
    <el-dialog
      :title="role.roleName + '--KPI权限管理'"
      :visible.sync="dialogVisible"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        label-width="120px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="角色名称："
          prop="roleName"
        >
          {{ role.roleName || '' }}
        </el-form-item>
        <el-form-item
          label="角色kpi  权限："
          prop="org"
        >
          <el-tree
            ref="tree"
            :data="data"
            :check-strictly="true"
            :default-checked-keys="defaultSelected"
            :default-expanded-keys="defaultExpanded"
            :props="defaultProps"
            :expand-on-click-node="false"
            node-key="id"
            show-checkbox
            @check="checkChange"
          >
            <template 
              v-slot="{ node, data }" 
            >
              <span>
                {{ node.label }}
              </span>

            </template>

          </el-tree>
          
        </el-form-item>

      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { filterResourceSelected, relateResource } from '@/api/system'
import { post } from '@/lib/Util'
import { generateTree, getMenuData } from '@/lib/Menu'
import { findKpiTree, findRoleKpi, saveRoleKpi } from '@/api/kpi'
import { ENUM } from '@/lib/Constant'

export default {
  components: {},
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  props: ['visible', 'role'],
  data() {
    return {
      loading: false,
      url: {},
      data: [],
      totalData: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        'row[nobile]': 1
      },
      defaultSelected: [],
      defaultExpanded: [],
      kpiFunction: ENUM.kpiFunction
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set() {}
    },
    $tree: function() {
      return this.$refs['tree']
    }
  },
  watch: {},
  mounted() {
    this.loadData()
    console.log(this.defaultProps)
  },
  methods: {
    async loadData() {
      // 获取所有资源
      const res = await post(findKpiTree, {})
      if (res.success) {
        this.totalData = res.data
        // this.updateNode(res.data)
        this.data = this.kpiFunction
          .map(item => {
            item.id = 'kpi' + item.value
            item.name = item.label
            item.children = res.data.filter(kpi => kpi.feature == item.value)
            return item
          })
          .filter(item => {
            return item.children.length
          })
      }
      const tree = await post(findRoleKpi, {
        page: 0,
        size: 10,
        roleCode: this.role.roleCode
      })
      if (tree.data.content.length) {
        this.defaultSelected = JSON.parse(tree.data.content[0].kids) || []
        this.defaultExpanded = JSON.parse(tree.data.content[0].kids) || []
      }
      // this.defaultSelected =

      // // 获取角色下资源
      // post(filterResourceSelected, { roleID: this.role.id }).then(res => {
      //   if (res.success) {
      //     this.defaultSelected = res.data
      //       .filter(item => item.isSelected === '1')
      //       .map(item => item.id)
      //     this.$nextTick(() => {
      //       res.data.filter(item => item.isSelected === '0').forEach(item => {
      //         this.$refs.tree.setChecked(item.id, false)
      //       })
      //     })
      //   }
      // })
    },
    updateNode(list) {
      list.forEach(item => {
        item.checked = false
        item.children && item.children.length && this.updateNode(item.children)
      })
    },
    handelConfirm() {
      this.loading = true
      const selectedList = this.$refs.tree
        .getCheckedNodes()
        .filter(item => item.feature !== undefined)
      console.log(selectedList)
      post(saveRoleKpi, {
        roleCode: this.role.roleCode,
        kids: JSON.stringify(selectedList.map(item => item.id))
      }).then(res => {
        this.loading = false
        if (res.success) {
          this.$message.success('分配成功！')
          this.close()
        } else {
          this.$message.success(res.message)
        }
      })
    },

    // 递归添加父级级单
    handleMenu(menuData, addMenu) {
      const addList = []
      // console.log(menuData, addMenu)
      addMenu.forEach(item => {
        if (!item.parentId) return
        if (
          !menuData.find(i => {
            return i.id && i.id === item.parentId
          })
        ) {
          const match = this.totalData.find(i => i.id === item.parentId)
          match && menuData.push(match)
          match && addList.push(match)
        }
      })
      if (addList.length) {
        this.handleMenu(menuData, addList)
      }
    },

    close() {
      this.$emit('update:visible', false)
    },

    checkChange(data, status) {
      const node = this.$tree.getNode(data.id)
      this.selectAll(data.children || [], node.checked) // 全选中或全取消
      if (status && !node.parent.checked) {
        if (!this.getParentStatus(node.parent)) {
          // 断层了
          this.selectAllParent(node)
        }
      }
      console.log(this.$tree.getHalfCheckedKeys())
    },

    // 递归获取目标值
    selectAll(list, status) {
      list &&
        list.length &&
        list.forEach(item => {
          this.$tree.setChecked(item.id, status, true)
          this.selectAll(item.children, status)
        })
    },

    // 递归父级状态
    getParentStatus(node) {
      if (node.level === 1 || node.level === 0) {
        return true
      }
      if (node.checked) return false // 断层了
      if (node.parent) {
        return this.getParentStatus(node.parent)
      }
      return true // 未断层
    },

    // 递归选中父级.
    selectAllParent(node) {
      this.$tree.setChecked(node.data.id, true)
      if (node.parent && !node.parent.checked) {
        this.selectAllParent(node.parent)
      }
    },
    getCheckStatus(node) {
      console.log(node)
      // 遍历获取子节点选择状态
      const checkList = []
      this.updateCheckStatus(checkList, node.childNodes)
      console.log(checkList)
      if (checkList.filter(item => item === true).length) {
        return true
      } else {
        return false
      }
    },
    updateCheckStatus(list, nodes) {
      console.log(nodes)
      nodes &&
        nodes.length &&
        nodes.forEach(item => {
          console.log(item)
          item.checked && list.push(item.checked)
          this.updateCheckStatus(list, item.childNodes)
        })
    }
  }
}
</script>
<style scoped>
</style>
