<template>
  <div class="full-height">
    <slot name="content"/>
    <div
      v-if="showTable"
      class="scroll-wrapper">
      <div class="chart-title text-right">
        <span
          v-command="'/screen/C2Meeting/edit'"
          v-if="showEdit"
          class="screen-btn"
          @click="openDialog">
          <el-icon class="el-icon-edit-outline"/>
          操作
        </span>
      </div>
      <div
        ref="table1"
        class="chart">
        <el-table
          v-loading="loading"
          :data="showGridData"
          :max-height="maxHeight"
          :size="'medium'"
          :span-method="spanMethod"
          class="center-table font-big-table"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.children"
                :key="index"
                :width="item.width || ''"
                :property="item.keySave"
                :label="item.label"
                :align="item.align">
                <template
                  v-for="(cItem, cIndex) in item.children">
                  <template v-if="item.inputType === 'textarea'">
                    <el-table-column
                      :key="cIndex"
                      :width="cItem.width || ''"
                      :property="cItem.keySave"
                      :label="cItem.label"
                      :align="cItem.align">
                      <template v-slot="{ row }">
                        <div
                          slot="content"
                          v-html="formatText(row[cItem.keySave], cItem.split)"
                        />
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      :key="cIndex"
                      :width="cItem.width || ''"
                      :property="cItem.keySave"
                      :label="cItem.label"
                      :align="cItem.align"/>
                  </template>
                </template>
              </el-table-column>
              <template v-else>
                <el-table-column
                  v-if="item.type === 'index'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                />
                <template v-else>
                  <template v-if="item.inputType === 'textarea'">
                    <el-table-column
                      :key="index"
                      :width="item.width || ''"
                      :property="item.keySave"
                      :label="item.label"
                      :align="item.align">
                      <template v-slot="{ row }">
                        <div
                          slot="content"
                          v-html="formatText(row[item.keySave], item.split)"
                        />
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      :key="index"
                      :width="item.width || ''"
                      :property="item.keySave"
                      :label="item.label"
                      :align="item.align">
                      <template
                        v-slot="{ row }"
                        v-if="item.formatter">
                        {{ item.formatter(row, null, row[item.keySave]) }}
                      </template>
                    </el-table-column>
                  </template>
                </template>
              </template>
            </template>
          </template>
        </el-table>
        <slot name="bottom"/>
        <div class="calculationHints">
          <br>
          <br>
          <!-- 综合指标{{ planAll }},实实绩指标{{ reality }},<br>计划成材率<input
            v-model="
            planData"
            style="color:skyblue;border:none;  font-size: 18px;width:5vh"
            type="text"
            @change="change">%。实际成材率<input
              v-model="finshData"
              style="color:skyblue;border:none;  font-size: 18px;width:5vh"
              type="text"
              @change="change">% -->
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :width="dialogWidth"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event)">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportTable">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="gridData"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.children"
                :key="index"
                :width="item.width || ''"
                :label="item.label">
                <template
                  v-for="(cItem, cIndex) in item.children">
                  <el-table-column
                    :key="cIndex"
                    :width="cItem.width || ''"
                    :property="cItem.keySave"
                    :label="cItem.label">
                    <template v-slot="{ row }">
                      <template v-if="cItem.inputType === 'textarea'">
                        <el-input
                          v-model="row[cItem.keySave]"
                          :rows="4"
                          :disabled="!isFieldEditable(cItem.keySave)"
                          type="textarea"
                        />
                      </template>
                      <template v-else>
                        <el-input
                          v-model="row[cItem.keySave]"
                          :disabled="!isFieldEditable(cItem.keySave)"
                        />
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </el-table-column>
              <template v-else>
                <el-table-column
                  v-if="item.type === 'index'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                />
                <template v-else>
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label">
                    <template v-slot="{ row }">
                      <template v-if="item.inputType === 'textarea'">
                        <el-input
                          v-model="row[item.keySave]"
                          :rows="4"
                          :disabled="!isFieldEditable(item.keySave)"
                          type="textarea"
                        />
                      </template>
                      <template v-else-if="item.inputType === 'date'">
                        <el-date-picker
                          v-model="row[item.keySave]"
                          :size="'mini'"
                          :value-format="'yyyy-MM-dd'"
                          :disabled="!isFieldEditable(item.keySave)"
                          type="date"
                          class="screen-input"/>
                      </template>
                      <template v-else-if="item.inputType === 'month'">
                        <el-date-picker
                          v-model="row[item.keySave]"
                          :size="'mini'"
                          :value-format="'yyyy-MM'"
                          :disabled="!isFieldEditable(item.keySave)"
                          type="month"
                          class="screen-input"/>
                      </template>
                      <template v-else-if="item.inputType === 'file'">
                        <template v-if="item.attachmentUpload">
                          <ul class="el-upload-list el-upload-list--picture-card">
                            <li
                              v-for="(item) in getPictureList(row.picture)"
                              :key="item"
                              class="el-upload-list__item is-ready">
                              <img-view
                                :key="item"
                                :id="item"
                                deleteable
                                @img-delete="handleImgDelete($event, row.index)"
                              />
                            </li>
                          </ul>
                        </template>
                        <el-upload
                          ref="upload"
                          :auto-upload="false"
                          :http-request="httpRequest"
                          :on-change="(file, fileList) => handleChange(file, fileList, row.index)"
                          :show-file-list="false"
                          :disabled="!isFieldEditable(item.keySave)"
                          multiple
                          list-type="picture-card"
                          action="#"
                          style="display: inline"
                          @click.native="editIndex = row.index">
                          <i class="el-icon-plus"/>
                        </el-upload>
                      </template>
                      <template v-else-if="item.keySave === 'isCompleted'">
                        <el-select
                          v-model="row[item.keySave]"
                          :disabled="!isFieldEditable(item.keySave)"
                          placeholder="请选择">
                          <el-option
                            :value="true"
                            label="完成"/>
                          <el-option
                            :value="false"
                            label="未完成"/>
                        </el-select>
                      </template>
                      <template v-else>
                        <el-input
                          v-model="row[item.keySave]"
                          :disabled="!isFieldEditable(item.keySave)"/>
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </template>
            </template>
          </template>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <!-- v-if="canEditQuality" -->
              <span
                v-show="false"
                class="screen-btn"
                disabled
                @click="delGridData($index)">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData()">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

  <script>
import moment from 'moment'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import {
  qmsQualityQuery,
  qmsQualitySave,
  findDielectricConsumption,
  findHeatTreatment,
  findRhfConsumptionByDate
} from '@/api/screen'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { batchUpdateResource, deleteFileByIds, uploadFile } from '@/api/system'
import ImgView from '@/components/ImgView'

export default {
  name: 'custom-table-research',
  components: { ScreenBorder },
  props: {
    title: {
      type: String,
      default: ''
    },
    setting: {
      type: Array,
      default: function() {
        return []
      }
    },
    mergeSet: {
      type: Object,
      default: function() {
        return {}
      }
    },
    selectDate: {
      type: [String, Date],
      default: ''
    },
    urlList: {
      type: String,
      default: ''
    },
    urlSave: {
      type: String,
      default: ''
    },
    showTable: {
      type: Boolean,
      default: false
    },
    showEdit: {
      type: Boolean,
      default: true
    },
    heightAuto: {
      type: Boolean,
      default: true
    },
    dialogWidth: {
      type: String,
      default: '80%'
    },
    editableFields: {
      type: Array,
      default: function() {
        return [] // 默认为空数组，表示所有字段都可编辑
      }
    }
  },
  data: function() {
    return {
      cDate: '',
      loading: false,
      dialogVisible: false,
      showGridData: [],
      showTestData: [],
      gridData: [],
      originalDataLength: 0, // 添加一个变量来记录原始数据长度
      importDate: null,
      importDateVisible: false,
      importFunName: '',
      mergeArr: [],
      spanArr: {},
      position: 0,
      maxHeight: null,
      ABC: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'K'
      ],
      reality: 0,
      finshData: 0,
      planData: 0,
      planAll: 0
    }
  },
  computed: {
    canEditQuality: function() {
      // console.log(moment().format('yyyy-MM-DD'), this.cDate)
      return (
        moment().format('yyyy-MM-DD') <=
        moment(this.cDate)
          .subtract(-1, 'day')
          .format('yyyy-MM-DD')
      )
    }
  },
  watch: {
    selectDate: function() {
      if (this.selectDate instanceof Date) {
        this.cDate = this.$moment(this.selectDate).format('YYYY-MM-DD')
      } else {
        this.cDate = this.selectDate
      }
    },
    cDate: function() {
      // 初始化数据
      this.getData()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    if (this.selectDate instanceof Date) {
      this.cDate = this.$moment(this.selectDate).format('YYYY-MM-DD')
    } else {
      this.cDate = this.selectDate
    }
    this.getData()
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    openDialog() {
      this.dialogVisible = true
      this.getData() // 确保打开时加载最新数据
      // 在下一个事件循环中记录原始数据长度，确保数据已加载
      this.$nextTick(() => {
        this.originalDataLength = this.gridData.length
      })
    },
    // 检查字段是否可编辑
    isFieldEditable(fieldKey) {
      // 如果editableFields为空，则所有字段都可编辑
      if (!this.editableFields || this.editableFields.length === 0) {
        return true
      }
      // 否则只有在editableFields中的字段才可编辑
      return this.editableFields.includes(fieldKey)
    },
    httpRequest(params) {},
    async handleChange(file, fileList, index) {
      console.log(file, fileList, index)
      const formData = new FormData()
      formData.append('files', file.raw)
      post(uploadFile, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        if (res.success) {
          this.$message.success('图片上传成功！')
          const obj = this.unfinished.gridData[index]
          const pictures = obj.picture ? obj.picture.split('|') : []
          pictures.push(res.data[0].id)
          this.unfinished.gridData.splice(
            index,
            1,
            Object.assign({}, obj, {
              picture: pictures.join('|')
            })
          )
          // this.unfinished.gridData[index].picture = pictures.join('|')
          console.log(this.unfinished.gridData)
        } else {
          this.$message.warning('文件上传失败！')
          this.loading = false
        }
      })
    },
    async handleImgDelete(file, index) {
      const del = await post(deleteFileByIds, { ids: [file.id] })
      if (del.success) {
        const obj = this.unfinished.gridData[index]
        this.unfinished.gridData.splice(
          index,
          1,
          Object.assign({}, obj, {
            picture: obj.picture
              .split('|')
              .filter(item => item !== file.id)
              .join('|')
          })
        )
      }
    },
    getPictureList(picture) {
      return picture.split('|')
    },
    // 导入文件
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = this.ABC[index]
      })
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, obj)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.gridData = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    // 导出表格
    exportTable() {
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = item.label
      })
      const data = [obj].concat(
        _.cloneDeep(
          this.gridData.map(item => {
            const objRow = {}
            this.setting.forEach(set => {
              objRow[set.keySave] = item[set.keySave]
            })
            return objRow
          })
        )
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `${this.title}（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    /**
     * 构建请求参数，根据接口类型添加不同的参数
     * @param {string} dateStr - 日期字符串
     * @param {string} dateParamName - 日期参数名称 ('date' 或 'setTime')
     * @returns {Object} - 构建好的请求参数对象
     */
    buildRequestParams(dateStr, dateParamName = 'date') {
      let params = {}

      if (this.urlList === findRhfConsumptionByDate) {
        // 加热炉单耗模块使用referenceDate参数
        params = { referenceDate: dateStr }
      } else if (
        this.urlList === findDielectricConsumption ||
        this.urlList === findHeatTreatment
      ) {
        // 对于能介消耗和热处理能耗接口，需要传递energyType参数
        const parentComponent = this.$parent.$parent
        if (
          parentComponent &&
          parentComponent.energyConsumptionRadio &&
          this.urlList === findDielectricConsumption
        ) {
          params = {
            [dateParamName]: dateStr,
            energyType: parentComponent.energyConsumptionRadio
          }
        } else if (
          parentComponent &&
          parentComponent.heatTreatmentRadio &&
          this.urlList === findHeatTreatment
        ) {
          params = {
            [dateParamName]: dateStr,
            energyType: parentComponent.heatTreatmentRadio
          }
        } else {
          params = { [dateParamName]: dateStr }
        }
      } else {
        params = { [dateParamName]: dateStr }
      }

      return params
    },

    // 获取数据
    getData() {
      // 处理日期格式，确保是字符串格式
      let dateStr = this.cDate
      if (this.cDate instanceof Date) {
        dateStr = this.$moment(this.cDate).format('YYYY-MM-DD')
      }

      // 构建请求参数
      const params = this.buildRequestParams(dateStr, 'date')

      post(this.urlList, params).then(res => {
        //
        this.loading = false

        this.showGridData = res.data.map(item => {
          this.planAll = item.planAll
          this.reality = item.reality
          this.finshData = item.actualYield
          this.planData = item.planYield
          const obj = {}
          this.setting.forEach(set => {
            if (set.children && set.children.length) {
              set.children.forEach(child => {
                obj[child.keySave] = item[child.keyQuery]
              })
            } else {
              obj[set.keySave] = item[set.keyQuery]
            }
          })
          return obj
        })
        this.gridData = _.cloneDeep(this.showGridData)
        this.originalDataLength = this.gridData.length // 记录原始数据长度
        this.$nextTick(() => {
          this.$emit('change', this.showGridData)
        })
      })
    },
    change() {
      this.saveData()
    },
    // 更新数据
    saveData() {
      this.loading = true
      const params = {
        setTime: this.cDate,
        data: this.gridData.map(item => {
          item.setTime = this.cDate
          return item
        })
      }
      post(this.urlSave, params).then(res => {
        this.loading = false
        if (res.status == 1) {
          this.$message.success('保存成功！')
          this.dialogVisible = false
          this.getData()
          this.$emit('save-success', this.title)
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    // 导入日期数据
    importData(date) {
      // 处理日期格式，确保是字符串格式
      let dateStr = date
      if (date instanceof Date) {
        dateStr = this.$moment(date).format('YYYY-MM-DD')
      }

      // 构建请求参数，使用setTime作为日期参数名
      const params = this.buildRequestParams(dateStr, 'setTime')

      post(this.urlList, params).then(res => {
        //
        this.loading = false
        this.gridData = res.data.map(item => {
          const obj = {}
          this.setting.forEach(set => {
            obj[set.keySave] = item[set.keyQuery]
          })
          return obj
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 执行导入
    importHistoryData() {
      this.importData(this.importDate)
      this.importDateVisible = false
    },
    // 下拉菜单指令
    handleProcessedCommand(command) {
      if (command === 'yesterday') {
        this.importData(
          this.$moment(this.cDate)
            .subtract(1, 'day')
            .format('yyyy-MM-DD')
        )
      } else {
        this.importDate = this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
        this.importDateVisible = true
      }
    },
    // 数据管理
    clearGridData() {
      this.gridData = []
    },
    addGridData() {
      // 检查是否已经添加了一条新数据
      if (this.gridData.length > this.originalDataLength) {
        // 如果已经添加了一条新数据，显示提示信息
        this.$message.warning('最多只能增加一条数据')
        return
      }
      // 否则允许添加一条新数据
      this.gridData.push({})
    },
    delGridData(index) {
      this.gridData.splice(index, 1)
    },
    // 日期改变推送
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    // 计算需要合并的单元格
    formatSpanData(data) {
      this.mergeArr.forEach(keyName => {
        this.spanArr[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
    },
    // 合并单元格
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // 排除序号列，只对第二列（索引为1的列）进行合并
      if (column.type === 'index' || columnIndex === 0) {
        return [1, 1] // 序号列不合并
      }

      if (columnIndex === 1) {
        const currentValue = row[column.property]
        const preRow = this.showGridData[rowIndex - 1]
        //上一行这一列的数据
        const preValue = preRow ? preRow[column.property] : null
        // 如果当前值和上一行的值相同，则将当前单元格隐藏
        if (currentValue === preValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let rowspan = 1
          // 计算应该合并的行数
          for (let i = rowIndex + 1; i < this.showGridData.length; i++) {
            const nextRow = this.showGridData[i]
            const nextValue = nextRow[column.property]
            if (nextValue === currentValue) {
              rowspan++
            } else {
              break
            }
          }
          return { rowspan, colspan: 1 }
        }
      }
    },
    // 生成带换行数据
    formatText(text, split) {
      if (!text) {
        return ''
      }
      if (split) text = text.split(split).join('\n')
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },
    // 计算高度
    calculate() {
      this.showTable &&
        this.heightAuto &&
        (this.maxHeight = this.$refs.table1.offsetHeight)
    }
  }
}
</script>

  <style scoped lang="less">
// 大屏按钮
.screen-btn {
  display: inline-block;
  min-width: 68px;
  height: 28px;
  padding: 0 5px;
  background: rgba(31, 198, 255, 0.3);
  border: 1px solid #1fc6ff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  color: #fff;
  &:hover {
    background: rgba(31, 198, 255, 0.6);
    border: 1px solid #1fc6ff;
  }
  &:disabled {
    background: rgba(150, 150, 150, 0.3);
    border: 1px solid #999;
    cursor: not-allowed;
    opacity: 0.6;
    &:hover {
      background: rgba(150, 150, 150, 0.3);
      border: 1px solid #999;
    }
  }
}
.scroll-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .chart-title {
    font-size: 16px;
    font-weight: bolder;
    color: #ffffff;
    line-height: 20px;
    margin: 5px 0 10px;

    &.text-right {
      position: absolute;
      right: 10px;
      top: -50px;
    }
  }
  .chart {
    flex: 1;
    height: 0;
  }
}
/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}
/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
.calculationHints {
  margin-top: 28px;
  font-size: 18px;
  color: skyblue;
}
.text-center {
  margin-top: 20px;
}

.screen-input {
  width: 130px;
}

/deep/ .el-upload {
  background: rgba(31, 198, 255, 0.2);
  color: #fff;
  border-color: rgba(31, 198, 255, 0.6);
}
</style>
