<template>
  <div class="page-content">
    <el-row
      :gutter="20"
      class="row-bg full-height"
      justify="start"
      type="flex"
    >
      <el-col
        :span="5"
        class="full-height"
      >
        <div class="tree-wrapper full-height shadow-light overflow-auto" >
          <el-tree
            ref="tree"
            :data="data"
            :load="loadNode"
            :props="defaultProps"
            lazy
            node-key="id"
            @node-click="handleNodeClick"
            @node-contextmenu="oncontextmenu"
          >
            <template
              v-slot="{node, data}">
              <span
                :class="{'first-node': node.level === 1}"
                class="custom-tree-node">
                <span>{{ node.label }}</span>
                <span>
                  <el-button
                    type="text"
                    size="mini"
                    @click.native.stop="handleAddChild(node, data)">
                    新增
                  </el-button>
                  <el-button
                    v-if="node.level !== 1"
                    type="text"
                    size="mini"
                    @click.native.stop="handleDelete(data)">
                    删除
                  </el-button>
                </span>
              </span>
            </template>
          </el-tree>
        </div>

      </el-col>
      <el-col
        :span="19"
        class="full-height overflow-auto"
      >
        <div class="page-operate">
          <div class="search-wrapper">
            <el-form
              ref="searchForm"
              :model="searchForm"
              size="mini"
              inline
              @keyup.enter.native="handleSearch(true)"
            >
              <el-form-item
                prop="id"
              >
                <el-input
                  v-model="searchForm.id"
                  clearable
                  size="small"
                  placeholder="请输入指标编号"
                  style="width: 140px"
                  suffix-icon="el-icon-search"
                  type="number"
                />
              </el-form-item>
              <el-form-item
                prop="name"
              >
                <el-input
                  v-model="searchForm.name"
                  clearable
                  size="small"
                  placeholder="请输入指标名称"
                  suffix-icon="el-icon-search"
                  style="width: 140px"
                  type="text"
                />
              </el-form-item>
              <el-form-item
                label="主题域"
                prop="feature"
              >
                <el-select
                  v-model="searchForm.feature"
                  :style="{width: '100px'}"
                  size="small"
                  clearable
                  placeholder="选择功能"
                >
                  <el-option
                    v-for="(item, index) in kpiFunction"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="厂区/处室"
                prop="factory"
              >
                <el-select
                  v-model="searchForm.factory"
                  :style="{width: '100px'}"
                  size="small"
                  clearable
                  placeholder="选择厂区"
                >
                  <el-option
                    v-for="(item, index) in factoryList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="层级"
                prop="rank"
              >
                <el-select
                  v-model="searchForm.rank"
                  :style="{width: '100px'}"
                  size="small"
                  clearable
                  placeholder="选择层级"
                >
                  <el-option
                    v-for="(item, index) in levelList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div class="text-right">
            <el-button
              icon="el-icon-search"
              type="primary"
              size="small"
              @click="handleSearch"
            >搜索
            </el-button>
            <el-button
              v-command="'/kpi/indicators/add'"
              icon="el-icon-circle-plus-outline"
              size="small"
              type="success"
              @click="handleAdd"
            >新增
            </el-button>
          </div>
        </div>
        <div class="page-card shadow-light">
          <el-table
            v-loading="loading"
            :data="tableData"
            :size="size"
            border
            style="width: 100%"
          >
            <el-table-column
              label="序号"
              type="index"
              width="60"
            />
            <el-table-column
              label="指标名称"
              prop="name"
              min-width="80"
              show-overflow-tooltip
            />
            <el-table-column
              label="主题域"
              prop="feature"
              width="80"
            >
              <template
                v-slot="{row}"
              >
                <!-- {{ row.feature }} -->
                {{ getFeature(kpiFunction, row.feature) }}
                <!-- {{ getValue(kpiFunction, row.feature).label }} -->
              </template>
            </el-table-column>
            <el-table-column
              label="厂区/处室"
              prop="factory"
              max-width="60"
            >
              <template
                v-slot="{row}"
              >
                {{ getValue(factoryList, row.factory).label }}
              </template>
            </el-table-column>
            <el-table-column
              label="指标层级"
              prop="rank"
              width="80"
            >
              <template
                v-slot="{row}"
              >
                {{ getValue(levelList, row.rank).label }}
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              prop="status"
              width="80"
            >
              <template
                v-slot="{row}"
              >
                <el-tag
                  :type="getValue(statusList, row.status).type"
                  disable-transitions
                >{{ getValue(statusList, row.status).label }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="180"
            >
              <template
                v-slot="{row}"
              >
                <span v-command="'/kpi/indicators/editRule'">
                  <el-button
                    size="small"
                    type="text"
                    @click="handleRuleList(row)"
                  >规则管理
                  </el-button>
                  <el-divider
                    direction="vertical" />
                </span>
                <span v-command="'/kpi/indicators/edit'">
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                  <el-divider
                    direction="vertical" />
                </span>
                <el-button
                  v-command="'/kpi/indicators/delete'"
                  slot="reference"
                  type="text"
                  @click="handleDelete(row)"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-row
            align="middle"
            class="table-pagination"
            justify="end"
            type="flex"
          >
            <el-pagination
              :current-page="page.page"
              :page-size="page.size"
              :page-sizes="[10, 20, 30, 40]"
              :total="page.total"
              background
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </el-row>
          
        </div>
      </el-col>
    </el-row>
    <Edit
      ref="modalForm"
      @success="handleSearch"
      @refresh-tree="refreshTreeNode"
    />
    <RuleList
      ref="modalRuleList"/>
    <!-- 右键菜单 -->
    <ul
      v-show="rightMenuVisible"
      ref="rightMenu"
      :style="{ left: rightMenuLeft + 'px', top: rightMenuTop + 'px' }"
      class="contextmenu"
    >
      <li @click="rightAddNext()">添加下级KPI</li>
      <template
        v-if="rightMenuData && rightMenuData.level !== 1">
        <li @click="rightAddBrother()">添加同级KPI</li>
        <li @click="rightMod()">修改</li>
        <li @click="rightDel">删除</li>
      </template>
    </ul>
  </div>
</template>

<script>
import Edit from './component/newEdit'
import RuleList from './component/ruleList'
import listMixins from '@/mixins/ListMixins'
import { post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'
import { findBySpecification, findNextRank, kpiDelete } from '@/api/kpi'
export default {
  name: 'kpi-indicators',
  components: {
    Edit,
    RuleList
  },
  mixins: [listMixins],
  data: () => {
    return {
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: findBySpecification, //分页接口地址
        delete: kpiDelete //删除接口地址
      },
      editUserId: null,
      data: [], // 树状数据初始化为空数组
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      kpiFunction: ENUM.kpiFunction,
      factoryList: ENUM.factoryList,
      levelList: ENUM.levelList,
      statusList: [
        {
          value: 1,
          label: '正常',
          type: 'success'
        },
        {
          value: 0,
          label: '废弃',
          type: 'warning'
        }
      ],
      rightMenuVisible: false,
      rightMenuLeft: 0,
      rightMenuTop: 0,
      rightMenuData: null,
      nodeInfo: null,
      featureValue: ''
    }
  },
  watch: {
    rightMenuVisible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  created() {},
  methods: {
    getFeature(list = [], value) {
      //考虑到指标可能包含多个主题域，所以需要将value分割成数组，然后遍历数组，返回每个主题域的label
      let features = value.split(',')
      if (features.length > 1) {
        let result = []
        features.forEach(feature => {
          result.push(list.find(item => item.value === feature).label)
        })
        return result.join(',')
      }
      return list.find(item => item.value === value).label
    },
    getValue: function(list = [], value) {
      // 如果value包含逗号，说明是多个值
      // if (typeof value === 'string' && value.includes(',')) {
      //   // 将多个值分割成数组
      //   const values = value.split(',')
      //   // 返回第一个匹配的值对应的对象
      //   return list.find(item => item.value == values[0]) || {}
      // }
      // 单个值的情况
      return list.find(item => item.value == value) || {}
    },
    async loadRootData() {
      //
      const list = this.kpiFunction.map(item => {
        item.id = 'kpi' + item.value
        item.name = item.label
        item.hasChild = true
        return item
      })
      return Promise.resolve(list)
    },
    async loadData(parentId) {
      const { data } = await post(findNextRank, { parentId: parentId })
      return Promise.resolve(data)
    },
    async loadNode(node, resolve) {
      let data = null
      if (node.level === 0) {
        data = await this.loadRootData()
      } else {
        const parentId = node.level === 1 ? 0 : node.data.id
        data = await this.loadData(parentId)
        if (node.level === 1) {
          data = data.filter(kpi => kpi.feature == node.data.value)
        }
      }
      // data.forEach(item => (item.isLeaf = !item.hasChild)) // 设置节点是否为叶子节点
      resolve(data)
    },
    async handleNodeClick(data, node) {
      this.nodeInfo = node // 保存节点信息,用于点击右上角进行回显
      this.featureValue = this.nodeInfo.level === 1 ? data.value : ''
      this.closeMenu()
      if (node.level === 1) {
        // 确保value值正确传递
        const feature = data.value.toString()
        this.searchForm = {
          ...this.searchForm,
          id: null,
          feature: feature
        }
      } else {
        this.searchForm = {
          ...this.searchForm,
          id: data.id,
          feature: null
        }
      }
      this.handleSearch(true)
    },
    handleAddChild(node, data) {
      console.log('%c 左侧树node', 'color: red', node)
      console.log('%c 左侧树data', 'color: red', data)
      this.$refs.modalForm.add()
      this.$nextTick(() => {
        this.$refs.modalForm.formData.parentId = node.level === 1 ? 0 : data.id
        this.$refs.modalForm.formData.parentName = data.name
        this.$refs.modalForm.formData.rank = node.level //回显
        this.$refs.modalForm.formData.factory = data.factory
      })
      this.$refs.modalForm.visible = true
    },
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$nextTick(() => {
        this.$refs.modalForm.formData.parentId = row.parentId
        this.$refs.modalForm.formData.parentName = this.nodeInfo.parent.data.name
        // this.$refs.modalForm.formData.parentName = row.name
        this.$refs.modalForm.formData.features = row.feature.split(',')
      })
      this.$refs.modalForm.visible = true
    },
    rightAddNext() {
      //
      console.log(this.rightMenuData)
      this.handleAddChild(this.rightMenuData, this.rightMenuData.data)
    },
    rightAddBrother() {
      //
      this.handleAddChild(
        this.rightMenuData.parent,
        this.rightMenuData.parent.data
      )
    },
    rightMod() {
      //
      this.handleEdit(this.rightMenuData.data)
    },
    rightDel() {
      //
      this.handleDelete(this.rightMenuData.data)
    },
    oncontextmenu(e, data, node) {
      //
      this.rightMenuTop = e.clientY
      this.rightMenuLeft = e.clientX
      this.rightMenuVisible = true
      this.rightMenuData = node
    },
    closeMenu(e) {
      this.rightMenuVisible = false
      this.rightMenuData = null
    },
    handleRuleList: function(row) {
      this.$refs.modalRuleList.kid = row.id
      this.$refs.modalRuleList.kpiName = row.name
      this.$refs.modalRuleList.rank = row.rank
      this.$refs.modalRuleList.visible = true
    },
    filterHandler(value, row, column) {
      console.log(value, row, column)
      const property = column['property']
      return row[property] === value
    },
    filterChange(i) {
      console.log(i)
    },
    async refreshTreeNode() {
      if (this.nodeInfo) {
        // 刷新树组件
        if (this.searchForm.id) {
          await this.loadData(this.searchForm.id)
        } else {
          await this.loadData(this.nodeInfo.data.id)
        }
      }
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}
.tree-wrapper {
  overflow: auto;
  padding: 10px;
  border: 1px solid #eee;
  background: #fff;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.first-node {
  font-size: 18px;
}
/deep/ .el-tree-node {
  margin: 5px 0;
}
/deep/ .el-tree > .el-tree-node {
  margin: 15px 0 12px;
}
.contextmenu {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  background: #fff;
  font-size: 14px;
  color: #666;
  border: 1px solid #e9e9e9;
  min-width: 250px;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
  li {
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #f4f4f5;
    }
  }
  li:last-child {
    border-top: 1px solid #e9e9e9;
  }
}
</style>
