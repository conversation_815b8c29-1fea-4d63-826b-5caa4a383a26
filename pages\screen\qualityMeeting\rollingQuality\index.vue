<template>
  <div class="content">
    <div class="content-auto">
      <custom-table
        :title="'轧钢质量管理'"
        :setting="steelMaking"
        :url-list="steelMakingUrl.list"
        :url-save="steelMakingUrl.save"
        :height-auto="false"
        :height-set="maxHeight"
        :select-date="selectDate"
        :content-class="'content-auto'"
        @change="updateChart()"/>
    </div>
    <div class="content-hold" />
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="6"
          class="full-height col-1-5">
          <custom-table
            :title="'材原钢种一次合格率'"
            :setting="shape"
            :url-list="materialUrl.list"
            :url-save="materialUrl.save"
            :select-date="selectDate"
            :show-table="false"
            @change="getMaterial">
            <template v-slot:content>
              <div class="chart-wrapper">
                <div
                  class="chart"
                  style="padding-top: 12px;">
                  <div
                    class="operate-box">
                    <el-radio-group
                      v-model="materialChart.dateType"
                      size="mini"
                      class="screen-input"
                      @input="changeMaterial($event)">
                      <el-radio-button :label="0">日</el-radio-button>
                      <el-radio-button :label="1">月</el-radio-button>
                    </el-radio-group>
                  </div>
                  <single-bars-chart
                    ref="chart5"
                    :show-legend="false"
                    :bar-width="35"
                    :chart-data="materialChart.bar1"
                    :unit="'%'"
                    :x-data="materialChart.barX1"
                    @selected="getMaterialDetailed($event)"/>
                </div>
                <div
                  class="fail-reason"
                  style="height: 15%">
                  <template v-if="materialChart.failReason">
                    <span>未完成原因：</span><span>{{ materialChart.failReason || '无' }}</span>
                  </template>
                </div>
              </div>
            </template>
          </custom-table>
        </el-col>
        <el-col
          :span="6"
          class="full-height col-1-5">
          <custom-table
            :title="'性能一次合格率'"
            :setting="performance"
            :url-list="performanceUrl.list"
            :url-save="performanceUrl.save"
            :select-date="selectDate"
            :show-table="false"
            @change="getPerformance">
            <template v-slot:content>
              <div class="chart-wrapper">
                <div
                  class="chart"
                  style="padding-top: 12px;">
                  <div
                    class="operate-box">
                    <el-radio-group
                      v-model="performanceChart.dateType"
                      size="mini"
                      class="screen-input"
                      @input="changePerformance($event)">
                      <el-radio-button :label="0">日</el-radio-button>
                      <el-radio-button :label="1">月</el-radio-button>
                    </el-radio-group>
                  </div>
                  <single-bars-chart
                    ref="chart2"
                    :show-legend="false"
                    :bar-width="35"
                    :chart-data="performanceChart.bar1"
                    :unit="'%'"
                    :x-data="performanceChart.barX1"
                    @selected="getTargetDetailed($event)"/>
                </div>
                <div
                  class="fail-reason"
                  style="height: 15%">
                  <template v-if="performanceChart.failReason">
                    <span>未完成原因：</span><span>{{ performanceChart.failReason || '无' }}</span>
                  </template>
                </div>
              </div>
            </template>
          </custom-table>
        </el-col>
        <el-col
          :span="6"
          class="full-height col-1-5">

          <custom-table
            :title="'板形合格率'"
            :setting="shape"
            :url-list="shapeUrl.list"
            :url-save="shapeUrl.save"
            :select-date="selectDate"
            :show-table="false"
            @change="getShape">
            <template v-slot:content>
              <div class="chart-wrapper">
                <div
                  class="chart"
                  style="padding-top: 12px;height: 100%">
                  <div
                    class="operate-box">
                    <el-radio-group
                      v-model="shapeChart.dateType"
                      size="mini"
                      class="screen-input"
                      @input="changeShape($event)">
                      <el-radio-button :label="0">日</el-radio-button>
                      <el-radio-button :label="1">月</el-radio-button>
                    </el-radio-group>
                  </div>
                  <single-bars-chart
                    ref="chart3"
                    :show-legend="false"
                    :bar-width="35"
                    :chart-data="shapeChart.bar1"
                    :unit="'%'"
                    :x-data="shapeChart.barX1"/>
                </div>
                <div
                  class="fail-reason"
                  style="height: 15%">
                  <template v-if="shapeChart.failReason">
                    <span>未完成原因：</span><span>{{ shapeChart.failReason || '无' }}</span>
                  </template>
                </div>
              </div>
            </template>
          </custom-table>
        </el-col>
        <el-col
          :span="6"
          class="full-height col-1-5">
          <screen-border :title="'缺陷非计划排列前五（t）'">
            <div class="chart-wrapper">
              <div
                class="chart"
                style="padding-top: 12px;height: 100%"
              >
                <bars-chart
                  ref="chart4"
                  :bar-width="8"
                  :color="['#FF2855', '#F5B544', '#2772F0', '#218f1d','#2fd9d3', '#D45454']"
                  :chart-data="defectChart.bar1"
                  :x-data="defectChart.barX1"/>
              </div>
              <div
                class="fail-reason"
                style="height: 15%">
                <template />
              </div>
            </div>

          </screen-border>
        </el-col>
        <el-col
          :span="6"
          class="full-height col-1-5">
          <screen-border :title="'工艺报警(MES)'">
            <div class="chart-wrapper">
              <div
                class="chart">
                <bars-chart
                  ref="chart1"
                  :bar-width="14"
                  :color="['#FF2855', '#F5B544', '#2772F0']"
                  :chart-data="technologyChart.bar1"
                  :x-data="technologyChart.barX1"
                  @selected="getProcessAlarmDetailed($event)"/>
              </div>
              <div
                class="fail-reason"
                style="height: 15%">
                <template />
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <!--    材原钢种一次合格率-->
    <el-dialog
      :visible.sync="dialogVisible2"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          材原钢种一次合格率
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList2"
        class="center-table"
        border>
        <el-table-column
          property="PLT"
          label="生产厂">
          <template v-slot="{ row }">
            {{ codePlt[row['PLT']] }}
          </template>
        </el-table-column>
        <el-table-column
          property="修磨量"
          label="修磨量"/>
        <el-table-column
          property="切割量"
          label="切割量"/>
        <el-table-column
          property="协议量"
          label="协议量" />
        <el-table-column
          property="废品量"
          label="废品量"/>
        <el-table-column
          property="性能复样"
          label="性能复样"/>
        <el-table-column
          property="改判量"
          label="改判量"/>
        <el-table-column
          property="次品量"
          label="次品量"/>
        <el-table-column
          property="热处理挽救量"
          label="热处理挽救量"/>
        <el-table-column
          property="矫直量"
          label="矫直量"
          min-width="120"/>
      </el-table>
    </el-dialog>

    <el-dialog
      :visible.sync="dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          工艺报警详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList"
        class="center-table"
        border>
        <el-table-column
          property="生产厂"
          label="生产厂">
          <template v-slot="{ row }">
            {{ codePlt[row['生产厂']] }}
          </template>
        </el-table-column>
        <el-table-column
          property="牌号"
          label="牌号"/>
        <el-table-column
          property="板坯号"
          label="板坯号"/>
        <el-table-column
          property="工艺异常等级"
          label="工艺异常等级" >
          <template v-slot="{ row }">
            {{ row['工艺异常等级']==4?'一级重大':row['工艺异常等级']==3?'二级重大':row['工艺异常等级']==2?'三级重大':row['工艺异常等级']==1?'一般异常':'' }}
          </template>
        </el-table-column>
        <el-table-column
          property="宽度"
          label="宽度"/>
        <el-table-column
          property="厚度"
          label="厚度"/>
        <el-table-column
          property="实绩值"
          label="实绩值"/>
        <el-table-column
          property="下限"
          label="下限"/>
        <el-table-column
          property="上限"
          label="上限"/>
        <el-table-column
          property="报警内容"
          label="报警内容"
          min-width="120"/>
        <el-table-column
          property="备注"
          label="备注"/>
      </el-table>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogVisible1"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          性能一次合格率详情
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="flawList1"
        class="center-table"
        border>
        <el-table-column
          property="plt"
          label="生产厂"/>
        <el-table-column
          property="jyrq"
          label="检验日期"/>
        <el-table-column
          property="sxh"
          label="试样号"/>
        <el-table-column
          property="bzh"
          label="标准号" />
        <el-table-column
          property="hd"
          label="厚度"
          width="80"/>
        <el-table-column
          property="fyxm"
          label="复验项目"/>
        <el-table-column
          property="bhgz"
          label="不合格值"/>
        <el-table-column
          property="bbz"
          label="标准值"/>
        <el-table-column
          property="zl"
          label="重量"
          width="80"/>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  findDefectCorrectionByDate,
  findPerformancePassByDate,
  findPlateYgzFirstPassRate,
  findProcessAlarmByDate,
  findShapePassByDate,
  findSubjectData,
  pfmcFirstPassRateDetailed,
  plateYgzFirstPassRateDetails,
  processAlarm,
  processAlarmB1,
  processAlarmDetailed,
  qmsQualityQueryPlateFlaw,
  qmsQualitySavePlateFlaw,
  qmsRollingsteelQualityControlQuery,
  qmsRollingsteelQualityControlSave,
  saveDefectCorrection,
  savePerformancePass,
  savePlateYgzFirstPassRate,
  saveProcessAlarm,
  saveShapePass,
  surplusMaterials
} from '@/api/screen'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/qualityMeeting/component/custom-table'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import BarsChart from '@/pages/screen/qualityMeeting/component/bars-chart'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import * as _ from 'lodash'
import { math } from '@/lib/Math'
export default {
  name: 'rollingQuality',
  components: { ScreenBorder, BarsChart, SingleBarsChart, CustomTable },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      steelMakingUrl: {
        save: qmsRollingsteelQualityControlSave,
        list: qmsRollingsteelQualityControlQuery
      },
      maxHeight: null,
      steelMaking: [
        {
          keyQuery: 'factorytype',
          keySave: 'factoryType',
          label: '厂别',
          width: '120'
        },
        {
          keyQuery: 'checknorm',
          keySave: 'checkNorm',
          label: '检验规范'
        },
        {
          keyQuery: 'samplenorm',
          keySave: 'sampleNorm',
          label: '取样规范'
        },
        {
          keyQuery: 'cleanerproduction',
          keySave: 'cleanerProduction',
          label: '清洁生产'
        },
        {
          keyQuery: 'drift',
          keySave: 'drift',
          label: '瓢曲发生量',
          inputType: 'textarea',
          split: '；'
        },
        {
          keyQuery: 'surfacedefectunplan',
          keySave: 'surfaceDefectUnplan',
          label: '表面缺陷原始非计划',
          inputType: 'textarea'
        },
        {
          keyQuery: 'repair',
          keySave: 'repair',
          label: '钢板修复挽救',
          width: '160'
        },
        {
          keyQuery: 'changesentencekeyvar',
          keySave: 'changeSentenceKeyVar',
          label: '重点品种（镍系/管线）原始非计划',
          inputType: 'textarea'
        },
        {
          keyQuery: 'other',
          keySave: 'other',
          label: '镍系钢合格率',
          width: '130'
        }
      ],
      technologyUrl: {
        save: saveProcessAlarm,
        list: findProcessAlarmByDate
      },
      technology: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'primary',
          keySave: 'primary',
          label: '一级'
        },
        {
          keyQuery: 'secondary',
          keySave: 'secondary',
          label: '二级'
        },
        {
          keyQuery: 'tertiary',
          keySave: 'tertiary',
          label: '三级'
        }
      ],
      flawList: [],
      dialogVisible: false,
      flawList1: [],
      dialogVisible1: false,
      flawList2: [],
      dialogVisible2: false,
      technologyChart: {
        bar1: [],
        barX1: []
      },
      performanceUrl: {
        save: savePerformancePass,
        list: findPerformancePassByDate
      },
      performance: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'value',
          keySave: 'Value',
          label: '实际值'
        },
        {
          keyQuery: 'targetvalue',
          keySave: 'targetValue',
          label: '目标值1'
        },
        {
          keyQuery: 'targetvalue2',
          keySave: 'targetValue2',
          label: '目标值2'
        },
        {
          keyQuery: 'description',
          keySave: 'description',
          label: '未完成原因'
        },
        {
          keyQuery: 'monthdata',
          keySave: 'monthData',
          label: '月数据',
          show: false
        }
      ],
      materialRateChart: {
        bar1: [],
        barX1: ['中厚板卷厂', '宽厚板厂', '中板厂'],
        failReason: '',
        dateType: 0
      },
      performanceChart: {
        bar1: [],
        barX1: [],
        failReason: '',
        dateType: 0
      },
      materialUrl: {
        save: savePlateYgzFirstPassRate,
        list: findPlateYgzFirstPassRate
      },
      materialChart: {
        dataList: [],
        bar1: [],
        barX1: [],
        failReason: '',
        dateType: 0
      },
      shapeUrl: {
        save: saveShapePass,
        list: findShapePassByDate
      },
      shape: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'value',
          keySave: 'Value',
          label: '实际值'
        },
        {
          keyQuery: 'targetvalue',
          keySave: 'targetValue',
          label: '目标值'
        },
        {
          keyQuery: 'description',
          keySave: 'description',
          label: '未完成原因'
        },
        {
          keyQuery: 'monthdata',
          keySave: 'monthData',
          label: '月数据',
          show: false
        }
      ],
      shapeChart: {
        dataList: [],
        bar1: [],
        barX1: [],
        failReason: '',
        dateType: 0
      },
      defectUrl: {
        save: saveDefectCorrection,
        list: findDefectCorrectionByDate
      },
      defect: [
        {
          keyQuery: 'rollingmill',
          keySave: 'rollingMill',
          label: '轧钢厂'
        },
        {
          keyQuery: 'surface',
          keySave: 'surface',
          label: '表面'
        },
        {
          keyQuery: 'plateshape',
          keySave: 'plateShape',
          label: '板形'
        },
        {
          keyQuery: 'dimension',
          keySave: 'dimension',
          label: '尺寸'
        }
      ],
      defectChart: {
        bar1: [],
        barX1: []
      },
      facList: [
        {
          code: 'B1',
          name: '第一炼钢厂'
        },
        {
          code: 'C1',
          name: '中厚板卷厂'
        },
        {
          code: 'C2',
          name: '宽厚板厂'
        },
        {
          code: 'C3',
          name: '中板厂'
        }
      ],
      facList2: [
        {
          code: 'C1',
          name: '中厚板卷厂'
        },
        {
          code: 'C2',
          name: '宽厚板厂'
        },
        {
          code: 'C3',
          name: '中板厂'
        },
        {
          code: 'F1',
          name: '事业部'
        }
      ]
    }
  },
  computed: {
    showTime: function() {
      return '（' + this.$moment(this.cDate).format('MM月DD日') + '）'
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.loadData()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.loadData()
  },
  mounted() {
    this.maxHeight = window.innerHeight - 500
  },
  methods: {
    loadData() {
      this.getTechnology()
    },
    getTechnology(data) {
      const params = {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD')
      }
      Promise.all([post(processAlarm, params)]).then(([res1]) => {
        const all = res1.data[0]
        this.technologyChart.bar1 = [
          // 中板厂一级: 0
          // 中板厂三级: 0
          // 中板厂二级: 0
          // 宽厚板厂一级: 47
          // 宽厚板厂三级: 2
          // 宽厚板厂二级: 0
          // 板卷厂一级: 58
          // 板卷厂三级: 2
          // 板卷厂二级: 0
          // 炼钢厂一级: 0
          // 炼钢厂三级: 0
          // 炼钢厂二级: 0
          {
            name: '一级',
            data: [
              all['炼钢厂一级'],
              all['板卷厂一级'],
              all['宽厚板厂一级'],
              all['中板厂一级']
            ]
          },
          {
            name: '二级',
            data: [
              all['炼钢厂二级'],
              all['板卷厂二级'],
              all['宽厚板厂二级'],
              all['中板厂二级']
            ]
          },
          {
            name: '三级',
            data: [
              all['炼钢厂三级'],
              all['板卷厂三级'],
              all['宽厚板厂三级'],
              all['中板厂三级']
            ]
          }
        ]
      })
      this.technologyChart.barX1 = this.facList.map(item => item.name)

      post(surplusMaterials, params).then(res => {
        let arr = res.data.rows.slice(0, res.data.rows.length - 1).map(item => {
          return {
            num: item['数量'],
            reason: item['余材原因'],
            PLT: item['PLT']
          }
        })
        const types = _.groupBy(arr, 'reason')
        const tempArr = _.orderBy(
          Object.keys(types).map(item => {
            // console.log(item)
            const num = Number(_.sumBy(types[item], 'num').toFixed(1))
            return {
              num,
              reason: item,
              PLT: 'F1'
            }
          }),
          ['num'],
          ['desc']
        )
        arr = arr.concat(tempArr)
        const group = _.groupBy(arr, 'PLT')
        this.defectChart.bar1 = [
          {
            name: '第一',
            data: this.facList2.map(fac => {
              // console.log(group[fac.code])
              return {
                value: group[fac.code][0] ? group[fac.code][0].num : '',
                name: group[fac.code][0] ? group[fac.code][0].reason : ''
              }
            })
          },
          {
            name: '第二',
            data: this.facList2.map(fac => {
              return {
                value: group[fac.code][1] ? group[fac.code][1].num : '',
                name: group[fac.code][1] ? group[fac.code][1].reason : ''
              }
            })
          },
          {
            name: '第三',
            data: this.facList2.map(fac => {
              return {
                value: group[fac.code][2] ? group[fac.code][2].num : '',
                name: group[fac.code][2] ? group[fac.code][2].reason : ''
              }
            })
          },
          {
            name: '第四',
            data: this.facList2.map(fac => {
              return {
                value: group[fac.code][3] ? group[fac.code][3].num : '',
                name: group[fac.code][3] ? group[fac.code][3].reason : ''
              }
            })
          },
          {
            name: '第五',
            data: this.facList2.map(fac => {
              return {
                value: group[fac.code][4] ? group[fac.code][4].num : '',
                name: group[fac.code][4] ? group[fac.code][4].reason : ''
              }
            })
          }
        ]
        this.defectChart.barX1 = this.facList2.map(item => item.name)
      })
    },

    // 工艺报警详情
    getProcessAlarmDetailed(data) {
      console.log(data)
      const plt = this.pltCode[
        this.technologyChart.barX1[data.fromActionPayload.dataIndexInside]
      ]
      if (!plt) return
      post(processAlarmDetailed, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        plt
      }).then(res => {
        this.flawList = res.data
        this.dialogVisible = true
      })
    },
    // 一次合格率详情
    getTargetDetailed(data) {
      console.log(data)
      const plt = this.pltCode[
        this.performanceChart.barX1[data.fromActionPayload.dataIndexInside]
      ]
      if (!plt) return
      post(pfmcFirstPassRateDetailed, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        plt
      }).then(res => {
        this.flawList1 = res.data
        this.dialogVisible1 = true
      })
    },

    getMaterialDetailed(data) {
      const plt = this.pltCode[
        this.performanceChart.barX1[data.fromActionPayload.dataIndexInside]
      ]
      if (!plt) return
      post(plateYgzFirstPassRateDetails, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        plt
      }).then(res => {
        console.log(res)
        this.flawList2 = res.rows
        this.dialogVisible2 = true
      })
    },

    getPerformance(data) {
      this.performanceChart.dataList = data
      this.changePerformance(0)
    },
    changePerformance($event) {
      const data = this.performanceChart.dataList
      this.performanceChart.bar1 = data.map(item => {
        const value =
          $event === 0 ? Number(item['Value']) : Number(item['monthData'] || 0)
        return {
          value: value,
          plan: Number(item.targetValue2),
          warning:
            value >= Number(item.targetValue2) &&
            value < Number(item.targetValue),
          finished: value >= Number(item.targetValue2)
        }
      })
      this.performanceChart.barX1 = data.map(item => item.rollingMill)
      this.performanceChart.failReason =
        $event === 0
          ? data
              .filter(item => item.description)
              .map(item => item.rollingMill + '：' + item.description)
              .join('；')
          : ''
    },
    getShape(data) {
      this.shapeChart.dataList = data
      this.changeShape(0)
    },
    changeShape($event) {
      const data = this.shapeChart.dataList
      this.shapeChart.bar1 = data.map(item => {
        return {
          value:
            $event === 0
              ? Number(item['Value'])
              : Number(item['monthData'] || 0),
          plan: Number(item.targetValue),
          finished:
            ($event === 0
              ? Number(item['Value'])
              : Number(item['monthData'] || 0)) >= Number(item.targetValue)
        }
      })
      this.shapeChart.barX1 = data.map(item => item.rollingMill)
      this.shapeChart.failReason =
        $event === 0
          ? data
              .filter(item => item.reasonNotCompl)
              .map(item => item.rollingMill + '：' + item.reasonNotCompl)
              .join('；')
          : ''
    },
    getMaterial(data) {
      this.materialChart.dataList = data
      this.changeMaterial(0)
    },
    changeMaterial($event) {
      const data = this.materialChart.dataList
      this.materialChart.bar1 = data.map(item => {
        return {
          value:
            $event === 0
              ? Number(item['Value'])
              : Number(item['monthData'] || 0),
          plan: Number(item.targetValue),
          finished:
            ($event === 0
              ? Number(item['Value'])
              : Number(item['monthData'] || 0)) >= Number(item.targetValue)
        }
      })
      this.materialChart.barX1 = data.map(item => item.rollingMill)
      this.materialChart.failReason =
        $event === 0
          ? data
              .filter(item => item.reasonNotCompl)
              .map(item => item.rollingMill + '：' + item.reasonNotCompl)
              .join('；')
          : ''
    },
    updateChart() {
      this.$refs.chart5.resizeChart()
      this.$refs.chart1.resizeChart()
      this.$refs.chart2.resizeChart()
      this.$refs.chart3.resizeChart()
      this.$refs.chart4.resizeChart()
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-auto {
    overflow: hidden;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
/deep/ .col-1-5 {
  width: 20%;
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  overflow: auto;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    padding-top: 12px;
    flex: 1;
    overflow: hidden;
    .operate-box {
      position: absolute;
      top: 4px;
      right: 4px;
      z-index: 1;
    }
  }
}
</style>
