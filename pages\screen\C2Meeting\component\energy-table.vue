<template>
  <div class="full-height">
    <div
      v-if="showTable"
      ref="table1"
      class="scroll-wrapper">
      <el-table
        v-loading="loading"
        :data="showGridData"
        :max-height="maxHeight"
        :size="'medium'"
        :span-method="spanMethod"
        class="center-table font-big-table"
        border>
        <template
          v-for="(item, index) in setting">
          <template v-if="item.show !== false">
            <el-table-column
              v-if="item.children"
              :key="index"
              :width="item.width || ''"
              :property="item.keySave"
              :label="item.label"
              :align="item.align">
              <template
                v-for="(cItem, cIndex) in item.children">
                <template v-if="item.inputType === 'textarea'">
                  <el-table-column
                    :key="cIndex"
                    :width="cItem.width || ''"
                    :property="cItem.keySave"
                    :label="cItem.label"
                    :align="cItem.align">
                    <template v-slot="{ row }">
                      <div
                        slot="content"
                        v-html="formatText(row[cItem.keySave], cItem.split)"
                      />
                    </template>
                  </el-table-column>
                </template>
                <template v-else>
                  <el-table-column
                    :key="cIndex"
                    :width="cItem.width || ''"
                    :property="cItem.keySave"
                    :label="cItem.label +(cItem.max ? ' (' + cItem.max + ')' : '') "
                    :align="cItem.align">
                    <template v-slot="{ row }">
                      <span :class="{'red': isColor(row, cItem.max, cItem.keySave)}">
                        {{ row[cItem.keySave] }}
                      </span>
                    </template>
                  </el-table-column>
                </template>
              </template>
            </el-table-column>
            <template v-else>
              <el-table-column
                v-if="item.type === 'index'"
                :key="index"
                :label="item.label"
                type="index"
                width="100"
              />
              <template v-else>
                <template v-if="item.inputType === 'textarea'">
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label"
                    :align="item.align">
                    <template v-slot="{ row }">
                      <div
                        slot="content"
                        v-html="formatText(row[item.keySave], item.split)"
                      />
                    </template>
                  </el-table-column>
                </template>
                <template v-else>
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label + (item.max ? ' (' + item.max + ')' : '') "
                    :align="item.align">
                    <template v-slot="{ row }">
                      <span :class="{'red': isColor(row, item.max, item.keySave)}">
                        {{ row[item.keySave] }}
                      </span>
                    </template>
                  </el-table-column>
                </template>
              </template>
            </template>
          </template>
        </template>
      </el-table>
      <!-- 新增的ECharts柱状图组件 -->
      <div class="chart-container">
        <div
          ref="echartBar"
          class="chart"/>
        <div
          ref="echartBarRight"
          class="chart"/>
      </div>

    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate" />
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event)">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline" />
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportTable">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-document-checked" />
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="gridData"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.children"
                :key="index"
                :width="item.width || ''"
                :label="item.label">
                <template
                  v-for="(cItem, cIndex) in item.children">
                  <el-table-column
                    :key="cIndex"
                    :width="cItem.width || ''"
                    :property="cItem.keySave"
                    :label="cItem.label">
                    <template v-slot="{ row }">
                      <template v-if="cItem.inputType === 'textarea'">
                        <el-input
                          v-model="row[cItem.keySave]"
                          :rows="4"
                          type="textarea"
                        />
                      </template>
                      <template v-else>
                        <el-input v-model="row[cItem.keySave]" />
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </el-table-column>
              <template v-else>
                <el-table-column
                  v-if="item.type === 'index'"
                  :key="index"
                  :label="item.label"
                  type="index"
                  width="100"
                />
                <template v-else>
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label">
                    <template v-slot="{ row }">
                      <template v-if="item.inputType === 'textarea'">
                        <el-input
                          v-model="row[item.keySave]"
                          :rows="4"
                          type="textarea"
                        />
                      </template>
                      <template v-else>
                        <el-input v-model="row[item.keySave]" />
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </template>
            </template>
          </template>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index)">
                <el-icon class="el-icon-delete" />
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData()">
          <el-icon class="el-icon-circle-plus-outline" />
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'" />
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import * as _ from 'lodash'
import * as echarts from 'echarts'
import { post } from '@/lib/Util'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'

export default {
  name: 'energy-table',
  components: { ScreenBorder },
  props: {
    active: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    setting: {
      type: Array,
      default: function() {
        return []
      }
    },
    mergeSet: {
      type: Object,
      default: function() {
        return {}
      }
    },
    selectDate: {
      type: String,
      default: ''
    },
    urlList: {
      type: String,
      default: ''
    },
    urlSave: {
      type: String,
      default: ''
    },
    showTable: {
      type: Boolean,
      default: true
    },
    showEdit: {
      type: Boolean,
      default: true
    },
    heightAuto: {
      type: Boolean,
      default: true
    }
  },
  data: function() {
    return {
      cDate: '',
      loading: false,
      dialogVisible: false,
      showGridData: [],
      gridData: [],
      importDate: null,
      importDateVisible: false,
      importFunName: '',
      mergeArr: [],
      spanArr: {},
      position: 0,
      maxHeight: null,
      ABC: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'K'
      ]
    }
  },
  computed: {
    canEditQuality: function() {
      return (
        moment().format('yyyy-MM-DD') <=
        moment(this.cDate)
          .subtract(-1, 'day')
          .format('yyyy-MM-DD')
      )
    },
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'day')
        .format('yyyy-MM-DD')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getData()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.getData()
  },
  mounted() {
    this.fetchData()
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    fetchData() {
      console.log(this.active)
      let url, urlRight

      // 原先的接口地址
      if (this.active === '1') {
        url = 'ems/generator/indexELE/eleConsumeCurve'
        urlRight = 'ems/generator/indexELE/eleConsumeCurveMonth' // 新的接口地址
      } else if (this.active === '2') {
        url = 'ems/generator/indexELE/htrConsumeCurve'
        urlRight = 'ems/generator/indexELE/htrConsumeCurveMonth'
      } else if (this.active === '3') {
        url = 'ems/generator/indexELE/htpConsumeCurve'
        urlRight = 'ems/generator/indexELE/htpConsumeCurveMonth'
      }

      // 获取左侧图表数据
      post(url).then(res => {
        let data = res.returnSumMap
        this.$nextTick(() => this.initEChartBarLeft(data)) // 传递左侧图表数据
      })

      // 获取右侧图表数据
      post(urlRight).then(res => {
        let dataRight = res.returnSumMap
        this.$nextTick(() => this.initEChartBarRight(dataRight)) // 传递右侧图表数据
      })
    },

    initEChartBarLeft(data) {
      const chartDomLeft = this.$refs.echartBar
      const myChartLeft = echarts.init(chartDomLeft)

      // 根据不同的 active 值构建不同的 series
      let seriesLeft = []

      if (this.active === '1') {
        seriesLeft = [
          {
            name: '主机',
            type: 'line',
            itemStyle: {
              normal: {
                color: '#3391ff',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.maindriveTos
          },
          {
            name: '板加',
            type: 'line',
            itemStyle: {
              normal: {
                color: '#55c6d4',
                label: {
                  show: false,
                  textStyle: {
                    color: '#fff'
                  },
                  position: 'insideTop',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.banjiaTos
          },
          {
            name: '高压水除鳞',
            type: 'line',
            itemStyle: {
              normal: {
                color: '#ff9800',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.descalingTos
          },
          {
            name: '精整',
            type: 'line',
            itemStyle: {
              normal: {
                color: '#e70e0e',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.jingzhengTos
          },
          {
            name: '除尘',
            type: 'line',
            itemStyle: {
              normal: {
                color: '#90cf22',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.dustremovalTos
          },
          {
            name: '热轧',
            type: 'line',
            itemStyle: {
              normal: {
                color: '#a146b0',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.rezhaTos
          }
          // 其他系列...
        ]
      } else if (this.active === '2') {
        seriesLeft = [
          {
            name: '煤气',
            type: 'line',
            itemStyle: {
              normal: {
                color: '#3391ff',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.gasTos
          },
          {
            name: '水',
            type: 'line',
            itemStyle: {
              normal: {
                color: '#55c6d4',
                label: {
                  show: false,
                  textStyle: {
                    color: '#fff'
                  },
                  position: 'insideTop',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.waterTos
          }
          // 其他系列...
        ]
      } else if (this.active === '3') {
        seriesLeft = [
          {
            name: '电',
            type: 'line',
            itemStyle: {
              normal: {
                color: '#3391ff',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.eleTos
          },
          {
            name: '煤气',
            type: 'line',
            itemStyle: {
              normal: {
                color: '#55c6d4',
                label: {
                  show: false,
                  textStyle: {
                    color: '#fff'
                  },
                  position: 'insideTop',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.gasTos
          },
          {
            name: '水',
            type: 'line',
            itemStyle: {
              normal: {
                color: '#ff9800',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.waterTos
          }
        ]
      }
      // 构建完整的 option
      const optionLeft = {
        tooltip: {
          trigger: 'axis',
          textStyle: {
            align: 'left'
          },
          axisPointer: {
            lineStyle: {
              color: 'rgba(255,255,255,0)',
              width: '1'
            },
            crossStyle: {
              color: 'rgba(255,255,255,0)',
              width: '1'
            }
          }
        },
        grid: {
          borderWidth: 0,
          top: '30%',
          bottom: '15%',
          right: '8%',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          x: 'center',
          top: '10%',
          itemWidth: 15,
          itemHeight: 8,
          textStyle: {
            color: '#fff',
            fontSize: 25
          }
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            axisLine: {
              show: false,
              lineStyle: {
                color: '#fff'
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitArea: {
              show: false
            },
            axisLabel: {
              interval: 0
            },
            data: data.xZhou || []
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dotted' //'dotted'虚线 'solid'实线
              }
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#fff'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: seriesLeft // 使用动态构建的 series
      }
      myChartLeft.setOption(optionLeft)
    },
    initEChartBarRight(data) {
      const chartDomRight = this.$refs.echartBarRight
      const myChartRight = echarts.init(chartDomRight)

      let seriesRight = []

      // 根据不同的 active 值构建不同的 series
      if (this.active === '1') {
        seriesRight = [
          {
            name: '板加',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#55c6d4',
                label: {
                  show: false,
                  textStyle: {
                    color: '#fff'
                  },
                  position: 'insideTop',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.banjiaTos
          },
          {
            name: '高压水除鳞',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#ff9800',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.descalingTos
          },
          {
            name: '精整',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#e70e0e',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.jingzhengTos
          },
          {
            name: '除尘',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#90cf22',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.dustremovalTos
          },
          {
            name: '热轧',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#a146b0',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.rezhaTos
          },
          {
            name: '主线',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#e70e0e',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.quanchangTos
          }
          // 其他系列...
        ]
      } else if (this.active === '2') {
        seriesRight = [
          {
            name: '煤气',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#3391ff',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.gasTos
          },
          {
            name: '水',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#55c6d4',
                label: {
                  show: false,
                  textStyle: {
                    color: '#fff'
                  },
                  position: 'insideTop',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.waterTos
          }
          // 其他系列...
        ]
      } else if (this.active === '3') {
        seriesRight = [
          {
            name: '电',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#3391ff',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.eleTos
          },
          {
            name: '煤气',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#55c6d4',
                label: {
                  show: false,
                  textStyle: {
                    color: '#fff'
                  },
                  position: 'insideTop',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.gasTos
          },
          {
            name: '水',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#ff9800',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter: function(p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: data.waterTos
          }
        ]
      }

      // 构建右侧图表的 option
      const optionRight = {
        tooltip: {
          trigger: 'axis',
          textStyle: {
            align: 'left'
          },
          axisPointer: {
            lineStyle: {
              color: 'rgba(255,255,255,0)',
              width: '1'
            },
            crossStyle: {
              color: 'rgba(255,255,255,0)',
              width: '1'
            }
          }
        },
        grid: {
          borderWidth: 0,
          top: '30%',
          bottom: '15%',
          right: '8%',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          x: 'center',
          top: '10%',
          itemWidth: 15,
          itemHeight: 8,
          textStyle: {
            color: '#fff',
            fontSize: 25
          }
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            axisLine: {
              show: false,
              lineStyle: {
                color: '#fff'
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitArea: {
              show: false
            },
            axisLabel: {
              interval: 0
            },
            data: data.xZhou || []
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dotted' //'dotted'虚线 'solid'实线
              }
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#fff'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: seriesRight // 使用动态构建的 series
      }

      myChartRight.setOption(optionRight)
    },
    isColor(row, max, keyName) {
      if (!max) return false
      if (row.classes === '当天汇总' || row.classes === '天汇总') {
        return this.judgeStatus(row.classes, '日单耗', max, keyName)
      }
      return this.judgeStatus(row.classes, '班单耗', max, keyName)
    },
    judgeStatus(classes, name, max, keyName) {
      const match = this.showGridData.find(
        item => item.classes === classes && item.name === name
      )
      if (!match) {
        return false
      }
      return match[keyName] > max
    },

    // 导入文件
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = this.ABC[index]
      })
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, obj)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.gridData = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    // 导出表格
    exportTable() {
      const obj = {}
      this.setting.forEach((item, index) => {
        obj[item.keySave] = item.label
      })
      const data = [obj].concat(
        _.cloneDeep(
          this.gridData.map(item => {
            const objRow = {}
            this.setting.forEach(set => {
              objRow[set.keySave] = item[set.keySave]
            })
            return objRow
          })
        )
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `${this.title}（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 获取数据
    getData() {
      this.loading = true
      post(this.urlList, {
        setDate: this.prevDate,
        date: this.prevDate
      }).then(res => {
        //
        this.loading = false
        this.showGridData = res.data.map(item => {
          const obj = {}
          this.setting.forEach(set => {
            if (set.children && set.children.length) {
              set.children.forEach(child => {
                obj[child.keySave] = item[child.keyQuery]
              })
            } else {
              if (item['classes'] === '热装比' && set.keyQuery === 'name') {
                if (
                  item['team'] === '250℃以上热装率' ||
                  item['team'] === '400℃以上热装率' ||
                  item['team'] === '平均温度'
                ) {
                  obj[set.keySave] = `${item['value']}`
                } else {
                  obj[set.keySave] = `${item['wgt']} / ${item['totalWgt']} / ${(
                    (item['wgt'] / item['totalWgt']) *
                    100
                  ).toFixed(2)}`
                }
              } else {
                obj[set.keySave] = item[set.keyQuery]
              }
            }
          })
          return obj
        })
        this.gridData = _.cloneDeep(this.showGridData)
        this.$nextTick(() => {
          this.$emit('change', this.showGridData)
        })
      })
    },
    // 更新数据
    saveData() {
      this.loading = true
      // 数据信息
      const params = {
        setDate: this.cDate,
        data: this.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(this.urlSave, params).then(res => {
        //
        this.loading = false
        if (res.status == 1) {
          this.$message.success('保存成功！')
          this.dialogVisible = false
          this.getData()
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    // 导入日期数据
    importData(date) {
      post(this.urlList, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.gridData = res.data.map(item => {
          const obj = {}
          this.setting.forEach(set => {
            obj[set.keySave] = item[set.keyQuery]
          })
          return obj
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 执行导入
    importHistoryData() {
      this.importData(this.importDate)
      this.importDateVisible = false
    },
    // 下拉菜单指令
    handleProcessedCommand(command) {
      if (command === 'yesterday') {
        this.importData(
          this.$moment(this.cDate)
            .subtract(1, 'day')
            .format('yyyy-MM-DD')
        )
      } else {
        this.importDate = this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
        this.importDateVisible = true
      }
    },
    // 数据管理
    clearGridData() {
      this.gridData = []
    },
    addGridData() {
      this.gridData.push({})
    },
    delGridData(index) {
      this.gridData.splice(index, 1)
    },
    // 日期改变推送
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    // 计算需要合并的单元格
    formatSpanData(data) {
      this.mergeArr.forEach(keyName => {
        this.spanArr[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
    },
    // 合并单元格
    handleObjectSpan({ row, column, rowIndex, columnIndex }) {
      // 需要合并的列
      // [0, 1, 2].includes(columnIndex ), 表示合并前三列
      if (this.mergeArr.includes(column.property)) {
        const _row = this.spanArr[column.property][rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 生成带换行数据
    formatText(text, split) {
      if (!text) {
        return ''
      }
      if (split) text = text.split(split).join('\n')
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },
    // 计算高度
    calculate() {
      this.showTable &&
        this.heightAuto &&
        (this.maxHeight = this.$refs.table1.offsetHeight)
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (!Object.keys(this.mergeSet).length) return [1, 1]
      console.log(rowIndex + '-' + columnIndex)
      if (this.mergeSet[rowIndex + '-' + columnIndex] !== undefined) {
        console.log('==', rowIndex + '-' + columnIndex)
        return !this.mergeSet[rowIndex + '-' + columnIndex]
          ? [0, 0]
          : this.mergeSet[rowIndex + '-' + columnIndex]
      }
      return [1, 1]
    }
  }
}
</script>

<style scoped lang="less">
// 大屏按钮
.screen-btn {
  display: inline-block;
  min-width: 68px;
  height: 28px;
  padding: 0 5px;
  background: rgba(31, 198, 255, 0.3);
  border: 1px solid #1fc6ff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  color: #fff;

  &:hover {
    background: rgba(31, 198, 255, 0.6);
    border: 1px solid #1fc6ff;
  }
}

.scroll-wrapper {
  overflow: auto;
  flex: 1;
}
.full-height {
  display: flex;
  flex-direction: column;
}

.chart-container {
  display: flex; /* 使用flex布局 */
  margin-top: 20px; /* 适当调整间距 */
}
.chart {
  width: 50%; /* 各占50%的宽度 */
  height: 400px;
}

.red {
  color: #ff0000;
}

/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}

/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
</style>
