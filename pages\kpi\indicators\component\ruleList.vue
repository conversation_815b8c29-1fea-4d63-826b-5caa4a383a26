<template>
  <el-dialog
    :title="'指标预警规则管理'"
    :visible.sync="visible"
    :width="'1200px'"
    v-bind="$attrs"
    :append-to-body="true"
    @close="onClose"
    @open="onOpen"
    v-on="$listeners"
  >
    <div class="page-content">
      <div class="page-operate">
        <div>
          当前指标：{{ kpiName }}
        </div>
        <div>
          <el-button
            v-command="'/kpi/indicators/add'"
            icon="el-icon-circle-plus-outline"
            size="small"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>
        </div>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        :size="size"
        border
        style="width: 100%"
      >
        <el-table-column
          label="序号"
          type="index"
          width="50"
        />
        <el-table-column
          label="规则类型"
          prop="ruleSign"
          min-width="100"
        >
          <template
            v-slot="{row}"
          >
            {{ getValue(ruleType, row.ruleSign).label }}
          </template>
        </el-table-column>
        <el-table-column
          label="预警逻辑"
          prop="logic"
          width="100"
          show-overflow-tooltip
        >
          <template
            v-slot="{row}"
          >
            {{ getValue(earlyWarningLogic, row.logic).label }}
          </template>
        </el-table-column>
        <el-table-column
          label="预警规则"
          prop="rule"
          width="120"
          show-overflow-tooltip
        >
          <template
            v-slot="{row}"
          >
            {{ getValue(earlyWarningRule, row.rule).label }}
          </template>
        </el-table-column>
        <el-table-column
          label="预警参数"
          prop="warningParam"
          width="100"
          show-overflow-tooltip
        >
          <template
            v-slot="{row}"
          >
            {{ row.warningParam }}
          </template>
        </el-table-column>
        <el-table-column
          label="比较值说明"
          prop="remarks"
          min-width="100"
          show-overflow-tooltip
        >
          <template
            v-slot="{row}"
          >
            {{ row.remarks }}
          </template>
        </el-table-column>
        <el-table-column
          label="目标值读取方式"
          prop="targetGetType"
          min-width="100"
          show-overflow-tooltip
        >
          <template
            v-slot="{row}"
          >
            {{ getValue(targetGetType, row.targetGetType).label }}
          </template>
        </el-table-column>
        <el-table-column
          label="关联目标ID"
          prop="linkedKpiId"
          width="100"
        >
          <template
            v-slot="{row}"
          >
            {{ row.linkedKpiId }}
          </template>
        </el-table-column>
        <el-table-column
          label="基础目标值"
          prop="basicValue"
          width="100"
        >
          <template
            v-slot="{row}"
          >
            {{ row.basicValue }}
          </template>
        </el-table-column>
        <el-table-column
          label="目标值倍数"
          prop="multiple"
          width="100"
        >
          <template
            v-slot="{row}"
          >
            {{ row.multiple }}
          </template>
        </el-table-column>
        <el-table-column
          label="是否核心指标"
          prop="multiple"
          width="100"
        >
          <template
            v-slot="{row}"
          >
            <el-tag
              :type="row.isCoreRule ? 'success' : 'primary'"
              disable-transitions
            >{{ row.isCoreRule ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="120"
        >
          <template
            v-slot="scope"
          >
            <span v-command="'/kpi/indicators/edit'">
              <el-button
                size="small"
                type="text"
                @click="handleEdit(scope)"
              >编辑
              </el-button>
              <el-divider
                direction="vertical" />
            </span>
            <el-button
              v-command="'/kpi/indicators/delete'"
              slot="reference"
              type="text"
              @click="handleDelete(scope.row)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <RuleEdit
        ref="modalForm"
        :kid="kid"
        :kpi-name="kpiName"
        :rank="rank"
        :has-day-code="hasDayCode"
        @success="onOpen"
      />
    </div>
    <div slot="footer">
      <el-button
        type="primary"
        @click="visible = false"
      >关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import RuleEdit from './ruleEdit'
import listMixins from '@/mixins/ListMixins'
import { post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'
import {
  deleteKpiWarningRules,
  findRulesByKid,
  findRulesByManagerId
} from '@/api/kpi'
export default {
  name: 'RuleList',
  components: {
    RuleEdit
  },
  mixins: [listMixins],
  data: () => {
    return {
      kid: null,
      kpiName: null,
      rank: null,
      visible: false,
      hasDayCode: false, // 当前编辑状态是否提示日取值覆盖信息
      url: {
        list: findRulesByManagerId, //分页接口地址,
        delete: deleteKpiWarningRules
      },
      ruleType: ENUM.ruleType,
      earlyWarningLogic: ENUM.earlyWarningLogic,
      earlyWarningRule: ENUM.earlyWarningRule,
      targetGetType: ENUM.targetGetType,
      statusList: [
        {
          value: 0,
          label: '正常',
          type: 'success'
        },
        {
          value: 1,
          label: '废弃',
          type: 'warning'
        }
      ]
    }
  },
  watch: {},
  created() {},
  methods: {
    async handleSearch(reset = false) {},
    getValue: function(list = [], value) {
      return list.find(item => item.value == value)
        ? list.find(item => item.value == value)
        : {}
    },
    onOpen() {
      post(findRulesByKid, { kid: this.kid }).then(res => {
        this.tableData = res.data
      })
    },
    handleEdit: function(scope) {
      this.hasDayCode = !!this.tableData
        .filter((item, index) => {
          return index !== scope.$index
        })
        .find(item => !!item.dayCode)
      this.$refs.modalForm.edit(scope.row)
      this.$refs.modalForm.visible = true
    },
    handleAdd: function() {
      this.hasDayCode = !!this.tableData.find(item => !!item.dayCode)
      this.$refs.modalForm.add()
      this.$refs.modalForm.visible = true
    },
    handleDelete: function(data) {
      if (!this.url.delete) {
        this.$message('请设置url.delete属性!')
        return
      }
      this.$confirm('是否确认删除此数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除操作
        post(this.url.delete, { id: data.id }).then(res => {
          this.onOpen()
          this.$message.success('删除成功！')
        })
      })
    },
    onClose() {
      this.tableData = []
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.page-title {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  margin-bottom: 15px;
}

.page-content {
  font-size: 18px;
  background: #fff;
  box-shadow: 0 0 10px rgba(117, 116, 116, 0.1);
}

.page-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .operate-icon {
    margin-left: 8px;
  }
}

.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}
.tree-wrapper {
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.first-node {
  font-size: 18px;
}
/deep/ .el-tree-node {
  margin: 5px 0;
}
/deep/ .el-tree > .el-tree-node {
  margin: 15px 0 12px;
}
.contextmenu {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  background: #fff;
  font-size: 14px;
  color: #666;
  border: 1px solid #e9e9e9;
  min-width: 250px;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
  li {
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #f4f4f5;
    }
  }
  li:last-child {
    border-top: 1px solid #e9e9e9;
  }
}
</style>
