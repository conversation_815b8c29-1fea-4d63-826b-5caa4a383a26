<!--文件宣贯-->
<template>
  <div class="content">
    <div class="content-item">
      <screen-border-multi :title="''">
        <template v-slot:headerRight>
          <div class="headerBox">
            <el-input
              v-model="fileName"
              type="input"
              clearable
              placeholder="请输入文件名"/>     
            <span
              class="screen-btn"
              @click="getDocumentPublicity">
              查询
            </span>
            <span
              v-command="'/screen/plateRollsSafe/edit'"
              class="screen-btn"
              @click="AddData">
              <el-icon class="el-icon-upload2"/>
              上传
            </span>
          </div>
        </template>
        <el-table
          v-loading="loading"
          :data="DP_Data"
          border>
          <el-table-column
            show-overflow-tooltip
            width="70"
            label="序号">
            <template slot-scope="scope">
              <div>{{ scope.$index+1 }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="文件名">
            <template slot-scope="scope">
              <div
                style="text-decoration: underline;cursor: pointer;"
                @click="watchFile(scope.row)">{{ scope.row.fileName }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="文件类型"
            width="130">
            <template slot-scope="scope">
              {{ scope.row.fileType }}
            </template>
          </el-table-column>
          <el-table-column
            label="上传日期"
            width="130">
            <template slot-scope="scope">
              <div>{{ scope.row.setDate }}</div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property=""
            width="150"
            label="操作">
            <template v-slot="scope">
              <span
                style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                @click="DownloadFile(scope.row)">下载</span>
              <span
                v-command="'/first/steel/safe/delete'"
                style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                @click="Del(scope.row)">删除</span>
            </template>
          </el-table-column>
        </el-table>
      </screen-border-multi>
    </div>

    <!--上传-->
    <el-dialog
      :visible.sync="DP_view"
      :close-on-click-modal="false"
      width="60"
      class="screen-dialog"
      @close="Close_DP_view">
      <template v-slot:title>
        <div class="custom-dialog-title">
          文件宣贯
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">文件</div>
          <el-upload
            :before-remove="beforeRemove"
            :before-upload="beforeUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
            :multiple="false"
            action=""
            class="upload-demo">
            <el-button
              size="small"
              type="primary">点击上传</el-button>
            <div
              slot="tip"
              class="el-upload__tip">请选择文件</div>
          </el-upload>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          v-loading="DP_loading"
          class="screen-btn"
          @click="SubmitData">
          确定
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { get, post } from '@/lib/Util'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { minio_upload, DP_INQUIRE, DP_NEWS, DP_DEL } from '@/api/screen'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi.vue'
export default {
  name: 'filePage',
  components: {
    ScreenBorderMulti
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      //文件类型
      fileType: '会议纪要',
      //文件名
      fileName: '',

      loading: false,
      //文件官宣数据
      DP_Data: [],

      //上传
      DP_view: false,
      FormData: {},
      fileData: null,
      fileList: [],
      DP_loading: false
    }
  },
  watch: {
    selectDate: function() {
      this.getDocumentPublicity()
    }
  },
  mounted() {
    this.getDocumentPublicity()
  },
  methods: {
    //文件类型筛选
    getFileData(val) {
      if (val == '全部') {
        this.fileType = ''
      } else {
        this.fileType = val
      }

      this.getDocumentPublicity()
    },

    //查询
    async getDocumentPublicity() {
      this.loading = true
      let res = await post(DP_INQUIRE, {
        setDate: this.selectDate,
        type: '会议纪要',
        fileName: this.fileName
      })
      // console.log('查询数据', res)
      if (res) {
        this.loading = false
        this.DP_Data = res.data
      }
    },

    //打开上传弹框
    AddData() {
      this.DP_view = true
      this.FormData = {
        fileName: '',
        fileType: '会议纪要',
        fileUrl: ''
      }
    },

    //提交
    async SubmitData() {
      this.DP_loading = true
      if (this.fileData) {
        let formData = new FormData()
        formData.append('file', this.fileData)

        let res = await post(minio_upload, formData)
        if (res) {
          let index = res.indexOf('?')
          if (index !== -1) {
            this.FormData.fileUrl = res.substring(0, index)
          } else {
            this.FormData.fileUrl = res
          }
          //清空文件
          this.fileList = []

          //上传文件数据给后台
          if (this.FormData.fileUrl != '') {
            let res1 = await post(DP_NEWS, [this.FormData])
            // console.log('数据上传', res1)
            if (res1.status == 1) {
              this.DP_loading = false
              this.Close_DP_view()
              this.getDocumentPublicity()
              this.$message.success('上传成功!')
            }
          }
        }
      } else {
        this.$message.warning('请选择文件和文件类型！')
      }
    },

    //关闭上传弹框
    Close_DP_view() {
      this.DP_view = false
      this.FormData = {}
    },

    //上传文件前
    beforeUpload(file) {
      this.fileData = file
      this.FormData.fileName = file.name
    },

    //删除文件之前
    beforeRemove(file, fileList) {
      const isDel = this.$confirm(`确定移除 ${file.name}?`)
      if (isDel) {
        this.fileData = null
      }
      return isDel
    },

    //超出文件上传数量
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },

    //查看文件
    async watchFile(row) {
      let format = row.fileUrl.substring(row.fileUrl.lastIndexOf('.') + 1)

      let type = ''
      if (format === 'pdf') {
        type = 'application/pdf'
      } else if (format === 'png') {
        type = 'image/png'
      } else if (format === 'jpeg') {
        type = 'image/jpeg'
      }

      //预览
      if (type != '') {
        let data = await get(row.fileUrl)
        if (!data) {
          return
        }
        const url = window.URL.createObjectURL(new Blob([data], { type: type }))
        window.open(url)
      } else {
        this.$message.warning('此文件不可在线浏览,请下载!')
      }
    },

    //下载文件
    DownloadFile(row) {
      window.open(row.fileUrl)
    },

    //删除
    async Del(row) {
      let res = await post(DP_DEL, [{ id: row.id }])
      if (res.status == 1) {
        this.getDocumentPublicity()
        this.$message.success('删除成功!')
      }
    }
  }
}
</script>

<style scoped lang="less">
.headerBox {
  width: 100%;
  display: flex;
  position: absolute;
  left: 0;
  justify-content: flex-end;
  align-items: center;
  padding: 0 25px;
  /deep/.el-input {
    position: relative;
    top: 1px;
    width: 180px;
  }
  /deep/.el-input + .screen-btn {
    margin: 0 10px;
  }
  /deep/.el-input__inner {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
  }
  .screen-btn {
    height: 32px;
    line-height: 32px;
  }
}
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
</style>
