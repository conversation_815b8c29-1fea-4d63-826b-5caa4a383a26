<template>
  <el-dialog
    :visible.sync="visible"
    :width="'900px'"
    :title="node.name || ''"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <loading v-if="loading"/>
    <kpi-node
      v-else
      :node="treeDate"
      :rank="2"/>
  </el-dialog>
</template>

<script>
import KpiNode from '@/components/diagramTree/KpiNode'
import { findKpiRelTree } from '@/api/kpi'
import { post } from '@/lib/Util'
export default {
  name: 'kpiTree',
  components: { KpiNode },
  props: {
    node: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data: () => {
    return {
      loading: true,
      visible: true,
      treeDate: {}
    }
  },
  watch: {
    visible(value) {
      this.$emit('input', value)
    }
  },
  created() {
    //
    this.init()
  },
  methods: {
    init() {
      post(findKpiRelTree, { kid: this.node.kid }).then(res => {
        this.loading = false
        if (res.success) {
          this.treeDate = res.data
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
