<template>
  <div class="content">
    <div class="content-item">
      <screen-border :title="'工艺技术绩效评价表'">
        <template v-slot:headerRight>
          <span
            class="screen-btn"
            @click="$refs.tableShow.clearFilter()">
            清除筛选
          </span>
          <span
            v-command="'/screen/technologyMeeting/pivot'"
            class="screen-btn"
            @click="getPivot();pivotTable.dialogVisible = true">
            <el-icon class="el-icon-edit-outline" />
            汇总表
          </span>
          <!-- 分厂编辑按钮-->
          <span
            v-command="'/screen/technologyMeeting/factory'"
            class="screen-btn"
            @click="pilotPlan1.dialogVisible = true">
            <el-icon class="el-icon-edit-outline" />
            操作
          </span>
          <span
            v-command="'/screen/technologyMeeting/edit'"
            class="screen-btn"
            @click="pilotPlan1.dialogVisible = true">
            <el-icon class="el-icon-edit-outline" />
            操作
          </span>
        </template>
        <div
          ref="table1"
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            ref="tableShow"
            :data="pilotPlan1.showGridData"
            :span-method="handleObjectSpan"
            :max-height="pilotPlan1.maxHeight"
            :row-class-name="totalClass"
            size="mini"
            class="center-table"
            border>
            <el-table-column
              type="index"
              label="序号"
              width="60" />
            <el-table-column
              :filters="pltList"
              :filter-method="filterPlt"
              property="plt"
              label="检查产线"
              width="110" />
            <el-table-column
              property="inspectionTime"
              label="检查时间"
              width="95" />
            <el-table-column
              property="inspectionContent"
              label="检查内容"
              width="85" />
            <el-table-column
              v-if="!isFactoryUser"
              property="department"
              label="牵头科室"
              width="85" />
            <el-table-column
              v-if="!isFactoryUser"
              :label="'参加人员'"
              property="participants"
              width="85">
              <template v-slot="{row}">
                <text-display :text="row.participants" />
              </template>
            </el-table-column>
            <el-table-column
              :label="'文件依据'"
              align="left"
              property="documentBasis">
              <template v-slot="{row}">
                <text-display :text="row.documentBasis" />
              </template>
            </el-table-column>
            <el-table-column
              :label="'规定内容'"
              align="left"
              property="prescribedContent">
              <template v-slot="{row}">
                <text-display :text="row.prescribedContent" />
              </template>
            </el-table-column>
            <el-table-column
              :label="'实际操作内容'"
              align="left"
              property="operationContent">
              <template v-slot="{row}">
                <text-display :text="row.operationContent" />
              </template>
            </el-table-column>
            <el-table-column
              :filters="[{ text: '符合', value: '符合' }, { text: '不符合', value: '不符合' }]"
              :filter-method="filterConclusion"
              :label="'结论'"
              property="conclusion"
              width="80" />
            <el-table-column
              :label="'问题等级'"
              property="issueLevel"
              width="85" />
            <el-table-column
              property="deductPoints"
              label="扣分"
              width="60" />
            <el-table-column
              :label="'整改期限'"
              property="rftDeadline"
              width="120" />
            <el-table-column
              :label="'违章造成改判'">
              <el-table-column
                :label="'坯（t）'"
                property="adjudicationSlab"
                width="70" />
              <el-table-column
                :label="'材（t）'"
                property="adjudicationMtl"
                width="70" />
            </el-table-column>
            <el-table-column
              :label="'整改情况'"
              align="left"
              property="rftSituation">
              <template v-slot="{row}">
                <text-display :text="row.rftSituation" />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </screen-border>
    </div>
    <!--工艺绩效评价-->
    <el-dialog
      :visible.sync="pilotPlan1.dialogVisible"
      :width="'95%'"
      :top="'50px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="工艺技术绩效评价表">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="$refs.tableEdit.clearFilter()">
              清除筛选
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM'"
              type="month"
              @change="changeDate" />
            <template>
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline" />
                  EXCEL导入
                </span>
              </el-upload>
            </template>
            <span
              class="screen-btn"
              @click="exportpilotPlan">
              导出
            </span>
          </div>
          工艺技术绩效评价表
        </div>
      </template>
      <el-form :disabled="!canEditMonth">
        <el-table
          v-loading="loading"
          ref="tableEdit"
          :data="pilotPlan1.gridData"
          :max-height="tableMaxHeight"
          class="center-table"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60" />
          <el-table-column
            :filters="pltList"
            :filter-method="filterPlt"
            property="plt"
            label="检查产线"
            width="105">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.plt">
                <el-option
                  v-for="(item, index) in pltList"
                  :key="index"
                  :value="item.value">
                  {{ item.value }}
                </el-option>
              </el-select>
              <template v-else>{{ row.plt }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="inspectionTime"
            label="检查时间"
            width="135">
            <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.inspectionTime"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%" />
              <template v-else>{{ row.inspectionTime }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="inspectionContent"
            label="检查内容"
            width="95">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.inspectionContent">
                <el-option
                  v-for="(item, index) in varietyList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.inspectionContent }}</template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            property="department"
            label="牵头科室"
            width="110">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.department">
                <el-option
                  v-for="(item, index) in departmentList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.department }}</template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!isFactoryUser"
            :label="'参加人员'"
            property="participants"
            width="75">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.participants"
                :rows="4"
                type="textarea" />
              <template v-else>
                <text-display :text="row.participants" />
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'文件依据'"
            min-width="100"
            property="documentBasis">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.documentBasis"
                :rows="4"
                type="textarea" />
              <template v-else>
                <text-display :text="row.documentBasis" />
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'规定内容'"
            min-width="100"
            property="prescribedContent">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.prescribedContent"
                :rows="4"
                type="textarea" />
              <template v-else>
                <text-display :text="row.prescribedContent" />
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'实际操作内容'"
            min-width="100"
            property="operationContent">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.operationContent"
                :rows="4"
                type="textarea" />
              <template v-else>
                <text-display :text="row.operationContent" />
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :filters="[{ text: '符合', value: '符合' }, { text: '不符合', value: '不符合' }]"
            :filter-method="filterConclusion"
            :label="'结论'"
            property="conclusion"
            width="80">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.conclusion"
                @change="conclusionChange($event, pilotPlan1.gridData)">
                <el-option
                  v-for="(item, index) in conclusionList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.conclusion }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'问题等级'"
            property="issueLevel"
            width="70">
            <template v-slot="{ row, $index }">
              <el-select
                v-if="$index === editIndex"
                v-model="row.issueLevel">
                <el-option
                  v-for="(item, index) in levelList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
              <template v-else>{{ row.issueLevel }}</template>
            </template>
          </el-table-column>
          <el-table-column
            property="deductPoints"
            label="扣分"
            width="60">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex"
                v-model="row.deductPoints"
                :disabled="row.deductPointsDisabled || false" />
              <template v-else>{{ row.deductPoints }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'整改期限'"
            property="rftDeadline"
            width="135">
            <template v-slot="{ row, $index }">
              <el-date-picker
                v-if="$index === editIndex"
                v-model="row.rftDeadline"
                :value-format="'yyyy-MM-dd'"
                style="width: 100%" />
              <template v-else>{{ row.rftDeadline }}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'违章造成改判'">
            <el-table-column
              :label="'坯（t）'"
              property="adjudication"
              width="80">
              <template v-slot="{ row, $index }">
                <el-input
                  v-if="$index === editIndex"
                  v-model="row.adjudicationSlab" />
                <template v-else>{{ row.adjudicationSlab }}</template>
              </template>
            </el-table-column>
            <el-table-column
              :label="'材（t）'"
              property="adjudication"
              width="80">
              <template v-slot="{ row, $index }">
                <el-input
                  v-if="$index === editIndex"
                  v-model="row.adjudicationMtl" />
                <template v-else>{{ row.adjudicationMtl }}</template>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            :label="'整改情况'"
            property="rftSituation">
            <template v-slot="{ row, $index }">
              <el-input
                v-if="$index === editIndex || $index === editPartIndex"
                v-model="row.rftSituation"
                :rows="4"
                type="textarea" />
              <template v-else>
                <text-display :text="row.rftSituation" />
              </template>
            </template>
          </el-table-column>
          <el-table-column
            :label="'操作'"
            fixed="right"
            property="proofResult">
            <template v-slot="{ row, $index}">
              <!-- 部分编辑-->
              <el-button
                v-command="'/screen/technologyMeeting/factory'"
                v-if="$index !== editPartIndex && row.conclusion === '不符合'"
                class="screen-btn edit-btn"
                type="text"
                @click="editPartItem(pilotPlan1.gridData, $index, 1)">编辑
              </el-button>
              <el-button
                v-command="'/screen/technologyMeeting/edit'"
                v-if="$index !== editIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="editItem(pilotPlan1.gridData, $index, 1)">编辑
              </el-button>
              <el-button
                v-if="$index === editIndex || $index === editPartIndex"
                class="screen-btn edit-btn"
                type="text"
                @click="savepilotPlanItem(row)">保存
              </el-button>
              <el-button
                v-command="'/screen/technologyMeeting/edit'"
                class="screen-btn edit-btn"
                type="text"
                @click="deleteItem(row, $index)">删除
              </el-button>
            </template>
          </el-table-column>

        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditMonth"
          class="screen-btn"
          style="margin-top: 10px"
          @click="addGridData('pilotPlan1');editIndex = pilotPlan1.gridData.length - 1">
          <el-icon class="el-icon-circle-plus-outline" />
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--工艺绩效-透视表-->
    <el-dialog
      :visible.sync="pivotTable.dialogVisible"
      :width="'90%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="汇总表">
      <template v-slot:title>
        <div class="custom-dialog-title">
          透视表
        </div>
      </template>
      <p class="dialog-item-tit">各产线检查次数</p>
      <el-table
        v-loading="loading"
        :data="pivotTable.table1"
        :summary-method="getSummaries"
        show-summary
        class="center-table font-table"
        border>
        <el-table-column
          property="PLT"
          label="检查产线" />
        <el-table-column
          property="GYCC"
          label="工艺抽查" />
        <el-table-column
          property="GZBJ"
          label="工装备件" />
        <el-table-column
          property="ZJ"
          label="总计" />
      </el-table>
      <p class="dialog-item-tit">各科室检查次数</p>
      <el-table
        v-loading="loading"
        :data="pivotTable.table2"
        show-summary
        class="center-table font-table"
        border>
        <el-table-column
          property="department"
          label="牵头科室" />
        <el-table-column
          label="工艺抽查">
          <el-table-column
            property="gyB1"
            label="第一炼钢厂" />
          <el-table-column
            property="gyC1"
            label="中厚板卷厂" />
          <el-table-column
            property="gyC2"
            label="宽厚板厂" />
          <el-table-column
            property="gyC3"
            label="中板厂" />
          <el-table-column
            property="gycc"
            label="合计" />
        </el-table-column>
        <el-table-column
          label="工装备件">
          <el-table-column
            property="gzB1"
            label="第一炼钢厂" />
          <el-table-column
            property="gzC1"
            label="中厚板卷厂" />
          <el-table-column
            property="gzC2"
            label="宽厚板厂" />
          <el-table-column
            property="gzC3"
            label="中板厂" />
          <el-table-column
            property="gzbj"
            label="合计" />
        </el-table-column>
        <el-table-column
          label="完成情况">
          <el-table-column
            property="gyccFinish"
            label="工艺抽查" />
          <el-table-column
            property="gzbjFinish"
            label="工装备件" />
        </el-table-column>
      </el-table>
      <p class="dialog-item-tit">各产线扣分</p>
      <el-table
        v-loading="loading"
        :data="pivotTable.table3"
        :summary-method="getSummaries"
        show-summary
        class="center-table font-table"
        border>
        <el-table-column
          property="PLT"
          label="检查产线" />
        <el-table-column
          property="GYCC"
          label="工艺抽查" />
        <el-table-column
          property="GZBJ"
          label="工装备件" />
        <el-table-column
          property="ZJ"
          label="总计" />
        <el-table-column
          property="DF"
          label="得分" />
      </el-table>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM'"
            type="month" />
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { qmsQualitySystemSaveNew, qmsQualitySystem } from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  checklistBySetDate,
  checklistDelete,
  checklistSave,
  findCountDeptBySetDate,
  findCountPltBySetDate,
  findDpPltBySetDate,
  progressReportingDeleteAllById,
  progressReportingFindAllBySetDate,
  progressReportingSave
} from '@/api/screenTechnolagy'
import moment from 'moment'
import { math } from '@/lib/Math'
import TextDisplay from '@/pages/screen/technologyMeeting/component/text-display'
import { findOneUserByUserNo } from '@/api/system'
import { mapState } from 'vuex'
import { showNotification } from '@/utils/notifyUtil'

export default {
  name: 'kpi',
  components: { TextDisplay, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      editIndex: null,
      editPartIndex: null, //部分编辑
      tableMaxHeight: null,
      pilotPlan1: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      pivotTable: {
        table1: [],
        table2: [],
        table3: [],
        dialogVisible: false,
        maxHeight: null
      },
      pltList: [
        { value: '第一炼钢厂', text: '第一炼钢厂' },
        { value: '宽厚板厂', text: '宽厚板厂' },
        { value: '中厚板卷厂', text: '中厚板卷厂' },
        { value: '中板厂', text: '中板厂' }
      ],
      departmentList: [
        '工艺研究室',
        '调质钢研发室',
        '结构船板研发室',
        '低温容器研发室',
        '能源用钢研发室'
      ],
      varietyList: ['工艺抽查', '工装备件'],
      conclusionList: ['符合', '不符合'],
      factoryList: [
        { code: 'X73', name: '第一炼钢厂' },
        { code: 'X38', name: '宽厚板厂' },
        { code: 'X32', name: '中厚板卷厂' },
        { code: 'X66', name: '中板厂' }
      ],
      isFactoryUser: false
    }
  },
  computed: {
    ...mapState('menu', ['pageButtonPower']),
    isKpiManager: function() {
      return this.pageButtonPower.includes(
        '/screen/technologyMeeting/kpiManager'
      )
    },
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'month')
        .format('yyyy-MM')
    },
    canEditMonth: function() {
      // console.log(moment().format('yyyy-MM-DD'), this.cDate)
      return (
        moment().format('yyyy-MM') <= this.cDate ||
        moment()
          .subtract(1, 'days')
          .format('yyyy-MM') === this.cDate ||
        this.isKpiManager
      )
    },
    levelList: function() {
      if (
        this.editIndex !== null &&
        this.pilotPlan1.gridData[this.editIndex] &&
        this.pilotPlan1.gridData[this.editIndex].inspectionContent ===
          '工装备件'
      ) {
        return [
          '一般1级',
          '一般2级',
          '一般3级',
          '重要1级',
          '重要2级',
          '重要3级'
        ]
      }
      return []
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = moment(this.selectDate).format('YYYY-MM')
    },
    cDate: function() {
      // 初始化数据
      this.loadData()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = moment(this.selectDate).format('YYYY-MM')
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.findOneUserByUserNo().then(res => {
      const userInfo = res
      const org = this.factoryList.find(
        item => userInfo.orgCode.indexOf(item.code) === 0
      )
      post(checklistBySetDate, {
        setDate: this.cDate,
        type: 1
      }).then(res => {
        let data = res.data
          .filter(item => !org || org.name === item.plt)
          .map((item, index) => {
            return {
              index: index + 1,
              plt: item.plt,
              inspectionTime: item.inspectiontime,
              inspectionContent: item.inspectioncontent,
              department: item.department,
              participants: item.participants,
              documentBasis: item.documentbasis,
              prescribedContent: item.prescribedcontent,
              operationContent: item.operationcontent,
              conclusion: item.conclusion,
              issueLevel: item.issuelevel,
              deductPoints: item.deductpoints,
              rftDeadline: item.rftdeadline,
              adjudicationSlab: item.adjudicationslab,
              adjudicationMtl: item.adjudicationmtl,
              rftSituation: item.rftsituation,
              id: item.id
            }
          })
        const map = new Map()
        data.forEach(item => {
          if (!item.rftDeadline) {
            return
          }
          let diff = this.getDaysDifference(item.rftDeadline)
          if (diff <= 3 && diff >= 0 && item.rftSituation === undefined) {
            map.set(item.plt + '0', {
              title: item.plt,
              message:
                '你厂工艺检查问题将于' +
                diff +
                '天后超过整改期限，请于' +
                item.rftDeadline +
                '前完成整改。',
              type: 'warning'
            })
          } else if (diff < 0 && item.rftSituation === undefined) {
            map.set(item.plt + '1', {
              title: item.plt,
              message: '你厂工艺检查问题已经超过整改期限，请尽快完成整改。',
              type: 'error'
            })
          }
        })
        map.forEach((value, key) => {
          showNotification({
            title: value.title,
            message: value.message,
            type: value.type
          })
        })
      })
    })
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    loadData() {
      this.getpilotPlan(1)
      this.getpilotPlan(2)
    },
    filterConclusion(value, row) {
      return row['conclusion'] === value
    },
    filterPlt(value, row) {
      return row['plt'] === value
    },
    async findOneUserByUserNo() {
      this.userNo = localStorage.getItem('userId')
      const user = await post(findOneUserByUserNo, {
        userNo: this.userNo
      })
      return new Promise(resolve => resolve(user.data))
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          index: 'A',
          plt: 'B',
          inspectionTime: 'C',
          inspectionContent: 'D',
          department: 'E',
          participants: 'F',
          documentBasis: 'G',
          prescribedContent: 'H',
          operationContent: 'I',
          conclusion: 'J',
          issueLevel: 'K',
          deductPoints: 'L',
          rftDeadline: 'M',
          adjudicationSlab: 'N',
          adjudicationMtl: 'O',
          rftSituation: 'P'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        sheet.shift()
        // 表格信息
        const datas = sheet.map(item => {
          if (item.rftDeadline && typeof item.rftDeadline === 'number') {
            item.rftDeadline = LAY_EXCEL.dateCodeFormat(
              item.rftDeadline,
              'YYYY-MM-DD'
            )
          }
          if (item.inspectionTime && typeof item.inspectionTime === 'number') {
            item.inspectionTime = LAY_EXCEL.dateCodeFormat(
              item.inspectionTime,
              'YYYY-MM-DD'
            )
          }
          item.deductPoints = item.deductPoints
            ? typeof item.deductPoints === 'string'
              ? item.deductPoints.trim()
              : item.deductPoints
            : item.deductPoints || 0
          return item
        })
        this.$message.success('解析成功！')
        this.savepilotPlan(datas)
      })
    },
    exportpilotPlan() {
      const data = [
        {
          index: '序号',
          plt: '检查产线',
          inspectionTime: '检查时间',
          inspectionContent: '检查内容',
          department: '牵头科室',
          participants: '参加人员',
          documentBasis: '文件依据',
          prescribedContent: '规定内容',
          operationContent: '实际操作内容',
          conclusion: '结论',
          issueLevel: '问题等级',
          deductPoints: '扣分',
          rftDeadline: '整改期限',
          adjudicationSlab: '违章造成误判',
          adjudicationMtl: '',
          rftSituation: '整改情况'
        },
        {
          index: '',
          plt: '',
          inspectionTime: '',
          inspectionContent: '',
          department: '',
          participants: '',
          documentBasis: '',
          prescribedContent: '',
          operationContent: '',
          conclusion: '',
          issueLevel: '',
          deductPoints: '',
          rftDeadline: '',
          adjudicationSlab: '坯（t）',
          adjudicationMtl: '材（t）',
          rftSituation: ''
        }
      ].concat(
        _.cloneDeep(this.pilotPlan1.gridData).map((item, index) => {
          delete item.id
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `工艺技术绩效评价表（${this.cDate}）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {
              '!merges': LAY_EXCEL.makeMergeConfig([
                ['A1', 'A2'],
                ['B1', 'B2'],
                ['C1', 'C2'],
                ['D1', 'D2'],
                ['E1', 'E2'],
                ['F1', 'F2'],
                ['G1', 'G2'],
                ['H1', 'H2'],
                ['I1', 'I2'],
                ['J1', 'J2'],
                ['K1', 'K2'],
                ['L1', 'L2'],
                ['M1', 'M2'],
                ['N1', 'O1'],
                ['P1', 'P2']
              ])
            }
          }
        }
      )
    },
    // 获取数据
    async getpilotPlan(type) {
      const userInfo = await this.findOneUserByUserNo()
      // const userInfo = {
      //   orgCode: 'X73'
      // }
      const org = this.factoryList.find(
        item => userInfo.orgCode.indexOf(item.code) === 0
      )
      this.isFactoryUser = !!org
      post(checklistBySetDate, {
        setDate: this.cDate,
        type: type
      }).then(res => {
        this.loading = false
        this.$emit('statusChange', !res.data.length)
        let data = res.data
          .filter(item => !org || org.name === item.plt)
          .map((item, index) => {
            return {
              index: index + 1,
              plt: item.plt,
              inspectionTime: item.inspectiontime,
              inspectionContent: item.inspectioncontent,
              department: item.department,
              participants: item.participants,
              documentBasis: item.documentbasis,
              prescribedContent: item.prescribedcontent,
              operationContent: item.operationcontent,
              conclusion: item.conclusion,
              issueLevel: item.issuelevel,
              deductPoints: item.deductpoints,
              rftDeadline: item.rftdeadline,
              adjudicationSlab: item.adjudicationslab,
              adjudicationMtl: item.adjudicationmtl,
              rftSituation: item.rftsituation,
              id: item.id
            }
          })
        this['pilotPlan1'].gridData = lodash.cloneDeep(data)
        this['pilotPlan1'].showGridData = data
        this.formatSpanData(this['pilotPlan1'].showGridData)
      })
    },
    savepilotPlanItem(row) {
      let err = 0
      let arr = [
        'plt',
        'inspectionTime',
        'inspectionContent',
        'department',
        'participants',
        'documentBasis',
        'prescribedContent',
        'operationContent',
        'conclusion',
        'deductPoints',
        'adjudication'
      ]
      arr.forEach(item => {
        if (row[item] === '' || row[item] === null) {
          console.log(item)
          err++
        }
      })
      if (err > 0) {
        return this.$message.warning('请补全信息！')
      }
      if (row['conclusion'] === '不符合') {
        if (row['deductPoints'] < 1) {
          return this.$message.warning('不符合项扣分需大于1！')
        }
        if (!row['rftDeadline']) {
          return this.$message.warning('不符合项整改期限不能为空！')
        }
      }
      this.savepilotPlan([row])
    },
    savepilotPlan(items) {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: items.map(item => {
          Object.assign(item, { setDate: this.cDate })
          return item
        })
      }
      post(checklistSave, params).then(res => {
        //
        this.loading = false
        if (res && res !== -1) {
          this.$message.success('保存成功！')
          this.getpilotPlan()
          this.editIndex = null
          this.editPartIndex = null
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    editItem(data, index, type) {
      this.editIndex = index
    },
    editPartItem(data, index, type) {
      this.editPartIndex = index
    },
    deleteItem(data, index) {
      post(checklistDelete, {
        id: data.id
      }).then(res => {
        this.$message.success('删除成功！')
        this.getpilotPlan()
      })
    },
    conclusionChange($event, data) {
      if ($event === '符合') {
        console.log(data)
        data[this.editIndex].deductPoints = 0
        data[this.editIndex].rftDeadline = null
        data[this.editIndex].deductPointsDisabled = true
      } else {
        data[this.editIndex].deductPointsDisabled = false
      }
    },
    getPivot() {
      post(findCountPltBySetDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.pivotTable.table1 = res
      })
      post(findCountDeptBySetDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.pivotTable.table2 = res
      })
      post(findDpPltBySetDate, { setDate: this.cDate }).then(res => {
        console.log(res)
        this.pivotTable.table3 = res.map(item => {
          item.DF = 100 - item.ZJ
          return item
        })
      })
    },
    calculate() {
      this.pilotPlan1.maxHeight = this.$refs.table1.offsetHeight
      this.tableMaxHeight = document.body.clientHeight - 240
    },
    totalClass(row) {
      if (row.row.serialNumber && row.row.serialNumber.trim() === '合计') {
        return 'table-total'
      }
      return ''
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总计'
          return
        }
        console.log(column)
        // if (![1, 2, 3].includes(index)) return (sums[index] = '')
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    getDaysDifference(dateStr) {
      const targetDate = new Date(dateStr)
      const today = new Date()
      targetDate.setHours(0, 0, 0, 0)
      today.setHours(0, 0, 0, 0)
      const diffTime = targetDate - today
      return diffTime / (1000 * 60 * 60 * 24)
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.edit-btn {
  margin: 0 3px;

  &:first-child {
    margin-bottom: 5px;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}

.dialog-item-tit {
  font-size: 18px;
  color: #dcdcdc;
  margin-bottom: 8px;
  margin-top: 15px;
}
</style>
