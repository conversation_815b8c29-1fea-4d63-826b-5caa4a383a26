<!--施工明细-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <div class="content">
            <div class="content-item">
              <screen-border title="施工明细">
                <template v-slot:headerRight>
                  <el-row :gutter="20">
                    <span
                      class="screen-btn"
                      @click="exportExcel">
                      <el-icon class="el-icon-download"/>
                      模板
                    </span>
                    <span
                      class="screen-btn"
                      @click="clickDownloadExcel">
                      <el-icon class="el-icon-download"/>
                      下载
                    </span>
                    <span
                      v-command="'/first/steel/safe/delete'"
                      class="screen-btn"
                      @click="clickAddProject">
                      <el-icon class="el-icon-edit-outline"/>
                      新增
                    </span>
                    <span
                      v-command="'/first/steel/safe/delete'"
                      class="screen-btn"
                      @click="handleDelete">
                      <el-icon class="el-icon-delete"/>
                      删除
                    </span>
                    <span
                      v-command="'/first/steel/safe/delete'"
                      class="screen-btn">
                      <el-upload
                        :multiple="false"
                        :show-file-list="false"
                        :auto-upload="false"
                        :on-change="importExcel"
                        :file-list="fileList"
                        action=""
                        accept=".xls,.xlsx">
                        <span>
                          <el-icon class="el-icon-upload2"/>
                          上传
                        </span>
                      </el-upload>
                    </span>

                  </el-row>
                </template>
                <div
                  ref="table1"
                  class="scroll-wrapper">
                  <el-table
                    v-loading="ProjectData.loading"
                    :data="ProjectData.showGridData"
                    :row-class-name="rowClassName"
                    border
                    @selection-change="handleSelectionChange">
                    <el-table-column
                      type="selection"
                      align="center"/>
                    <el-table-column
                      show-overflow-tooltip
                      width="60"
                      align="center"
                      label="序号">
                      <template v-slot="scope">
                        <div>{{ scope.$index + 1 }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="单位"
                      align="center"
                      width="100">
                      <template v-slot="scope">
                        <div>{{ scope.row.deptName }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="项目名称"
                      align="center"
                      width="300">
                      <template v-slot="scope">
                        <div>{{ scope.row.projectName }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="区域"
                      align="center"
                      width="100">
                      <template v-slot="scope">
                        <div>{{ scope.row.area }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="高危等级"
                      align="center"
                      width="100">
                      <template v-slot="scope">
                        <div>{{ scope.row.hazardLevel }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="作业内容">
                      <template v-slot="scope">
                        <div>{{ scope.row.workContent }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="监护姓名"
                      align="center"
                      width="100">
                      <template v-slot="scope">
                        <div>{{ scope.row.guardianName }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="施工单位"
                      align="center"
                      width="200">
                      <template v-slot="scope">
                        <div>{{ scope.row.constructionUnit }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="施工人数"
                      align="center"
                      width="100">
                      <template v-slot="scope">
                        <div>{{ scope.row.constructionPersons }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="日期"
                      align="center"
                      width="130">
                      <template v-slot="scope">
                        <div>{{ scope.row.setDate }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property=""
                      width="150"
                      label="操作">
                      <template v-slot="scope">
                        <span
                          v-command="'/first/steel/safe/delete'"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectItem(scope.row)">编辑</span>
                        <span
                          v-command="'/first/steel/safe/delete'"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                          @click="clickProjectDeleteItem(scope.row)">删除</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </screen-border>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--新增修改-->
    <el-dialog
      v-loading="ProjectData.loading"
      :visible.sync="ProjectData.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="施工明细">
      <template v-slot:title>
        <div class="custom-dialog-title">
          施工明细
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">单位</div>
          <el-select
            v-model="projectItem.deptName"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in deptList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">项目名称</div>
          <el-input
            v-model="projectItem.projectName"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">区域</div>
          <el-input
            v-model="projectItem.area"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">高危等级</div>
          <el-select
            v-model="projectItem.hazardLevel"
            class="screen-input"
            placeholder="请选择">
            <el-option
              v-for="item in hazardLevelList"
              :key="item.value"
              :label="item.text"
              :value="item.value"/>
          </el-select>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">作业内容</div>
          <el-input
            v-model="projectItem.workContent"
            :rows="3"
            type="textarea"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">监护姓名</div>
          <el-input
            v-model="projectItem.guardianName"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">施工单位</div>
          <el-input
            v-model="projectItem.constructionUnit"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">施工人数</div>
          <el-input
            v-model="projectItem.constructionPersons"
            :rows="3"
            type="input"
            clearable
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
      </div>
      <div
        style="margin-top: 10px"
        class="text-center">
        <span
          class="screen-btn"
          @click="addProjectData([projectItem])">
          确定
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  mino_download,
  oneBulletinBoard_deleteConstructionDetails,
  oneBulletinBoard_exportConstructionDetails,
  oneBulletinBoard_getConstructionDetails,
  oneBulletinBoard_saveConstructionDetails,
  oneBulletinBoard_uploadConstructionDetails
} from '@/api/firstMeeting'
import moment from 'moment'

export default {
  name: 'ProjectPage',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      ProjectData: {
        loading: false,
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      projectItem: {},
      deptList: [
        {
          text: '原料车间',
          value: '原料车间',
          type: 'YLCJ'
        },
        {
          text: '炼钢车间',
          value: '炼钢车间',
          type: 'LGCJ'
        },
        {
          text: '精炼车间',
          value: '精炼车间',
          type: 'JLCJ'
        },
        {
          text: '连铸车间',
          value: '连铸车间',
          type: 'LZCJ'
        },
        {
          text: '运行车间',
          value: '运行车间',
          type: 'YXCJ'
        },
        {
          text: '坯料车间',
          value: '坯料车间',
          type: 'PLCJ'
        },
        {
          text: '综合管理室',
          value: '综合管理室',
          type: 'ZHGLS'
        },
        {
          text: '设备管理室',
          value: '设备管理室',
          type: 'SBGLS'
        },
        {
          text: '品质室',
          value: '品质室',
          type: 'PZS'
        },
        {
          text: '生产管理室',
          value: '生产管理室',
          type: 'SCGLS'
        }
      ],
      deptMap: {
        YLCJ: '原料车间',
        LGCJ: '炼钢车间',
        JLCJ: '精炼车间',
        LZCJ: '连铸车间',
        YXCJ: '运行车间',
        PLCJ: '坯料车间',
        ZHGLS: '综合管理室',
        SBGLS: '设备管理室',
        PZS: '品质室',
        SCGLS: '生产管理室'
      },
      hazardLevelList: [
        {
          text: '一级',
          value: '一级',
          type: 'A'
        },
        {
          text: '二级',
          value: '二级',
          type: 'B'
        },
        {
          text: '无',
          value: '无',
          type: 'C'
        }
      ],
      hazardLevelMap: {
        A: '一级',
        B: '二级',
        C: '无'
      },
      fileList: [],
      multipleSelection: []
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getProjectData()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    // 批量删除
    handleDelete() {
      if (!this.multipleSelection.length)
        return this.$message.warning('请先选择数据！')
      // /productionForecast/deleteOrders
      let list = []
      this.multipleSelection.forEach(item => list.push({ id: item.id }))
      console.log('删除列表：', list)
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteProject(list)
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    exportExcel() {
      const data = [
        {
          num: '序号',
          deptName: '单位',
          projectName: '项目名称',
          area: '区域',
          hazardLevel: '高危等级',
          workContent: '作业内容',
          guardianName: '监护姓名',
          constructionUnit: '施工单位',
          constructionPersons: '施工人数'
        }
      ]
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `施工明细模板.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          num: 'A',
          deptName: 'B',
          projectName: 'C',
          area: 'D',
          hazardLevel: 'E',
          workContent: 'F',
          guardianName: 'G',
          constructionUnit: 'H',
          constructionPersons: 'I'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          // item.setTime = LAY_EXCEL.dateCodeFormat(item.setTime, 'MM月DD日')
          // item.deptName = item.deptName.trim()
          // item.projectName = item.projectName.trim()
          // item.area = item.area.trim()
          // item.hazardLevel = item.hazardLevel.trim()
          // item.workContent = item.workContent.trim()
          // item.guardianName = item.guardianName.trim()
          // item.constructionUnit = item.constructionUnit.trim()
          // item.constructionPersons = item.constructionPersons.trim()
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据
        this.addProjectData(list)
      })
    },
    rowClassName({ row, rowIndex }) {
      if (row.hazardLevel === 'C') {
        return 'class_red'
      } else if (row.hazardLevel === 'B') {
        return 'class_yellow'
      } else {
        return ''
      }
    },
    // importExcel(file) {
    //   this.uploadFile(file)
    // },
    clickDownloadTemplate() {
      this.downloadTemplateFile()
    },
    clickDownloadExcel() {
      this.downloadFile()
    },
    //点击新增
    clickAddProject() {
      this.projectItem = {
        deptName: '',
        projectName: '',
        area: '',
        hazardLevel: '',
        workContent: '',
        guardianName: '',
        constructionUnit: '',
        constructionPersons: '',
        setDate: this.cDate
      }
      this.ProjectData.dialogVisible = true
    },
    //点击查看详情
    clickProjectItem(row) {
      this.projectItem = JSON.parse(JSON.stringify(row))
      this.ProjectData.dialogVisible = true
    },
    //点击删除
    clickProjectDeleteItem(row) {
      this.$confirm(`是否确认删除?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteProject([{ id: row.id }])
        })
        .catch(e => {
          console.log('e', e)
        })
    },
    //新增/修改
    addProjectData(list) {
      // let deptName = this.projectItem.deptName
      // let projectName = this.projectItem.projectName
      // let area = this.projectItem.area
      // let hazardLevel = this.projectItem.hazardLevel
      // let workContent = this.projectItem.workContent
      // let guardianName = this.projectItem.guardianName
      // let constructionUnit = this.projectItem.constructionUnit
      // let constructionPersons = this.projectItem.constructionPersons
      // if (deptName === null || deptName.length === 0) {
      //   this.$message.warning('请选择单位！')
      //   return
      // }
      // if (projectName === null || projectName.length === 0) {
      //   this.$message.warning('请输入项目名称！')
      //   return
      // }
      // if (area === null || area.length === 0) {
      //   this.$message.warning('请输入区域！')
      //   return
      // }
      // if (hazardLevel === null || hazardLevel.length === 0) {
      //   this.$message.warning('请选择高危等级！')
      //   return
      // }
      // if (workContent === null || workContent.length === 0) {
      //   this.$message.warning('请输入作业内容！')
      //   return
      // }
      // if (guardianName === null || guardianName.length === 0) {
      //   this.$message.warning('请输入监护姓名！')
      //   return
      // }
      // if (constructionUnit === null || constructionUnit.length === 0) {
      //   this.$message.warning('请输入施工单位！')
      //   return
      // }
      // if (constructionPersons === null || constructionPersons.length === 0) {
      //   this.$message.warning('请输入施工人数！')
      //   return
      // }
      // const params = [this.projectItem]
      const params = list
      this.ProjectData.loading = true
      post(oneBulletinBoard_saveConstructionDetails, params)
        .then(res => {
          if (res.success) {
            this.$notify.success('操作成功！')
            this.ProjectData.dialogVisible = false
            this.getProjectData()
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    //删除
    deleteProject(list) {
      post(oneBulletinBoard_deleteConstructionDetails, list).then(res => {
        if (res.success) {
          this.$notify.success('删除成功！')
          this.getProjectData()
        }
      })
    },
    calculateHeight() {
      this.ProjectData.maxHeight = this.$refs.table1.offsetHeight
    },
    getProjectData() {
      this.ProjectData.loading = true
      post(oneBulletinBoard_getConstructionDetails, {
        setDate: this.cDate
      })
        .then(res => {
          if (res && res.success) {
            // this.$message.success('查询成功！')
            this.ProjectData.showGridData = res.data.map(item => {
              return {
                id: item.id,
                deptName: item.deptName,
                setDate: item.setDate,
                projectName: item.projectName,
                area: item.area,
                hazardLevel: item.hazardLevel,
                workContent: item.workContent,
                guardianName: item.guardianName,
                constructionUnit: item.constructionUnit,
                constructionPersons: item.constructionPersons
              }
            })
            this.ProjectData.gridData = lodash.cloneDeep(
              this.ProjectData.showGridData
            )
          } else {
            this.$message.error(res.message)
          }
        })
        .finally(_ => {
          this.ProjectData.loading = false
        })
    },
    //导入文件
    async uploadFile(file) {
      let formData = new FormData()
      formData.append('file', file)
      // formData.append('userNo', this.userNo)
      post(oneBulletinBoard_uploadConstructionDetails, formData)
        .then(res => {
          if (res.success === true) {
            this.$message.success('导入成功！')
            this.getProjectData()
          } else {
            this.$message.error('导入失败！' + res.message)
          }
        })
        .catch(err => {
          this.$message.error('导入失败！' + err)
          console.log(err)
        })
    },
    downloadTemplateFile() {
      // let param =
      //   '/%E6%96%BD%E5%B7%A5%E6%98%8E%E7%BB%86%E8%A1%A8%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=CU7ANY7878XLHTE5MK51%2F20231012%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20231012T063120Z&X-Amz-Expires=604797&X-Amz-Security-Token=eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.1Yjmf6C771qOVImouEXJbLKtEWqbg4iI0uaSlpUQLkpdZYtyX8qM_q7GqiV9yRgShjxqxhTIwp7fXbNjHg-0IA&X-Amz-SignedHeaders=host&versionId=null&X-Amz-Signature=4bd235d63d532981cba86cde4c8f044551ec8b63e2a45b1643c62cec42d44c84'
      let param =
        '/%E6%96%BD%E5%B7%A5%E6%98%8E%E7%BB%86%E8%A1%A8%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
      let url = mino_download + param
      window.open(url)
      // //http://************:9123/dsm/%E6%96%BD%E5%B7%A5%E6%98%8E%E7%BB%86%E8%A1%A8%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=CU7ANY7878XLHTE5MK51%2F20231012%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20231012T063120Z&X-Amz-Expires=604797&X-Amz-Security-Token=eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.1Yjmf6C771qOVImouEXJbLKtEWqbg4iI0uaSlpUQLkpdZYtyX8qM_q7GqiV9yRgShjxqxhTIwp7fXbNjHg-0IA&X-Amz-SignedHeaders=host&versionId=null&X-Amz-Signature=4bd235d63d532981cba86cde4c8f044551ec8b63e2a45b1643c62cec42d44c84
      // //http://************:9124/api/v1/buckets/dsm/objects/download?prefix=5pa95bel5piO57uG6KGo5a+85YWl5qih5p2/Lnhsc3g=
      // get(mino_download + param).then(res => {
      //   let data = res
      //   if (!data) {
      //     return
      //   }
      //   const url = window.URL.createObjectURL(new Blob([data]))
      //   let link = document.createElement('a')
      //   let myDate = moment(new Date()).format('yyyy-MM-DD')
      //
      //   link.style.display = 'none'
      //   link.href = url
      //   link.setAttribute('download', '施工明细模板.xls')
      //   document.body.appendChild(link)
      //   link.click()
      //   document.body.removeChild(link) //下载完成移除元素
      //   window.URL.revokeObjectURL(url) //释放掉blob对象
      // })
    },
    //导出Excel
    downloadFile() {
      let param = {
        setDate: this.cDate
      }
      post(oneBulletinBoard_exportConstructionDetails, param).then(res => {
        let data = res
        if (!data) {
          return
        }
        const url = window.URL.createObjectURL(new Blob([data]))
        let link = document.createElement('a')
        let myDate = moment(new Date()).format('yyyy-MM-DD')

        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '施工明细' + myDate + '.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) //下载完成移除元素
        window.URL.revokeObjectURL(url) //释放掉blob对象
      })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-table .el-table__cell {
  font-size: 19px;
}
/deep/ .el-table__body tr:hover > td {
  background-color: rgba(245, 247, 250, 0.1) !important;
  //background-color: transparent !important;
}

/deep/ .el-table__body tr.current-row > td {
  background-color: rgba(245, 247, 250, 0.1) !important;
  //background-color: transparent !important;
}

/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}

.dialog-body {
  overflow: scroll;

  .dialog-cell {
    margin-bottom: 12px;

    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }

    .dialog-cell-input {
    }
  }
}

.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }

  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
