<template>
  <!--质量处晨会看板-->
  <div class="screen-wrapper">
    <div class="screen-header">
      <div class="screen-header-inner">
        <div class="header-left header-side">
          <div>
            <screen-switch/>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'small'"
              class="screen-input"
              style="width: 105px; margin-left: 2px"
              @input="$forceUpdate()"/>
          </div>
          <div class="tab-box">
            <span
              v-for="(item, index) in leftList"
              :key="index"
              :class="{active: active === item.value, disabled: item.disabled}"
              class="header-btn"
              @click="active = item.value">{{ item.name }}</span>
          </div>
          <el-dropdown
            v-for="items in dropdownList"
            :key="items.index"
          >
            <span class="el-dropdown-link header-btn">
              {{ items.text }}<i class="el-icon-arrow-down el-icon--right"/>
            </span>
            <el-dropdown-menu
              slot="dropdown">
             
              <el-dropdown-item
                v-for="(item, i) in items.commands"
                :key="i"
                :class="{active: active === item.value, disabled: item.disabled}"
                @click.native="handleCommand(item)"
              >
                <el-checkbox v-model="item.checked">{{ item.text }}</el-checkbox>
                <!-- {{ item.text }}  -->
              </el-dropdown-item>
             
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div class="header-title">
          <span class="header-arrow"/>
          <span class="header-arrow header-arrow-right"/>
          <img
            src="../../../assets/images/screen/C2-header.png"
            alt=""
            @drag.prevent
          >
        </div>
        <div class="header-right header-side">
          <div class="tab-box">
            <span
              v-for="(item, index) in rightList"
              :key="index"
              :class="{active: active === item.value, disabled: item.disabled}"
              class="header-btn"
              @click="active = item.value">{{ item.name }}</span>
            <span 
              class="header-btn"
              @click="chagePageup()"
            >     <el-icon class="el-icon-caret-left"/></span>
            <span 
              class="header-btn"
              @click="chagePagedown()"
            ><el-icon class="el-icon-caret-right"/></span>
          </div>
        </div>
      </div>
    </div>
    <div class="screen-content">
      <oath
        v-if="active === 1"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <C1Main
        v-if="active === 2"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <product-yest
        v-if="active === 3"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <product-today
        v-if="active === 4"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <energy-c1
        v-if="active === 5"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <divice-c1
        v-if="active === 6"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <CoordinateB1
        v-if="active === 7"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <Quality
        v-if="active === 8"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <security-env
        v-if="active === 9"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <qualityToday
        v-if="active === 10"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <unPlanned
        v-if="active === 11"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <workInProcess
        v-if="active === 12"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <constructionWork
        v-if="active === 13"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <QualityManagement
        v-if="active === 15"
        :select-date="selectDate"
        @dateChange="changeDate"/>
      <WeldingManagement
        v-if="active === 16"
        :select-date="selectDate"
        @dateChange="changeDate"/>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import ScreenSwitch from '@/pages/screen/morningMeeting/component/screen-switch'
import Oath from '@/pages/screen/C2Meeting/oath'
import ProductYest from '@/pages/screen/C2Meeting/productYest'
import ProductToday from '@/pages/screen/C2Meeting/productToday'
import EnergyC1 from '@/pages/screen/C2Meeting/energy'
import DiviceC1 from '@/pages/screen/C2Meeting/divice'
import C1Main from '@/pages/screen/C2Meeting/main'
import CoordinateB1 from '@/pages/screen/C2Meeting/coordinate'
import constructionWork from '@/pages/screen/C2Meeting/constructionWork'
import qualityToday from '@/pages/screen/C2Meeting/qualityToday'
import workInProcess from '@/pages/screen/C2Meeting/workInProcess'
import unPlanned from '@/pages/screen/C2Meeting/unPlanned'
import Quality from '@/pages/screen/C2Meeting/quality'
import SecurityEnv from '@/pages/screen/C2Meeting/securityEnv'
import QualityManagement from '@/pages/screen/C2Meeting/qualityManagement'
import WeldingManagement from '@/pages/screen/C2Meeting/welding'
export default {
  name: 'MorningMeeting',
  components: {
    SecurityEnv,
    CoordinateB1,
    C1Main,
    DiviceC1,
    EnergyC1,
    ProductToday,
    ProductYest,
    Oath,
    ScreenSwitch,
    Quality,
    qualityToday,
    unPlanned,
    workInProcess,
    constructionWork,
    QualityManagement,
    WeldingManagement
  },
  layout: 'screenLayout',
  data: () => {
    return {
      active: 1,
      cDate: '',
      selectedValue: '',
      pageOrder: [1, 3, 5, 6, 9, 2, 4, 13],
      dropdownList: [
        {
          text: '生产',
          commands: [
            // {
            //   text: '综判量',
            //   value: 2,
            //   disabled: false
            // },
            {
              text: '昨日生产情况',
              value: 3,
              disabled: false
            },
            {
              text: '当月生产情况',
              value: 2,
              disabled: false
            },
            {
              text: '当日生产情况',
              value: 4,
              disabled: false
            },
            {
              text: '在制品',
              value: 12,
              disabled: false
            }
          ],
          disabled: false
        },
        {
          text: '质量',
          //  commands: ['当日质量概况', '非计划', '质量管理体系'],
          commands: [
            {
              text: '当日质量概况',
              value: 10,
              disabled: false
            },
            {
              text: '成材率、非计划',
              value: 11,
              disabled: false
            },
            {
              text: '质量管理体系',
              value: 15,
              disabled: false
            },
            {
              text: '补焊数据分析',
              value: 16,
              disabled: false
            }
          ],
          disabled: false
        }
        //   {
        //     text: '能源',
        //     disabled: true,
        //     commands: [
        //       {
        //         text: '昨日主线能源消耗情况汇总',
        //         value: 5,
        //         disabled: true
        //       },
        //       {
        //         text: '昨日主线能源消耗情况汇总',
        //         value: 5,
        //         disabled: false
        //       },
        //       {
        //         text: '昨日热处理能源消耗情况汇总',
        //         value: 5,
        //         disabled: false
        //       }
        //     ]
        //   }
        //   {
        //     text: '能源',
        //     commands: ['昨日主线能源消耗情况汇总', '在制品', '产量']
        //   },
        //   {
        //     text: '第一炼钢厂',
        //     commands: ['设备']
        //   },
        //   {
        //     text: '生产晨会看板',
        //     commands: ['国贸出口订单', '在制品', '产量']
        //   }
        // 可以添加更多的下拉菜单配置
      ],
      leftList: [
        {
          name: '质量宣誓',
          value: 1,
          disabled: false
        }
        //   {
        //     name: '昨日生产情况',
        //     value: 3,
        //     disabled: false
        //   }
      ],
      rightList: [
        //   {
        //     name: '原钢种工序一次合格率',
        //     value: 8,
        //     disabled: false
        //   },
        //   {
        //     name: '当月生产情况',
        //     value: 2,
        //     disabled: false
        //   },
        {
          name: '能源',
          value: 5,
          disabled: false
        },
        {
          name: '设备',
          value: 6,
          disabled: false
        },
        {
          name: '异常记录',
          value: 9,
          disabled: false
        },
        {
          name: '跟踪事项',
          value: 7,
          disabled: false
        },
        {
          name: '检维修施工作业公示',
          value: 13,
          disabled: false
        }
      ]
    }
  },
  computed: {
    selectDate: function() {
      return moment(this.cDate || '').format('yyyy-MM-DD')
    }
  },
  created() {
    this.cDate = moment()
  },
  mounted() {
    if (!window.LAY_EXCEL) {
      const a = require('lay-excel')
    }
  },
  methods: {
    handleCheckAllChange(val) {
      console.log('allvalue', val)
    },
    handleCommand(item) {
      item.checked = true
      this.active = item.value
      console.log('执行命令:', '来自', item)
      // 这里可以执行命令对应的逻辑
    },
    changeDate($event) {
      this.cDate = $event
    },
    chagePagedown(index) {
      index = this.active
      let pageNumber = this.pageOrder.indexOf(index)
      // index += 1
      this.active = this.pageOrder[pageNumber + 1]
      console.log('aa', this.active)
    },
    chagePageup(index) {
      index = this.active
      let pageNumber = this.pageOrder.indexOf(index)
      // index -= 1
      this.active = this.pageOrder[pageNumber - 1]
      // console.log('bb', this.active)
    },
    changeStatus(status, type) {
      this.leftList.forEach(item => {
        if (item.value === type) {
          item.disabled = status
        }
      })
      this.rightList.forEach(item => {
        if (item.value === type) {
          item.disabled = status
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.screen-wrapper {
  position: relative;
  display: flex;
  height: 100vh;
  width: 100vw;
  flex-direction: column;
  background: #041a21 url('../../../assets/images/screen/screen-bg.png') repeat
    center;
  &:after {
    content: '';
    position: absolute;
    left: 25px;
    right: 25px;
    height: 4px;
    bottom: 8px;
    background: url('../../../assets/images/screen/footer-line.png') no-repeat;
    background-size: 100% 100%;
  }
  .screen-header {
    height: 71px;
    margin: 0 25px;
    &-inner {
      position: relative;
      display: flex;
      width: 100%;
    }
    .header-side {
      position: relative;
      flex: 1;
      margin: 7px 0;
      white-space: nowrap;
      border-top: 1px solid #136480;
      align-items: center;
      display: flex;
      overflow: hidden;
      .tab-box {
        flex: 1;
        overflow-y: hidden;
        overflow-x: auto;
      }
      &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 1px;
        left: 0;
        bottom: 0;
        background: url(../../../assets/images/screen/header-line.png) repeat
          left;
      }
    }
    .header-left {
      margin-right: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:after {
        content: '';
        position: absolute;
        left: -16px;
        top: 0;
        bottom: 0;
        padding: 0;
        margin: auto;
        display: block;
        width: 12px;
        height: 40px;
        box-sizing: border-box;
        background: #1fc6ff;
        clip-path: polygon(
          5px 0,
          calc(100%) 0,
          calc(100%) 1px,
          5px 1px,
          1px 5px,
          1px calc(100% - 5px),
          5px calc(100% - 1px),
          100% calc(100% - 1px),
          100% 100%,
          calc(100% - 5px) 100%,
          5px 100%,
          0 calc(100% - 5px),
          0 5px
        );
      }
      .tab-box {
        flex: 1;
        text-align: right;
      }
    }
    .header-right {
      margin-left: 8px;
      text-align: left;
      &:after {
        content: '';
        position: absolute;
        right: -16px;
        top: 0;
        bottom: 0;
        padding: 0;
        margin: auto;
        display: block;
        width: 12px;
        height: 40px;
        box-sizing: border-box;
        background: #1fc6ff;
        transform: rotate(180deg);
        clip-path: polygon(
          5px 0,
          calc(100%) 0,
          calc(100%) 1px,
          5px 1px,
          1px 5px,
          1px calc(100% - 5px),
          5px calc(100% - 1px),
          100% calc(100% - 1px),
          100% 100%,
          calc(100% - 5px) 100%,
          5px 100%,
          0 calc(100% - 5px),
          0 5px
        );
      }
      &:before {
        background: url(../../../assets/images/screen/header-line2.png) repeat
          right;
      }
    }
    .header-btn {
      position: relative;
      display: inline-block;
      height: 35px;
      margin: 0 5px;
      padding: 0 10px;
      min-width: 6%;
      background: rgba(31, 198, 255, 0.12);
      border: 1px solid rgba(31, 198, 255, 0.2);
      font-weight: 400;
      font-size: 12px;
      line-height: 35px;
      text-align: center;
      color: #1fc6ff;
      cursor: pointer;
      &.active {
        color: #fff;
        font-weight: bold;
        background: rgba(31, 198, 255, 0.3);
      }
      &.disabled {
        color: #999;
        font-weight: bold;
        background: rgba(70, 77, 79, 0.2);
      }
      &:after {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        border: 1px solid rgba(31, 198, 255, 0.2);
      }
    }
    .header-title {
      position: relative;
      height: 71px;
      padding: 3px 0;
      img {
        display: block;
        height: 100%;
        margin: auto;
        user-select: none;
        -webkit-user-drag: none;
      }
    }
  }
  .screen-content {
    flex: 1;
    margin: 25px 25px 32px;
    overflow: hidden;
    position: relative;
  }
  .header-arrow {
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 80px;
    height: 20px;
    background-image: linear-gradient(100grad, #1ec5fe, #106685);
    animation: move-arrows 1s linear infinite;
    transform-origin: center;
    mask-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAUCAYAAAAa2LrXAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAH8SURBVHgB7ZhLSgNBEIb/6uBbURcKgo/ZuHChCLoxopgjeAO9gd4gnkBvoDfwCBFFoxDxBSq4iUFwIfhANCYhKXvEQMg0JulpSC38FiGpRfFRNVXpHsCAd8jTXpITXoL74ID+FPeOnPDCTIpb4ABJfgoGOQYSzFgqtWITIfHlerJYVAUMPGUxhZBI81OGyCYIP50lwsroIW8jBN1fmNJ5Wn7zjY0e8AzCIMwvWMAvLOvPdPlnWMnBdhwT47MiX7giCvMjU1DvFo/bkPC/lmN6ZHYy87QKC4ZS3Nmmx4QJnRX57jMLdAoLJPkpUzAdozTlEENVp70jtto5j7P0mevAfnWnh494EhZI8lNoQFIv7zX9eMfhSDJSwvhYkidggRQ/Qg2M41LEhn6847DANC6I4OZ+jm5gQbP9ahZQgqRkv7oK2GxJyX6q7ozthpjy14478hwiX5P86hvhpO4uB44NcX1s2IAFpu7mFa4fo3QLC5rpV/tP5L94f/qRZLlaSPCjhuSArUyU1uFIrqhw9xClK1ggxY/qlpN0lRPkF6kOeGfcxwUkXcn579jyWcScFU+YX+AYw1nsupLzef7AnKviSfQLFFDfBdf1Mnl1Iefz2oULnadgI2dCml+ggOl5OteL0b+k76k8rBZyJS+z9PauL+kl4GmgA5cIiTS/b5T7QUH8z5WLAAAAAElFTkSuQmCC');
    mask-size: contain;
  }
  .header-arrow-right {
    left: auto;
    right: 15px;
    transform: rotate(180deg);
  }
  @keyframes move-arrows {
    0% {
      mask-position: 0 0;
    }
    100% {
      mask-position: 40px 0;
    }
  }
}
/deep/ * {
  *::-webkit-scrollbar {
    width: 6px; /*对垂直流动条有效*/
    height: 6px; /*对水平流动条有效*/
    margin-left: 5px;
  }
  /*定义滑块颜色、内阴影及圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 7px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.15);
    background: #1fc6ff;
    opacity: 0.5;
  }
}
/deep/ .screen-dialog {
  .el-dialog {
    position: relative;
    border: 1px solid #0c4d63;
    background: #041a21;
    box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.12),
      0px 4px 8px rgba(0, 0, 0, 0.08), 0px 4px 16px 4px rgba(0, 0, 0, 0.04);
    &:after {
      content: '';
      position: absolute;
      top: -6px;
      left: 0;
      width: 59px;
      height: 13px;
      background: url(../../../assets/images/screen/dialog-top.png) no-repeat
        center;
    }
    .el-dialog__headerbtn {
      top: -19px;
      right: -19px;
      width: 36px;
      height: 36px;
      background: #0b3f67;
      text-align: center;
      line-height: 20px;
      /* 亮色 */
      border: 1px solid #1fc6ff;
      box-shadow: inset 0px 0px 10px #0e9cff;
      border-radius: 6px;
      transform: rotate(-45deg);
      .el-icon {
        transform: rotate(-45deg);
        font-size: 26px;
        color: #fff;
      }
      &:hover {
        box-shadow: inset 0px 0px 15px #54e1fa;
      }
    }
    .el-dialog__header {
      padding: 24px;
    }
    .custom-dialog-title {
      position: relative;
      padding-left: 66px;
      padding-right: 10px;
      height: 44px;
      font-style: normal;
      font-weight: 1000;
      font-size: 22px;
      line-height: 44px;
      color: #1fc6ff;
      background: #0f2b3f url(../../../assets/images/screen/dialog-title-bg.png)
        no-repeat left bottom;
      .btn-box {
        float: right;
        line-height: 40px;
        white-space: nowrap;
        color: #fff;
      }
    }
    .el-dialog__body {
      padding-top: 0;
    }
  }
  .el-upload:focus {
    color: #fff;
  }
  .el-textarea__inner,
  .el-input__inner {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
  }
  .el-select-dropdown {
    color: #fff;
    background: rgba(9, 60, 77);
    border-color: rgba(31, 198, 255, 0.6);
  }
  .el-select-dropdown__item {
    color: #fff;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background: rgba(4, 26, 33);
  }
  .el-popper[x-placement^='bottom'] .popper__arrow:after {
    border-bottom-color: rgba(9, 60, 77);
  }
  .el-divider__text {
    background-color: #041a21;
    color: #1fc6ff;
  }
  .el-divider {
    background-color: #58627a;
  }
  .el-form-item__label {
    color: #e0f9fc;
  }
  .el-table {
    .cell {
      padding: 0 5px;
    }
  }
  .el-date-editor {
    .el-input__inner {
      padding-left: 30px;
    }
    &.el-input--small .el-input__icon {
      color: #fff;
    }
  }
  .el-input__inner,
  .el-textarea__inner {
    padding: 0 5px;
  }
}
/deep/ .el-table {
  .table-total td.el-table__cell {
    background: rgba(31, 198, 255, 0.2);
  }
}
/deep/ .el-table {
  background: #041a21;
  color: #def0ff;
  &.font-table {
    font-size: 16px;
    td.big-font {
      font-size: 20px;
      .cell {
        line-height: 1.3;
      }
    }
  }
  &.font-big-table {
    font-size: 20px;
    td.big-font {
      font-size: 24px;
      .cell {
        line-height: 1.5;
      }
    }
  }
  &.el-table--medium .el-table__cell {
    padding: 15px 0;
  }
  &.center-table .el-table__cell {
    text-align: center;
    &.is-left {
      text-align: left;
    }
  }
  .red-row {
    color: #ff0000;
  }
  .split-row {
    td.el-table__cell {
      border-bottom: 4px solid #255a6c;
    }
  }
  tr {
    background: transparent;
  }
  .el-table__fixed-right {
    tr {
      background: #041a21;
    }
  }
  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background: transparent;
  }
  th.el-table__cell {
    background: transparent;
    & > .cell {
      white-space: pre-line; /*保留换行符*/
      padding-left: 5px;
      padding-right: 5px;
    }
  }

  .el-table__cell:first-child .cell,
  .cell {
    padding-left: 3px;
    padding-right: 3px;
  }
  &.el-table--border th.el-table__cell,
  td.el-table__cell,
  th.el-table__cell.is-leaf {
    border-bottom: 1px solid #0b2a34;
  }
  &.el-table--border .el-table__cell {
    border-right: 2px solid #0b2a34;
  }
  thead.is-group th.el-table__cell,
  .el-table__footer-wrapper tbody td.el-table__cell,
  .el-table__header-wrapper tbody td.el-table__cell,
  th.el-table__cell {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-top: none;
    white-space: pre-line; /*保留换行符*/
  }
  &.el-table--border {
    border-width: 0;
    border-color: #0b2a34;
  }
  &:before {
    background: transparent;
  }
  &:after {
    background: transparent;
  }
  &.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell,
  .el-table__body tr.hover-row > td.el-table__cell {
    background: rgba(31, 198, 255, 0.1);
  }
  .el-table__fixed-right-patch {
    background: #041a21;
    border: none;
  }
  .el-table__fixed,
  .el-table__fixed-right {
    box-shadow: 0 0 14px rgba(127, 191, 255, 0.15);
    margin-right: -10px;
  }
  .el-loading-mask {
    background: rgba(175, 223, 239, 0.6);
  }
  .el-table__empty-text {
    color: #fff6ee;
  }
  /*全局滚动条样式 chrome内核*/
  /*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/

  *::-webkit-scrollbar {
    width: 6px; /*对垂直流动条有效*/
    height: 6px; /*对水平流动条有效*/
  }

  /*定义滚动条的轨道颜色、内阴影及圆角*/
  *::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.15);
    border-radius: 3px;
  }

  /*定义滑块颜色、内阴影及圆角*/
  *::-webkit-scrollbar-thumb {
    border-radius: 7px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.15);
    background: #1fc6ff;
    opacity: 0.5;
  }

  /*定义两端按钮的样式*/
  *::-webkit-scrollbar-button {
    display: none;
  }

  /*定义右下角汇合处的样式*/
  *::-webkit-scrollbar-corner {
    display: none;
  }
}
/deep/ .screen-input {
  .el-textarea__inner,
  .el-input__inner {
    background: rgba(31, 198, 255, 0.2);
    color: #fff;
    border-color: rgba(31, 198, 255, 0.6);
    padding-right: 5px;
  }
  .el-input__prefix {
    color: #fff;
  }
  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    background: rgba(31, 198, 255, 0.3);
    border-color: rgba(31, 198, 255, 0.6);
    //border: 1px solid #1fc6ff;
  }
  .el-radio-button--mini .el-radio-button__inner {
    background: #d8edff;
  }
}
/deep/ .tabs-class {
  display: flex;
  flex-direction: row;
  .tab-pane {
    color: #ffffffbf;
    margin-right: 21px;
  }
  .tab-pane-active {
    color: #ffffff;
  }
  .tab-pane-title-class {
    display: flex;
    flex-direction: column;
    position: relative;
    .tab-pane-img {
      .tab-pane-img2 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        max-width: 100px;
      }
      .tab-pane-img1 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        max-width: 90px;
        margin-bottom: 7px;
        margin-right: auto;
        margin-left: auto;
      }
    }
  }
}
.el-dropdown-link {
  cursor: pointer;
  color: #1db9ee;
  font-size: 12px;
}
.el-icon-arrow-down {
  font-size: 12px;
}
</style>
