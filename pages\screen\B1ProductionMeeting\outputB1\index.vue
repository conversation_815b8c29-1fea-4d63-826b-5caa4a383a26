<template>
  <div class="content">
    <div class="content-item top">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="9"
          class="full-height">
          <el-row
            :gutter="32"
            class="full-height">
            <el-col
              :span="12"
              class="full-height">
              <screen-border title="钢产量（昨日）">
                <template v-slot:headerRight>
                  <span
                    v-command="'/screen/B1ProductionMeeting/edit'"
                    class="screen-btn"
                    @click="steelYesterday.dialogVisible = true">
                    <el-icon class="el-icon-edit-outline"/>
                    操作
                  </span>
                </template>
                <div class="chart-wrapper">
                  <div
                    class="chart">
                    <steel-bars-chart
                      :show-legend="false"
                      :chart-data="steelYesterday.bar1"
                      :chart-data2="steelYesterday.bar2"
                      :x-data="['一炼钢吨位', '一炼钢炉数']"/>
                  </div>
                  <div
                    class="fail-reason"
                    style="height: 15%">
                    <template v-if="steelYesterday.failReason">
                      <span>未完成原因：</span><span>{{ steelYesterday.failReason || '无' }}</span>
                    </template>
                  </div>
                </div>
              </screen-border>
            </el-col>

            <el-col
              :span="12"
              class="full-height">
              <screen-border title="钢产量（月度）">
                <div class="chart-wrapper">
                  <div
                    class="cards">
                    <div class="card">
                      <span class="name">累计产量</span>
                      <span class="num"><em>{{ steelMonth.output }}</em> t</span>
                    </div>
                    <div class="card">
                      <span class="name">超欠</span>
                      <span class="num">
                        <em :class="{'red': steelMonth.targetSchedule < 0, 'green': steelMonth.targetSchedule > 0}">
                          {{ steelMonth.targetSchedule }}
                        </em>
                        t</span>
                    </div>
                    <div class="card">
                      <span class="name">超欠进度</span>
                      <span class="num"><em>{{ steelMonth.percent }}</em> %</span>
                    </div>
                  </div>
                  <div style="flex: 1">
                    <single-bars-chart
                      :show-legend="false"
                      :chart-data="steelMonth.bar1"
                      :x-data="['一炼钢吨位']"/>
                  </div>
                </div>
              </screen-border>
            </el-col>
          </el-row>
        </el-col>

        <el-col
          :span="15"
          class="full-height">
          <custom-table
            :title="'产量指标'"
            :setting="tableObj1.setting"
            :merge-set="tableObj1.mergeObj"
            :url-list="tableObj1.url.list"
            :url-save="tableObj1.url.save"
            :show-table="false"
            :select-date="selectDate"
            @change="getIndex">
            <template
              v-slot:content>
              <el-row
                :gutter="32"
                class="full-height">
                <el-col
                  :span="6"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div class="chart-tit">
                      单日炉产 <small>(吨)</small>
                    </div>
                    <div class="chart">
                      <bars-chart
                        ref="chart1"
                        :bar-width="46"
                        :show-label="true"
                        :show-legend="false"
                        :unit="''"
                        :color="['#FF2855', '#F5B544', '#2772F0']"
                        :chart-data="chartData.bar1"
                        :x-data="chartData.bar1X"/>
                    </div>
                  </div>
                </el-col>
                <el-col
                  :span="6"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div class="chart-tit">
                      当日铁耗 <small>(kg/吨)</small>
                    </div>
                    <div class="chart">
                      <bars-chart
                        ref="chart1"
                        :bar-width="46"
                        :show-label="true"
                        :show-legend="false"
                        :unit="'kg/吨'"
                        :color="['#FF2855', '#F5B544', '#2772F0']"
                        :chart-data="chartData.bar2"
                        :x-data="chartData.bar2X"/>
                    </div>
                  </div>
                </el-col>
                <el-col
                  :span="6"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div class="chart-tit">
                      当日接铁量 <small>(吨)</small>
                    </div>
                    <div class="chart">
                      <bars-chart
                        ref="chart1"
                        :bar-width="46"
                        :show-label="true"
                        :show-legend="false"
                        :unit="'吨'"
                        :color="['#FF2855', '#F5B544', '#2772F0']"
                        :chart-data="chartData.bar3"
                        :x-data="chartData.bar3X"/>
                    </div>
                  </div>
                </el-col>
                <el-col
                  :span="6"
                  class="full-height">
                  <div class="chart-wrapper">
                    <div class="chart-tit">
                      钢铁料消耗 <small>(kg/吨)</small>
                    </div>
                    <div class="chart">
                      <bars-chart
                        ref="chart1"
                        :bar-width="46"
                        :show-label="true"
                        :show-legend="false"
                        :unit="'kg/吨'"
                        :color="['#FF2855', '#F5B544', '#2772F0']"
                        :chart-data="chartData.bar4"
                        :x-data="chartData.bar4X"/>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </template>
          </custom-table>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <custom-table
        :title="'班计划完成情况'"
        :setting="tableObj2.setting"
        :url-list="tableObj2.url.list"
        :url-save="tableObj2.url.save"
        :select-date="selectDate"
        :table-class="'big-table'"/>
    </div>
    <!--产量-->
    <el-dialog
      :visible.sync="steelYesterday.dialogVisible"
      :width="'1300px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="产量详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <span
              class="screen-btn"
              @click="exportSteel">
              <el-icon class="el-icon-export"/>
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveSteel">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          产量详情
        </div>
      </template>
      <el-form
        v-loading="syncLoading"
        :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="steelYesterday.gridData"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="45"/>
          <el-table-column
            property="plt"
            label="产线"
            width="100">
            <template v-slot="{ row }">
              <el-input
                v-model="row.plt" />
            </template>
          </el-table-column>
          <el-table-column
            property="unit"
            label="单位">
            <template v-slot="{ row }">
              <el-select
                :popper-append-to-body="false"
                v-model="row.unit">
                <el-option
                  v-for="(item, index) in unitList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            property="plan"
            label="计划">
            <template v-slot="{ row }">
              <el-input
                v-model="row.plan" />
            </template>
          </el-table-column>
          <el-table-column
            property="reality"
            label="实绩">
            <template v-slot="{ row }">
              <el-input
                v-model="row.reality"
                :class="{'input-green': row.unit !== '炉数'}" />
            </template>
          </el-table-column>
          <el-table-column
            property="complete"
            label="是否完成">
            <template v-slot="{ row }">
              <el-select
                :popper-append-to-body="false"
                v-model="row.complete">
                <el-option
                  v-for="(item, index) in finishList"
                  :key="index"
                  :value="item">
                  {{ item }}
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            property="unfinishedCause"
            label="未完成原因">
            <template v-slot="{ row }">
              <el-input v-model="row.unfinishedCause" />
            </template>
          </el-table-column>
          <el-table-column
            property="responsibleUnit"
            label="责任单位">
            <template v-slot="{ row }">
              <el-input v-model="row.responsibleUnit" />
            </template>
          </el-table-column>
          <el-table-column
            property="cumulativeOutput"
            label="月计划产量">
            <template v-slot="{ row }">
              <el-input
                v-model="row.monplan"
                :class="{'input-green': row.unit !== '炉数'}" />
            </template>
          </el-table-column>
          <el-table-column
            property="targetProduction"
            label="月目标产量">
            <template v-slot="{ row }">
              <el-input
                v-model="row.targetProduction"
                :class="{'input-green': row.unit !== '炉数'}" />
            </template>
          </el-table-column>
          <el-table-column
            property="cumulativeOutput"
            label="累计产量">
            <template v-slot="{ row }">
              <el-input
                v-model="row.cumulativeOutput"
                :class="{'input-green': row.unit !== '炉数'}" />
            </template>
          </el-table-column>
          <el-table-column
            property="targetSchedule"
            label="超欠目标进度(%)">
            <template v-slot="{ row }">
              <el-input v-model="row.targetSchedule" />
            </template>
          </el-table-column>
          <el-table-column
            property="targetSchedule"
            label="日需均产">
            <template v-slot="{ row }">
              <el-input v-model="row.avgDailyProduction" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index, 'steelYesterday')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
        <br>
        <div class="text-center">
          <span
            v-if="canEdit"
            class="screen-btn"
            @click="addGridData('steelYesterday')">
            <el-icon class="el-icon-circle-plus-outline"/>
            增加数据
          </span>
        </div>
      </el-form>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/B1ProductionMeeting/component/custom-table'
import { post } from '@/lib/Util'
import * as _ from 'lodash'
import {
  findSteelOutputByDate,
  HeatTreatmentYieldTask,
  saveBoardParameter,
  saveSteelOutput,
  steelOutputTask
} from '@/api/screen'
import { math } from '@/lib/Math'
import {
  planCompletionStatusFind,
  planCompletionStatusSave,
  steelOutputBfFind,
  steelOutputBfSave,
  yieldIndicatorsFind,
  yieldIndicatorsSave
} from '@/api/screenB1Production'
import BarsChart from '@/pages/screen/B1ProductionMeeting/component/bars-chart'
export default {
  name: 'OutputB1',
  components: {
    BarsChart,
    CustomTable,
    SingleBarsChart,
    SteelBarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      steelOutputTask: steelOutputTask,
      HeatTreatmentYieldTask: HeatTreatmentYieldTask,
      loading: false,
      steelYesterday: {
        bar1: [
          {
            value: 0,
            plan: 0,
            unit: '吨'
          }
        ],
        bar2: [
          {
            value: 0,
            plan: 0,
            unit: '炉'
          },
          {
            value: 0,
            plan: 0,
            unit: '炉'
          }
        ],
        failReason: '',
        notice: '',
        noticeEdit: '',
        gridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      steelMonth: {
        bar1: [
          {
            value: 0,
            plan: 0,
            unit: '吨'
          }
        ],
        bar2: [
          {
            value: 0,
            plan: 0,
            unit: '炉'
          },
          {
            value: 0,
            plan: 0,
            unit: '炉'
          }
        ],
        output: 0,
        targetSchedule: 0,
        percent: 0
      },
      chartData: {
        bar1: [],
        bar1X: ['实际', '计划', '平均'],
        bar2: [],
        bar2X: ['实际', '平均'],
        bar3: [],
        bar3X: ['实际', '计划'],
        bar4: [],
        bar4X: ['实际', '计划']
      },

      tableObj1: {
        url: {
          save: yieldIndicatorsSave,
          list: yieldIndicatorsFind
        },
        mergeObj: {},
        setting: [
          {
            keyQuery: 'project',
            keySave: 'project',
            label: '指标名'
          },
          {
            keyQuery: 'reality',
            keySave: 'reality',
            label: '实际'
          },
          {
            keyQuery: 'plan',
            keySave: 'plan',
            label: '计划'
          },
          {
            keyQuery: 'average',
            keySave: 'average',
            label: '平均'
          },
          {
            keyQuery: 'homeworkdays',
            keySave: 'homeworkDays',
            label: '月作业天数'
          }
        ]
      },
      tableObj2: {
        url: {
          save: planCompletionStatusSave,
          list: planCompletionStatusFind
        },
        mergeObj: {},
        setting: [
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次',
            width: '100'
          },
          {
            keyQuery: 'groups',
            keySave: 'groups',
            label: '班别',
            width: '100'
          },
          {
            keyQuery: 'sybplan',
            keySave: 'sybPlan',
            label: '事业部计划',
            width: '120'
          },
          {
            keyQuery: 'dynamicplanning',
            keySave: 'dynamicPlanning',
            label: '动态计划',
            width: '120'
          },
          {
            keyQuery: 'ondutyproduction',
            keySave: 'onDutyProduction',
            label: '当班炉数',
            width: '100'
          },
          {
            keyQuery: 'zeroth',
            keySave: 'zeroth',
            label: '0#机',
            width: '100'
          },
          {
            keyQuery: 'first',
            keySave: 'first',
            label: '1#机',
            width: '100'
          },
          {
            keyQuery: 'second',
            keySave: 'second',
            label: '2#机',
            width: '100'
          },
          {
            keyQuery: 'third',
            keySave: 'third',
            label: '3#机',
            width: '100'
          },
          {
            keyQuery: 'analysis',
            keySave: 'analysis',
            class: 'red',
            label: '原因分析'
          }
        ]
      },
      unitList: ['吨位', '炉数'],
      finishList: ['是', '否']
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.$nextTick(() => {
        this.loadData()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.$nextTick(() => {
        this.getSteal()
      })
    },
    handlePreview(file) {
      try {
        LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
          data = LAY_EXCEL.filterImportData(data, {
            // A: "序号 "
            // B: "产线 "
            // C: ""
            // D: "计划 "
            // E: "实际 "
            // F: "是否完成"
            // G: "未完成原因 "
            // H: "责任单位 "
            // I: "累计产量"
            // J: "超欠目标进度"
            num: 'A',
            plt: 'B',
            unit: 'C',
            plan: 'D',
            reality: 'E',
            complete: 'F',
            unfinishedCause: 'G',
            responsibleUnit: 'H',
            monplan: 'I',
            targetProduction: 'J',
            cumulativeOutput: 'K',
            targetSchedule: 'L',
            avgdailyproduction: 'M'
          })
          // 去除第一行
          const sheet = data[0].Sheet1 || data[0].sheet1
          sheet.shift()
          // 表格信息
          this.steelYesterday.gridData = sheet.map(item => {
            item.unit = item.unit.trim()
            item.targetSchedule = item.targetSchedule.toString()
            if (item.targetSchedule.includes('%')) {
              item.targetSchedule = item.targetSchedule.replace('%', '')
            } else {
              item.targetSchedule = Number(
                math.multiply(Number(item.targetSchedule), 100).toFixed(2)
              )
            }
            return item
          })
          this.$message.success('解析成功！')
        })
      } catch (e) {
        this.$message.warning('解析失败！')
      }
    },
    getSteal() {
      this.steelYesterday.gridData = []
      // this.clearViewData()
      post(steelOutputBfFind, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.clearViewData()
        this.steelYesterday.gridData = res.data.map(item => {
          return {
            num: item.num,
            plt: item.plt,
            unit: item.unit,
            plan: item.plan,
            reality: item.reality,
            complete: item.complete,
            unfinishedCause: item.unfinishedcause,
            responsibleUnit: item.responsibleunit,
            monplan: item.monplan,
            targetProduction: item.targetproduction,
            cumulativeOutput: item.cumulativeoutput,
            targetSchedule: Number(item.targetschedule || 0).toFixed(2),
            avgDailyProduction: item.avgdailyproduction
          }
        })
        if (!res.data.length) return
        this.formatViewData()
      })
    },
    clearViewData() {
      Object.assign(this.steelYesterday, {
        bar1: [
          {
            value: 0,
            plan: 0,
            unit: '吨'
          }
        ],
        bar2: [
          {
            value: 0,
            plan: 0,
            unit: '炉'
          },
          {
            value: 0,
            plan: 0,
            unit: '炉'
          }
        ],
        failReason: '',
        gridData: [],
        gridMerge: []
      })
      this.steelMonth = {
        bar1: [
          {
            value: 0,
            plan: 0,
            unit: '吨'
          }
        ],
        bar2: [
          {
            value: 0,
            plan: 0,
            unit: '炉'
          },
          {
            value: 0,
            plan: 0,
            unit: '炉'
          }
        ],
        output: 0,
        targetSchedule: 0,
        percent: 0
      }
      this.rollYesterday = {
        bar1: [],
        failReason: ''
      }
      this.rollMonth = {
        bar1: [],
        failReason: '',
        output: 0,
        targetSchedule: 0,
        percent: 0
      }
      this.otherYesterday = {
        bar1: [],
        failReason: ''
      }
      this.otherMonth = {
        bar1: [],
        failReason: '',
        targetScheduleD1: '',
        targetScheduleD2: '',
        outputD1: '',
        outputD2: '',
        percentD1: '',
        percentD2: ''
      }
    },
    formatViewData() {
      const b11 = this.steelYesterday.gridData.find(
        item => item.plt.trim() === 'B1' && item.unit.trim() === '吨位'
      )
      const b12 = this.steelYesterday.gridData.find(
        item => item.plt.trim() === 'B1' && item.unit.trim() === '炉数'
      )
      this.steelYesterday.failReason = b11 ? b11.unfinishedCause : ''
      b11 &&
        Object.assign(this.steelYesterday, {
          bar1: [
            {
              value: b11 ? b11.reality : 0,
              plan: b11 ? b11.plan : 0,
              finished: b11.complete !== '否',
              unit: '吨',
              show: true,
              avgDaily: b11.avgDailyProduction ? b11.avgDailyProduction : ''
            }
          ],
          bar2: [
            {
              value: 0,
              plan: 0,
              unit: '炉',
              show: false
            },
            {
              value: b12 ? b12.reality : 0,
              plan: b12 ? b12.plan : 0,
              finished: b12.complete !== '否',
              unit: '炉',
              show: true,
              avgDaily: b12.avgDailyProduction ? b12.avgDailyProduction : ''
            }
          ]
        })
      b11 &&
        Object.assign(this.steelMonth, {
          bar1: [
            {
              value: b11.cumulativeOutput,
              plan: b11.targetProduction || b11.monplan,
              finished: b11.targetSchedule >= 0,
              unit: '吨',
              totalText: b11.targetProduction ? '目标' : '计划',
              targetSchedule: b11.targetSchedule ? b11.targetSchedule : '',
              schedule: b11.targetSchedule
                ? math.divide(
                    math.multiply(
                      b11.targetSchedule || 0,
                      b11.targetProduction || b11.monplan
                    ),
                    100
                  )
                : '',
              avgDaily: b11.avgDailyProduction ? b11.avgDailyProduction : ''
            }
          ],
          targetSchedule: math.divide(
            math.multiply(
              b11.targetSchedule || 0,
              b11.targetProduction || b11.monplan
            ),
            100
          ),
          output: b11.cumulativeOutput,
          percent: b11.targetSchedule
        })
    },
    async saveSteel() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.steelYesterday.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(steelOutputBfSave, params).then(res => {
        //
        if (res.status === 1) {
          this.getSteal()
        } else {
          this.$message.warning('保存失败！')
        }
        const params2 = {
          data: [
            {
              parameter: 'outputNotice',
              content: this.steelYesterday.noticeEdit,
              setDate: this.cDate
            }
          ]
        }
        post(saveBoardParameter, params2).then(res => {
          this.loading = false
          if (res.status === 1) {
            this.steelYesterday.dialogVisible = false
            this.getZZP()
            this.$message.success('保存成功！')
          }
        })
      })
    },
    exportSteel() {
      const data = [
        {
          num: '序号',
          plt: '产线',
          unit: '单位',
          plan: '计划',
          reality: '实际',
          complete: '是否完成',
          unfinishedCause: '未完成原因',
          responsibleUnit: '责任单位',
          monplan: '月计划',
          targetProduction: '月目标',
          cumulativeOutput: '累计产量',
          targetSchedule: '超欠目标进度',
          avgDailyProduction: '日需均产'
        }
      ].concat(
        _.cloneDeep(this.steelYesterday.gridData).map(item => {
          item.targetSchedule = item.targetSchedule + '%'
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `钢产量详情（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
    },
    getIndex(data) {
      this.chartData.bar1 = []
      this.chartData.bar2 = []
      this.chartData.bar3 = []
      this.chartData.bar4 = []
      const obj1 = data.find(item => item.project === '当日炉产')
      obj1 &&
        (this.chartData.bar1 = [
          {
            name: '炉产',
            data: [
              {
                value: obj1.reality,
                itemStyle: {
                  color: obj1.reality > obj1.plan ? '#66cc6a' : '#ff2855'
                }
              },
              {
                value: obj1.plan,
                itemStyle: {
                  color: '#3391ff'
                }
              },
              {
                value: obj1.average,
                itemStyle: {
                  color: '#a146b0'
                }
              }
            ]
          }
        ])
      const obj2 = data.find(item => item.project === '当日铁耗')
      obj2 &&
        (this.chartData.bar2 = [
          {
            name: '铁耗',
            data: [
              {
                value: obj2.reality,
                itemStyle: {
                  color: obj2.reality > obj2.average ? '#66cc6a' : '#ff2855'
                }
              },
              {
                value: obj2.average,
                itemStyle: {
                  color: '#a146b0'
                }
              }
            ]
          }
        ])
      const obj3 = data.find(item => item.project === '当日接铁量')
      obj3 &&
        (this.chartData.bar3 = [
          {
            name: '接铁量',
            data: [
              {
                value: obj3.reality,
                itemStyle: {
                  color: obj3.reality > obj3.plan ? '#66cc6a' : '#ff2855'
                }
              },
              {
                value: obj3.plan,
                itemStyle: {
                  color: '#3391ff'
                }
              }
            ]
          }
        ])
      const obj4 = data.find(item => item.project === '钢铁料消耗')
      obj4 &&
        (this.chartData.bar4 = [
          {
            name: '钢铁料消耗',
            data: [
              {
                value: obj4.reality,
                itemStyle: {
                  color: obj4.reality > obj4.plan ? '#ff2855' : '#66cc6a'
                }
              },
              {
                value: obj4.plan,
                itemStyle: {
                  color: '#3391ff'
                }
              }
            ]
          }
        ])
    },
    getMergeData(rowIndex, columnIndex) {
      const matchLeftTop = this.steelYesterday.gridMerge.find(
        item => item.s.c === columnIndex && item.s.r === rowIndex
      )
      if (matchLeftTop) {
        return [
          matchLeftTop.e.r - matchLeftTop.s.r + 1,
          matchLeftTop.e.c - matchLeftTop.s.c + 1
        ]
      }
      const merged = this.steelYesterday.gridMerge.find(item => {
        return (
          item.s.c < columnIndex &&
          columnIndex <= item.e.c &&
          item.s.r < rowIndex &&
          rowIndex <= item.e.r
        )
      })
      if (merged) {
        console.log(merged)
        return [0, 0]
      }
    },
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    getParam(name, list) {
      const match = list.find(item => item.parameter === name)
      return match ? match.content : null
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .slick {
    position: relative;
    //top: -15px;
    margin-bottom: 10px;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    width: 90px;
    line-height: 24px;
    font-size: 16px;
    white-space: nowrap;
    color: #ffffff;
  }
  span:last-child {
    flex: 1;
    overflow: auto;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    font-size: 0;
    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.green {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }
  .chart-tit {
    font-size: 16px;
    font-weight: bolder;
    color: #ffffff;
    line-height: 20px;
    margin: 10px 0;
    &:before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 6px;
      height: 100%;
      margin-right: 4px;
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
