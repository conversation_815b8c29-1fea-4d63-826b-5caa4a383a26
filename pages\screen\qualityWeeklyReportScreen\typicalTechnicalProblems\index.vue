<template>
  <div class="container">
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="上周快停情况(敬请期待)">
          <custom-table 
            ref="lastWeekStopRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'lastWeekStop'"
            :title="'上周快停情况'" 
            :setting="tableObj.setting" 
            :url-list="tableObj.url.list" 
            :url-save="tableObj.url.save"
            :select-date="selectDate" 
            :dialog-width="'80%'"
          />
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="4月班组快停情况(敬请期待)">
          <custom-table 
            ref="lastMonthStopRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'lastMonthStop'"
            :title="'4月班组快停情况'" 
            :setting="tableObj2.setting" 
            :url-list="tableObj2.url.list"
            :url-save="tableObj2.url.save" 
            :select-date="selectDate"
            :dialog-width="'55%'"
          />
        </screen-border>
      </div>
    </div>
    <div class="chart-row chart-row-layout">
      <div class="chart-box">
        <screen-border title="专利申请情况(敬请期待)">
          <custom-table 
            ref="patentApplicationRef" 
            :show-table="true" 
            :show-edit="true" 
            :key="'patentApplication'"
            :title="'专利申请情况'" 
            :setting="tableObj3.setting" 
            :url-list="tableObj3.url.list" 
            :url-save="tableObj3.url.save"
            :select-date="selectDate" 
            :dialog-width="'80%'"
          />
        </screen-border>
      </div>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityWeeklyReportScreen/components/screen-border.vue'
import CustomTable from '@/pages/screen/qualityWeeklyReportScreen/components/custom-table.vue'
import {
  workshopAssessmentFindAllDate,
  workshopAssessmentSaveAll,
  workshopQualityPerformanceFindAllDate,
  workshopQualityPerformanceSaveAll,
  stakeholderAssessmentFindAllDate,
  stakeholderAssessmentSaveAll,
  offlineFlawDetectionFindAllDate,
  offlineFlawDetectionSaveAll
} from '@/api/screen'

export default {
  name: 'TypicalTechnicalProblems',
  components: {
    CustomTable,
    ScreenBorder
  },
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      highValueInput: '',
      headTailFlawInput: '',
      weeklyBuckleInput: '',
      tableObj: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'billetNo',
            keySave: 'billetNo',
            label: '板坯号',
            width: '285'
          },
          {
            keyQuery: 'rollingTime',
            keySave: 'rollingTime',
            label: '轧制时间',
            width: '250'
          },
          {
            keyQuery: 'steelType',
            keySave: 'steelType',
            label: '钢种',
            width: '250'
          },
          {
            keyQuery: 'orderThickness',
            keySave: 'orderThickness',
            label: '订单厚度',
            width: '250'
          },
          {
            keyQuery: 'slabWidth',
            keySave: 'slabWidth',
            label: '坯料宽度',
            width: '250'
          },
          {
            keyQuery: 'downgradeTon',
            keySave: 'downgradeTon',
            label: '降级吨位',
            width: '250'
          },
          {
            keyQuery: 'responsibleUnit',
            keySave: 'responsibleUnit',
            label: '责任单位',
            width: '250'
          },
          {
            keyQuery: 'assessment',
            keySave: 'assessment',
            label: '考核',
            width: '250'
          }
        ],
        url: {
          list: workshopAssessmentFindAllDate,
          save: workshopAssessmentSaveAll
        }
      },
      tableObj2: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'team',
            keySave: 'team',
            label: '班组',
            width: '300'
          },
          {
            keyQuery: 'count',
            keySave: 'count',
            label: '数量',
            width: '250'
          },
          {
            keyQuery: 'tonnage',
            keySave: 'tonnage',
            label: '吨位',
            width: '250'
          }
        ],
        url: {
          list: workshopQualityPerformanceFindAllDate,
          save: workshopQualityPerformanceSaveAll
        }
      },
      tableObj3: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'unit',
            keySave: 'unit',
            label: '单位',
            width: '250'
          },
          {
            label: '提交已受理',
            width: '500',
            children: [
              {
                keyQuery: 'invention',
                keySave: 'invention',
                label: '发明指标',
                width: '250'
              },
              {
                keyQuery: 'inventionPatent',
                keySave: 'inventionPatent',
                label: '发明专利',
                width: '250'
              },
              {
                keyQuery: 'utilityModel',
                keySave: 'utilityModel',
                label: '实用新型指标',
                width: '250'
              },
              {
                keyQuery: 'utilityModelPatent',
                keySave: 'utilityModelPatent',
                label: '实用新型专利',
                width: '250'
              }
            ]
          },
          {
            label: '提交暂未受理',
            width: '500',
            children: [
              {
                keyQuery: 'inventionPatentNot',
                keySave: 'inventionPatentNot',
                label: '发明专利',
                width: '250'
              },
              {
                keyQuery: 'utilityModelPatentNot',
                keySave: 'utilityModelPatentNot',
                label: '实用新型',
                width: '250'
              }
            ]
          }
        ],
        url: {
          list: stakeholderAssessmentFindAllDate,
          save: stakeholderAssessmentSaveAll
        }
      },
      tableObj4: {
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'workshop',
            keySave: 'workshop',
            label: '车间',
            width: '100'
          },
          {
            keyQuery: 'equipmentName',
            keySave: 'equipmentName',
            label: '设备名称',
            width: '175'
          },
          {
            keyQuery: 'checkContentAndStandard',
            keySave: 'checkContentAndStandard',
            label: '检查内容和标准',
            width: '170'
          },
          {
            keyQuery: 'equipmentUsage',
            keySave: 'equipmentUsage',
            label: '设备使用情况',
            width: '170'
          },
          {
            keyQuery: 'remark',
            keySave: 'remark',
            label: '备注',
            width: '170'
          }
        ],
        url: {
          list: offlineFlawDetectionFindAllDate,
          save: offlineFlawDetectionSaveAll
        }
      }
    }
  },
  methods: {}
}
</script>

<style scoped lang="less">
.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  gap: 10px;
  background-color: #041a21;
  overflow: hidden;

  .chart-row {
    margin-bottom: 10px;
    height: 50%;
    flex-direction: column;

    &.chart-row-layout {
      width: 50%;
      height: 100%;
    }
  }

  .chart-row,
  .table-row {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 20px;
    width: 50%;
    height: 100%;
  }

  .chart-box,
  .table-box {
    width: 100%;
    flex: 1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .chart-footer {
    margin: 0px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    padding: 5px 8px;
    background: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);
    width: 100%;
  }

  .chart-input {
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      border: 1px solid rgba(31, 198, 255, 0.3);
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      padding: 10px;
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
    }
  }

  /deep/ .el-table {
    background-color: transparent;
    width: 100% !important;
    table-layout: fixed;

    &::before {
      display: none;
    }

    th {
      background-color: rgba(31, 198, 255, 0.3);
      color: #fff;
      border-color: #1fc6ff;
    }

    td {
      background-color: transparent;
      color: #fff;
      border-color: #2e4262;
    }

    tr {
      background-color: transparent;
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-content {
    padding: 10px;
    box-sizing: border-box;
  }
}
</style>
