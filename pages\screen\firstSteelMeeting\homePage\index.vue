<!--首页-->
<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="8"
          class="full-height">
          <screen-border-multi>
            <template v-slot:title>
              <div class="tabs-class">
                <div
                  v-for="(item, index) in tabList"
                  :key="item.id"
                  :class="{'tab-pane-active': item.active}"
                  class="tab-pane"
                  @click="clickTabPane(item, index)">
                  <div class="tab-pane-title-class">
                    <div>{{ item.title }}</div>
                    <div
                      v-if="item.active"
                      class="tab-pane-img">
                      <img
                        class="tab-pane-img2"
                        src="@/assets/images/screen/tab-pane-active-line2.png"
                        alt="">
                      <img
                        class="tab-pane-img1"
                        src="@/assets/images/screen/tab-pane-active-line.png"
                        alt="">
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-slot:default>
              <div
                v-if="active===0"
                style="display: flex;flex-direction: column;width: 100%;height: 100%">
                <div class="chart-wrapper">
                  <div class="chart">
                    <first-steel-chart
                      :title="'库存动态储位统计(万元)'"
                      :chart-data="option1.series"
                      :color="['#3391FF','#66CC6A']"
                      :x-data="option1.xData"
                    />
                  </div>
                </div>
                <div class="chart-wrapper">
                  <div
                    class="chart">
                    <first-steel-chart
                      :title="'维修及生产费用完成情况（万元）'"
                      :chart-data="option2.series"
                      :color="['#3391FF','#66CC6A']"
                      :bar-width="20"
                      :x-data="option2.xData"
                    />
                  </div>
                </div>
              </div>
              <div
                v-else-if="active===1"
                class="chart-wrapper">
                <div style="display: flex;justify-content: space-between">
                  <div class="cards">
                    <div class="card">
                      <span class="name">燃动成本</span>
                      <span class="num"><em>{{ fuelData.fuelCost }}</em> 元/t</span>
                    </div>
                    <div class="card">
                      <span class="name">产量</span>
                      <span class="num">
                        <em :class="{'red': fuelData.fuelProduction < 0, 'green': fuelData.fuelProduction > 0}">
                          {{ fuelData.fuelProduction }}
                        </em>
                        t</span>
                    </div>
                  </div>
                  <span
                    class="screen-btn"
                    @click="dialogVisibleFuel = true">
                    查看报表
                  </span>
                </div>

                <div class="chart">
                  <first-steel-pie
                    :chart-data="option3.series"
                    :title="option3.title"
                    :title-num="option3.titleNum"
                    :color="['#3391FF','#55C6D4','#66CC6A','#FFDA35','#FF9800']"
                    :x-data="option3.xData"
                    :show-legend="option3.showLegend"
                  />
                </div>
              </div>

            </template>

          </screen-border-multi>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <screen-border-multi :title="'隐患'">
            <div
              ref="table1"
              class="chart-wrapper">
              <el-table
                :data="hiddenList"
                border
                height="450">
                <el-table-column
                  label="序号"
                  width="50">
                  <template slot-scope="scope">
                    <div>{{ scope.$index + 1 }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="区域"
                  width="60">
                  <template slot-scope="scope">
                    <div>{{ scope.row.area }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="产线"
                  width="60">
                  <template slot-scope="scope">
                    <div>{{ scope.row.productionLine }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="隐患描述">
                  <template slot-scope="scope">
                    <div>{{ scope.row.description }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="隐患等级">
                  <template slot-scope="scope">
                    <div>{{ scope.row.hiddenLevel ==='B'?'一般':scope.row.hiddenLevel ==='C'?'重大':'常规' }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="应对措施">
                  <template slot-scope="scope">
                    <div>{{ scope.row.measure }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="整改计划">
                  <template slot-scope="scope">
                    <div>{{ scope.row.rectificationPlan }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="整改时间">
                  <template slot-scope="scope">
                    <div>{{ scope.row.rectificationDate }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  width="100"
                  label="是否完成">
                  <template slot-scope="scope">
                    <div>{{ scope.row.isFinish==='Y'?'是':'否' }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  width="140"
                  label="完成时间">
                  <template slot-scope="scope">
                    <div>{{ scope.row.finishTime }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="创建时间">
                  <template slot-scope="scope">
                    <div>{{ scope.row.createTime }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="备注">
                  <template slot-scope="scope">
                    <div>{{ scope.row.remarks }}</div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </screen-border-multi>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <screen-border-multi>
            <template v-slot:title>
              <div class="tabs-class">
                <div
                  v-for="(item, index) in tabList3"
                  :key="item.id"
                  :class="{'tab-pane-active': item.active}"
                  class="tab-pane"
                  @click="clickTabPane3(item, index)">
                  <div class="tab-pane-title-class">
                    <div>{{ item.title }}</div>
                    <div
                      v-if="item.active"
                      class="tab-pane-img">
                      <img
                        class="tab-pane-img2"
                        src="@/assets/images/screen/tab-pane-active-line2.png"
                        alt="">
                      <img
                        class="tab-pane-img1"
                        src="@/assets/images/screen/tab-pane-active-line.png"
                        alt="">
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-slot:default>
              <div
                v-if="active3===0"
                ref="table2"
                class="chart-wrapper">
                <el-table
                  :data="checkList"
                  border
                  height="450">
                  <el-table-column
                    label="序号"
                    width="50">
                    <template slot-scope="scope">
                      <div>{{ scope.$index + 1 }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="区域"
                    width="60">
                    <template slot-scope="scope">
                      <div>{{ scope.row.areaName }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="线路"
                    width="60">
                    <template slot-scope="scope">
                      <div>{{ scope.row.routeName }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="隐患描述">
                    <template slot-scope="scope">
                      <div>{{ scope.row.dangerDesc }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="整改情况">
                    <template slot-scope="scope">
                      <div :style="{color:scope.row.rectification==='未整改'?'#FF2855':scope.row.rectification==='已整改'?'#19BE6B':''}">{{ scope.row.rectification }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="整改时间">
                    <template slot-scope="scope">
                      <div>{{ scope.row.rectificationTime }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="备注">
                    <template slot-scope="scope">
                      <div>{{ scope.row.remarks }}</div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div
                v-else-if="active3===1"
                ref="table9"
                class="chart-wrapper">
                <el-table
                  :data="checkOnlineList"
                  border
                  height="450">
                  <el-table-column
                    label="序号"
                    width="50">
                    <template slot-scope="scope">
                      <div>{{ scope.$index + 1 }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="分类名称">
                    <template slot-scope="scope">
                      <div>{{ scope.row.name }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="启用设备路线总数">
                    <template slot-scope="scope">
                      <div>{{ scope.row.routeTotal }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="路线未完成数">
                    <template slot-scope="scope">
                      <div>{{ scope.row.routeNotChked }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="路线漏检率">
                    <template slot-scope="scope">
                      <div>{{ scope.row.routeNotChkedBate }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="任务总数">
                    <template slot-scope="scope">
                      <div>{{ scope.row.totalCount }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="任务漏检数">
                    <template slot-scope="scope">
                      <div>{{ scope.row.notChked }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="漏检率">
                    <template slot-scope="scope">
                      <div :style="{color:scope.row.notChkedBate==='0%'?'':'#FF2855'}">{{ scope.row.notChkedBate }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="完成率">
                    <template slot-scope="scope">
                      <div>{{ scope.row.chkedBate }}</div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>

          </screen-border-multi>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="8"
          class="full-height">
          <screen-border-multi>
            <template v-slot:title>
              <div class="tabs-class">
                <div
                  v-for="(item, index) in tabList2"
                  :key="item.id"
                  :class="{'tab-pane-active': item.active}"
                  class="tab-pane"
                  @click="clickTabPane2(item, index)">
                  <div class="tab-pane-title-class">
                    <div>{{ item.title }}</div>
                    <div
                      v-if="item.active"
                      class="tab-pane-img">
                      <img
                        class="tab-pane-img2"
                        src="@/assets/images/screen/tab-pane-active-line2.png"
                        alt="">
                      <img
                        class="tab-pane-img1"
                        src="@/assets/images/screen/tab-pane-active-line.png"
                        alt="">
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-slot:default>
              <div
                v-if="active2===0"
                ref="table3"
                class="chart-wrapper">
                <el-table
                  :data="troubleList"
                  border
                  height="450">
                  <el-table-column
                    label="序号"
                    width="50">
                    <template slot-scope="scope">
                      <div>{{ scope.$index + 1 }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="故障描述">
                    <template slot-scope="scope">
                      <div>{{ scope.row.faultAppearance }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    width="80"
                    label="操作">
                    <template slot-scope="scope">
                      <span
                        style="cursor: pointer;color: #1FC6FF;text-decoration: underline"
                        @click="clickTroubleItem(scope.row)">查看详情</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div
                v-else-if="active2===1"
                ref="table4"
                class="chart-wrapper">
                <el-table
                  :data="waterList"
                  border
                  height="450">
                  <el-table-column
                    show-overflow-tooltip
                    label="设备名称">
                    <template slot-scope="scope">
                      <div>{{ scope.row.eqName }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="结晶器补水">
                    <template slot-scope="scope">
                      <div :style="{color:scope.row.examineOne.includes('-')?'':'#FF2855'}">{{ scope.row.crystallizeHydrating }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="考核情况">
                    <template slot-scope="scope">
                      <div :style="{color:scope.row.examineOne.includes('-')?'':'#FF2855'}">{{ scope.row.examineOne }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="设备水补水">
                    <template slot-scope="scope">
                      <div :style="{color:scope.row.examineTwo.includes('-')?'':'#FF2855'}">{{ scope.row.equipmentWaterHydrating }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="考核情况">
                    <template slot-scope="scope">
                      <div :style="{color:scope.row.examineTwo.includes('-')?'':'#FF2855'}">{{ scope.row.examineTwo }}</div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div
                v-else-if="active2===2"
                ref="table5"
                class="chart-wrapper">
                <el-table
                  :data="hangList"
                  border
                  height="450">
                  <el-table-column
                    show-overflow-tooltip
                    label="项次">
                    <template slot-scope="scope">
                      <div>{{ scope.row.item }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="内容">
                    <template slot-scope="scope">
                      <div>{{ scope.row.content }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="处理计划">
                    <template slot-scope="scope">
                      <div>{{ scope.row.handlePlan }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    width="100"
                    label="是否完成">
                    <template slot-scope="scope">
                      <div>{{ scope.row.isFinish==='Y'?'是':'否' }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    width="140"
                    label="完成时间">
                    <template slot-scope="scope">
                      <div>{{ scope.row.finishTime }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="备注">
                    <template slot-scope="scope">
                      <div>{{ scope.row.remarks }}</div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>

          </screen-border-multi>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <screen-border-multi :title="'重要事项跟踪'">
            <div
              ref="table6"
              class="chart-wrapper">
              <el-table
                :data="importantList"
                border
                height="450">
                <el-table-column
                  label="序号"
                  width="50">
                  <template slot-scope="scope">
                    <div>{{ scope.$index + 1 }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="事项"
                  width="100">
                  <template slot-scope="scope">
                    <div>{{ scope.row.item }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="内容">
                  <template slot-scope="scope">
                    <div>{{ scope.row.content }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="时间">
                  <template slot-scope="scope">
                    <div>{{ scope.row.time }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="备注">
                  <template slot-scope="scope">
                    <div>{{ scope.row.remarks }}</div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </screen-border-multi>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <screen-border-multi :title="'项目汇报'">
            <div
              ref="table6"
              class="chart-wrapper">
              <el-table
                :data="projectList"
                border
                height="450">
                <el-table-column
                  label="序号"
                  width="50">
                  <template slot-scope="scope">
                    <div>{{ scope.$index + 1 }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="项目"
                  width="100">
                  <template slot-scope="scope">
                    <div>{{ scope.row.item }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="昨日进度">
                  <template slot-scope="scope">
                    <div>{{ scope.row.process }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="今日计划">
                  <template slot-scope="scope">
                    <div>{{ scope.row.todayPlan }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="备注">
                  <template slot-scope="scope">
                    <div>{{ scope.row.remarks }}</div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </screen-border-multi>
        </el-col>
      </el-row>
    </div>

    <!--燃动成本查看报表-->
    <el-dialog
      :visible.sync="dialogVisibleFuel"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="各车间燃动成本报表">
      <template v-slot:title>
        <div class="custom-dialog-title">
          各车间燃动成本报表
        </div>
      </template>
      <el-table
        v-loading="fuelLoading"
        :data="fuelList"
        :height="'calc(100vh - 315px)'"
        border>
        <el-table-column
          align="center"
          show-overflow-tooltip
          prop="name"
          label="项目"
          width="100"/>
        <el-table-column
          align="center"
          show-overflow-tooltip
          prop="unit"
          label="计量单位"/>
        <el-table-column
          align="center"
          show-overflow-tooltip
          prop="price"
          label="单价"/>
        <el-table-column
          align="center"
          show-overflow-tooltip
          prop=""
          label="原料">
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="rawPlan"
            label="计划"/>
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="rawActual"
            label="实际"/>
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="rawCost"
            label="降本"/>
        </el-table-column>
        <el-table-column
          align="center"
          show-overflow-tooltip
          prop=""
          label="炼钢">
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="stePlan"
            label="计划"/>
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="steActual"
            label="实际"/>
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="steCost"
            label="降本"/>
        </el-table-column>
        <el-table-column
          align="center"
          show-overflow-tooltip
          prop=""
          label="精炼">
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="refPlan"
            label="计划"/>
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="refActual"
            label="实际"/>
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="refCost"
            label="降本"/>
        </el-table-column>
        <el-table-column
          align="center"
          show-overflow-tooltip
          prop=""
          label="连铸">
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="conPlan"
            label="计划"/>
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="conActual"
            label="实际"/>
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="conCost"
            label="降本"/>
        </el-table-column>
        <el-table-column
          align="center"
          show-overflow-tooltip
          prop=""
          label="综合(全厂)">
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="compPlan"
            label="计划"/>
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="compActual"
            label="实际"/>
          <el-table-column
            align="center"
            show-overflow-tooltip
            prop="compCost"
            label="降本"/>
        </el-table-column>
        <!--        <el-table-column
          align="center"
          show-overflow-tooltip
          prop="monthPrice"
          label="2022年(7-9)月"/>-->
      </el-table>
    </el-dialog>
    <!--故障详情-->
    <el-dialog
      :visible.sync="dialogVisibleTrouble"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="故障详情">
      <template v-slot:title>
        <div class="custom-dialog-title">
          故障详情
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="dialog-cell">
          <div class="dialog-cell-title">故障时间</div>
          <el-input
            v-model="troubleData.faultTime"
            :rows="3"
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">故障描述</div>
          <el-input
            v-model="troubleData.faultAppearance"
            :rows="3"
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">原因分析</div>
          <el-input
            v-model="troubleData.reason"
            :rows="8"
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">整改措施</div>
          <el-table
            :data="troubleData.faultDetailList"
            border>
            <el-table-column
              align="center"
              label="序号"
              width="50">
              <template v-slot="{ row, $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property="measure"
              label="整改措施">
              <template v-slot="{ row }">
                <el-input v-model="row.measure" />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property="responsiblePerson"
              width="200"
              label="责任人">
              <template v-slot="{ row }">
                <el-input v-model="row.responsiblePerson" />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property="responsiblePerson"
              width="200"
              label="完成时间">
              <template v-slot="{ row }">
                <el-date-picker
                  v-model="row.finishTime"
                  :clearable="false"
                  :size="'mini'"
                  :value-format="'yyyy-MM-dd'"
                  style="width: 160px"
                  class="screen-input"/>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="dialog-cell">
          <div class="dialog-cell-title">考核落实</div>
          <el-input
            v-model="troubleData.examine"
            :rows="4"
            type="textarea"
            placeholder="请输入内容"
            class="dialog-cell-input"/>
        </div>

      </div>

    </el-dialog>
  </div>
</template>
<script>
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import FirstSteelChart from '@/pages/screen/firstSteelMeeting/component/first-steel-chart'
import FirstSteelPie from '@/pages/screen/firstSteelMeeting/component/first-steel-pie.vue'
import { post } from '@/lib/Util'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import {
  firstMeetingBackup2,
  firstMeetingBackup3,
  firstMeetingCheck1,
  firstMeetingCheck4,
  firstMeetingDevice1,
  firstMeetingDevice4,
  firstMeetingDevice5,
  firstMeetingFuel1,
  firstMeetingFuel2,
  firstMeetingHidden1,
  firstMeetingImportant1,
  firstMeetingProject1
} from '@/api/firstMeeting'
import moment from 'moment/moment'
export default {
  name: 'homePage',
  components: {
    ScreenBorder,
    ScreenBorderMulti,
    FirstSteelChart,
    FirstSteelPie
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      active: 0,
      tabList: [
        {
          id: '1',
          active: true,
          title: '备件库存维修'
        },
        {
          id: '2',
          active: false,
          title: '燃动成本'
        }
      ],
      active3: 0,
      tabList3: [
        {
          id: '1',
          active: true,
          title: '联合点检'
        },
        {
          id: '2',
          active: false,
          title: '线上点检'
        }
      ],
      option1: {
        xData: [
          '1月',
          '2月',
          '3月',
          '4月',
          '5月',
          '6月',
          '7月',
          '8月',
          '9月',
          '10月',
          '11月',
          '12月'
        ],
        series: [
          {
            name: '计划',
            type: 'line',
            yAxisIndex: 0,
            barGap: 0,
            data: []
          },
          {
            name: '实际完成',
            type: 'line',
            yAxisIndex: 0,
            barGap: 0,
            smooth: true,
            data: []
          }
        ]
      },
      option2: {
        xData: [
          '运行车间',
          '炼钢车间',
          '原料车间',
          '连铸车间',
          '精炼车间',
          '管理中心',
          '预提费用'
        ],
        series: [
          {
            name: '计划',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: []
          },
          {
            name: '实际',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            smooth: true,
            data: []
          }
        ]
      },
      option3: {
        title: '燃动成本',
        titleNum: '+0',
        showLegend: false,
        xData: [],
        series: [
          {
            name: '燃动成本',
            type: 'pie',
            radius: ['50%', '80%'],
            data: [
              { value: 0, name: '电耗增本' },
              { value: 0, name: '氮耗增本' },
              { value: 0, name: '蒸汽回收增本' },
              { value: 0, name: '煤气回收增本' },
              { value: 0, name: '其他增本' }
            ]
          }
        ]
      },
      dialogVisibleFuel: false,
      fuelData: {
        fuelCost: 0,
        fuelProduction: 0
      },
      fuelLoading: false,
      fuelList: [],
      hiddenLoading: false,
      hiddenList: [],
      //产线类别
      factoryGradeList: [
        {
          id: 'A',
          name: '主要产线'
        },
        {
          id: 'B',
          name: '重要产线'
        },
        {
          id: 'C',
          name: '辅助产线'
        }
      ],
      checkLoading: false,
      checkList: [],
      checkOnlineList: [],
      active2: 0,
      tabList2: [
        {
          id: '0',
          active: true,
          title: '故障'
        },
        {
          id: '1',
          active: false,
          title: '水系统'
        },
        {
          id: '2',
          active: false,
          title: '行车'
        }
      ],
      deviceLoading: false,
      troubleList: [],
      dialogVisibleTrouble: false,
      troubleData: {},
      waterList: [
        {
          id: 'LZ0',
          eqName: '0#连铸机',
          crystallizeHydrating: '',
          examineOne: '',
          equipmentWaterHydrating: '',
          examineTwo: ''
        },
        {
          id: 'LZ1',
          eqName: '1#连铸机',
          crystallizeHydrating: '',
          examineOne: '',
          equipmentWaterHydrating: '',
          examineTwo: ''
        },
        {
          id: 'LZ2',
          eqName: '2#连铸机',
          crystallizeHydrating: '',
          examineOne: '',
          equipmentWaterHydrating: '',
          examineTwo: ''
        },
        {
          id: 'LZ3',
          eqName: '3#连铸机',
          crystallizeHydrating: '',
          examineOne: '',
          equipmentWaterHydrating: '',
          examineTwo: ''
        }
      ],
      hangList: [],
      importantLoading: false,
      importantList: [],
      projectLoading: false,
      projectList: [],
      TimeDta: {
        nowTime: '',
        nowDate: '',
        nowWeek: '',
        firstDayOfMonth: '',
        endDayOfMonth: '',
        lastMonth: '',
        firstMonthDay: '',
        endDay: '',
        year: '',
        lastDay: '',
        WeekBegin: '',
        firstYearDay: '',
        oneFactoryTime: {
          startTime: '',
          endTime: ''
        },
        tomorrow: ''
      },
      newDate: ''
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.$nextTick(item => {
        this.newDate = moment(
          new Date(new Date(this.cDate).getTime() - 24 * 60 * 60 * 1000)
        ).format('yyyy-MM-DD')
        // this.getNowTime()
        this.init()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    // this.getNowTime()
    this.init()
  },
  methods: {
    //备件tab切换
    clickTabPane(item, index) {
      this.tabList.forEach(item => {
        item.active = false
      })
      item.active = true
      this.active = index
    },
    //设备运行tab切换
    clickTabPane2(item, index) {
      this.tabList2.forEach(item => {
        item.active = false
      })
      item.active = true
      this.active2 = index
    },
    //点检tab切换
    clickTabPane3(item, index) {
      this.tabList3.forEach(item => {
        item.active = false
      })
      item.active = true
      this.active3 = index
    },
    //故障点击查看详情
    clickTroubleItem(row) {
      this.troubleData = row
      this.dialogVisibleTrouble = true
    },
    // //获取各时间段
    // getNowTime() {
    //   this.TimeDta.nowTime = moment().format('HH:mm:ss') //当前时间时分秒
    //   this.TimeDta.nowDate = moment().format('YYYY-MM-DD') //当前时间年月日
    //   this.TimeDta.nowWeek = this.getWeek() //当前周
    //   this.TimeDta.firstDayOfMonth = moment()
    //     .startOf('months')
    //     .format('YYYY-MM-DD')
    //   this.TimeDta.endDayOfMonth = moment().format('YYYY-MM')
    //   // 获取上一个月的时间
    //   this.TimeDta.lastMonth = moment(
    //     moment()
    //       .month(moment().month() - 1)
    //       .startOf('month')
    //       .valueOf()
    //   ).format('YYYY-MM')
    //   // 获取今年第一个月
    //   this.TimeDta.firstDay = moment().format('YYYY') + '-01'
    //   // 本月第一天
    //   this.TimeDta.firstMonthDay = moment().format('YYYY-MM') + '-01'
    //   this.TimeDta.endDay = moment().format('YYYY') + '-12'
    //   // 获取今年
    //   this.TimeDta.year = moment().format('YYYY')
    //   //获取昨日时间
    //   this.TimeDta.lastDay = moment(
    //     moment()
    //       .add(-1, 'days')
    //       .startOf('day')
    //       .valueOf()
    //   ).format('YYYY-MM-DD')
    //   // 获取本周周一日期
    //   const weekOfday = moment().format('E')
    //   this.TimeDta.WeekBegin = moment()
    //     .subtract(weekOfday - 1, 'days')
    //     .format('YYYY-MM-DD')
    //   // 本年第一天
    //   this.TimeDta.firstYearDay = moment().format('YYYY') + '-01-01'
    //   this.TimeDta.oneFactoryTime.endTime = this.TimeDta.nowDate
    //   // 获取明天的时间
    //   this.TimeDta.tomorrow = moment()
    //     .add(1, 'days')
    //     .format('YYYY-MM-DD')
    // },
    // //转换周几
    // getWeek() {
    //   // 参数时间戳
    //   let week = moment(new Date()).day()
    //   switch (week) {
    //     case 1:
    //       return '星期一'
    //     case 2:
    //       return '星期二'
    //     case 3:
    //       return '星期三'
    //     case 4:
    //       return '星期四'
    //     case 5:
    //       return '星期五'
    //     case 6:
    //       return '星期六'
    //     case 0:
    //       return '星期日'
    //   }
    // },
    init() {
      //库存动态储位统计查询-年
      this.getLibData()
      //维修及生产费用完成情况
      this.getRepairCostData()
      //燃动成本分析饼图
      this.getFuelData1()
      //燃动成本报表查询
      this.getFuelData2()
      //隐患查询
      this.getHiddenData1()
      //联合点检查询
      this.getCheckData1()
      //线上点检查询
      this.getCheckOnlineData()
      //故障查询
      this.getDeviceData1()
      //主要水系统查询
      this.getDeviceData2()
      //行车查询
      this.getDeviceData3()
      //主要事项跟踪查询
      this.getImportantData1()
      //项目汇报查询
      this.getProjectData1()
    },
    //库存动态储位统计查询-年
    getLibData() {
      const params = {
        setDate: this.cDate.substring(0, 4)
      }
      post(firstMeetingBackup3, params).then(res => {
        if (res.success) {
          let planList = [] //计划
          let factList = [] //实际
          let xData = []
          res.data.plan.forEach(item => {
            planList.push(
              item.endAmt === null || item.endAmt.length === 0
                ? ''
                : (parseFloat(item.endAmt) / 10000).toFixed(2)
            )
            xData.push(item.setDate.substring(5, item.setDate.length) + '月')
          })
          res.data.fact.forEach(item => {
            factList.push(
              item.endAmt === null || item.endAmt.length === 0
                ? ''
                : (parseFloat(item.endAmt) / 10000).toFixed(2)
            )
          })
          this.option1.xData = xData
          this.option1.series[0].data = planList
          this.option1.series[1].data = factList
        }
      })
    },
    //维修及生产费用完成情况
    getRepairCostData() {
      const params = {
        setDate: this.cDate
      }
      post(firstMeetingBackup2, params).then(res => {
        if (res.success) {
          let xData = []
          let planList = [] //计划
          let factList = [] //实际
          res.data.plan.forEach(item => {
            planList.push(
              item.cost === null || item.cost.length === 0
                ? ''
                : (parseFloat(item.cost) / 10000).toFixed(2)
            )
            xData.push(item.workShop)
          })
          res.data.fact.forEach(item => {
            factList.push(
              item.cost === null || item.cost.length === 0
                ? ''
                : (parseFloat(item.cost) / 10000).toFixed(2)
            )
          })
          this.option2.xData = xData
          this.option2.series[0].data = planList
          this.option2.series[1].data = factList
        }
      })
    },
    //燃动成本分析饼图
    getFuelData1() {
      const params = {
        start: this.newDate + ' 00:00:00',
        end: this.newDate + ' 23:59:59'
      }
      post(firstMeetingFuel1, params).then(res => {
        if (
          res &&
          res.watsEnergyReportTableForMonth &&
          res.watsEnergyReportTableForMonth.returnYieldMap &&
          res.watsEnergyReportTableForMonth.returnYieldMap.synYield
        ) {
          // let list = res.watsEnergyReportTableForMonth.syn
          //产量
          this.fuelData.fuelProduction =
            res.watsEnergyReportTableForMonth.returnYieldMap.synYield
          // //燃动成本增减
          // this.option3.titleNum = '+' + list[list.length - 1].CostDiffer2
        }
      })
    },
    //燃动成本报表查询
    getFuelData2() {
      const params = {
        date: this.newDate
      }
      post(firstMeetingFuel2, params).then(res => {
        if (res.data && res.data.length > 0) {
          let list = res.data
          this.fuelList = res.data
          //燃动成本
          this.fuelData.fuelCost = res.data[0].compActual
          //燃动成本增减
          this.option3.titleNum = '+' + res.data[0].compCost
          //找出最大的5个增本
          const newList = list
            .slice(1, list.length - 1)
            .sort((a, b) => {
              // console.log(a, b)
              return b.compCost - a.compCost
            })
            .slice(0, 5)
          console.log(newList)
          let seriesData = []
          newList.forEach((item, index) => {
            seriesData.push({
              name: item.name,
              value: item.compCost
            })
          })
          this.option3.series[0].data = seriesData
        }
      })
    },
    //隐患查询
    getHiddenData1() {
      const params = {
        setDate: this.cDate
      }
      this.hiddenLoading = true
      post(firstMeetingHidden1, params)
        .then(res => {
          if (res.success) {
            this.hiddenList = res.data
          }
        })
        .finally(_ => {
          this.hiddenLoading = false
        })
    },
    //联合点检查询
    getCheckData1() {
      const params = {
        time: this.cDate
      }
      this.checkLoading = true
      post(firstMeetingCheck1, params)
        .then(res => {
          if (res.success) {
            this.checkList = res.data
          }
        })
        .finally(_ => {
          this.checkLoading = false
        })
    },
    //线上点检查询
    getCheckOnlineData() {
      const params = {
        setDate: this.cDate
      }
      // this.checkOnlineLoading = true
      post(firstMeetingCheck4, params)
        .then(res => {
          if (res.success) {
            this.checkOnlineList = res.data
          }
        })
        .finally(_ => {
          // this.checkOnlineLoading = false
        })
    },
    //故障查询
    getDeviceData1() {
      const params = {
        // time: this.cDate
        time: ''
      }
      this.deviceLoading = true
      post(firstMeetingDevice1, params)
        .then(res => {
          if (res.success) {
            this.troubleList = res.data
          }
        })
        .finally(_ => {
          this.deviceLoading = false
        })
    },
    //主要水系统查询
    getDeviceData2() {
      const params = {
        month: this.newDate.substring(0, 7)
      }
      post(firstMeetingDevice4, params).then(res => {
        if (res.code === 0) {
          let list = res.result
          if (list && list.length > 0) {
            const data = list.find(
              item => item.day + '' === this.cDate.substring(8)
            )
            if (data) {
              this.waterList.forEach(item => {
                switch (item.id) {
                  case 'LZ0':
                    item.crystallizeHydrating = data.LZ0_CRYS_ACT + '吨'
                    item.examineOne = data.LZ0_CRYS_RP + '元'
                    item.equipmentWaterHydrating = data.LZ0_DEVICE_ACT + '吨'
                    item.examineTwo = data.LZ0_DEVICE_RP + '元'
                    break
                  case 'LZ1':
                    item.crystallizeHydrating = data.LZ1_CRYS_ACT + '吨'
                    item.examineOne = data.LZ1_CRYS_RP + '元'
                    item.equipmentWaterHydrating = data.LZ1_DEVICE_ACT + '吨'
                    item.examineTwo = data.LZ1_DEVICE_RP + '元'
                    break
                  case 'LZ2':
                    item.crystallizeHydrating = data.LZ2_CRYS_ACT + '吨'
                    item.examineOne = data.LZ2_CRYS_RP + '元'
                    item.equipmentWaterHydrating = data.LZ2_DEVICE_ACT + '吨'
                    item.examineTwo = data.LZ2_DEVICE_RP + '元'
                    break
                  case 'LZ3':
                    item.crystallizeHydrating = data.LZ3_CRYS_ACT + '吨'
                    item.examineOne = data.LZ3_CRYS_RP + '元'
                    item.equipmentWaterHydrating = data.LZ3_DEVICE_ACT + '吨'
                    item.examineTwo = data.LZ3_DEVICE_RP + '元'
                    break
                }
              })
            }
          }
        }
      })
    },
    //行车查询
    getDeviceData3() {
      const params = {
        time: this.cDate
      }
      post(firstMeetingDevice5, params).then(res => {
        if (res.success) {
          this.hangList = res.data
        }
      })
    },
    //主要事项跟踪查询
    getImportantData1() {
      const params = {
        time: this.cDate
      }
      this.importantLoading = true
      post(firstMeetingImportant1, params)
        .then(res => {
          if (res.success) {
            this.importantList = res.data
          }
        })
        .finally(_ => {
          this.importantLoading = false
        })
    },
    //项目汇报查询
    getProjectData1() {
      const params = {
        time: this.cDate
      }
      this.projectLoading = true
      post(firstMeetingProject1, params)
        .then(res => {
          if (res.success) {
            this.projectList = res.data
          }
        })
        .finally(_ => {
          this.projectLoading = false
        })
    }
  }
}
</script>

<style scoped lang="less">
.dialog-body {
  overflow: scroll;
  .dialog-cell {
    margin-bottom: 12px;
    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }
    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }
    .dialog-cell-input {
    }
  }
}

.tabs-class {
  display: flex;
  flex-direction: row;
  .tab-pane {
    color: #ffffffbf;
    margin-right: 21px;
  }
  .tab-pane-active {
    color: #ffffff;
  }
  .tab-pane-title-class {
    display: flex;
    flex-direction: column;
    position: relative;
    .tab-pane-img {
      .tab-pane-img2 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
      .tab-pane-img1 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        margin-bottom: 7px;
      }
    }
  }
}
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }

  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  overflow: auto;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
