<template>
  <div>
    <div class="page-content">
      <el-row
        :gutter="30"
        class="row-bg"
        justify="start"
        type="flex"
      >
        <el-col
          :span="24"
        >
          <div class="page-operate">
            <div class="search-wrapper">
              <el-form
                ref="searchForm"
                :label-width="'80px'"
                :model="searchForm"
                size="mini"
                inline
                onsubmit="return false;"
                @keyup.enter.native="handleSearch(true)"
              >
                <el-form-item
                  label=""
                  prop="id"
                >
                  <el-input
                    v-model="searchForm.reportTitle"
                    prefix-icon="el-icon-search"
                    clearable
                    size="small"
                    placeholder="请输入报表名称"
                    style="width: 200px"
                    type="text"
                  />
                </el-form-item>
              </el-form>

            </div>
            <div>
              <el-button
                icon="el-icon-search"
                type="primary"
                size="small"
                @click="handleSearch"
              >搜索
              </el-button>
              <el-button
                icon="el-icon-search"
                type="success"
                size="small"
                @click="handleRedirect"
              >创建
              </el-button>
            </div>
          </div>
          <div class="page-card shadow-light">
            <el-table
              v-loading="loading"
              :data="showData"
              :size="size"
              border
              style="width: 100%"
            >
              <el-table-column
                label="序号"
                type="index"
                width="60"
              />
              <el-table-column
                label="名称"
                prop="reportTitle"
                min-width="150"
              />
              <el-table-column
                label="类型"
                prop="reportType"
                min-width="100"
              >
                <template
                  v-slot="{row}"
                >
                  报表
                </template>
              </el-table-column>
              <el-table-column
                label="修改时间"
                prop="modified"
                min-width="150"
              >
                <template slot-scope="scope">
                  {{ scope.row.modified | formatDate }}
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                label="操作"
                width="240"
              >
                <template
                  v-slot="{row}"
                >
                  <span v-command="'/kpi/indicators/edit'">
                    <el-button
                      size="small"
                      type="text"
                      @click="updown(row)"
                    >打开
                    </el-button>
                    <el-divider
                      direction="vertical"
                    />
                    <el-button
                      size="small"
                      type="text"
                      @click="copyStr(row.reportUrl)"
                    >复制链接
                    </el-button>
                    <el-divider
                      direction="vertical"
                    />
                    <el-button
                      size="small"
                      type="text"
                      @click="copyWidgetStr(row.reportUrl)"
                    >小组件链接
                    </el-button>
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <el-row
              align="middle"
              class="table-pagination"
              justify="end"
              type="flex"
            >
              <el-pagination
                :current-page="page.page"
                :page-size="page.size"
                :page-sizes="[10, 20, 30, 40]"
                :total="page.total"
                layout="total, sizes, prev, pager, next, jumper"
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </el-row>
          </div>
        </el-col>
      </el-row>
    </div>
    <Add
      ref="modalForm"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import { copy, onCopy, post } from '@/lib/Util'
import { formatDate } from '@/lib/FormatTime'
import Add from './component/add'
import listMixins from '@/mixins/ListMixins'
import { findListAll } from '@/api/dept'
import * as _ from 'lodash'

export default {
  name: 'User',
  components: {
    Add
  },
  filters: {
    formatDate
  },
  mixins: [listMixins],
  data: () => {
    return {
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: findListAll //分页接口地址
        // add: saveItem, //添加接口地址
        // delete: deleteItem //删除接口地址
      },
      searchForm: { reportTitle: '' },
      rightMenuVisible: false,
      rightMenuLeft: 0,
      rightMenuTop: 0,
      rightMenuData: null,
      totalData: []
    }
  },
  computed: {
    // 根据接口获取数据长度(注意：这里不能为空)
    showData: function() {
      return this.totalData.slice(
        (this.page.page - 1) * this.page.size,
        this.page.page * this.page.size
      )
    }
  },
  watch: {},
  created() {},
  methods: {
    copyStr(str) {
      return onCopy(str) // http://172.25.63.144:9730/
    },
    copyWidgetStr(str) {
      // 截取path
      const index = str.indexOf('/', 8)
      return onCopy(str.slice(index))
    },
    getValue: function(list = [], value) {
      return list.find(item => item.value == value)
    },
    async handleSearch(reset = false) {
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.page = 1
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          id: parseInt(this.$route.params.id),
          page: this.page.page,
          size: this.page.size
        })
      )
      // console.log(data)
      this.totalData = data ? _.orderBy(data, ['modified'], ['desc']) : []
      // this.page.page = data.results.pageable.pageNumber + 1
      // this.page.size = data.results.pageable.pageSize
      this.page.total = data ? data.length : 0
      this.afterHandleSearch(this.tableData)
      this.loading = false
    },
    handleDelete: function(data) {
      if (!this.url.delete) {
        this.$message('请设置url.delete属性!')
        return
      }
      this.$confirm('是否确认删除此数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除操作
        post(this.url.delete, data).then(res => {
          this.handleSearch()
        })
      })
    },
    async updown(row) {
      window.open(row.reportUrl)
    },
    // 分页
    // 每页显示的条数
    handleSizeChange(val) {
      this.page.size = val
      this.page.page = 1
    },
    // 显示第几页
    handleCurrentChange(val) {
      this.page.page = val
    },
    // 跳转创建
    handleRedirect() {
      window.open(
        'http://************:9999/create?token=074FFCE2BD7F4012C96E8F1D4D090CB032D4B8B9E2CE55895313A566A870C45D'
      )
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.page-title {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  margin-bottom: 15px;
}

.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}

.tree-wrapper {
  height: 75vh;
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.first-node {
  font-size: 18px;
}

/deep/ .el-tree-node {
  margin: 5px 0;
}

/deep/ .el-tree > .el-tree-node {
  margin: 15px 0 12px;
}

.contextmenu {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  background: #fff;
  font-size: 14px;
  color: #666;
  border: 1px solid #e9e9e9;
  min-width: 250px;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);

  li {
    padding: 7px 16px;
    cursor: pointer;

    &:hover {
      background: #f4f4f5;
    }
  }

  li:last-child {
    border-top: 1px solid #e9e9e9;
  }
}
</style>
