<!--燃动成本-->
<template>
  <div class="content">
    <div
      style="min-height: 240px"
      class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <screen-border-multi :title="'燃动成本分析 '+newDate">
            <template v-slot:headerRight>
              <span
                class="screen-btn"
                @click="clickNumberOfPoweringDialog">
                <el-icon class="el-icon-s-data" />
                通电次数
              </span>
              <span
                class="screen-btn"
                @click="clickTeamOxygenArgonDialog">
                <el-icon class="el-icon-s-data" />
                班组氧氩氮气评比
              </span>
              <span
                class="screen-btn"
                @click="clickTeamDialog">
                <el-icon class="el-icon-s-data" />
                班组煤气回收评比
              </span>
              <span
                class="screen-btn"
                @click="clickLianZhuDialog">
                <el-icon class="el-icon-s-data" />
                停机监测
              </span>
              <span
                class="screen-btn"
                @click="clickRecycleDialog">
                <el-icon class="el-icon-s-data" />
                回收趋势
              </span>
              <span
                class="screen-btn"
                @click="clickDialog">
                <el-icon class="el-icon-s-data" />
                盛钢时间和过热度
              </span>
              <span
                class="screen-btn"
                @click="clickRemark">
                <el-icon
                  v-if="isEdit"
                  class="el-icon-finished" />
                <el-icon
                  v-else
                  class="el-icon-edit" />
                {{ isEdit ? '提交' : '编辑' }}
              </span>
            </template>
            <template v-slot:default>
              <div class="chart-wrapper">
                <div class="chart">
                  <FirstSteelPieChart2
                    v-loading="fuelLoading"
                    ref="FirstSteelPieChart2"
                    :is-edit="isEdit"
                    :f-data="option3.fuelData"
                    :f-legend-data="option3.fuelLegendData"
                    :chart-data="option3.series"
                    :color="['#3391FF','#55C6D4','#66CC6A','#FFDA35','#FF9800','#F45549','#A146B0','#3F51B5']"
                    :x-data="option3.xData"
                    :show-legend="option3.showLegend"
                  />
                </div>
              </div>
            </template>
          </screen-border-multi>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold" />
    <div
      style="flex: 2"
      class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <screen-border-multi :title="'各车间燃动成本报表 ' + newDate">
            <template v-slot:default>
              <div
                ref="table1"
                class="chart-wrapper">
                <el-table
                  v-loading="fuelLoading"
                  :data="fuelList"
                  height="450"
                  border>
                  <el-table-column
                    align="center"
                    show-overflow-tooltip
                    prop="name"
                    label="项目"
                    width="110" />
                  <el-table-column
                    align="center"
                    show-overflow-tooltip
                    prop="unit"
                    label="计量单位" />
                  <el-table-column
                    align="center"
                    show-overflow-tooltip
                    prop="price"
                    label="单价" />
                  <el-table-column
                    align="center"
                    show-overflow-tooltip
                    prop=""
                    label="原料">
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="rawPlan"
                      label="计划" />
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="rawActual"
                      label="实际" />
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="rawCost"
                      label="降本" />
                  </el-table-column>
                  <el-table-column
                    align="center"
                    show-overflow-tooltip
                    prop=""
                    label="炼钢">
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="stePlan"
                      label="计划" />
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="steActual"
                      label="实际">
                      <template v-slot="scope">
                        <span
                          v-if="scope.row.name==='蒸汽回收'"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline;"
                          @click="clickSteamDialog">{{ scope.row.steActual }}</span>
                        <span
                          v-else-if="scope.row.name==='煤气回收'"
                          style="cursor: pointer;color: #1FC6FF;text-decoration: underline;"
                          @click="clickGasDialog">{{ scope.row.steActual }}</span>
                        <span v-else>{{ scope.row.steActual }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="steCost"
                      label="降本" />
                  </el-table-column>
                  <el-table-column
                    align="center"
                    show-overflow-tooltip
                    prop=""
                    label="精炼">
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="refPlan"
                      label="计划" />
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="refActual"
                      label="实际" />
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="refCost"
                      label="降本" />
                  </el-table-column>
                  <el-table-column
                    align="center"
                    show-overflow-tooltip
                    prop=""
                    label="连铸">
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="conPlan"
                      label="计划" />
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="conActual"
                      label="实际" />
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="conCost"
                      label="降本" />
                  </el-table-column>
                  <el-table-column
                    align="center"
                    show-overflow-tooltip
                    prop=""
                    label="综合(全厂)">
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="compPlan"
                      label="计划" />
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="compActual"
                      label="实际" />
                    <el-table-column
                      align="center"
                      show-overflow-tooltip
                      prop="compCost"
                      label="降本" />
                  </el-table-column>
                  <!--                  <el-table-column
                    align="center"
                    show-overflow-tooltip
                    prop="monthPrice"
                    label="2022年(7-9)月"/>-->
                </el-table>
              </div>
            </template>

          </screen-border-multi>
        </el-col>
      </el-row>
    </div>
    <!--通电次数-->
    <el-dialog
      :visible.sync="teamVisible3"
      :width="'95%'"
      :close-on-click-modal="false"
      class="screen-dialog mt8vh"
      title="通电次数">
      <template v-slot:title>
        <div
          style="display:flex;align-items:center;justify-content:space-between"
          class="custom-dialog-title">
          通电次数
          <el-date-picker
            v-model="fireCostTime"
            :picker-options="pickerOptions"
            style="color:#fff"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            size="small"
            @change="getFireCostData3"
          />
        </div>

        <div
          style="display: flex; justify-content: center; align-items: center; margin-top: 10px; color: #fff; font-size: 14px;">
          标准值分别为5、6、7、9
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 260px)'}"
        style="display:flex;flex-direction: column;"
        class="dialog-body">
        <div
          id="fireCost41"
          class="fireCost-chart">1
        </div>
        <div
          id="fireCost42"
          class="fireCost-chart">2
        </div>
        <div
          id="fireCost43"
          class="fireCost-chart">3
        </div>
        <div
          id="fireCost44"
          class="fireCost-chart">4
        </div>
      </div>
    </el-dialog>
    <!--班组煤气回收评比-->
    <el-dialog
      :visible.sync="teamVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog mt8vh"
      title="班组煤气回收评比">
      <template v-slot:title>
        <div
          style="display:flex;align-items:center;justify-content:space-between"
          class="custom-dialog-title">
          班组煤气回收评比
          <el-date-picker
            v-model="fireCostTime"
            :picker-options="pickerOptions"
            style="color:#fff"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            size="small"
            @change="getFireCostData"
          />
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 260px)'}"
        style="display:flex;flex-direction: column;"
        class="dialog-body">
        <div
          id="fireCost1"
          class="fireCost-chart">1
        </div>
        <div
          id="fireCost2"
          class="fireCost-chart">2
        </div>

        <div
          id="fireCost3"
          class="fireCost-chart">3
        </div>

      </div>
    </el-dialog>

    <!--班组氧氩氮气评比-->
    <el-dialog
      :visible.sync="teamVisible2"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog mt8vh"
      title="班组氧氩氮气评比">
      <template v-slot:title>
        <div
          style="display:flex;align-items:center;justify-content:space-between"
          class="custom-dialog-title">
          班组氧氩氮气评比
          <el-date-picker
            v-model="fireCostTime"
            :picker-options="pickerOptions"
            style="color:#fff"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            size="small"
            @change="getFireCostData2"
          />
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 260px)'}"
        style="display:flex;flex-direction: column;"
        class="dialog-body">
        <div
          id="fireCost21"
          class="fireCost-chart">1
        </div>
        <div
          id="fireCost22"
          class="fireCost-chart">2
        </div>
        <div
          id="fireCost23"
          class="fireCost-chart">3
        </div>
      </div>
    </el-dialog>
    <!--盛钢时间和过热度-->
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog mt8vh"
      title="盛钢时间和过热度">
      <template v-slot:title>
        <div class="custom-dialog-title">
          盛钢时间和过热度 {{ newDate }}
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 260px)'}"
        class="dialog-body">
        <div class="scrollable-table">
          <el-table
            v-loading="timeLoading"
            :data="chengGangList"
            class="cheng-gang-table"
            border>
            <el-table-column label="盛钢时间统计">
              <el-table-column
                show-overflow-tooltip
                label="名称">
                <template v-slot="scope">
                  <div>{{ scope.row.name }}</div>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="平均盛钢时间">
                <template v-slot="scope">
                  <div>{{ scope.row.avg }}</div>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="标准盛钢时间">
                <template v-slot="scope">
                  <div>{{ scope.row.standard }}</div>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="校准盛钢时间">
                <template v-slot="scope">
                  <div>{{ scope.row.adjustVal }}</div>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="生产炉数">
                <template v-slot="scope">
                  <div>{{ scope.row.num }}</div>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="超标炉数">
                <template v-slot="scope">
                  <div>{{ scope.row.exceedNum }}</div>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
        <div class="scrollable-table">
          <el-table
            v-loading="timeLoading"
            :data="superHeatList"
            class="cheng-gang-table"
            style="margin-top: 20px"
            border>
            <el-table-column label="中包过热度统计">
              <el-table-column
                show-overflow-tooltip
                label="名称">
                <template v-slot="scope">
                  <div>{{ scope.row.name }}</div>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="平均过热度">
                <template v-slot="scope">
                  <div>{{ scope.row.avg }}</div>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="平均超标温度">
                <template v-slot="scope">
                  <div>{{ scope.row.avgExceed }}</div>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="生产炉数">
                <template v-slot="scope">
                  <div>{{ scope.row.num }}</div>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="超标炉数">
                <template v-slot="scope">
                  <div>{{ scope.row.exceedNum }}</div>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
        <div class="scrollable-table">
          <el-table
            v-loading="timeLoading"
            :data="slagSplashList"
            class="cheng-gang-table"
            style="margin-top: 20px"
            border>
            <el-table-column label="溅渣时长统计">
              <el-table-column
                show-overflow-tooltip
                label="名称">
                <template v-slot="scope">
                  <div>{{ scope.row.name }}</div>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="平均溅渣时长">
                <template v-slot="scope">
                  <div>{{ scope.row.avg }}</div>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="生产炉数">
                <template v-slot="scope">
                  <div>{{ scope.row.num }}</div>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="超标炉数">
                <template v-slot="scope">
                  <div>{{ scope.row.exceedNum }}</div>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
    <!--蒸汽回收或煤气回收-->
    <el-dialog
      :visible.sync="echartVisiable"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          {{ echartTitle }}
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <div class="chart-wrapper">
          <div class="chart">
            <first-steel-chart
              v-loading="echartLoading"
              :chart-data="option2.series"
              :color="['#3391FF','#66CC6A']"
              :bar-width="20"
              :unit="option2.unit"
              :x-data="option2.xData"
            />
          </div>
        </div>
      </div>
    </el-dialog>
    <!--蒸汽回收和煤气回收-->
    <el-dialog
      :visible.sync="dialogVisibleRecycle"
      :close-on-click-modal="false"
      width="90%"
      class="screen-dialog mt8vh"
      @closed="gasDayMsg=[{}]">
      <template v-slot:title>
        <div class="custom-dialog-title">
          蒸汽回收和煤气回收
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 300px)'}"
        class="dialog-body">
        <div
          class="chart-wrapper"
          style="height:calc(100% - 100px)">
          <div class="chart">
            <first-steel-chart
              v-loading="echartLoading"
              :chart-data="option4.series"
              :color="['#3391FF','#66CC6A']"
              :bar-width="20"
              :unit="option4.unit"
              :x-data="option4.xData"
            />
          </div>
          <div class="chart">
            <first-steel-chart
              v-loading="echartLoading"
              ref="chengGangChart"
              :chart-data="option5.series"
              :color="['#3391FF','#66CC6A']"
              :bar-width="20"
              :unit="option5.unit"
              :x-data="option5.xData"
              :tooltip-formatter="true"
            />
          </div>
        </div>
        <el-table
          :data="gasDayMsg"
          :cell-style="{height:'32px'}"
          :max-height="80"
          class="cheng-gang-table"
          style="margin-top: 20px"
          border>
          <el-table-column
            show-overflow-tooltip
            prop="time"
            label="日期" />
          <el-table-column
            show-overflow-tooltip
            prop="count"
            label="总炉数" />
          <el-table-column
            show-overflow-tooltip
            prop="exCount"
            label="低于20000炉数" />
        </el-table>
      </div>
    </el-dialog>
    <!--停机监测-->
    <el-dialog
      :visible.sync="dialogVisible2"
      :width="'98%'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div
          class="custom-dialog-title">
          停机监测
        </div>
      </template>
      <div
        :style="{height: 'calc(100vh - 355px)'}"
        class="dialog-body">
        <el-table
          v-loading="lianLoading"
          :data="lianListSort"
          height="100%"
          border>
          <el-table-column
            width="130"
            show-overflow-tooltip
            label="设备">
            <template v-slot="scope">
              <div>{{ scope.row.areaName }}</div>
            </template>
          </el-table-column>
          <el-table-column
            width="200"
            show-overflow-tooltip
            label="停机开始时间">
            <template v-slot="scope">
              <div>{{ scope.row.startTime }}</div>
            </template>
          </el-table-column>
          <el-table-column
            width="200"
            show-overflow-tooltip
            label="停机结束时间">
            <template v-slot="scope">
              <div>{{ scope.row.endTime }}</div>
            </template>
          </el-table-column>
          <el-table-column
            width="100"
            show-overflow-tooltip
            label="停机时间">
            <template v-slot="scope">
              <div>{{ scope.row.stopTime }}</div>
            </template>
          </el-table-column>
          <el-table-column
            width="120"
            show-overflow-tooltip
            label="停介质时长">
            <template v-slot="scope">
              <div>{{ scope.row.stopTimeActual }}</div>
            </template>
          </el-table-column>
          <el-table-column
            width="100"
            show-overflow-tooltip
            label="占比">
            <template v-slot="scope">
              <div :style="{color:scope.row.stopTimeActual ==0?'#f45549':''}">{{ scope.row.stopTimeActualScale }}</div>
            </template>
          </el-table-column>
          <el-table-column
            width="80"
            show-overflow-tooltip
            label="班组">
            <template v-slot="scope">
              <div>{{ scope.row.class }}</div>
            </template>
          </el-table-column>
          <el-table-column
            width="250"
            show-overflow-tooltip
            label="点位名称">
            <template v-slot="scope">
              <div>{{ scope.row.name }}</div>
            </template>
          </el-table-column>
          <el-table-column
            min-width="300"
            label="介质实绩">
            <template v-slot="scope">
              <div
                :id="'echartsState_'+scope.row.id"
                class="echartsState" />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import FirstSteelChart from '@/pages/screen/firstSteelMeeting/component/first-steel-chart'
import FirstSteelPie from '@/pages/screen/firstSteelMeeting/component/first-steel-pie.vue'
import FirstSteelPieChart2 from '@/pages/screen/firstSteelMeeting/component/first-steel-pie-chart-2.vue'
import { post } from '@/lib/Util'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import {
  firstMeetingFuel1,
  firstMeetingFuel2,
  firstMeetingFuel3,
  firstMeetingFuel4,
  firstMeetingFuel5,
  firstMeetingFuel6,
  firstMeetingFuel7,
  firstMeetingFuel8,
  firstMeetingteam,
  firstMeetingteamOxygenArgon,
  firstPowerCounts
} from '@/api/firstMeeting'
import moment from 'moment'

export default {
  name: 'fuelPage',
  components: {
    ScreenBorder,
    ScreenBorderMulti,
    FirstSteelChart,
    FirstSteelPie,
    FirstSteelPieChart2
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      teamVisible: false,
      teamVisible2: false,
      teamVisible3: false,
      fireCostTime: [
        moment(new Date())
          .add(-1, 'days')
          .format('YYYY-MM-DD 00:00:00'),
        moment(new Date())
          .add(-1, 'days')
          .format('YYYY-MM-DD 23:59:59')
      ],
      fireCostCharts: {},
      fireCostCharts2: {},
      fireCostCharts3: {},
      fireCostCharts4: {},
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date()).format('YYYY-MM-DD 00:00:00')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '昨日',
            onClick(picker) {
              const end = new Date(
                moment(new Date())
                  .add(-1, 'days')
                  .format('YYYY-MM-DD 23:59:59')
              )
              const start = new Date(
                moment(new Date())
                  .add(-1, 'days')
                  .format('YYYY-MM-DD 00:00:00')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本周',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .startOf('isoWeek')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .startOf('month')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '上个月',
            onClick(picker) {
              const end = new Date(
                moment(new Date())
                  .subtract(1, 'months')
                  .endOf('month')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .subtract(1, 'months')
                  .startOf('month')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      gasDayMsg: [{}],
      isEdit: false,
      option3: {
        // fuelData: {
        //   fuelCost: '83.803',
        //   fuelCostChange: '+13.21',
        //   fuelProduction: '17588.74'
        // },
        fuelData: [
          {
            name: '燃动成本',
            value: '0',
            color: '#ffffff',
            unit: '元/吨',
            img: require('@/assets/images/screen/fuel-img-1.png')
          },
          {
            name: '燃动成本增减',
            value: '+0',
            color: '#ff2855',
            unit: '万元',
            img: require('@/assets/images/screen/fuel-img-1.png')
          },
          {
            name: '产量',
            value: '0',
            color: '#ffffff',
            unit: '吨',
            img: require('@/assets/images/screen/fuel-img-2.png')
          }
        ],
        fuelLegendData: [
          {
            name: '电',
            value: '+0',
            color: '#3391FF',
            unit: '万元'
          },
          {
            name: '循环水',
            value: '+0',
            color: '#55C6D4',
            unit: '万元'
          },
          {
            name: '煤气回收',
            value: '+0',
            color: '#66CC6A',
            unit: '万元'
          },
          {
            name: '蒸汽回收',
            value: '+0',
            color: '#FFDA35',
            unit: '万元'
          },
          {
            name: '氧气',
            value: '+0',
            color: '#FF9800',
            unit: '万元'
          },
          {
            name: '氮气',
            value: '+0',
            color: '#F45549',
            unit: '万元'
          },
          {
            name: '氩气',
            value: '+0',
            color: '#A146B0',
            unit: '万元'
          },
          {
            name: '压缩空气',
            value: '+0',
            color: '#3F51B5',
            unit: '万元'
          }
        ],
        showToolbox: true,
        showLegend: false,
        xData: [],
        series: [
          {
            name: '燃动成本',
            type: 'pie',
            radius: ['40%', '80%'],
            data: [
              { value: 0, name: '电', color: '#3391FF' },
              { value: 0, name: '循环水', color: '#55C6D4' },
              { value: 0, name: '煤气回收', color: '#66CC6A' },
              { value: 0, name: '蒸汽回收', color: '#FFDA35' },
              { value: 0, name: '氧气', color: '#FF9800' },
              { value: 0, name: '氮气', color: '#F45549' },
              { value: 0, name: '氩气', color: '#A146B0' },
              { value: 0, name: '压缩空气', color: '#3F51B5' }
            ]
          }
        ]
      },
      dialogVisible2: false,
      dialogVisibleFuel: false,
      dialogVisibleRecycle: false,
      dialogVisible: false,
      lianLoading: false,
      fuelLoading: false,
      timeLoading: false,
      echartLoading: false,
      echartVisiable: false,
      echartTitle: '',
      fuelList: [],
      newDate: '',
      chengGangList: [
        {
          name: '普通钢',
          avg: '',
          standard: '',
          adjustVal: '',
          num: '',
          exceedNum: ''
        },
        {
          name: '真空钢',
          avg: '',
          standard: '',
          adjustVal: '',
          num: '',
          exceedNum: ''
        },
        {
          name: '低碳低磷低硫真空钢',
          avg: '',
          standard: '',
          adjustVal: '',
          num: '',
          exceedNum: ''
        }
      ],
      superHeatList: [
        {
          name: '中包过热度',
          avg: '',
          avgExceed: '',
          num: '',
          exceedNum: ''
        }
      ],
      slagSplashList: [
        {
          name: '溅渣时长',
          avg: '',
          num: '',
          exceedNum: ''
        }
      ],
      option2: {
        unit: '',
        xData: [],
        series: [
          {
            name: '蒸汽回收',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: []
          }
        ]
      },
      option4: {
        unit: '',
        xData: [],
        series: [
          {
            name: '蒸汽回收',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: []
          }
        ]
      },
      option5: {
        unit: '',
        xData: [],
        series: [
          {
            name: '煤气回收',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: []
          }
        ]
      },
      value4: 0,
      value5: 138,
      paramList: [
        { stopArea: 'CODE10111012', medium: '1481441064840597505' },
        { stopArea: 'CODE10111013', medium: '1481441064840597505' },
        { stopArea: 'CODE10111014', medium: '1481441064840597505' },
        { stopArea: 'CODE10111018', medium: '1481441064840597505' },
        { stopArea: 'CODE10111019', medium: '1481441064840597505' },
        { stopArea: 'CODE10111020', medium: '1481441064840597505' },
        { stopArea: 'CODE10111021', medium: '1481441064840597505' },
        { stopArea: 'CODE10111022', medium: '1481441064840597505' },
        { stopArea: 'CODE10111023', medium: '1481441064840597505' },
        { stopArea: 'CODE10111024', medium: '1481441064840597505' },
        { stopArea: 'CODE10111025', medium: '1481441064840597505' },
        { stopArea: 'CODE10111026', medium: '1481441064840597505' },
        { stopArea: 'CODE10111027', medium: '1481441064840597505' },
        { stopArea: 'CODE10111028', medium: '1481441064840597505' }
      ],
      lianList: [],
      fullscreen: false
    }
  },
  computed: {
    lianListSort() {
      if (this.lianList.length > 0) {
        const datas = [...this.lianList]
        let sortResult = datas.sort(
          (item1, item2) =>
            parseFloat(item1.stopTimeActualScale) -
            parseFloat(item2.stopTimeActualScale)
        )
        return sortResult
      } else {
        return this.lianList
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.$nextTick(item => {
        this.newDate = moment(
          new Date(new Date(this.cDate).getTime() - 24 * 60 * 60 * 1000)
        ).format('yyyy-MM-DD')
        this.init()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    // this.init()
  },
  beforeDestroy() {
    for (let key of this.fireCostCharts) {
      if (this.fireCostCharts[key]) {
        window.removeEventListener('resize', this.fireCostCharts[key].resize)
      }
    }
    for (let key of Object.keys(this.fireCostCharts2)) {
      if (this.fireCostCharts2[key]) {
        window.removeEventListener('resize', this.fireCostCharts2[key].resize)
      }
    }
    for (let key of Object.keys(this.fireCostCharts4)) {
      if (this.fireCostCharts4[key]) {
        window.removeEventListener('resize', this.fireCostCharts4[key].resize)
      }
    }

    if (this.fireCostCharts3[chartname]) {
      window.removeEventListener(
        'resize',
        this.fireCostCharts3[chartname].resize
      )
      this.fireCostCharts3[chartname].dispose()
      this.fireCostCharts3[chartname] = null
    }
  },
  methods: {
    // //全屏
    // handleFullScreen() {
    //   // console.log(this.fullscreen)
    //   let element = document.getElementById('screen') // 放大的元素，如果整个系统放大，直接赋值 document.documentElement
    //
    //   if (this.fullscreen) {
    //     if (document.exitFullscreen) {
    //       document.exitFullscreen()
    //     } else if (document.webkitCancelFullScreen) {
    //       document.webkitCancelFullScreen()
    //     } else if (document.mozCancelFullScreen) {
    //       document.mozCancelFullScreen()
    //     } else if (document.msExitFullscreen) {
    //       document.msExitFullscreen()
    //     }
    //   } else {
    //     if (element.requestFullscreen) {
    //       element.requestFullscreen()
    //     } else if (element.webkitRequestFullScreen) {
    //       element.webkitRequestFullScreen()
    //     } else if (element.mozRequestFullScreen) {
    //       element.mozRequestFullScreen()
    //     } else if (element.msRequestFullscreen) {
    //       // IE11
    //       element.msRequestFullscreen()
    //     }
    //   }
    //   this.fullscreen = !this.fullscreen
    // },
    //班组评比煤气回收
    clickTeamDialog() {
      this.teamVisible = true
      this.$nextTick(() => {
        this.getFireCostData()
      })
    },
    //班组评比氧氩气
    clickTeamOxygenArgonDialog() {
      this.teamVisible2 = true
      this.$nextTick(() => {
        this.getFireCostData2()
      })
    },
    //通电次数
    clickNumberOfPoweringDialog() {
      this.teamVisible3 = true
      this.$nextTick(() => {
        this.getFireCostData3()
      })
    },
    getFireCostData() {
      const [startTime, endTime] = this.fireCostTime
      const data = { startTime, endTime }
      post(firstMeetingteam, data).then(res => {
        if (res.code === 0 && res.evaluateTeamCoalGasRecovery) {
          const resultData = res.evaluateTeamCoalGasRecovery
          for (let key in resultData) {
            const coefficientArray = resultData[key].coefficientArray || []
            const gasconArray = resultData[key].gasconArray || []
            const moltenIronRatioArray =
              resultData[key].moltenIronRatioArray || []
            const rankData = resultData[key].rankList || []
            const hoodArray = resultData[key].hoodArray || []
            this.chartIn(
              key,
              gasconArray,
              coefficientArray,
              moltenIronRatioArray,
              rankData,
              hoodArray
            )
          }
        }
      })
    },
    getFireCostData2() {
      const [startTime, endTime] = this.fireCostTime
      const data = { startTime, endTime }
      post(firstMeetingteamOxygenArgon, data).then(res => {
        if (res.code === 0 && res.evaluateTeamCoalOxygenArgon) {
          const resultData = res.evaluateTeamCoalOxygenArgon
          for (let key in resultData) {
            const oxygen = resultData[key].oxygen || []
            const argon = resultData[key].argon || []
            const nitrogen = resultData[key].nitrogen || []
            this.chartIn2(key, oxygen, argon, nitrogen)
          }
        }
      })
    },
    getFireCostData3() {
      const [startTime, endTime] = this.fireCostTime
      const data = { startTime, endTime }
      post(firstPowerCounts, data).then(res => {
        if (res.code === 0 && res.data) {
          const resultData = res.data
          for (let key in resultData) {
            const ordinary = resultData[key].ordinary || []
            const vacuum = resultData[key].vacuum || []
            const lowCps = resultData[key].lowCps || []
            const special = resultData[key].special || []
            const standard = resultData[key].standard || []
            this.chartIn4(key, ordinary, vacuum, lowCps, special, standard)
          }
        }
      })

      // const [startTime, endTime] = this.fireCostTime
      // const data = { startTime, endTime }
      // post(firstPowerCounts, data).then(res => {
      //   if (res.code === 0 && res.data) {
      //     const resultData = res.data
      //     this.chartIn3(resultData)
      //   }
      // })
    },
    //连铸趋势
    async clickLianZhuDialog() {
      this.lianList = []
      for (const item of this.paramList) {
        await this.getLianZhuData(item)
      }
      this.dialogVisible2 = true
    },
    //回收趋势
    clickRecycleDialog() {
      this.getSteamData()
      this.getGasData()
      this.dialogVisibleRecycle = true
    },
    clickDialog() {
      //盛钢时间和过热度
      this.getTimeAndHotData()
      this.dialogVisible = true
    },
    clickSteamDialog() {
      //蒸汽回收
      this.echartTitle = '蒸汽回收'
      this.option2.xData = []
      this.option2.unit = 'm³/t'
      this.option2.series[0].name = '蒸汽回收'
      this.option2.series[0].data = []
      this.getSteamData()
      this.echartVisiable = true
    },
    clickGasDialog() {
      //煤气回收
      this.echartTitle = '煤气回收'
      this.option2.xData = []
      this.option2.series[0].name = '煤气回收'
      this.option2.unit = 'm³/t'
      this.option2.series[0].data = []
      this.getGasData()
      this.echartVisiable = true
    },
    clickRemark() {
      if (this.isEdit) {
        this.saveRemarkData()
      }
      this.isEdit = !this.isEdit
    },
    init() {
      //燃动成本分析饼图
      this.getFuelData1()
      //燃动成本报表查询
      this.getFuelData()
      //查询备注
      this.getRemarkData()
    },
    numAdd(num1, num2) {
      let baseNum, baseNum1, baseNum2
      try {
        baseNum1 = num1.toString().split('.')[1].length
      } catch (e) {
        baseNum1 = 0
      }
      try {
        baseNum2 = num2.toString().split('.')[1].length
      } catch (e) {
        baseNum2 = 0
      }
      baseNum = Math.pow(10, Math.max(baseNum1, baseNum2))
      return (num1 * baseNum + num2 * baseNum) / baseNum
    },
    //燃动成本分析饼图
    getFuelData1() {
      const params = {
        start: this.newDate + ' 00:00:00',
        end: this.newDate + ' 23:59:59'
      }
      post(firstMeetingFuel1, params).then(res => {
        // this.option3
        // let list = res.watsEnergyReportTableForMonth.syn
        // //燃动成本
        // this.option3.fuelData[0].value =
        //   res.watsEnergyReportTableForMonth.returnYieldMap.synYield
        // //燃动成本增减
        // this.option3.fuelData[1].value = list[list.length - 1].CostDiffer2
        //产量
        this.option3.fuelData[2].value =
          res.watsEnergyReportTableForMonth.returnYieldMap.synYield
        // //找出最大的5个增本
        // const newList = list
        //   .slice(0, list.length - 1)
        //   .sort((a, b) => {
        //     console.log(a, b)
        //     return b.CostDiffer2 - a.CostDiffer2
        //   })
        //   .slice(0, 5)
        // console.log(newList)
        // let seriesData = []
        // newList.forEach((item, index) => {
        //   this.option3.fuelLegendData[index].name = item.medium
        //   this.option3.fuelLegendData[index].value = item.CostDiffer2
        //   seriesData.push({
        //     name: item.medium,
        //     value: item.CostDiffer2
        //   })
        // })
        // this.option3.series[0].data = seriesData
      })
    },
    //燃动成本报表查询
    getFuelData() {
      const params = {
        date: this.newDate
      }
      this.fuelLoading = true
      post(firstMeetingFuel2, params)
        .then(res => {
          let list = res.data
          this.fuelList = res.data
          //燃动成本
          this.option3.fuelData[0].value = res.data[0].compActual
          //燃动成本增减
          this.option3.fuelData[1].value = list[list.length - 1].compCost
          this.option3.fuelLegendData.forEach(fuelItem => {
            const item = list.find(item => item.name === fuelItem.name)
            if (item) {
              fuelItem.value = item.compCost
              // fuelItem.value = item.compCost === null ? '' : item.compCost
              // fuelItem.unit = item.compCost === null ? '无数据' : '万元'
            }
          })
          this.option3.series[0].data.forEach(seriesItem => {
            const item = list.find(item => item.name === seriesItem.name)
            if (item) {
              seriesItem.value = item.compCost
            }
          })

          //电增本计算
          let electricity = 0
          list.forEach(item => {
            if (
              item.name === '其中:峰' ||
              item.name === '平' ||
              item.name === '谷'
            ) {
              electricity = this.numAdd(
                electricity,
                item.compCost ? parseFloat(item.compCost) : 0
              ).toFixed(2)
            }
            if (item.name === '蒸汽回收') {
              this.value4 = Math.abs(item.stePlan)
            }
          })
          this.option3.fuelLegendData[0].value = electricity
          this.option3.series[0].data[0].value = electricity
          // //找出最大的5个增本
          // const newList = list
          //   .slice(1, list.length - 1)
          //   .sort((a, b) => {
          //     // console.log(a, b)
          //     return b.compCost - a.compCost
          //   })
          //   .slice(0, 5)
          // console.log(newList)
          // let seriesData = []
          // newList.forEach((item, index) => {
          //   this.option3.fuelLegendData[index].name = item.name
          //   this.option3.fuelLegendData[index].value = item.compCost
          //   seriesData.push({
          //     name: item.name,
          //     value: item.compCost
          //   })
          // })
          // this.option3.series[0].data = seriesData
        })
        .finally(_ => {
          this.fuelLoading = false
        })
    },
    getRemarkData() {
      const params = {
        time: this.newDate
      }
      post(firstMeetingFuel3, params).then(res => {
        if (res.success) {
          if (res.data && res.data.length > 0) {
            this.$refs.FirstSteelPieChart2.remark = res.data[0].remarks
            this.$refs.FirstSteelPieChart2.id = res.data[0].id
          } else {
            this.$refs.FirstSteelPieChart2.remark = ''
            this.$refs.FirstSteelPieChart2.id = ''
          }
        }
      })
    },
    //盛钢时间和过热度
    getTimeAndHotData() {
      const params = {
        startDate: this.newDate,
        endDate: this.newDate
      }
      this.fuelLoading = true
      post(firstMeetingFuel5, params)
        .then(res => {
          if (res.code === 0) {
            this.chengGangList = res.data.chengGang
            this.superHeatList = res.data.superHeat
            this.slagSplashList = res.data.slagSplash
          }
        })
        .finally(_ => {
          this.fuelLoading = false
        })
    },
    //蒸汽回收
    getSteamData() {
      const params = {
        date: this.cDate
      }
      this.echartLoading = true
      post(firstMeetingFuel6, params)
        .then(res => {
          if (res.code === 0) {
            let option = {
              unit: 'm³/t',
              xData: res.eleConsumptionStatisticsCurve.xZhou,
              series: [
                {
                  name: '蒸汽回收',
                  type: 'bar',
                  yAxisIndex: 0,
                  barGap: 0,
                  data: res.eleConsumptionStatisticsCurve.yZhou,
                  markLine: {
                    show: true,
                    silent: true,
                    lineStyle: {
                      color: '#FF9800'
                    },
                    data: [
                      {
                        yAxis: this.value4,
                        label: {
                          normal: {
                            show: true,
                            position: 'start'
                          }
                        }
                      }
                    ]
                  }
                }
              ]
            }
            this.option2 = JSON.parse(JSON.stringify(option))
            this.option4 = JSON.parse(JSON.stringify(option))
          }
        })
        .finally(_ => {
          this.echartLoading = false
        })
    },
    //煤气回收
    getGasData() {
      const params = {
        date: this.cDate
      }
      this.echartLoading = true
      post(firstMeetingFuel7, params)
        .then(res => {
          if (res.code === 0) {
            let option = {
              unit: 'm³/t',
              xData: res.eleConsumptionStatisticsCurve.xZhou,
              series: [
                {
                  name: '煤气回收',
                  type: 'bar',
                  yAxisIndex: 0,
                  barGap: 0,
                  data: res.eleConsumptionStatisticsCurve.yZhou,
                  markLine: {
                    show: true,
                    silent: true,
                    lineStyle: {
                      color: '#FF9800'
                    },
                    data: [
                      {
                        yAxis: this.value5,
                        label: {
                          normal: {
                            show: true,
                            position: 'start'
                          }
                        }
                      }
                    ]
                  }
                }
              ]
            }
            this.option2 = JSON.parse(JSON.stringify(option))
            this.option5 = JSON.parse(JSON.stringify(option))
          }
          if (this.$refs.chengGangChart && this.$refs.chengGangChart.myChart) {
            const that = this
            const yData = res.eleConsumptionStatisticsCurve.yZhou
            const mychart = this.$refs.chengGangChart.myChart
            mychart.getZr().on('click', function(params) {
              let pointInPixel = [params.offsetX, params.offsetY]
              if (mychart.containPixel('grid', pointInPixel)) {
                //点击第几个柱子
                let pointInGrid = mychart.convertFromPixel(
                  { seriesIndex: 0 },
                  pointInPixel
                )
                let clickCol = pointInGrid[0]
                let data = yData[clickCol]
                that.gasDayMsg = [data]
                console.log('that.gasDayMsg', that.gasDayMsg)
                // console.log(data, Nowpage)//点击的当前数据和页数
              }
            })
          }
        })
        .finally(_ => {
          this.echartLoading = false
        })
    },
    saveRemarkData() {
      console.log('remark:', this.$refs.FirstSteelPieChart2.remark)
      let remark = this.$refs.FirstSteelPieChart2.remark
      let id = this.$refs.FirstSteelPieChart2.id
      const params = [
        {
          id: id,
          remarks: remark,
          createTime: this.newDate
        }
      ]
      post(firstMeetingFuel4, params).then(res => {
        if (res.success) {
          this.$message.success('提交成功！')
          this.isEdit = false
          this.getRemarkData()
        } else {
          this.$message.error('提交失败！')
        }
      })
    },
    //连铸趋势
    getLianZhuData(param) {
      this.lianLoading = true
      param.time = this.cDate
      post(firstMeetingFuel8, param)
        .then(res => {
          if (res.code === 0) {
            let list = []
            res.data.forEach((item, index) => {
              let infoData = {
                areaName: item.areaName,
                startTime: item.startTime,
                endTime: item.endTime,
                class: item.class,
                stopTime: item.stopTime,
                name: '',
                id: '',
                stopTimeActualScale: '',
                stopTimeActual: '',
                xData: [],
                yData: []
              }
              item.list.forEach((cItem, cIndex) => {
                // infoData.id = '' + index + '' + cIndex
                // infoData.id = uuid().replace(new RegExp('-', 'g'), '')
                infoData.name = cItem.name
                infoData.stopTimeActualScale = cItem.stopTimeActualScale
                infoData.stopTimeActual = cItem.stopTimeActual
                infoData.xData = cItem.xZhou
                infoData.yData = cItem.yZhou
              })
              list.push(infoData)
            })
            this.lianList.push(...list)
            this.lianList.forEach((item, index) => {
              item.id = '' + index
            })
            console.log(this.lianList)
            this.$nextTick(() => {
              // console.log('initEcharts')
              if (this.lianList.length > 0) {
                this.$nextTick(() => {
                  console.log('initEcharts')
                  this.initEcharts()
                })
              }
            })
          }
        })
        .finally(_ => {
          this.lianLoading = false
        })
    },
    //赋值echarts数据
    initEcharts() {
      for (let i = 0; i < this.lianList.length; i++) {
        let item = this.lianList[i]
        // if (item.xData.length === 0) {
        //   break
        // }
        // let id = document.getElementById('echartsState_' + item.id)
        // console.log('id:', id)
        // let id = document.getElementsByClassName('echartsState')[i].id
        // if (!document.querySelector('#echartsState_' + item.id)) {
        //   break
        // }
        this.$echarts
          .init(document.querySelector('#echartsState_' + item.id))
          .dispose() // 销毁实例
        // 找到容器
        var myChart = this.$echarts.init(
          document.querySelector('#echartsState_' + item.id)
        )

        // 开始渲染
        myChart.setOption({
          tooltip: {
            appendToBody: true,
            show: true,
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            borderColor: '#1fc6ff',
            backgroundColor: '#041a21',
            textStyle: {
              color: '#fff',
              fontSize: 14
            },
            padding: 5,
            formatter: '{b}<br>{c}'
          },
          grid: {
            top: '2%',
            left: '2%',
            right: '3%',
            bottom: '0%',
            containLabel: true
          },
          xAxis: {
            data: item.xData,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            }
            // axisLabel: {
            //   rotate: -38,
            //   interval: 0,
            //   textStyle: {
            //     fontSize: 10
            //   }
            // }
          },
          yAxis: {
            // max: 1,
            // min: 0,
            // interval: 1,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            }
          },
          visualMap: {
            show: false,
            type: 'piecewise',
            // splitNumber: 2,
            pieces: [
              { gte: 0, lt: 10, color: '#51DF81' },
              { gte: 10, color: '#D45454' }
            ],
            outOfRange: {
              color: '#999'
            }
          },
          dataZoom: [
            {
              type: 'inside',
              // disabled: true,
              filterMode: 'none',
              start: 0,
              end: 100,
              height: 20,
              bottom: 0
            },
            {
              show: false,
              start: 0,
              end: 0,
              height: 20,
              bottom: 0
            }
          ],
          series: [
            {
              name: item.name,
              type: 'line',
              step: 'end',
              data: item.yData,
              symbol: 'none'
            }
          ]
        })
      }
    },
    //班组echarts
    chartIn(id, recovery, factor, moltenIronRatio, rankData, hoodArray) {
      const chartname = `fireCost${id}`
      let myChart = null
      if (this.fireCostCharts[chartname] != null) {
        this.fireCostCharts[chartname].dispose()
        this.fireCostCharts[chartname] = null
      }
      if (!this.fireCostCharts[chartname]) {
        myChart = this.$echarts.init(document.getElementById(chartname))
        window.addEventListener('resize', myChart.resize)
        this.fireCostCharts[chartname] = myChart
      } else {
        myChart = this.fireCostCharts[chartname]
      }
      myChart.clear()

      let option = {
        title: {
          text: id + '#转炉',
          left: '20',
          top: '10',
          textStyle: {
            color: '#fff',
            fontSize: 18
          }
        },
        legend: {
          x: 'center',
          icon: 'roundRect',
          top: '8%',
          itemWidth: 20,
          itemHeight: 12,
          itemGap: 30,
          textStyle: {
            color: '#C9E1FDF2',
            fontSize: 12
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          padding: 10
        },
        xAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          nameTextStyle: {
            color: '#d2d2d2'
          },
          axisLabel: {
            color: '#fff',
            fontSize: 12,
            interval: 0,
            rotate: this.labelRotate || 0
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#EAEBF0'
            }
          },
          data: rankData.map(item => `${item.rank}、${item.class}`) // 使用排名和班组名作为 X 轴数据
        },
        grid: {
          top: '22%',
          left: '8%',
          right: '4%',
          bottom: '10%'
        },
        yAxis: {
          type: 'value',
          nameTextStyle: {
            color: '#d2d2d2'
          },
          axisLabel: {
            color: '#fff',
            fontSize: 12,
            align: 'right'
          },
          splitLine: {
            lineStyle: {
              color: '#2e4262'
            }
          }
        },
        series: [
          {
            name: '吨钢热量',
            type: 'bar',
            barWidth: '13%',
            itemStyle: {
              normal: {
                color: '#66cc6a'
              }
            },
            data: recovery,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            }
          },
          {
            name: '燃烧系数',
            type: 'bar',
            barWidth: '13%',
            // barGap: '0%',
            itemStyle: {
              normal: {
                color: '#f45549'
              }
            },
            data: factor,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            }
          },
          {
            name: '吨铁热量',
            type: 'bar',
            barWidth: '13%',
            itemStyle: {
              normal: {
                color: '#3391FF'
              }
            },
            data: moltenIronRatio,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            }
          },
          {
            name: '降罩时长占比',
            type: 'bar',
            barWidth: '13%',
            itemStyle: {
              normal: {
                color: '#FF9800'
              }
            },
            data: hoodArray,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            }
          }
        ]
      }
      myChart.setOption(option)
    },
    //班组氧氩氮气echarts
    chartIn2(id, oxygen, argon, nitrogen) {
      const chartname = `fireCost2${id}`
      let myChart = null
      if (this.fireCostCharts2[chartname] != null) {
        this.fireCostCharts2[chartname].dispose()
        this.fireCostCharts2[chartname] = null
      }
      if (!this.fireCostCharts2[chartname]) {
        myChart = this.$echarts.init(document.getElementById(chartname))
        window.addEventListener('resize', myChart.resize)
        this.fireCostCharts2[chartname] = myChart
      } else {
        myChart = this.fireCostCharts2[chartname]
      }
      myChart.clear()
      let option = {
        title: {
          text: id + '#转炉',
          left: '20',
          top: '10',
          textStyle: {
            color: '#fff',
            fontSize: 18
          }
        },
        legend: {
          x: 'center',
          icon: 'roundRect',
          top: '8%',
          itemWidth: 20,
          itemHeight: 12,
          itemGap: 30,
          textStyle: {
            color: '#C9E1FDF2',
            fontSize: 12
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          padding: 10
        },
        xAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          nameTextStyle: {
            color: '#d2d2d2'
          },
          axisLabel: {
            color: '#fff',
            fontSize: 12,
            interval: 0,
            rotate: this.labelRotate || 0
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#EAEBF0'
            }
          },
          data: ['甲班', '乙班', '丙班', '丁班']
        },
        grid: {
          top: '22%',
          left: '8%',
          right: '4%',
          bottom: '10%'
        },
        yAxis: [
          {
            type: 'value',
            nameTextStyle: {
              color: '#d2d2d2'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'right'
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          },
          {
            type: 'value',
            nameTextStyle: {
              color: '#d2d2d2'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'left' // 设置对齐方式为左对齐
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        series: [
          {
            name: '氧气',
            type: 'bar',
            barWidth: '13%',
            itemStyle: {
              normal: {
                color: '#FF9800'
              }
            },
            data: oxygen,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            }
          },
          {
            name: '氩气',
            type: 'bar',
            barWidth: '13%',
            itemStyle: {
              normal: {
                color: '#A146B0'
              }
            },
            data: argon,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            },
            yAxisIndex: 1 // 指定使用第二个纵坐标轴
          },
          {
            name: '氮气',
            type: 'bar',
            barWidth: '13%',
            itemStyle: {
              normal: {
                color: '#F45549'
              }
            },
            data: nitrogen,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            }
          }
        ]
      }
      myChart.setOption(option)
    },
    chartIn3(oxygen) {
      const chartname = `tdcs`
      let myChart = this.$echarts.init(document.getElementById(chartname))
      window.addEventListener('resize', myChart.resize)
      this.fireCostCharts3[chartname] = myChart
      myChart.clear()

      let option = {
        title: {
          text: '通电次数',
          left: '20',
          top: '10',
          textStyle: {
            color: '#fff',
            fontSize: 18
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          padding: 10
        },
        xAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          nameTextStyle: {
            color: '#d2d2d2'
          },
          axisLabel: {
            color: '#fff',
            fontSize: 12,
            interval: 0,
            rotate: this.labelRotate || 0
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#EAEBF0'
            }
          },
          data: ['普通钢', '真空钢', '低碳低硫真空钢', '特殊钢']
        },
        grid: {
          top: '22%',
          left: '8%',
          right: '4%',
          bottom: '10%'
        },
        yAxis: [
          {
            type: 'value',
            nameTextStyle: {
              color: '#d2d2d2'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'right'
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        series: [
          {
            name: '通电次数',
            type: 'bar',
            barWidth: '13%',
            itemStyle: {
              normal: {
                color: '#FF9800'
              }
            },
            data: oxygen,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            }
          }
        ]
      }
      myChart.setOption(option)
    },
    chartIn4(id, ordinary, vacuum, lowCps, special, standard) {
      const chartname = `fireCost4${id}`
      let myChart = null
      if (this.fireCostCharts4[chartname] != null) {
        this.fireCostCharts4[chartname].dispose()
        this.fireCostCharts4[chartname] = null
      }
      if (!this.fireCostCharts4[chartname]) {
        myChart = this.$echarts.init(document.getElementById(chartname))
        window.addEventListener('resize', myChart.resize)
        this.fireCostCharts4[chartname] = myChart
      } else {
        myChart = this.fireCostCharts4[chartname]
      }
      myChart.clear()
      let option = {
        title: {
          text: id + '#LF',
          left: '20',
          top: '10',
          textStyle: {
            color: '#fff',
            fontSize: 18
          }
        },
        legend: {
          x: 'center',
          icon: 'roundRect',
          top: '8%',
          itemWidth: 20,
          itemHeight: 12,
          itemGap: 30,
          textStyle: {
            color: '#C9E1FDF2',
            fontSize: 12
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          padding: 10
        },
        xAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          nameTextStyle: {
            color: '#d2d2d2'
          },
          axisLabel: {
            color: '#fff',
            fontSize: 12,
            interval: 0,
            rotate: this.labelRotate || 0
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#EAEBF0'
            }
          },
          data: ['甲班', '乙班', '丙班', '丁班']
        },
        grid: {
          top: '25%',
          left: '8%',
          right: '4%',
          bottom: '14%'
        },
        yAxis: [
          {
            type: 'value',
            nameTextStyle: {
              color: '#d2d2d2'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'right'
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          },
          {
            type: 'value',
            nameTextStyle: {
              color: '#d2d2d2'
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'left' // 设置对齐方式为左对齐
            },
            splitLine: {
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        series: [
          {
            name: '普通钢',
            type: 'bar',
            barWidth: '10%',
            itemStyle: {
              normal: {
                color: '#3391FF'
              }
            },
            data: ordinary,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            }
          },
          {
            name: '真空钢',
            type: 'bar',
            barWidth: '10%',
            itemStyle: {
              normal: {
                color: '#55C6D4'
              }
            },
            data: vacuum,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            }
          },
          {
            name: '低碳低磷低硫真空钢',
            type: 'bar',
            barWidth: '10%',
            itemStyle: {
              normal: {
                color: '#66CC6A'
              }
            },
            data: lowCps,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            }
          },
          {
            name: '特殊钢',
            type: 'bar',
            barWidth: '10%',
            itemStyle: {
              normal: {
                color: '#FFDA35'
              }
            },
            data: special,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            }
          },
          {
            name: '超标炉数',
            type: 'bar',
            barWidth: '10%',
            itemStyle: {
              normal: {
                color: '#F45549'
              }
            },
            data: standard,
            label: {
              show: true,
              position: 'top', // 设置标签显示在柱形的顶部
              color: '#fff' // 设置标签文本颜色
            },
            yAxisIndex: 1
          }
        ]
      }
      myChart.setOption(option)
    }
  }
}
</script>

<style scoped lang="less">
.echartsState {
  width: 540px;
  height: 80px;
}

/deep/ .el-table .el-table__cell {
  font-size: 19px;
}

.dialog-body {
  overflow: scroll;
  display: flex;
  flex-direction: column;

  .dialog-cell {
    margin-bottom: 12px;

    .dialog-cell-title {
      font-size: 16px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .dialog-cell-title::before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 8px;
      height: 100%;
      margin-right: 4px;
    }

    .dialog-cell-input {
    }
  }
}

.tabs-class {
  display: flex;
  flex-direction: row;

  .tab-pane {
    color: #ffffffbf;
    margin-right: 21px;
  }

  .tab-pane-active {
    color: #ffffff;
  }

  .tab-pane-title-class {
    display: flex;
    flex-direction: column;
    position: relative;

    .tab-pane-img {
      .tab-pane-img2 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }

      .tab-pane-img1 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        margin-bottom: 7px;
      }
    }
  }
}

.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }

  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  overflow: auto;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}

/deep/ .cheng-gang-table.el-table.el-table--border .el-table__cell {
  border-bottom-width: 2px;
  padding: 6px 0;
  font-size: 18px;
}

/deep/ .screen-dialog.mt8vh .el-dialog {
  margin-top: 8vh !important;
}

.fireCost-chart {
  flex: 1;
  min-height: 170px;
  width: 100%; /* 调整图表的宽度 */
}

/deep/ .el-date-editor.el-range-editor .el-range-input,
/deep/ .el-date-editor.el-range-editor .el-range-separator {
  color: #fff !important;
}
</style>
