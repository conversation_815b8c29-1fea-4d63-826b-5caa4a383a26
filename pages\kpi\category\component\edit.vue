<template>
  <div>
    <el-dialog
      :title="title + '指标'"
      :visible.sync="visible"
      :width="'600px'"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="150px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="指标名称"
          prop="name"
        >
          <el-input
            v-model="formData.name"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入指标名称"
          />
        </el-form-item>

        <el-form-item
          label="适用对象"
          prop="spareField"
        >
          <el-radio
            v-for="(item, index) in spareFieldList"
            v-model="formData.type"
            :key="index"
            :label="item.value"
            border>{{ item.label }}</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { roleAdd, roleEdit, userAdd, userEdit } from '@/api/system'
import { kpiCategorySave, kpiSave } from '@/api/kpi'
import { ENUM } from '@/lib/Constant'
import SelectKpi from '@/components/SelectKpi'

export default {
  components: { SelectKpi },
  mixins: [EditMixins],
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  data() {
    return {
      visible: false,
      url: {
        edit: kpiCategorySave,
        add: kpiCategorySave
      },
      kpiFunction: ENUM.kpiFunction,
      factoryList: ENUM.factoryList,
      levelList: ENUM.levelList,
      statusList: [
        {
          value: 0,
          label: '正常',
          type: 'success'
        },
        {
          value: 1,
          label: '废弃',
          type: 'warning'
        }
      ],
      formData: {
        spareField: null,
        name: null
      },
      spareFieldList: ENUM.categoryList,
      rules: {
        name: [
          {
            required: true,
            message: '请输入指标名称',
            trigger: 'change'
          }
        ],
        type: [
          {
            required: true,
            message: '请选择适用分类',
            trigger: 'change'
          }
        ]
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    console.log('编辑页面')
  },
  methods: {
    show() {
      console.log(this.formData.parentName)
    },
    submitBefore() {
      // this.formData.isShow = true
      // this.status = 0
    }
  }
}
</script>
<style scoped>
</style>
