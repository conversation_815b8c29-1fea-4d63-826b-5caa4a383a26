<template>
  <div>
    <div class="forecast-top" >
      <el-row :gutter="10">
        <el-col :span="6">
          <div
            class="item">
            <img
              :src="getIcon(8)"
              alt="">
            <div class="node-describe">
              <div class="tit">目标产量</div>
              <span> {{ topModel.targetValue.toFixed(0) || 0 }} <small>吨</small></span>
            </div>
          </div>
        </el-col>
        <!--        <el-col :span="6">-->
        <!--          <div-->
        <!--            class="item">-->
        <!--            <img-->
        <!--              :src="getIcon(1)"-->
        <!--              alt="">-->
        <!--            <div class="node-describe">-->
        <!--              <div class="tit">预测目标产量</div>-->
        <!--              <span>  {{ productionForecast || 0 }} <small>吨</small></span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </el-col>-->
        <el-col :span="6">
          <div
            class="item">
            <img
              :src="getIcon(1)"
              alt="">
            <div class="node-describe">
              <div class="tit">滚动预测产量</div>
              <span>  {{ topModel.productionForecast.toFixed(0) || 0 }} <small>吨</small></span>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div
            class="item">
            <img
              :src="getIcon(2)"
              alt="">
            <div class="node-describe">
              <div class="tit">目标机时产量</div>
              <span> {{ targetHourOutput }} <small/>t/h</span>
            </div>
          </div>
        </el-col>
        <!--        <el-col :span="4">-->
        <!--          <div-->
        <!--            class="item">-->
        <!--            <img-->
        <!--              :src="getIcon(6)"-->
        <!--              alt="">-->
        <!--            <div class="node-describe">-->
        <!--              <div class="tit">预测目标机时产量</div>-->
        <!--              <span> {{ avgMachineOutput }} <small>t/h</small></span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </el-col>-->
        <el-col :span="6">
          <div
            class="item">
            <img
              :src="getIcon(6)"
              alt="">
            <div class="node-describe">
              <div class="tit">滚动预测机时产量</div>
              <span> {{ topModel.orderReliableMachineOutput }} <small>t/h</small></span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="forecast-time shadow-light">
      <div class="name">时间预测</div>
      <div class="time-period">
        <el-form 
          :inline="true"
          class="demo-form-inline">
          <el-form-item label="生产周期">
            <el-date-picker
              v-model="forecastObj.period"
              :picker-options="pickerOptions"
              type="month"
              value-format="yyyy-MM"
              placeholder="选择月份"
              @change="init"/>
          </el-form-item>
          <el-form-item
            label="共计">
            <el-input
              v-model="days"
              placeholder="请输入内容"
              style="width: 220px">
              <template slot="append">天</template>
            </el-input>
          </el-form-item>
          <el-form-item
            label-width="0"
            style="width: 220px">
            <el-input
              v-model="times"
              placeholder="请输入内容">
              <template slot="append">班次</template>
            </el-input>
          </el-form-item>
          <el-form-item
            label-width="0"
            style="width: 220px">
            <el-input
              v-model="hours"
              placeholder="请输入内容">
              <template slot="append">小时</template>
            </el-input>
          </el-form-item>
        </el-form>
        <el-alert
          v-if="!canEdit"
          :title="'本周期产量已经预测完毕'"
          :closable="false"
          class="forecast-alert"
          type="warning"
          show-icon/>
      </div>
      <div class="time-con">
        <div class="time-item">
          <el-form
            :inline="true"
            class="demo-form-inline">
            <el-form-item
              label-width="0">
              <el-checkbox v-model="forecastObj.conditionStopsStatus"/>
            </el-form-item>
            <el-form-item
              :label="'检修时间'">
              <el-autocomplete
                v-model="forecastObj.reconditionTime"
                :fetch-suggestions="reasonList[0].fn"
                :popper-append-to-body="false"
                placeholder="请输入内容"
                popper-class="forecast-inp"
                style="width: 220px">
                <template slot="append">小时</template>
                <template slot-scope="{ item }">
                  <span><em style="display: inline-block; min-width: 110px">{{ item.name }}</em> {{ item.value }}</span>
                </template>
              </el-autocomplete>
            </el-form-item>
            <el-form-item
              :label="'换辊时间'">
              <el-autocomplete
                v-model="forecastObj.rollChangeTime"
                :fetch-suggestions="reasonList[1].fn"
                :popper-append-to-body="false"
                placeholder="请输入内容"
                popper-class="forecast-inp"
                style="width: 220px">
                <template slot="append">小时</template>
                <template slot-scope="{ item }">
                  <span><em style="display: inline-block; min-width: 110px">{{ item.name }}</em> {{ item.value }}</span>
                </template>
              </el-autocomplete>
            </el-form-item>
            <el-form-item
              :label="'故障停时'">
              <el-autocomplete
                v-model="forecastObj.repairTime"
                :fetch-suggestions="reasonList[2].fn"
                :popper-append-to-body="false"
                placeholder="请输入内容"
                popper-class="forecast-inp"
                style="width: 220px">
                <template slot="append">小时</template>
                <template slot-scope="{ item }">
                  <span><em style="display: inline-block; min-width: 110px">{{ item.name }}</em> {{ item.value }}</span>
                </template>
              </el-autocomplete>
            </el-form-item>
          </el-form>
        </div>
        <div class="time-item">
          <el-form
            :inline="true"
            class="demo-form-inline">
            <el-form-item
              label-width="0">
              <el-checkbox v-model="forecastObj.researchStopsStatus"/>
            </el-form-item>
            <el-form-item
              :label="'品种试验'">
              <el-autocomplete
                v-model="forecastObj.researchTime"
                :fetch-suggestions="reasonList[3].fn"
                :popper-append-to-body="false"
                popper-class="forecast-inp"
                placeholder="请输入内容"
                style="width: 220px">
                <template slot="append">小时</template>
                <template slot-scope="{ item }">
                  <span><em style="display: inline-block; min-width: 110px">{{ item.name }}</em> {{ item.value }}</span>
                </template>
              </el-autocomplete>
            </el-form-item>
          </el-form>
        </div>
        <div class="time-item">
          <el-form
            :inline="true"
            class="demo-form-inline">
            <el-form-item
              label-width="0">
              <el-checkbox v-model="forecastObj.otherStopsStatus"/>
            </el-form-item>
            <el-form-item
              :label="'其他停时'">
              <el-autocomplete
                v-model="forecastObj.otherBlockingTime"
                :fetch-suggestions="reasonList[4].fn"
                :popper-append-to-body="false"
                popper-class="forecast-inp"
                placeholder="请输入内容"
                style="width: 220px">
                <template slot="append">小时</template>
                <template slot-scope="{ item }">
                  <span><em style="display: inline-block; min-width: 110px">{{ item.name }}</em> {{ item.value }}</span>
                </template>
              </el-autocomplete>
            </el-form-item>
          </el-form>
          <div class="btn-wrapper text-center">
            <el-button
              v-if="canEdit"
              :loading="loading || orderAdapting"
              type="primary"
              @click="saveForecast" >提交</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="forecast-order shadow-light">
      <div class="name">
        订单预测
      </div>
      <el-row :gutter="0">
        <el-col :span="24">
          <el-alert
            v-if="orderAdapting"
            :title="'正在预测'"
            :closable="false" 
            :description="loadingText"
            class="forecast-alert"
            type="warning"
            show-icon/>
          <div
            v-loading="orderAdapting"
            :element-loading-text="loadingText"
            class="order-wrapper">
            <div class="list-header">
              <div>
                手持订单
              </div>
              <div class="right inp-name">
                <el-form inline>
                  <el-form-item :label="'订单时间'">
                    <el-date-picker
                      v-model="orderProductionForm.handleDateRange"
                      :clearable="false"
                      :picker-options="pickerRangeOptions"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"/>
                  </el-form-item>
                  <el-form-item :label="'预测规则'">
                    <el-select
                      v-model="orderProductionRule"
                      placeholder="请选择">
                      <el-option
                        v-for="item in rules"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                  </el-form-item>
                  <el-form-item label-width="0">
                    <el-button
                      v-if="canEdit"
                      type="warning"
                      @click="orderAdapt">开始预测</el-button>
                    <el-button
                      type="primary"
                      @click="orderMachineOutput()"
                    >机时产量查询
                    </el-button>
                    <el-button
                      type="primary"
                      @click="orderDetail()"
                    >订单详情
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
            <el-dialog
              :width="'500px'"
              :close-on-click-modal="false"
              :visible.sync="importVisible">
              <el-form>
                <el-form-item :label="'订单时间'">
                  <el-date-picker
                    v-model="orderProductionForm.handleDateRange"
                    :clearable="false"
                    :picker-options="pickerRangeOptions"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"/>
                </el-form-item>
                <el-form-item :label="'推算规则'">
                  <el-select
                    v-model="orderProductionRule"
                    placeholder="请选择">
                    <el-option
                      v-for="item in rules"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"/>
                  </el-select>
                </el-form-item>
              </el-form>
              <div
                slot="footer"
                class="text-center">
                <el-button
                  type="primary"
                  @click="orderAdapt()"
                >确定
                </el-button>
              </div>
            </el-dialog>
            <order-detail
              ref="orderDetail"
              :factory="forecastObj.factory"
              :period="forecastObj.period"
              :can-edit="canEdit"
              :order-type="1"
              @update="updateProductForecast"
              @modify="addManualProductionLogs($event)"/>
            <div class="description-box">
              <el-descriptions
                :column="4"
                :size="'medium'"
                border>
                <el-descriptions-item
                  label="订单总重量">
                  <span>{{ forecastObj.orderWeight }}吨</span>
                </el-descriptions-item>
                <el-descriptions-item
                  label="订单开始时间">
                  <span>{{ forecastObj.orderStartTime || 0 }}</span>
                </el-descriptions-item>
                <el-descriptions-item
                  :span="2"
                  label="订单结束时间">
                  <span>{{ forecastObj.orderEndTime || 0 }}</span>
                </el-descriptions-item>
                <el-descriptions-item
                  label="已匹配总重量">
                  <span>{{ forecastObj.orderAdaptedWeight || 0 }}吨</span>
                </el-descriptions-item>
                <el-descriptions-item
                  label="可信总重量">
                  <span>{{ forecastObj.orderReliableWgt || 0 }}吨</span>
                </el-descriptions-item>
                <el-descriptions-item
                  label="平均宽度">
                  <span>{{ forecastObj.orderAvgWidth || 0 }} mm</span>
                </el-descriptions-item>
                <el-descriptions-item
                  label="平均厚度">
                  <span>{{ forecastObj.orderAvgThickness || 0 }} mm</span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="table">
              <el-table
                :data="orderProductionData"
                border
                style="width: 100%">
                <el-table-column
                  type="index"
                  label="序号"/>
                <el-table-column
                  prop="otherSteelType"
                  label="国标钢种"/>
                <el-table-column
                  prop="standardNo"
                  label="标准号"/>
                <el-table-column
                  prop="dealResult"
                  label="保性能"
                  width="80">
                  <template v-slot="{ row }">
                    <el-tag
                      v-if="row.matrFl !== null"
                      :type="getDict(row.matrFl, 'matrFlList').type"
                      disable-transitions>{{ getDict(row.matrFl, 'matrFlList').label }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="thickness"
                  label="订单厚度"/>
                <el-table-column
                  prop="width"
                  label="订单宽度"/>
                <el-table-column
                  prop="dealResult"
                  label="适配结果"
                  width="120px">
                  <template v-slot="{ row }">
                    <el-tag
                      v-if="row.dealResult !== null"
                      :type="getDict(row.dealResult, 'matchType').tag"
                      disable-transitions>{{ getDict(row.dealResult, 'matchType').label }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="wgt"
                  label="订单重量">
                  <template v-slot="{ row }">
                    <div style="white-space: nowrap">
                      {{ row.orderWeight }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="userDefMachineOutput"
                  label="用户确认机时产量">
                  <template v-slot="{ row }">
                    <div style="white-space: nowrap">
                      {{ row.userDefMachineOutput }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="productionTime"
                  label="生产耗时"/>
                <el-table-column
                  prop="time"
                  label="操作">
                  <template v-slot="{ row }">
                    <el-button
                      v-if="canEdit"
                      slot="append"
                      type="text"
                      @click="handleEditOrder(row)">修改</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <br>
              <el-row
                align="middle"
                class="table-pagination"
                justify="end"
                type="flex"
              >
                <el-pagination
                  :current-page="orderProductionForm.page"
                  :page-size="orderProductionForm.size"
                  :page-sizes="[10, 20, 30, 40]"
                  :total="orderProductionForm.total"
                  background
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </el-row>
            </div>
            <br>
            <div class="list-header">
              <div>
                修改日志
              </div>
              <div class="right inp-name">
                <el-button
                  type="primary"
                  @click="$refs.orderLogDetail.orderDetailVisible = true"
                >查看更多
                </el-button>
              </div>
            </div>
            <order-log-detail
              ref="orderLogDetail"
              :factory="forecastObj.factory"
              :period="forecastObj.period"/>
            <div class="table table-log">
              <el-table
                :data="manualProductionLogs"
                border
                style="width: 100%">
                <el-table-column
                  type="index"
                  label="序号"/>
                <el-table-column
                  prop="owfid"
                  label="订单项次"/>
                <el-table-column
                  prop="standardNo"
                  label="标准号"/>
                <el-table-column
                  prop="otherSteelType"
                  label="国标钢种"/>
                <el-table-column
                  prop="dealResult"
                  label="保性能"
                  width="80">
                  <template v-slot="{ row }">
                    <el-tag
                      v-if="row.matrFl !== null"
                      :type="getDict(row.matrFl, 'matrFlList').type"
                      disable-transitions>{{ getDict(row.matrFl, 'matrFlList').label }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="thickness"
                  label="厚度"/>
                <el-table-column
                  prop="width"
                  label="宽度"/>
                <el-table-column
                  prop="logs"
                  label="调整记录"/>
              </el-table>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="0">
        <el-col :span="24">
          <div class="order-wrapper">
            <div class="list-header">
              <el-checkbox v-model="forecastObj.manualOrderStatus">订单手动补齐（仅预测）</el-checkbox>
              <div class="right inp-name">
                <el-button
                  type="primary"
                  @click="orderDetailManual()"
                >订单详情
                </el-button>
                <el-button
                  v-if="canEdit"
                  type="success"
                  @click="showAddManual">添加订单</el-button>
              </div>
            </div>
            <order-detail
              ref="orderDetailManual"
              :factory="forecastObj.factory"
              :period="forecastObj.period"
              :can-edit="canEdit"
              :order-type="2"
              @update="updateProductForecast"/>
            <!--手补订单添加显示-->
            <el-dialog
              :width="'1400px'"
              :visible.sync="manualMachineOutputVisible"
              @close="updateProductForecast">
              <div
                v-if="manualMachineOutputVisible"
                class="search-wrapper" >
                <el-form
                  ref="form"
                  :label-width="'80px'"
                  :model="orderMachineOutputForm.searchForm"
                  size="small"
                  inline
                  @submit.native.prevent="getOrderMachineOutput(true)"
                >
                  <el-form-item
                    prop="standardNo"
                  >
                    <el-input
                      v-model="orderMachineOutputForm.searchForm.standardNo"
                      suffix-icon="el-icon-search"
                      clearable
                      placeholder="请输入标准号"
                      style="width:120px"
                      type="text"
                    />
                  </el-form-item>
                  <el-form-item
                    prop="standardNo"
                    label="厚度区间"
                  >
                    <el-input-number
                      v-model="orderMachineOutputForm.searchForm.thicknessDown"
                      :min="0"
                      :max="orderMachineOutputForm.searchForm.thicknessUp"
                      :controls="false"
                      style="width: 80px"/>
                    —
                    <el-input-number
                      v-model="orderMachineOutputForm.searchForm.thicknessUp"
                      :min="orderMachineOutputForm.searchForm.thicknessDown || 0"
                      :max="100000"
                      :controls="false"
                      style="width: 80px"/>
                  </el-form-item>
                  <el-form-item
                    prop="standardNo"
                    label="宽度区间"
                  >
                    <el-input-number
                      v-model="orderMachineOutputForm.searchForm.widthDown"
                      :min="0"
                      :max="orderMachineOutputForm.searchForm.widthUp"
                      :controls="false"
                      style="width: 80px"/>
                    —
                    <el-input-number
                      v-model="orderMachineOutputForm.searchForm.widthUp"
                      :min="orderMachineOutputForm.searchForm.widthDown || 0"
                      :max="100000"
                      :controls="false"
                      style="width: 80px"/>
                  </el-form-item>
                  <el-form-item
                    prop="matrFl"
                    label="保性能"
                  >
                    <el-select
                      v-model="orderMachineOutputForm.searchForm.matrFl"
                      placeholder="请选择"
                      clearable
                      style="width: 100px">
                      <el-option
                        :label="'是'"
                        :value="'1'"/>
                      <el-option
                        :label="'否'"
                        :value="'0'"/>
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    prop="matrFl"
                    label-width="80"
                    label="机时产量推荐"
                  >
                    <el-select
                      v-model="manualProductionRule"
                      placeholder="请选择"
                      clearable
                      style="width: 120px">
                      <el-option
                        v-for="item in rules"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                  </el-form-item>
                  <el-button
                    icon="el-icon-search"
                    type="primary"
                    @click="getOrderMachineOutput"
                  >搜索
                  </el-button>
                </el-form>
              </div>
              <div class="table">
                <el-table
                  :data="orderMachineOutputForm.data"
                  border
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    label="序号"/>
                  <el-table-column
                    prop="standardNo"
                    label="标准号"/>
                  <el-table-column
                    prop="thickness"
                    label="订单厚度"/>
                  <el-table-column
                    prop="width"
                    label="订单宽度"/>
                  <el-table-column
                    prop="dealResult"
                    label="保性能">
                    <template v-slot="{ row }">
                      <el-tag
                        v-if="row.matrFl !== null"
                        :type="getDict(row.matrFl, 'matrFlList').type"
                        disable-transitions>{{ getDict(row.matrFl, 'matrFlList').label }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="avgInMat"
                    label="机时产量-推荐值">
                    <template v-slot="{ row }">
                      <span>{{ row[getDict(manualProductionRule, 'rules').dict] }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="avgInMat"
                    label="订单重量">
                    <template v-slot="{ row }">
                      <el-input
                        v-model="row.orderWeight"/>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="avgInMat"
                    label="操作">
                    <template v-slot="{ row }">
                      <el-button
                        type="text"
                        size="mini"
                        @click.native.stop="addManual($event, row)">
                        添加
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <br>
                <el-row
                  align="middle"
                  class="table-pagination"
                  justify="end"
                  type="flex"
                >
                  <el-pagination
                    :current-page="orderMachineOutputForm.page"
                    :page-size="orderMachineOutputForm.size"
                    :page-sizes="[10, 20, 30, 40]"
                    :total="orderMachineOutputForm.total"
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleMachineSizeChange"
                    @current-change="handleMachineCurrentChange"
                  />
                </el-row>
              </div>
            </el-dialog>
            <div class="table">
              <el-table
                :data="manualProductionData"
                border
                style="width: 100%">
                <el-table-column
                  type="index"
                  label="序号"/>
                <el-table-column
                  prop="standardNo"
                  label="标准号"/>
                <el-table-column
                  prop="thickness"
                  label="订单厚度"/>
                <el-table-column
                  prop="width"
                  label="订单宽度"/>
                <el-table-column
                  prop="dealResult"
                  label="适配结果">
                  <template v-slot="{ row }">
                    <el-tag
                      v-if="row.dealResult !== null"
                      :type="getDict(row.dealResult, 'matchType').tag"
                      disable-transitions>{{ getDict(row.dealResult, 'matchType').label }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="orderWeight"
                  label="订单重量"
                  width="100px"/>
                <el-table-column
                  prop="userDefMachineOutput"
                  label="用户确认机时产量">
                  <template v-slot="{ row }">
                    <div style="white-space: nowrap">
                      <el-input
                        v-model="row.userDefMachineOutput"
                        @change="userDefChange($event, row)"/>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="productionTime"
                  label="生产耗时"/>
              </el-table>
              <br>
              <el-row
                align="middle"
                class="table-pagination"
                justify="end"
                type="flex"
              >
                <el-pagination
                  :current-page="manualProductionForm.page"
                  :page-size="manualProductionForm.size"
                  :page-sizes="[10, 20, 30, 40]"
                  :total="manualProductionForm.total"
                  background
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleManualSizeChange"
                  @current-change="handleManualCurrentChange"
                />
              </el-row>
            </div>
          </div>
        </el-col>

        <el-col :span="24">
          <div class="order-wrapper">
            <div
              class="name"
              style="padding: 0">剩余生产时间产量预测</div>

            <el-form
              :inline="true"
              class="demo-form-inline">
              <el-form-item
                :class="{'warning': remainHours < 0}"
                :label="'剩余时数'">
                <el-input
                  v-model="remainHours"
                  readonly
                  placeholder="请输入内容">
                  <template slot="append">小时</template>
                </el-input>
              </el-form-item>
              <el-form-item
                :label="'预测机时产量'">
                <el-autocomplete
                  v-model="forecastObj.historyMachineProductionWgt"
                  :fetch-suggestions="historyMachineProductionWgtList"
                  :popper-append-to-body="false"
                  popper-class="forecast-inp"
                  placeholder="请输入内容"
                  style="width: 220px">
                  <template slot="append">吨/小时</template>
                  <template slot-scope="{ item }">
                    <span>{{ item.name }}</span>
                  </template>
                </el-autocomplete>
              </el-form-item>
              <el-form-item
                :label="'预估产量'">
                <el-input
                  v-model="remainHoursWeight"
                  readonly
                  placeholder="请输入内容">
                  <template slot="append">吨/小时</template>
                </el-input>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
      </el-row>
      <div class="btn-wrapper">
        <el-button
          v-if="canEdit"
          :loading="loading || orderAdapting"
          type="primary"
          @click="saveForecast" >提交</el-button>
      </div>
    </div>

  </div>
</template>

<script>
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import { math } from '@/lib/Math.js'
import * as _ from 'lodash'
import {
  findForecast,
  findMachineOutputByConditions,
  findMachineOutputData,
  findMachineOutputDatasById,
  findOrders,
  findStoppedTimeByReason,
  findStoppedTimeData,
  getBUProductWgt,
  getEvalOverview,
  machineOutputAdapt,
  orderAdapt,
  saveForecast,
  saveOrder,
  saveOrders,
  updateOrder,
  updatePlannedConditionServiceStops
} from '@/api/kpi'
import OrderDetail from '@/pages/kpiForecast/forecast/component/orderDetail'
import OrderLogDetail from '@/pages/kpiForecast/forecast/component/orderLogDetail'

export default {
  name: 'forecast',
  components: { OrderLogDetail, OrderDetail },
  data: () => {
    return {
      //
      list: [],
      loading: false,
      reasonList: [
        // 101.检修 102.换辊 103.故障 201.研发 301.限电停时 302.其他
        {
          code: 101,
          list: []
        },
        {
          code: 102,
          list: []
        },
        {
          code: 103,
          list: []
        },
        {
          code: 201,
          list: []
        },
        {
          code: 301,
          list: []
        }
      ],
      historyMachineProductionWgtList: null,
      forecastObj: {
        factory: 2,
        period: '',
        conditionStopsStatus: false,
        researchStopsStatus: false,
        otherStopsStatus: false,
        orderStatus: true,
        manualOrderStatus: false,
        effectiveTimeToProduce: null, // 有效生产时间
        spendTimeOnOrder: null, // 手持订单生产耗时
        equipmentOperationRate: null, // 设备作业率预测
        productionForecast: null, // 产量预测
        reconditionTime: null, // 检修时间
        rollChangeTime: null, // 换辊时间
        repairTime: null, // 故障停时
        researchTime: null, // 品种试验停时
        otherBlockingTime: null, // 其他停时
        orderProductionTime: null, // 手持订单预估生产用时
        orderWeight: null, // 手持订单预估生产重量
        orderProductionType: null, // 中间品机时产量协作方式
        midProductionPrdTime: null, // 中间品预估生产用时
        midProductionWeight: null, // 中间品预估生产重量
        midProductionType: null, // 手持订单机时产量协作方式
        manualProductionTime: null, // 手补订单预估生产用时
        manualWeight: null, // 手补订单预估生产重量
        manualProductionType: null, // 手补订单机时产量协作方式
        remainHours: null, // 剩余时数
        historyProductionType: null, // 历史机时产量协作方式
        historyMachineProductionWgt: null, // 历史机时产量
        remainHoursWeight: null // 剩余时数预估产量
      },
      orderProductionData: [],
      manualProductionData: [],
      manualProductionLogs: [],
      orderProductionRule: 0,
      manualProductionRule: 0,
      rules: ENUM.cooperation,
      matchType: ENUM.matchType,
      // 性能要求列表
      matrFlList: ENUM.matrFlList,

      // C1 板卷厂   C2 宽厚板厂  C3 中板厂
      // 3：中板厂 2：板卷厂 3：宽厚板厂
      // 厂代号映射关系
      CFM_MILL_PLT: {
        3: 'C3',
        1: 'C1',
        2: 'C2'
      },
      orderProductionForm: {
        newOrder: false,
        handleDateRange: null,
        page: 0,
        size: 10,
        total: 0
      },
      orderAdapting: false,
      loadingText: '',
      orderAdaptTime: 0,
      importVisible: false,
      orderDetailVisible: false, // 订单详情
      orderMachineOutputVisible: false, // 机时产量查询
      orderMachineOutputForm: {
        searchForm: {
          standardNo: '',
          steelType: ''
        },
        data: [],
        page: 0,
        size: 10,
        total: 0,
        edit: {}
      },
      manualMachineOutputVisible: false, // 手补机时产量查询
      manualProductionForm: {
        searchForm: {
          standardNo: '',
          steelType: ''
        },
        data: [],
        page: 0,
        size: 10,
        total: 0,
        edit: {}
      },
      topModel: {
        monthTarget: 0, // 本月目标值,
        targetValue: 0, // 本月目标值,
        planWgt: 0, // 滚动预测产量
        realMonthWgt: 0, // 本月实时产量
        historyMachineProductionWgt: 0, // 滚动预测机时产量
        productionForecast: 0, // 滚动预测目标产量
        orderReliableMachineOutput: 0 // 可信机时产量
      },
      selectDate: null,
      otherProductionForecast: 0 // 其他两厂产量预测
    }
  },
  computed: {
    pickerOptions: function() {
      return {
        disabledDate: time => {
          return (
            time.getTime() > this.$moment().month(this.$moment().month() + 1)
          )
        }
      }
    },
    pickerRangeOptions: function() {
      return {
        onPick: ({ maxDate, minDate }) => {
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.selectDate = minDate.getTime()
          if (maxDate) {
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            this.selectDate = ''
          }
        },
        // 最多只能选择五个月限制
        disabledDate: time => {
          if (this.selectDate !== '') {
            const one = 150 * 24 * 3600 * 1000
            const minTime = this.selectDate - one
            const maxTime = this.selectDate + one
            return time.getTime() < minTime || time.getTime() > maxTime
          }
        }
      }
    },
    days: function() {
      return this.$moment(this.forecastObj.period, 'YYYY-MM').daysInMonth()
    },
    hours: function() {
      return this.days * 24
    },
    times: function() {
      return this.days * 3
    },
    // 是否可编辑
    canEdit: function() {
      if (!this.forecastObj.period) return false
      return (
        this.forecastObj.period >
        this.$moment()
          .subtract(1, 'month')
          .format('yyyy-MM')
      )
    },
    // 月可用生产时间
    effectiveTimeToProduce: function() {
      let num = this.hours
      if (this.forecastObj.conditionStopsStatus) {
        num = _.subtract(num, Number(this.forecastObj.repairTime))
        num = _.subtract(num, Number(this.forecastObj.rollChangeTime))
        num = _.subtract(num, Number(this.forecastObj.reconditionTime))
      }
      if (this.forecastObj.researchStopsStatus) {
        num = _.subtract(num, Number(this.forecastObj.researchTime))
      }
      if (this.forecastObj.otherStopsStatus) {
        num = _.subtract(num, Number(this.forecastObj.otherBlockingTime))
      }
      return num
    },
    // 目标机时产量
    targetHourOutput: function() {
      return math
        .divide(
          this.topModel.targetValue || 0,
          this.effectiveTimeToProduce || 0
        )
        .toFixed(1)
    },
    spendTimeOnOrder: function() {
      let num = 0
      if (this.forecastObj.orderStatus) {
        num += _.sumBy(this.orderProductionData, function(o) {
          return Number(o.productionTime)
        })
      }
      if (this.forecastObj.manualOrderStatus) {
        num += _.sumBy(this.manualProductionData, function(o) {
          return Number(o.productionTime)
        })
      }
      return num
    },
    equipmentOperationRate: function() {
      return (_.divide(this.effectiveTimeToProduce, this.hours) * 100).toFixed(
        2
      )
    },

    // 剩余时长
    remainHours: function() {
      let num = this.effectiveTimeToProduce
      if (this.forecastObj.orderStatus) {
        num = math.subtract(num, this.forecastObj.orderProductionTime)
      }
      if (this.forecastObj.manualOrderStatus) {
        num = math.subtract(num, this.forecastObj.manualProductionTime)
      }
      return num.toFixed(2)
    },

    // 历史-预估产量
    remainHoursWeight() {
      return math
        .multiply(
          this.remainHours,
          this.forecastObj.historyMachineProductionWgt || 0
        )
        .toFixed(2)
    },

    // 产量预测
    productionForecast() {
      let num = 0
      if (this.forecastObj.orderStatus) {
        num = math.add(num, this.forecastObj.orderReliableWgt || 0)
      }
      if (this.forecastObj.manualOrderStatus) {
        num = math.add(num, this.forecastObj.manualWeight || 0)
      }
      num = math.add(num, this.remainHoursWeight || 0)
      return num.toFixed(0)
    },

    // 事业部总产量
    allProductionForecast() {
      return math.add(this.productionForecast, this.otherProductionForecast)
    },

    // 平均机时产量
    avgMachineOutput() {
      return this.forecastObj.orderProductionTime
        ? math
            .divide(
              this.forecastObj.orderReliableWgt || 0,
              this.forecastObj.orderProductionTime || 0
            )
            .toFixed(2)
        : 0
    }
  },
  created() {
    // 已生产时间: 调用轧制实绩数据接口
    // 剩余可用生产时间=总时数-设备总停时目标值-5个分目标超目标值的时间-已生产时间
    // 建议后期排产订单机时产量值= (本月目标产量-本月实时产量)/剩余可用生产时间

    // 获取默认订单时间范围
    this.orderProductionForm.handleDateRange = [
      this.$moment()
        .subtract(3, 'months')
        .format('yyyy-MM') + '-20',
      this.$moment()
        .subtract(-4, 'months')
        .format('yyyy-MM') + '-20'
    ]
    this.selectDate = this.$moment().subtract(60, 'days')
    this.forecastObj.factory = parseInt(this.$route.params.id)
    this.forecastObj.period = this.$moment()
      .subtract(1, 'days')
      .format('yyyy-MM')
    this.init()
  },
  methods: {
    init() {
      this.getData()
      // this.getOrders(1, 'orderProduction')
      // this.getOrders(2, 'manualProduction')
    },
    async getData() {
      this.clearForm()
      // 输数据
      post(findForecast, {
        period: this.forecastObj.period,
        factory: this.forecastObj.factory
      }).then(res => {
        if (res.data) {
          Object.assign(this.forecastObj, res.data)
          this.getOrders(1, 'orderProduction')
          this.getOrders(2, 'manualProduction')
          this.orderProductionForm.newOrder = false
          this.forecastObj.rollChangeTime =
            this.forecastObj.rollChangeTime ||
            (this.topModel.targetValue / 6000 / 2).toFixed(2)
        } else {
          this.orderProductionForm.newOrder = true
        }
        // 生成预测机时产量
        this.forecastObj.historyMachineProductionWgt =
          this.avgMachineOutput || this.forecastObj.historyMachineProductionWgt
      })
      // 滚动预测数据
      post(findForecast, {
        period: this.forecastObj.period + '-R',
        factory: this.forecastObj.factory
      }).then(res => {
        if (res.success) {
          this.topModel.productionForecast = res.data
            ? res.data.productionForecast
            : 0
          this.topModel.historyMachineProductionWgt = res.data
            ? res.data.historyMachineProductionWgt
            : 0
          this.topModel.orderReliableMachineOutput = res.data
            ? res.data.orderReliableMachineOutput
            : 0
        }
      })
      // 数据概览
      post(getEvalOverview, {
        period: this.forecastObj.period,
        factory: this.forecastObj.factory,
        millDate: this.$moment()
          .subtract(1, 'days')
          .format('yyyy-MM-DD')
      }).then(res => {
        Object.assign(this.topModel, res.data)
        this.forecastObj.rollChangeTime =
          this.forecastObj.rollChangeTime ||
          (this.topModel.targetValue / 6000 / 2).toFixed(2)
      })
      // 获取其他厂的产量
      post(getBUProductWgt, {
        period: this.forecastObj.period,
        factory: this.forecastObj.factory
      }).then(res => {
        if (res.success) {
          this.otherProductionForecast = res.data
        }
      })

      // 停时列表
      for (const item of this.reasonList) {
        const index = this.reasonList.indexOf(item)
        const res = await post(findStoppedTimeByReason, {
          stoppedReason: item.code, // 101.检修 102.换辊 103.故障 201.研发 301.其他
          factory: this.forecastObj.factory
        })
        item.list = [
          { name: '近1年最好值', value: String(res.data.bestInMat) },
          { name: '近1年最差值', value: String(res.data.worstInMat) },
          { name: '近1年平均值', value: String(res.data.avgInMat) },
          { name: '近1年中位数', value: String(res.data.midInMat) },
          { name: '近3月平均值', value: String(res.data.avgInLast3m) },
          { name: '滚动3月最佳值', value: String(res.data.bestInRolling3m) },
          // { name: '本月均值', value: String(res.data.avgInThisMonth) },
          { name: '上月均值', value: String(res.data.avgInLastMonth) }
        ]
        if (item.code === 101) {
          const res = await post(updatePlannedConditionServiceStops, {})
          item.list.unshift({
            name: '检修计划停时',
            value:
              res.data[
                this.CFM_MILL_PLT[this.forecastObj.factory] +
                  'PlannedConditionServiceStops'
              ]
          })
        }
        item.fn = (str, cb) => {
          cb(item.list)
        }
        new Promise(resolve => true)
      }
      // 历史机时产量
      post(findMachineOutputDatasById, {
        id: '0'
      }).then(res => {
        const list = res.data
          ? [
              { name: '近1年最好值', value: String(res.data.bestInMat) },
              { name: '近1年最差值', value: String(res.data.worstInMat) },
              { name: '近1年平均值', value: String(res.data.avgInMat) },
              { name: '近1年中位数', value: String(res.data.midInMat) },
              { name: '近3月平均值', value: String(res.data.avgInLast3m) },
              {
                name: '滚动3月最佳值',
                value: String(res.data.bestInRolling3m)
              },
              { name: '本月均值', value: String(res.data.avgInThisMonth) },
              { name: '上月均值', value: String(res.data.avgInLastMonth) }
            ]
          : []
        this.historyMachineProductionWgtList = (str, cb) => {
          cb(list)
        }
      })
    },
    addManualProductionLogs($event) {
      this.manualProductionLogs.push($event)
    },
    // 更新产量预测
    updateProductForecast() {
      post(findForecast, {
        period: this.forecastObj.period,
        factory: this.forecastObj.factory
      }).then(res => {
        if (res.data) {
          this.forecastObj = res.data
          this.forecastObj.orderStatus = true
          this.getOrders(1, 'orderProduction')
          this.getOrders(2, 'manualProduction')
        }
      })
    },
    // 重置表单
    clearForm() {
      Object.assign(this.forecastObj, {
        effectiveTimeToProduce: null, // 有效生产时间
        spendTimeOnOrder: null, // 手持订单生产耗时
        equipmentOperationRate: null, // 设备作业率预测
        productionForecast: null, // 产量预测
        reconditionTime: null, // 检修时间
        rollChangeTime: null, // 换辊时间
        repairTime: null, // 故障停时
        researchTime: null, // 品种试验停时
        otherBlockingTime: null, // 其他停时
        orderProductionTime: null, // 手持订单预估生产用时
        orderWeight: null, // 手持订单预估生产重量
        orderProductionType: null, // 手持订单机时产量协作方式
        midProductionPrdTime: null, // 中间品预估生产用时
        midProductionWeight: null, // 中间品预估生产重量
        midProductionType: null, // 中间品机时产量协作方式
        manualProductionTime: null, // 手补订单预估生产用时
        manualWeight: null, // 手补订单预估生产重量
        manualProductionType: null, // 手补订单机时产量协作方式
        remainHours: null, // 剩余时数
        historyProductionType: null, // 历史机时产量协作方式
        historyMachineProductionWgt: null, // 历史机时产量
        remainHoursWeight: null // 剩余时数预估产量
      })
      this.forecastObj.conditionStopsStatus = false
      this.forecastObj.researchStopsStatus = false
      this.forecastObj.otherStopsStatus = false
      this.forecastObj.orderStatus = true
      this.forecastObj.manualOrderStatus = false
      this.orderProductionData = []
      this.manualProductionData = []
      this.manualProductionLogs = []
    },
    getIcon(feature) {
      const icon = ENUM.kpiFunction.find(item => item.value == feature)
      return icon ? icon.icon : ''
    },
    // 显示订单详情
    orderDetail() {
      this.$refs.orderDetail.orderDetailVisible = true
      this.$refs.orderDetail.getOrders()
    },
    // 订单预测点击
    async orderAdapt() {
      // 导入订单
      await this.$confirm(
        '开始预测，本次预测越需要' + 2 + '分钟, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.startForecast()
        })
        .catch(() => {
          this.importVisible = false
        })
    },
    // 订单预测请求
    startForecast() {
      // 提醒用户本次匹配时间大概需要 总记录数/100/7*60分钟(即每计算100单约需要7秒，秒数转化成分钟数
      const times = (((this.orderProductionForm.total / 100) * 7) / 60).toFixed(
        1
      )
      this.$message.warning('开始预测，本次预测越需要' + 2 + '分钟')
      this.loadingText = '订单预测中，本次预测约需要' + 2 + '分钟,请耐心等待...'
      this.orderAdapting = true
      post(machineOutputAdapt, {
        period: this.forecastObj.period,
        factory: this.forecastObj.factory,
        orderType: 1,
        userDef: this.orderProductionRule,
        newOrder: this.orderProductionForm.newOrder,
        startTime: this.$moment(
          this.orderProductionForm.handleDateRange[0]
        ).format('yyyy-MM-DD'),
        endTime: this.$moment(
          this.orderProductionForm.handleDateRange[1]
        ).format('yyyy-MM-DD'),
        orderState: 0
      }).then(res => {
        this.orderAdapting = false
        if (res.success) {
          this.$message.success(res.data)
          this.updateProductForecast()
        } else {
          this.$message.warning(res.data)
        }
      })
    },

    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.orderProductionForm.size = val
      this.getOrders(1, 'orderProduction')
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.orderProductionForm.page = val
      this.getOrders(1, 'orderProduction')
    },
    getOrders(orderType, name) {
      //orderType： 手持： 1， 手补：2， 中间品：3  历史所有： 0
      post(findOrders, {
        period: this.forecastObj.period,
        factory: this.forecastObj.factory,
        orderType: orderType,
        page: this[name + 'Form'].page,
        size: this[name + 'Form'].size,
        orderState: 0
      }).then(res => {
        this[name + 'Data'] = res.data.content || []
        this[name + 'Form'].total = res.data.totalResult
        // this.getMachineProduction(name)
      })
    },
    // 保存订单
    userDefChange(e, row) {
      post(updateOrder, {
        id: row.id,
        newWeight: row.orderWeight,
        newUserDefMachineOutput: row.userDefMachineOutput
      }).then(res => {
        this.$message.success('保存订单成功')
      })
    },
    handleEditOrder(row) {
      this.$refs.orderDetail.searchForm.steelType = row.steelType
      this.$refs.orderDetail.searchForm.thicknessDown = row.thickness
      this.$refs.orderDetail.searchForm.thicknessUp = row.thickness
      this.$refs.orderDetail.searchForm.widthUp = row.width
      this.$refs.orderDetail.searchForm.widthDown = row.width
      this.$refs.orderDetail.searchForm.matrFl = row.matrFl
      this.$refs.orderDetail.orderDetailVisible = true
      this.$refs.orderDetail.getOrders()
    },
    orderMachineOutput() {
      this.$refs.orderDetail.orderMachineOutputVisible = true
      this.$refs.orderDetail.getOrderMachineOutput(true)
    },
    handleMachineSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.orderMachineOutputForm.size = val
      this.getOrderMachineOutput()
    },
    handleMachineCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.orderMachineOutputForm.page = val
      this.getOrderMachineOutput()
    },

    getOrderMachineOutput(reset = false) {
      if (reset) {
        this.orderMachineOutputForm.page = 0
        this.orderMachineOutputForm.size = 10
      }
      post(
        findMachineOutputByConditions,
        Object.assign({}, this.orderMachineOutputForm.searchForm, {
          page: this.orderMachineOutputForm.page - 1,
          size: this.orderMachineOutputForm.size
        })
      ).then(res => {
        this.orderMachineOutputForm.data = res.data.content || []
        this.orderMachineOutputForm.total = res.data.totalElements
        // this.getMachineProduction(name)
      })
    },

    // 显示订单详情
    orderDetailManual() {
      this.$refs.orderDetailManual.orderDetailVisible = true
      this.$refs.orderDetailManual.getOrders()
    },

    handleManualSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.orderProductionForm.size = val
      this.getOrders(2, 'manualProduction')
    },
    handleManualCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.orderProductionForm.page = val
      this.getOrders(2, 'manualProduction')
    },
    // 手补订单添加显示
    showAddManual() {
      this.manualMachineOutputVisible = true
      this.getOrderMachineOutput(true)
    },

    // 添加手补订单
    addManual(e, row) {
      if (!row.orderWeight) return this.$message.warning('请输入订单重量')
      const data = Object.assign({}, row)
      data.userDefMachineOutput =
        row[this.getDict(this.manualProductionRule, 'rules').dict]
      post(
        saveOrder,
        Object.assign(data, {
          period: this.forecastObj.period,
          factory: this.forecastObj.factory,
          orderType: 2,
          orderState: 0,
          productionTime: (
            data.orderWeight / data.userDefMachineOutput
          ).toFixed(2)
        })
      ).then(res => {
        this.$message.success('添加订单成功!')
      })
    },

    // 推算规则变化回调
    // orderProductionTypeChange() {
    //   const type = this.getDict(this.orderProductionRule, 'rules')
    //   if (type.value) {
    //     this.orderProductionData.forEach(item => {
    //       item.userDefMachineOutput = item[type.dict]
    //     })
    //   }
    // },
    async getMachineProduction(name) {
      console.log(name)
      this.forecastObj[name + 'Type'] = this.rules.find(
        item => item.value === this[name + 'Rule']
      ).type
      for (const item of this[name + 'Data']) {
        const index = this[name + 'Data'].indexOf(item)
        let data = await post(findMachineOutputData, {
          id: item.specId,
          type: this[name + 'Rule']
        })
        if (data.data === 0) {
          data = await post(findMachineOutputData, {
            id: '0',
            type: this[name + 'Rule']
          })
        }
        item.machineProductionWgt = data.data
        if (item.machineProductionWgt && item.orderWeight) {
          item.productionTime = math
            .divide(item.orderWeight / item.machineProductionWgt)
            .toFixed(2)
        } else {
          item.productionTime = 0
        }
        this[name + 'Data'].splice(index, 1, item)
      }
    },
    saveOrder(type, name) {
      //
      post(saveOrders, this[name + 'Data']).then(res => {
        this.$message.success('保存订单成功')
      })
    },

    // 合计
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        if (![6, 7, 8].includes(index)) {
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return math.add(prev, curr)
            } else {
              return prev
            }
          }, 0)
        }
      })

      return sums
    },

    // 保存预测
    saveForecast(type, name) {
      //
      const params = this.forecastObj
      Object.assign(params, {
        effectiveTimeToProduce: this.effectiveTimeToProduce,
        spendTimeOnOrder: this.spendTimeOnOrder,
        equipmentOperationRate: this.equipmentOperationRate,
        remainHours: this.remainHours,
        remainHoursWeight: this.remainHoursWeight,
        productionForecast: this.productionForecast
      })
      // let manualWeight = 0
      // let manualProductionTime = 0
      // if (this.forecastObj.orderStatus) {
      //   manualWeight += _.sumBy(this.manualProductionData, function(o) {
      //     return Number(o.orderWeight)
      //   })
      //   manualProductionTime += _.sumBy(this.manualProductionData, function(o) {
      //     return Number(o.productionTime)
      //   })
      // }
      // params.manualWeight = manualWeight || null
      // params.manualProductionTime = manualProductionTime || null
      //
      // let orderWeight = 0
      // let orderProductionTime = 0
      // if (this.forecastObj.orderStatus) {
      //   orderWeight += _.sumBy(this.orderProductionData, function(o) {
      //     return Number(o.orderWeight)
      //   })
      //   orderProductionTime += _.sumBy(this.orderProductionData, function(o) {
      //     return Number(o.productionTime)
      //   })
      // }
      // params.orderWeight = orderWeight || null
      // params.orderProductionTime = orderProductionTime || null
      this.loading = true
      post(saveForecast, params).then(res => {
        this.loading = false
        if (res.success) {
          this.$message.success('保存成功')
        } else {
          this.$message.warning('保存失败')
        }
      })
    },

    getDict(value, list) {
      const match = this[list].find(item => item.value == value)
      return match ? match : {}
    }
  }
}
</script>

<style lang="less" scoped>
.inp-name {
  color: #666;
  margin-right: 5px;
  font-size: 14px;
}
.name {
  font-size: 16px;
  margin-bottom: 24px;
  font-weight: bold;
  color: #666;
}
.forecast-top {
  margin-bottom: 20px;
  .item {
    display: flex;
    justify-content: space-between;
    color: #fff;
    background: #5e93ed;
    padding: 4% 6%;
    border-radius: 6px;
    box-shadow: 0 0 10px rgba(34, 35, 35, 0.5);
    gap: 4px;
    span {
      display: block;
      font-size: 20px;
      line-height: 38px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      em {
        font-size: 14px;
      }
    }
    img {
      margin-right: 10px;
      width: 15%;
    }
    .tit {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    &.trend {
      background: #ffa958;
    }
    &.warning {
      background: #f56c6c;
    }
    .node-describe {
      //margin-right: 15px;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
      span {
        display: block;
        min-height: 40px;
      }
    }
    .node-arrow {
      cursor: pointer;
    }
  }
}
.forecast-time {
  margin-bottom: 20px;
  background: #fff;
  padding: 24px;
  .time-period {
    border-bottom: 1px solid #efefef;
    margin-bottom: 15px;
  }
}
.forecast-alert {
  margin-bottom: 20px;
}
.forecast-order {
  margin-bottom: 20px;
  padding: 24px;
  background: #fff;
  .inp-name {
  }
  .name {
    margin-bottom: 20px;
  }
  .list-header {
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .order-wrapper {
    //padding: 0 25px;
    margin-bottom: 20px;
  }
  .description-box {
    margin-bottom: 20px;
  }
  .btn-wrapper {
    padding: 25px;
    text-align: center;
  }
}

/deep/ .forecast-inp {
  min-width: 200px;
}
.warning /deep/ .el-input__inner {
  border-color: #c71717;
}
</style>
