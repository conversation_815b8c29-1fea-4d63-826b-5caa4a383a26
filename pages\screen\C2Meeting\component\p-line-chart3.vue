<template>
  <div
    :id="containerId"
    :style="{ height: '100%' }"/>
</template>

<script>
export default {
  name: 'p-line-chart',
  props: {
    height: {
      type: Number,
      default: 180
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    pointLine: {
      type: String,
      default: ''
    },
    lastMonthData: {
      type: String,
      default: ''
    },
    monthPlanData: {
      type: String,
      default: ''
    },
    chartData2: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
      }
    },
    showLegend: {
      type: Boolean,
      default: false
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: <PERSON><PERSON><PERSON>,
      default: true
    },
    barWidth: {
      type: Number,
      default: 46
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
        window.addEventListener('resize', this.resizeChart)
      }
      const options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#57617B'
            }
          },
          borderColor: '#1fc6ff',
          backgroundColor: '#041a21',
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        color: this.color,
        grid: {
          top: this.showLegend ? '15%' : '5%',
          left: '2%',
          right: '2%',
          bottom: '5%',
          containLabel: true
        },
        legend: {
          show: this.showLegend,
          right: 30,
          itemHeight: 8, // 修改icon图形大小
          itemWidth: 25, // 修改icon图形大小
          itemGap: 20,
          lineStyle: {
            join: 'bevel'
          },
          textStyle: {
            color: '#9facd5',
            fontSize: 12
          }
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisTick: { show: false },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0'
              }
            },
            data: this.xData
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#57617B'
              }
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'right'
            },
            splitLine: {
              lineStyle: {
                color: '#2E4262'
              }
            }
          },
          {
            type: 'value',
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#57617B'
              }
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12,
              align: 'left'
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: this.chartData.map(item => {
          return {
            name: item.name,
            type: 'line',
            smooth: true,
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 2,
                  type: item.lineType //'dotted'虚线 'solid'实线
                }
              }
            },

            // lineStyle: {
            //   type: item.lineType, // 设置为虚线
            //   //  type: 'dashed', // 设置为虚线
            //   normal: {
            //     width: 2
            //   }
            // },
            symbol: 'emptyCircle',
            symbolSize: 6,
            data: item.data
          }
        })
        //  .concat(
        //    this.chartData2.map(item => {
        //      return {
        //        name: item.name,
        //        yAxisIndex: 1,
        //        type: 'line',
        //        smooth: true,
        //        lineStyle: {
        //          type: 'dotted', // 设置为虚线
        //          normal: {
        //            width: 2
        //          }
        //        },
        //        symbol: 'emptyCircle',
        //        symbolSize: 6,
        //        data: item.data
        //        //  markLine: {
        //        //    data: 20
        //        //  }
        //      }
        //    })
        //  )
      }
      this.myChart.setOption(options)
    },
    getColor(item) {
      return !item.show
        ? 'transparent'
        : item.value < item.plan
          ? '#FF2855'
          : '#19BE6B'
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    /**
     * 将数字取整为10的倍数
     * @param {Number} num 需要取整的值
     * @param {Boolean} ceil 是否向上取整
     * @param {Number} prec 需要用0占位的数量
     */
    formatInt(num, prec = 2, ceil = true) {
      const len = String(num).length
      if (len <= prec) {
        return num
      }
      const mult = Math.pow(10, prec)
      return ceil ? Math.ceil(num / mult) * mult : Math.floor(num / mult) * mult
    },

    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;
    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }
    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
