<template>
  <div class="content">
    <div class="content-item top">
      <screen-border title="铁水">
        <!--        <template v-slot:headerRight>-->
        <!--          <span-->
        <!--            v-command="'/screen/qualityMeeting/edit'"-->
        <!--            class="screen-btn"-->
        <!--            @click="steelYesterday.dialogVisible = true">-->
        <!--            <el-icon class="el-icon-edit-outline"/>-->
        <!--            操作-->
        <!--          </span>-->
        <!--        </template>-->
        <div class="chart-wrapper">
          <div
            class="chart">
            <!--            <single-bars-chart-->
            <!--              :show-legend="false"-->
            <!--              :chart-data="steelYesterday.bar1"-->
            <!--              :chart-data2="steelYesterday.bar2"-->
            <!--              :x-data="steelYesterday.barX"/>-->
            <p-line-chart
              :show-legend="true"
              :chart-data="steelYesterday.lineBar"
              :chart-data2="steelYesterday.lineBar2"
              :x-data="steelYesterday.lineBarX"/>
          </div>
        </div>
      </screen-border>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <screen-border :title="'P含量'">
            <!--            <template v-slot:headerRight>-->
            <!--              <span-->
            <!--                v-command="'/screen/qualityMeeting/edit'"-->
            <!--                class="screen-btn"-->
            <!--                @click="PP.dialogVisible = true">-->
            <!--                <el-icon class="el-icon-edit-outline"/>-->
            <!--                操作-->
            <!--              </span>-->
            <!--            </template>-->
            <div class="chart-wrapper">
              <pie-chart
                :chart-data="PP.bar1"
                :unit="'%'" />
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <screen-border title="质量情况">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/qualityMeeting/edit'"
                class="screen-btn"
                @click="unPlanedTotal.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div
              ref="table2"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="unPlanedTotal.showGridData"
                :max-height="unPlanedTotal.maxHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  align="center"
                  type="index"
                  label="序号"
                  width="100"/>
                <el-table-column
                  align="center"
                  prop="category"
                  label="类别"
                  width="100"/>
                <el-table-column
                  align="center"
                  property="qualitySituation"
                  label="质量情况"/>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <!--产量-->
    <el-dialog
      :visible.sync="steelYesterday.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="铁水">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('steelYesterday')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importSteelData')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportSteel">
              <el-icon class="el-icon-export"/>
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveSteel">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          铁水
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="steelYesterday.gridData"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="50"/>
          <el-table-column
            property="composition"
            label="成分">
            <template v-slot="{ row }">
              <el-input v-model="row.composition" />
            </template>
          </el-table-column>
          <el-table-column
            property="value"
            label="值">
            <template v-slot="{ row }">
              <el-input v-model="row.value" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'steelYesterday')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('steelYesterday')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>

    <!--库存参数-->
    <el-dialog
      :visible.sync="PP.dialogVisible"
      :width="'600px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="库存情况操作">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div
            class="btn-box"
            style="font-size: 18px">
            时间：{{ cDate }}
          </div>
          P浓度
        </div>
      </template>
      <el-form
        :model="PP"
        label-width="150px"
        class="demo-form-inline">
        <el-form-item label="小于0.13">
          <el-input
            v-model="PP.lessThanV"
            placeholder="小于0.13"/>
        </el-form-item>
        <el-form-item label="0.13到0.135之间">
          <el-input
            v-model="PP.betweenAndV"
            placeholder="0.13到0.135之间"/>
        </el-form-item>
        <el-form-item label="大于0.135">
          <el-input
            v-model="PP.greaterThanV"
            placeholder="大于0.135"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="saveHeat">保存</el-button>
        </div>
      </el-form>
    </el-dialog>
    <!--非计划汇总-->
    <el-dialog
      :visible.sync="unPlanedTotal.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="原辅料管理">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unPlanedTotal')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <el-dropdown
              v-if="canEditQuality"
              @command="handleProcessedCommand($event, 'importUnPlanedTotalData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handleUnPlanedTotalPreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportUnplanedTotal">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveUnPlanedTotal">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          原辅料管理
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="unPlanedTotal.gridData"
          border>
          <el-table-column
            align="center"
            type="index"
            label="序号"/>
          <el-table-column
            align="center"
            prop="classify"
            label="类别">
            <template v-slot="{ row }">
              <el-input v-model="row.category" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="blockNum"
            label="质量情况">
            <template v-slot="{ row }">
              <el-input v-model="row.qualitySituation" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'unPlanedTotal')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('unPlanedTotal')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SingleBarsChart from '@/pages/screen/qualityMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import * as _ from 'lodash'
import {
  castingMachine,
  findHeatTreatmentYieldByDate,
  findSteelOutputByDate,
  pproportionQuery,
  qmsMoltenIronCompositionQuery,
  qmsMoltenIronCompositionSave,
  qmsPPotencyScaleQuery,
  qmsPPotencyScaleSave,
  qmsQualityObjectionsQuery,
  qmsRawAuxiliaryManageQuery,
  qmsRawAuxiliaryManageSave,
  qmsRollingsteelQualityControlQuery,
  saveCorrectionGather,
  saveHeatTreatmentYield,
  saveSteelOutput
} from '@/api/screen'
import { math } from '@/lib/Math'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import PieChart from '@/pages/screen/qualityMeeting/component/pie-chart'
import lodash from 'lodash'
import PLineChart from '@/pages/screen/qualityMeeting/component/p-line-chart'
export default {
  name: 'Materials',
  components: { PLineChart, PieChart, SingleBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      steelYesterday: {
        lineBar: [],
        lineBar2: [],
        lineBarX: [],
        bar1: [],
        bar2: [],
        barX: [],
        failReason: '',
        gridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      PP: {
        bar1: [],
        lessThanV: 0,
        betweenAndV: 0,
        greaterThanV: 0,
        dialogVisible: false
      },
      unitList: ['吨数', '炉数'],
      finishList: ['是', '否'],
      unPlanedTotal: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      }
    }
  },
  computed: {
    timeStr: function() {
      return (
        '（' +
        this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('MM月DD日') +
        '）'
      )
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.$nextTick(item => {
        this.getSteal()
        this.getHeat()
        this.getUnfinished()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.getSteal()
    this.getHeat()
    this.getUnfinished()
  },
  methods: {
    handlePreview(file) {
      try {
        LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
          data = LAY_EXCEL.filterImportData(data, {
            composition: 'A',
            value: 'B'
          })
          // 去除第一行
          const sheet = data[0].Sheet1 || data[0].sheet1
          sheet.shift()
          // 表格信息
          this.steelYesterday.gridData = sheet.map(item => {
            return item
          })
          this.$message.success('解析成功！')
        })
      } catch (e) {
        this.$message.warning('解析失败！')
      }
    },
    getSteal() {
      post(castingMachine, {
        startTime: this.$moment(this.cDate)
          .subtract(30, 'day')
          .format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD')
      }).then(res => {
        this.steelYesterday.lineBar = [
          {
            name: 'P',
            data: res.data.map(item => item.P.toFixed(4))
          },
          {
            name: 'SI',
            data: res.data.map(item => item.SI.toFixed(4))
          }
        ]
        this.steelYesterday.lineBar2 = [
          {
            name: 'S',
            data: res.data.map(item => item.S.toFixed(4))
          },
          {
            name: 'As',
            data: res.data.map(item => item.ARSENIC.toFixed(4))
          }
        ]
        this.steelYesterday.lineBarX = res.data.map(item =>
          item.HMPS_ARR_DATE.substring(5, 10)
        )
      })
      return
      this.steelYesterday.gridData = []
      // this.clearViewData()
      post(qmsMoltenIronCompositionQuery, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.clearViewData()
        this.steelYesterday.gridData = res.data.map(item => {
          return {
            composition: item.composition,
            value: item.value
          }
        })
        if (!res.data.length) return
        this.steelYesterday.bar1 = res.data
          .filter(item => item['composition'] !== 'S')
          .map(item => {
            return {
              value: item.value,
              name: item['composition'],
              finished: true
            }
          })
        this.steelYesterday.bar2 = this.steelYesterday.bar1
          .map(item => {
            return {
              value: 0,
              name: 0,
              finished: true,
              show: false
            }
          })
          .concat(
            res.data.filter(item => item['composition'] === 'S').map(item => {
              return {
                value: item.value,
                name: item['composition'],
                finished: true
              }
            })
          )
        this.steelYesterday.barX = res.data.map(item => item['composition'])
        // this.formatViewData()
      })
    },
    clearViewData() {
      this.steelYesterday.bar1 = []
      this.steelYesterday.bar2 = []
      this.steelYesterday.barX = []
    },
    // 铁水
    saveSteel() {
      this.loading = true
      const params = {
        setDate: this.cDate,
        data: this.steelYesterday.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(qmsMoltenIronCompositionSave, params).then(res => {
        //
        this.loading = false
        if (res !== -1) {
          this.$message.success('保存成功！')
          this.steelYesterday.dialogVisible = false
          this.getSteal()
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    exportSteel() {
      const data = [
        {
          composition: '成分',
          value: '值'
        }
      ].concat(
        _.cloneDeep(this.steelYesterday.gridData).map(item => {
          return {
            composition: item.composition,
            value: item.value
          }
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `钢产量详情（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    // 铁水导入
    importSteelData(date) {
      this.steelYesterday.gridData = []
      post(qmsMoltenIronCompositionQuery, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.steelYesterday.gridData = res.data.map(item => {
          return {
            composition: item.composition,
            value: item.value
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // P浓度
    getHeat() {
      post(pproportionQuery, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD')
      }).then(res => {
        const lessthan013 = res.data.filter(item => item.P < 0.13).length
        const between013and0135 = res.data.filter(
          item => item.P >= 0.13 && item.P <= 0.135
        ).length
        const greaterthan0135 = res.data.filter(item => item.P > 0.135).length
        const total = lessthan013 + between013and0135 + greaterthan0135
        this.PP.bar1 = [
          {
            name: '小于0.13',
            value: lessthan013,
            percent: ((lessthan013 / total) * 100).toFixed(2)
          },
          {
            name: '0.13到0.135之间',
            value: between013and0135,
            percent: ((between013and0135 / total) * 100).toFixed(2)
          },
          {
            name: '大于0.135',
            value: greaterthan0135,
            percent: ((greaterthan0135 / total) * 100).toFixed(2)
          }
        ]
      })
      // post(qmsPPotencyScaleQuery, {
      //   setDate: this.cDate
      // }).then(res => {
      //   //
      //   this.loading = false
      //   if (res.data.length) {
      //     this.PP.lessThanV = res.data[0].lessthan013
      //     this.PP.betweenAndV = res.data[0].between013and0135
      //     this.PP.greaterThanV = res.data[0].greaterthan0135
      //
      //   }
      // })
    },
    saveHeat() {
      this.loading = true
      // 保存P
      const params = {
        setDate: this.cDate,
        data: [
          {
            setDate: this.cDate,
            lessthan013: this.PP.lessThanV,
            between013and0135: this.PP.betweenAndV,
            greaterthan0135: this.PP.greaterThanV
          }
        ]
      }
      post(qmsPPotencyScaleSave, params).then(res => {
        //
        this.loading = false
        if (res.status == 1) {
          this.$message.success('保存成功！')
          this.PP.dialogVisible = false
          this.getHeat()
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
    },
    getMergeData(rowIndex, columnIndex) {
      const matchLeftTop = this.steelYesterday.gridMerge.find(
        item => item.s.c === columnIndex && item.s.r === rowIndex
      )
      if (matchLeftTop) {
        return [
          matchLeftTop.e.r - matchLeftTop.s.r + 1,
          matchLeftTop.e.c - matchLeftTop.s.c + 1
        ]
      }
      const merged = this.steelYesterday.gridMerge.find(item => {
        return (
          item.s.c < columnIndex &&
          columnIndex <= item.e.c &&
          item.s.r < rowIndex &&
          rowIndex <= item.e.r
        )
      })
      if (merged) {
        console.log(merged)
        return [0, 0]
      }
    },
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    formatName(value, num = 6) {
      let maxLength = num //每项显示文字个数
      let valLength = value.length //X轴类目项的文字个数
      let rowN = Math.ceil(valLength / maxLength) //类目项需要换行的行数
      if (rowN > 1) {
        // 如果类目项的文字大于3,
        let ret = value.substring(0, maxLength) //每次截取的字符串
        let start = 0 //开始截取的位置
        for (let i = 1; i < rowN; i++) {
          start += maxLength
          ret += '\n' + value.substring(start, start + maxLength)
        }
        return ret
      } else {
        // return percent
        return value
      }
    },
    unPlanedTotalClass(row) {
      console.log(row)
      if (row.row.rollingPlant && row.row.rollingPlant.indexOf('汇总') !== -1) {
        return 'table-total'
      }
      return ''
    },
    // 原辅料管理
    handleUnPlanedTotalPreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          category: 'A',
          qualitySituation: 'B'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unPlanedTotal.gridData = sheet.map(item => {
          return item
        })
        this.$message.success('解析成功！')
      })
    },
    exportUnplanedTotal() {
      const data = [
        {
          category: '类别',
          qualitySituation: '质量情况'
        }
      ].concat(
        _.cloneDeep(this.unPlanedTotal.gridData).map(item => {
          return {
            category: item.category,
            qualitySituation: item.qualitySituation
          }
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `非计划改判（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    getUnfinished() {
      post(qmsRawAuxiliaryManageQuery, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.$emit('statusChange', !res.data.length)
        if (!res) return
        this.unPlanedTotal.showGridData = res.data.map(item => {
          return {
            category: item.category,
            qualitySituation: item.qualitysituation
          }
        })
        this.unPlanedTotal.gridData = lodash.cloneDeep(
          this.unPlanedTotal.showGridData
        )
      })
    },
    saveUnPlanedTotal() {
      this.loading = true
      const params = {
        setDate: this.cDate,
        data: this.unPlanedTotal.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(qmsRawAuxiliaryManageSave, params).then(res => {
        //
        this.loading = false
        if (res !== -1) {
          this.$message.success('保存成功！')
          this.unPlanedTotal.dialogVisible = false
          this.getUnfinished()
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    importUnPlanedTotalData(date) {
      post(qmsRawAuxiliaryManageQuery, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.unPlanedTotal.gridData = res.data.map(item => {
          return {
            category: item.category,
            qualitySituation: item.qualitysituation
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    font-size: 0;
    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.green {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
