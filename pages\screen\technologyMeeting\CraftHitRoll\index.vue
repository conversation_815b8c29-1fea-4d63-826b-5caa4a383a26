<template>
  <div 
    class="full-height" 
    style="overflow: hidden">
    <el-row
      :gutter="32"
      class="full-height">
      <el-col
        :span="24"
        class="full-height">
        <div class="content">
          <div
            class="content-item"
            style="height: 36%; flex: unset">
            <screen-border :title="'轧钢工序关键参数工艺命中率（昨日）'">
              <template v-slot:headerRight>
                <!--          <span-->
                <!--            v-command="'/screen/technologyMeeting/edit'"-->
                <!--            class="screen-btn"-->
                <!--            @click="pilotPlan1.dialogVisible = true">-->
                <!--            <el-icon class="el-icon-edit-outline"/>-->
                <!--            操作-->
                <!--          </span>-->
              </template>
              <div
                class="scroll-wrapper">
                <el-row
                  :gutter="20"
                  class="full-height">
                  <el-col
                    :span="5"
                    class="full-height">
                    <div class="chart-wrapper">
                      <div class="chart-tit">二阶段开轧温度</div>
                      <div
                        class="chart">
                        <gauge-chart :chart-data="chartData"/>
                      </div>
                    </div>
                  </el-col>
                  <el-col
                    :span="5"
                    class="full-height">
                    <div class="chart-wrapper">
                      <div class="chart-tit">二阶段厚度比</div>
                      <div
                        class="chart">
                        <gauge-chart :chart-data="chartData2"/>
                      </div>
                    </div>
                  </el-col>
                  <el-col
                    :span="5"
                    class="full-height">
                    <div class="chart-wrapper">
                      <div class="chart-tit">终轧温度</div>
                      <div
                        class="chart">
                        <gauge-chart :chart-data="chartData3"/>
                      </div>
                    </div>
                  </el-col>
                  <!-- 入水温度 -->
                  <el-col
                    :span="5"
                    class="full-height">
                    <div class="chart-wrapper">
                      <div class="chart-tit">入水温度</div>
                      <div
                        class="chart">
                        <gauge-chart :chart-data="chartDataFive"/>
                      </div>
                    </div>
                  </el-col>
                  <el-col
                    :span="4"
                    class="full-height">
                    <div class="chart-wrapper">
                      <div class="chart-tit">返红温度</div>
                      <div
                        class="chart">
                        <gauge-chart :chart-data="chartData4"/>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </screen-border>
          </div>
          <div class="content-hold"/>
          <div class="content-item">
            <screen-border :title="'轧钢工序关键参数工艺命中率（月度）'">
              <template v-slot:headerRight>
                <!--          <span-->
                <!--            v-command="'/screen/technologyMeeting/edit'"-->
                <!--            class="screen-btn"-->
                <!--            @click="pilotPlan1.dialogVisible = true">-->
                <!--            <el-icon class="el-icon-edit-outline"/>-->
                <!--            操作-->
                <!--          </span>-->
              </template>
              <el-row
                :gutter="32"
                class="full-height">
                <el-col
                  :span="24"
                  class="full-height">
                  <el-row
                    :gutter="20"
                    class="full-height">
                    <el-col
                      :span="12"
                      class="full-height">
                      <div class="content">
                        <div
                          class="content-item">
                          <div class="chart-wrapper">
                            <div class="chart-tit">二阶段开轧温度</div>
                            <div class="chart">
                              <line-chart
                                :show-legend="true"
                                :chart-data="lineData1.data1"
                                :x-data="lineData1.dataX"/>
                            </div>
                          </div>
                        </div>
                        <div
                          style="height: 6px"
                          class="content-hold"/>
                        <div
                          class="content-item">
                          <div class="chart-wrapper">
                            <div class="chart-tit">二阶段厚度比</div>
                            <div class="chart">
                              <line-chart
                                :show-legend="true"
                                :chart-data="lineData2.data1"
                                :x-data="lineData2.dataX"/>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-col>
                    <el-col
                      :span="12"
                      class="full-height">
                      <div class="content">
                        <div
                          class="content-item">
                          <div class="chart-wrapper">
                            <div class="chart-tit">终轧温度</div>
                            <div class="chart">
                              <line-chart
                                :show-legend="true"
                                :chart-data="lineData3.data1"
                                :x-data="lineData3.dataX"/>
                            </div>
                          </div>
                        </div>
                        <div
                          style="height: 0px"
                          class="content-hold"/>
                        <div
                          class="content-item">
                          <div class="chart-wrapper">
                            <div class="chart-tit">返红温度</div>
                            <div class="chart">
                              <line-chart
                                :show-legend="true"
                                :chart-data="lineData4.data1"
                                :x-data="lineData4.dataX"/>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </screen-border>
          </div>
        </div>
        <el-dialog
          :visible.sync="pieChart.dialogVisible"
          :width="'1200px'"
          :close-on-click-modal="false"
          class="screen-dialog"
          title="">
          <template v-slot:title>
            <div class="custom-dialog-title">
              成分不合格详情
            </div>
          </template>
          <el-table
            v-loading="loading"
            :data="pieChart.flawList"
            class="center-table"
            border>
            <el-table-column
              label="物料号"
              property="matId"/>
            <el-table-column
              label="钢种"
              property="steelGrade"/>
            <el-table-column
              label="不合格元素及含量"
              property="chemCd"/>
            <el-table-column
              label="规格"
              property="thk"/>
            <el-table-column
              label="处置"
              property="opinion"/>
          </el-table>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import { post } from '@/lib/Util'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  checklistBySetDate,
  checklistDelete,
  peakTargetHitRate,
  rollingParameters,
  steelMakingCompositionMax,
  steelMakingPhr,
  steelRollingC1C3,
  steelRollingRate,
  steelEntry
} from '@/api/screenTechnolagy'
import moment from 'moment'
import BarsChart from '@/pages/screen/technologyMeeting/component/bars-chart'
import { getChemQualified, getChemQualifiedDetail } from '@/api/screen'
import PieRateChart from '@/pages/screen/qualityMeeting/component/pie-rate-chart'
import BarsStackChart from '@/pages/screen/technologyMeeting/component/bars-stack-chart'
import GaugeChart from '@/pages/screen/technologyMeeting/component/gauge-chart'
import LineChart from '@/pages/screen/technologyMeeting/component/line-chart'

export default {
  name: 'CraftHitRoll',
  components: {
    LineChart,
    GaugeChart,
    BarsStackChart,
    PieRateChart,
    BarsChart,
    ScreenBorder
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      pieChart: {
        total: '',
        RATE: 0, //
        NUM: 0, //
        color: '#19be6b',
        dateType1: 0,
        flawList: [],
        dialogVisible: false
      },
      chartData: [
        {
          value: 0,
          name: 'C1',
          title: {
            offsetCenter: ['-23px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 0,
          name: 'C2',
          title: {
            offsetCenter: ['-23px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        },
        {
          value: 0,
          name: 'C3',
          title: {
            offsetCenter: ['-23px', '30%']
          },
          detail: {
            offsetCenter: ['13px', '30%']
          },
          itemStyle: {
            color: '#51DF81'
          }
        }
      ],
      chartData2: [
        {
          value: 0,
          name: 'C1',
          title: {
            offsetCenter: ['-23px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 0,
          name: 'C2',
          title: {
            offsetCenter: ['-23px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        },
        {
          value: 0,
          name: 'C3',
          title: {
            offsetCenter: ['-23px', '30%']
          },
          detail: {
            offsetCenter: ['13px', '30%']
          },
          itemStyle: {
            color: '#51DF81'
          }
        }
      ],
      chartData3: [
        {
          value: 0,
          name: 'C1',
          title: {
            offsetCenter: ['-23px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 0,
          name: 'C2',
          title: {
            offsetCenter: ['-23px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        },
        {
          value: 0,
          name: 'C3',
          title: {
            offsetCenter: ['-23px', '30%']
          },
          detail: {
            offsetCenter: ['13px', '30%']
          },
          itemStyle: {
            color: '#51DF81'
          }
        }
      ],
      chartData4: [
        {
          value: 0,
          name: 'C1',
          title: {
            offsetCenter: ['-23px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 0,
          name: 'C2',
          title: {
            offsetCenter: ['-23px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        },
        {
          value: 0,
          name: 'C3',
          title: {
            offsetCenter: ['-23px', '30%']
          },
          detail: {
            offsetCenter: ['13px', '30%']
          },
          itemStyle: {
            color: '#51DF81'
          }
        }
      ],
      chartDataFive: [
        {
          value: 0,
          name: 'C1',
          title: {
            offsetCenter: ['-25px', '-30%']
          },
          detail: {
            offsetCenter: ['20px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: 0,
          name: 'C2',
          title: {
            offsetCenter: ['-25px', '0']
          },
          detail: {
            offsetCenter: ['20px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        },
        {
          value: 0,
          name: 'C3',
          title: {
            offsetCenter: ['-25px', '30%']
          },
          detail: {
            offsetCenter: ['20px', '30%']
          },
          itemStyle: {
            color: '#51DF81'
          }
        }
      ],
      chartData5: {
        bar1: [
          {
            name: 'C1',
            data: []
          },
          {
            name: 'C2',
            data: []
          },
          {
            name: 'C3',
            data: []
          }
        ],
        bar1X: ['二阶段', '终轧', '返红']
      },
      lineData1: {
        dataX: [],
        data1: [
          {
            name: 'C1',
            data: []
          },
          {
            name: 'C2',
            data: []
          },
          {
            name: 'C3',
            data: []
          }
        ]
      },
      lineData2: {
        dataX: [],
        data1: [
          {
            name: 'C1',
            data: []
          },
          {
            name: 'C2',
            data: []
          },
          {
            name: 'C3',
            data: []
          }
        ]
      },
      lineData3: {
        dataX: [],
        data1: [
          {
            name: 'C1',
            data: []
          },
          {
            name: 'C2',
            data: []
          },
          {
            name: 'C3',
            data: []
          }
        ]
      },
      lineData4: {
        dataX: [],
        data1: [
          {
            name: 'C1',
            data: []
          },
          {
            name: 'C2',
            data: []
          },
          {
            name: 'C3',
            data: []
          }
        ]
      }
    }
  },
  computed: {
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'day')
        .format('yyyy-MM-DD')
    },
    nextDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(-1, 'day')
        .format('yyyy-MM-dd')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.loadData()
    }
  },
  destroyed() {},
  created() {
    this.cDate = this.selectDate
    this.loadData()
  },
  mounted() {},
  methods: {
    loadData() {
      this.getSteelRollingRate()
      this.getSteelRollingLine()

      // 获取入水温度
      this.getSteelEntry()
    },

    //获取入水温度
    async getSteelEntry() {
      const dateStr = moment(this.prevDate).format('yyyy-MM-DD')
      const C1C3Data = await post(steelEntry, {
        startTime: dateStr
      })

      this.chartDataFive = [
        {
          value: C1C3Data['C1入水温度'],
          name: 'C1',
          title: {
            offsetCenter: ['-25px', '-30%']
          },
          detail: {
            offsetCenter: ['20px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: C1C3Data['C2入水温度'],
          name: 'C2',
          title: {
            offsetCenter: ['-25px', '0']
          },
          detail: {
            offsetCenter: ['20px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        },
        {
          value: C1C3Data['C3入水温度'],
          name: 'C3',
          title: {
            offsetCenter: ['-25px', '30%']
          },
          detail: {
            offsetCenter: ['20px', '30%']
          },
          itemStyle: {
            color: '#51DF81'
          }
        }
      ]
    },
    // 获取轧钢工艺信息
    async getSteelRollingRate() {
      const dateStr = moment(this.prevDate).format('yyyyMMDD')
      const C2Data = await post(steelRollingRate, {
        beforeTime: dateStr,
        afterTime: dateStr
      })
      console.log(C2Data)
      const C1C3Data = await post(steelRollingC1C3, {
        startTime: this.prevDate,
        endTime: this.prevDate
      })
      this.chartData = [
        {
          value: C1C3Data['C1二阶段开轧温度'][0]['target_hit_rate'],
          name: 'C1',
          title: {
            offsetCenter: ['-23px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: C2Data['二阶段开轧温度'][0]['RATE'],
          name: 'C2',
          title: {
            offsetCenter: ['-23px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        },
        {
          value: C1C3Data['C3二阶段开轧温度'][0]['target_hit_rate'],
          name: 'C3',
          title: {
            offsetCenter: ['-23px', '30%']
          },
          detail: {
            offsetCenter: ['13px', '30%']
          },
          itemStyle: {
            color: '#51DF81'
          }
        }
      ]
      this.chartData2 = [
        {
          value: C1C3Data['C1二阶段厚度比'][0]['target_hit_rate'],
          name: 'C1',
          title: {
            offsetCenter: ['-23px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: C2Data['二阶段开轧厚度比'][0]['RATE'],
          name: 'C2',
          title: {
            offsetCenter: ['-23px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        },
        {
          value: C1C3Data['C3二阶段厚度比'][0]['target_hit_rate'],
          name: 'C3',
          title: {
            offsetCenter: ['-23px', '30%']
          },
          detail: {
            offsetCenter: ['13px', '30%']
          },
          itemStyle: {
            color: '#51DF81'
          }
        }
      ]
      this.chartData3 = [
        {
          value: C1C3Data['C1终轧温度'][0]['target_hit_rate'],
          name: 'C1',
          title: {
            offsetCenter: ['-23px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: C2Data['终轧温度'][0]['RATE'],
          name: 'C2',
          title: {
            offsetCenter: ['-23px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        },
        {
          value: C1C3Data['C3终轧温度'][0]['target_hit_rate'],
          name: 'C3',
          title: {
            offsetCenter: ['-23px', '30%']
          },
          detail: {
            offsetCenter: ['13px', '30%']
          },
          itemStyle: {
            color: '#51DF81'
          }
        }
      ]
      this.chartData4 = [
        {
          value: C1C3Data['C1返红温度'][0]['target_hit_rate'],
          name: 'C1',
          title: {
            offsetCenter: ['-23px', '-30%']
          },
          detail: {
            offsetCenter: ['13px', '-30%']
          },
          itemStyle: {
            color: '#2772F0'
          }
        },
        {
          value: C2Data['返红温度'][0]['RATE'],
          name: 'C2',
          title: {
            offsetCenter: ['-23px', '0']
          },
          detail: {
            offsetCenter: ['13px', '0']
          },
          itemStyle: {
            color: '#F5B544'
          }
        },
        {
          value: C1C3Data['C3返红温度'][0]['target_hit_rate'],
          name: 'C3',
          title: {
            offsetCenter: ['-23px', '30%']
          },
          detail: {
            offsetCenter: ['13px', '30%']
          },
          itemStyle: {
            color: '#51DF81'
          }
        }
      ]
    },

    async getSteelRollingLine() {
      const dateStr = moment(this.prevDate).format('yyyyMMDD')
      const C1Data = await post(steelRollingRate, {
        beforeTime: moment(this.prevDate)
          .startOf('month')
          .format('yyyyMMDD'),
        afterTime: dateStr
      })
      const C1C3Data = await post(steelRollingC1C3, {
        startTime: moment(this.prevDate)
          .startOf('month')
          .format('yyyy-MM-DD'),
        endTime: moment(this.prevDate).format('yyyy-MM-DD')
      })
      this.lineData1 = {
        dataX: C1Data['二阶段开轧温度'].map(item =>
          item.PROD_DATE.substring(item.PROD_DATE.length - 2)
        ),
        data1: [
          {
            name: 'C1',
            data: C1C3Data['C1二阶段开轧温度']
              .reverse()
              .map(item => item.target_hit_rate)
          },
          {
            name: 'C2',
            data: C1Data['二阶段开轧温度'].map(item => item.RATE)
          },
          {
            name: 'C3',
            data: C1C3Data['C3二阶段开轧温度']
              .reverse()
              .map(item => item.target_hit_rate)
          }
        ]
      }
      this.lineData2 = {
        dataX: C1Data['二阶段开轧厚度比'].map(item =>
          item.PROD_DATE.substring(item.PROD_DATE.length - 2)
        ),
        data1: [
          {
            name: 'C1',
            data: C1C3Data['C1二阶段厚度比']
              .reverse()
              .map(item => item.target_hit_rate)
          },
          {
            name: 'C2',
            data: C1Data['二阶段开轧厚度比'].map(item => item.RATE)
          },
          {
            name: 'C3',
            data: C1C3Data['C3二阶段厚度比']
              .reverse()
              .map(item => item.target_hit_rate)
          }
        ]
      }
      this.lineData3 = {
        dataX: C1Data['终轧温度'].map(item =>
          item.PROD_DATE.substring(item.PROD_DATE.length - 2)
        ),
        data1: [
          {
            name: 'C1',
            data: C1C3Data['C1终轧温度']
              .reverse()
              .map(item => item.target_hit_rate)
          },
          {
            name: 'C2',
            data: C1Data['终轧温度'].map(item => item.RATE)
          },
          {
            name: 'C3',
            data: C1C3Data['C3终轧温度']
              .reverse()
              .map(item => item.target_hit_rate)
          }
        ]
      }
      this.lineData4 = {
        dataX: C1Data['返红温度'].map(item =>
          item.PROD_DATE.substring(item.PROD_DATE.length - 2)
        ),
        data1: [
          {
            name: 'C1',
            data: C1C3Data['C1返红温度']
              .reverse()
              .map(item => item.target_hit_rate)
          },
          {
            name: 'C2',
            data: C1Data['返红温度'].map(item => item.RATE)
          },
          {
            name: 'C3',
            data: C1C3Data['C3返红温度']
              .reverse()
              .map(item => item.target_hit_rate)
          }
        ]
      }
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.edit-btn {
  margin: 0 3px;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .chart-tit {
    font-size: 16px;
    font-weight: bolder;
    color: #ffffff;
    line-height: 20px;
    margin: 10px 0 5px;
    &:before {
      content: '1';
      color: #ffffff;
      background: #ffffff;
      width: 6px;
      height: 100%;
      margin-right: 4px;
    }
  }
  .chart {
    flex: 1;
    height: 0;
  }
}
.dialog-item-tit {
  font-size: 18px;
  color: #dcdcdc;
  margin-bottom: 8px;
  margin-top: 15px;
}
</style>
