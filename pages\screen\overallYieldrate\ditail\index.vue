<template>
  <div class="content">
    <!-- <div class="content-item">

      <custom-table2
        :title="'协调事项表'"
        :setting="tableObj2.setting"
        :url-list="tableObj2.url.list"
        :url-save="tableObj2.url.save"
        :select-date="selectDate"
        :table-class="'big-table'"/>
    </div>
    <div class="content-hold"/> -->
    <div class="content-item">

      <custom-table-merge
        :title="'收得率详情表'"
        :setting="tableObj1.setting"
        :url-list="tableObj1.url.list"
        :url-save="tableObj1.url.save"
        :url-steel="tableObj1.url.steel"
        :select-date="selectDate"
        :table-class="'big-table'"/>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import * as _ from 'lodash'
import {
  FullProcessYieldFindDetailsDate,
  FindSteelGrade,
  TripleOrderTrackSave
} from '@/api/screen'
import moment from 'moment'
import CustomTableMerge from '@/pages/screen/overallYieldrate/component/custom-table-merge'

export default {
  name: 'TripleOrderTrack',
  components: { CustomTableMerge, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      editIndex: null,
      tableObj1: {
        url: {
          save: '',
          list: FullProcessYieldFindDetailsDate,
          steel: FindSteelGrade
        },
        setting: [
          // {
          //   keyQuery: 'index',
          //   keySave: 'index',
          //   label: '序号',
          //   type: 'index',
          //   fixed: true
          // },
          {
            keyQuery: 'heatNo',
            keySave: 'heatNo',
            label: '炉号',
            width: 100
          },
          {
            keyQuery: 'stlgrd',
            keySave: 'stlgrd',
            label: '坯料钢种',
            width: 200
          },
          {
            keyQuery: 'wgtSum',
            keySave: 'wgtSum',
            label: '坯料重量',
            width: 200
          },
          {
            keyQuery: 'slabNo',
            keySave: 'slabNo',
            label: '子坯料号',
            width: 200
          },
          {
            keyQuery: 'stlgrdOrd',
            keySave: 'stlgrdOrd',
            label: '钢种',
            width: 250
          },
          {
            keyQuery: 'stlgrdOrdDetail',
            keySave: 'stlgrdOrdDetail',
            label: '替代钢种',
            width: 200
          },
          {
            keyQuery: 'ordNo',
            keySave: 'ordNo',
            label: '订单号',
            width: 200
          },
          {
            keyQuery: 'overFl',
            keySave: 'overFl',
            label: '坯料种类',
            width: 200
          },
          {
            keyQuery: 'wgt',
            keySave: 'wgt',
            label: '子坯料重量',
            width: 200
          },
          {
            keyQuery: 'prodDate',
            keySave: 'prodDate',
            label: '母坯生产时间',
            width: 200
          },
          {
            keyQuery: 'outPlt',
            keySave: 'outPlt',
            label: '流向',
            width: 200
          },
          {
            keyQuery: 'estComment',
            keySave: 'estComment',
            label: '坯料判废原因',
            width: 250
          },
          {
            keyQuery: 'thk',
            keySave: 'thk',
            label: '入炉坯料厚度',
            width: 200
          },
          {
            keyQuery: 'stlgrdFlag',
            keySave: 'stlgrdFlag',
            label: '是否替代轧制',
            width: 200
          },
          {
            keyQuery: 'replaceReason1',
            keySave: 'replaceReason1',
            label: '替代原因',
            width: 200
          },
          {
            keyQuery: 'cadManaNo',
            keySave: 'cadManaNo',
            label: '原因代码',
            width: 250
          },
          {
            keyQuery: 'cadManaNo1',
            keySave: 'cadManaNo1',
            label: 'CAD代码',
            width: 200
          },
          {
            keyQuery: 'cadComment',
            keySave: 'cadComment',
            label: 'CAD锁定详称',
            width: 250
          },
          {
            keyQuery: 'estCd',
            keySave: 'estCd',
            label: '处理',
            width: 250
          },
          {
            keyQuery: 'cdShortName',
            keySave: 'cdShortName',
            label: '处理名称',
            width: 250
          },
          {
            keyQuery: 'estComment1',
            keySave: 'estComment1',
            label: '处理详细内容',
            width: 250
          },
          {
            keyQuery: 'plateNo',
            keySave: 'plateNo',
            label: '钢板号',
            width: 250
          },
          {
            keyQuery: 'millStdspec',
            keySave: 'millStdspec',
            label: '轧制标准',
            width: 250
          },
          {
            keyQuery: 'stdspecStlgrd',
            keySave: 'stdspecStlgrd',
            label: '标准钢种-轧制标准',
            width: 250
          },
          {
            keyQuery: 'aplyStdspec',
            keySave: 'aplyStdspec',
            label: '标准号',
            width: 250
          },
          {
            keyQuery: 'stdspecStamp',
            keySave: 'stdspecStamp',
            label: '标准钢种-轧钢实绩',
            width: 250
          },
          {
            keyQuery: 'stlgrdOrdSlab',
            keySave: 'stlgrdOrdSlab',
            label: '原始坯料钢种',
            width: 250
          },
          {
            keyQuery: 'plt',
            keySave: 'plt',
            label: '生产厂',
            width: 250
          },
          {
            keyQuery: 'thk1',
            keySave: 'thk1',
            label: '订单厚度',
            width: 250
          },
          {
            keyQuery: 'wid1',
            keySave: 'wid1',
            label: '订单宽度',
            width: 250
          },
          {
            keyQuery: 'len1',
            keySave: 'len1',
            label: '订单长度',
            width: 250
          },
          {
            keyQuery: 'wgt1',
            keySave: 'wgt1',
            label: '实绩钢板重量',
            width: 250
          },
          {
            keyQuery: 'millOccrDate',
            keySave: 'millOccrDate',
            label: '轧制时间',
            width: 250
          },
          {
            keyQuery: 'cutEndDate',
            keySave: 'cutEndDate',
            label: '剪切时间',
            width: 250
          },
          {
            keyQuery: 'prodGrd',
            keySave: 'prodGrd',
            label: '产品等级',
            width: 250
          },
          {
            keyQuery: 'corrtctDefect',
            keySave: 'corrtctDefect',
            label: '改判缺陷',
            width: 250
          },
          {
            keyQuery: 'procCd',
            keySave: 'procCd',
            label: '钢板进程状态',
            width: 250
          },
          {
            keyQuery: 'orgOrdNo',
            keySave: 'orgOrdNo',
            label: '原始订单号',
            width: 250
          },
          {
            keyQuery: 'saleWay',
            keySave: 'saleWay',
            label: '销售方式',
            width: 250
          },
          {
            keyQuery: 'ordNo1',
            keySave: 'ordNo1',
            label: '最终订单号',
            width: 250
          },
          {
            keyQuery: 'custName',
            keySave: 'custName',
            label: '客户名称',
            width: 250
          },
          {
            keyQuery: 'slabWgt',
            keySave: 'slabWgt',
            label: '原料重量',
            width: 200
          },
          {
            keyQuery: 'reasonComment3',
            keySave: 'reasonComment3',
            label: '锁定详称',
            width: 250
          },
          {
            keyQuery: 'subClass',
            keySave: 'subClass',
            label: '产品分类',
            width: 250
          },
          {
            keyQuery: 'isSameStd',
            keySave: 'isSameStd',
            label: '材是否原钢种',
            width: 250
          },
          {
            keyQuery: 'replaceReason',
            keySave: 'replaceReason',
            label: '余材原因',
            width: 250
          },
          {
            keyQuery: 'isReplace',
            keySave: 'isReplace',
            label: '是否余材替代',
            width: 250
          }
        ]
      }
    }
  },
  computed: {
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'month')
        .format('yyyy-MM')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      // this.getpilotPlan()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    calculate() {
      // this.pilotPlan1.maxHeight = this.$refs.table1.offsetHeight
    },
    totalClass(row) {
      if (row.row.serialNumber && row.row.serialNumber.trim() === '合计') {
        return 'table-total'
      }
      return ''
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.edit-btn {
  margin: 0 3px;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.dialog-item-tit {
  font-size: 18px;
  color: #dcdcdc;
  margin-bottom: 8px;
  margin-top: 15px;
}
</style>
