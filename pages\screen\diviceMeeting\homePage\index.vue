<template>
  <div class="content">
    <div class="content-item">
      <el-row 
        :gutter="32"
        class="full-height">
        <el-col 
          :span="8"
          class="full-height">
          <screen-border :title="'维修费用（上月）'">
            <div class="chart-wrapper">
              <div class="chart">
                <!-- <cost-chart
                  :show-legend="false"
                  :chart-data="cost"
                  :bar-width = "50"
                  :unit="'元/吨'"
                  :tooltipbg="true"
                  :x-data="['第一炼钢厂', '中板厂', '宽厚板厂', '中厚板卷厂', '金石材料厂', '金瑞智能工厂']"
                /> -->
                <FoldedColumnChart 
                  :chart-data="cost"
                  :color="['#2772F0','#F5B544']"
                  :bar-width="40"
                  :unit="'万元'"
                  :unit1="'元'"
                  :x-data="costX" />
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col 
          :span="8"
          class="full-height">
          <screen-border :title="'故障停时频次（本月）'">
            <div class="chart-wrapper">
              <div class="chart">
                <!-- <line-chart
                  :show-legend="true"
                  :chart-data="analysis"
                  :unit="'小时'"
                  :color="['#D45454']"
                  :x-data="['5-1', '5-2', '5-3', '5-4', '5-5', '5-6', '5-7']"
                /> -->
                <FoldedColumnChart 
                  :show-legend="false"
                  :chart-data="analysis"
                  :color="['#D45454','#F5B544']"
                  :bar-width="40"
                  :unit="'min'"
                  :unit1="'频次'"
                  :x-data="StopTime"
                  @selected="getStopTimeDetail($event)" />
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col 
          :span="8"
          class="full-height">
          <screen-border :title="'隐患未处理数'">
            <div class="chart-wrapper">
              <div class="chart">
                <FoldedColumnChart 
                  :show-legend="false"
                  :chart-data="hiddenDanger"
                  :color="['#61a4e4','#ffa958','#f56c6c']"
                  :unit="'数量'"
                  :bar-width="10"
                  :x-data="hiddenDangerX"
                  @selected="getHiddenDangerDetail($event)" />
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold" />
    <div class="content-item">
      <el-row 
        :gutter="32"
        class="full-height">
        <el-col 
          :span="8"
          class="full-height">
          <screen-border :title="'备件库存'">
            <div class="chart-wrapper">
              <div class="chart">
                <!-- <waterfall-chart
                  :show-toolbox="false"
                  :show-legend="false"
                  :chart-data="overhaul"
                  :chart-data2="overhaul2"
                  :chart-len="overhaul2Len"
                  :bar-width = "40"
                  :min="overhaulMin"
                  :max="overhaulMax"
                  :x-data="overhaulX"
                /> -->
                <FoldedColumnChart 
                  :show-legend="false"
                  :chart-data="hiddenDanger2"
                  :color="['#61a4e4','#ffa958','#f56c6c']"
                  :unit="'万元'"
                  :bar-width="10"
                  :x-data="hiddenDangerX2"
                />
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col 
          :span="16"
          class="full-height">
          <screen-border :title="'检修计划'">
            <div
              class="planTable"
            >
              <!--      <div
        id="progressChart"
        ref="progressChart"></div>-->
              <table style="border:solid 1px red;border-collapse: collapse;height:100px">
                <tr class="tr-hread">
                  <td style="border: solid 1px rgb(207, 211, 226);height: 40px;width: 150px;margin: 0px;padding: 0px;">生产厂</td>
                  <td style="border: solid 1px rgb(207, 211, 226);height: 40px;width: 150px;margin: 0px;padding: 0px;">产线</td>
                  <td
                    v-for="(item, index) in datesList"
                    :key="index+'step0'">{{ item }}</td>
                </tr>
                <tr
                  v-for="(item, index) in planTableJson['第一炼钢厂']"
                  :key="index+'step1'"
                >
                  <td
                    v-if="index == 0"
                    :rowspan="planTableJson['第一炼钢厂'].length"
                    class="centered-text">{{ item.prcLine }}</td>
                  <td
                    class="centered-text">{{ item.checkArea }}</td>
                  <td
                    v-for="(ite, ind) in datesList"
                    :key="ind+'step0'">
                    <div
                      v-for="(num, inx) in item.plan"
                      :key="inx + 'step2'"
                      class="box-content">
                      <div
                        v-if="ite == num.day + '日'"
                        :style="num.divStyle"
                        class="box-progress tooltip">
                        <span class="tooltiptext">{{ item.prcLine + '-' + item.checkArea }}<br>{{ '月检修 检修' + num.longtime + 'h' }}<br>开始时时间:{{ num.checkStartDate }}<br>结束时时间:{{ num.checkEndDate }}</span>
                      </div>
                      <div
                        v-if="ite == num.day + '日'"
                        class="box-text">{{ ite }}检修{{ num.longtime }}h</div>
                    </div>
                  </td>
                </tr>
                <tr
                  v-for="(item, index) in planTableJson['中板厂']"
                  :key="index+'step3'"
                >
                  <td
                    v-if="index == 0"
                    :rowspan="planTableJson['中板厂'].length"
                    class="centered-text">{{ item.prcLine }}</td>
                  <td
                    class="centered-text">{{ item.checkArea }}</td>
                  <td
                    v-for="(ite, ind) in datesList"
                    :key="ind+'step4'">
                    <div
                      v-for="(num, inx) in item.plan"
                      :key="inx + 'step13'"
                      class="box-content">
                      <div
                        v-if="ite == num.day + '日'"
                        :style="num.divStyle"
                        class="box-progress tooltip">
                        <span class="tooltiptext">{{ item.prcLine + '-' + item.checkArea }}<br>{{ '月检修 检修' + num.longtime + 'h' }}<br>开始时时间:{{ num.checkStartDate }}<br>结束时时间:{{ num.checkEndDate }}</span>
                      </div>
                      <div
                        v-if="ite == num.day + '日'"
                        class="box-text">{{ ite }}检修{{ num.longtime }}h</div>
                    </div>
                  </td>
                </tr>
                <tr
                  v-for="(item, index) in planTableJson['中厚板卷厂']"
                  :key="index+'step5'"
                >
                  <td
                    v-if="index == 0"
                    :rowspan="planTableJson['中厚板卷厂'].length"
                    class="centered-text">{{ item.prcLine }}</td>
                  <td
                    class="centered-text">{{ item.checkArea }}</td>
                  <td
                    v-for="(ite, ind) in datesList"
                    :key="ind+'step6'">
                    <div
                      v-for="(num, inx) in item.plan"
                      :key="inx + 'step14'"
                      class="box-content">
                      <div
                        v-if="ite == num.day + '日'"
                        :style="num.divStyle"
                        class="box-progress tooltip">
                        <span class="tooltiptext">{{ item.prcLine + '-' + item.checkArea }}<br>{{ '月检修 检修' + num.longtime + 'h' }}<br>开始时时间:{{ num.checkStartDate }}<br>结束时时间:{{ num.checkEndDate }}</span>
                      </div>
                      <div
                        v-if="ite == num.day + '日'"
                        class="box-text">{{ ite }}检修{{ num.longtime }}h</div>
                    </div>
                  </td>
                </tr>
                <tr
                  v-for="(item, index) in planTableJson['宽厚板厂']"
                  :key="index+'step7'"
                >
                  <td
                    v-if="index == 0"
                    :rowspan="planTableJson['宽厚板厂'].length"
                    class="centered-text">{{ item.prcLine }}</td>
                  <td
                    class="centered-text">{{ item.checkArea }}</td>
                  <td
                    v-for="(ite, ind) in datesList"
                    :key="ind+'step8'">
                    <div
                      v-for="(num, inx) in item.plan"
                      :key="inx + 'step15'"
                      class="box-content">
                      <div
                        v-if="ite == num.day + '日'"
                        :style="num.divStyle"
                        class="box-progress tooltip">
                        <span class="tooltiptext">{{ item.prcLine + '-' + item.checkArea }}<br>{{ '月检修 检修' + num.longtime + 'h' }}<br>开始时时间:{{ num.checkStartDate }}<br>结束时时间:{{ num.checkEndDate }}</span>
                      </div>
                      <div
                        v-if="ite == num.day + '日'"
                        class="box-text">{{ ite }}检修{{ num.longtime }}h</div>
                    </div>
                  </td>
                </tr>
                <tr
                  v-for="(item, index) in planTableJson['金石材料厂']"
                  :key="index+'step9'"
                >
                  <td
                    v-if="index == 0"
                    :rowspan="planTableJson['金石材料厂'].length"
                    class="centered-text">{{ item.prcLine }}</td>
                  <td
                    class="centered-text">{{ item.checkArea }}</td>
                  <td
                    v-for="(ite, ind) in datesList"
                    :key="ind+'step10'">
                    <div
                      v-for="(num, inx) in item.plan"
                      :key="inx + 'step16'"
                      class="box-content">
                      <div
                        v-if="ite == num.day + '日'"
                        :style="num.divStyle"
                        class="box-progress tooltip">
                        <span class="tooltiptext">{{ item.prcLine + '-' + item.checkArea }}<br>{{ '月检修 检修' + num.longtime + 'h' }}<br>开始时时间:{{ num.checkStartDate }}<br>结束时时间:{{ num.checkEndDate }}</span>
                      </div>
                      <div
                        v-if="ite == num.day + '日'"
                        class="box-text">{{ ite }}检修{{ num.longtime }}h</div>
                    </div>
                  </td>
                </tr>
                <tr
                  v-for="(item, index) in planTableJson['金润智能工厂']"
                  :key="index+'step11'"
                >
                  <td
                    v-if="index == 0"
                    :rowspan="planTableJson['金润智能工厂'].length"
                    class="centered-text">{{ item.prcLine }}</td>
                  <td
                    class="centered-text">{{ item.checkArea }}</td>
                  <td
                    v-for="(ite, ind) in datesList"
                    :key="ind+'step12'">
                    <div
                      v-for="(num, inx) in item.plan"
                      :key="inx + 'step16'"
                      class="box-content">
                      <div
                        v-if="ite == num.day + '日'"
                        :style="num.divStyle"
                        class="box-progress tooltip">
                        <span class="tooltiptext">{{ item.prcLine + '-' + item.checkArea }}<br>{{ '月检修 检修' + num.longtime + 'h' }}<br>开始时时间:{{ num.checkStartDate }}<br>结束时时间:{{ num.checkEndDate }}</span>
                      </div>
                      <div
                        v-if="ite == num.day + '日'"
                        class="box-text">{{ ite }}检修{{ num.longtime }}h</div>
                    </div>
                  </td>
                </tr>
                <!--          <tr>
            <td></td>
            <td></td>
            <td>
              <div class="box-content">
                <div
                  class="box-progress"
                  style="width:10px"></div>
              </div>
            </td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td></td>
            <td></td>
            <td>
              <div class="box-content">
                <div
                  class="box-progress"
                  style="background:yellow;width:56px;left:20px"></div>
                <div class="box-text">检修28h</div>
              </div>
            </td>
            <td></td>
            <td></td>
          </tr>-->
              </table>
            </div>
            <el-table
              v-if="false"
              id="tableExcel"
              ref="multipleTable"
              :data="tableData"
              :height="'calc(100vh - 100px)'"
              :row-key="getRowKey"
              :span-method="objectSpanMethod"
              :default-sort = "{prop: 'checkStartDate', order: 'descending'}"
              border
              style="width: 100%;"
              element-loading-text="拼命加载中..."
              element-loading-spinner="el-icon-loading"
              tooltip-effect="dark">
              <el-table-column
                v-for="item in fields"
                :key="item.key"
                :prop="item.key"
                :label="item.label"
                :width="item.width"/>
                <!--        <el-table-column
          align="center"
          label="单位"
          show-overflow-tooltip
          prop="prcLine"
          width="180">
        </el-table-column>
        <el-table-column
          align="center"
          label="检修区域"
          show-overflow-tooltip
          prop="checkArea"
          width="180">
        </el-table-column>
        <el-table-column
          align="center"
          prop="checkDevice"
          show-overflow-tooltip
          label="检修次数"
          width="160">
        </el-table-column>
        <el-table-column
          align="center"
          prop="checkStartDate"
          label="检修日期及时长"
          show-overflow-tooltip
          width="">
        </el-table-column>-->
            </el-table>
  
            <div
              class="planTable"
              style="margin-top:10px;padding-top:30px">

              <!-- <table style="border:solid 1px rgb(207, 211, 226);border-collapse: collapse;height:100px">
                <tr class="tr-hread">
                  <td style="border: solid 1px rgb(207, 211, 226);height: 18px;width: 150px;margin: 0px;padding: 0px;">生产厂</td>
                  <td style="border: solid 1px rgb(207, 211, 226);height: 18px;width: 150px;margin: 0px;padding: 0px;">产线</td>
                  <td
                    v-for="(item, index) in datesList"
                    :key="index+'step0'">{{ item }}</td>
                </tr>
                <tr
                  v-for="(item, index) in planTableJson['第一炼钢厂']"
                  :key="index+'step1'"
                >
                  <td
                    v-if="index == 0"
                    :rowspan="planTableJson['第一炼钢厂'].length"
                    class="centered-text">{{ item.prcLine }}</td>
                  <td
                    class="centered-text">{{ item.checkArea }}</td>
                  <td
                    v-for="(ite, ind) in datesList"
                    :key="ind+'step0'">
                    <div
                      v-for="(num, inx) in item.plan"
                      :key="inx + 'step2'"
                      class="box-content">
                      <div
                        v-if="ite == num.day + '日'"
                        :style="num.divStyle"
                        class="box-progress tooltip">
                        <span class="tooltiptext">{{ item.prcLine + '-' + item.checkArea }}<br>{{ '月检修 检修' + num.longtime + 'h' }}<br>开始时时间:{{ num.checkStartDate }}<br>结束时时间:{{ num.checkEndDate }}</span>
                      </div>
                      <div
                        v-if="ite == num.day + '日'"
                        class="box-text">{{ ite }}检修{{ num.longtime }}h</div>
                    </div>
                  </td>
                </tr>
                <tr
                  v-for="(item, index) in planTableJson['中板厂']"
                  :key="index+'step3'"
                >
                  <td
                    v-if="index == 0"
                    :rowspan="planTableJson['中板厂'].length"
                    class="centered-text">{{ item.prcLine }}</td>
                  <td
                    class="centered-text">{{ item.checkArea }}</td>
                  <td
                    v-for="(ite, ind) in datesList"
                    :key="ind+'step4'">
                    <div
                      v-for="(num, inx) in item.plan"
                      :key="inx + 'step13'"
                      class="box-content">
                      <div
                        v-if="ite == num.day + '日'"
                        :style="num.divStyle"
                        class="box-progress tooltip">
                        <span class="tooltiptext">{{ item.prcLine + '-' + item.checkArea }}<br>{{ '月检修 检修' + num.longtime + 'h' }}<br>开始时时间:{{ num.checkStartDate }}<br>结束时时间:{{ num.checkEndDate }}</span>
                      </div>
                      <div
                        v-if="ite == num.day + '日'"
                        class="box-text">{{ ite }}检修{{ num.longtime }}h</div>
                    </div>
                  </td>
                </tr>
                <tr
                  v-for="(item, index) in planTableJson['中厚板卷厂']"
                  :key="index+'step5'"
                >
                  <td
                    v-if="index == 0"
                    :rowspan="planTableJson['中厚板卷厂'].length"
                    class="centered-text">{{ item.prcLine }}</td>
                  <td
                    class="centered-text">{{ item.checkArea }}</td>
                  <td
                    v-for="(ite, ind) in datesList"
                    :key="ind+'step6'">
                    <div
                      v-for="(num, inx) in item.plan"
                      :key="inx + 'step14'"
                      class="box-content">
                      <div
                        v-if="ite == num.day + '日'"
                        :style="num.divStyle"
                        class="box-progress tooltip">
                        <span class="tooltiptext">{{ item.prcLine + '-' + item.checkArea }}<br>{{ '月检修 检修' + num.longtime + 'h' }}<br>开始时时间:{{ num.checkStartDate }}<br>结束时时间:{{ num.checkEndDate }}</span>
                      </div>
                      <div
                        v-if="ite == num.day + '日'"
                        class="box-text">{{ ite }}检修{{ num.longtime }}h</div>
                    </div>
                  </td>
                </tr>
                <tr
                  v-for="(item, index) in planTableJson['宽厚板厂']"
                  :key="index+'step7'"
                >
                  <td
                    v-if="index == 0"
                    :rowspan="planTableJson['宽厚板厂'].length"
                    class="centered-text">{{ item.prcLine }}</td>
                  <td
                    class="centered-text">{{ item.checkArea }}</td>
                  <td
                    v-for="(ite, ind) in datesList"
                    :key="ind+'step8'">
                    <div
                      v-for="(num, inx) in item.plan"
                      :key="inx + 'step15'"
                      class="box-content">
                      <div
                        v-if="ite == num.day + '日'"
                        :style="num.divStyle"
                        class="box-progress tooltip">
                        <span class="tooltiptext">{{ item.prcLine + '-' + item.checkArea }}<br>{{ '月检修 检修' + num.longtime + 'h' }}<br>开始时时间:{{ num.checkStartDate }}<br>结束时时间:{{ num.checkEndDate }}</span>
                      </div>
                      <div
                        v-if="ite == num.day + '日'"
                        class="box-text">{{ ite }}检修{{ num.longtime }}h</div>
                    </div>
                  </td>
                </tr>
                <tr
                  v-for="(item, index) in planTableJson['金石材料厂']"
                  :key="index+'step9'"
                >
                  <td
                    v-if="index == 0"
                    :rowspan="planTableJson['金石材料厂'].length"
                    class="centered-text">{{ item.prcLine }}</td>
                  <td
                    class="centered-text">{{ item.checkArea }}</td>
                  <td
                    v-for="(ite, ind) in datesList"
                    :key="ind+'step10'">
                    <div
                      v-for="(num, inx) in item.plan"
                      :key="inx + 'step16'"
                      class="box-content">
                      <div
                        v-if="ite == num.day + '日'"
                        :style="num.divStyle"
                        class="box-progress tooltip">
                        <span class="tooltiptext">{{ item.prcLine + '-' + item.checkArea }}<br>{{ '月检修 检修' + num.longtime + 'h' }}<br>开始时时间:{{ num.checkStartDate }}<br>结束时时间:{{ num.checkEndDate }}</span>
                      </div>
                      <div
                        v-if="ite == num.day + '日'"
                        class="box-text">{{ ite }}检修{{ num.longtime }}h</div>
                    </div>
                  </td>
                </tr>
                <tr
                  v-for="(item, index) in planTableJson['金润智能工厂']"
                  :key="index+'step11'"
                >
                  <td
                    v-if="index == 0"
                    :rowspan="planTableJson['金润智能工厂'].length"
                    class="centered-text">{{ item.prcLine }}</td>
                  <td
                    class="centered-text">{{ item.checkArea }}</td>
                  <td
                    v-for="(ite, ind) in datesList"
                    :key="ind+'step12'">
                    <div
                      v-for="(num, inx) in item.plan"
                      :key="inx + 'step16'"
                      class="box-content">
                      <div
                        v-if="ite == num.day + '日'"
                        :style="num.divStyle"
                        class="box-progress tooltip">
                        <span class="tooltiptext">{{ item.prcLine + '-' + item.checkArea }}<br>{{ '月检修 检修' + num.longtime + 'h' }}<br>开始时时间:{{ num.checkStartDate }}<br>结束时时间:{{ num.checkEndDate }}</span>
                      </div>
                      <div
                        v-if="ite == num.day + '日'"
                        class="box-text">{{ ite }}检修{{ num.longtime }}h</div>
                    </div>
                  </td>
                </tr>
              </table> -->
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <el-dialog 
      :visible.sync="dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          故障停时详情
        </div>
      </template>
      <el-table 
        v-loading="loading"
        :data="stopShowDetail"
        class="center-table"
        border>
        <el-table-column 
          property="FACTORYNAME"
          label="生产厂" />
        <el-table-column 
          property="ACSTOPMIN"
          label="非计划停时（分钟）" />
        <el-table-column 
          property="RATEMINUTE"
          label="折算后停时" />
        <el-table-column 
          property="STOPMINUTE"
          label="故障停机时长（分钟）" />
        <el-table-column 
          property="STOPTIMES"
          label="非计划停机次数" />
        <el-table-column 
          property="RATETIMES"
          label="折算后次数" />
        <el-table-column 
          property="FAILURETIMES"
          label="故障次数" />
        <el-table-column 
          property="STDLINE"
          label="标准线" />
      </el-table>
    </el-dialog>
    <el-dialog 
      :visible.sync="dialogVisible1"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="">
      <template v-slot:title>
        <div class="custom-dialog-title">
          隐患未处理详情
        </div>
      </template>
      <el-table 
        v-loading="loading"
        :data="flawList1"
        class="center-table"
        border>
        <el-table-column 
          property="FACTORY_NAME"
          label="生产厂"
          width="120" />
        <el-table-column 
          property="OBS_STAR_TIME"
          label="隐患发现时间"
          width="140" />
        <el-table-column 
          property="AREA"
          label="区域" />
        <el-table-column 
          property="DANGER_DESC"
          label="故障描述" />
        <el-table-column 
          property="STATUS"
          label="状态"
          width="100" />
      </el-table>
    </el-dialog>
  </div>
</template>
<script>
import CostChart from '@/pages/screen/diviceMeeting/component/cost-chart'
import BarsChart from '@/pages/screen/diviceMeeting/component/bars-chart'
import LineChart from '@/pages/screen/diviceMeeting/component/line-chart'
import WaterfallChart from '@/pages/screen/diviceMeeting/component/waterfall-chart'
import FoldedColumnChart from '@/pages/screen/diviceMeeting/component/FoldedColumn-chart'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border.vue'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  getYearFirstDay,
  getYearLastDay,
  getNowDay,
  getMonthFirstDay,
  getLastFirstMonth,
  getYearFirstDayLast
} from '@/utils/dateUtil'
import * as echarts from 'echarts'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import {
  spotDetectMissRate,
  maintenanceCost,
  getAllFailureStatisticData,
  troublePotentRate,
  dangerQuantityStatisticsFactory,
  unaddressDangerStatistics,
  overhaulMonth,
  queryNotDealDangerInfo,
  MaintenanceSchedule,
  unaddressDangerStatisticsDetail,
  getAllFailureDetail,
  spareParts,
  checkScheduleFindAll
} from '@/api/device'
import moment from 'moment'
import { getMonthDay } from '@/pages/screen/diviceMeeting/component/dateUtil/dateUtil'
import { get } from 'http'
import { pfmcFirstPassRateDetailed, processAlarmDetailed } from '@/api/screen'
export default {
  name: 'homePage',
  components: {
    CostChart,
    BarsChart,
    LineChart,
    WaterfallChart,
    FoldedColumnChart,
    ScreenBorder,
    echarts
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      loading: false,
      flawList: [],
      dialogVisible: false,
      flawList1: [],
      dialogVisible1: false,
      costX: [],
      cost: [
        {
          name: '总计',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        },
        {
          name: '单耗',
          type: 'line',
          yAxisIndex: 1,
          barGap: 0,
          smooth: true,
          data: []
        }
      ],
      analysis: [
        {
          name: '停时',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        },
        {
          name: '考核线',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        },
        {
          name: '频次',
          type: 'line',
          yAxisIndex: 1,
          barGap: 0,
          smooth: true,
          data: []
        }
      ],
      StopTime: [],
      stopDetail: [],
      stopDetailFactory: null,
      firstDay: '',
      hiddenDanger: [
        {
          name: '未处理数',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        }
      ],
      hiddenDangerX: [],
      hiddenDanger2: [
        {
          name: '未处理数',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        }
      ],
      prcLine: '',
      checkArea: '',
      checkDate: moment(new Date())
        .add(1, 'months')
        .format('YYYY-MM'),
      checkDevice: '',
      state: '',
      xAxisData: [],
      yAxisData: [],
      seriesOne: [],
      seriesTwo: [],
      progressChart: null,
      hiddenDangerX2: [],
      overhaulX: [],
      overhaul: [],
      overhaul2: [],
      overhaul2Len: null,
      overhaulStart: '',
      overhaulEnd: '',
      overhaulMin: null,
      overhaulMax: null,
      SpotCheckX: [],
      SpotCheck: [
        //点检漏检率
        {
          name: '未检数',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        },
        {
          name: '总数',
          type: 'bar',
          yAxisIndex: 0,
          barGap: 0,
          data: []
        },
        {
          name: '未检率',
          type: 'line',
          yAxisIndex: 1,
          smooth: true,
          data: []
        }
      ],
      TimeDta: {
        nowTime: '',
        nowDate: '',
        nowWeek: '',
        firstDayOfMonth: '',
        endDayOfMonth: '',
        lastMonth: '',
        firstMonthDay: '',
        endDay: '',
        year: '',
        lastDay: '',
        WeekBegin: '',
        firstYearDay: '',
        oneFactoryTime: {
          startTime: '',
          endTime: ''
        },
        tomorrow: ''
      },
      seriesOne: [],
      seriesTwo: [],
      datesList: [
        '26日',
        '27日',
        '28日',
        '29日',
        '30日',
        '31日',
        '01日',
        '02日',
        '03日',
        '04日',
        '05日',
        '06日',
        '07日',
        '08日',
        '09日',
        '10日',
        '11日',
        '12日',
        '13日',
        '14日',
        '15日',
        '16日',
        '17日',
        '18日',
        '19日',
        '20日',
        '21日',
        '22日',
        '23日',
        '24日',
        '25日'
      ],
      planTableJson: {
        第一炼钢厂: [
          /*{
            prcLine: '第一炼钢厂',
            checkArea: '1#连铸机',
            plan: [
              /!*{
                checkStartDate: '2024-11-21 08:30',
                checkEndDate: '2024-11-21 18:30',
                day: 21,
                hour: 8, //!*2-起止时间-left
                longtime: 10, //!*2-width
                divStyle: {
                  background: 'yellow',
                  width: 20 + 'px',
                  left: 16 + 'px'
                }
              },
              {
                checkStartDate: '2024-10-29 08:30',
                checkEndDate: '2024-10-29 12:30',
                day: 29,
                hour: 8, //!*2
                longtime: 4, //!*2
                divStyle: {
                  background: 'yellow',
                  width: 8 + 'px',
                  left: 16 + 'px'
                }
              }*!/
            ]
          } ,
          { prcLine: '第一炼钢厂', checkArea: '1#转炉', plan: [] },
          { prcLine: '第一炼钢厂', checkArea: '2#连铸机', plan: [] },
          { prcLine: '第一炼钢厂', checkArea: '2#转炉', plan: [] },
          { prcLine: '第一炼钢厂', checkArea: '3#连铸机', plan: [] },
          { prcLine: '第一炼钢厂', checkArea: '3#转炉', plan: [] }*/
        ],
        中板厂: [
          // { prcLine: '中板厂', checkArea: '热处理线', plan: [] },
          // { prcLine: '中板厂', checkArea: '主线', plan: [] }
        ],
        中厚板卷厂: [
          // { prcLine: '中厚板卷厂', checkArea: '1#加热炉', plan: [] },
          // { prcLine: '中厚板卷厂', checkArea: '热处理线', plan: [] },
          // { prcLine: '中厚板卷厂', checkArea: '主线', plan: [] }
        ],
        宽厚板厂: [
          // { prcLine: '宽厚板厂', checkArea: '热处理线', plan: [] },
          // { prcLine: '宽厚板厂', checkArea: '轧线', plan: [] }
        ],
        金石材料厂: [
          // { prcLine: '金石材料厂', checkArea: '1#石灰窑', plan: [] },
          // { prcLine: '金石材料厂', checkArea: '其他产线', plan: [] }
        ],
        金润智能工厂: [
          // { prcLine: '金润智能工厂', checkArea: '深加工产线', plan: [] }
        ]
      },
      form: {
        businessDivision: '3',
        // beginDate: getMonthFirstDay(),
        beginDate: getYearFirstDayLast(),
        endDate: getNowDay()
      },
      mergeFields: ['prcLine', 'checkDevice'],
      mergeObj: {},
      fields: [
        {
          label: '单位',
          key: 'prcLine',
          type: 'text',
          width: '180'
        },
        {
          label: '检修区域',
          key: 'checkArea',
          type: 'text',
          width: '180'
        },
        {
          label: '检修次数',
          key: 'checkDevice',
          type: 'input',
          width: '180'
        },
        {
          label: '检修日期及时长',
          key: 'checkStartDate',
          type: 'text'
        }
      ],
      tableData: [
        {
          prcLine: '宽厚板厂',
          checkArea: '热处理线压平机',
          checkDevice: '1',
          checkStartDate: '10月25日，8小时'
        },
        {
          prcLine: '宽厚板厂',
          checkArea: '热处理线压平机',
          checkDevice: '2',
          checkStartDate: '10月25日，8小时'
        },
        {
          prcLine: '宽厚板厂',
          checkArea: '热处理线压平机',
          checkDevice: '3',
          checkStartDate: '10月25日，8小时'
        },
        {
          prcLine: '中板厂',
          checkArea: '热处理线压平机',
          checkDevice: '4',
          checkStartDate: '10月25日，8小时'
        },
        {
          prcLine: '第一炼钢厂',
          checkArea: '热处理线压平机',
          checkDevice: '4',
          checkStartDate: '10月25日，8小时'
        },
        {
          prcLine: '第一炼钢厂',
          checkArea: '热处理线压平机',
          checkDevice: '4',
          checkStartDate: '10月25日，8小时'
        },
        {
          prcLine: '宽厚板厂',
          checkArea: '热处理线压平机',
          checkDevice: '4',
          checkStartDate: '10月25日，8小时'
        }
      ],
      form2: {
        startYearAndMonth: moment(new Date()).format('YYYY-MM'),
        scheduledTime1: 30,
        scheduledTime2: 30,
        scheduledTime3: 30,
        scheduledTime4: 30
      }
    }
  },
  computed: {
    stopShowDetail: function() {
      return this.stopDetail.filter(
        item => item.FACTORYNO == this.stopDetailFactory
      )
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.$nextTick(item => {
        this.getNowTime()
        this.getSpotCheck()
        this.getCost()
        this.getStoppingTime()
        this.getHiddenDanger()
        this.getHiddenDanger2()
        this.getOverhaul()
        this.queryTo()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
    this.prcLine = this.$route.params.prcLine
    this.checkArea = this.$route.params.checkArea
    if (
      this.$route.params.checkDate !== null &&
      this.$route.params.checkDate !== undefined &&
      this.$route.params.isStatus === true
    ) {
      this.form2.startYearAndMonth = this.$route.params.checkDate
      this.checkDate = this.$route.params.checkDate
    } else {
      this.checkDate = this.form2.startYearAndMonth
    }
    this.checkDevice = this.$route.params.checkDevice
    this.state = this.$route.params.state
    this.titleNameYear = this.checkDate.slice(0, 4)
    this.titleNameMonth = this.checkDate.slice(-2)
    this.monthDate = this.form2.startYearAndMonth.slice(-2)
    this.queryTo()
  },
  mounted() {
    this.getNowTime()
    this.getSpotCheck()
    this.getCost()
    this.getStoppingTime()
    this.getHiddenDanger()
    this.getHiddenDanger2()
    this.getOverhaul()
    this.getSpanArr(this.tableData)
  },
  methods: {
    queryTo() {
      post(checkScheduleFindAll, {
        prcLine: this.prcLine,
        checkArea: this.checkArea,
        checkDate: this.checkDate,
        checkDevice: this.checkDevice,
        state: this.state
      }).then(res => {
        if (res.success) {
          //-------json数据甘特图处理
          this.planTableJson['第一炼钢厂'] = []
          this.planTableJson['中板厂'] = []
          this.planTableJson['中厚板卷厂'] = []
          this.planTableJson['宽厚板厂'] = []
          this.planTableJson['金石材料厂'] = []
          this.planTableJson['金润智能工厂'] = []
          res.data.forEach((item, index) => {
            if (item.prcLine == '第一炼钢厂') {
              this.planTableJson['第一炼钢厂'].push({
                prcLine: item.prcLine,
                checkArea: item.checkArea,
                plan: []
              })
            }
            this.planTableJson['第一炼钢厂'] = this.planTableJson[
              '第一炼钢厂'
            ].filter(
              (item, index, self) =>
                self.findIndex(t => t.checkArea === item.checkArea) === index
            )
            this.planTableJson['第一炼钢厂'].forEach((num, ind) => {
              if (
                num.prcLine == item.prcLine &&
                num.checkArea == item.checkArea
              ) {
                this.planTableJson['第一炼钢厂'][ind].plan.push({
                  checkStartDate: item.checkStartDate,
                  checkEndDate: item.checkEndDate,
                  day:
                    new Date(item.checkStartDate).getDate() < 10
                      ? '0' + new Date(item.checkStartDate).getDate()
                      : new Date(item.checkStartDate).getDate(),
                  hour: new Date(item.checkStartDate).getHours(), //*2-起止时间-left
                  longtime:
                    (new Date(item.checkEndDate) -
                      new Date(item.checkStartDate)) /
                    3600000, //!*2-width
                  divStyle: {
                    background: '#ffc000',
                    width:
                      ((new Date(item.checkEndDate) -
                        new Date(item.checkStartDate)) *
                        1.8) /
                        3600000 +
                      'px',
                    left: new Date(item.checkStartDate).getHours() * 2 + 'px'
                  }
                })
                console.log(
                  "this.planTableJson['第一炼钢厂']",
                  this.planTableJson['第一炼钢厂']
                )
              }
            })
          })
          res.data.forEach((item, index) => {
            if (item.prcLine == '中板厂') {
              this.planTableJson['中板厂'].push({
                prcLine: item.prcLine,
                checkArea: item.checkArea,
                plan: []
              })
            }
            this.planTableJson['中板厂'] = this.planTableJson['中板厂'].filter(
              (item, index, self) =>
                self.findIndex(t => t.checkArea === item.checkArea) === index
            )
            this.planTableJson['中板厂'].forEach((num, ind) => {
              if (
                num.prcLine == item.prcLine &&
                num.checkArea == item.checkArea
              ) {
                this.planTableJson['中板厂'][ind].plan.push({
                  checkStartDate: item.checkStartDate,
                  checkEndDate: item.checkEndDate,
                  day:
                    new Date(item.checkStartDate).getDate() < 10
                      ? '0' + new Date(item.checkStartDate).getDate()
                      : new Date(item.checkStartDate).getDate(),
                  hour: new Date(item.checkStartDate).getHours(), //*2-起止时间-left
                  longtime:
                    (new Date(item.checkEndDate) -
                      new Date(item.checkStartDate)) /
                    3600000, //!*2-width
                  divStyle: {
                    background: '#548235',
                    width:
                      ((new Date(item.checkEndDate) -
                        new Date(item.checkStartDate)) *
                        1.8) /
                        3600000 +
                      'px',
                    left: new Date(item.checkStartDate).getHours() * 2 + 'px'
                  }
                })
              }
            })
          })
          res.data.forEach((item, index) => {
            if (item.prcLine == '中厚板卷厂') {
              this.planTableJson['中厚板卷厂'].push({
                prcLine: item.prcLine,
                checkArea: item.checkArea,
                plan: []
              })
            }
            this.planTableJson['中厚板卷厂'] = this.planTableJson[
              '中厚板卷厂'
            ].filter(
              (item, index, self) =>
                self.findIndex(t => t.checkArea === item.checkArea) === index
            )
            this.planTableJson['中厚板卷厂'].forEach((num, ind) => {
              if (
                num.prcLine == item.prcLine &&
                num.checkArea == item.checkArea
              ) {
                this.planTableJson['中厚板卷厂'][ind].plan.push({
                  checkStartDate: item.checkStartDate,
                  checkEndDate: item.checkEndDate,
                  day:
                    new Date(item.checkStartDate).getDate() < 10
                      ? '0' + new Date(item.checkStartDate).getDate()
                      : new Date(item.checkStartDate).getDate(),
                  hour: new Date(item.checkStartDate).getHours(), //*2-起止时间-left
                  longtime:
                    (new Date(item.checkEndDate) -
                      new Date(item.checkStartDate)) /
                    3600000, //!*2-width
                  divStyle: {
                    background: '#00b0f0',
                    width:
                      ((new Date(item.checkEndDate) -
                        new Date(item.checkStartDate)) *
                        1.7) /
                        3600000 +
                      'px',
                    left: new Date(item.checkStartDate).getHours() * 2 + 'px'
                  }
                })
              }
            })
          })
          res.data.forEach((item, index) => {
            if (item.prcLine == '宽厚板厂') {
              this.planTableJson['宽厚板厂'].push({
                prcLine: item.prcLine,
                checkArea: item.checkArea,
                plan: []
              })
            }
            this.planTableJson['宽厚板厂'] = this.planTableJson[
              '宽厚板厂'
            ].filter(
              (item, index, self) =>
                self.findIndex(t => t.checkArea === item.checkArea) === index
            )
            this.planTableJson['宽厚板厂'].forEach((num, ind) => {
              if (
                num.prcLine == item.prcLine &&
                num.checkArea == item.checkArea
              ) {
                this.planTableJson['宽厚板厂'][ind].plan.push({
                  checkStartDate: item.checkStartDate,
                  checkEndDate: item.checkEndDate,
                  day:
                    new Date(item.checkStartDate).getDate() < 10
                      ? '0' + new Date(item.checkStartDate).getDate()
                      : new Date(item.checkStartDate).getDate(),
                  hour: new Date(item.checkStartDate).getHours(), //*2-起止时间-left
                  longtime:
                    (new Date(item.checkEndDate) -
                      new Date(item.checkStartDate)) /
                    3600000, //!*2-width
                  divStyle: {
                    background: '#ffff00',
                    width:
                      ((new Date(item.checkEndDate) -
                        new Date(item.checkStartDate)) *
                        1.8) /
                        3600000 +
                      'px',
                    left: new Date(item.checkStartDate).getHours() * 2 + 'px'
                  }
                })
              }
            })
          })
          res.data.forEach((item, index) => {
            if (item.prcLine == '金石材料厂') {
              this.planTableJson['金石材料厂'].push({
                prcLine: item.prcLine,
                checkArea: item.checkArea,
                plan: []
              })
            }
            this.planTableJson['金石材料厂'] = this.planTableJson[
              '金石材料厂'
            ].filter(
              (item, index, self) =>
                self.findIndex(t => t.checkArea === item.checkArea) === index
            )
            this.planTableJson['金石材料厂'].forEach((num, ind) => {
              if (
                num.prcLine == item.prcLine &&
                num.checkArea == item.checkArea
              ) {
                this.planTableJson['金石材料厂'][ind].plan.push({
                  checkStartDate: item.checkStartDate,
                  checkEndDate: item.checkEndDate,
                  day:
                    new Date(item.checkStartDate).getDate() < 10
                      ? '0' + new Date(item.checkStartDate).getDate()
                      : new Date(item.checkStartDate).getDate(),
                  hour: new Date(item.checkStartDate).getHours(), //*2-起止时间-left
                  longtime:
                    (new Date(item.checkEndDate) -
                      new Date(item.checkStartDate)) /
                    3600000, //!*2-width
                  divStyle: {
                    background: '#c55a11',
                    width:
                      ((new Date(item.checkEndDate) -
                        new Date(item.checkStartDate)) *
                        1.8) /
                        3600000 +
                      'px',
                    left: new Date(item.checkStartDate).getHours() * 2 + 'px'
                  }
                })
              }
            })
          })
          res.data.forEach((item, index) => {
            if (item.prcLine == '金润智能工厂') {
              this.planTableJson['金润智能工厂'].push({
                prcLine: item.prcLine,
                checkArea: item.checkArea,
                plan: []
              })
            }
            this.planTableJson['金润智能工厂'] = this.planTableJson[
              '金润智能工厂'
            ].filter(
              (item, index, self) =>
                self.findIndex(t => t.checkArea === item.checkArea) === index
            )
            this.planTableJson['金润智能工厂'].forEach((num, ind) => {
              if (
                num.prcLine == item.prcLine &&
                num.checkArea == item.checkArea
              ) {
                this.planTableJson['金润智能工厂'][ind].plan.push({
                  checkStartDate: item.checkStartDate,
                  checkEndDate: item.checkEndDate,
                  day:
                    new Date(item.checkStartDate).getDate() < 10
                      ? '0' + new Date(item.checkStartDate).getDate()
                      : new Date(item.checkStartDate).getDate(),
                  hour: new Date(item.checkStartDate).getHours(), //*2-起止时间-left
                  longtime:
                    (new Date(item.checkEndDate) -
                      new Date(item.checkStartDate)) /
                    3600000, //!*2-width
                  divStyle: {
                    background: '#c5e0b4',
                    width:
                      ((new Date(item.checkEndDate) -
                        new Date(item.checkStartDate)) *
                        1.8) /
                        3600000 +
                      'px',
                    left: new Date(item.checkStartDate).getHours() * 2 + 'px'
                  }
                })
              }
            })
          })
          //甘特图数据处理
          res.data.forEach((item, index) => {
            if (item.checkDevice.length > 5) {
              item.checkDevice = item.checkDevice.slice(0, 2)
            }
            this.xAxisData.push(
              item.prcLine + '-' + item.checkArea + '-' + item.checkDevice
            )
            this.seriesOne.push(item.checkEndDate)
            this.seriesTwo.push(item.checkStartDate)
          })
          this.yAxisData = JSON.parse(JSON.stringify(this.xAxisData))
          console.log('this.yAxisData', this.yAxisData)
          console.log('this.seriesOne', this.seriesOne)
          this.updateChart()
          //对比图数据处理
          //   res.data.forEach((item, index) => {
          //     if (res.data[index].checkTime === null) {
          //       res.data[index].checkTime = 0
          //     }
          //     if (item.prcLine === '宽厚板厂' && item.checkArea === '轧线') {
          //       console.log(
          //         'Number(res.data[index].checkTime)',
          //         Number(res.data[index].checkTime)
          //       )
          //       this.totalFour += Number(res.data[index].checkTime)
          //       this.totalFour = Math.round(this.totalFour * 100) / 100
          //     }
          //     if (item.prcLine === '中厚板卷厂' && item.checkArea === '主线') {
          //       this.totalTree += Number(res.data[index].checkTime)
          //       this.totalTree = Math.round(this.totalTree * 100) / 100
          //     }
          //     if (item.prcLine === '中板厂' && item.checkArea === '主线') {
          //       this.totalTwo += Number(res.data[index].checkTime)
          //       this.totalTwo = Math.round(this.totalTwo * 100) / 100
          //     }
          //     if (item.prcLine === '第一炼钢厂') {
          //       this.totalOne += Number(res.data[index].checkTime)
          //       this.totalOne = Math.round(this.totalOne * 100) / 100
          //     }
          //     //----------
          //     if (item.prcLine === '宽厚板厂' && item.checkArea === '热处理线') {
          //       this.totalFour1 += Number(res.data[index].checkTime)
          //       this.totalFour1 = Math.round(this.totalFour1 * 100) / 100
          //     }
          //     if (
          //       item.prcLine === '中厚板卷厂' &&
          //       item.checkArea === '热处理线'
          //     ) {
          //       this.totalTree1 += Number(res.data[index].checkTime)
          //       this.totalTree1 = Math.round(this.totalTree1 * 100) / 100
          //     }
          //     if (item.prcLine === '中板厂' && item.checkArea === '热处理线') {
          //       this.totalTwo1 += Number(res.data[index].checkTime)
          //       this.totalTwo1 = Math.round(this.totalTwo1 * 100) / 100
          //     }
          //     // if (item.prcLine === '第一炼钢厂') {
          //     //   this.totalOne += Number(res.data[index].checkTime)
          //     //   this.totalOne = Math.round(this.totalOne * 100) / 100
          //     // }
          //   })
          //   this.updateCompareChart()
          //   this.updateCompareChart1()
          //分布图
          //   let numOne = 0
          //   let numList = []
          //   res.data.forEach((item, index) => {
          //     if (item.updReason !== null && item.updReason !== undefined) {
          //       numList.push(item.updReason)
          //     }
          //   })
          //   console.log('numList', numList)
          //   // 创建一个空对象来存储每个元素及其计数
          //   const countMap = {}

          // 遍历数组
          //   numList.forEach(item => {
          //     // 如果元素已经在对象中，则增加其计数
          //     if (countMap[item]) {
          //       countMap[item]++
          //     } else {
          //       // 否则，初始化计数为1
          //       countMap[item] = 1
          //     }
          //   })
          //   console.log('countMap', countMap)
          //   this.lineOption3.series[0].data[0].value = countMap['生产']
          //   this.lineOption3.series[0].data[1].value = countMap['工艺']
          //   this.lineOption3.series[0].data[2].value = countMap['设备']
          //   this.lineOption3.series[0].data[3].value = countMap['质量']
          //   this.lineOption3.series[0].data[4].value = countMap['公司统筹']
          //   this.lineOption3.series[0].data[5].value = countMap['生产管控']
        } else {
          // this.$message.error('查询失败！')
        }
      })
    },
    //echarts甘特图
    getEchartsDis() {
      echarts.init(document.getElementById('progressChart')).dispose() // 销毁实例
      this.progressChart = echarts.init(
        document.getElementById('progressChart')
      )
      const endTimeData = this.seriesOne
      const startTimeData = this.seriesTwo
      let option = {
        // 鼠标移入提示工具
        tooltip: {
          trigger: 'axis',
          formatter(params) {
            // console.log('params', params)
            if (params[1].data && params[0].data) {
              const startTime1 = new Date(params[1].data)
              const endTime1 = new Date(params[0].data)
              if (startTime1 > endTime1) {
                console.error('开始时间不能晚于结束时间')
                return 0 // 或者返回一个错误代码，表示时间顺序错误
              }
              // 计算时间差（毫秒）
              const diff = startTime1 - endTime1
              // 将毫秒转换为小时
              // 注意：1小时 = 1000毫秒 * 60秒 * 60分钟 = 3600000毫秒
              const hoursDiff = diff / 3600000
              return (
                `<div>${params[1].axisValue}</div>` +
                `<div>月检修 检修${hoursDiff}h</div>` +
                `<div>开始时间：${params[1].data}</div>` +
                `<div>结束时间：${params[0].data}</div>`
              )
            } else {
              return ''
            }
          },
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          containLabel: true,
          show: false,
          right: 352,
          left: 40,
          bottom: 40,
          top: 20,
          backgroundColor: '#fff'
        },
        legend: {
          // 图例组件
          data: ['持续时间'],
          align: 'auto',
          top: 'bottom',
          show: false
        },
        xAxis: {
          type: 'time',
          position: 'top', // x 轴位置
          boundaryGap: ['0%', '1%'],
          scale: true,
          axisLabel: {
            formatter: {
              // // 自定义时间格式
              // var date = new Date(value)
              // return echarts.format.formatTime('dd' + '日', date)
              day: '{d}日'
            },
            interval: 1000
          },
          // min: '2024-09-26 00:00',
          // max: '2024-10-25 23:59',
          minInterval: 1000,
          axisTick: {
            // 隐藏刻度
            show: false
          },
          axisLine: {
            // 隐藏轴线
            show: false
          },
          splitLine: {
            // 网格线
            show: true,
            lineStyle: {
              //分割线
              color: '#C0C4CC',
              width: 1,
              type: 'dashed' //dotted：虚线 solid:实线
            }
          }
        },
        yAxis: {
          inverse: true, // y 轴数据翻转，该操作是为了保证项目一放在最上面，项目七在最下面
          axisTick: {
            // 隐藏刻度
            show: true
          },
          splitLine: {
            // 网格线
            show: true,
            lineStyle: {
              //分割线
              color: '#C0C4CC',
              width: 1,
              type: 'dashed' //dotted：虚线 solid:实线
            }
          },
          axisLine: {
            // 隐藏轴线
            show: true
          },
          data: []
        },
        series: [
          {
            name: '持续时间',
            type: 'bar',
            stack: 'duration',
            itemStyle: {
              color: '#007acc',
              borderColor: '#fff',
              borderWidth: 1
            },
            label: {
              show: true,
              position: 'right',
              color: '#191919',
              formatter: params => {
                const findIndex = endTimeData.findIndex(
                  item => item === params.value
                )
                const startTime = startTimeData[findIndex]
                // -----计算检修时长，精确到小时----
                // 将时间字符串转换为Date对象
                const startTime1 = new Date(startTime)
                const endTime1 = new Date(params.value)
                if (startTime1 > endTime1) {
                  console.error('开始时间不能晚于结束时间')
                  return 0 // 或者返回一个错误代码，表示时间顺序错误
                }
                // 计算时间差（毫秒）
                const diff = startTime1 - endTime1
                // 将毫秒转换为小时
                // 注意：1小时 = 1000毫秒 * 60秒 * 60分钟 = 3600000毫秒
                const hoursDiff = diff / 3600000
                return `${startTime} ~ ${
                  params.value
                } 月检修，检修${hoursDiff}h`
              } // 格式化 label
            },
            zlevel: -1,
            data: [] // 结束时间
          },
          {
            name: '持续时间',
            type: 'bar',
            stack: 'duration', // 堆叠标识符，同个类目轴上系列配置相同的 stack 值可以堆叠放置
            itemStyle: {
              color: '#f4f4f5'
            },
            zlevel: -1, // zlevel 大的 Canvas 会放在 zlevel 小的 Canvas 的上面
            z: 9, // z值小的图形会被z值大的图形覆盖，z相比zlevel优先级更低，而且不会创建新的 Canvas
            data: [] // 开始时间
          }
        ]
      }
      this.progressChart.setOption(option)
      // console.log('option', option)
      // 浏览器窗口大小变化，图表大小自适应
      window.addEventListener('resize', () => {
        this.progressChart.resize()
      })
    },
    updateChart() {
      // 更新 ECharts 配置，特别是 y 轴和系列数据
      this.progressChart.setOption({
        xAxis: {
          // 如果需要，也可以在这里更新 xAxis 的数据
        },
        yAxis: {
          // 通常 yAxis 不需要设置 data，这里只是为了示例
          // 如果真的需要设置 yAxis 的 data（不常见），可以这样：
          data: this.yAxisData
        },
        series: [
          {
            name: '持续时间',
            type: 'bar',
            stack: 'duration',
            itemStyle: {
              color: '#007acc',
              borderColor: '#fff',
              borderWidth: 1
            },
            zlevel: -1,
            data: this.seriesOne // 结束时间
          },
          {
            name: '持续时间',
            type: 'bar',
            stack: 'duration', // 堆叠标识符，同个类目轴上系列配置相同的 stack 值可以堆叠放置
            itemStyle: {
              color: '#f4f4f5'
            },
            zlevel: -1, // zlevel 大的 Canvas 会放在 zlevel 小的 Canvas 的上面
            z: 9, // z值小的图形会被z值大的图形覆盖，z相比zlevel优先级更低，而且不会创建新的 Canvas
            data: this.seriesTwo // 开始时间
          }
        ]
      })
    },
    //获取各时间段
    getNowTime() {
      this.TimeDta.nowTime = moment().format('HH:mm:ss') //当前时间时分秒
      this.TimeDta.nowDate = moment().format('YYYY-MM-DD') //当前时间年月日
      this.TimeDta.nowWeek = this.getWeek() //当前周
      this.TimeDta.firstDayOfMonth = moment()
        .startOf('months')
        .format('YYYY-MM-DD')
      this.TimeDta.endDayOfMonth = moment().format('YYYY-MM')
      // 获取上一个月的时间
      this.TimeDta.lastMonth = moment(
        moment()
          .month(moment().month() - 1)
          .startOf('month')
          .valueOf()
      ).format('YYYY-MM')
      // 获取今年第一个月
      this.TimeDta.firstDay = moment().format('YYYY') + '-01'
      // 本月第一天
      this.TimeDta.firstMonthDay = moment().format('YYYY-MM') + '-01'
      this.TimeDta.endDay = moment().format('YYYY') + '-12'
      // 获取今年
      this.TimeDta.year = moment().format('YYYY')
      //获取昨日时间
      this.TimeDta.lastDay = moment(
        moment()
          .add(-1, 'days')
          .startOf('day')
          .valueOf()
      ).format('YYYY-MM-DD')
      // 获取本周周一日期
      const weekOfday = moment().format('E')
      this.TimeDta.WeekBegin = moment()
        .subtract(weekOfday - 1, 'days')
        .format('YYYY-MM-DD')
      // 本年第一天
      this.TimeDta.firstYearDay = moment().format('YYYY') + '-01-01'
      this.TimeDta.oneFactoryTime.endTime = this.TimeDta.nowDate
      // 获取明天的时间
      this.TimeDta.tomorrow = moment()
        .add(1, 'days')
        .format('YYYY-MM-DD')
    },
    //转换周几
    getWeek() {
      // 参数时间戳
      let week = moment(new Date()).day()
      switch (week) {
        case 1:
          return '星期一'
        case 2:
          return '星期二'
        case 3:
          return '星期三'
        case 4:
          return '星期四'
        case 5:
          return '星期五'
        case 6:
          return '星期六'
        case 0:
          return '星期日'
      }
    },
    // 处理厂名
    getFactoryName(name) {
      switch (name) {
        case '中厚板卷厂':
          return '中厚板卷厂'
        case '金石材料厂':
          return '金石材料厂'
        case '金润厂':
          return '金润智能工厂'
        case '金润智能制造厂':
          return '金润智能工厂'
        case '第一炼钢厂':
          return '第一炼钢厂'
        case '中板厂':
          return '中板厂'
        case '宽厚板厂':
          return '宽厚板厂'
        case '板材事业部机关':
          return '板材事业部机关'
        case '事业部':
          return '事业部'
        default:
          return name
      }
    },
    getFactorycode(name) {
      switch (name) {
        case '32':
          return '中厚板卷厂'
        case '38':
          return '宽厚板厂'
        case '66':
          return '中板厂'
        case '87':
          return '金润智能工厂'
        case '73':
          return '第一炼钢厂'
        case '84':
          return '金石材料厂'
        // case '宽厚板厂':
        //   return '宽厚板厂'
        // case '板材事业部机关':
        //   return '板材事业部机关'
        case '10':
          return '事业部'
        default:
          return name
      }
    },
    //点检漏检率
    getSpotCheck() {
      const params = {
        beginDate: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD'),
        endDate: this.$moment(this.cDate)
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
      }
      post(spotDetectMissRate, params).then(res => {
        // console.log('点检漏检率', res)
        let data = [
          '第一炼钢厂',
          '中厚板卷厂',
          '宽厚板厂',
          '中板厂',
          '金石材料厂',
          '金润智能制造厂'
        ]
        const sortedRes = res.data
        console.log(sortedRes)
        this.SpotCheckX = data.map(item => this.getFactoryName(item))
        this.SpotCheck[0].data = sortedRes.map(x => x.notChked) //未检数
        this.SpotCheck[1].data = sortedRes.map(x => x.totalCount) //总数
        this.SpotCheck[2].data = sortedRes.map(x =>
          x.notChkedRate.replace('%', '')
        ) //未检率
        // console.log(this.SpotCheck)
      })
    },
    //费用
    getCost() {
      const params = {
        yearMonth: this.$moment(this.cDate)
          .subtract(1, 'month')
          .format('yyyy-MM')
      }
      post(maintenanceCost, params).then(res => {
        if (res.data.length > 0) {
          this.cost[0].data = []
          this.cost[1].data = []
          this.costX = res.data.map(item => this.getFactoryName(item.factoryNo))
          res.data.forEach(item => {
            this.cost[0].data.push(Number(item.equipSingCons).toFixed(3))
            // this.cost[1].data.push(Number(item.DANHAO).toFixed(3))
          })
        }
      })
    },
    // 停时分析
    getStoppingTime() {
      const params = {
        businessDivision: '3',
        beginMonth: this.$moment(this.cDate).format('yyyy-MM'),
        endMonth: this.$moment(this.cDate).format('yyyy-MM')
      }
      post(getAllFailureStatisticData, params).then(res => {
        let factoryName = []
        let stopMinute = []
        let RATETIMES = []
        let STDLINEAll = []
        if (res.success) {
          this.stopDetail = res.data
          res.data.forEach(item => {
            item.RATEMINUTE = item.RATEMINUTE ? item.RATEMINUTE : 0
            item.RATETIMES = item.RATETIMES ? item.RATETIMES : 0
            item.STDLINE = item.STDLINE ? item.STDLINE : 0
            stopMinute.push(item.RATEMINUTE)
            STDLINEAll.push(item.STDLINE)
            factoryName.push({
              value: this.getFactoryName(item.FACTORYNAME),
              code: item.FACTORYNO
            })
            RATETIMES.push(item.RATETIMES)
          })
        }
        this.analysis[0].data = stopMinute
        this.analysis[1].data = STDLINEAll
        this.analysis[2].data = RATETIMES
        this.StopTime = factoryName
      })
    },
    //隐患
    getHiddenDanger() {
      const params = this.form
      post(queryNotDealDangerInfo, params).then(res => {
        let allSum1 = res.data.reduce((sum, item) => sum + item.NOMAL, 0)
        let allSum2 = res.data.reduce((sum, item) => sum + item.BIGGER, 0)
        let allSum3 = res.data.reduce((sum, item) => sum + item.MAJOR, 0)
        res.data.push({
          FACTORY_NO: '10',
          NOMAL: allSum1,
          BIGGER: allSum2,
          MAJOR: allSum3
        })
        res.data.map(item => {
          return {
            value: this.getFactorycode(item.FACTORY_NO),
            code: item.FACTORY_NO
          }
        })
        this.hiddenDangerX = res.data.map(item => {
          return {
            value: this.getFactorycode(item.FACTORY_NO),
            code: item.FACTORY_NO
          }
        })
        this.hiddenDanger = [
          //隐患
          {
            name: '一般隐患',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: res.data.map(item => item.NOMAL)
          },
          {
            name: '较大隐患',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: res.data.map(item => item.BIGGER)
          },
          {
            name: '重大隐患',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: res.data.map(item => item.MAJOR)
          }
        ]
      })
    },
    getHiddenDanger2() {
      post(spareParts, {}).then(res => {
        let amt = []
        let amt1 = []
        let amt2 = []
        let name = []
        let name1 = []
        let name2 = []
        let cause1 = null
        let cause2 = null
        let cause3 = null
        // res.data.map((item) => {
        //   return {
        //     value: this.getFactorycode(item.FACTORY_NO),
        //     code: item.FACTORY_NO,
        //   }
        // })
        this.hiddenDangerX2 = [
          '中厚板卷厂',
          '宽厚板厂',
          '中板厂',
          '第一炼钢厂',
          '金石材料厂',
          '金润智能制造厂',
          '板材事业部'
        ]
        if (res.data.length > 0) {
          res.data.forEach(item => {
            if (item.TYPE === '5年以上' && item.DEPT !== '板材事业部机关') {
              amt.push(item.AMT)
              name.push(item.DEPT)
              cause1 += item.AMT
            }
            if (item.TYPE === '3-5年' && item.DEPT !== '板材事业部机关') {
              amt1.push(item.AMT)
              name1.push(item.DEPT)
              cause2 += item.AMT
            }
            if (item.TYPE === '三年以内' && item.DEPT !== '板材事业部机关') {
              amt2.push(item.AMT)
              name2.push(item.DEPT)
              cause3 += item.AMT
            }
          })
          let sum2 = amt2.reduce((accumulator, currentValue) => {
            return accumulator + currentValue
          }, 0)
          let sum = amt.reduce((accumulator, currentValue) => {
            return accumulator + currentValue
          }, 0)
          let sum1 = amt1.reduce((accumulator, currentValue) => {
            return accumulator + currentValue
          }, 0)
          amt.push(sum)
          amt1.push(sum1)
          amt2.push(sum2)
          //   console.log('amt2:', sum2)
          //   console.log('amt1:', sum1)
          //   console.log('amt:', sum)
        }

        this.hiddenDanger2 = [
          {
            name: '3年以内',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: amt2
          },
          {
            name: '3-5年',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: amt1
          },
          {
            name: '5年以上',
            type: 'bar',
            yAxisIndex: 0,
            barGap: 0,
            data: amt
          }
        ]
      })
    },
    getOverhaul() {
      const begDate = this.$moment(this.cDate)
        .startOf('week')
        .format('yyyyMMDD')
      const endDate = this.$moment(begDate)
        .add(7, 'day')
        .format('yyyyMMDD')
      this.overhaulStart = this.$moment(begDate).format('MM月DD日')
      this.overhaulEnd = this.$moment(endDate).format('MM月DD日')
      const params = {
        begDate,
        endDate
      }
      post(MaintenanceSchedule, params).then(res => {
        const bar = []
        this.overhaulX = [
          '一钢',
          '板卷',
          '宽板',
          '中板',
          '金石',
          '金润'
        ].reverse()
        res.data.forEach((item, index) => {
          bar.push({
            value: [
              this.overhaulX.findIndex(x => x === item.LINENO),
              this.$moment(item.KSSJ, 'YYYYMMDDHHmmss').format(
                'YYYY-MM-DD HH:mm:ss'
              ),
              this.$moment(item.JSSJ, 'YYYYMMDDHHmmss').format(
                'YYYY-MM-DD HH:mm:ss'
              ),
              item.PCODE,
              item.JXNR2
            ] //0,1,2代表y轴的索引，后两位代表x轴数据开始和结束
          })
        })
        this.overhaulMax = this.$moment(endDate).format('YYYY-MM-DD')
        this.overhaulMin = this.$moment(begDate).format('YYYY-MM-DD')
        this.overhaul2 = this.overhaulX.map((item, index) => {
          return [
            index,
            this.$moment(endDate, 'YYYYMMDD').format('YYYY-MM-DD') +
              ' 24:00:00',
            this.$moment(endDate, 'YYYYMMDD').format('YYYY-MM-DD') +
              ' 24:00:00',
            res.data
              .filter(e => e.LINENO === item)
              .map(e => e.JXNR2)
              .join('；')
          ]
        })
        this.overhaul2Len = _.max(this.overhaul2.map(item => item[3].length))
        this.overhaul = bar
      })
    },
    // 详情
    getStopTimeDetail(data) {
      console.log(data)
      this.stopDetailFactory = this.StopTime[
        data.fromActionPayload.dataIndexInside
      ].code
      this.dialogVisible = true
    },
    // 详情
    getHiddenDangerDetail(data) {
      console.log(data)
      post(getAllFailureDetail, {
        factoryNo: this.hiddenDangerX[data.fromActionPayload.dataIndexInside]
          .code
      }).then(res => {
        this.flawList1 = res.data
        this.dialogVisible1 = true
      })
    },
    //获取合并序号
    getSpanArr(data = []) {
      this.mergeFields.forEach(key => {
        // 用来记录需要合并行的起始位置
        let count = 0
        // 记录每一列的合并信息
        this.mergeObj[key] = []
        data.forEach((item, index) => {
          // 第一行直接 push 一个 1
          if (!index) {
            this.mergeObj[key].push(1)
            return
          }

          // 判断当前行是否与上一行其值相等
          // 如果相等在count记录的位置其值+1
          // 表示当前行需要合并 并添加一个 0 占位
          if (item[key] === data[index - 1][key]) {
            this.mergeObj[key][count] += 1
            this.mergeObj[key].push(0)
            return
          }

          // 如果当前行和上一行其值不相等 记录当前位置
          count = index
          // 重新push一个 1
          this.mergeObj[key].push(1)
        })
      })
    },
    //获取合并序号
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {},
    //-----------甘特图调整
    printDatesFromLastMonth16ToThisMonth25(inputMonth) {
      // 创建日期对象
      let currentDate = new Date()

      // 设置输入的月份（注意：JavaScript 月份从0开始，所以要减1）
      currentDate.setMonth(inputMonth - 1, 1) // 这里设置为当前月的1号，确保月份正确

      // 获取上个月的年份和月份
      let lastMonthYear = currentDate.getFullYear()
      let lastMonth = currentDate.getMonth() + 1 // 获取上个月（因为月份从0开始，所以要加1）

      // 创建输出数组
      let dates = []

      // 从上个月16号开始
      for (let day = 26; day <= 31; day++) {
        // 尝试创建上个月某一天的日期对象
        let lastMonthDate = new Date(lastMonthYear, lastMonth - 1, day)

        // 检查日期是否合法（即是否在上个月内）
        if (lastMonthDate.getMonth() === lastMonth - 1) {
          dates.push(
            `${lastMonthDate.toLocaleDateString('zh-CN', {
              // year: 'numeric',
              // month: '2-digit',
              day: '2-digit'
            })}`
          )
        } else {
          break // 超出上个月范围，跳出循环
        }
      }

      // 接着输出当前输入月份的日期，从1号到25号
      let thisMonthYear = currentDate.getFullYear()
      let thisMonth = inputMonth

      for (let day = 1; day <= 25; day++) {
        let thisMonthDate = new Date(thisMonthYear, thisMonth - 1, day)
        dates.push(
          `${thisMonthDate.toLocaleDateString('zh-CN', {
            // year: 'numeric',
            // month: '2-digit',
            day: '2-digit'
          })}`
        )
      }
      this.datesList = []
      this.datesList.push(dates)
      // 输出结果
      console.log(dates)
      console.log('datesList', this.datesList)
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
  }

  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #9e2d2d;
  overflow: auto;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.tooltip .tooltiptext {
  visibility: hidden;
  width: 260px;
  background-color: black;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 0;

  /* 位置 */
  position: absolute;
  z-index: 1;
  bottom: 100%;
  left: 50%;
  margin-left: -270px;

  /* 动画 */
  opacity: 0;
  transition: opacity 0.3s;
}
/* 鼠标悬停在容器上时显示tooltip */
.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}
.contentBox {
  height: 100%;
  width: 100%;
  padding: 16px 24px 24px 24px;
  overflow-x: auto;
  overflow-y: auto;
}
#container {
  height: 500px;
  width: 100%;
}
#progressChart {
  height: 700px;
  width: 1600px;
}
#compareChart {
  height: 475px;
  width: 100%;
}
#compareChart1 {
  height: 475px;
  width: 100%;
}
/deep/.el-form.el-form--inline {
  height: 36px;
}
/deep/.el-input__inner {
  height: 36px;
}
/deep/.el-select__caret.el-input__icon.el-icon-arrow-up {
  height: 40px;
}
.foot {
  margin-top: 20px;
}
/*-----表格高度调整----*/
/deep/.el-table__row {
  height: 48px;
}
/deep/ tr {
  height: 38px;
}
/deep/.el-card__body {
  padding: 24px;
}
/deep/.el-dialog {
  height: 60%;
}
/deep/.input-border .el-popover__reference {
  width: 214px;
}
/deep/ input[disabled],
input:disabled,
input.disabled {
  -webkit-text-fill-color: #3a3f63; /*disabled里面字的颜色*/
  background: #f5f7fa;
  -webkit-opacity: 1;
  opacity: 1;
  cursor: not-allowed;
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}
/deep/.el-textarea.is-disabled .el-textarea__inner {
  background-color: #f5f7fa;
  border-color: #e6e9f4;
  color: #3a3f63;
  cursor: not-allowed;
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}
.buttonTableDel {
  color: #ff2855;
}
.buttonTableGray {
  color: #dce8f9;
}
.buttonTableCheck {
  color: #19be6b;
}
.headStyle {
  font-weight: 1000;
  font-size: 28px;
  line-height: 28px;
  color: #0a0a0a;
  text-align: center;
}
.headStyleTwo {
  font-weight: 800;
  font-size: 20px;
  line-height: 20px;
  color: #555555;
  text-align: center;
}
.formInput {
  float: left;
  height: 28px;
  margin-right: 50px;
  margin-bottom: 5px;
}
/deep/.el-input__icon {
  height: 100%;
  width: 25px;
  text-align: center;
  transition: all 0.3s;
  line-height: 35px;
}
.error-message {
  color: red;
  margin-top: 10px;
}
//---------------甘特图样式
th,
td {
  border: solid 1px rgb(60, 69, 104);
  height: 20px;
  width: 48px;
  margin: 0px;
  padding: 0px;
}
.planTable tr:hover {
  background-color: rgb(15 52 67);
}
.planTable {
  height: 390px;
  overflow: auto; /* 启用滚动条 */
}
.tr-hread {
  font-size: 12px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  font-weight: bold;
}

.box-content {
  position: relative;
  top: -18px;
}

.box-progress {
  background: black;
  height: 20px;
  position: absolute;
  float: left;
}

.box-text {
  position: absolute;
  float: left;
  width: 500px;
  top: 20px;
  //left: -50px;
  font-size: 12px;
  font-weight: bold;
}
.centered-text {
  text-align: center;
  vertical-align: middle;
}
</style>
