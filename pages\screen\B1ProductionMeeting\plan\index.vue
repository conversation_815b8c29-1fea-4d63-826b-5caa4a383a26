<template>
  <div class="content">
    <div class="desc-top">
      <div
        v-command="'/screen/B1ProductionMeeting/edit'"
        class="screen-btn"
        style="float: right; margin: 12px 12px 0 0;"
        @click="HeatRate.dialogVisible = true">
        <el-icon class="el-icon-edit-outline"/>
        操作
      </div>
      <span class="desc-tit">班组计划炉数</span>
      <span>大夜班 <b>{{ getByClasses('大夜班').classPlanFurnaceNum }}</b> 炉</span>
      <span>白班 <b>{{ getByClasses('白班').classPlanFurnaceNum }}</b> 炉</span>
      <span>小夜班 <b>{{ getByClasses('小夜班').classPlanFurnaceNum }}</b> 炉</span>
    </div>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <screen-border title="0#机炼钢计划">
            <template v-slot:headerRight/>
            <div
              ref="table1"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="showData"
                :row-class-name="tableRowClassName"
                :span-method="arraySpanMethod"
                :max-height="HeatRate.maxHeight"
                class="font-table big-table"
                border>
                <el-table-column
                  align="center"
                  property="classes"
                  show-overflow-tooltip
                  label="班次"
                />
                <el-table-column
                  align="center"
                  label="0#机">
                  <el-table-column
                    align="center"
                    property="planFurnaceNum0"
                    label="计划炉数"
                    width="100"/>
                  <el-table-column
                    align="center"
                    property="steelTypeSection0"
                    label="钢种及断面"
                    show-overflow-tooltip
                    min-width="95"/>
                  <el-table-column
                    align="center"
                    property="furnaceGroups0"
                    label="每组炉数"
                    width="100"/>
                  <el-table-column
                    align="center"
                    property="use0"
                    label="用途"
                    show-overflow-tooltip
                    width="60"/>
                  <el-table-column
                    align="center"
                    property="castingTimeRemarks0"
                    show-overflow-tooltip
                    label="浇铸时间"
                    min-width="100"/>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <screen-border title="1#机炼钢计划">
            <template v-slot:headerRight/>
            <div
              ref="table1"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="showData"
                :row-class-name="tableRowClassName"
                :span-method="arraySpanMethod"
                :max-height="HeatRate.maxHeight"
                class="font-table big-table"
                border>
                <el-table-column
                  align="center"
                  property="classes"
                  show-overflow-tooltip
                  label="班次"
                />
                <el-table-column
                  align="center"
                  label="1#机">
                  <el-table-column
                    align="center"
                    property="planFurnaceNum1"
                    label="计划炉数"
                    width="100"/>
                  <el-table-column
                    align="center"
                    property="steelTypeSection1"
                    label="钢种及断面"
                    show-overflow-tooltip
                    min-width="95"/>
                  <el-table-column
                    align="center"
                    property="furnaceGroups1"
                    label="每组炉数"
                    width="100"/>
                  <el-table-column
                    align="center"
                    property="use1"
                    label="用途"
                    show-overflow-tooltip
                    width="60"/>
                  <el-table-column
                    align="center"
                    property="castingTimeRemarks1"
                    show-overflow-tooltip
                    label="浇铸时间"
                    min-width="100"/>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <screen-border title="2#机炼钢计划">
            <template v-slot:headerRight/>
            <div
              ref="table1"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="showData"
                :row-class-name="tableRowClassName"
                :span-method="arraySpanMethod"
                :max-height="HeatRate.maxHeight"
                class="font-table big-table"
                border>
                <el-table-column
                  align="center"
                  property="classes"
                  show-overflow-tooltip
                  label="班次"
                />
                <el-table-column
                  align="center"
                  label="2#机">
                  <el-table-column
                    align="center"
                    property="planFurnaceNum2"
                    label="计划炉数"
                    width="100"/>
                  <el-table-column
                    align="center"
                    property="steelTypeSection2"
                    label="钢种及断面"
                    show-overflow-tooltip
                    min-width="95"/>
                  <el-table-column
                    align="center"
                    property="furnaceGroups2"
                    label="每组炉数"
                    width="100"/>
                  <el-table-column
                    align="center"
                    property="use2"
                    label="用途"
                    show-overflow-tooltip
                    width="60"/>
                  <el-table-column
                    align="center"
                    property="castingTimeRemarks2"
                    show-overflow-tooltip
                    label="浇铸时间"
                    min-width="100"/>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <screen-border title="3#机炼钢计划">
            <template v-slot:headerRight/>
            <div
              ref="table1"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="showData"
                :row-class-name="tableRowClassName"
                :span-method="arraySpanMethod"
                :max-height="HeatRate.maxHeight"
                class="font-table big-table"
                border>
                <el-table-column
                  align="center"
                  property="classes"
                  show-overflow-tooltip
                  label="班次"
                />
                <el-table-column
                  align="center"
                  label="3#机">
                  <el-table-column
                    align="center"
                    property="planFurnaceNum3"
                    label="计划炉数"
                    width="100"/>
                  <el-table-column
                    align="center"
                    property="steelTypeSection3"
                    label="钢种及断面"
                    show-overflow-tooltip
                    min-width="95"/>
                  <el-table-column
                    align="center"
                    property="furnaceGroups3"
                    label="每组炉数"
                    width="100"/>
                  <el-table-column
                    align="center"
                    property="use3"
                    label="用途"
                    show-overflow-tooltip
                    width="60"/>
                  <el-table-column
                    align="center"
                    property="castingTimeRemarks3"
                    show-overflow-tooltip
                    label="浇铸时间"
                    min-width="100"/>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div 
      v-if="getByClasses('备注').classPlanFurnaceNum" 
      class="desc-bottom">
      备注：
      {{ getByClasses('备注').classPlanFurnaceNum }}
    </div>
    <!--炼钢-->
    <el-dialog
      :visible.sync="HeatRate.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="SyncData(steelmakingPlanTask)">
              手动同步数据
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('HeatRate')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importHeatRateData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportHeatRate">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveHeatRate">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          炼钢计划
        </div>
      </template>
      <el-form
        v-loading="syncLoading" 
        :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="HeatRate.gridData"
          :row-class-name="tableRowClassName"
          :span-method="arraySpanMethod"
          border>
          <el-table-column
            property="classes"
            label="班次">
            <template v-slot="{ row }">
              <el-input v-model="row.classes" />
            </template>
          </el-table-column>
          <el-table-column
            property="classPlanFurnaceNum"
            label="班次计划炉数"
            min-width="100">
            <template v-slot="{ row }">
              <el-input v-model="row.classPlanFurnaceNum" />
            </template>
          </el-table-column>
          <el-table-column label="0#机">
            <el-table-column
              property="planFurnaceNum0"
              label="当班计划炉数"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.planFurnaceNum0" />
              </template>
            </el-table-column>
            <el-table-column
              property="steelTypeSection0"
              label="钢种及断面"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.steelTypeSection0" />
              </template>
            </el-table-column>
            <el-table-column
              property="furnaceGroups0"
              label="每组炉数"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.furnaceGroups0" />
              </template>
            </el-table-column>
            <el-table-column
              property="use0"
              label="用途"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.use0" />
              </template>
            </el-table-column>
            <el-table-column
              property="castingTimeRemarks0"
              label="浇铸时间 (Min)备注"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.castingTimeRemarks0" />
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="1#机">
            <el-table-column
              property="planFurnaceNum1"
              label="当班计划炉数"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.planFurnaceNum1" />
              </template>
            </el-table-column>
            <el-table-column
              property="steelTypeSection1"
              label="钢种及断面"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.steelTypeSection1" />
              </template>
            </el-table-column>
            <el-table-column
              property="furnaceGroups1"
              label="每组炉数"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.furnaceGroups1" />
              </template>
            </el-table-column>
            <el-table-column
              property="use1"
              label="用途"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.use1" />
              </template>
            </el-table-column>
            <el-table-column
              property="castingTimeRemarks1"
              label="浇铸时间 (Min)备注"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.castingTimeRemarks1" />
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="2#机">
            <el-table-column
              property="planFurnaceNum2"
              label="当班计划炉数"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.planFurnaceNum2" />
              </template>
            </el-table-column>
            <el-table-column
              property="steelTypeSection2"
              label="钢种及断面"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.steelTypeSection2" />
              </template>
            </el-table-column>
            <el-table-column
              property="furnaceGroups2"
              label="每组炉数"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.furnaceGroups2" />
              </template>
            </el-table-column>
            <el-table-column
              property="use2"
              label="用途"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.use2" />
              </template>
            </el-table-column>
            <el-table-column
              property="castingTimeRemarks2"
              label="浇铸时间 (Min)备注"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.castingTimeRemarks2" />
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="2#机">
            <el-table-column
              property="planFurnaceNum3"
              label="当班计划炉数"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.planFurnaceNum3" />
              </template>
            </el-table-column>
            <el-table-column
              property="steelTypeSection3"
              label="钢种及断面"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.steelTypeSection3" />
              </template>
            </el-table-column>
            <el-table-column
              property="furnaceGroups3"
              label="每组炉数"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.furnaceGroups3" />
              </template>
            </el-table-column>
            <el-table-column
              property="use3"
              label="用途"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.use3" />
              </template>
            </el-table-column>
            <el-table-column
              property="castingTimeRemarks3"
              label="浇铸时间 (Min)备注"
              min-width="100">
              <template v-slot="{ row }">
                <el-input v-model="row.castingTimeRemarks3" />
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index, 'HeatRate')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEdit"
          class="screen-btn"
          @click="addGridData('HeatRate')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { batchUpdateResource } from '@/api/system'
import lodash from 'lodash'
import { post } from '@/lib/Util'
import { findSteelmakingPlanByDate, saveSteelmakingPlan } from '@/api/screen'
import { math } from '@/lib/Math'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
export default {
  name: 'Plan',
  components: { SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      HeatRate: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      }
    }
  },
  computed: {
    showData: function() {
      return this.HeatRate.showGridData.filter(item => item.classes !== '备注')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getHeatRate()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    calculateHeight() {
      this.HeatRate.maxHeight = this.$refs.table1.offsetHeight
    },
    tableRowClassName() {
      return ''
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          classes: 'A',
          classPlanFurnaceNum: 'B',
          planFurnaceNum0: 'C',
          steelTypeSection0: 'D',
          furnaceGroups0: 'E',
          use0: 'F',
          castingTimeRemarks0: 'G',
          planFurnaceNum1: 'H',
          steelTypeSection1: 'I',
          furnaceGroups1: 'J',
          use1: 'K',
          castingTimeRemarks1: 'L',
          planFurnaceNum2: 'M',
          steelTypeSection2: 'N',
          furnaceGroups2: 'O',
          use2: 'P',
          castingTimeRemarks2: 'Q',
          planFurnaceNum3: 'R',
          steelTypeSection3: 'S',
          furnaceGroups3: 'T',
          use3: 'U',
          castingTimeRemarks3: 'V'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        sheet.shift()
        // 表格信息
        this.HeatRate.gridData = sheet
          .filter(item => item.classes && item.classes !== '班次')
          .map(item => {
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportHeatRate() {
      const data = [
        {
          classes: '炼钢计划',
          classPlanFurnaceNum: '',
          planFurnaceNum0: '',
          steelTypeSection0: '',
          furnaceGroups0: '',
          use0: '',
          castingTimeRemarks0: '',
          planFurnaceNum1: '',
          steelTypeSection1: '',
          furnaceGroups1: '',
          use1: '',
          castingTimeRemarks1: '',
          planFurnaceNum2: '',
          steelTypeSection2: '',
          furnaceGroups2: '',
          use2: '',
          castingTimeRemarks2: '',
          planFurnaceNum3: '',
          steelTypeSection3: '',
          furnaceGroups3: '',
          use3: '',
          castingTimeRemarks3: ''
        },
        {
          classes: '班次',
          classPlanFurnaceNum: '班计划炉数',
          planFurnaceNum0: '0#机',
          steelTypeSection0: '',
          furnaceGroups0: '',
          use0: '',
          castingTimeRemarks0: '',
          planFurnaceNum1: '1#机',
          steelTypeSection1: '',
          furnaceGroups1: '',
          use1: '',
          castingTimeRemarks1: '',
          planFurnaceNum2: '2#机',
          steelTypeSection2: '',
          furnaceGroups2: '',
          use2: '',
          castingTimeRemarks2: '',
          planFurnaceNum3: '3#机',
          steelTypeSection3: '',
          furnaceGroups3: '',
          use3: '',
          castingTimeRemarks3: ''
        },
        {
          classes: '班次',
          classPlanFurnaceNum: '班计划炉数',
          planFurnaceNum0: '当班计划炉数',
          steelTypeSection0: '钢种及断面',
          furnaceGroups0: '每组炉数',
          use0: '用途',
          castingTimeRemarks0: '浇铸时间',
          planFurnaceNum1: '当班计划炉数',
          steelTypeSection1: '钢种及断面',
          furnaceGroups1: '每组炉数',
          use1: '用途',
          castingTimeRemarks1: '浇铸时间',
          planFurnaceNum2: '当班计划炉数',
          steelTypeSection2: '钢种及断面',
          furnaceGroups2: '每组炉数',
          use2: '用途',
          castingTimeRemarks2: '浇铸时间',
          planFurnaceNum3: '当班计划炉数',
          steelTypeSection3: '钢种及断面',
          furnaceGroups3: '每组炉数',
          use3: '用途',
          castingTimeRemarks3: '浇铸时间'
        }
      ].concat(_.cloneDeep(this.HeatRate.gridData))
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `炼钢计划（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {
              '!merges': LAY_EXCEL.makeMergeConfig([
                ['A1', 'V1'],
                ['A2', 'A3'],
                ['B2', 'B3'],
                ['C2', 'G2'],
                ['H2', 'L2'],
                ['M2', 'Q2'],
                ['R2', 'V2']
              ])
            }
          }
        }
      )
    },
    getHeatRate() {
      post(findSteelmakingPlanByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.HeatRate.showGridData = res.data.map(item => {
          if (item.classes === '备注') {
            item.classplanfurnacenum = item.remarks
          }
          return {
            classes: item.classes,
            classPlanFurnaceNum: item.classplanfurnacenum,
            planFurnaceNum0: item.planfurnacenum0,
            steelTypeSection0: item.steeltypesection0,
            furnaceGroups0: item.furnacegroups0,
            use0: item.use0,
            castingTimeRemarks0: item.castingtimeremarks0,
            planFurnaceNum1: item.planfurnacenum1,
            steelTypeSection1: item.steeltypesection1,
            furnaceGroups1: item.furnacegroups1,
            use1: item.use1,
            castingTimeRemarks1: item.castingtimeremarks1,
            planFurnaceNum2: item.planfurnacenum2,
            steelTypeSection2: item.steeltypesection2,
            furnaceGroups2: item.furnacegroups2,
            use2: item.use2,
            castingTimeRemarks2: item.castingtimeremarks2,
            planFurnaceNum3: item.planfurnacenum3,
            steelTypeSection3: item.steeltypesection3,
            furnaceGroups3: item.furnacegroups3,
            use3: item.use3,
            castingTimeRemarks3: item.castingtimeremarks3,
            classPlanFurnaceNumTotal: item.classesclassplanfurnacenum,
            planFurnaceNumTotal: item.classesclassplanfurnacenum
          }
        })
        this.HeatRate.gridData = lodash.cloneDeep(this.HeatRate.showGridData)
      })
    },
    saveHeatRate() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.HeatRate.gridData.map(item => {
          if (item.classes === '备注') {
            item.remarks = item.classPlanFurnaceNum
            item.classPlanFurnaceNum = null
          }
          item.setDate = this.cDate
          return item
        })
      }
      post(saveSteelmakingPlan, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.HeatRate.dialogVisible = false
          this.getHeatRate()
        }
      })
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
      if (row.classes === '备注') {
        if (columnIndex === 1) {
          return [1, 21]
        } else if (columnIndex > 1) {
          return [0, 0]
        }
      }
    },
    importHeatRateData(date) {
      post(findSteelmakingPlanByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.HeatRate.gridData = res.data.map(item => {
          if (item.classes === '备注') {
            item.classplanfurnacenum = item.remarks
          }
          return {
            classes: item.classes,
            classPlanFurnaceNum: item.classplanfurnacenum,
            planFurnaceNum0: item.planfurnacenum0,
            steelTypeSection0: item.steeltypesection0,
            furnaceGroups0: item.furnacegroups0,
            use0: item.use0,
            castingTimeRemarks0: item.castingtimeremarks0,
            planFurnaceNum1: item.planfurnacenum1,
            steelTypeSection1: item.steeltypesection1,
            furnaceGroups1: item.furnacegroups1,
            use1: item.use1,
            castingTimeRemarks1: item.castingtimeremarks1,
            planFurnaceNum2: item.planfurnacenum2,
            steelTypeSection2: item.steeltypesection2,
            furnaceGroups2: item.furnacegroups2,
            use2: item.use2,
            castingTimeRemarks2: item.castingtimeremarks2,
            planFurnaceNum3: item.planfurnacenum3,
            steelTypeSection3: item.steeltypesection3,
            furnaceGroups3: item.furnacegroups3,
            use3: item.use3,
            castingTimeRemarks3: item.castingtimeremarks3,
            classPlanFurnaceNumTotal: item.classesclassplanfurnacenum,
            planFurnaceNumTotal: item.classesclassplanfurnacenum
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    getByClasses(classes) {
      const match = this.HeatRate.showGridData.find(
        item => item.classes === classes
      )
      return match ? match : {}
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.desc-top {
  display: inline-block;
  position: relative;
  top: -15px;
  font-size: 0;
  line-height: 0;
  border: 1px solid #117090;
  color: rgba(216, 231, 250, 0.95);
  b {
    font-weight: bold;
    font-size: 28px;
    line-height: 50px;
    color: #fff;
  }
  span {
    display: inline-block;
    vertical-align: middle;
    font-size: 16px;
    line-height: 50px;
    height: 50px;
    min-width: 160px;
    padding: 0 20px;
    border-right: 1px solid #117090;
  }
  .desc-tit {
    background-color: #117090;
    color: rgba(216, 231, 250, 0.95);
    text-align: center;
  }
}
.desc-bottom {
  padding: 20px 0;
  font-size: 22px;
}
</style>
