const pkg = require('./package')
module.exports = {
  // mode: 'universal',
  mode: 'spa',
  /*
  ** Headers of the page
  */
  head: {
    title: '目标管控',
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { hid: 'description', name: 'description', content: pkg.description }
    ],
    link: [{ rel: 'icon', href: 'iplat.ico' }]
  },

  /*
  ** Customize the progress-bar color
  */
  // loading: { color: '#fff' },
  loading: false,

  /*
  ** Global CSS
  */
  css: [
    'element-ui/lib/theme-chalk/index.css',
    '@/assets/icon/iconfont.css',
    '@/assets/css/reset.css'
  ],

  /*
  ** Plugins to load before mounting the App
  */
  plugins: [
    '@/plugins/element-ui',
    '@/plugins/directive',
    '@/plugins/api',
    '@/plugins/filters',
    '@/plugins/i18n',
    '@/plugins/request',
    { src: '@/plugins/interceptors', ssr: false },
    { src: '~/plugins/route', ssr: false },
    { src: '~/plugins/persistedstate.js', ssr: false },
    { src: '~/plugins/vue-echarts.js', ssr: false },
    { src: '~/plugins/vue-tools.js', ssr: false },
    { src: '~/plugins/event-bus.js', ssr: false },
    { src: '@/plugins/component', ssr: false }
  ],
  /*
  ** Nuxt.js modules
  */
  modules: [
    // Doc: https://github.com/nuxt-community/axios-module#usage
    '@nuxtjs/axios',
    '@nuxtjs/style-resources'
  ],
  styleResources: {
    less: '@/assets/css/global.less'
  },

  server: {
    port: 9720 || process.env.PORT,
    host: '0.0.0.0' || process.env.BASE_URL
  },
  /*
  ** Axios module configuration
  */
  env: {
    baseUrl: 'http://************:9800/'
    // 本地
    // baseUrl: 'http://************:9690/'
  },
  axios: {
    baseURL: 'http://************:9800/', //设置统一URL,关闭代理时使用.
    // 本地
    // baseUrl: 'http://************:9690/',
    timeout: 60000,
    retry: { retries: 3 },
    proxy: true // 表示开启代理
  },
  proxy: {
    '/api/it': {
      target: 'http://bcapi.nisco.cn/',
      pathRewrite: { '^/api/it/': '' },
      changeOrigin: true,
      logLevel: 'debug'
    },
    '/api/ht': {
      target: 'http://************:9704/',
      pathRewrite: { '^/api/ht/': '' },
      changeOrigin: true,
      logLevel: 'debug'
    },
    '/api/pcdp': {
      target: 'http://************:9088/',
      pathRewrite: { '^/api/pcdp/': '' },
      changeOrigin: true,
      logLevel: 'debug'
    },
    '/api/quality': {
      //匹配代理对象
      target: 'http://172.25.63.188:9170/', // 目标接口域名
      // 本地
      // target: 'http://************:9690/',
      pathRewrite: { '^/api/quality/': '' }, // 匹配 /quality 替换成 /
      changeOrigin: true, // 表示是否跨域
      logLevel: 'debug'
    },
    '/api/orgApi': {
      //匹配代理对象
      target: 'http://172.25.63.188:9084/', // 目标接口域名
      // 本地
      // target: 'http://************:9690/',
      pathRewrite: { '^/api/orgApi': 'orgApi' }, // 匹配 /api 替换成 /
      changeOrigin: true, // 表示是否跨域
      logLevel: 'debug'
    },
    '/api/orgApi2': {
      //匹配代理对象
      target: 'http://172.25.63.188:9084/', // 目标接口域名
      // 本地
      // target: 'http://************:9690/',
      pathRewrite: { '^/api/orgApi2': 'orgApi2' }, // 匹配 /api 替换成 /
      changeOrigin: true, // 表示是否跨域
      logLevel: 'debug'
    },
    '/api/ztkb': {
      //匹配代理对象
      target: 'http://172.25.63.188:9084/', // 目标接口域名
      // 本地
      // target: 'http://************:9690/',
      pathRewrite: { '^/api/ztkb': 'ztkb' }, // 匹配 /api 替换成 /
      changeOrigin: true, // 表示是否跨域
      logLevel: 'debug'
    },
    '/minoApi': {
      //匹配代理对象
      target: 'http://172.25.63.72:9123/', // 目标接口域名
      pathRewrite: { '^/minoApi': '' }, // 匹配 /api 替换成 /   minoApi
      changeOrigin: true, // 表示是否跨域
      logLevel: 'debug'
    },
    '/minoApi2': {
      //匹配代理对象
      target: 'http://172.25.63.72:9124/', // 目标接口域名
      pathRewrite: { '^/minoApi2': 'minoApi2' }, // 匹配 /api 替换成 /
      changeOrigin: true, // 表示是否跨域
      logLevel: 'debug'
    },
    '/api': {
      //匹配代理对象
      target: 'http://************:9800/', // 目标接口域名
      // 本地
      // target: 'http://************:9690/',
      pathRewrite: { '^/api/': '' }, // 匹配 /api 替换成 /
      changeOrigin: true, // 表示是否跨域
      logLevel: 'debug'
    }
  },
  router: {
    middleware: 'redirect'
  },
  /*
  ** Build configuration
  */
  build: {
    /*
    ** You can extend webpack config here
    */
    transpile: ['three'],
    uglify: {
      uglifyOptions: {
        compress: true
      },
      cache: true
    },
    extend(config, ctx) {
      // Run ESLint on save
      if (ctx.isDev && ctx.isClient) {
        config.module.rules.push({
          enforce: 'pre',
          test: /\.(js|vue)$/,
          loader: 'eslint-loader',
          exclude: /(node_modules)/
        })
      }
    }
  }
}
