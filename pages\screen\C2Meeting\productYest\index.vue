<template>
  <div class="content">
    <div class="content-item">
      <screen-border-multi>
        <template v-slot:title>
          <div class="tabs-class">
            <div
              v-for="(item) in tabList"
              :key="item.id"
              :class="{'tab-pane-active': active === item.id}"
              class="tab-pane"
              @click="active = item.id">
              <div class="tab-pane-title-class">
                <div>{{ item.title }}</div>
                <div
                  v-if="active === item.id"
                  class="tab-pane-img">
                  <img
                    class="tab-pane-img2"
                    src="@/assets/images/screen/tab-pane-active-line2.png"
                    alt="">
                  <img
                    class="tab-pane-img1"
                    src="@/assets/images/screen/tab-pane-active-line.png"
                    alt="">
                </div>
              </div>
            </div>
          </div>
        </template>
        <custom-table-noheader17
          v-if="active === '1'"
          :title="'热轧轧制情况'"
          :key="'productYes1'"
          :setting="tableObj1.setting"
          :url-list="tableObj1.url.list"
          :url-save="tableObj1.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader18
          v-if="active === '9'"
          :title="'热处理生产情况'"
          :key="'productYes1'"
          :setting="tableObj9.setting"
          :url-list="tableObj9.url.list"
          :url-save="tableObj9.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '2'"
          :key="'productYes2'"
          :title="'精整剪切情况'"
          :setting="tableObj2.setting"
          :url-list="tableObj2.url.list"
          :url-save="tableObj2.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '3'"
          :key="'productYes3'"
          :title="'剪切非计划率'"
          :setting="tableObj3.setting"
          :url-list="tableObj3.url.list"
          :url-save="tableObj3.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '4'"
          :key="'productYes4'"
          :title="'生产作业情况汇总'"
          :setting="tableObj4.setting"
          :url-list="tableObj4.url.list"
          :url-save="tableObj4.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '5'"
          :key="'productYes5'"
          :title="'生产计划完成情况报表'"
          :setting="tableObj5.setting"
          :url-list="tableObj5.url.list"
          :url-save="tableObj5.url.save"
          :select-date="selectDate"/>
        <template
          v-if="active === '6'">
          <div 
            class="right inp-name" 
            style="float:right;margin-bottom:5px">
            <el-date-picker
              v-model="device.dateTime"
              :clearable="false"
              type="daterange"
              style="background-color:rgb(7 44 59);border:1px solid rgb(7 44 59);color:#fff"
              start-placeholder="开始时间"
              end-placeholder="结束时间"/>
            <span
              class="screen-btn"
              @click="loadData()">
              查询
            </span>
            <span
              class="screen-btn"
              @click="exportTable">
              导出
            </span>
          </div>
          <el-table
            v-loading="loading"
            :data="deviceSetting.dataList"
            :size="'medium'"
            class="center-table font-big-table"
            border>
            <template
              v-for="(item, index) in deviceSetting.setting">
              <template v-if="item.show !== false">
                <el-table-column
                  v-if="item.children"
                  :key="index"
                  :width="item.width || ''"
                  :property="item.keySave"
                  :label="item.label"
                  :align="item.align">
                  <template
                    v-for="(cItem, cIndex) in item.children">
                    <template v-if="item.inputType === 'textarea'">
                      <el-table-column
                        :key="cIndex"
                        :width="cItem.width || ''"
                        :property="cItem.keySave"
                        :label="cItem.label"
                        :align="cItem.align">
                        <template v-slot="{ row }">
                          <div
                            slot="content"
                            v-html="formatText(row[cItem.keySave], cItem.split)"
                          />
                        </template>
                      </el-table-column>
                    </template>
                    <template v-else>
                      <el-table-column
                        :key="cIndex"
                        :width="cItem.width || ''"
                        :property="cItem.keySave"
                        :label="cItem.label"
                        :align="cItem.align"/>
                    </template>
                  </template>
                </el-table-column>
                <template v-else>
                  <el-table-column
                    v-if="item.type === 'index'"
                    :key="index"
                    :label="item.label"
                    type="index"
                    width="100"
                  />
                  <template v-else>
                    <template v-if="item.inputType === 'textarea'">
                      <el-table-column
                        :key="index"
                        :width="item.width || ''"
                        :property="item.keySave"
                        :label="item.label"
                        :align="item.align">
                        <template v-slot="{ row }">
                          <div
                            slot="content"
                            v-html="formatText(row[item.keySave], item.split)"
                          />
                        </template>
                      </el-table-column>
                    </template>
                    <template v-else>
                      <el-table-column
                        :key="index"
                        :width="item.width || ''"
                        :property="item.keySave"
                        :label="item.label"
                        :align="item.align"/>
                    </template>
                  </template>
                </template>
              </template>
            </template>
          </el-table>
        </template>
        <custom-table-noheader16
          v-if="active === '7'"
          :key="'productYes5'"
          :title="'会议记录'"
          :setting="tableObj7.setting"
          :url-list="tableObj7.url.list"
          :url-save="tableObj7.url.save"
          :select-date="selectDate"/>
        <custom-table-noheader
          v-if="active === '8'"
          :key="'productYes5'"
          :title="'班组生产情况'"
          :setting="tableObj8.setting"
          :url-list="tableObj8.url.list"
          :url-save="tableObj8.url.save"
          :select-date="selectDate"/>
      </screen-border-multi>
    </div>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import {
  qmsQualitySupervisionQuery,
  qmsQualitySupervisionSave
} from '@/api/screen'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/C2Meeting/component/custom-table'
import {
  cutUnplannedRateFind,
  cutUnplannedRateSave,
  findEquipmentOperation,
  finishingShearingFind,
  finishingShearingSave,
  hotRollingSituationFind,
  hotRollingSituationSave,
  productionSituationDayFind,
  productionSituationDaySave,
  PSCDayFind,
  PSCDaySave,
  MeetingRecordFind,
  MeetingRecordSave,
  WorkTeamProductionFind,
  WorkTeamProductionSave,
  RCLSituationFind,
  RCLSituationSave
} from '@/api/screenC2'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import CustomTableNoheader from '@/pages/screen/C2Meeting/component/custom-table-noheader'
import CustomTableNoheader16 from '@/pages/screen/C2Meeting/component/custom-table-noheader16'
import CustomTableNoheader17 from '@/pages/screen/C2Meeting/component/custom-table-noheader17'
import CustomTableNoheader18 from '@/pages/screen/C2Meeting/component/custom-table-noheader18'
import { post } from '@/lib/Util'
import { expenseDetail } from '@/api/device'
export default {
  name: 'productYest',
  components: {
    CustomTableNoheader,
    CustomTableNoheader16,
    CustomTableNoheader17,
    CustomTableNoheader18,
    ScreenBorderMulti,
    CustomTable,
    SingleBarsChart
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      active: '1',
      loading: false,
      tabList: [
        {
          id: '1',
          active: true,
          title: '热轧轧制情况'
        },
        {
          id: '2',
          active: false,
          title: '精整剪切情况'
        },
        {
          id: '9',
          active: false,
          title: '热处理生产情况'
        },
        {
          id: '3',
          active: false,
          title: '剪切非计划率'
        },
        {
          id: '4',
          active: false,
          title: '生产作业情况汇总'
        },
        {
          id: '5',
          active: false,
          title: '生产计划完成情况报表'
        },
        {
          id: '6',
          active: false,
          title: '设备运行'
        },
        {
          id: '7',
          active: false,
          title: '会议记录'
        },
        {
          id: '8',
          active: false,
          title: '班组生产情况'
        }
      ],
      tableObj1: {
        url: {
          save: hotRollingSituationSave,
          list: hotRollingSituationFind
        },
        setting: [
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次',
            width: '60'
          },
          {
            keyQuery: 'groups',
            keySave: 'groups',
            label: '班',
            width: '60'
          },
          {
            keyQuery: 'plan',
            keySave: 'plan',
            label: '计划（吨）'
          },
          {
            keyQuery: 'production',
            keySave: 'production',
            label: '产量（吨）'
          },
          {
            keyQuery: 'reason',
            keySave: 'reason',
            label: '未完成原因'
          },
          {
            keyQuery: 'impacttime',
            keySave: 'impactTime',
            label: '影响时间'
          },
          {
            keyQuery: 'impactton',
            keySave: 'impactTon',
            label: '影响吨位'
          },
          {
            keyQuery: 'responsibilityunit',
            keySave: 'responsibilityUnit',
            label: '责任单位'
          }
        ]
      },
      tableObj2: {
        url: {
          save: finishingShearingSave,
          list: finishingShearingFind
        },
        setting: [
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次',
            width: '60'
          },
          {
            keyQuery: 'groups',
            keySave: 'groups',
            label: '班',
            width: '60'
          },
          {
            keyQuery: 'plancut',
            keySave: 'planCut',
            label: '计划剪切块数'
          },
          {
            keyQuery: 'realitycut',
            keySave: 'realityCut',
            label: '实际剪切块数'
          },
          {
            keyQuery: 'psjwarehousing',
            keySave: 'psjWarehousing',
            label: 'pjs入库'
          },
          {
            keyQuery: 'faultdescription',
            keySave: 'faultDescription',
            type: 'textarea',
            label: '故障情况说明'
          }
        ]
      },
      tableObj3: {
        url: {
          save: cutUnplannedRateSave,
          list: cutUnplannedRateFind
        },
        setting: [
          {
            keyQuery: 'project',
            keySave: 'project',
            label: '项目'
          },
          {
            keyQuery: 'situationday',
            keySave: 'situationDay',
            label: '当日情况'
          },
          {
            keyQuery: 'accumulatemonth',
            keySave: 'accumulateMonth',
            label: '本月累计'
          }
        ]
      },
      tableObj4: {
        url: {
          save: productionSituationDaySave,
          list: productionSituationDayFind
        },
        setting: [
          {
            keyQuery: 'project',
            keySave: 'project',
            label: '项目'
          },
          {
            keyQuery: 'plan',
            keySave: 'plan',
            label: '计划'
          },
          {
            keyQuery: 'reality',
            keySave: 'reality',
            label: '实际'
          },
          {
            keyQuery: 'reason',
            keySave: 'reason',
            label: '未完成计划原因'
          }
        ]
      },
      tableObj5: {
        url: {
          save: PSCDaySave,
          list: PSCDayFind
        },
        setting: [
          {
            keyQuery: 'project',
            keySave: 'project',
            label: '项目'
          },
          {
            keyQuery: 'plan',
            keySave: 'plan',
            label: '计划'
          },
          {
            keyQuery: 'big',
            keySave: 'big',
            label: '大'
          },
          {
            keyQuery: 'white',
            keySave: 'white',
            label: '白'
          },
          {
            keyQuery: 'small',
            keySave: 'small',
            label: '小'
          },
          {
            keyQuery: 'amountto',
            keySave: 'amountTo',
            label: '合计'
          }
        ]
      },
      tableObj7: {
        url: {
          save: MeetingRecordSave,
          list: MeetingRecordFind
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'meetingDate',
            keySave: 'meetingDate',
            label: '日期',
            width: 160
          },
          {
            keyQuery: 'meetingType',
            keySave: 'meetingType',
            label: '类型',
            width: 100
          },
          {
            keyQuery: 'meetingInfo',
            keySave: 'meetingInfo',
            label: '内容'
          }

          //  {
          //    keyQuery: 'setdate',
          //    keySave: 'setdate',
          //    label: '晨会日期'
          //  }
        ]
      },
      tableObj8: {
        url: {
          save: WorkTeamProductionSave,
          list: WorkTeamProductionFind
        },
        setting: [
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次'
          },
          {
            keyQuery: 'groups',
            keySave: 'groups',
            label: '班组'
          },
          {
            keyQuery: 'comment1',
            keySave: 'comment1',
            label: '当班生产总结'
          },
          {
            keyQuery: 'comment5',
            keySave: 'comment5',
            label: '离线生产情况'
          }
          //  {
          //    keyQuery: 'small',
          //    keySave: 'small',
          //    label: '小'
          //  },
          //  {
          //    keyQuery: 'amountto',
          //    keySave: 'amountTo',
          //    label: '合计'
          //  }
        ]
      },
      tableObj9: {
        url: {
          save: RCLSituationSave,
          list: RCLSituationFind
        },
        setting: [
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次',
            width: '60'
          },
          {
            keyQuery: 'groups',
            keySave: 'groups',
            label: '班',
            width: '60'
          },
          {
            keyQuery: 'prcLine',
            keySave: 'prcLine',
            label: '热处理炉'
          },
          {
            keyQuery: 'productionNum',
            keySave: 'productionNum',
            label: '生产块数'
          },
          {
            keyQuery: 'productionWgt',
            keySave: 'productionWgt',
            label: '生产吨位'
          },
          {
            keyQuery: 'faultDes',
            keySave: 'faultDes',
            label: '故障情况说明'
          }
        ]
      },
      device: { dateTime: [] },
      deviceSetting: {
        url: {
          list: findEquipmentOperation
        },
        dataList: [],
        setting: [
          {
            keyQuery: 'T_DATE_FROM',
            keySave: 'T_DATE_FROM',
            label: '停机开始'
          },
          {
            keyQuery: 'T_DATE_TO',
            keySave: 'T_DATE_TO',
            label: '停机结束'
          },
          {
            keyQuery: 'TIMES',
            keySave: 'TIMES',
            label: '影响时间'
          },
          {
            keyQuery: 'FAULT_DESCRIPTION',
            keySave: 'FAULT_DESCRIPTION',
            label: '事故描述'
          },
          {
            keyQuery: 'CHG_GRD_DEP',
            keySave: 'CHG_GRD_DEP',
            label: '责任单位'
          }
        ]
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
      this.loadData()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.device.dateTime[0] = this.$moment(this.selectDate).subtract(1, 'day')
    this.device.dateTime[1] = this.selectDate
    this.loadData()
  },
  methods: {
    loadData() {
      // console.log('aaa', this.device.dateTime)
      post(this.deviceSetting.url.list, {
        startTime: this.$moment(this.device.dateTime[0]).format('YYYYMMDD'),
        endTime: this.$moment(this.device.dateTime[1]).format('YYYYMMDD')
      }).then(res => {
        this.deviceSetting.dataList = res.rows
      })
    },
    // 导出表格
    exportTable() {
      const obj = {}
      this.deviceSetting.setting.forEach((item, index) => {
        obj[item.keySave] = item.label
      })
      const data = [obj].concat(
        _.cloneDeep(
          this.deviceSetting.dataList.map(item => {
            const objRow = {}
            this.deviceSetting.setting.forEach(set => {
              objRow[set.keySave] = item[set.keySave]
            })
            return objRow
          })
        )
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `设备运行.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.inp-name {
  color: #fff;
  margin-right: 5px;
  font-size: 14px;
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
::v-deep .el-date-editor .el-range-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: none;
  outline: 0;
  display: inline-block;
  height: 100%;
  margin: 0;
  padding: 0;
  width: 39%;
  text-align: center;
  font-size: 16px;
  color: #fdfdfd;
}
</style>
