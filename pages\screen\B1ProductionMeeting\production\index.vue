<template>
  <div class="content">
    <div class="content-item">
      <custom-table
        :title="'产量指标'"
        :setting="tableObj1.setting"
        :url-list="tableObj1.url.list"
        :url-save="tableObj1.url.save"
        :select-date="selectDate"
        :table-class="'big-table'"/>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <custom-table
        :title="'产量指标'"
        :setting="tableObj1.setting"
        :url-list="tableObj1.url.list"
        :url-save="tableObj1.url.save"
        :select-date="selectDate"
        :table-class="'big-table'"/>
    </div>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/B1ProductionMeeting/component/custom-table'
import { fireCuttingFind, fireCuttingSave } from '@/api/screenC2'
export default {
  name: 'qualityAbnormal',
  components: { CustomTable, SingleBarsChart },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      tableObj1: {
        url: {
          save: fireCuttingSave,
          list: fireCuttingFind
        },
        setting: [
          {
            keyQuery: 'classification',
            keySave: 'classification',
            label: '班次'
          },
          {
            keyQuery: 'plan',
            keySave: 'plan',
            label: '时间'
          },
          {
            keyQuery: 'piece',
            keySave: 'piece',
            label: '情况描述'
          },
          {
            keyQuery: 'wgt',
            keySave: 'wgt',
            label: '责任单位'
          },
          {
            keyQuery: 'reason',
            keySave: 'reason',
            label: '考核情况'
          }
        ]
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  methods: {}
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
