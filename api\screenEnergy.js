const path = 'mesAPI/'
const pathEnergy = 'ems/'
//
export const FSMONTHTOSUsingPOST = pathEnergy + 'generator/indexELE/FSMONTHTOS'
// 峰谷平饼图
export const ELEFGP = pathEnergy + '/generator/indexELE/ELEFGP'
// 工序能耗 工序成本
export const ecAndCostB1 = pathEnergy + '/ec-and-cost/b1'
// 工序能耗 工序成本
export const ecAndCostGs = pathEnergy + '/ec-and-cost/gs'
// 工序能耗 工序成本
export const ecAndCostC3 = pathEnergy + '/ec-and-cost/c3'
// 工序能耗 工序成本
export const ecAndCostC2 = pathEnergy + '/ec-and-cost/c2'
// 工序能耗 工序成本
export const ecAndCostC1 = pathEnergy + '/ec-and-cost/c1'
// 煤气回收率饼图
export const GASRECOVERY = pathEnergy + '/generator/indexFS/GASRECOVERY'
// 金石能介单耗
export const GSMONTHTOS = pathEnergy + '/generator/indexELE/GSMONTHTOS'
// 中板厂能介单耗
export const MSMONTHTOS = pathEnergy + '/generator/indexELE/MSMONTHTOS'
// 宽厚板厂能介单耗
export const ELEMONTHTOS = pathEnergy + '/generator/indexELE/ELEMONTHTOS'
// 中厚板卷厂能介单耗
export const MATSMONTHTOS = pathEnergy + '/generator/indexELE/MATSMONTHTOS'
// 加热炉利用率
export const RHFTEMPTOS = pathEnergy + '/generator/indexELE/RHFTEMPTOS'
// 真空比例计算
export const LGVACUUMTOS = pathEnergy + '/generator/indexFS/LGVACUUMTOS'
// 加热炉热装率
export const HOTTRANSFERMONTHU =
  pathEnergy + '/generator/indexELE/HOTTRANSFERMONTH'
// 加热炉热装率
export const HOTTRANSFERDAY = pathEnergy + '/generator/indexELE/HOTTRANSFERDAY'
