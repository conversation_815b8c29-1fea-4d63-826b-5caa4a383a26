<template>
  <div class="bigBox">
    <el-row :gutter="32">
      <el-col :span="12">
        <screen-border title="热处理炉">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(1)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <el-table
            :data="listData1"
            height="380">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="stove"
              label="热处理炉"
              width="160"
              align="center"/>
            <el-table-column
              prop="dayProduction"
              label="日产量(块/吨)"
              align="center"
              width="140"/>
            <el-table-column
              prop="monthPlanProduction"
              label="月度计划产量"
              align="center"
              width="140"/>
            <el-table-column
              prop="monthSumProduction"
              label="月度累计产量"
              align="center"
              width="140"/>
            <el-table-column
              prop="calendarProgress"
              label="日历进度"
              align="center"
              width="120"/>
            <el-table-column
              prop="actualProgress"
              label="实际进度"
              align="center"
              width="120"/>
            <el-table-column
              prop="mainVarieties"
              label="主要品种"
              align="center"
              width="120"/>
            <el-table-column
              prop="situation"
              label="设备情况"
              align="center"
              width="180"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="台车炉">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(2)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <el-table
            :data="listData2"
            height="380">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="classes"
              label="班次"
              align="center"/>
            <el-table-column
              prop="classes1"
              label="班别"
              align="center"/>
            <el-table-column
              prop="trolleyFurnace1"
              label="1#台车炉"
              align="center"/>
            <el-table-column
              prop="trolleyFurnace2"
              label="2#台车炉"
              align="center"/>
            <el-table-column
              prop="trolleyFurnace3"
              label="3#台车炉"
              align="center"/>
            <el-table-column
              prop="remark"
              label="备注"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="24">
        <screen-border title="生产报告">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(3)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <div
            id="Team"
            style="width: 100%; height:380px;"/>
        </screen-border>
      </el-col>
    </el-row>
    
    <!--弹框-->
    <el-dialog
      :visible.sync="dialogBox"
      :width="'90%'"
      :close-on-click-modal="false"
      :top="title=='生产计划'?'3vh':'10vh'"
      class="screen-dialog"
      @close="closeDialogBox">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span> -->
            <span
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download"/>
              下载
            </span>
            <span
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-printer"/>
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <el-table
        id="table"
        :data="formData"
        border>
        <el-table-column
          type="index"
          label="序号"
          width="60"/>
        <el-table-column
          v-for="(item,index) in Header"
          :key="index"
          :prop="item.prop"
          :width="item.label=='产品未完成原因'?'300':''"
          :label="item.label"
          align="center">
          <template v-slot="{ row }">
            <el-input 
              :disabled="item.disabled?item.disabled:false" 
              v-model="row[item.prop]"/>
            <span v-show="false">{{ row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          width="100"
          label="操作">
          <template v-slot="scope">
            <div class="btn">
              <el-button 
                :disabled="title=='热处理炉'||title=='台车炉'||title=='生产报告'"
                type="danger"
                icon="el-icon-delete"
                @click="delRow(scope.$index)"/>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- <div class="text-center">
        <span
          class="screen-btn"
          @click="addNewRow()">
          <el-icon class="el-icon-circle-plus-outline" />
          增加数据
        </span>
      </div> -->
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { saveAs } from 'file-saver'
import * as echarts from 'echarts'
import moment from 'moment'

import {
  HP_DATA1,
  HP_SAVE1,
  HP_DATA2,
  HP_SAVE2,
  HP_DATA3,
  HP_SAVE3,
  PRODUCEREPORT,
  PRODUCEREPORT_SAVE,
  PRODUCEDAYTABLE,
  PRODUCEDAYTABLE_SAVE,
  HP_REPORTS,
  HP_REPORTS_SAVE,
  HP_HT_STOVE,
  HP_HT_STOVE_SAVE,
  CT_REPORTS,
  CT_REPORTS_SAVE,
  PRODUCEPLAN,
  PRODUCEPLAN_SAVE
} from '@/api/screen'

export default {
  name: 'HarbourPoolProduce',
  components: {
    // SingleBarsChart,
    // SteelBarsChart,
    ScreenBorder,
    ScreenBorderMulti
  },
  props: {
    selTime: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      //热处理炉
      listData1: [],

      //台车炉
      listData2: [],

      //生产报告
      listData3: [],

      //弹框统一
      dialogBox: false,
      title: '',
      //弹框统一表字段
      Header: [],
      //弹框统一表数据
      formData: [],
      //上传Excel
      fileList: []
    }
  },
  watch: {
    selTime: function() {
      this.getListData1()
      this.getListData2()
      setTimeout(() => {
        this.getListData3()
      }, 500)
    }
  },

  created() {
    this.getListData1()
    this.getListData2()
    setTimeout(() => {
      this.getListData3()
    }, 500)
  },

  methods: {
    //热处理炉
    async getListData1() {
      let res = await post(HP_DATA1, {
        setTime: this.selTime
      })
      // console.log('热处理炉', res)

      if (res.data) {
        this.listData1 = res.data
      }
    },

    //台车炉
    async getListData2() {
      let res = await post(HP_DATA2, {
        setTime: this.selTime
      })
      // console.log('热处理炉', res)

      if (res.data) {
        this.listData2 = res.data
      }
    },

    //生产报告
    async getListData3() {
      let YData1 = []
      let YData2 = []
      let YData3 = []
      let YData4 = []
      let YData5 = []
      let res = await post(HP_DATA3, {
        setTime: this.selTime
      })
      console.log('生产报告', res)

      if (res.data) {
        this.listData3 = res.data
        res.data.forEach(item => {
          if (item.classes == '大') {
            YData1 = [
              item.blasting1,
              item.blasting2,
              item.spray,
              item.straightening,
              item.flatten,
              item.flawDetection,
              item.autoFlawDetection,
              item.slicing
            ]
          }
          if (item.classes == '白') {
            YData2 = [
              item.blasting1,
              item.blasting2,
              item.spray,
              item.straightening,
              item.flatten,
              item.flawDetection,
              item.autoFlawDetection,
              item.slicing
            ]
          }
          if (item.classes == '小') {
            YData3 = [
              item.blasting1,
              item.blasting2,
              item.spray,
              item.straightening,
              item.flatten,
              item.flawDetection,
              item.autoFlawDetection,
              item.slicing
            ]
          }
        })

        let item1 = res.daySumData
        YData4 = [
          item1.blasting1,
          item1.blasting2,
          item1.spray,
          item1.straightening,
          item1.flatten,
          item1.flawDetection,
          item1.autoFlawDetection,
          item1.slicing
        ]
        let item2 = res.daySumData
        YData5 = [
          item2.blasting1,
          item2.blasting2,
          item2.spray,
          item2.straightening,
          item2.flatten,
          item2.flawDetection,
          item2.autoFlawDetection,
          item2.slicing
        ]
      }

      let XData = [
        '抛丸1',
        '抛丸2',
        '喷涂',
        '矫直',
        '压平',
        '探伤',
        '自动探伤',
        '厚板切割'
      ]

      this.getEcharts(XData, YData1, YData2, YData3, YData4, YData5)
    },

    //弹框
    openView(nub) {
      this.dialogBox = true
      if (nub == 1) {
        this.title = '热处理炉'
        this.Header = [
          {
            label: '热处理炉',
            prop: 'stove',
            disabled: true
          },
          {
            label: '日产量(块/吨)',
            prop: 'dayProduction',
            disabled: true
          },
          {
            label: '月度计划产量',
            prop: 'monthPlanProduction'
          },
          {
            label: '月度累计产量',
            prop: 'monthSumProduction',
            disabled: true
          },
          {
            label: '日历进度',
            prop: 'calendarProgress',
            disabled: true
          },
          {
            label: '实际进度',
            prop: 'actualProgress',
            disabled: true
          },
          {
            label: '主要品种',
            prop: 'mainVarieties',
            disabled: true
          },
          {
            label: '设备情况',
            prop: 'situation'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData1))
      } else if (nub == 2) {
        this.title = '台车炉'
        this.Header = [
          {
            label: '班次',
            prop: 'classes'
          },
          {
            label: '班别',
            prop: 'classes1'
          },
          {
            label: '1#台车炉',
            prop: 'trolleyFurnace1'
          },
          {
            label: '2#台车炉',
            prop: 'trolleyFurnace2'
          },
          {
            label: '3#台车炉',
            prop: 'trolleyFurnace3'
          },
          {
            label: '备注',
            prop: 'remark'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData2))
      } else if (nub == 3) {
        this.title = '生产报告'
        this.Header = [
          {
            label: '班次',
            prop: 'classes'
          },
          {
            label: '抛丸1',
            prop: 'blasting1'
          },
          {
            label: '抛丸2',
            prop: 'blasting2'
          },
          {
            label: '喷涂',
            prop: 'spray'
          },
          {
            label: '矫直',
            prop: 'straightening'
          },
          {
            label: '压平',
            prop: 'flatten'
          },
          {
            label: '探伤',
            prop: 'flawDetection'
          },
          {
            label: '自动探伤',
            prop: 'autoFlawDetection'
          },
          {
            label: '厚板切割',
            prop: 'slicing'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData3))
      }
    },

    //添加行
    addNewRow() {
      let row = {}
      this.Header.forEach(item => {
        row[item.prop] = ''
      })

      this.formData.push(row)
    },

    //删除行
    delRow(indexs) {
      this.formData.forEach((item, index) => {
        if (indexs == index) {
          this.formData.splice(index, 1)
        }
      })
    },

    //下载模板
    DownloadExcel() {
      let data = [{}]
      this.Header.forEach(item => {
        data[0][item.prop] = item.label
      })

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `${this.title}.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          `${this.title}.xlsx`
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      let header = {}
      let letter = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      this.Header.forEach((item, index) => {
        header[item.prop] = letter[index]
      })

      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, header)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        console.log(list)

        list.forEach(item => {
          item.selTime = this.selTime
          this.formData.push(item)
        })
      })
    },

    //保存新增数据
    async saveData() {
      let res
      if (this.title == '热处理炉') {
        res = await post(HP_SAVE1, {
          setTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '台车炉') {
        res = await post(HP_SAVE2, {
          setTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '生产报告') {
        res = await post(HP_SAVE3, {
          setTime: this.selTime,
          data: this.formData
        })
      }

      // console.log('保存', res)
      if (res.status == 1) {
        this.$message.success('保存成功')
        if (this.title == '热处理炉') {
          this.getListData1()
        } else if (this.title == '台车炉') {
          this.getListData2()
        } else if (this.title == '生产报告') {
          setTimeout(() => {
            this.getListData3()
          }, 500)
        }

        this.closeDialogBox()
      }
    },

    //关闭弹框
    closeDialogBox() {
      this.dialogBox = false
      this.title = ''
      this.Header = []
      this.formData = []
    },

    //班组轧制节奏柱状图
    getEcharts(XData, YData1, YData2, YData3, YData4, YData5) {
      echarts.init(document.getElementById('Team')).dispose() // 销毁实例
      // 找到容器
      let dayEcharts = echarts.init(document.getElementById('Team'))

      // 开始渲染
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function(params) {
            let name1 =
              params[0].marker +
              params[0].seriesName +
              ':' +
              params[0].data +
              '块'
            let name2 =
              params[1].marker +
              params[1].seriesName +
              ':' +
              params[1].data +
              '块'
            let name3 =
              params[2].marker +
              params[2].seriesName +
              ':' +
              params[2].data +
              '块'
            let name4 =
              params[3].marker +
              params[3].seriesName +
              ':' +
              params[3].data +
              '块'

            return (
              params[0].axisValue +
              '<br>' +
              name1 +
              '<br>' +
              name2 +
              '<br>' +
              name3 +
              '<br>' +
              name4
            )
          }
        },
        legend: {
          data: ['大夜班', '白班', '小夜班', '日合计', '月合计'],
          textStyle: {
            fontSize: 14, //字体大小
            color: '#ffffff' //字体颜色
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          top: '12%',
          bottom: '23%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: XData,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#FFCC22',
              width: 0,
              type: 'solid'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '块',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#FFCC22',
              width: 0,
              type: 'solid'
            }
          }
        },
        series: [
          {
            data: YData1,
            type: 'bar',
            name: '大夜班',
            barWidth: '15%',
            itemStyle: {
              normal: {
                color: '#0071F5',
                label: {
                  show: true, //开启显示
                  position: 'top', //在上方显示
                  textStyle: {
                    //数值样式
                    color: 'white',
                    fontSize: 14
                  }
                }
              }
            }
          },
          {
            data: YData2,
            type: 'bar',
            name: '白班',
            barWidth: '15%',
            itemStyle: {
              normal: {
                color: '#FF9800',
                label: {
                  show: true, //开启显示
                  position: 'top', //在上方显示
                  textStyle: {
                    //数值样式
                    color: 'white',
                    fontSize: 14
                  }
                }
              }
            }
          },
          {
            data: YData3,
            type: 'bar',
            name: '小夜班',
            barWidth: '15%',
            itemStyle: {
              normal: {
                color: '#3DB842',
                label: {
                  show: true, //开启显示
                  position: 'top', //在上方显示
                  textStyle: {
                    //数值样式
                    color: 'white',
                    fontSize: 14
                  }
                }
              }
            }
          },
          {
            data: YData4,
            type: 'bar',
            name: '日合计',
            barWidth: '15%',
            itemStyle: {
              normal: {
                color: '#81388D',
                label: {
                  show: true, //开启显示
                  position: 'top', //在上方显示
                  textStyle: {
                    //数值样式
                    color: 'white',
                    fontSize: 14
                  }
                }
              }
            }
          },
          {
            data: YData5,
            type: 'bar',
            name: '月合计',
            barWidth: '15%',
            itemStyle: {
              normal: {
                color: '#F0E68C',
                label: {
                  show: true, //开启显示
                  position: 'top', //在上方显示
                  textStyle: {
                    //数值样式
                    color: 'white',
                    fontSize: 14
                  }
                }
              }
            }
          }
        ]
      }

      dayEcharts.setOption(option)
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .tabBox {
      display: flex;
      .tab {
        color: #ffffffbf;
        margin-right: 20px;
      }
      .tab_block {
        display: flex;
        flex-direction: column;
        position: relative;
        .tab_img {
          .tab_img2 {
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
          }
          .tab_img1 {
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
            margin-bottom: 7px;
          }
        }
      }
    }
    .border-content {
      height: 380px;
    }
  }
  .EchartsBox {
    height: 380px;
    .setRadio {
      /deep/.el-radio {
        color: white;
      }
    }
  }
  .border-wrapper {
    margin-bottom: 15px;
  }
  /deep/.el-textarea__inner {
    background-color: #041a21;
    border: 1px solid #1fc6ff;
    color: white;
    font-size: 14px;
    height: 70px;
  }
}

.btn {
  /deep/.el-button {
    font-size: 15px;
    padding: 4px 15px;
    border-radius: 4px;
  }
}

/deep/ .el-table .el-table__cell {
  font-size: 19px;
}

/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}
</style>
