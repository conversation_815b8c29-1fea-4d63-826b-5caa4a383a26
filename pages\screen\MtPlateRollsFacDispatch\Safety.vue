<template>
  <div class="bigBox">
    <el-row :gutter="32">
      <el-col :span="24">
        <screen-border title="今日安环事故情况">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/coilScreen/bjsc'"
              class="screen-btn"
              @click="openView()">
              <el-icon class="el-icon-edit-outline" />
              操作
            </span>
          </template>
          <el-table
            :data="month_heatTreatment"
            height="60vh">
            <el-table-column
              type="index"
              label="序号"
              width="60" />
            <el-table-column
              prop="projectName"
              label="项目"
              align="center" />
            <el-table-column
              prop="incidentDes"
              label="情况说明"
              align="center" />
            <el-table-column
              prop="remarks"
              label="备注"
              align="center" />
          </el-table>
        </screen-border>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-input
        :autosize="{ minRows:4}"
        v-model="textarea2"
        class="custom-textarea"
        type="textarea"
        placeholder="请输入内容" />
    </el-row>
    <!--弹框-->
    <el-dialog
      :visible.sync="dialogBox"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      @close="closeDialogBox">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span> -->
            <span
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download" />
              下载
            </span>
            <span
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-printer" />
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <el-table
        id="table"
        :data="formData"
        border>
        <el-table-column
          type="index"
          label="序号"
          width="60" />
        <el-table-column
          v-for="(item,index) in Header"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          align="center">
          <template v-slot="{ row }">
            <el-input v-model="row[item.prop]" />
            <span v-show="false">{{ row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          width="100"
          label="操作">
          <template v-slot="{ row, $index }">
            <span
              class="screen-btn"
              @click="delRow($index)">
              <el-icon class="el-icon-delete" />
              删除
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div class="text-center">
        <span
          class="screen-btn"
          @click="addNewRow()">
          <el-icon class="el-icon-circle-plus-outline" />
          增加数据
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { saveAs } from 'file-saver'
import * as echarts from 'echarts'
import moment from 'moment'

import {
  HESIncidentFindall,
  HESIncidentSave,
  SAIL_HEATTREATMENT,
  SAIL_HEATTREATMENT_SAVE
} from '@/api/screen'

export default {
  name: 'HeatTreatment',
  components: {
    // SingleBarsChart,
    // SteelBarsChart,
    ScreenBorder
  },
  props: {
    selTime: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      textarea2: '今日一级危险作业一起，二级危险作业0起',
      //当月热处理
      month_heatTreatment: [
        {
          project: '当月1#炉',
          planYield: 0,
          accruedYield: 0,
          timeProgress: 0,
          yieldProgress: 0,
          averageYield: 0,
          monYield: 0
        },
        {
          project: '当月2#炉',
          planYield: 0,
          accruedYield: 0,
          timeProgress: 0,
          yieldProgress: 0,
          averageYield: 0,
          monYield: 0
        }
      ],

      //班次热处理
      sail_heatTreatment: [
        {
          sail: '大夜班',
          planTon_1: 0,
          actualTon_1: 0,
          remark_1: '',
          planTon_2: 0,
          actualTon_2: 0,
          remark_2: ''
        },
        {
          sail: '白班',
          planTon_1: 0,
          actualTon_1: 0,
          remark_1: '',
          planTon_2: 0,
          actualTon_2: 0,
          remark_2: ''
        },
        {
          selTime: '2024-12-23',
          sail: '小夜班',
          planTon_1: 0,
          actualTon_1: 0,
          remark_1: '',
          planTon_2: 0,
          actualTon_2: 0,
          remark_2: ''
        },
        {
          selTime: '2024-12-23',
          sail: '合计',
          planTon_1: 0,
          actualTon_1: 0,
          remark_1: '',
          planTon_2: 0,
          actualTon_2: 0,
          remark_2: ''
        },
        {
          selTime: '2024-12-23',
          sail: '待热处理量',
          planTon_1: 0,
          actualTon_1: 0,
          remark_1: '',
          planTon_2: 0,
          actualTon_2: 0,
          remark_2: ''
        }
      ],

      //弹框统一
      dialogBox: false,
      title: '',
      //弹框统一表字段
      Header: [],
      //弹框统一表数据
      formData: [],
      //上传Excel
      fileList: []
    }
  },

  created() {
    this.getMonth_heatTreatment()
    this.getSail_heatTreatment()
  },
  methods: {
    //获取当月热处理数据
    getMonth_heatTreatment() {
      post(HESIncidentFindall, {
        setDate: this.selTime
      }).then(res => {
        this.month_heatTreatment = res.data
        this.textarea2 = res.data1
      })
    },

    //获取班次热处理数据
    async getSail_heatTreatment() {
      let res = await post(SAIL_HEATTREATMENT, {
        selTime: this.selTime
      })
      // console.log('班次热处理', res)

      if (res.data.length != 0) {
        this.sail_heatTreatment = res.data
      }
    },
    //弹框
    openView() {
      this.dialogBox = true
      this.title = '今日安环事故情况'
      this.Header = [
        {
          label: '项目',
          prop: 'projectName'
        },
        {
          label: '情况说明',
          prop: 'incidentDes'
        },
        {
          label: '备注',
          prop: 'remarks'
        }
      ]
      this.formData = this.month_heatTreatment
    },

    //添加行
    addNewRow() {
      this.formData.push({})
    },

    //删除行
    delRow(index) {
      this.formData.splice(index, 1)
    },

    //下载模板
    DownloadExcel() {
      let data = [{}]
      this.Header.forEach(item => {
        data[0][item.prop] = item.label
      })

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `${this.title}.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          `${this.title}.xlsx`
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      let header = {}
      let letter = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      this.Header.forEach((item, index) => {
        header[item.prop] = letter[index]
      })

      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, header)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        console.log(list)

        list.forEach(item => {
          item.selTime = this.selTime
          this.formData.push(item)
        })
      })
    },

    //保存新增数据
    saveData() {
      console.log('保存', this.formData)
      post(HESIncidentSave, {
        setDate: this.selTime,
        data: this.formData
      }).then(res => {
        // console.log('保存', res)
        if (res.status === 1) {
          this.$message.success('保存成功')
          this.getMonth_heatTreatment()
          this.closeDialogBox()
        }
      })
    },

    //关闭弹框
    closeDialogBox() {
      this.dialogBox = false
      this.title = ''
      this.Header = []
      this.formData = []
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  .EchartsBox {
    height: 680px;

    .setRadio {
      /deep/ .el-radio {
        color: white;
      }
    }
  }

  .border-wrapper {
    margin-bottom: 15px;
  }
}

.btn {
  /deep/ .el-button {
    font-size: 15px;
    padding: 4px 15px;
    border-radius: 4px;
  }
}

/deep/ .el-table .el-table__cell {
  font-size: 19px;
}

/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}

/deep/ .custom-textarea .el-textarea__inner {
  height: 145px; /* 设置最小高度 */
  background-color: #06252f; /* 设置背景颜色 */
  font-size: 18px;
  color: #fff;
}
</style>
