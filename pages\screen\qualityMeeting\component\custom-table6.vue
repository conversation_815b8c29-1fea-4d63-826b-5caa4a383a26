<template>
  <div class="full-height">
    <screen-border
      :title="title"
      :content-class="contentClass">
      <template v-slot:headerRight>
        <template v-if="steelmakingShow">
          <slot name="topRight" />
        </template>
        <template v-else>
          <span
            v-command="'/screen/qualityMeeting/edit'"
            class="screen-btn"
            @click="dialogVisible = true">
            <el-icon class="el-icon-edit-outline" />
            操作
          </span>
        </template>
      </template>
      <slot name="content" />
      <div
        v-if="showTable"
        ref="table1"
        class="scroll-wrapper">
        <el-table
          v-loading="loading"
          :data="showGridData"
          :max-height="maxHeight"
          class="font-table center-table"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false && !item.onlyEdit">
              <el-table-column
                v-if="item.type === 'index'"
                :key="index"
                :label="item.label"
                type="index"
                width="100"
              />
              <template v-else>
                <template v-if="item.inputType === 'textarea'">
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label"
                    :align="item.align">
                    <template v-slot="{ row }">
                      <div
                        slot="content"
                        v-html="formatText(row[item.keySave], item.split)"
                      />
                    </template>
                  </el-table-column>
                </template>
                <template v-else>
                  <el-table-column
                    :key="index"
                    :width="item.width || ''"
                    :property="item.keySave"
                    :label="item.label"
                    :align="item.align" />
                </template>
              </template>
            </template>
          </template>
        </el-table>
      </div>
    </screen-border>
    <el-dialog
      :visible.sync="dialogVisible"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate" />
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-document-checked" />
              保存
            </span>
          </div>
          {{ popTitle || title }}
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="gridData"
          border>
          <template
            v-for="(item, index) in setting">
            <template v-if="item.show !== false">
              <el-table-column
                v-if="item.type === 'index'"
                :key="index"
                :label="item.label"
                :align="item.align"
                type="index"
                width="100"
              />
              <template v-else>
                <el-table-column
                  :key="index"
                  :width="item.width || ''"
                  :property="item.keySave"
                  :align="item.align"
                  :label="item.label">
                  <template v-slot="{ row }">
                    <template v-if="item.inputType === 'textarea'">
                      <el-input
                        v-model="row[item.keySave]"
                        :disabled="item.disabled"
                        :rows="4"
                        type="textarea"
                      />
                    </template>
                    <template v-else>
                      <el-input
                        v-model="row[item.keySave]"
                        :disabled="item.disabled" />
                    </template>
                  </template>
                </el-table-column>
              </template>
            </template>
          </template>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index)">
                <el-icon class="el-icon-delete" />
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData()">
          <el-icon class="el-icon-circle-plus-outline" />
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'" />
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import {
  qmsQualityQuery,
  qmsQualitySave,
  qmsPygzFirstpassrateNew,
  qmsPygzFirstpassrateSaveNew
} from '@/api/screen'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'

export default {
  name: 'custom-table',
  components: {
    ScreenBorder,
    qmsPygzFirstpassrateNew,
    qmsPygzFirstpassrateSaveNew
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    popTitle: {
      type: String,
      default: ''
    },
    setting: {
      type: Array,
      default: function() {
        return []
      }
    },
    selectDate: {
      type: String,
      default: ''
    },
    urlList: {
      type: String,
      default: ''
    },
    urlSave: {
      type: String,
      default: ''
    },
    showTable: {
      type: Boolean,
      default: true
    },
    heightAuto: {
      type: Boolean,
      default: true
    },
    heightSet: {
      type: Number,
      default: 0
    },
    steelmakingShow: {
      type: Boolean,
      default: false
    },
    contentClass: {
      type: String,
      default: ''
    }
  },
  data: function() {
    return {
      cDate: '',
      loading: false,
      dialogVisible: false,
      showGridData: [],
      gridData: [],
      importDate: null,
      importDateVisible: false,
      importFunName: '',
      mergeArr: [],
      spanArr: {},
      position: 0,
      maxHeight: null,
      ABC: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'K'
      ]
    }
  },
  computed: {
    canEditQuality: function() {
      // console.log(moment().format('yyyy-MM-DD'), this.cDate)
      return (
        moment().format('yyyy-MM-DD') <=
        moment(this.cDate)
          .subtract(-1, 'day')
          .format('yyyy-MM-DD')
      )
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getData()
    },
    heightSet: function() {
      this.maxHeight = this.heightSet ? this.heightSet : null
    }
  },
  destroyed() {
    // window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = this.selectDate
    this.getData()
  },
  mounted() {
    this.calculate()
    // window.addEventListener('resize', this.calculate)
  },
  methods: {
    // 获取数据
    getData() {
      post(this.urlList, {
        setTime: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.showGridData = res.data
        console.log(
          '%c 获取数据',
          'color: red; font-size: 16px;',
          this.showGridData
        )
        this.gridData = _.cloneDeep(this.showGridData)
        this.$nextTick(() => {
          this.$emit('change', this.showGridData)
        })
      })
    },
    // 更新数据
    saveData() {
      // this.loading = true
      // 数据信息
      console.log('%c 修改数据1', 'color: red; font-size: 16px;', params)
      const params = {
        setTime: this.cDate,
        data: this.gridData
      }
      post(this.urlSave, params).then(res => {
        this.loading = false
        // console.log('%c 修改数据1', 'color: red; font-size: 16px;', params)
        if (res.status == 1) {
          this.$message.success('保存成功！')
          this.getData()
          this.dialogVisible = false
        } else {
          this.$message.warning('保存失败！')
        }
      })
    },
    // 导入日期数据
    importData(date) {
      post(this.urlList, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.gridData = res.data.map(item => {
          const obj = {}
          this.setting.forEach(set => {
            obj[set.keySave] = item[set.keyQuery]
          })
          return obj
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 执行导入
    importHistoryData() {
      this.importData(this.importDate)
      this.importDateVisible = false
    },
    // 数据管理
    clearGridData() {
      this.gridData = []
    },
    addGridData() {
      this.gridData.push({})
    },
    delGridData(index) {
      this.gridData.splice(index, 1)
    },
    // 日期改变推送
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    // 计算需要合并的单元格
    formatSpanData(data) {
      this.mergeArr.forEach(keyName => {
        this.spanArr[keyName] = []
        this.position = 0
        // this.data 列表数据
        data.forEach((item, index) => {
          if (index === 0) {
            // 第一列默认push一个1 然后position位置为0
            this.spanArr[keyName].push(1)
            this.position = 0
          } else {
            //除第一列以外就判断 后一个和前一个要合并的值是否相同
            if (data[index][keyName] === data[index - 1][keyName]) {
              //相同 就给spanArr位置变量position的值+1
              this.spanArr[keyName][this.position] += 1
              //然后往列表中push 0 占位 并且当前位置rowspan值为0 不展示达到合并效果
              this.spanArr[keyName].push(0)
            } else {
              //否则就 push 1 证明需要合并的值不想同,无发合并 rowspan值为 1
              this.spanArr[keyName].push(1)
              //位置变量再继续 设置为当前列id的值
              this.position = index
            }
          }
        })
      })
    },
    // 合并单元格
    handleObjectSpan({ row, column, rowIndex, columnIndex }) {
      // 需要合并的列
      // [0, 1, 2].includes(columnIndex ), 表示合并前三列
      if (this.mergeArr.includes(column.property)) {
        const _row = this.spanArr[column.property][rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 生成带换行数据
    formatText(text, split) {
      if (!text) {
        return ''
      }
      if (split) text = text.split(split).join('\n')
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },
    // 计算高度
    calculate() {
      this.showTable &&
        this.heightAuto &&
        (this.maxHeight = this.$refs.table1.offsetHeight)
    }
  }
}
</script>

<style scoped lang="less">
// 大屏按钮
.screen-btn {
  display: inline-block;
  min-width: 68px;
  height: 28px;
  padding: 0 5px;
  background: rgba(31, 198, 255, 0.3);
  border: 1px solid #1fc6ff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  color: #fff;

  &:hover {
    background: rgba(31, 198, 255, 0.6);
    border: 1px solid #1fc6ff;
  }
}

.scroll-wrapper {
  height: 100%;
  overflow: auto;
}

/deep/ .el-loading-mask {
  background: rgba(217, 231, 245, 0.4);
}

/deep/ .el-loading-spinner .path {
  stroke: #0a4456;
}
</style>
