<template>
  <div class="content">
    <div class="content-item top">
      <screen-border title="烧嘴故障率（近30天）">
        <div class="chart-wrapper">
          <div
            class="chart">
            <bars-chart
              :show-legend="false"
              :chart-data="Burner"
              :bar-width = "50"
              :color="['#ffc107']"
              :unit="'%'"
              :x-data="[
                '板卷厂1#炉'+'\n故障数：'+Burner[0].failureNum[0],
                '板卷厂2#炉'+'\n故障数：' + Burner[0].failureNum[1],
                '中板厂3#炉'+'\n故障数：' + Burner[0].failureNum[2],
                '中板厂4#炉'+'\n故障数：' + Burner[0].failureNum[3],
                '宽板厂5#炉'+'\n故障数：' + Burner[0].failureNum[4],
                '宽板厂6#炉'+'\n故障数：' + Burner[0].failureNum[5]
              ]"
            />
          </div>
        </div>
      </screen-border>
    </div>
    <div class="content-hold"/>
    <div class="content-item top">
      <screen-border title="热处理停时统计（本月）">
        <div class="chart-wrapper">
          <div
            class="chart">
            <bars-chart
              :bar-width="15"
              :unit="'min'"
              :color="[
                '#ff5722',
                '#ffc107',
                '#cddc39',
                '#4caf50',
                '#e91e63',
                '#00bcd4',
                '#2196f3',
                '#3f51b5',
                '#9c27b0',
                '#009688',
              ]"
              :chart-data="defectChart.bar1"
              :x-data="defectChart.barX1"/>
            />
          </div>
        </div>
      </screen-border>
    </div>
  </div>
</template>

<script>
import BarsChart from '@/pages/screen/diviceMeeting/component/bars-chart'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border.vue'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import { post } from '@/lib/Util'
import { findFaultTime, getBurnerFailureRate } from '@/api/device'
import * as _ from 'lodash'
export default {
  name: 'heatTreatment',
  components: { BarsChart, ScreenBorder, ScreenMixins },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      Burner: [
        //烧嘴故障率
        {
          data: [],
          failureNum: []
        }
      ],
      defectChart: {
        bar1: [
          {
            name: '停炉',
            data: [],
            barGap: '0.3'
          },
          {
            name: '待料',
            data: [],
            barGap: '0.3'
          },
          {
            name: '机械故障',
            data: [],
            barGap: '0.3'
          },
          {
            name: '电气故障',
            data: [],
            barGap: '0.3'
          },
          {
            name: '操作故障',
            data: [],
            barGap: '0.3'
          },
          {
            name: '通炉',
            data: [],
            barGap: '0.3'
          },
          {
            name: '检修',
            data: [],
            barGap: '0.3'
          },
          {
            name: '工艺升降温',
            data: [],
            barGap: '0.3'
          },
          {
            name: '规格转换',
            data: [],
            barGap: '0.3'
          },
          {
            name: '环保检测',
            data: [],
            barGap: '0.3'
          }
        ],
        barX1: [
          '板卷厂1#炉',
          '板卷厂2#炉',
          '中板厂3#炉',
          '中板厂4#炉',
          '宽板厂5#炉',
          '宽板厂6#炉'
        ]
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.$nextTick(item => {
        this.getBurnerFault()
        this.getHeatTreatment()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.getBurnerFault()
    this.getHeatTreatment()
  },
  methods: {
    // 烧嘴故障率
    getBurnerFault() {
      const params = {
        startTime:
          this.$moment(this.cDate)
            .subtract(30, 'day')
            .format('yyyy-MM-DD') + ' 00:00:00',
        endTime: this.$moment(this.cDate).format('yyyy-MM-DD') + ' 23:59:59'
      }
      post(getBurnerFailureRate, params).then(res => {
        this.Burner[0].data = res.data.map(item => item.failureRate) //故障率
        this.Burner[0].failureNum = res.data.map(item => item.failureNum) //故障编号
        // console.log('烧嘴故障率', res)
      })
    },
    //判断是否在26号之前还是之后
    //26号之前  开始时间为上个月26号 到选择时间
    //26号之后  开始时间为这个月26号 到选择时间

    checkIfAfter26(selectedDate) {
      // 将选定日期转换为 Moment 对象
      const selectedDateMoment = this.$moment(selectedDate)

      // 获取当前月份的 26 号的 Moment 对象
      const currentMonth26 = this.$moment()
        .date(26)
        .startOf('day')

      // 判断选择的日期是否在当前月份的 26 号之后
      if (selectedDateMoment.isAfter(currentMonth26)) {
        console.log('选择日期在当前月份的 26 号之后')
        return 0
      } else if (selectedDateMoment.isSame(currentMonth26, 'day')) {
        console.log('选择日期是当前月份的 26 号')
        return 2
      } else {
        console.log('选择日期在当前月份的 26 号之前')
        return 1
      }
    },
    //热处理停时统计
    getHeatTreatment() {
      const params = {
        beginDate: this.$moment(this.cDate)
          .startOf('month')
          .format('yyyy-MM-DD'),
        endDate: this.$moment(this.cDate).format('yyyy-MM-DD')
      }
      //26号之前  开始时间为上个月26号 到选择时间
      let params_ = {}
      if (this.checkIfAfter26(this.cDate) == 1) {
        params_ = {
          beginDate: this.$moment()
            .subtract(1, 'month')
            .startOf('month')
            .date(26)
            .startOf('day')
            .format('yyyy-MM-DD'),
          endDate: this.$moment(this.cDate).format('yyyy-MM-DD')
        }
      } else if (this.checkIfAfter26(this.cDate) == 0) {
        //26号之后  开始时间为这个月26号 到选择时间
        params_ = {
          beginDate: this.$moment()
            .date(26)
            .startOf('day')
            .format('yyyy-MM-DD'),
          endDate: this.$moment(this.cDate).format('yyyy-MM-DD')
        }
      } else if (this.checkIfAfter26(this.cDate) == 2) {
        //当前26号  开始丶结束都是26号
        params_ = {
          beginDate: this.$moment(this.cDate).format('yyyy-MM-DD'),
          endDate: this.$moment(this.cDate).format('yyyy-MM-DD')
        }
      }

      console.log('开始结束时间', params_)
      post(findFaultTime, params).then(res => {
        // "PLT": "产线",
        // "CHARGEFURLINE": "炉座号
        // console.log('热处理停时统计', res)
        const fields = Object.keys(res.data[0])
        const result = fields.reduce((acc, field) => {
          acc[field] = res.data.map(item => item[field])
          return acc
        }, {})
        this.defectChart.bar1[0].data = result.blowout //停炉时长
        this.defectChart.bar1[1].data = result.waitmater //待料时长
        this.defectChart.bar1[2].data = result.mechanicalFault //机械故障时长
        this.defectChart.bar1[3].data = result.electricalFault //电气故障时长
        this.defectChart.bar1[4].data = result.operatFault //操作故障时长
        this.defectChart.bar1[5].data = result.throughFurnace //通炉时长
        this.defectChart.bar1[6].data = result.overhaul //检修时长
        this.defectChart.bar1[7].data = result.fallTemp //工艺升降温时长
        this.defectChart.bar1[8].data = result.specTrans //规则转换时长
        this.defectChart.bar1[9].data = result.envirMonitor //环境监测时长
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    font-size: 0;
    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.green {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
