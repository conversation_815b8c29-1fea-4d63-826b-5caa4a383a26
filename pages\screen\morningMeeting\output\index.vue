<template>
  <div class="content">
    <div
      v-if="steelYesterday.notice"
      class="content-hold slick">
      <notice-bar
        :text="steelYesterday.notice"/>
    </div>
    <div class="content-item top">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <el-row
            :gutter="32"
            class="full-height">
            <el-col
              :span="10"
              class="full-height">
              <screen-border title="钢产量（昨日）">
                <template v-slot:headerRight>
                  <span
                    v-command="'/screen/morningMeeting/edit'"
                    class="screen-btn"
                    @click="handleSteel('B')">
                    <el-icon class="el-icon-edit-outline"/>
                    操作
                  </span>
                </template>
                <div class="chart-wrapper">
                  <div
                    class="chart">
                    <steel-bars-chart
                      :show-legend="false"
                      :chart-data="steelYesterday.bar1"
                      :chart-data2="steelYesterday.bar2"
                      :x-data="['一炼钢吨位', '一炼钢炉数']"/>
                  </div>
                  <div
                    class="fail-reason"
                    style="height: 15%">
                    <template v-if="steelYesterday.failReason">
                      <span>未完成原因：</span><span>{{ steelYesterday.failReason || '无' }}</span>
                    </template>
                  </div>
                </div>
              </screen-border>
            </el-col>
            <el-col
              :span="14"
              class="full-height">
              <screen-border title="轧钢产量（昨日）">
                <template v-slot:headerRight>
                  <span
                    v-command="'/screen/morningMeeting/edit'"
                    class="screen-btn"
                    @click="handleSteel('C')">
                    <el-icon class="el-icon-edit-outline"/>
                    操作
                  </span>
                </template>
                <div class="chart-wrapper">
                  <div
                    style="height: 70%;"
                  >
                    <single-bars-chart
                      :show-legend="false"
                      :chart-data="rollYesterday.bar1"
                      :x-data="['板卷厂', '宽厚板厂', '中板厂']"/>
                  </div>
                  <div
                    
                    style="height: 43%">
                    <template >
                      <el-table
                        v-loading="loading3"
                        :data="SteelRolligInterval"
                        :cell-style="{ padding: 0 }"
                        border>
                        <el-table-column
                          label="产线"
                          property="factoryName"/>
                        <el-table-column
                          label="<=30s"
                          property="lessThirtySeconds"/>
                        <el-table-column
                          label="<=60s"
                          property="lessSixtySeconds"/>
                      </el-table>
                    </template>
                  </div>
                  <div
                    v-if="rollYesterday.failReason"
                    style="height: 10%;margin-top:35px">
                    <template >
                      <span>未完成原因：</span><span>{{ rollYesterday.failReason || '无' }}</span>
                    </template>
                    <!-- <template>
                      <span>未完成原因：</span><span>ajsdhjahsdjhakjdhkjsahdkjahsdkj</span>
                    </template> -->
                  </div>
                </div>
              </screen-border>
            </el-col>
          </el-row>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <el-row
            :gutter="32"
            class="full-height">
            <el-col
              :span="10"
              class="full-height">
              <screen-border title="金润、金石产量（昨日）">
                <template v-slot:headerRight>
                  <span
                    v-command="'/screen/morningMeeting/edit'"
                    class="screen-btn"
                    @click="handleSteel('J')">
                    <el-icon class="el-icon-edit-outline"/>
                    操作
                  </span>
                </template>
                <div class="chart-wrapper">
                  <div
                    class="chart">
                    <single-bars-chart
                      :show-legend="false"
                      :chart-data="otherYesterday.bar1"
                      :x-data="['金润', '金石石灰', '金石渣罐']"/>
                  </div>
                  <div
                    class="fail-reason"
                    style="height: 15%">
                    <template v-if="otherYesterday.failReason">
                      <span>未完成原因：</span><span>{{ otherYesterday.failReason || '无' }}</span>
                    </template>
                  </div>
                </div>
              </screen-border>
            </el-col>
            <el-col
              :span="14"
              class="full-height">
              <screen-border title="热处理产量（昨日）">
                <template v-slot:headerRight>
                  <span
                    v-command="'/screen/morningMeeting/edit'"
                    class="screen-btn"
                    @click="heatYesterday.dialogVisible = true">
                    <el-icon class="el-icon-edit-outline"/>
                    操作
                  </span>
                </template>
                <div class="chart-wrapper">
                  <div
                    class="chart">
                    <single-bars-chart
                      :show-legend="false"
                      :chart-data="heatYesterday.bar1"
                      :bar-width="20"
                      :x-data="heatYesterday.barX"/>
                  </div>
                  <div
                    class="fail-reason"
                    style="height: 15%">
                    <template v-if="heatYesterday.failReason">
                      <span>未完成原因：</span><span>{{ heatYesterday.failReason || '无' }}</span>
                    </template>
                  </div>
                </div>
              </screen-border>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <el-row
            :gutter="32"
            class="full-height">
            <el-col
              :span="10"
              class="full-height">
              <screen-border title="钢产量（月度）">
                <div class="chart-wrapper">
                  <div
                    class="cards">
                    <div class="card">
                      <span class="name">累计产量</span>
                      <span class="num"><em>{{ steelMonth.output }}</em> t</span>
                    </div>
                    <div class="card">
                      <span class="name">超欠</span>
                      <span class="num">
                        <em :class="{'red': steelMonth.percent < 0, 'green': steelMonth.percent > 0}">
                          {{ steelMonth.targetSchedule }}
                        </em>
                        t</span>
                    </div>
                    <div class="card">
                      <span class="name">超欠进度</span>
                      <span class="num"><em>{{ steelMonth.percent }}</em> %</span>
                    </div>
                  </div>
                  <div style="flex: 1">
                    <single-bars-chart
                      :show-legend="false"
                      :chart-data="steelMonth.bar1"
                      :x-data="['一炼钢吨位']"/>
                  </div>
                </div>
              </screen-border>
            </el-col>
            <el-col
              :span="14"
              class="full-height">
              <screen-border title="轧钢产量（月度）">
                <div class="chart-wrapper">
                  <div
                    class="cards">
                    <div class="card">
                      <span class="name">累计产量</span>
                      <span class="num"><em>{{ rollMonth.output }}</em> t</span>
                    </div>
                    <div class="card">
                      <span class="name">超欠</span>
                      <span class="num"><em :class="{'red': rollMonth.percent < 0, 'green': rollMonth.percent >= 0}">{{ rollMonth.targetSchedule }}</em> t</span>
                    </div>
                    <div class="card">
                      <span class="name">超欠进度</span>
                      <span class="num"><em>{{ rollMonth.percent }}</em> %</span>
                    </div>
                  </div>
                  <div style="height: 85%">
                    <single-bars-chart
                      :show-legend="false"
                      :chart-data="rollMonth.bar1"
                      :x-data="['板卷厂', '宽厚板厂', '中板厂']"/>
                  </div>
                </div>
              </screen-border>
            </el-col>
          </el-row>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <el-row
            :gutter="32"
            class="full-height">
            <el-col
              :span="10"
              class="full-height">
              <screen-border title="金润、金石产量（月度）">
                <div class="chart-wrapper">
                  <div
                    class="cards"
                    style="height: 132px">
                    <div
                      class="card"
                      style="margin-left: 0; min-width: 50px;">
                      <span class="name">分厂</span>
                      <span class="num">金润</span>
                      <span class="num">金石石灰</span>
                      <span class="num">金石渣罐</span>
                    </div>
                    <div
                      class="card"
                      style="margin-left: 0;">
                      <span class="name">累计产量</span>
                      <span class="num"><em>{{ otherMonth.outputD1 }}</em> t</span>
                      <span class="num"><em>{{ otherMonth.outputD2 }}</em> t</span>
                      <span class="num"><em>{{ otherMonth.outputD22 }}</em> 罐</span>
                    </div>
                    <div
                      class="card"
                      style="margin-left: 0;">
                      <span class="name">超欠</span>
                      <span class="num"><em :class="{'red': otherMonth.percentD1 < 0, 'green': otherMonth.percentD1 > 0}">{{ otherMonth.targetScheduleD1 }}</em> t</span>
                      <span class="num"><em :class="{'red': otherMonth.percentD2 < 0, 'green': otherMonth.percentD2 > 0}">{{ otherMonth.targetScheduleD2 }}</em> t</span>
                      <span class="num"><em :class="{'red': otherMonth.percentD22 < 0, 'green': otherMonth.percentD22 > 0}">{{ otherMonth.targetScheduleD22 }}</em> 罐</span>
                    </div>
                    <div
                      class="card"
                      style="margin-left: 0;">
                      <span class="name">超欠进度</span>
                      <span class="num"><em>{{ otherMonth.percentD1 }}</em> %</span>
                      <span class="num"><em>{{ otherMonth.percentD2 }}</em> %</span>
                      <span class="num"><em>{{ otherMonth.percentD22 }}</em> %</span>
                    </div>
                  </div>
                  <div style="height: 85%">
                    <single-bars-chart
                      :show-legend="false"
                      :chart-data="otherMonth.bar1"
                      :x-data="['金润', '金石石灰', '金石渣罐']"/>
                  </div>
                </div>
              </screen-border>
            </el-col>
            <el-col
              :span="14"
              class="full-height">
              <screen-border title="热处理产量（月度）">
                <div class="chart-wrapper">
                  <div
                    class="cards">
                    <div class="card">
                      <span class="name">累计产量</span>
                      <span class="num"><em>{{ heatMonth.output }}</em> t</span>
                    </div>
                    <div class="card">
                      <span class="name">超欠</span>
                      <span class="num"><em :class="{'red': heatMonth.targetSchedule < 0, 'green': heatMonth.targetSchedule > 0}">{{ heatMonth.targetSchedule }}</em> t</span>
                    </div>
                    <div class="card">
                      <span class="name">超欠进度</span>
                      <span class="num"><em>{{ heatMonth.percent }}</em> %</span>
                    </div>
                  </div>
                  <div style="height: 85%">
                    <single-bars-chart
                      :show-legend="false"
                      :chart-data="heatMonth.bar1"
                      :bar-width="20"
                      :x-data="heatMonth.barX"/>
                  </div>
                </div>
              </screen-border>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <!--产量-->
      <el-dialog
        :visible.sync="steelYesterday.dialogVisible"
        :width="'80%'"
        :close-on-click-modal="false"
        class="screen-dialog"
        title="产量详情">
        <template v-slot:title>
          <div class="custom-dialog-title">
            <div class="btn-box">
              <span
                class="screen-btn"
                @click="SyncData(steelOutputTask)">
                手动同步数据
              </span>
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="clearShowGridData('steelYesterday')">
                清空数据
              </span>
              <el-date-picker
                v-model="cDate"
                :clearable="false"
                :size="'mini'"
                :value-format="'yyyy-MM-dd'"
                class="screen-input"
                @change="changeDate"/>
              <template v-if="canEdit">
                <el-dropdown @command="handleProcessedCommand($event, 'importSteelData')">
                  <el-upload
                    ref="upload"
                    :show-file-list="false"
                    :on-change="handlePreview"
                    :auto-upload="false"
                    :action="''"
                    style="display: inline-block">
                    <span
                      class="screen-btn">
                      <el-icon class="el-icon-edit-outline"/>
                      EXCEL导入
                    </span>
                  </el-upload>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      command="yesterday"
                      icon="el-icon-copy">
                      从上一日导入
                    </el-dropdown-item>
                    <el-dropdown-item
                      command="other"
                      icon="el-icon-copy">
                      从其他日期导入
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
              <span
                class="screen-btn"
                @click="exportSteel">
                <el-icon class="el-icon-export"/>
                导出
              </span>
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="saveSteel">
                <el-icon class="el-icon-document-checked"/>
                保存
              </span>
            </div>
            产量详情
          </div>
        </template>
        <el-form
          v-loading="syncLoading"
          :disabled="!canEdit">
          <el-table
            v-loading="loading"
            :data="steelYesterday.showGridData"
            border>
            <el-table-column
              type="index"
              label="序号"
              width="45"/>
            <el-table-column
              property="plt"
              label="产线"
              width="100">
              <template v-slot="{ row }">
                <el-input
                  v-model="row.plt" />
              </template>
            </el-table-column>
            <el-table-column
              property="unit"
              label="单位">
              <template v-slot="{ row }">
                <el-select
                  :popper-append-to-body="false"
                  v-model="row.unit">
                  <el-option
                    v-for="(item, index) in unitList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              property="plan"
              label="计划">
              <template v-slot="{ row }">
                <el-input
                  v-model="row.plan" />
              </template>
            </el-table-column>
            <el-table-column
              property="reality"
              label="实绩">
              <template v-slot="{ row }">
                <el-input
                  v-model="row.reality"
                  :class="{'input-green': row.unit !== '炉数'}" />
              </template>
            </el-table-column>
            <el-table-column
              property="complete"
              label="是否完成">
              <template v-slot="{ row }">
                <el-select
                  :popper-append-to-body="false"
                  v-model="row.complete">
                  <el-option
                    v-for="(item, index) in finishList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              property="unfinishedCause"
              label="未完成原因">
              <template v-slot="{ row }">
                <el-input v-model="row.unfinishedCause" />
              </template>
            </el-table-column>
            <el-table-column
              property="responsibleUnit"
              label="责任单位">
              <template v-slot="{ row }">
                <el-input v-model="row.responsibleUnit" />
              </template>
            </el-table-column>
            <el-table-column
              property="cumulativeOutput"
              label="月计划产量">
              <template v-slot="{ row }">
                <el-input
                  v-model="row.monplan"
                  :class="{'input-green': row.unit !== '炉数'}" />
              </template>
            </el-table-column>
            <el-table-column
              property="targetProduction"
              label="月目标产量">
              <template v-slot="{ row }">
                <el-input
                  v-model="row.targetProduction"
                  :class="{'input-green': row.unit !== '炉数'}" />
              </template>
            </el-table-column>
            <el-table-column
              property="cumulativeOutput"
              label="累计产量">
              <template v-slot="{ row }">
                <el-input
                  v-model="row.cumulativeOutput"
                  :class="{'input-green': row.unit !== '炉数'}" />
              </template>
            </el-table-column>
            <el-table-column
              property="mtcPfdTime"
              label="已执行检修时间（h）">
              <template v-slot="{ row }">
                <el-input
                  v-model="row.mtcPfdTime" />
              </template>
            </el-table-column>
            <el-table-column
              property="rmMtTime"
              label="剩余检修时间（h）">
              <template v-slot="{ row }">
                <el-input
                  v-model="row.rmMtTime" />
              </template>
            </el-table-column>
            <el-table-column
              property="targetSchedule"
              label="超欠目标进度(%)">
              <template v-slot="{ row }">
                <el-input v-model="row.targetSchedule" />
              </template>
            </el-table-column>
            <el-table-column
              property="targetSchedule"
              label="日需均产">
              <template v-slot="{ row }">
                <el-input v-model="row.avgDailyProduction" />
              </template>
            </el-table-column>
            <el-table-column
              property="progress"
              label="操作"
              width="100">
              <template v-slot="{ row, $index }">
                <span
                  v-if="canEdit"
                  class="screen-btn"
                  @click="delShowGridData($index, 'steelYesterday')">
                  <el-icon class="el-icon-delete"/>
                  删除
                </span>
              </template>
            </el-table-column>
          </el-table>
          <div class="text-center">
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="addShowGridData('steelYesterday')">
              <el-icon class="el-icon-circle-plus-outline"/>
              增加数据
            </span>
          </div>
          <br>
          <!-- <el-table
            v-loading="loading3"
            :data="SteelRolligInterval"
            class="center-table"
            border>
            <el-table-column
              label="产线"
              property="factoryName">
              <template v-slot="{ row }">
                <el-input v-model="row.factoryName" />
              </template>
            </el-table-column>
            <el-table-column
              label="<=30s"
              property="lessThirtySeconds">
              <template v-slot="{ row }">
                <el-input v-model="row.lessThirtySeconds" />
              </template>
            </el-table-column>
            <el-table-column
              label="<=60s"
              property="lessSixtySeconds">
              <template v-slot="{ row }">
                <el-input v-model="row.lessSixtySeconds" />
              </template>
            </el-table-column>
          </el-table> -->
          <br>
          <el-form-item label="喜报:">
            <el-input
              v-model="steelYesterday.noticeEdit"
              type="textarea"
              placeholder="输入喜报"/>
          </el-form-item>
        </el-form>
      </el-dialog>
      <!--热处理详情-->
      <el-dialog
        :visible.sync="heatYesterday.dialogVisible"
        :width="'80%'"
        :close-on-click-modal="false"
        class="screen-dialog"
        title="热处理详情">
        <template v-slot:title>
          <div class="custom-dialog-title">
            <div class="btn-box">
              <span
                class="screen-btn"
                @click="showNum()">
                显示
              </span>
              <span
                class="screen-btn"
                @click="SyncData(HeatTreatmentYieldTask)">
                手动同步数据
              </span>
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="clearGridData('heatYesterday')">
                清空数据
              </span>
              <el-date-picker
                v-model="cDate"
                :clearable="false"
                :size="'mini'"
                :value-format="'yyyy-MM-dd'"
                class="screen-input"
                @change="changeDate"/>
              <el-dropdown
                v-if="canEdit"
                @command="handleProcessedCommand($event, 'importHeatData')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handleHeatPreview"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <span
                class="screen-btn"
                @click="exportHeat">
                <el-icon class="el-icon-export"/>
                导出
              </span>
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="saveHeat">
                <el-icon class="el-icon-document-checked"/>
                保存
              </span>
            </div>
            热处理详情
          </div>
        </template>
        <el-form :disabled="!canEdit">
          <el-table
            v-loading="loading"
            :data="heatYesterday.gridData"
            border>
            <el-table-column
              type="index"
              label="序号"
              width="50"/>
            <el-table-column
              v-if="numFlag"
              property="num1"
              width="80"
              label="数据1">
              <template v-slot="{ row }">
                <el-input
                  v-model="row.num1"
                  @blur="blurChange(row, 'num1')" />
              </template>
            </el-table-column>
            <el-table-column
              v-if="numFlag"
              property="num2"
              width="80"
              label="数据2">
              <template v-slot="{ row }">
                <el-input
                  v-model="row.num2"
                  @blur="blurChange(row, 'num2')"
                />
              </template>
            </el-table-column>
            <el-table-column
              property="hearthnumber"
              label="炉座号"
              width="80"/>
            <el-table-column
              property="plan"
              label="计划">
              <template v-slot="{ row }">
                <el-input
                  v-model="row.plan"
                  @blur="blurChange(row, 'plan')"
                />
              </template>
            </el-table-column>
            <el-table-column
              property="reality"
              label="实绩">
              <template v-slot="{ row }">
                <el-input
                  v-model="row.reality"
                  @blur="blurChange(row, 'reality')"
                />
              </template>
            </el-table-column>
            <el-table-column
              property="reality"
              label="装钢延误时间">
              <template v-slot="{ row }">
                <el-input v-model="row.loadingdelaytime" />
              </template>
            </el-table-column>
            <el-table-column
              property="complete"
              label="是否完成">
              <template v-slot="{ row }">
                <el-select
                  :popper-append-to-body="false"
                  v-model="row.complete">
                  <el-option
                    v-for="(item, index) in finishList"
                    :key="index"
                    :value="item">
                    {{ item }}
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              property="unfinishedCause"
              label="未完成原因">
              <template v-slot="{ row }">
                <el-input v-model="row.unfinishedcause" />
              </template>
            </el-table-column>
            <el-table-column
              property="responsibleUnit"
              label="月作业天数">
              <template v-slot="{ row }">
                <el-input v-model="row.homeworkdays" />
              </template>
            </el-table-column>
            <el-table-column
              property="responsibleUnit"
              label="月目标产量">
              <template v-slot="{ row }">
                <el-input v-model="row.plannedmonthlyoutput" />
              </template>
            </el-table-column>
            <el-table-column
              property="cumulativeOutput"
              label="累计产量">
              <template v-slot="{ row }">
                <el-input v-model="row.cumulativeoutput" />
              </template>
            </el-table-column>
            <el-table-column
              property="targetSchedule"
              label="超欠目标进度(%)">
              <template v-slot="{ row }">
                <el-input v-model="row.targetschedule" />
              </template>
            </el-table-column>
            <el-table-column
              property="avgDailyProduction"
              label="日需均产">
              <template v-slot="{ row }">
                <el-input v-model="row.avgDailyProduction" />
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-dialog>
      <!--导入日期-->
      <el-dialog
        :visible.sync="importDateVisible"
        :width="'400px'"
        :close-on-click-modal="false"
        :append-to-body="false"
        class="screen-dialog"
        title="导入日期选择">
        <template v-slot:title>
          <div class="custom-dialog-title">
            导入日期选择
          </div>
        </template>
        <el-form
          label-width="120px"
          class="demo-form-inline">
          <el-form-item label="导入日期">
            <el-date-picker
              v-model="importDate"
              :clearable="false"
              :value-format="'yyyy-MM-dd'"/>
          </el-form-item>
          <div class="text-center">
            <el-button
              type="primary"
              @click="importHistoryData()">确定</el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { batchUpdateResource } from '@/api/system'
import { post } from '@/lib/Util'
import * as _ from 'lodash'
import {
  findBoardParameterByDateAndPara,
  findHeatTreatmentYieldByDate,
  findSteelOutputByDate,
  HeatTreatmentYieldTask,
  saveBoardParameter,
  saveHeatTreatmentYield,
  saveSteelOutput,
  steelOutputTask,
  SteelRolligIntervalFindAllDate,
  SteelRolligIntervalSaveAll
} from '@/api/screen'
import { math } from '@/lib/Math'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import NoticeBar from '@/pages/screen/morningMeeting/component/notice-bar'
export default {
  name: 'Output',
  components: { NoticeBar, SingleBarsChart, SteelBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      numFlag: true,
      cDate: '',
      steelOutputTask: steelOutputTask,
      HeatTreatmentYieldTask: HeatTreatmentYieldTask,
      loading: false,
      steelYesterday: {
        bar1: [
          {
            value: 0,
            plan: 0,
            unit: '吨'
          }
        ],
        bar2: [
          {
            value: 0,
            plan: 0,
            unit: '炉'
          },
          {
            value: 0,
            plan: 0,
            unit: '炉'
          }
        ],
        failReason: '',
        notice: '',
        noticeEdit: '',
        editType: '',
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      steelMonth: {
        bar1: [
          {
            value: 0,
            plan: 0,
            unit: '吨'
          }
        ],
        bar2: [
          {
            value: 0,
            plan: 0,
            unit: '炉'
          },
          {
            value: 0,
            plan: 0,
            unit: '炉'
          }
        ],
        output: 0,
        targetSchedule: 0,
        percent: 0
      },
      rollYesterday: {
        bar1: [],
        failReason: ''
      },
      rollMonth: {
        bar1: [],
        failReason: '',
        output: 0,
        targetSchedule: 0,
        percent: 0
      },
      otherYesterday: {
        bar1: [],
        failReason: ''
      },
      SteelRolligInterval: [],
      loading3: true,
      otherMonth: {
        bar1: [],
        failReason: '',
        targetScheduleD1: '',
        targetScheduleD2: '',
        targetScheduleD22: '',
        outputD1: '',
        outputD2: '',
        outputD22: '',
        percentD1: '',
        percentD2: '',
        percentD22: ''
      },
      heatYesterday: {
        bar1: [],
        barX: [],
        failReason: '',
        gridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      heatMonth: {
        bar1: [],
        barX: [],
        failReason: '',
        output: 0,
        targetSchedule: 0,
        percent: 0
      },
      unitList: ['吨位', '炉数'],
      finishList: ['是', '否']
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.$nextTick(() => {
        this.loadData()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.loadData()
  },
  methods: {
    //显示隐藏num1  num2
    showNum() {
      this.numFlag = !this.numFlag
    },
    blurChange(item, flag) {
      if (flag == 'num1' || flag == 'num2') {
        if (item.num1 >= item.num2) {
          item.plan = item.num1
        } else {
          item.plan = item.num2
        }
      }
      if (item.reality >= item.plan) {
        item.complete = '是'
      } else {
        item.complete = '否'
      }
    },
    async loadData() {
      await this.getZZP()
      this.$nextTick(() => {
        this.getSteal()
        this.getHeat()
      })
    },
    handlePreview(file) {
      try {
        LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
          data = LAY_EXCEL.filterImportData(data, {
            // A: "序号 "
            // B: "产线 "
            // C: ""
            // D: "计划 "
            // E: "实际 "
            // F: "是否完成"
            // G: "未完成原因 "
            // H: "责任单位 "
            // I: "累计产量"
            // J: "超欠目标进度"
            num: 'A',
            plt: 'B',
            unit: 'C',
            plan: 'D',
            reality: 'E',
            complete: 'F',
            unfinishedCause: 'G',
            responsibleUnit: 'H',
            monplan: 'I',
            targetProduction: 'J',
            cumulativeOutput: 'K',
            mtcPfdTime: 'L',
            rmMtTime: 'M',
            targetSchedule: 'N',
            avgdailyproduction: 'O'
          })
          // 去除第一行
          const sheet = data[0].Sheet1 || data[0].sheet1
          sheet.shift()
          // 表格信息
          this.steelYesterday.showGridData = sheet.map(item => {
            item.unit = item.unit.trim()
            item.targetSchedule = item.targetSchedule.toString()
            item.type = this.steelYesterday.editType
            if (item.targetSchedule.includes('%')) {
              item.targetSchedule = item.targetSchedule.replace('%', '')
            } else {
              item.targetSchedule = Number(
                math.multiply(Number(item.targetSchedule), 100).toFixed(2)
              )
            }
            return item
          })
          this.$message.success('解析成功！')
        })
      } catch (e) {
        this.$message.warning('解析失败！')
      }
    },
    handleSteel(type) {
      this.steelYesterday.editType = type
      this.steelYesterday.showGridData = this.steelYesterday.gridData.filter(
        item => item.type === type
      )
      console.log(this.steelYesterday.gridData, type)
      this.steelYesterday.dialogVisible = true
    },
    getSteal() {
      this.steelYesterday.gridData = []
      // this.clearViewData()
      post(findSteelOutputByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.clearViewData()
        this.steelYesterday.gridData = res.data.map(item => {
          return {
            num: item.num,
            plt: item.plt,
            unit: item.unit,
            plan: item.plan,
            reality: item.reality,
            complete: item.complete,
            unfinishedCause: item.unfinishedcause,
            responsibleUnit: item.responsibleunit,
            monplan: item.monplan,
            targetProduction: item.targetproduction,
            cumulativeOutput: item.cumulativeoutput,
            mtcPfdTime: item.mtcpfdtime,
            rmMtTime: item.rmmttime,
            targetSchedule: Number(item.targetschedule || 0).toFixed(2),
            avgDailyProduction: item.avgdailyproduction,
            type: item.type,
            rclwgt: item.rclwgt,
            machiningwgt: item.machiningwgt,
            monrclwgt: item.monrclwgt,
            monmachiningwgt: item.monmachiningwgt
          }
        })
        this.steelYesterday.editType &&
          (this.steelYesterday.showGridData = this.steelYesterday.gridData.filter(
            item => item.type === this.steelYesterday.editType
          ))
        if (!res.data.length) return
        this.formatViewData()
      })
    },
    clearViewData() {
      Object.assign(this.steelYesterday, {
        bar1: [
          {
            value: 0,
            plan: 0,
            unit: '吨'
          }
        ],
        bar2: [
          {
            value: 0,
            plan: 0,
            unit: '炉'
          },
          {
            value: 0,
            plan: 0,
            unit: '炉'
          }
        ],
        failReason: '',
        gridData: [],
        showGridData: [],
        gridMerge: []
      })
      this.steelMonth = {
        bar1: [
          {
            value: 0,
            plan: 0,
            unit: '吨'
          }
        ],
        bar2: [
          {
            value: 0,
            plan: 0,
            unit: '炉'
          },
          {
            value: 0,
            plan: 0,
            unit: '炉'
          }
        ],
        output: 0,
        targetSchedule: 0,
        percent: 0
      }
      this.rollYesterday = {
        bar1: [],
        failReason: ''
      }
      this.rollMonth = {
        bar1: [],
        failReason: '',
        output: 0,
        targetSchedule: 0,
        percent: 0
      }
      this.otherYesterday = {
        bar1: [],
        failReason: ''
      }
      this.otherMonth = {
        bar1: [],
        failReason: '',
        targetScheduleD1: '',
        targetScheduleD2: '',
        outputD1: '',
        outputD2: '',
        percentD1: '',
        percentD2: ''
      }
    },
    formatViewData() {
      try {
        const b11 = this.steelYesterday.gridData.find(
          item => item.plt.trim() === 'B1' && item.unit.trim() === '吨位'
        )
        const b12 = this.steelYesterday.gridData.find(
          item => item.plt.trim() === 'B1' && item.unit.trim() === '炉数'
        )
        this.steelYesterday.failReason = b11 ? b11.unfinishedCause : ''
        b11 &&
          Object.assign(this.steelYesterday, {
            bar1: [
              {
                value: b11 ? b11.reality : 0,
                plan: b11 ? b11.plan : 0,
                finished: b11.complete !== '否',
                unit: '吨',
                show: true,
                avgDaily: b11.avgDailyProduction ? b11.avgDailyProduction : ''
              }
            ],
            bar2: [
              {
                value: 0,
                plan: 0,
                unit: '炉',
                show: false
              },
              {
                value: b12 ? b12.reality : 0,
                plan: b12 ? b12.plan : 0,
                finished: b12.complete !== '否',
                unit: '炉',
                show: true,
                avgDaily: b12.avgDailyProduction ? b12.avgDailyProduction : ''
              }
            ]
          })
        b11 &&
          Object.assign(this.steelMonth, {
            bar1: [
              {
                value: b11.cumulativeOutput,
                plan: b11.targetProduction || b11.monplan,
                finished: b11.targetSchedule >= 0,
                unit: '吨',
                totalText: b11.targetProduction ? '目标' : '计划',
                targetSchedule: b11.targetSchedule ? b11.targetSchedule : '',
                schedule: b11.targetSchedule
                  ? math.divide(
                      math.multiply(
                        b11.targetSchedule || 0,
                        b11.targetProduction || b11.monplan
                      ),
                      100
                    )
                  : '',
                avgDaily: b11.avgDailyProduction ? b11.avgDailyProduction : ''
              }
            ],
            targetSchedule: math.divide(
              math.multiply(
                b11.targetSchedule || 0,
                b11.targetProduction || b11.monplan
              ),
              100
            ),
            // targetSchedule: Math.abs(
            //   math.divide(
            //     math.multiply(
            //       b11.targetSchedule || 0,
            //       b11.targetProduction || b11.monplan
            //     ),
            //     100
            //   )
            // ),
            // targetSchedule: b11.cumulativeOutput - b11.targetProduction,
            output: b11.cumulativeOutput,
            percent: b11.targetSchedule
          })
      } catch (e) {}

      try {
        const C1 = this.steelYesterday.gridData.find(
          item => item.plt.trim() === 'C1'
        )
        const C2 = this.steelYesterday.gridData.find(
          item => item.plt.trim() === 'C2'
        )
        const C3 = this.steelYesterday.gridData.find(
          item => item.plt.trim() === 'C3'
        )

        Object.assign(this.rollYesterday, {
          bar1: [
            {
              value: C1 ? C1.reality : 0,
              plan: C1 ? C1.plan : 0,
              finished: C1.complete !== '否',
              unit: '吨',
              failReason: C1.unfinishedCause ? C1.unfinishedCause : '',
              avgDaily: C1.avgDailyProduction ? C1.avgDailyProduction : ''
            },
            {
              value: C2 ? C2.reality : 0,
              plan: C2 ? C2.plan : 0,
              finished: C2.complete !== '否',
              unit: '吨',
              failReason: C2.unfinishedCause ? C2.unfinishedCause : '',
              avgDaily: C2.avgDailyProduction ? C2.avgDailyProduction : ''
            },
            {
              value: C3 ? C3.reality : 0,
              plan: C3 ? C3.plan : 0,
              finished: C3.complete !== '否',
              unit: '吨',
              failReason: C3.unfinishedCause ? C3.unfinishedCause : '',
              avgDaily: C3.avgDailyProduction ? C3.avgDailyProduction : ''
            }
          ],
          failReason:
            `${
              C1 && C1.unfinishedCause ? 'C1：' + C1.unfinishedCause + '；' : ''
            }` +
            `${
              C2 && C2.unfinishedCause ? 'C2：' + C2.unfinishedCause + '；' : ''
            }` +
            `${
              C3 && C3.unfinishedCause ? 'C3：' + C3.unfinishedCause + '；' : ''
            }`
        })
        Object.assign(this.rollMonth, {
          bar1: [
            {
              value: C1 ? C1.cumulativeOutput : 0,
              plan: C1 ? C1.targetProduction || C1.monplan : 0,
              finished: C1.targetSchedule >= 0,
              totalText: C1.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C1.targetSchedule ? C1.targetSchedule : '',
              schedule: C1.targetSchedule
                ? math.divide(
                    math.multiply(
                      C1.targetSchedule || 0,
                      C1.targetProduction || C1.monplan
                    ),
                    100
                  )
                : '',
              avgDaily: C1.avgDailyProduction ? C1.avgDailyProduction : ''
            },
            {
              value: C2 ? C2.cumulativeOutput : 0,
              plan: C2 ? C2.targetProduction || C2.monplan : 0,
              finished: C2.targetSchedule >= 0,
              totalText: C2.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C2.targetSchedule ? C2.targetSchedule : '',
              schedule: C2.targetSchedule
                ? math.divide(
                    math.multiply(
                      C2.targetSchedule || 0,
                      C2.targetProduction || C2.monplan
                    ),
                    100
                  )
                : '',
              avgDaily: C2.avgDailyProduction ? C2.avgDailyProduction : ''
            },
            {
              value: C3 ? C3.cumulativeOutput : 0,
              plan: C3 ? C3.targetProduction || C3.monplan : 0,
              finished: C3.targetSchedule >= 0,
              totalText: C3.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: C3.targetSchedule ? C3.targetSchedule : '',
              schedule: C3.targetSchedule
                ? math.divide(
                    math.multiply(
                      C3.targetSchedule || 0,
                      C3.targetProduction || C3.monplan
                    ),
                    100
                  )
                : '',
              avgDaily: C3.avgDailyProduction ? C3.avgDailyProduction : ''
            }
          ],
          // targetSchedule:
          //   math.multiply(
          //     math.add(
          //       C1.targetproduction,
          //       C2.targetproduction,
          //       C3.targetproduction
          //     ),
          //     C1.rclwgt
          //   ) / 100,
          targetSchedule: math.add(
            math.multiply(C1.rclwgt, C1.targetProduction || C1.monplan) / 100,
            math.multiply(C2.rclwgt, C2.targetProduction || C2.monplan) / 100,
            math.multiply(C3.rclwgt, C3.targetProduction || C3.monplan) / 100
          ),
          output: math.add(
            C1.cumulativeOutput,
            C2.cumulativeOutput,
            C3.cumulativeOutput
          )
        })
        // 计算进度
        this.rollMonth.percent = C1.rclwgt
        // this.rollMonth.percent = math.multiply(
        //   math
        //     .divide(
        //       this.rollMonth.targetSchedule,
        //       math.add(
        //         C1.targetProduction || C1.monplan,
        //         C2.targetProduction || C2.monplan,
        //         C3.targetProduction || C3.monplan
        //       )
        //     )
        //     .toFixed(4),
        //   100
        // )
      } catch (e) {}

      try {
        const D1 = this.steelYesterday.gridData.find(
          item => item.plt.trim() === '金润'
        )
        console.log('aaa', D1)

        const D2 = this.steelYesterday.gridData.find(
          item => item.plt.trim() === '金石' || item.plt.trim() === '金石石灰'
        )
        const D22 =
          this.steelYesterday.gridData.find(
            item => item.plt.trim() === '金石渣罐'
          ) || {}

        Object.assign(this.otherYesterday, {
          bar1: [
            {
              value: D1 ? D1.reality : 0,
              plan: D1 ? D1.plan : 0,
              rclwgt: D1 ? D1.rclwgt : 0,
              machiningwgt: D1 ? D1.machiningwgt : 0,
              finished: D1.complete !== '否',
              unit: '吨',
              avgDaily: D1.avgDailyProduction ? D1.avgDailyProduction : ''
            },
            {
              value: D2 ? D2.reality : 0,
              plan: D2 ? D2.plan : 0,
              rclwgt: D2 ? D2.rclwgt : 0,
              machiningwgt: D2 ? D2.machiningwgt : 0,
              finished: D2.complete !== '否',
              unit: '吨',
              avgDaily: D2.avgDailyProduction ? D2.avgDailyProduction : ''
            },
            {
              value: D22 ? D22.reality : 0,
              plan: D22 ? D22.plan : 0,
              rclwgt: D22 ? D2.rclwgt : 0,
              machiningwgt: D22 ? D22.machiningwgt : 0,
              finished: D22.complete !== '否',
              unit: '罐',
              avgDaily: D22.avgDailyProduction ? D22.avgDailyProduction : ''
            }
          ],
          failReason:
            `${
              D1 && D1.unfinishedCause
                ? '金润：' + D1.unfinishedCause + '；'
                : ''
            }` +
            `${
              D2 && D2.unfinishedCause
                ? '金石石灰：' + D2.unfinishedCause + '；'
                : ''
            }` +
            `${
              D22 && D22.unfinishedCause
                ? '金石渣罐：' + D22.unfinishedCause + '；'
                : ''
            }`
        })
        Object.assign(this.otherMonth, {
          bar1: [
            {
              value: D1 ? D1.cumulativeOutput : 0,
              plan: D1 ? D1.targetProduction || D1.monplan : 0,
              finished: D1.targetSchedule >= 0,
              monrclwgt: D1 ? D1.monrclwgt : 0,
              monmachiningwgt: D1 ? D1.monmachiningwgt : 0,
              totalText: D1.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: D1.targetSchedule ? D1.targetSchedule : '',
              schedule: D1.targetSchedule
                ? math.divide(
                    math.multiply(
                      D1.targetSchedule || 0,
                      D1.targetProduction || D1.monplan
                    ),
                    100
                  )
                : '',
              avgDaily: D1.avgDailyProduction ? D1.avgDailyProduction : ''
            },
            {
              value: D2 ? D2.cumulativeOutput : 0,
              plan: D2 ? D2.targetProduction || D2.monplan : 0,
              finished: D2.targetSchedule >= 0,
              monrclwgt: D2.monrclwgt >= 0,
              monmachiningwgt: D2.monmachiningwgt >= 0,
              totalText: D2.targetProduction ? '目标' : '计划',
              unit: '吨',
              targetSchedule: D2.targetSchedule ? D2.targetSchedule : '',
              schedule: D2.targetSchedule
                ? math.divide(
                    math.multiply(
                      D2.targetSchedule || 0,
                      D2.targetProduction || D2.monplan
                    ),
                    100
                  )
                : '',
              avgDaily: D2.avgDailyProduction ? D2.avgDailyProduction : ''
            },
            {
              value: D22 ? D22.cumulativeOutput : 0,
              plan: D22 ? D22.targetProduction || D22.monplan : 0,
              finished: D22.targetSchedule >= 0,
              monrclwgt: D22.monrclwgt >= 0,
              monmachiningwgt: D22.monmachiningwgt >= 0,
              totalText: D22.targetProduction ? '目标' : '计划',
              unit: '罐',
              targetSchedule: D22.targetSchedule ? D22.targetSchedule : '',
              schedule: D22.targetSchedule
                ? math.divide(
                    math.multiply(
                      D22.targetSchedule || 0,
                      D22.targetProduction || D22.monplan
                    ),
                    100
                  )
                : '',
              avgDaily: D22 ? D22.avgDailyProduction : ''
            }
          ],
          targetScheduleD1: math.divide(
            math.multiply(D1.targetSchedule, D1.targetProduction || D1.monplan),
            100
          ),
          targetScheduleD2: math.divide(
            math.multiply(D2.targetSchedule, D2.targetProduction || D2.monplan),
            100
          ),
          targetScheduleD22: D22.targetSchedule
            ? math
                .divide(
                  math.multiply(
                    D22.targetSchedule,
                    D22.targetProduction || D22.monplan
                  ),
                  100
                )
                .toFixed(0)
            : 0,

          outputD1: D1.cumulativeOutput || 0,
          outputD2: D2.cumulativeOutput || 0,
          outputD22: D22.cumulativeOutput || 0,
          percentD1: D1.targetSchedule || 0,
          percentD2: D2.targetSchedule || 0,
          percentD22: D22.targetSchedule || 0
        })
      } catch (e) {}
    },
    async saveSteel() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        type: this.steelYesterday.editType,
        data: this.steelYesterday.showGridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }

      //确保params.data中的每一个plt有值，没有的话，全局给出提示，不再走下面
      let isAllPlt = params.data.every(item => item.plt)
      if (!isAllPlt) {
        this.$message.warning('请确保每行的产线有对应值')
        this.loading = false
        return
      }

      this.SteelRolligIntervalSaveAll()

      post(saveSteelOutput, params).then(res => {
        //
        if (res.status === 1) {
          this.getSteal()
        } else {
          this.$message.warning('保存失败！')
        }
        const params2 = {
          data: [
            {
              parameter: 'outputNotice',
              content: this.steelYesterday.noticeEdit,
              setDate: this.cDate
            }
          ]
        }
        post(saveBoardParameter, params2).then(res => {
          this.loading = false
          if (res.status === 1) {
            this.steelYesterday.dialogVisible = false
            this.getZZP()
            this.$message.success('保存成功！')
          }
        })
      })
    },
    exportSteel() {
      const data = [
        {
          num: '序号',
          plt: '产线',
          unit: '单位',
          plan: '计划',
          reality: '实际',
          complete: '是否完成',
          unfinishedCause: '未完成原因',
          responsibleUnit: '责任单位',
          monplan: '月计划',
          targetProduction: '月目标',
          cumulativeOutput: '累计产量',
          mtcPfdTime: '已执行检修时间',
          rmMtTime: '剩余检修时间',
          targetSchedule: '超欠目标进度',
          avgDailyProduction: '日需均产'
        }
      ].concat(
        _.cloneDeep(this.steelYesterday.showGridData).map(item => {
          item.targetSchedule = item.targetSchedule + '%'
          delete item.type
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `钢产量详情（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    handleHeatPreview(file) {
      try {
        LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
          data = LAY_EXCEL.filterImportData(data, {
            // A: "炉座号"
            // B: "计划"
            // C: "实际"
            // D: "装钢延误时间"
            // E: "是否完成"
            // F: "未完成原因"
            // G: "月目标产量"
            // H: "当前累计产量"
            // I: "超欠进度"
            hearthnumber: 'A',
            plan: 'B',
            reality: 'C',
            loadingdelaytime: 'D',
            complete: 'E',
            unfinishedcause: 'F',
            homeworkdays: 'G',
            plannedmonthlyoutput: 'H',
            cumulativeoutput: 'I',
            targetschedule: 'J',
            avgDailyProduction: 'K'
          })
          // 去除第一行
          const sheet = data[0].Sheet1 || data[0].sheet1
          if (!sheet) this.$message('未找到sheet1，请检查！')
          sheet.shift()
          // 表格信息
          this.heatYesterday.gridData = sheet.map(item => {
            item.hearthnumber = item.hearthnumber.trim()
            item.targetschedule = item.targetschedule.toString()
            if (item.targetschedule.includes('%')) {
              item.targetschedule = item.targetschedule.replace('%', '')
            } else {
              item.targetschedule = Number(
                math.multiply(Number(item.targetschedule), 100).toFixed(2)
              )
            }
            return item
          })
          this.$message.success('解析成功！')
        })
      } catch (e) {
        this.$message.warning('解析失败！')
      }
    },
    exportHeat() {
      const data = [
        {
          hearthnumber: '炉座号',
          plan: '计划',
          reality: '实际',
          loadingdelaytime: '装钢延误时间',
          complete: '是否完成',
          unfinishedcause: '未完成原因',
          homeworkdays: '月作业天数',
          plannedmonthlyoutput: '月目标产量',
          cumulativeoutput: '当前累计产量',
          targetschedule: '超欠进度',
          avgDailyProduction: '日需均产'
        }
      ].concat(
        _.cloneDeep(this.heatYesterday.gridData).map(item => {
          item.targetschedule = item.targetschedule + '%'
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `热处理产量详情（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    getHeat() {
      this.SteelRolligIntervalFindAllDate()
      post(findHeatTreatmentYieldByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.heatYesterday.gridData = res.data.map(item => {
          return {
            num1: item.num1,
            // Number(item.reality * 0.97) - Number((item.reality * 0.97) % 10),
            num2: item.num2,
            hearthnumber: item.hearthnumber,
            plan: item.plan,
            reality: item.reality,
            loadingdelaytime: item.loadingdelaytime,
            complete: item.complete,
            unfinishedcause: item.unfinishedcause,
            homeworkdays: item.homeworkdays,
            plannedmonthlyoutput: item.plannedmonthlyoutput,
            cumulativeoutput: item.cumulativeoutput,
            targetschedule: Number(item.targetschedule).toFixed(2),
            avgDailyProduction: item.avgdailyproduction
          }
        })
        if (!res.data.length) {
          this.heatYesterday = {
            bar1: [],
            barX: [],
            failReason: '',
            gridData: [],
            gridMerge: [],
            dialogVisible: false
          }
          this.heatMonth = {
            bar1: [],
            barX: [],
            failReason: '',
            output: 0,
            targetSchedule: 0,
            percent: 0
          }
          return
        }
        Object.assign(this.heatYesterday, {
          bar1: res.data
            .filter(item => item.hearthnumber !== '合计')
            .map(item => {
              return {
                value: item.reality,
                plan: item.plan,
                failReason: item.unfinishedcause,
                finished: item.reality >= item.plan,
                unit: '吨',
                avgDaily: item.avgdailyproduction ? item.avgdailyproduction : ''
              }
            }),
          barX: res.data
            .filter(item => item.hearthnumber !== '合计')
            .map(item => {
              return this.formatName(item.hearthnumber, 3)
            })
        })
        const merge = res.data.find(item => item.hearthnumber === '合计')
        Object.assign(this.heatMonth, {
          bar1: res.data
            .filter(item => item.hearthnumber !== '合计')
            .map(item => {
              return {
                value: item.cumulativeoutput,
                plan: item.plannedmonthlyoutput,
                finished: item.targetschedule > 0,
                unit: '吨',
                targetSchedule: item.targetschedule ? item.targetschedule : '',
                schedule: item.targetschedule
                  ? math.divide(
                      math.multiply(
                        item.targetschedule || 0,
                        item.plannedmonthlyoutput
                      ),
                      100
                    )
                  : '',
                avgDaily: item.avgdailyproduction ? item.avgdailyproduction : ''
              }
            }),
          barX: res.data
            .filter(item => item.hearthnumber !== '合计')
            .map(item => {
              return this.formatName(item.hearthnumber, 3)
            }),
          output: merge ? merge.cumulativeoutput : 0,
          targetSchedule: merge
            ? math
                .multiply(
                  Number(merge.plannedmonthlyoutput || 0),
                  Number(math.divide(merge.targetschedule, 100) || 0)
                )
                .toFixed(2)
            : 0,

          percent: merge ? Number(merge.targetschedule).toFixed(2) : 0
        })
        this.heatYesterday.failReason = this.heatYesterday.gridData
          .filter(item => item.unfinishedcause)
          .map(item => item.hearthnumber + '：' + item.unfinishedcause)
          .join('；')
      })
    },
    saveHeat() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.heatYesterday.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(saveHeatTreatmentYield, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.heatYesterday.dialogVisible = false
          this.getHeat()
        }
      })
    },
    SteelRolligIntervalFindAllDate() {
      this.loading3 = false
      post(SteelRolligIntervalFindAllDate, {
        setDate: this.cDate
      }).then(res => {
        console.log('res', res)
        this.SteelRolligInterval = res.data
      })
    },
    SteelRolligIntervalSaveAll() {
      let parms0 = this.SteelRolligInterval.map(item => {
        delete item.id
        return item
      })
      let parm1 = parms0.map(item => {
        delete item.createTime
        return item
      })
      const params = {
        data: parm1,
        setDate: this.cDate
      }
      post(SteelRolligIntervalSaveAll, params).then(res => {
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.SteelRolligIntervalFindAllDate()
        }
      })
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
    },
    getMergeData(rowIndex, columnIndex) {
      const matchLeftTop = this.steelYesterday.gridMerge.find(
        item => item.s.c === columnIndex && item.s.r === rowIndex
      )
      if (matchLeftTop) {
        return [
          matchLeftTop.e.r - matchLeftTop.s.r + 1,
          matchLeftTop.e.c - matchLeftTop.s.c + 1
        ]
      }
      const merged = this.steelYesterday.gridMerge.find(item => {
        return (
          item.s.c < columnIndex &&
          columnIndex <= item.e.c &&
          item.s.r < rowIndex &&
          rowIndex <= item.e.r
        )
      })
      if (merged) {
        console.log(merged)
        return [0, 0]
      }
    },
    changeDate($event) {
      this.$emit('dateChange', $event)
    },
    formatName(value, num = 6) {
      let maxLength = num //每项显示文字个数
      let valLength = value.length //X轴类目项的文字个数
      let rowN = Math.ceil(valLength / maxLength) //类目项需要换行的行数
      if (rowN > 1) {
        // 如果类目项的文字大于3,
        let ret = value.substring(0, maxLength) //每次截取的字符串
        let start = 0 //开始截取的位置
        for (let i = 1; i < rowN; i++) {
          start += maxLength
          ret += '\n' + value.substring(start, start + maxLength)
        }
        return ret
      } else {
        // return percent
        return value
      }
    },
    importHeatData(date) {
      post(findHeatTreatmentYieldByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.heatYesterday.gridData = res.data.map(item => {
          return {
            hearthnumber: item.hearthnumber,
            plan: item.plan,
            reality: item.reality,
            loadingdelaytime: item.loadingdelaytime,
            complete: item.complete,
            homeworkdays: item.homeworkdays,
            unfinishedcause: item.unfinishedcause,
            plannedmonthlyoutput: item.plannedmonthlyoutput,
            cumulativeoutput: item.cumulativeoutput,
            targetschedule: item.targetschedule,
            avgDailyProduction: item.avgdailyproduction
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    importSteelData(date) {
      post(findSteelOutputByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.steelYesterday.showGridData = res.data
          .filter(item => item.type === this.steelYesterday.editType)
          .map(item => {
            return {
              num: item.num,
              plt: item.plt,
              unit: item.unit,
              plan: item.plan,
              reality: item.reality,
              complete: item.complete,
              unfinishedCause: item.unfinishedcause,
              responsibleUnit: item.responsibleunit,
              monplan: item.monplan,
              targetProduction: item.targetproduction,
              cumulativeOutput: item.cumulativeoutput,
              mtcPfdTime: item.mtcpfdtime,
              rmMtTime: item.rmmttime,
              targetSchedule: Number(item.targetschedule || 0).toFixed(2),
              avgDailyProduction: item.avgdailyproduction,
              type: item.type
            }
          })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    // 数据管理
    clearShowGridData(name) {
      this[name].showGridData = []
    },
    addShowGridData(name) {
      this[name].showGridData.push({})
    },
    delShowGridData(index, name) {
      console.log(name)
      this[name].showGridData.splice(index, 1)
    },
    async getZZP() {
      // 获取喜报参数
      const parameters = await post(findBoardParameterByDateAndPara, {
        setDate: this.cDate
      })
      this.steelYesterday.notice = this.steelYesterday.noticeEdit = this.getParam(
        'outputNotice',
        parameters.data
      )
      return new Promise(resolve => {
        resolve(true)
      })
    },
    getParam(name, list) {
      const match = list.find(item => item.parameter === name)
      return match ? match.content : null
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .slick {
    position: relative;
    //top: -15px;
    margin-bottom: 10px;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    width: 90px;
    line-height: 24px;
    font-size: 16px;
    white-space: nowrap;
    color: #ffffff;
  }
  span:last-child {
    flex: 1;
    overflow: auto;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    font-size: 0;
    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.green {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
