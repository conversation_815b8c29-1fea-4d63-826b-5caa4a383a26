<template>
  <div>

    <div class="page-content">
      <div class="page-operate">

        <div class="search-wrapper">
          <el-form
            ref="form"
            :label-width="'80px'"
            :model="searchForm"
            size="mini"
            inline
            @submit.native.prevent="handleSearch(true)"
          >
            <el-form-item
              prop="nickname"
            >
              <el-input
                v-model="searchForm.roleName"
                suffix-icon="el-icon-search"
                clearable
                placeholder="请输入角色名称"
                style="width: 200px"
                type="text"
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="text-right">
          <el-button
            icon="el-icon-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
        </div>
      </div>
      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
            width="180"
          />
          <el-table-column
            label="角色编码"
            prop="roleCode"
            width="180"
          />
          <el-table-column
            label="角色名称"
            prop="roleName"
          />
          <el-table-column
            label="状态"
            prop="status"
          >
            <template
              v-slot="{row}"
            >
              <el-tag
                :type="roleStatusName(row.status).type"
                disable-transitions
              >{{ roleStatusName(row.status).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="270"
          >
            <template
              v-slot="{row}"
            >
              <span v-if="row.status === 1">
                <el-button
                  v-command="'/kpi/role/resource'"
                  size="small"
                  type="text"
                  @click="distributeResource(row)"
                >指标权限管理
                </el-button>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.currentPage"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <DistributeResource
      v-if="visibleResource"
      :role="editRole"
      :visible.sync="visibleResource"
    />
  </div>
</template>

<script>
import DistributeResource from './component/distributeResource'
import listMixins from '@/mixins/ListMixins'
import { roleDelete, roleList } from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'

export default {
  layout: 'menuLayout',
  name: 'Role',
  components: {
    DistributeResource
  },
  mixins: [listMixins],
  data: () => {
    return {
      visibleDistribute: false,
      visibleResource: false,
      searchForm: {
        roleCode: 'KPI200'
        // roleType: 1
      },
      url: {
        list: roleList, //分页接口地址
        delete: roleDelete //删除接口地址
      },
      roleStatusList: ENUM.roleStatus,
      editRole: null
    }
  },
  methods: {
    distributeUser(data) {
      this.editRole = data
      this.visibleDistribute = !this.visibleDistribute
    },
    distributeResource(data) {
      this.editRole = data
      this.visibleResource = !this.visibleResource
    },
    roleStatusName: function(status) {
      return this.roleStatusList.find(item => item.value === status)
    },
    async handleSearch(reset = false) {
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.page = 1
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          pageIndex: this.page.page,
          pageSize: this.page.size
        })
      )
      // console.log(data)
      this.tableData = data ? data.content : []
      this.page.page = data.pageable.pageNumber
      this.page.size = data.pageable.pageSize
      this.page.total = data.totalElements
      this.afterHandleSearch(this.tableData)
      this.loading = false
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.page-content {
  font-size: 18px;
  .page-card {
    background: #fff;
    padding: 20px;
  }
  .page-operate {
    display: flex;
    justify-content: space-between;
    .operate-icon {
      margin-left: 8px;
    }
  }
  .page-title {
    font-size: 18px;
    padding: 20px;
    background: #fff;
    margin-bottom: 10px;
  }
}

.table-pagination {
  margin-top: 20px;
}
</style>
