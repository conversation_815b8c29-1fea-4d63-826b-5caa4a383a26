<template>
  <div class="content">
    <div class="content-item">
      <screen-border-multi>
        <template v-slot:title>
          <div class="tabs-class">
            <div
              v-for="(item) in tabList"
              :key="item.id"
              :class="{'tab-pane-active': active === item.id}"
              class="tab-pane"
              @click="active = item.id">
              <div class="tab-pane-title-class">
                <div>{{ item.title }}</div>
                <div
                  v-if="active === item.id"
                  class="tab-pane-img">
                  <img
                    class="tab-pane-img2"
                    src="@/assets/images/screen/tab-pane-active-line2.png"
                    alt="">
                  <img
                    class="tab-pane-img1"
                    src="@/assets/images/screen/tab-pane-active-line.png"
                    alt="">
                </div>
              </div>
            </div>
          </div>
        </template>
        <custom-table-research
          v-if="active === '1'"
          :title="'质量攻关项目'"
          :key="'quality'"
          :setting="tableSettings1"
          :url-list="tableObj1.url.list"
          :url-save="tableObj1.url.save"
          :select-date="selectDate"/>
        <custom-table-research
          v-if="active === '2'"
          :key="'accuracy'"
          :title="'设备精度'"
          :setting="tableObj2.setting"
          :url-list="tableObj2.url.list"
          :url-save="tableObj2.url.save"
          :select-date="selectDate"/>
        <custom-table-research
          v-if="active === '3'"
          :key="'progress'"
          :title="'质量改进-专利计划'"
          :setting="tableObj3.setting"
          :url-list="tableObj3.url.list"
          :url-save="tableObj3.url.save"
          :url-delete="tableObj3.url.delete"
          :select-date="selectDate"/>
        <custom-table-research-pagination
          v-if="active === '4'"
          :key="'workshop'"
          :has-page="true"
          :title="'过程管理-车间履职'"
          :setting="tableObj4.setting"
          :url-list="tableObj4.url.list"
          :url-save="tableObj4.url.save"
          :url-delete="tableObj4.url.delete"
          :select-date="selectDate"/>
        <custom-table-research
          v-if="active === '5'"
          :key="'typical'"
          :title="'质量改进-典型问题'"
          :setting="tableObj5.setting"
          :url-list="tableObj5.url.list"
          :url-save="tableObj5.url.save"
          :url-delete="tableObj5.url.delete"
          :select-date="selectDate"/>          
      </screen-border-multi>
    </div>
  </div>
</template>

<script>
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import CustomTableResearch from '@/pages/screen/C2Meeting/component/custom-table-research'
import CustomTableResearchPagination from '@/pages/screen/C2Meeting/component/custom-table-research-pagination'
import {
  qualityFindAllDate,
  qualitySaveAll,
  deviceAccuracyFindAllDate,
  deviceAccuracySaveAll,
  patentProgramFindAllDate,
  patentProgramSaveAll,
  patentProgramDeleteAll,
  workshopPerformanceFindAllDate,
  workshopPerformanceSaveAll,
  workshopPerformanceDeleteAll,
  typicalQualityProblemFindAllDate,
  typicalQualityProblemSaveAll
} from '@/api/screenC2'
import { post } from '@/lib/Util'

export default {
  name: 'QualityManagement',
  components: {
    CustomTableResearch,
    ScreenBorderMulti,
    CustomTableResearchPagination
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      cDate: '',
      active: '1',
      loading: false,
      tabList: [
        {
          id: '1',
          active: true,
          title: '质量攻关项目'
        },
        {
          id: '2',
          active: false,
          title: '设备精度'
        },
        {
          id: '3',
          active: false,
          title: '质量改进-专利计划'
        },
        {
          id: '4',
          active: false,
          title: '过程管理-车间履职'
        },
        {
          id: '5',
          active: false,
          title: '质量改进-典型问题'
        }
      ],
      tableObj1: {
        url: {
          save: qualitySaveAll,
          list: qualityFindAllDate
        }
      },
      tableObj2: {
        url: {
          save: deviceAccuracySaveAll,
          list: deviceAccuracyFindAllDate
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'createTime',
            keySave: 'createTime',
            label: '案例系统创建日期',
            width: '150',
            inputType: 'date'
          },
          {
            keyQuery: 'problem',
            keySave: 'problem',
            label: '设备存在问题',
            width: '200'
          },
          {
            keyQuery: 'solution',
            keySave: 'solution',
            label: '解决方案',
            width: '200'
          },
          {
            keyQuery: 'leader',
            keySave: 'leader',
            label: '责任人',
            width: '150'
          },
          {
            keyQuery: 'causeAnalysis',
            keySave: 'causeAnalysis',
            label: '设备故障原因分析',
            width: '200'
          },
          {
            keyQuery: 'correctionTime',
            keySave: 'correctionTime',
            label: '措施计划制定、预计整改完成时间',
            width: '150',
            inputType: 'date'
          },
          {
            keyQuery: 'performInspection',
            keySave: 'performInspection',
            label: '执行检查',
            width: '130'
          },
          {
            keyQuery: 'attachmentUpload',
            keySave: 'attachmentUpload',
            label: '附件上传',
            width: '200',
            inputType: 'file'
          },
          {
            keyQuery: 'remind',
            keySave: 'remind',
            label: '时间节点、完成情况提醒',
            width: '200',
            inputType: 'textarea'
          },
          {
            keyQuery: 'inspectionAndSubmission',
            keySave: 'inspectionAndSubmission',
            label: '改造后的日常尽职检查与提交',
            width: '300',
            inputType: 'textarea'
          }
        ]
      },
      tableObj3: {
        url: {
          list: patentProgramFindAllDate,
          save: patentProgramSaveAll,
          delete: patentProgramDeleteAll
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'unit',
            keySave: 'unit',
            label: '单位',
            width: '250'
          },
          {
            keyQuery: 'projectName',
            keySave: 'projectName',
            label: '项目名称',
            width: '330'
          },
          {
            keyQuery: 'userName',
            keySave: 'userName',
            label: '撰写负责人',
            width: '300'
          },
          {
            keyQuery: 'planTime',
            keySave: 'planTime',
            label: '计划完成时间',
            width: '300',
            inputType: 'date'
          },
          {
            keyQuery: 'isSubmit',
            keySave: 'isSubmit',
            label: '是否已提交',
            width: '180'
          },
          {
            keyQuery: 'attachmentUpload',
            keySave: 'attachmentUpload',
            label: '附件上传',
            width: '370',
            inputType: 'file'
          },
          {
            keyQuery: 'id',
            keySave: 'id',
            label: 'id信息',
            show: false
          }
        ]
      },
      tableObj4: {
        url: {
          list: workshopPerformanceFindAllDate,
          save: workshopPerformanceSaveAll,
          delete: workshopPerformanceDeleteAll
        },
        setting: [
          {
            keyQuery: 'setTime',
            keySave: 'setTime',
            label: '日期',
            inputType: 'date',
            width: '150'
          },
          {
            keyQuery: 'unit',
            keySave: 'unit',
            label: '检查单位',
            width: '200'
          },
          {
            keyQuery: 'inspectionArea',
            keySave: 'inspectionArea',
            label: '检查区域',
            width: '200'
          },
          {
            keyQuery: 'inspectionProject',
            keySave: 'inspectionProject',
            label: '检查项目',
            width: '250'
          },
          {
            keyQuery: 'inspectionProblem',
            keySave: 'inspectionProblem',
            label: '检查问题',
            width: '280'
          },
          {
            keyQuery: 'inspectionUser',
            keySave: 'inspectionUser',
            label: '检查人',
            width: '250'
          },
          {
            keyQuery: 'personLiable',
            keySave: 'personLiable',
            label: '责任人',
            width: '250'
          },
          {
            keyQuery: 'examine',
            keySave: 'examine',
            label: '考核',
            width: '250'
          },
          {
            keyQuery: 'id',
            keySave: 'id',
            label: 'id信息',
            show: false
          }
        ]
      },
      tableObj5: {
        url: {
          list: typicalQualityProblemFindAllDate,
          save: typicalQualityProblemSaveAll
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          {
            keyQuery: 'createTime',
            keySave: 'createTime',
            label: '案例系统创建日期',
            width: '200',
            inputType: 'date'
          },
          {
            keyQuery: 'problem',
            keySave: 'problem',
            label: '问题描述',
            width: '200'
          },
          {
            keyQuery: 'attachmentUpload',
            keySave: 'attachmentUpload',
            label: '附件上传',
            width: '400',
            inputType: 'file'
          },
          {
            keyQuery: 'defect',
            keySave: 'defect',
            label: '缺陷种类',
            width: '200'
          },
          {
            keyQuery: 'unit',
            keySave: 'unit',
            label: '责任单位',
            width: '200'
          },
          {
            keyQuery: 'person',
            keySave: 'person',
            label: '责任人',
            width: '200'
          },
          {
            keyQuery: 'reason',
            keySave: 'reason',
            label: '原因',
            width: '200'
          },
          {
            keyQuery: 'measure',
            keySave: 'measure',
            label: '措施',
            inputType: 'textarea',
            width: '200'
          },
          {
            keyQuery: 'executeCheck',
            keySave: 'executeCheck',
            label: '执行检查',
            inputType: 'textarea',
            width: '200'
          },
          {
            keyQuery: 'verify',
            keySave: 'verify',
            label: '闭环验证',
            width: '200'
          },
          // {
          //   keyQuery: 'attachmentUpload1',
          //   keySave: 'attachmentUpload1',
          //   label: '验证附件上传',
          //   width: '300',
          //   inputType: 'file'
          // },
          {
            keyQuery: 'completionStatus',
            keySave: 'completionStatus',
            label: '时间节点、完成情况提醒',
            width: '250'
          },
          {
            keyQuery: 'disposalResults',
            keySave: 'disposalResults',
            label: '处置结果',
            width: '200'
          },
          {
            keyQuery: 'routineCheck',
            keySave: 'routineCheck',
            label: '措施持续尽职日常检查与提交',
            width: '300'
          }
        ]
      }
    }
  },
  computed: {
    currentMonthLabel() {
      // const date = new Date()
      // const year = date.getFullYear()
      // const month = date.getMonth() + 1 // JavaScript 月份从0开始，所以需要+1
      const year = this.cDate.split('-')[0]
      const month = this.cDate.split('-')[1]
      return `${year}年${month}月份攻关完成情况`
    },
    tableSettings1() {
      return [
        {
          keyQuery: 'index',
          keySave: 'index',
          label: '序号',
          type: 'index'
        },
        {
          keyQuery: 'projectName',
          keySave: 'projectName',
          label: '项目名称',
          width: '150'
        },
        {
          keyQuery: 'presentSituation',
          keySave: 'presentSituation',
          label: '现状',
          width: '300'
        },
        {
          keyQuery: 'kro',
          keySave: 'kro',
          label: '攻关目标（增加具体攻关措施）',
          width: '300',
          align: 'left',
          inputType: 'textarea'
        },
        {
          keyQuery: 'leader',
          keySave: 'leader',
          label: '攻关负责人',
          width: '230'
        },
        {
          keyQuery: 'businessUnit',
          keySave: 'businessUnit',
          label: '责任单位',
          width: '130'
        },
        {
          keyQuery: 'startTime',
          keySave: 'startTime',
          label: '项目开始时间',
          width: '150',
          inputType: 'month'
        },
        {
          keyQuery: 'endTime',
          keySave: 'endTime',
          label: '项目完成时间',
          width: '150',
          inputType: 'month'
        },
        {
          keyQuery: 'completionStatus',
          keySave: 'completionStatus',
          label: this.currentMonthLabel,
          width: '300',
          inputType: 'textarea'
        },
        {
          keyQuery: 'nextMonthPlan',
          keySave: 'nextMonthPlan',
          label: '下个月计划',
          width: '300',
          inputType: 'textarea'
        }
      ]
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  methods: {}
}
</script>

<style scoped lang="less">
/deep/ .border-wrapper {
  height: 87vh;
}
</style>
