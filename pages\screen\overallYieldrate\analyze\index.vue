<template>
  <div class="content">
    <div
      class="content-item"
      style="height: 38vh;">
      <custom-table6
        ref="table1"
        :title="'炼钢分析表'"
        :flag="flag.toString()"
        :setting="tableObj1.setting"
        :url-list="tableObj1.url.list"
        :url-save="tableObj1.url.save"
        :url-steel="tableObj1.url.steel"
        :select-date="selectDate"
        :table-class="'big-table'"/>
    </div>
    <div class="content-hold"/>
    <div
      class="content-item"
      style="height: 48vh;"
    >
      <screen-border-multi>
        <template v-slot:title>
          <div class="tabs-class">
            <div
              v-for="(item,index) in tabList3"
              :key="item.id"
              :class="{'tab-pane-active': item.active}"
              class="tab-pane"
              @click="clickTabPane3(item, index)">
              <div class="tab-pane-title-class">
                <div>{{ item.title }}</div>
                <div
                  v-if="item.active"
                  class="tab-pane-img">
                  <img
                    class="tab-pane-img2"
                    src="@/assets/images/screen/tab-pane-active-line2.png"
                    alt="">
                  <img
                    class="tab-pane-img1"
                    src="@/assets/images/screen/tab-pane-active-line.png"
                    alt="">
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-slot:default>
          <custom-table7
            v-if="active3 === 0"
            ref="table1"
            :max-height="maxHeight"
            :flag="flag.toString()"
            :plt="''"
            :setting="tableObj2.setting"
            :url-list="tableObj2.url.list"
            :url-save="tableObj2.url.save"
            :url-steel="tableObj2.url.steel"
            :select-date="selectDate"
            :table-class="'big-table'"/>
          <custom-table9
            v-if="active3 === 1"
            ref="table1"
            :key="1"
            :max-height="maxHeight"
            :flag="flag.toString()"
            :plt="'C1'"
            :setting="tableObj3.setting"
            :url-list="tableObj3.url.list"
            :url-save="tableObj3.url.save"
            :url-steel="tableObj3.url.steel"
            :select-date="selectDate"
            :table-class="'big-table'"/>
          <custom-table9
            v-if="active3 === 2"
            ref="table1"
            :key="2"
            :max-height="maxHeight"
            :flag="flag.toString()"
            :plt="'C2'"
            :setting="tableObj3.setting"
            :url-list="tableObj3.url.list"
            :url-save="tableObj3.url.save"
            :url-steel="tableObj3.url.steel"
            :select-date="selectDate"
            :table-class="'big-table'"/>
          <custom-table9
            v-if="active3 === 3"
            ref="table1"
            :key="3"
            :max-height="maxHeight"
            :flag="flag.toString()"
            :plt="'C3'"
            :setting="tableObj3.setting"
            :url-list="tableObj3.url.list"
            :url-save="tableObj3.url.save"
            :url-steel="tableObj3.url.steel"
            :select-date="selectDate"
            :table-class="'big-table'"/>
        </template>
      </screen-border-multi>
    </div>
  </div>
</template>

 <script>
import ScreenBorder from '@/pages/screen/productionKpiScreen/component/screen-border'
import SingleBarsChart from '@/pages/screen/productionKpiScreen/component/single-bars-chart'
import SingleBarsChart1 from '@/pages/screen/productionKpiScreen/component/single-bars-chart1'
import SingleBarsChart2 from '@/pages/screen/productionKpiScreen/component/single-bars-chart2'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import BarsChart from '@/pages/screen/overallYieldrate/component/bars-chart'
import BarsChart2 from '@/pages/screen/productionKpiScreen/component/bars-chart2'
import StockLineChart from '@/pages/screen/morningMeeting/component/stock-line-chart'
import CustomTable6 from '@/pages/screen/overallYieldrate/component/custom-table6'
import CustomTable7 from '@/pages/screen/overallYieldrate/component/custom-table7'
import CustomTable9 from '@/pages/screen/overallYieldrate/component/custom-table9'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import { post } from '@/lib/Util'
import {
  FindSteelGrade,
  AnalysisSlab,
  AnalysisSlabQlt,
  AnalysisSlabQltPlt,
  FindQltFullProcessy,
  FindQltFullProcessz
} from '@/api/screen'
import * as _ from 'lodash'
import {
  findBlankStock,
  findBlankStockPltZj,
  steelOutputTask,
  findSteelOutputByDate1,
  FindYieldRateSteel,
  FindYieldRateSteel1,
  FindYieldRateSteel2
} from '@/api/screen'
import { math } from '@/lib/Math'
import moment from 'moment'
import lodash from 'lodash'
export default {
  name: 'Output',
  components: {
    CustomTable6,
    CustomTable7,
    CustomTable9,
    BarsChart,
    BarsChart2,
    StockLineChart,
    SingleBarsChart,
    ScreenBorder,
    SingleBarsChart1,
    SingleBarsChart2,
    ScreenBorderMulti
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: true,
      currentTime: false,
      steelOutputTask: steelOutputTask,
      cDate2: [
        moment(new Date().getTime() - 7 * 24 * 1000 * 60 * 60).format(
          'YYYY-MM-DD'
        ),
        moment(new Date().getTime()).format('YYYY-MM-DD')
      ],
      steelType: '',
      steelTypeAll: '',
      maxHeight: null,
      flag: '',
      active3: 0,
      tabList3: [
        {
          id: '1',
          active: true,
          title: '轧钢分析表-综合'
        },
        {
          id: '2',
          active: false,
          title: '轧钢分析表-板卷厂'
        },
        {
          id: '3',
          active: false,
          title: '轧钢分析表-宽板厂'
        },
        {
          id: '4',
          active: false,
          title: '轧钢分析表-中板厂'
        }
      ],
      stock1: {
        bar1: [],
        barX: [],
        gridData: [],
        dialogVisible: false,
        detailVisible: false,
        blankMonthPlan: '',
        blankLastMonth: ''
      },
      tableObj1: {
        url: {
          save: '',
          list: AnalysisSlab,
          steel: FindSteelGrade
        },
        setting: [
          // {
          //   keyQuery: 'index',
          //   keySave: 'index',
          //   label: '序号',
          //   type: 'index',
          //   fixed: true
          // },
          {
            keyQuery: 'plgz',
            keySave: 'plgz',
            label: '坯料钢种',
            width: 150
          },
          {
            keyQuery: 'plzscl',
            keySave: 'plzscl',
            label: '坯料生产量'
          },
          {
            keyQuery: 'plygzsdl',
            keySave: 'plygzsdl',
            label: '坯料原钢种收得量'
          },
          {
            keyQuery: 'gygzsdl',
            keySave: 'gygzsdl',
            label: '坯料原钢种收得率'
          },
          {
            label: '坯料判废影响',
            children: [
              {
                keyQuery: 'fpzl',
                keySave: 'fpzl',
                label: '判废量',
                showChart: true
              },
              {
                keyQuery: 'fpl',
                keySave: 'fpl',
                label: '判废率',
                showChart: true
              }
            ]
          },
          {
            label: '坯料替代影响',
            children: [
              {
                keyQuery: 'tdzl',
                keySave: 'tdzl',
                label: '替代量',
                showChart: true
              },
              {
                keyQuery: 'tdl',
                keySave: 'tdl',
                label: '替代率',
                showChart: true
              }
            ]
          },
          {
            label: '库存坯',
            children: [
              {
                keyQuery: 'kcpzl',
                keySave: 'kcpzl',
                label: '重量',
                showChart: true
              },
              {
                keyQuery: 'kcpbl',
                keySave: 'kcpbl',
                label: '比例',
                showChart: true
              }
            ]
          },
          {
            label: '其中:头尾坯',
            children: [
              {
                keyQuery: 'twpzl',
                keySave: 'twpzl',
                label: '重量'
              },
              {
                keyQuery: 'twpbl',
                keySave: 'twpbl',
                label: '比例'
              }
            ]
          }
        ]
      },
      tableObj2: {
        url: {
          save: '',
          list: AnalysisSlabQlt,
          steel: FindSteelGrade
        },
        setting: [
          {
            keyQuery: 'plgz',
            keySave: 'plgz',
            label: '坯料钢种',
            width: 150
          },
          {
            keyQuery: 'ygzplrlzl',
            keySave: 'ygzplrlzl',
            label: '原钢种坯料入炉重量',
            width: 150
          },
          {
            keyQuery: 'ygzplsjgbzl',
            keySave: 'ygzplsjgbzl',
            label: '原钢种坯料实绩钢板重量',
            width: 150
          },
          {
            keyQuery: 'ygzsjgbzlbhpfl',
            keySave: 'ygzsjgbzlbhpfl',
            label: '原钢种实绩钢板重量(不含判废量)',
            width: 150
          },
          {
            keyQuery: 'ygzplzl',
            keySave: 'ygzplzl',
            label: '原钢种坯料重量',
            width: 150
          },
          {
            keyQuery: 'cygzsdlv',
            keySave: 'cygzsdlv',
            label: '材原钢种收得率',
            width: 150
          },
          {
            label: '材轧制影响',
            children: [
              {
                keyQuery: 'sjccl',
                keySave: 'sjccl',
                label: '实际成材率',
                width: 150
              },
              {
                keyQuery: 'ygzsjccl',
                keySave: 'ygzsjccl',
                label: '原钢种实际成材率',
                width: 150
              },
              {
                keyQuery: 'zzqsl',
                keySave: 'zzqsl',
                label: '轧制切损量',
                width: 150
              },
              {
                keyQuery: 'zzqslzb',
                keySave: 'zzqslzb',
                label: '轧制切损量占比',
                width: 150
              }
            ]
          },
          {
            label: '判废影响',
            children: [
              {
                keyQuery: 'ygzpfl',
                keySave: 'ygzpfl',
                label: '判废量',
                width: 150,
                showChart: true
              },
              {
                keyQuery: 'ygzpflv',
                keySave: 'ygzpflv',
                label: '判废率',
                width: 150,
                showChart: true
              }
            ]
          },
          {
            label: '改判、协议影响',
            children: [
              {
                keyQuery: 'ygzgpxyl',
                keySave: 'ygzgpxyl',
                label: '改判协议量',
                width: 150,
                showChart: true
              },
              {
                keyQuery: 'ygzgpxylv',
                keySave: 'ygzgpxylv',
                label: '改判率',
                width: 150,
                showChart: true
              }
            ]
          },
          {
            label: '改轧影响',
            children: [
              {
                keyQuery: 'gzl',
                keySave: 'gzl',
                label: '非原钢种正品量-含待判',
                width: 150,
                showChart: true
              },
              {
                keyQuery: 'gzlv',
                keySave: 'gzlv',
                label: '改轧率',
                width: 150,
                showChart: true
              }
            ]
          },
          {
            label: '其中：正品现货影响',
            children: [
              {
                keyQuery: 'ygzzpxhl',
                keySave: 'ygzzpxhl',
                label: '判现量',
                width: 150,
                showChart: true
              },
              {
                keyQuery: 'cygzzpxhl',
                keySave: 'cygzzpxhl',
                label: '正品现货率',
                width: 150,
                showChart: true
              }
            ]
          },
          {
            label: '其中：待判材影响',
            children: [
              {
                keyQuery: 'dpl',
                keySave: 'dpl',
                label: '待判量',
                width: 150,
                showChart: true
              },
              {
                keyQuery: 'dplbl',
                keySave: 'dplbl',
                label: '待判率',
                width: 150,
                showChart: true
              }
            ]
          }
        ]
      },
      tableObj3: {
        url: {
          save: '',
          list: AnalysisSlabQltPlt,
          steel: FindSteelGrade
        },
        setting: [
          {
            keyQuery: 'plgz',
            keySave: 'plgz',
            label: '坯料钢种',
            width: 150
          },
          {
            keyQuery: 'ygzplrlzl',
            keySave: 'ygzplrlzl',
            label: '原钢种坯料入炉重量',
            width: 150
          },
          {
            keyQuery: 'ygzplsjgbzl',
            keySave: 'ygzplsjgbzl',
            label: '原钢种坯料实绩钢板重量',
            width: 150
          },
          {
            keyQuery: 'ygzsjgbzlbhpfl',
            keySave: 'ygzsjgbzlbhpfl',
            label: '原钢种实绩钢板重量(不含判废量)',
            width: 150
          },
          {
            keyQuery: 'ygzplzl',
            keySave: 'ygzplzl',
            label: '原钢种坯料重量',
            width: 150
          },
          {
            keyQuery: 'cygzsdlv',
            keySave: 'cygzsdlv',
            label: '材原钢种收得率',
            width: 150
          },
          {
            label: '材轧制影响',
            children: [
              {
                keyQuery: 'sjccl',
                keySave: 'sjccl',
                label: '实际成材率',
                width: 150
              },
              {
                keyQuery: 'ygzsjccl',
                keySave: 'ygzsjccl',
                label: '原钢种实际成材率',
                width: 150
              },
              {
                keyQuery: 'zzqsl',
                keySave: 'zzqsl',
                label: '轧制切损量',
                width: 150
              },
              {
                keyQuery: 'zzqslzb',
                keySave: 'zzqslzb',
                label: '轧制切损量占比',
                width: 150
              }
            ]
          },
          {
            label: '判废影响',
            children: [
              {
                keyQuery: 'ygzpfl',
                keySave: 'ygzpfl',
                label: '判废量',
                width: 150,
                showChart: true
              },
              {
                keyQuery: 'ygzpflv',
                keySave: 'ygzpflv',
                label: '判废率',
                width: 150,
                showChart: true
              }
            ]
          },
          {
            label: '改判、协议影响',
            children: [
              {
                keyQuery: 'ygzgpxyl',
                keySave: 'ygzgpxyl',
                label: '改判协议量',
                width: 150,
                showChart: true
              },
              {
                keyQuery: 'ygzgpxylv',
                keySave: 'ygzgpxylv',
                label: '改判率',
                width: 150,
                showChart: true
              }
            ]
          },
          {
            label: '改轧影响',
            children: [
              {
                keyQuery: 'gzl',
                keySave: 'gzl',
                label: '非原钢种正品量-含待判',
                width: 150,
                showChart: true
              },
              {
                keyQuery: 'gzlv',
                keySave: 'gzlv',
                label: '改轧率',
                width: 150,
                showChart: true
              }
            ]
          },
          {
            label: '其中：正品现货影响',
            children: [
              {
                keyQuery: 'ygzzpxhl',
                keySave: 'ygzzpxhl',
                label: '判现量',
                width: 150,
                showChart: true
              },
              {
                keyQuery: 'cygzzpxhl',
                keySave: 'cygzzpxhl',
                label: '正品现货率',
                width: 150,
                showChart: true
              }
            ]
          },
          {
            label: '其中：待判材影响',
            children: [
              {
                keyQuery: 'dpl',
                keySave: 'dpl',
                label: '待判量',
                width: 150,
                showChart: true
              },
              {
                keyQuery: 'dplbl',
                keySave: 'dplbl',
                label: '待判率',
                width: 150,
                showChart: true
              }
            ]
          }
        ]
        // setting: [
        //   {
        //     keyQuery: 'plgz',
        //     keySave: 'plgz',
        //     label: '坯料钢种',
        //     width: 150
        //   },
        //   {
        //     keyQuery: 'ygzplrlzl',
        //     keySave: 'ygzplrlzl',
        //     label: '原钢种坯料投入量',
        //     width: 150
        //   },
        //   {
        //     keyQuery: 'ygzsjgbzlbhpfl',
        //     keySave: 'ygzsjgbzlbhpfl',
        //     label: '原钢种实际钢板重',
        //     width: 150
        //   },
        //   {
        //     keyQuery: 'ygzplzl',
        //     keySave: 'ygzplzl',
        //     label: '原钢种坯料重量',
        //     width: 150
        //   },
        //   {
        //     keyQuery: 'cygzsdlv',
        //     keySave: 'cygzsdlv',
        //     label: '材原钢种收得率',
        //     width: 150
        //   },
        //   // {
        //   //   keyQuery: 'plzscl',
        //   //   keySave: 'plzscl',
        //   //   label: '坯料生产量'
        //   // },
        //   // {
        //   //   keyQuery: 'plygzsdl',
        //   //   keySave: 'plygzsdl',
        //   //   label: '坯料原钢种收得量'
        //   // },
        //   // {
        //   //   keyQuery: 'gygzsdl',
        //   //   keySave: 'gygzsdl',
        //   //   label: '坯料原钢种收得率'
        //   // },
        //   {
        //     label: '材轧制影响',
        //     children: [
        //       {
        //         keyQuery: 'ygzsjccl',
        //         keySave: 'ygzsjccl',
        //         label: '成材率'
        //       },
        //       {
        //         keyQuery: 'zzqsl',
        //         keySave: 'zzqsl',
        //         label: '轧制切损量'
        //       },
        //       {
        //         keyQuery: 'zzqsl',
        //         keySave: 'zzqsl',
        //         label: '占比'
        //       }
        //     ]
        //   },
        //   {
        //     label: '材判废影响',
        //     children: [
        //       {
        //         keyQuery: 'ygzpfl',
        //         keySave: 'ygzpfl',
        //         label: '判废量',
        //         showChart: true
        //       },
        //       {
        //         keyQuery: 'ygzpflv',
        //         keySave: 'ygzpflv',
        //         label: '判废率',
        //         showChart: true
        //       }
        //     ]
        //   },
        //   {
        //     label: '材改判影响',
        //     children: [
        //       {
        //         keyQuery: 'ygzgpxyl',
        //         keySave: 'ygzgpxyl',
        //         label: '改判量',
        //         showChart: true
        //       },
        //       {
        //         keyQuery: 'ygzgpxylv',
        //         keySave: 'ygzgpxylv',
        //         label: '改判率',
        //         showChart: true
        //       }
        //     ]
        //   },
        //   {
        //     label: '材判现货影响',
        //     children: [
        //       {
        //         keyQuery: 'ygzzpxhl',
        //         keySave: 'ygzzpxhl',
        //         label: '判现量',
        //         showChart: true
        //       },
        //       {
        //         keyQuery: 'cygzzpxhl',
        //         keySave: 'cygzzpxhl',
        //         label: '改判率',
        //         showChart: true
        //       }
        //     ]
        //   },
        //   {
        //     label: '材改轧影响',
        //     children: [
        //       {
        //         keyQuery: 'gzl',
        //         keySave: 'gzl',
        //         label: '改轧量',
        //         showChart: true
        //       },
        //       {
        //         keyQuery: 'gzlv',
        //         keySave: 'gzlv',
        //         label: '改轧率',
        //         showChart: true
        //       }
        //     ]
        //   },
        //   {
        //     label: '待判材',
        //     children: [
        //       {
        //         keyQuery: 'dpl',
        //         keySave: 'dpl',
        //         label: '重量',
        //         showChart: true
        //       },
        //       {
        //         keyQuery: 'dplbl',
        //         keySave: 'dplbl',
        //         label: '比例',
        //         showChart: true
        //       }
        //     ]
        //   }
        // ]
      }
    }
  },
  computed: {
    searchTime: function() {
      return moment(this.cDate).subtract(2, 'day')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.$nextTick(() => {
        this.loadData()
      })
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.loadData()
    this.getSteelData()
    this.checkAnyTimeRange()
    // this.FindYieldRateSteel()
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    calculateHeight() {
      this.maxHeight = this.$refs.table1.offsetHeight
      // this.resourceConfirm.maxHeight = this.$refs.table2.offsetHeight
    },
    async loadData() {
      this.$nextTick(() => {
        this.FindYieldRateSteel()
        // this.getZZP()
      })
    },
    isCurrentTimeInAnyRange(timeRanges) {
      // 获取当前时间
      const currentTime = moment()

      // 遍历所有时间范围
      for (let range of timeRanges) {
        const start = moment(range.start)
        const end = moment(range.end)

        // 判断当前时间是否在任何一个范围内（包括边界）
        if (currentTime.isBetween(start, end, null, '[]')) {
          return true // 如果在范围内，立即返回 true
        }
      }

      // 如果遍历完所有范围都不在内，则返回 false
      return false
    },
    checkAnyTimeRange() {
      // 定义多个时间范围
      const timeRanges = [
        {
          start: moment()
            .hour(0)
            .minute(50)
            .second(0)
            .millisecond(0),
          end: moment()
            .hour(2)
            .minute(10)
            .second(0)
            .millisecond(0)
            .subtract(1, 'second')
        },
        {
          start: moment()
            .hour(8)
            .minute(30)
            .second(0)
            .millisecond(0),
          end: moment()
            .hour(10)
            .minute(10)
            .second(0)
            .millisecond(0)
            .subtract(1, 'second')
        },
        {
          start: moment()
            .hour(16)
            .minute(50)
            .second(0)
            .millisecond(0),
          end: moment()
            .hour(18)
            .minute(10)
            .second(0)
            .millisecond(0)
            .subtract(1, 'second')
        }
        // {
        //   start: moment()
        //     .hour(12)
        //     .minute(0)
        //     .second(0)
        //     .millisecond(0),
        //   end: moment()
        //     .hour(13)
        //     .minute(10)
        //     .second(0)
        //     .millisecond(0)
        //     .subtract(1, 'second')
        // }
        // ... 可以添加更多时间范围
      ]

      const isInAnyRange = this.isCurrentTimeInAnyRange(timeRanges)

      if (isInAnyRange) {
        console.log('当前时间在某个指定范围内')
        this.currentTime = false
      } else {
        console.log('当前时间不在任何指定范围内')
        this.currentTime = true
      }
    },
    //点检tab切换
    clickTabPane3(item, index) {
      this.tabList3.forEach(item => {
        item.active = false
      })
      item.active = true
      this.active3 = index
      console.log('aaaa', item)
      console.log('aaaa', index)
    },
    confirmSearch() {
      // 用户点击确认按钮时执行的逻辑
      this.getData()
    },
    cancelDelete() {
      console.log('删除操作已取消')
    },
    parsePercentageToNumber(percentageStr) {
      // 移除百分号
      let numberStr = percentageStr.replace('%', '')

      // 转换为数值
      let number = parseFloat(numberStr)

      // （可选）转换为小数（即比例值）
      // 如果你想要得到的是 0.5 而不是 50，则执行这一步
      let decimalNumber = number

      // 返回你需要的值，可以是原始数值或小数
      // return number; // 如果你只需要原始数值（50）
      return decimalNumber // 如果你需要小数（0.5）
    },
    getSteelData() {
      post(FindSteelGrade, {
        startDate: this.cDate2[0],
        endDate: this.cDate2[1]
      }).then(res => {
        let obj = []
        for (const item of res.data) {
          let data = item
          for (let key in item) {
            obj.push(key)
          }
        }
        this.steelTypeAll = obj
      })
    },
    async FindYieldRateSteel() {
      this.stock1.barX = []
      this.stock1.bar1 = [
        {
          name: '钢原钢种收得率',
          data: []
        },

        {
          name: '钢板综合收得率',
          data: []
        },
        {
          name: '钢板原钢种收得率',
          data: []
        }
      ]
      this.loading = true
      const parameters = await post(FindYieldRateSteel, {
        // setDate: this.searchTime.format('yyyy-MM')
        steelType: this.steelType
      })
      this.stock1.barX = parameters.data.map(item => item.nowDate)
      if (parameters.data) {
        this.loading = false
      }
      this.stock1.bar1 = [
        {
          name: '钢原钢种收得率',
          data: parameters.data.map(item =>
            this.parsePercentageToNumber(item.rate)
          )
        },

        {
          name: '钢板综合收得率',
          data: parameters.data.map(
            item => this.parsePercentageToNumber(item.zrate)
            // parseInt().replace('%', '')
          )
        },
        {
          name: '钢板原钢种收得率',
          data: parameters.data.map(
            item => this.parsePercentageToNumber(item.yrate)
            // parseInt(item.zrate).replace('%', '')
          )
        }
      ]
    },
    // 获取点击信息
    getDetectionDetailed(data) {
      this.flag = data.fromActionPayload.dataIndexInside + 1
    }
  }
}
</script>

 <style scoped lang="less">
// /deep/.screen-wrapper {
//   position: relative;
//   display: flex;
//   height: 200vh;
//   width: 100vw;
// }
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  // display: flex;
  flex-direction: column;
  .content-hold {
    height: 20px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .slick {
    height: 65px;
    position: relative;
    //top: -15px;
    margin-bottom: 15px;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    width: 90px;
    line-height: 24px;
    font-size: 16px;
    white-space: nowrap;
    color: #ffffff;
  }
  span:last-child {
    flex: 1;
    overflow: auto;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: left;
    font-size: 0;
    margin-left: 10px;
    .card {
      display: inline-block;
      margin-left: 6px;
      text-align: left;
      min-width: 75px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      border-bottom: none;
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.green {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
        border-bottom: 1px solid rgba(31, 198, 255, 0.5);
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
.scroll-wrapper {
  height: 100%;
}
/deep/.el-input--small .el-input__inner {
  height: 32px;
  line-height: 32px;
  background-color: #093d4d;
  color: #fff;
}
/deep/.el-table.el-table--border {
  border-width: 0px;
  border-color: #081f27;
}
.tabs-class {
  display: flex;
  flex-direction: row;
  .tab-pane {
    color: #ffffffbf;
    margin-right: 21px;
  }
  .tab-pane-active {
    color: #ffffff;
  }
  .tab-pane-title-class {
    display: flex;
    flex-direction: column;
    position: relative;
    .tab-pane-img {
      .tab-pane-img2 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
      .tab-pane-img1 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        margin-bottom: 7px;
      }
    }
  }
}
.kpi-list {
  font-size: 0;
  .item {
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12.5%;
    img {
      width: 48px;
      margin-right: 10px;
    }
    .name {
      font-size: 18px;
      font-weight: 700;
      line-height: 18px;
      margin-bottom: 10px;
      letter-spacing: 0px;
      text-align: left;
    }
    .num {
      font-size: 32px;
      font-weight: 700;
      line-height: 32px;
      letter-spacing: 0px;
      text-align: left;
    }
    .unit {
      font-size: 20px;
      font-weight: 350;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
    }
  }
}
</style>
