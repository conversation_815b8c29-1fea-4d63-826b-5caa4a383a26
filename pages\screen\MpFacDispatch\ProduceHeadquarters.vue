<template>
  <div class="bigBox">
    <el-row :gutter="32">
      <el-col :span="12">
        <screen-border title="生产报告">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(1)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <el-table
            :data="produceReport"
            height="380">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="classes"
              label="班次"
              align="center"/>
            <el-table-column
              prop="classes1"
              label="班别"
              align="center"/>
            <el-table-column
              prop="targetWgt"
              label="目标产量"
              width="120"
              align="center"/>
            <el-table-column
              prop="actualWgt"
              label="实际产量"
              width="120"
              align="center"/>
            <el-table-column
              prop="actualNumberOfBlocks"
              label="实际轧制块数"
              align="center"/>
            <el-table-column
              prop="length"
              label="坯料长度≥2.75"
              width="120"
              align="center"/>
            <el-table-column
              prop="sumNumberOfBlocks"
              label="综合轧制块数"
              align="center"/>
            <el-table-column
              prop="reason"
              label="产品未完成原因"
              show-tooltip-when-overflow
              width="200"
              align="center"/>
            <el-table-column
              prop="unit"
              label="责任单位"
              width="120"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="生产日报表">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(2)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <el-table
            :data="produceDayTable"
            height="380">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="process"
              label="工序"
              width="140"
              align="center"/>
            <el-table-column
              prop="target"
              label="目标"
              width="100"
              align="center"/>
            <el-table-column
              prop="day"
              label="当日"
              width="140"
              align="center"/>
            <el-table-column
              prop="accrued"
              label="累计"
              width="140"
              align="center"/>
            <el-table-column
              prop="underpayment"
              label="欠量"
              width="140"
              align="center"/>
            <el-table-column
              prop="dayProgress"
              label="日历进度"
              width="140"
              align="center"/>
            <el-table-column
              prop="actualProgress"
              label="实际进度(%)"
              width="130"
              align="center"/>
            <el-table-column
              prop="completion"
              label="完成情况"
              show-tooltip-when-overflow
              width="140"
              align="center"/>
            <el-table-column
              prop="daily"
              label="日均"
              width="100"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="合同跟踪报告">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(3)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <el-input
            :rows="2"
            v-model="contractText"
            disabled
            style="white-space: pre-wrap;margin-bottom: 10px;"
            type="textarea"
            placeholder="备注!"/>
          <el-table
            :data="CT_reports"
            height="calc(100vh - 730px)">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="title"
              label="标头"
              width="210"
              align="center"/>
            <el-table-column
              prop="kuBe"
              label="ZB待判板块/吨"
              align="center"/>
            <el-table-column
              prop="pendJudgment"
              label="WG待判板块/吨"
              align="center"/>
            <el-table-column
              prop="landPlate"
              label="落地板块数"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="剪切生产报告">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(4)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <div class="disData">
            <div class="block">
              <div class="title">外库入库块数(达钢)</div>
              <div class="context"><i/><span>{{ params.store }}块</span></div>
            </div>
            <div class="block">
              <div class="title">外库(达钢)库存量</div>
              <div class="context"><i/><span>{{ params.stock }}块</span></div>
            </div>
          </div>
          <el-table
            :data="cutProduction"
            height="calc(100vh - 730px)">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="classes"
              label="班次"
              width="60"
              align="center"/>
            <el-table-column
              prop="classes1"
              label="班别"
              width="80"
              align="center"/>
            <el-table-column
              prop="targetCut"
              label="目标剪切"
              align="center"
              width="120"/>
            <el-table-column
              prop="actualCut"
              label="实际剪切"
              align="center"
              width="120"/>
            <el-table-column
              prop="actualNumberOfStore"
              label="实际入库块数"
              align="center"
              width="160"/>
            <el-table-column
              prop="insideThePlan"
              label="毛边计划内"
              align="center"
              width="150"/>
            <el-table-column
              prop="multipleOrders"
              label="一坯多订单宽大"
              align="center"
              width="160"/>
            <el-table-column
              prop="landNumber"
              label="落地块数"
              align="center"
              width="120"/>
            <el-table-column
              prop="loopNumber"
              label="回线块数"
              align="center"
              width="120"/>
            <el-table-column
              prop="reason"
              label="剪切未完成原因"
              align="center"
              width="160"/>
          </el-table>
        </screen-border>
      </el-col>
    </el-row>
    
    <!--弹框-->
    <el-dialog
      :visible.sync="dialogBox"
      :width="'90%'"
      :close-on-click-modal="false"
      :top="title=='生产计划'?'3vh':'10vh'"
      class="screen-dialog"
      @close="closeDialogBox">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span> -->
            <span
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download"/>
              下载
            </span>
            <span
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-printer"/>
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <el-table
        id="table"
        :data="formData"
        border>
        <el-table-column
          type="index"
          label="序号"
          width="60"/>
        <el-table-column
          v-for="(item,index) in Header"
          :key="index"
          :prop="item.prop"
          :width="item.label=='产品未完成原因'?'300':''"
          :label="item.label"
          align="center">
          <template v-slot="{ row }">
            <el-input 
              :disabled="item.disabled?item.disabled:false" 
              v-model="row[item.prop]"/>
            <span v-show="false">{{ row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          width="100"
          label="操作">
          <template v-slot="scope">
            <div class="btn">
              <el-button 
                :disabled="title=='生产报告'||title=='合同跟踪报告'||title=='生产日报表'||title=='剪切生产报告'"
                type="danger"
                icon="el-icon-delete"
                @click="delRow(scope.$index)"/>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- <div class="text-center">
        <span
          class="screen-btn"
          @click="addNewRow()">
          <el-icon class="el-icon-circle-plus-outline" />
          增加数据
        </span>
      </div> -->

      <el-input
        v-if="title=='合同跟踪报告'"
        :rows="2"
        :disabled="true"
        v-model="textareaCopy"
        style="white-space: pre-wrap;margin-top: 20px;"
        type="textarea"
        placeholder="备注!"/>
     
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { saveAs } from 'file-saver'
import * as echarts from 'echarts'
import moment from 'moment'

import {
  PRODUCEREPORT,
  PRODUCEREPORT_SAVE,
  PRODUCEDAYTABLE,
  PRODUCEDAYTABLE_SAVE,
  CT_REPORTS,
  CT_REPORTS_SAVE,
  CUT_PRODUCTION_DATA,
  CUT_PRODUCTION_SAVE
} from '@/api/screen'

export default {
  name: 'ProduceHeadquarters',
  components: {
    // SingleBarsChart,
    // SteelBarsChart,
    ScreenBorder,
    ScreenBorderMulti
  },
  props: {
    selTime: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      //生产报告
      produceReport: [],

      //生产日报表
      produceDayTable: [],

      //合同跟踪报告
      CT_reports: [],
      //备注
      contractText: '',

      //剪切生产报告
      cutProduction: [],
      params: [],

      //弹框统一
      dialogBox: false,
      title: '',
      //弹框统一表字段
      Header: [],
      //弹框统一表数据
      formData: [],
      //上传Excel
      fileList: [],
      //备注
      textareaCopy: ''
    }
  },
  watch: {
    selTime: function() {
      this.getProduceReport()
      this.getProduceDayTable()
      this.getCT_reports()
      this.getCutProduction()
    }
  },

  created() {
    this.getProduceReport()
    this.getProduceDayTable()
    this.getCT_reports()
    this.getCutProduction()
  },

  methods: {
    //获取生产报告数据
    async getProduceReport() {
      let res = await post(PRODUCEREPORT, {
        setTime: this.selTime
      })
      // console.log('生产报告', res)

      if (res.data) {
        this.produceReport = res.data
      }
    },

    //获取生产日报表数据
    async getProduceDayTable() {
      let res = await post(PRODUCEDAYTABLE, {
        setTime: this.selTime
      })
      // console.log('生产日报表', res)

      if (res.data.length != 0) {
        this.produceDayTable = res.data
      }
    },

    //获取合同跟踪报告数据
    async getCT_reports() {
      let res = await post(CT_REPORTS, {
        setTime: this.selTime
      })
      // console.log('合同跟踪报告', res)

      if (res.data.length != 0) {
        this.CT_reports = res.data
        this.contractText = res.remark
      }
    },

    //获取剪切生产报告
    async getCutProduction() {
      let res = await post(CUT_PRODUCTION_DATA, {
        setTime: this.selTime
      })
      // console.log('剪切生产报告', res)

      if (res.data) {
        this.cutProduction = res.data
        this.params = res.data1
      }
    },

    //弹框
    openView(nub) {
      this.dialogBox = true
      if (nub == 1) {
        this.title = '生产报告'
        this.Header = [
          {
            label: '班次',
            prop: 'classes',
            disabled: true
          },
          {
            label: '班别',
            prop: 'classes1',
            disabled: true
          },
          {
            label: '目标产量',
            prop: 'targetWgt'
          },
          {
            label: '实际产量',
            prop: 'actualWgt',
            disabled: true
          },
          {
            label: '实际轧制块数',
            prop: 'actualNumberOfBlocks',
            disabled: true
          },
          {
            label: '坯料长度≥2.75',
            prop: 'length',
            disabled: true
          },
          {
            label: '综合轧制块数',
            prop: 'sumNumberOfBlocks',
            disabled: true
          },
          {
            label: '产品未完成原因',
            prop: 'reason',
            disabled: true
          },
          {
            label: '责任单位',
            prop: 'unit',
            disabled: true
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.produceReport))
      } else if (nub == 2) {
        this.title = '生产日报表'
        this.Header = [
          {
            label: '工序',
            prop: 'process',
            disabled: true
          },
          {
            label: '目标',
            prop: 'target'
          },
          {
            label: '当日',
            prop: 'day',
            disabled: true
          },
          {
            label: '累计',
            prop: 'accrued',
            disabled: true
          },
          {
            label: '欠量',
            prop: 'underpayment',
            disabled: true
          },
          {
            label: '日历进度',
            prop: 'dayProgress',
            disabled: true
          },
          {
            label: '实际进度',
            prop: 'actualProgress',
            disabled: true
          },
          {
            label: '完成情况',
            prop: 'completion',
            disabled: true
          },
          {
            label: '日均',
            prop: 'daily',
            disabled: true
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.produceDayTable))
      } else if (nub == 3) {
        this.title = '合同跟踪报告'
        this.Header = [
          {
            label: '标头',
            prop: 'title'
          },
          {
            label: 'ZB待判板块/吨',
            prop: 'kuBe'
          },
          {
            label: 'WG待判板块/吨',
            prop: 'pendJudgment'
          },
          {
            label: '落地板块书',
            prop: 'landPlate'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.CT_reports))
        this.textareaCopy = JSON.parse(
          JSON.stringify(this.contractText ? this.contractText : null)
        )
      } else if (nub == 4) {
        this.title = '剪切生产报告'
        this.Header = [
          {
            label: '班次',
            prop: 'classes'
          },
          {
            label: '班别',
            prop: 'classes1'
          },
          {
            label: '目标剪切',
            prop: 'targetCut'
          },
          {
            label: '实际剪切',
            prop: 'actualCut'
          },
          {
            label: '实际入库块数',
            prop: 'actualNumberOfStore'
          },
          {
            label: '毛边计划内',
            prop: 'insideThePlan'
          },
          {
            label: '一坯多订单宽大',
            prop: 'multipleOrders'
          },
          {
            label: '落地块数',
            prop: 'landNumber'
          },
          {
            label: '回线块数',
            prop: 'loopNumber'
          },
          {
            label: '剪切未完成原因',
            prop: 'reason'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.cutProduction))
      }
    },

    //添加行
    addNewRow() {
      let row = {}
      this.Header.forEach(item => {
        row[item.prop] = ''
      })

      this.formData.push(row)
    },

    //删除行
    delRow(indexs) {
      this.formData.forEach((item, index) => {
        if (indexs == index) {
          this.formData.splice(index, 1)
        }
      })
    },

    //下载模板
    DownloadExcel() {
      let data = [{}]
      this.Header.forEach(item => {
        data[0][item.prop] = item.label
      })

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `${this.title}.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          `${this.title}.xlsx`
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      let header = {}
      let letter = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      this.Header.forEach((item, index) => {
        header[item.prop] = letter[index]
      })

      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, header)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        console.log(list)

        list.forEach(item => {
          item.selTime = this.selTime
          this.formData.push(item)
        })
      })
    },

    //保存新增数据
    async saveData() {
      let res
      if (this.title == '生产报告') {
        res = await post(PRODUCEREPORT_SAVE, {
          setTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '生产日报表') {
        res = await post(PRODUCEDAYTABLE_SAVE, {
          setTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '合同跟踪报告') {
        res = await post(CT_REPORTS_SAVE, {
          setTime: this.selTime,
          data: this.formData,
          notes: this.textareaCopy
        })
      } else if (this.title == '剪切生产报告') {
        res = await post(CUT_PRODUCTION_SAVE, {
          setTime: this.selTime,
          data: this.formData
        })
      }

      // console.log('保存', res)
      if (res.status == 1) {
        this.$message.success('保存成功')
        if (this.title == '生产报告') {
          this.getProduceReport()
        } else if (this.title == '生产日报表') {
          this.getProduceDayTable()
        } else if (this.title == '合同跟踪报告') {
          this.getCT_reports()
        } else if (this.title == '剪切生产报告') {
          this.getCutProduction()
        }

        this.closeDialogBox()
      }
    },

    //关闭弹框
    closeDialogBox() {
      this.dialogBox = false
      this.title = ''
      this.Header = []
      this.formData = []
      this.textareaCopy = ''
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .tabBox {
      display: flex;
      .tab {
        color: #ffffffbf;
        margin-right: 20px;
      }
      .tab_block {
        display: flex;
        flex-direction: column;
        position: relative;
        .tab_img {
          .tab_img2 {
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
          }
          .tab_img1 {
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
            margin-bottom: 7px;
          }
        }
      }
    }
    .border-content {
      height: 380px;
    }
  }
  .EchartsBox {
    height: 380px;
    .setRadio {
      /deep/.el-radio {
        color: white;
      }
    }
  }
  .border-wrapper {
    margin-bottom: 15px;
  }
  /deep/.el-textarea__inner {
    background-color: #041a21;
    border: 1px solid #1fc6ff;
    color: white;
    font-size: 14px;
    height: 70px;
  }
}

//剪切生产报告模块演示
.disData {
  display: flex;
  margin-bottom: 15px;
  .block {
    border: 1px solid #1fc6ff;
    width: 220px;
    color: white;
    padding: 2px 10px;
    margin-right: 20px;
    .title {
      font-size: 14px;
    }
    .context {
      i {
        display: inline-block;
        width: 6px;
        height: 16px;
        background: #19be6b;
      }
      span {
        font-size: 20px;
        display: inline-block;
        margin: 5px 5px;
      }
    }
  }
}

.btn {
  /deep/.el-button {
    font-size: 15px;
    padding: 4px 15px;
    border-radius: 4px;
  }
}

/deep/ .el-table .el-table__cell {
  font-size: 19px;
}

/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}
</style>
