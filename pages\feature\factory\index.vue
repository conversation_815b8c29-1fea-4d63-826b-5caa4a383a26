<template>
  <div>
    <kpi-def/>
    <div style="position: relative">
      <el-tabs
        v-model="activeName"
        type="border-card"
        @tab-click="handleClick">
        <el-tab-pane
          v-for="(item, index) in kpiFunction"
          :key="index"
          :label="item.label"
          :name="item.value.toString()"/>
        <loading v-if="loading"/>
        <template
          v-else>
          <div
            v-if="dataList.length"
            class="operate-wrapper">
            <el-select
              v-model="rank"
              placeholder="显示级别">
              <el-option
                v-for="(item, index) in levelList"
                :key="index"
                :disabled="item.name"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-checkbox
              v-model="onlyWarning"
              label="仅预警"
              border
              size="small"/>
          </div>
          <Empty v-else/>
          <kpi-node
            v-for="item in dataList"
            :key="item.id"
            :node="item"
            :only-warning="onlyWarning"
            :rank="rank"/>
        </template>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { findKpiDataTree } from '@/api/kpi'
import { post } from '@/lib/Util'
import KpiNode from '@/components/kpiTree/KpiNode'
import KpiDef from '@/components/KpiDef'
import { ENUM } from '@/lib/Constant'

export default {
  name: 'feature-factory',
  components: { KpiDef, KpiNode },
  data() {
    return {
      activeName: '0',
      dataList: [],
      levelList: ENUM.levelList,
      kpiFunction: ENUM.kpiFunction,
      onlyWarning: false,
      rank: 2,
      factory: null,
      feature: 0,
      loading: true
    }
  },
  computed: {},
  watch: {
    $route: {
      handler(newVal, oldVal) {
        //判断newVal有没有值监听路由变化
        this.reset()
        this.loadData()
      },
      deep: true
    },
    factory: function() {
      this.loadData()
    },
    feature: function() {
      this.loadData()
    }
  },
  created() {
    this.reset()
    this.loadData()
  },
  methods: {
    reset() {
      this.feature = 0
      this.activeName = '0'
      this.rank = 2
      this.factory = this.$route.query.factory
    },
    handleClick(tab, event) {
      this.feature = Number(tab.name)
      this.loadData()
    },
    async loadData() {
      this.onlyWarning = false
      this.rank = 2
      this.loading = true
      const data = await post(findKpiDataTree, {
        factory: this.factory,
        feature: this.feature
      })
      if (data.success) {
        this.dataList = data.data || []
        // this.activeName = this.dataList.length ? this.dataList[0].id : null
      }
      this.loading = false
    }
  }
}
</script>

<style scoped lang="less">
.operate-wrapper {
  text-align: right;
  margin: 10px;
}
.tab-select {
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 9;
}
</style>
