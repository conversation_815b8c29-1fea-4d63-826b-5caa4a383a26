const path = 'kpi/'
// 获取所有一级指标
export const findFirstRank = path + 'kpi/findFirstRank'
export const findNextRank = path + 'kpi/findNextRank'
export const findKpiTree = path + 'kpi/findKpiTree'
export const kpiSave = path + 'kpi/save'
export const findBySpecification = path + 'kpi/findPageByMultiCondition'
export const kpiDelete = path + 'kpi/delete'
export const updateIsShow = path + 'kpi/updateIsShow'

export const findKpiDataTree = path + 'kpiData/findKpiDataTree'
export const syncKpiToKpiData = path + 'kpiData/syncKpiToKpiData'

export const findDefKpiByUserNo = path + 'userDefKpi/findByUserNo'
export const saveDefKpiByUserNo = path + 'userDefKpi/save'

// 指标关系
export const findKpiDiagram = path + 'kpiRelation/findKpiDiagram'
export const saveDiagram = path + 'kpiRelation/save'
export const deleteDiagram = path + 'kpiRelation/delete'
export const findKpiRelTree = path + 'kpiRelation/findKpiRelTreeByKid'
export const findKpiIndexTree = path + 'KpiIndex/findKpiIndexTree'
export const saveKpiIndex = path + 'KpiIndex/save'
export const deleteKpiIndex = path + 'KpiIndex/delete'

// 根据规则id删除一个KPI指标预警规则
export const deleteKpiWarningRules = path + '/kpiWarningRules/delete'
// 根据kpi id查询所有预警规则
export const findRulesByKid = path + '/kpiWarningRules/findRulesByKid'
// 根据目标管理角色id查询所有预警规则
export const findRulesByManagerId =
  path + '/kpiWarningRules/findRulesByManagerId'
// 添加/修改KPI指标预警规则
export const updateKpiWarningRules = path + '/kpiWarningRules/update'
// 根据kpi的规则id查询其目标值
export const findTargetValueByRid =
  path + '/kpiWarningRules/findTargetValueByRid'
// 查询当前period下所有的预警规则的规则名称、目标值、实际值
export const findRulesOfKpi = path + '/kpiWarningData/findRulesOfKpi'
export const findCode = path + 'kpiWarningRules/findCode'
// 根据kpiId查询月和日实际值
export const findResultValueOfMonthAndDay =
  path + '/kpiData/findResultValueOfMonthAndDay'

// 新关系图
export const findKpiCateDiagram = path + '/kpiCateRelation/findKpiCateDiagram'
export const findKpiCateDiagramSave = path + '/kpiCateRelation/save'
export const findKpiCateDiagramDelete = path + '/kpiCateRelation/delete'

export const findForecast = path + '/productionForecast/findForecast'
export const findStoppedTimeByReason =
  path + '/productionForecast/findStoppedTimeByReason'
// 根据停时原因、厂线、取值方式查询停时数据
export const findStoppedTimeData =
  path + '/productionForecast/findStoppedTimeData'
export const findOrders = path + '/productionForecast/findOrders'
export const findDetailedOrders =
  path + '/productionForecast/findDetailedOrders'
export const findLogs = path + '/productionForecast/findLogs'
// 自定义查询机时产量
export const findMachineOutputByConditions =
  path + '/productionForecast/findMachineOutputByConditions'
// 获取另外两个厂的产量之和
export const getBUProductWgt = path + '/productionForecast/getBUProductWgt'
// 删除订单
export const deleteOrders = path + '/productionForecast/deleteOrders'
export const updateOrders = path + '/productionForecast/updateOrders'
export const findMachineOutputData =
  path + '/productionForecast/findMachineOutputData'
export const findMachineOutputDatasById =
  path + '/productionForecast/findMachineOutputDatasById'
export const findOtherSteelTypes =
  path + '/productionForecast/findOtherSteelTypes'
export const updateOrder = path + '/productionForecast/updateOrder'
export const saveOrder = path + '/productionForecast/saveOrder'
export const saveOrders = path + '/productionForecast/saveOrders'
export const saveForecast = path + '/productionForecast/saveForecast'
export const machineOutputAdapt =
  path + '/productionForecast/machineOutputAdapt'
export const updatePlannedConditionServiceStops =
  path + '/productionForecast/updatePlannedConditionServiceStops'

export const findKpiCateRelationByKid =
  path + '/kpiCateRelation/findKpiCateRelationByKid'

// 分页查找KPI类别
export const kpiCategory = path + '/kpiCategory/findPageByMultiCondition'
// export const kpiCategoryPage = path + '/kpiCategory/findByTypeGroupByFeature'
// export const kpiCategorySave = path + '/kpiCategory/save'
export const kpiCategoryDel = path + '/kpiCategory/delete'
export const findByTypeGroupByFeature =
  path + '/kpiCategory/findByTypeGroupByFeature'
export const kpiCategorySave = path + '/kpiCategory/save'

// 根据周期和KID查询对应的 is_target_mark 的规则的相关历史数据
export const findKpiDatas = path + 'kpiWarningData/findKpiDatas'
export const saveByTargetMark = path + 'kpiWarningRules/saveByTargetMark'
export const findKpiTargetTree = path + 'kpiData/findKpiTargetTree'

// kpi 角色
export const findRoleKpi = path + 'KpiRole/findPageByMultiCondition'
export const saveRoleKpi = path + 'KpiRole/save'

// 导入订单
export const orderAdapt = path + 'productionForecast/orderAdapt'

// 滚动预测
export const findWarningDataByConditions =
  path + 'kpiWarningData/findWarningDataByConditions'
export const getRollForecastData =
  path + 'productionForecast/getRollForecastData'

// 分析评价
export const analysisEvaluationFind =
  path + '/analysisEvaluation/findPageByMultiCondition'
export const getEvalOverview = path + '/analysisEvaluation/getEvalOverview'

// 轧制实绩
export const rollingSteelActualFind =
  path + 'rollingSteelActual/findPageByMultiCondition'
export const getTeamProdOverview =
  path + 'analysisEvaluation/getTeamProdOverview'
export const getSubSlabHistoryById =
  path + 'rollingSteelActual/getSubSlabHistoryById'
export const getContributeValSummary =
  path + 'rollingSteelActual/getContributeValSummary'

export const findRollingAvgMachineoutput =
  path + '/productionForecast/findRollingAvgMachineOutput'
// 用户用户选择机时产量
export const userMachineOutputSave = path + '/userMachineOutput/save'
