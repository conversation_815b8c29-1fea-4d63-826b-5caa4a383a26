<template>
  <div>
    <kpi-def/>
    <div class="kpi-chart">
      <el-row :gutter="20">
        <el-col
          v-for="(item, index) in dataList"
          :key="index" 
          :span="12">
          <div 
            v-if="item.kpiList.length"
            class="chart-wrapper shadow-light">
            <div
              :id="'kpi-chart' + index"
              class="chart"
            />
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { findKpiDataTree } from '@/api/kpi'
import { getCoreResultValue, post } from '@/lib/Util'
import KpiNode from '@/components/kpiTree/KpiNode'
import KpiDef from '@/components/KpiDef'
import { ENUM } from '@/lib/Constant'

export default {
  name: 'traced-warning',
  components: { KpiDef, KpiNode },
  data() {
    return {
      activeName: null,
      dataList: [],
      kpiFunction: ENUM.kpiFunction,
      showCount: 8
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleClick(tab, event) {
      // console.log(tab, event)
    },
    async loadData() {
      this.dataList = await Promise.all(
        this.kpiFunction.map(async item => {
          const data = await post(findKpiDataTree, {
            feature: String(item.value)
          })
          if (data.success && data.data) {
            item.kpiList = data.data.map(kpi => {
              if (kpi.name == '余材替代汇总') {
                kpi.coreResultValue = 8328
                console.log('kai', kpi)
              }
              return {
                id: kpi.id,
                name: kpi.name,
                value: 1,
                warningStatus: kpi.warningStatus,
                trendWarningStatus: kpi.trendWarningStatus,
                resultValue: getCoreResultValue(kpi) || 0,
                unit: kpi.unit
              }
            })
            item.showCount = 8
          } else {
            item.kpiList = []
          }
          return Promise.resolve(item)
        })
      )
      this.$nextTick(() => {
        this.dataList.forEach((item, index) => {
          item.chart = this.getChart(item, index)
        })
        window.onresize = () => {
          this.dataList.forEach((item, index) => {
            console.log(index)
            item.chart && item.chart.resize()
          })
        }
      })
    },
    getChart(data, index) {
      if (!data.kpiList.length) return null
      const chart = this.$echarts.init(
        document.getElementById('kpi-chart' + index),
        'light'
      )
      chart.setOption({
        color: data.kpiList.map(item => {
          if (item.warningStatus) {
            return '#f56c6c'
          } else if (data.trendWarningStatus) {
            return '#ffa958'
          } else {
            return '#5e93ed'
          }
        }),
        tooltip: {
          trigger: 'item',
          formatter: function(params, ticket, callback) {
            // console.log(params)
            return `<b style="font-weight: bold; font-size: 16px">${
              params.seriesName
            }</b><br/> ${params.data.name}: ${params.data.resultValue} ${
              params.data.unit
            }`
          }
        },
        legend: {
          type: 'scroll',
          top: '3%',
          left: 'center'
        },
        title: {
          text: data.label,
          textStyle: {
            fontSize: 30,
            color: '#0396da'
          },
          subtextStyle: {
            fontSize: 12,
            color: '#0396da',
            align: 'center'
          },
          textAlign: 'center',
          itemGap: 5,
          left: '50%',
          top: '50%'
        },
        series: [
          {
            name: data.label,
            type: 'pie',
            radius: ['40%', '55%'],
            top: '4%',
            left: '3%',
            right: '3%',
            bottom: '0%',
            avoidLabelOverlap: false,
            center: ['50%', '52%'],
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              position: 'outer',
              alignTo: 'edge',
              formatter: function(params) {
                const origin = params.data
                return `${origin.name}\n${origin.resultValue ||
                  '0'}${origin.unit || ''}`
              },
              fontSize: 14,
              minMargin: 10,
              edgeDistance: 20,
              lineHeight: 20
            },
            labelLine: {
              length: 30,
              length2: 0,
              maxSurfaceAngle: 80
            },
            labelLayout: function(params) {
              // console.log(params)
              const isLeft = params.labelRect.x < chart.getWidth() / 2
              const points = params.labelLinePoints
              // Update the end point.
              points[2][0] = isLeft
                ? params.labelRect.x
                : params.labelRect.x + params.labelRect.width
              return {
                labelLinePoints: points
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            data: data.kpiList.slice(0, data.showCount)
          }
        ]
      })
      chart.getZr().on('click', params => {
        // 设置限制条件，确保只有点击的节点为title时才生效
        if (params.topTarget && params.topTarget.style.text === data.label) {
          data.showCount = data.showCount === -1 ? 8 : -1
          //
          chart.setOption({
            series: [
              {
                data: data.kpiList.slice(0, data.showCount)
              }
            ]
          })
        }
      })
      return chart
    }
  }
}
</script>

<style scoped lang="less">
.chart-wrapper {
  margin-bottom: 20px;
  padding-top: 65%;
  position: relative;
  background: #fff;
  .chart {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
  }
}
.kpi-chart {
}
</style>
