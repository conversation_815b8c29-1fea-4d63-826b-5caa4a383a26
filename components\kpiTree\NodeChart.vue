<template>
  <el-dialog
    :visible.sync="visible"
    :width="'900px'"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <div
      slot="title"
      style="height: 32px; line-height: 32px"
    >
      {{ node.name }}
      <el-select
        v-if="mode === 'year'"
        v-model="selectRule"
        size="small"
        placeholder="请选择">
        <el-option
          v-for="(val, key) in monthData"
          :key="key"
          :label="val[0].ruleName"
          :value="key"/>
      </el-select>
    </div>
    <div class="page-content">
      <el-row :gutter="20">
        <el-col :span="18">

          <el-row :gutter="20">
            <el-col :span="12">
              <el-radio-group v-model="chartType">
                <el-radio-button label="bar">柱状图</el-radio-button>
                <el-radio-button label="line">折线图</el-radio-button>
              </el-radio-group>
            </el-col>
            <el-col
              :span="12"
              class="text-right"
            >
              <el-radio-group v-model="mode">
                <el-radio-button label="month">月</el-radio-button>
                <el-radio-button label="year">年</el-radio-button>
              </el-radio-group>
            </el-col>
          </el-row>
          <div
            :id="'rule-chart'"
            class="chart"
          />
        </el-col>
        <el-col :span="6">
          <div
            v-for="(item, index) in overView"
            :key="index"
            class="num-badge"
          >
            {{ item.name }}：<span>{{ getResultValue(item) }}</span> {{ item.unit }}
          </div>
        </el-col>
      </el-row>


    </div>
    <div slot="footer">
      <el-button
        type="primary"
        @click="visible = false"
      >关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import listMixins from '@/mixins/ListMixins'
import { getCoreResultValue, post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'
import {
  deleteKpiWarningRules,
  findResultValueOfMonthAndDay,
  findRulesByKid,
  findRulesByManagerId
} from '@/api/kpi'
import * as lodash from 'lodash'
import { getResultValue } from '@/lib/GetData'
export default {
  name: 'NodeChart',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    node: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {}
    }
  },
  data: () => {
    return {
      kid: null,
      kpiName: null,
      rank: null,
      visible: true,
      selectRule: null,
      dailyData: [],
      monthData: {},
      mode: 'year',
      chartType: 'bar',
      showData: [],
      chart: null,
      overView: {}
    }
  },
  watch: {
    visible(value) {
      this.$emit('input', value)
    },
    chartType(value) {
      this.updateChart()
    },
    mode(value) {
      if (value === 'year') {
        this.showData = this.monthData[this.selectRule]
      } else {
        this.showData = this.dailyData
      }
      this.updateChart()
    },
    selectRule() {
      this.showData = this.monthData[this.selectRule]
      this.updateChart()
    }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      const data = await post(findResultValueOfMonthAndDay, {
        kid: this.node.kid
      })
      if (!data.success) return false
      this.dailyData = data.data.day || []
      this.monthData = lodash.groupBy(data.data.month, 'ruleCode') || {}
      this.overView = data.data.overView
      //
      for (let key in this.monthData) {
        if (this.monthData[key][0].isCoreRule) {
          this.selectRule = key
        }
      }
      this.showData = this.monthData[this.selectRule] || []
      this.drawChart()
    },
    drawChart() {
      this.chart = this.$echarts.init(
        document.getElementById('rule-chart'),
        'light'
      )
      this.chart.setOption({
        xAxis: {
          type: 'category',
          data: this.showData.map(item => item.date)
        },
        yAxis: {
          type: 'value'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '3%',
          containLabel: true
        },
        series: [
          {
            data: this.showData.map(item => item.resultValue),
            type: this.chartType
          }
        ]
      })
    },
    updateChart() {
      this.chart &&
        this.chart.setOption({
          xAxis: {
            type: 'category',
            data: this.showData.map(item => item.date)
          },
          series: [
            {
              data: this.showData.map(item => item.resultValue),
              type: this.chartType
            }
          ]
        })
    },
    onClose() {
      this.tableData = []
    },
    getResultValue(item) {
      return getCoreResultValue({
        coreResultValue: item.value,
        unit: item.unit
      })
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.page-title {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  margin-bottom: 15px;
}

.page-content {
  font-size: 18px;
  background: #fff;
  box-shadow: 0 0 10px rgba(117, 116, 116, 0.1);
}
.chart {
  height: 400px;
}

/deep/ .el-dialog__header {
  border-bottom: 1px solid #eee;
}

.num-badge {
  margin-bottom: 10px;
  font-size: 14px;
  color: #999;
  span {
    color: #666;
    font-size: 20px;
  }
}
</style>
