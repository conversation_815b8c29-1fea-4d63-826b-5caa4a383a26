<template>
  <div>
    <el-dialog
      :title="title + '指标'"
      :visible.sync="visible"
      :width="'600px'"
      :top="'40px'"
      :close-on-click-modal="false"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="150px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="指标名称"
          prop="name"
        >
          <el-input
            v-model="formData.name"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入指标名称"
          />
        </el-form-item>
        <el-form-item
          label="功能"
          prop="features"
        >
          <el-select
            v-model="formData.features"
            :style="{width: '100%'}"
            size="small"
            clearable
            multiple
            placeholder="请选择功能"
          >
            <el-option
              v-for="(item, index) in kpiFunction"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="其他功能分类"
          prop="otherType"
        >
          <el-input
            v-model="formData.otherType"
            :style="{width: '100%'}"
            clearable
            placeholder="请选择其他功能分类"
          />
        </el-form-item>
        <el-form-item
          label="厂区"
          prop="factory"
        >
          <el-select
            v-model="formData.factory"
            :style="{width: '100%'}"
            size="small"
            clearable
            placeholder="请选择厂区"
          >
            <el-option
              v-for="(item, index) in factoryList"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          label="层级"
          prop="rank"
        >
          <el-select
            v-model="formData.rank"
            :style="{width: '100%'}"
            size="small"
            clearable
            placeholder="请选择层级"
          >
            <el-option
              v-for="(item, index) in levelList"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="上级指标"
          prop="parentId"
        >
          <select-kpi
            v-model="formData.parentId"
            :parent-name="formData.parentName"/>
        </el-form-item>
        <el-form-item
          label="指标分类"
          prop="cid"
        >
          <select-kpi-category
            v-model="formData.cid"
            :style="{width: '100%'}"/>
        </el-form-item>
        <el-form-item
          label="指标等级"
          prop="grade"
        >
          <el-select
            v-model="formData.grade"
            :style="{width: '150px'}"
            size="small"
            clearable
            placeholder="请选择指标等级"
          >
            <el-option
              v-for="(item, index) in gradeList"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <template v-if="formData.grade === 3">
            <el-select
              v-model="formData.team"
              :style="{width: '150px'}"
              size="small"
              placeholder="请选择班组"
            >
              <el-option
                v-for="(item, index) in teamList"
                :key="index"
                :disabled="item.disabled"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-form-item>
        <el-form-item
          label="协同跳转url"
          prop="pageDirection"
        >
          <el-input
            v-model="formData.pageDirection"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入协同跳转url"
          />
        </el-form-item>
        <el-form-item
          label="状态"
          prop="remark"
        >
          <el-switch
            v-model="formData.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="废弃"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
        >
          <el-input
            v-model="formData.remark"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { roleAdd, roleEdit, userAdd, userEdit } from '@/api/system'
import { kpiSave } from '@/api/kpi'
import { ENUM } from '@/lib/Constant'
import SelectKpi from '@/components/SelectKpi'
import SelectKpiCategory from '@/components/SelectKpiCategory'

export default {
  components: { SelectKpiCategory, SelectKpi },
  mixins: [EditMixins],
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  data() {
    return {
      visible: false,
      url: {
        edit: kpiSave,
        add: kpiSave
      },
      kpiFunction: ENUM.kpiFunction,
      factoryList: ENUM.factoryList,
      levelList: ENUM.levelList,
      teamList: ENUM.teamList,
      gradeList: ENUM.gradeList,
      statusList: [
        {
          value: 0,
          label: '正常',
          type: 'success'
        },
        {
          value: 1,
          label: '废弃',
          type: 'warning'
        }
      ],
      formData: {
        factory: null,
        features: null,
        cid: null,
        name: null,
        otherType: null,
        pageDirection: null,
        rank: null,
        remark: null,
        status: 0,
        grade: null,
        team: null
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入指标名称',
            trigger: 'change'
          }
        ],
        factory: [
          {
            required: true,
            message: '请选择工厂',
            trigger: 'change'
          }
        ],
        features: [
          {
            required: true,
            message: '请选择功能',
            trigger: 'change'
          }
        ],
        rank: [
          {
            required: true,
            message: '请选择层级',
            type: 'number',
            trigger: 'change'
          }
        ],
        parentId: [
          {
            required: true,
            message: '请选择上级指标',
            type: 'number'
          }
        ],
        cid: [
          {
            required: true,
            message: '请选择指标分类',
            type: 'number'
          }
        ],
        grade: [
          {
            required: true,
            message: '请选择指标等级',
            type: 'number'
          }
        ],
        status: [
          {
            required: true,
            message: '请选择状态',
            type: 'number',
            trigger: 'change'
          }
        ]
      }
    }
  },
  computed: {},
  watch: {
    'formData.grade': function(value) {
      if (value === 3) {
        this.formData.team = '甲'
      } else {
        this.formData.team = null
      }
    }
  },
  created() {
    console.log('编辑页面')
  },
  methods: {
    show() {
      console.log(this.formData.parentName)
    },
    submitBefore() {
      this.formData.feature = this.formData.features.join(',')
    }
  }
}
</script>
<style scoped>
</style>
