<template>
  <div class="bigBox">
    <el-row :gutter="32">
      <el-col :span="12">
        <screen-border title="中板库坯料报表">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(1)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span>
          </template>
          <el-table
            :data="listData1"
            height="342">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="rawMaterialStatus"
              label="坯料状态"
              width="160"
              align="center"/>
            <el-table-column
              prop="numberBlocks"
              label="块数"
              width="100"
              align="center"/>
            <el-table-column
              prop="tonnage"
              label="吨位"
              width="100"
              align="center"/>
            <el-table-column
              prop="remark"
              label="备注"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="中板订单坯料报表">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(2)">
              <el-icon class="el-icon-edit-outline" />
              操作
            </span>
          </template>
          <el-table
            :data="listData2"
            height="342">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="rawMaterialStatus"
              label="坯料状态"
              width="160"
              align="center"/>
            <el-table-column
              prop="warehouse"
              label="堆放仓库"
              width="100"
              align="center"/>
            <el-table-column
              prop="numberBlocks"
              label="块数"
              width="100"
              align="center"/>
            <el-table-column
              prop="tonnage"
              label="吨位"
              width="100"
              align="center"/>
            <el-table-column
              prop="remark"
              label="备注"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="在制品统计">
          <template v-slot:headerRight>
            <!-- <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(3)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span> -->
          </template>
          <el-table
            :data="listData3"
            height="calc(100vh - 630px)">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="toolOfProduction"
              label="生产工序"
              align="center"/>
            <el-table-column
              prop="yesterdayHandleValue"
              label="昨日处理量"
              align="center"/>
            <el-table-column
              prop="yesterdayRollingValue"
              label="昨日轧制量"
              align="center"/>
            <el-table-column
              prop="yesterdayTransportValue"
              label="昨日运转量"
              align="center"/>
            <el-table-column
              prop="workInProgressValue"
              label="目前在制品量"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="12">
        <screen-border title="中板半成品运转">
          <template v-slot:headerRight>
            <!-- <span
              v-command="'/screen/MpFacDispatch/edit'"
              class="screen-btn"
              @click="openView(4)">
              <el-icon class="el-icon-edit-outline"/>
              操作
            </span> -->
          </template>
          <el-table
            :data="listData4"
            height="calc(100vh - 630px)">
            <el-table-column
              type="index"
              label="序号"
              width="60"/>
            <el-table-column
              prop="library"
              label="库别"
              align="center"/>
            <el-table-column
              prop="ppCar"
              label="PP/汽车"
              align="center"/>
            <el-table-column
              prop="ppTrain"
              label="PP/火车"
              align="center"/>
            <el-table-column
              prop="mpCar"
              label="MP/汽车"
              align="center"/>
            <el-table-column
              prop="mpTrain"
              label="MP/火车"
              align="center"/>
            <el-table-column
              prop="daySum"
              label="日总计"
              align="center"/>
            <el-table-column
              prop="monthSum"
              label="日累计"
              align="center"/>
          </el-table>
        </screen-border>
      </el-col>
    </el-row>
    
    <!--弹框-->
    <el-dialog
      :visible.sync="dialogBox"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      @close="closeDialogBox">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span> -->
            <span
              v-show="title!='原因说明'"
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download" />
              下载
            </span>
            <span
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-printer" />
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <div>
        <el-table
          id="table"
          :data="formData"
          border>
          <el-table-column
            type="index"
            label="序号"
            width="60" />
          <el-table-column
            v-for="(item,index) in Header"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            align="center">
            <template v-slot="{ row }">
              <el-input 
                :disabled="item.disabled?item.disabled:false" 
                v-model="row[item.prop]" />
              <span v-show="false">{{ row[item.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            width="100"
            label="操作">
            <template v-slot="{ row, $index }">
              <div class="btn">
                <el-button 
                  :disabled="title=='中板库坯料报表'||title=='中板订单坯料报表'||title=='中板半成品运转'||title=='在制品统计'"
                  type="danger"
                  icon="el-icon-delete"
                  @click="delRow($index)"/>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div 
          v-if="title=='中板订单坯料报表'" 
          class="text-center">
          <span
            class="screen-btn"
            @click="addNewRow">
            <el-icon class="el-icon-circle-plus-outline" />
            增加数据
          </span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { saveAs } from 'file-saver'
import * as echarts from 'echarts'
import moment from 'moment'

import {
  INVENTORYANALYSIS_DATA1,
  INVENTORYANALYSIS_SAVE1,
  INVENTORYANALYSIS_DATA2,
  INVENTORYANALYSIS_SAVE2,
  INVENTORYANALYSIS_DATA3,
  INVENTORYANALYSIS_SAVE3,
  INVENTORYANALYSIS_DATA4,
  INVENTORYANALYSIS_SAVE4
} from '@/api/screen'

export default {
  name: 'InventoryAnalysis',
  components: {
    // SingleBarsChart,
    // SteelBarsChart,
    ScreenBorder,
    ScreenBorderMulti
  },
  props: {
    selTime: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      radio: 0,

      //中板库坯料报表
      listData1: [],

      //中板订单坯料报表
      listData2: [],

      //在制品统计
      listData3: [],

      //中板半成品运转
      listData4: [],

      //弹框统一
      dialogBox: false,
      title: '',
      //弹框统一表字段
      Header: [],
      //弹框统一表数据
      formData: [],
      //上传Excel
      fileList: []
    }
  },
  watch: {
    selTime: function() {
      this.getListData1()
      this.getListData2()
      this.getListData3()
      this.getListData4()
    }
  },

  created() {
    this.getListData1()
    this.getListData2()
    this.getListData3()
    this.getListData4()
  },

  methods: {
    //获取中板库坯料报表
    async getListData1() {
      let res = await post(INVENTORYANALYSIS_DATA1, {
        setTime: this.selTime
      })
      // console.log('获取中板库坯料报表', res)
      if (res.data) {
        this.listData1 = res.data
      }
    },

    //获取中板订单坯料报表
    async getListData2() {
      let res = await post(INVENTORYANALYSIS_DATA2, {
        setTime: this.selTime
      })
      // console.log('获取中板订单坯料报表', res)
      if (res.data) {
        this.listData2 = res.data
      }
    },

    //在制品统计
    async getListData3() {
      let res = await post(INVENTORYANALYSIS_DATA3, {
        setTime: this.selTime
      })
      // console.log('在制品统计', res)
      if (res.data) {
        this.listData3 = res.data
      }
    },

    //中板半成品运转
    async getListData4() {
      let res = await post(INVENTORYANALYSIS_DATA4, {
        setTime: this.selTime
      })
      console.log('中板半成品运转', res)
      if (res.data) {
        this.listData4 = res.data
      }
    },

    //弹框
    openView(nub) {
      this.dialogBox = true
      if (nub == 1) {
        this.title = '中板库坯料报表'
        this.Header = [
          {
            label: '坯料状态',
            prop: 'rawMaterialStatus'
          },
          {
            label: '块数',
            prop: 'numberBlocks'
          },
          {
            label: '吨位',
            prop: 'tonnage'
          },
          {
            label: '备注',
            prop: 'remark'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData1))
      } else if (nub == 2) {
        this.title = '中板订单坯料报表'
        this.Header = [
          {
            label: '坯料状态',
            prop: 'rawMaterialStatus'
          },
          {
            label: '堆放仓库',
            prop: 'warehouse'
          },
          {
            label: '块数',
            prop: 'numberBlocks'
          },
          {
            label: '吨位',
            prop: 'tonnage'
          },
          {
            label: '备注',
            prop: 'remark'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData2))
      } else if (nub == 3) {
        this.title = '在制品统计'
        this.Header = [
          {
            label: '生产工序',
            prop: 'toolOfProduction'
          },
          {
            label: '昨日处理量',
            prop: 'yesterdayHandleValue'
          },
          {
            label: '昨日轧制量',
            prop: 'yesterdayRollingValue'
          },
          {
            label: '昨日运转量',
            prop: 'yesterdayTransportValue'
          },
          {
            label: '目前在制品量',
            prop: 'workInProgressValue'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData3))
      } else if (nub == 4) {
        this.title = '中板半成品运转'
        this.Header = [
          {
            label: '库别',
            prop: 'library'
          },
          {
            label: 'PP/汽车',
            prop: 'ppCar'
          },
          {
            label: 'PP/火车',
            prop: 'ppTrain'
          },
          {
            label: 'MP/汽车',
            prop: 'mpCar'
          },
          {
            label: 'MP/火车',
            prop: 'mpTrain'
          },
          {
            label: '日总计',
            prop: 'daySum'
          },
          {
            label: '日累计',
            prop: 'monthSum'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.listData4))
      }
    },

    //添加行
    addNewRow() {
      let row = {}
      this.Header.forEach(item => {
        row[item.prop] = ''
      })

      this.formData.push(row)
    },

    //删除行
    delRow(indexs) {
      this.formData.forEach((item, index) => {
        if (indexs == index) {
          this.formData.splice(index, 1)
        }
      })
    },

    //下载模板
    DownloadExcel() {
      let data = [{}]
      this.Header.forEach(item => {
        data[0][item.prop] = item.label
      })

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `${this.title}.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          `${this.title}.xlsx`
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      let header = {}
      let letter = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      this.Header.forEach((item, index) => {
        header[item.prop] = letter[index]
      })

      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, header)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        console.log(list)

        list.forEach(item => {
          item.selTime = this.selTime
          this.formData.push(item)
        })
      })
    },

    //保存新增数据
    async saveData() {
      let res
      if (this.title == '中板库坯料报表') {
        res = await post(INVENTORYANALYSIS_SAVE1, {
          setTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '中板订单坯料报表') {
        res = await post(INVENTORYANALYSIS_SAVE2, {
          setTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '在制品统计') {
        res = await post(INVENTORYANALYSIS_SAVE3, {
          setTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '中板半成品运转') {
        res = await post(INVENTORYANALYSIS_SAVE4, {
          setTime: this.selTime,
          data: this.formData
        })
      }

      // console.log('保存', res)
      if (res.status == 1) {
        this.$message.success('保存成功')
        if (this.title == '中板库坯料报表') {
          this.getListData1()
        } else if (this.title == '中板订单坯料报表') {
          this.getListData2()
        } else if (this.title == '在制品统计') {
          this.getListData3()
        } else if (this.title == '中板半成品运转') {
          this.getListData4()
        }

        this.closeDialogBox()
      }
    },

    //关闭弹框
    closeDialogBox() {
      this.dialogBox = false
      this.title = ''
      this.Header = []
      this.formData = []
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .tabBox {
      display: flex;
      .tab {
        color: #ffffffbf;
        margin-right: 20px;
      }
      .tab_block {
        display: flex;
        flex-direction: column;
        position: relative;
        .tab_img {
          .tab_img2 {
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
          }
          .tab_img1 {
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
            margin-bottom: 7px;
          }
        }
      }
    }
    .border-content {
      height: 380px;
    }
  }
  .EchartsBox {
    height: 380px;
    .setRadio {
      /deep/.el-radio {
        color: white;
      }
    }
  }
  .border-wrapper {
    margin-bottom: 15px;
  }
  /deep/.el-textarea__inner {
    background-color: #041a21;
    border: 1px solid #1fc6ff;
    color: white;
    font-size: 14px;
    height: 70px;
  }
}

.btn {
  /deep/.el-button {
    font-size: 15px;
    padding: 4px 15px;
    border-radius: 4px;
  }
}

/deep/ .el-table .el-table__cell {
  font-size: 19px;
}

/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}
</style>
