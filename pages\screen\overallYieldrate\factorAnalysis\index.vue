<template>
  <div class="container">
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="炼钢因子分析">
          <template v-slot:headerRight>
            <el-date-picker
              v-model="dateRange"
              :clearable="false"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="screen-input"
              popper-class="date-picker-dropdown"
              @change="(value) => handleDateRangeChange(value, 'steel')" />
          </template>
          <pie-chart
            :chart-data="pieChartData"
            :color="['#3DB842','#F7C900','#0071F5', '#EF1E0E']"
            unit="吨"
            @click-item="handlePieChartClick"
          />
          <div class="total-info">
            损失量（坯替代 + 坯判废）：{{ totalBlankReplacementAndScrap }} 吨<br>
            损失金额（坯料替代 + 坯料判废）： {{ cost }} 万元
          </div>
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="坯料替代">
          <div class="radio-selector">
            <el-radio
              v-model="blankSubstitutionSelected"
              label="替代量"
              @change="handleRadioChange('blankSubstitution', '替代量', 2)">替代量
            </el-radio>
            <el-radio
              v-model="blankSubstitutionSelected"
              label="成本损失"
              @change="handleRadioChange('blankSubstitution', '成本损失', 10)">成本损失（万元）
            </el-radio>
          </div>
          <bar-direction-chart-column
            v-if="hasData(getSelectedChartData(blankSubstitutionData, blankSubstitutionSelected))"
            :chart-data="getSelectedChartData(blankSubstitutionData, blankSubstitutionSelected)"
            :x-data="blankSubstitutionXData"
            :show-label="true"
            :show-legend="false"
            :bar-width="20"
            :color="['#F7C900']"
            :start-date="dateRange[0]"
            :end-date="dateRange[1]"
            :slab-flag="'2'"
            :title="'坯料替代'"
          />
          <div
            v-else
            class="no-data-tip">暂无数据
          </div>
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="坯料判废">
          <div class="radio-selector">
            <el-radio
              v-model="blankScrapSelected"
              label="判废量"
              @change="handleRadioChange('blankScrap', '判废量', 4)">判废量
            </el-radio>
            <el-radio
              v-model="blankScrapSelected"
              label="成本损失"
              @change="handleRadioChange('blankScrap', '成本损失', 9)">成本损失（万元）
            </el-radio>
          </div>
          <bar-direction-chart-column
            v-if="hasData(getSelectedChartData(blankScrapData, blankScrapSelected))"
            :chart-data="getSelectedChartData(blankScrapData, blankScrapSelected)"
            :x-data="blankScrapXData"
            :show-label="true"
            :show-legend="false"
            :bar-width="20"
            :color="['#EF1E0E']"
            :start-date="dateRange[0]"
            :end-date="dateRange[1]"
            :slab-flag="'1'"
            :title="'坯料判废'"
          />
          <div
            v-else
            class="no-data-tip">暂无数据
          </div>
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="坯料库存">
          <div class="radio-selector">
            <!-- <el-radio
              v-model="blankStockSelected"
              label="库存量">库存量</el-radio>
            <el-radio
              v-model="blankStockSelected"
              label="成本损失">成本损失（万元）</el-radio> -->
          </div>
          <bar-direction-chart-column
            v-if="hasData(getSelectedChartData(blankStockData, blankStockSelected))"
            :chart-data="getSelectedChartData(blankStockData, blankStockSelected)"
            :x-data="blankStockXData"
            :show-label="true"
            :show-legend="false"
            :bar-width="20"
            :color="['#0071F5']"
            :start-date="dateRange[0]"
            :end-date="dateRange[1]"
            :slab-flag="'3'"
            :title="'坯料库存'"
          />
          <div
            v-else
            class="no-data-tip">暂无数据
          </div>
        </screen-border>
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-box">
        <screen-border title="轧钢因子分析">
          <template v-slot:headerRight>
            <el-date-picker
              v-model="dateRangeTwo"
              :clearable="false"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="screen-input"
              popper-class="date-picker-dropdown"
              @change="(value) => handleDateRangeChange(value, 'roll')" />
          </template>
          <pie-chart
            :chart-data="pieChartDataTwo"
            :color="['#F7C900','#0071F5', '#EF1E0E','#3DB842']"
            unit="吨"
            @click-item="handleRollPieChartClick"
          />
          <div class="total-info">
            总和（材改判协议 + 材判废）：{{ totalSteelChangeAndScrap }} 吨
          </div>
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="材改判协议">
          <div class="radio-selector">
            <!-- <el-radio
              v-model="steelGradeChangeSelected"
              label="改判协议量">改判协议量</el-radio>
            <el-radio
              v-model="steelGradeChangeSelected"
              label="成本损失">成本损失（万元）</el-radio> -->
          </div>
          <bar-direction-chart-column
            v-if="hasData(getSelectedChartData(steelGradeChangeData, steelGradeChangeSelected))"
            :chart-data="getSelectedChartData(steelGradeChangeData, steelGradeChangeSelected)"
            :x-data="steelGradeChangeXData"
            :show-label="true"
            :show-legend="false"
            :bar-width="20"
            :color="['#F7C900']"
            :start-date="dateRangeTwo[0]"
            :end-date="dateRangeTwo[1]"
            :slab-flag="'2'"
            :title="'材改判协议'"
            @click-bar="handleMaterialChartClick"
          />
          <div
            v-else
            class="no-data-tip">暂无数据
          </div>
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="材判废">
          <div class="radio-selector">
            <!-- <el-radio
              v-model="materialsScrapSelected"
              label="判废量">判废量</el-radio>
            <el-radio
              v-model="materialsScrapSelected"
              label="成本损失">成本损失（万元）</el-radio> -->
          </div>
          <bar-direction-chart-column
            v-if="hasData(getSelectedChartData(materialsScrapData, materialsScrapSelected))"
            :chart-data="getSelectedChartData(materialsScrapData, materialsScrapSelected)"
            :x-data="materialsScrapXData"
            :show-label="true"
            :show-legend="false"
            :bar-width="20"
            :color="['#EF1E0E']"
            :start-date="dateRangeTwo[0]"
            :end-date="dateRangeTwo[1]"
            :slab-flag="'1'"
            :title="'材判废'"
            @click-bar="handleMaterialChartClick"
          />
          <div
            v-else
            class="no-data-tip">暂无数据
          </div>
        </screen-border>
      </div>
      <div class="chart-box">
        <screen-border title="材现货">
          <div class="radio-selector">
            <!-- <el-radio
              v-model="materialsStockSelected"
              label="现货量">现货量</el-radio>
            <el-radio
              v-model="materialsStockSelected"
              label="成本损失">成本损失（万元）</el-radio> -->
          </div>
          <bar-direction-chart-column
            v-if="hasData(getSelectedChartData(materialsStockData, materialsStockSelected))"
            :chart-data="getSelectedChartData(materialsStockData, materialsStockSelected)"
            :x-data="materialsStockXData"
            :show-label="true"
            :show-legend="false"
            :bar-width="20"
            :color="['#0071F5']"
            :start-date="dateRangeTwo[0]"
            :end-date="dateRangeTwo[1]"
            :slab-flag="'3'"
            :title="'材现货'"
            @click-bar="handleMaterialChartClick"
          />
          <div
            v-else
            class="no-data-tip">暂无数据
          </div>
        </screen-border>
      </div>

    </div>
    <el-dialog
      :visible.sync="dialogVisible2"
      :title="titleDetail"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          {{ titleDetail }}
        </div>
      </template>
      <div class="dialog-content">
        <pie-factor-analysis
          v-if="dialogVisible2 && pieDetailData && pieDetailData.length > 0"
          :detail-data="pieDetailData"
          :title="titleDetail"
        />
        <div
          v-else-if="dialogVisible2"
          class="no-data-tip no-data-tip-detail">
          暂无数据
        </div>
      </div>
      <span
        slot="footer"
        class="dialog-footer">
        <el-button @click="dialogVisible2 = false">取消</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/qualityDailyReportScreen/components/screen-border.vue'
import PieChart from '@/pages/screen/overallYieldrate/component/pie-chart.vue'
import BarDirectionChart from '@/pages/screen/overallYieldrate/component/bar-direction-chart.vue'
import BarDirectionChartColumn from '@/pages/screen/overallYieldrate/component/bar-direction-chart-column.vue'
import PieFactorAnalysis from '@/pages/screen/overallYieldrate/component/pie-factor-analysis.vue'
import {
  SlabQltPanFei1,
  findDetailsDateQlt1,
  SlabPanFei1,
  findSteelmakingAnalysis1,
  findSteelmakingAnalysis2
} from '@/api/screen'
import { post } from '@/lib/Util'

export default {
  name: 'KeySteelGrade',
  components: {
    ScreenBorder,
    PieChart,
    BarDirectionChart,
    BarDirectionChartColumn,
    PieFactorAnalysis
  },
  data() {
    return {
      // 初始化日期范围为今天和7天前
      dateRange: [
        this.$moment()
          .subtract(7, 'days')
          .format('YYYY-MM-DD'),
        this.$moment().format('YYYY-MM-DD')
      ],
      dateRangeTwo: [
        this.$moment()
          .subtract(7, 'days')
          .format('YYYY-MM-DD'),
        this.$moment().format('YYYY-MM-DD')
      ],
      dialogVisible2: false,
      pieChartData: [],
      pieChartDataTwo: [],
      blankSubstitutionData: [],
      processAppealData: [],
      processAppealXData: [],
      blankSubstitutionSelected: '替代量',
      blankJudgmentChangeSelected: '改判量',
      blankStockSelected: '库存量',
      blankScrapSelected: '判废量',
      steelGradeChangeSelected: '改判协议量',
      materialsStockSelected: '现货量',
      materialsScrapSelected: '判废量',

      // 坯料改判数据
      blankJudgmentChangeData: [],

      // 坯料库存数据
      blankStockData: [],

      // 坯料判废数据
      blankScrapData: [],

      // 材改判协议数据
      steelGradeChangeData: [],

      // 材现货数据
      materialsStockData: [],

      // 材判废数据
      materialsScrapData: [],

      // 记录各模块当前选择的moduleFlag
      moduleFlags: {
        blankSubstitution: 2,
        blankJudgmentChange: 3,
        blankStock: 3,
        blankScrap: 4,
        steelGradeChange: 6,
        materialsStock: 7,
        materialsScrap: 8
      },
      blankSubstitutionXData: [],
      blankScrapXData: [],
      blankStockXData: [],
      steelGradeChangeXData: [],
      materialsStockXData: [],
      materialsScrapXData: [],
      titleDetail: '',
      pieDetailData: [],
      loading: false,
      cost: 0
    }
  },
  computed: {
    totalBlankReplacementAndScrap() {
      // 应该只从饼图数据中获取信息，而不是从柱状图数据
      const blankReplacement = this.pieChartData.find(
        item => item.name === '坯替代'
      )
      const blankScrap = this.pieChartData.find(item => item.name === '坯判废')

      // 获取坯替代数据值
      const replacementTotal = blankReplacement
        ? Number(blankReplacement.value) || 0
        : 0
      // 获取坯判废数据值
      const scrapTotal = blankScrap ? Number(blankScrap.value) || 0 : 0

      // 计算总和并格式化为两位小数
      const total = replacementTotal + scrapTotal
      return total.toFixed(2)
    },
    totalSteelChangeAndScrap() {
      // 直接从第二个饼图数据中获取信息
      const steelGradeChange = this.pieChartDataTwo.find(
        item => item.name === '材改判协议'
      )
      const materialsScrap = this.pieChartDataTwo.find(
        item => item.name === '材判废'
      )

      // 获取材改判协议数据值
      const changeTotal = steelGradeChange
        ? Number(steelGradeChange.value) || 0
        : 0
      // 获取材判废数据值
      const scrapTotal = materialsScrap ? Number(materialsScrap.value) || 0 : 0

      // 计算总和并格式化为两位小数
      const total = changeTotal + scrapTotal
      return total.toFixed(2)
    }
  },
  created() {
    this.getSteelmakingAnalysis1()
    this.getSteelmakingAnalysis2()
  },
  methods: {
    // 处理日期范围变化
    handleDateRangeChange(value, type) {
      if (type === 'steel') {
        this.dateRange = value
        this.getSteelmakingAnalysis1()
      } else if (type === 'roll') {
        this.dateRangeTwo = value
        this.getSteelmakingAnalysis2()
      }
    },

    // 获取炼钢因子分析数据
    async getSteelmakingAnalysis1() {
      try {
        const loadingInstance = this.$loading({
          lock: true,
          text: '加载中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)',
          customClass: 'custom-loading'
        })

        const res = await post(findSteelmakingAnalysis1, {
          startDate: this.dateRange[0],
          endDate: this.dateRange[1]
        })

        if (res.data) {
          // 处理炼钢因子分析饼图数据
          if (res.data.LGYZFX) {
            this.pieChartData = this.handlePieChartData(res.data.LGYZFX)
          }

          // 处理坯料替代数据
          if (res.data.PLTD) {
            this.blankSubstitutionData = this.handleBarChartData(
              res.data.PLTD,
              '替代量',
              '成本损失',
              2
            ).chartData
            this.blankSubstitutionXData = this.handleBarChartData(
              res.data.PLTD,
              '替代量',
              '成本损失',
              2
            ).sortedNames
          }

          // 处理坯料替代成本损失数据
          if (res.data.TDCBSS) {
            // 当选择了成本损失时，使用TDCBSS数据
            if (this.blankSubstitutionSelected === '成本损失') {
              this.blankSubstitutionData = this.handleBarChartData(
                res.data.TDCBSS,
                '替代量',
                '成本损失',
                10
              ).chartData
              this.blankSubstitutionXData = this.handleBarChartData(
                res.data.TDCBSS,
                '替代量',
                '成本损失',
                10
              ).sortedNames
            }
          }

          // 处理坯料判废数据
          if (res.data.PLPF) {
            this.blankScrapData = this.handleBarChartData(
              res.data.PLPF,
              '判废量',
              '成本损失',
              4
            ).chartData
            this.blankScrapXData = this.handleBarChartData(
              res.data.PLPF,
              '判废量',
              '成本损失',
              4
            ).sortedNames
          }

          // 处理坯料判废成本损失数据
          if (res.data.PFCBSS) {
            // 当选择了成本损失时，使用PFCBSS数据
            if (this.blankScrapSelected === '成本损失') {
              this.blankScrapData = this.handleBarChartData(
                res.data.PFCBSS,
                '判废量',
                '成本损失',
                9
              ).chartData
              this.blankScrapXData = this.handleBarChartData(
                res.data.PFCBSS,
                '判废量',
                '成本损失',
                9
              ).sortedNames
            }
          }

          // 处理坯料库存数据
          if (res.data.PLKC) {
            this.blankStockData = this.handleBarChartData(
              res.data.PLKC,
              '库存量',
              '成本损失',
              3
            ).chartData
            this.blankStockXData = this.handleBarChartData(
              res.data.PLKC,
              '库存量',
              '成本损失',
              3
            ).sortedNames
          }

          // 处理成本损失数据 - 坯料替代总损失 + 坯料判废总损失
          let totalCost = 0
          if (res.data.PFZCBSS && res.data.PFZCBSS.length > 0) {
            totalCost += Number(res.data.PFZCBSS[0].costLost || 0)
          }
          if (res.data.TDCBZSS && res.data.TDCBZSS.length > 0) {
            totalCost += Number(res.data.TDCBZSS[0].costLost || 0)
          }
          this.cost = totalCost.toFixed(3)
        }

        loadingInstance.close()
      } catch (error) {
        console.error('获取炼钢因子分析数据失败:', error)
        this.$message.error('获取炼钢因子分析数据失败')
      }
    },

    // 获取轧钢因子分析数据
    async getSteelmakingAnalysis2() {
      try {
        const loadingInstance = this.$loading({
          lock: true,
          text: '加载中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)',
          customClass: 'custom-loading'
        })

        const res = await post(findSteelmakingAnalysis2, {
          startDate: this.dateRangeTwo[0],
          endDate: this.dateRangeTwo[1]
        })

        if (res.data) {
          // 处理轧钢因子分析饼图数据
          if (res.data.ZGYZFX) {
            this.pieChartDataTwo = this.handlePieChartData(res.data.ZGYZFX)
          }

          // 处理材改判协议数据
          if (res.data.CGPXY) {
            this.steelGradeChangeData = this.handleBarChartData(
              res.data.CGPXY,
              '改判协议量',
              '成本损失',
              6
            ).chartData
            this.steelGradeChangeXData = this.handleBarChartData(
              res.data.CGPXY,
              '改判协议量',
              '成本损失',
              6
            ).sortedNames
          }

          // 处理材判废数据
          if (res.data.CPF) {
            this.materialsScrapData = this.handleBarChartData(
              res.data.CPF,
              '判废量',
              '成本损失',
              8
            ).chartData
            this.materialsScrapXData = this.handleBarChartData(
              res.data.CPF,
              '判废量',
              '成本损失',
              8
            ).sortedNames
          }

          // 处理材现货数据
          if (res.data.CXH) {
            this.materialsStockData = this.handleBarChartData(
              res.data.CXH,
              '现货量',
              '成本损失',
              7
            ).chartData
            this.materialsStockXData = this.handleBarChartData(
              res.data.CXH,
              '现货量',
              '成本损失',
              7
            ).sortedNames
          }
        }

        loadingInstance.close()
      } catch (error) {
        console.error('获取轧钢因子分析数据失败:', error)
        this.$message.error('获取轧钢因子分析数据失败')
      }
    },

    // 保留原有的getFactorAnalysis方法以兼容旧代码
    async getFactorAnalysis(
      type = 'all',
      specificModule = null,
      specificFlag = null
    ) {
      // 使用新的API接口
      if (type === 'all' || type === 'steel') {
        await this.getSteelmakingAnalysis1()
      }

      if (type === 'all' || type === 'roll') {
        await this.getSteelmakingAnalysis2()
      }
    },

    // 处理响应数据
    processResponseData(moduleName, moduleFlag, data) {
      console.log('处理响应数据:', moduleName, moduleFlag, data) // 添加日志

      switch (Number(moduleFlag)) {
        case 1: // 炼钢因子分析
          const pieData = Array.isArray(data) && data.length > 0 ? data : []
          this.pieChartData = this.handlePieChartData(pieData)
          break
        case 2: // 坯料替代
        case 3: // 坯料库存
        case 4: // 坯料判废
        case 6: // 材改判协议
        case 7: // 材现货
        case 8: // 材判废
        case 9: // 坯料判废-成本损失
        case 10: // 坯料替代-成本损失（添加这个case）
          // 确定数据应该更新到哪个属性
          let targetDataProp = this.getDataPropByModuleName(moduleName)
          if (targetDataProp) {
            const result = this.handleBarChartData(
              data,
              this.getLabelsByModule(moduleName)[0],
              this.getLabelsByModule(moduleName)[1],
              moduleFlag
            )
            console.log('图表数据处理结果:', result) // 添加日志
            this[targetDataProp] = result.chartData
            // 为每个模块维护独立的X轴数据
            this.$set(this, `${moduleName}XData`, result.sortedNames)
          }
          break
        case 5: // 轧钢因子分析
          const pieTwoData = Array.isArray(data) && data.length > 0 ? data : []
          this.pieChartDataTwo = this.handlePieChartData(pieTwoData)
          break
        case 11:
          this.cost = 0
          data.forEach(item => {
            this.cost = Number(this.cost) + Number(item.costLost)
          })
          this.cost = Number(this.cost).toFixed(3)
          break
      }
    },

    // 获取模块名称通过moduleFlag
    getModuleNameByFlag(flag) {
      const flagToName = {
        1: 'steelAnalysis',
        2: 'blankSubstitution',
        3: 'blankStock',
        4: 'blankScrap',
        5: 'rollAnalysis',
        6: 'steelGradeChange',
        7: 'materialsStock',
        8: 'materialsScrap',
        9: 'blankScrap' // 坯料判废-成本损失也映射到同一模块
      }
      return flagToName[flag] || ''
    },

    // 获取数据属性名通过模块名称
    getDataPropByModuleName(moduleName) {
      const nameToDataProp = {
        blankSubstitution: 'blankSubstitutionData',
        blankStock: 'blankStockData',
        blankScrap: 'blankScrapData',
        steelGradeChange: 'steelGradeChangeData',
        materialsStock: 'materialsStockData',
        materialsScrap: 'materialsScrapData'
      }
      return nameToDataProp[moduleName] || ''
    },

    // 获取模块对应的标签名称
    getLabelsByModule(moduleName) {
      const moduleLabels = {
        blankSubstitution: ['替代量', '成本损失'],
        blankStock: ['库存量', '成本损失'],
        blankScrap: ['判废量', '成本损失'],
        steelGradeChange: ['改判协议量', '成本损失'],
        materialsStock: ['现货量', '成本损失'],
        materialsScrap: ['判废量', '成本损失']
      }
      return moduleLabels[moduleName] || ['', '']
    },

    // 处理单选框变更
    handleRadioChange(moduleName, selectedValue, moduleFlag) {
      console.log('单选框变更:', moduleName, selectedValue, moduleFlag)

      // 清空当前模块数据，避免数据混淆
      const targetDataProp = this.getDataPropByModuleName(moduleName)
      if (targetDataProp) {
        this[targetDataProp] = []
        this.$set(this, `${moduleName}XData`, [])
      }

      // 更新模块对应的 moduleFlag
      this.moduleFlags[moduleName] = moduleFlag

      // 更新选中的值
      if (moduleName === 'blankSubstitution') {
        this.blankSubstitutionSelected = selectedValue
      } else if (moduleName === 'blankScrap') {
        this.blankScrapSelected = selectedValue
      }

      // 根据模块类型重新获取数据
      if (this.isSteelModule(moduleFlag)) {
        this.getSteelmakingAnalysis1()
      } else {
        this.getSteelmakingAnalysis2()
      }
    },

    // 添加一个辅助函数判断模块是否属于炼钢模块
    isSteelModule(moduleFlag) {
      // 炼钢相关的moduleFlag包括1, 2, 3, 4, 9, 10
      return [1, 2, 3, 4, 9, 10].includes(Number(moduleFlag))
    },
    //处理饼图数据
    handlePieChartData(data) {
      if (!Array.isArray(data)) return []
      return data.map(item => ({
        name: item.name || '未知',
        value: Number(item.wgt) || 0,
        rate: item.rate || '0%' // 修正这里，确保保留rate字段
      }))
    },
    // 提取X轴数据（钢种名称）
    extractXAxisData(data) {
      if (!Array.isArray(data) || data.length === 0) return []
      return data.map(item => item.name || '未知')
    },
    // 处理条形图数据
    handleBarChartData(data, typeName1, typeName2, moduleFlag) {
      console.log('处理条形图数据:', data, typeName1, typeName2, moduleFlag) // 添加日志

      if (!Array.isArray(data) || data.length === 0) {
        return {
          chartData: [
            { name: typeName1, data: [] },
            { name: typeName2, data: [] }
          ],
          sortedNames: []
        }
      }

      // 根据模块标志决定使用哪个字段
      const valueField =
        moduleFlag === 10 || moduleFlag === 9 ? 'costLost' : 'wgt'
      console.log('使用字段:', valueField) // 添加日志

      // 获取正确的显示名称
      const displayName =
        moduleFlag === 10 || moduleFlag === 9 ? '成本损失' : typeName1

      // 创建包含名称和数值的完整数据副本，并过滤掉值为0的数据
      const fullData = data
        .filter(item => {
          // 过滤掉值为0的数据项
          const value = parseFloat(item[valueField]) || 0
          return value > 0
        })
        .map(item => ({
          name: item.name || '未知',
          value: parseFloat(item[valueField]) || 0
        }))
      console.log('处理后的完整数据(已过滤0值):', fullData) // 添加日志

      // 按数值降序排序
      const sortedData = [...fullData].sort((a, b) => b.value - a.value)
      console.log('排序后的数据:', sortedData) // 添加日志

      // 分离排序后的名称和数值
      const sortedNames = sortedData.map(item => item.name)
      const type1Data = sortedData.map(item => ({
        value: item.value,
        name: item.name // 确保包含原始名称用于提示
      }))

      // 成本损失模式下只有一组数据
      const result = {
        chartData: [{ name: displayName, data: type1Data }],
        sortedNames
      }

      console.log('最终图表数据:', result) // 添加日志
      return result
    },
    // 检查图表数据是否为空
    hasData(chartData) {
      console.log('检查数据是否存在:', chartData) // 添加日志
      return (
        chartData &&
        chartData.length > 0 &&
        chartData[0].data &&
        chartData[0].data.length > 0
      )
    },
    // 获取选中的图表数据
    getSelectedChartData(dataArray, selectedValue) {
      console.log('获取选中的图表数据:', dataArray, selectedValue) // 添加日志

      if (!Array.isArray(dataArray) || dataArray.length === 0) {
        return []
      }

      // 成本损失时，可能只有一个数据项
      if (selectedValue.includes('成本') && dataArray.length === 1) {
        return dataArray
      }

      // 找到与所选值匹配的数据集
      let selectedIndex = 0
      if (selectedValue.includes('成本') || selectedValue.includes('损失')) {
        selectedIndex = dataArray.length > 1 ? 1 : 0
      }

      // 如果找不到对应索引的数据，返回空数组
      if (!dataArray[selectedIndex]) {
        return []
      }

      return [dataArray[selectedIndex]]
    },
    handlePieChartClick(item) {
      console.log('点击炼钢饼图项:', item)

      // 映射点击的饼图项名称到对应的slabFlag
      const nameToSlabFlag = {
        坯判废: '1',
        坯替代: '2',
        坯库存: '3'
      }

      const slabFlag = nameToSlabFlag[item.name]

      // 如果点击的不是这些类型之一，则不显示弹窗
      if (!slabFlag) {
        return
      }

      // 设置弹窗标题
      this.titleDetail = `${item.name}详情`

      // 获取详情数据
      this.getPieDetailData(slabFlag)
    },
    handleRollPieChartClick(item) {
      console.log('点击轧钢饼图项:', item)

      // 映射点击的饼图项名称到对应的slabFlag
      const nameToSlabFlag = {
        材判废: '1',
        材改判协议: '2',
        材现货: '3'
      }

      const slabFlag = nameToSlabFlag[item.name]

      // 如果点击的不是这些类型之一，则不显示弹窗
      if (!slabFlag) {
        return
      }

      // 设置弹窗标题
      this.titleDetail = `${item.name}详情`

      // 获取轧钢详情数据
      this.getRollPieDetailData(slabFlag)
    },
    async getPieDetailData(slabFlag) {
      const loadingInstance = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'custom-loading'
      })

      try {
        const res = await post(SlabPanFei1, {
          startDate: this.dateRange[0],
          endDate: this.dateRange[1],
          slabFlag: slabFlag
        })

        // 检查数据是否为空
        if (res.data && Array.isArray(res.data) && res.data.length > 0) {
          this.pieDetailData = res.data
          this.dialogVisible2 = true
        } else {
          this.pieDetailData = [] // 确保数据为空数组
          this.dialogVisible2 = true // 仍然显示弹窗
        }
      } catch (error) {
        console.error('请求饼图详情失败:', error)
        this.$message.error('获取详情数据失败')
      } finally {
        loadingInstance.close()
      }
    },
    async getRollPieDetailData(slabFlag) {
      const loadingInstance = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'custom-loading'
      })

      try {
        const res = await post(SlabQltPanFei1, {
          startDate: this.dateRangeTwo[0],
          endDate: this.dateRangeTwo[1],
          slabFlag: slabFlag
        })

        // 检查数据是否为空
        if (res.data && Array.isArray(res.data) && res.data.length > 0) {
          this.pieDetailData = res.data
          this.dialogVisible2 = true
        } else {
          this.pieDetailData = [] // 确保数据为空数组
          this.dialogVisible2 = true // 仍然显示弹窗
        }
      } catch (error) {
        console.error('请求轧钢饼图详情失败:', error)
        this.$message.error('获取详情数据失败')
      } finally {
        loadingInstance.close()
      }
    },
    // 修改handleMaterialChartClick方法，确保使用正确的slabFlag
    async handleMaterialChartClick(steelType) {
      console.log('材料图表点击:', steelType)
      if (!steelType) return

      let slabFlag = '1' // 默认材判废
      let title = ''

      // 根据当前点击的图表类型设置不同的slabFlag
      if (event && event.target) {
        const chartElement = event.target.closest('.chart-box')
        if (chartElement) {
          const titleElement = chartElement.querySelector(
            '.screen-border-header-title'
          )
          if (titleElement) {
            title = titleElement.textContent.trim()

            if (title.includes('材改判协议')) {
              slabFlag = '2'
            } else if (title.includes('材现货')) {
              slabFlag = '3'
            } else if (title.includes('材判废')) {
              slabFlag = '1'
            }
          }
        }
      }

      this.titleDetail = `${title}详情 - ${steelType}`
      this.dialogVisible2 = true

      try {
        const res = await post(findDetailsDateQlt1, {
          startDate: this.dateRangeTwo[0],
          endDate: this.dateRangeTwo[1],
          slabFlag: slabFlag, // 使用根据图表类型确定的slabFlag
          steelType: steelType
        })

        if (res.data && Array.isArray(res.data)) {
          this.pieDetailData = res.data
        } else {
          this.pieDetailData = []
        }
      } catch (error) {
        console.error('获取详情数据失败:', error)
        this.$message.error('获取详情数据失败')
        this.pieDetailData = []
      }
    }
  }
}
</script>

<style lang="less">
/* 日期选择器下拉面板科技风格 */
.date-picker-dropdown {
  background-color: rgba(4, 29, 53, 0.95);
  border: 1px solid rgba(0, 242, 254, 0.7);
  box-shadow: 0 0 15px rgba(0, 242, 254, 0.3);

  /* 标题和头部样式 */
  .el-date-range-picker__header {
    margin: 8px;
    color: #fff;
  }

  /* 日期表格样式 */
  .el-picker-panel__content {
    color: #fff;

    .el-date-table {
      th {
        color: rgba(0, 242, 254, 0.8);
        font-weight: normal;
        border-bottom: 1px solid rgba(0, 242, 254, 0.3);
      }

      td {
        &.available:hover {
          background-color: rgba(0, 242, 254, 0.2);
        }

        &.in-range div {
          background-color: rgba(0, 242, 254, 0.15);
          color: #fff;
        }

        &.end-date div,
        &.start-date div {
          background-color: rgba(0, 242, 254, 0.8);
          color: #000;
        }

        &.today span {
          color: #1fc6ff;
        }

        &.disabled div {
          background-color: transparent;
          color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  /* 按钮样式 */
  .el-picker-panel__footer {
    background-color: rgba(4, 29, 53, 0.8);
    border-top: 1px solid rgba(0, 242, 254, 0.3);

    .el-button {
      background: transparent;
      border: 1px solid rgba(0, 242, 254, 0.5);
      color: #fff;

      &:hover {
        background-color: rgba(0, 242, 254, 0.2);
        border-color: rgba(0, 242, 254, 0.8);
      }

      &--default {
        margin-right: 10px;
      }

      &--primary {
        background-color: rgba(0, 242, 254, 0.3);
        border-color: rgba(0, 242, 254, 0.7);

        &:hover {
          background-color: rgba(0, 242, 254, 0.5);
        }
      }
    }
  }

  /* 箭头样式 */
  .el-date-range-picker__header .el-icon-arrow-left,
  .el-date-range-picker__header .el-icon-arrow-right,
  .el-date-range-picker__header .el-icon-d-arrow-left,
  .el-date-range-picker__header .el-icon-d-arrow-right {
    color: rgba(0, 242, 254, 0.8);

    &:hover {
      color: rgba(0, 242, 254, 1);
    }
  }

  .el-date-table td.end-date span,
  .el-date-table td.start-date span {
    background-color: rgba(0, 242, 254, 0.6);
    color: #fff;
    font-weight: bold;
  }
}
</style>


<style lang="less">
/* 全局日期选择器样式 - 不使用scoped以确保样式应用到所有日期选择器 */
.el-date-editor.el-range-editor.el-input__inner {
  background-color: rgba(8, 47, 60, 0.9) !important;
  border: 1px solid #1fc6ff !important;
  border-radius: 4px !important;
  width: 240px !important;
  height: 32px !important;
  line-height: 32px !important;
  padding-left: 10px !important; /* 减少左侧内边距，因为日历图标已隐藏 */
}

.el-range-input {
  background-color: transparent !important;
  color: #fff !important;
  font-size: 14px !important;
  height: 30px !important;
  line-height: 30px !important;
}

.el-range-separator {
  color: #1fc6ff !important;
}

.el-input__icon,
.el-range__close-icon {
  color: #1fc6ff !important;
  line-height: 30px !important;
}

/* 隐藏日期选择器的日历图标 */
.el-range__icon {
  display: none !important;
}

/* 日期选择器悬停和焦点状态 */
.el-date-editor.el-range-editor.el-input__inner:hover,
.el-date-editor.el-range-editor.el-input__inner:focus,
.el-date-editor.el-range-editor.el-input.is-active .el-input__inner {
  border-color: #1fc6ff !important;
  background-color: rgba(8, 47, 60, 0.95) !important;
}

/* 日期选择器下拉面板样式 */
.el-picker-panel.el-date-range-picker {
  background-color: rgba(4, 29, 53, 0.95) !important;
  border: 1px solid rgba(31, 198, 255, 0.7) !important;
  box-shadow: 0 0 15px rgba(31, 198, 255, 0.3) !important;
}

.el-date-range-picker__header {
  margin: 8px !important;
  color: #fff !important;
}

.el-date-table th {
  color: #fff !important;
  border-bottom: 1px solid rgba(31, 198, 255, 0.3) !important;
}

.el-date-table td {
  color: #fff !important;
}

.el-date-table td.in-range div {
  background-color: rgba(31, 198, 255, 0.1) !important;
}

.el-date-table td.end-date span,
.el-date-table td.start-date span {
  background-color: rgba(31, 198, 255, 0.6) !important;
  color: #fff !important;
}

.el-date-range-picker__header .el-icon-arrow-left,
.el-date-range-picker__header .el-icon-arrow-right,
.el-date-range-picker__header .el-icon-d-arrow-left,
.el-date-range-picker__header .el-icon-d-arrow-right {
  color: rgba(31, 198, 255, 0.8) !important;
}

.el-picker-panel__footer {
  background-color: rgba(4, 29, 53, 0.95) !important;
  border-top: 1px solid rgba(31, 198, 255, 0.3) !important;
}

.el-picker-panel__footer .el-button {
  color: #fff !important;
  background: transparent !important;
  border: 1px solid rgba(31, 198, 255, 0.5) !important;
}
</style>

<style scoped lang="less">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #041a21;
  overflow: hidden;

  .chart-row {
    display: flex;
    gap: 10px;
    height: 48%;
    width: 100%;
    margin-bottom: 20px;
  }

  .chart-box {
    flex: 1;
    width: calc((100% - 30px) / 4);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .chart-footer {
    margin: 0px;
    height: 61px;
    border-radius: 4px;
    opacity: 1;
    padding: 5px 8px;
    background: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(31, 198, 255, 0.3);
    width: 100%;
  }

  .chart-input {
    height: 100%;
    width: 100%;

    /deep/ .el-textarea__inner {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      height: 100%;
      padding: 0;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
        border-color: transparent !important;
      }
    }

    /deep/ .el-textarea.is-focused .el-textarea__inner {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
      border-color: transparent !important;
    }
  }

  /deep/ .el-table {
    background-color: transparent;
    width: 100% !important;
    table-layout: fixed;

    &::before {
      display: none;
    }

    th {
      background-color: rgba(31, 198, 255, 0.3);
      color: #fff;
      border-color: #1fc6ff;
    }

    td {
      background-color: transparent;
      color: #fff;
      border-color: #2e4262;
    }

    tr {
      background-color: transparent;
    }
  }

  .screen-btn {
    display: inline-block;
    min-width: 68px;
    height: 28px;
    padding: 0 5px;
    background: rgba(31, 198, 255, 0.3);
    border: 1px solid #1fc6ff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    color: #fff;

    &:hover {
      background: rgba(31, 198, 255, 0.6);
      border: 1px solid #1fc6ff;
    }
  }

  /deep/ .border-content {
    padding: 10px;
    box-sizing: border-box;
  }

  /* 日期选择器样式已移至全局样式 */

  .radio-selector {
    display: flex;
    margin: 10px;

    .el-radio {
      margin-right: 20px;
      color: #fff;

      /deep/ .el-radio__label {
        color: #fff;
        font-size: 16px;
      }

      /deep/ .el-radio__input.is-checked .el-radio__inner {
        border-color: #1fc6ff;
        background: #1fc6ff;
      }

      /deep/ .el-radio__input.is-checked + .el-radio__label {
        color: #1fc6ff;
      }

      /deep/ .el-radio__inner {
        background-color: transparent;
        border: 1px solid #fff;
      }
    }
  }

  .no-data-tip {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #fff;
    font-size: 16px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;

    &.no-data-tip-detail {
      width: 50%;
      height: 100%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .total-info {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    color: #fff;
    font-size: 16px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }
}

.screen-dialog {
  /deep/ .el-dialog {
    background: #041a21;
    border: 1px solid #1fc6ff;
  }

  /deep/ .el-dialog__body {
    padding: 10px 20px;
    position: relative;
  }

  .dialog-content {
    min-height: 200px;
    max-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .no-data-tip {
    height: 100px; // 减小高度
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 16px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    width: 100%; // 确保宽度填充
    margin: 20px 0; // 添加上下边距
  }
}

/deep/ .el-button {
  background-color: transparent;
  color: #fff;
  border: 1px solid #1fc6ff;

  &:hover {
    color: #fff;
    background-color: rgba(31, 198, 255, 0.2);
  }
}

/deep/ .custom-loading {
  .el-loading-text {
    font-size: 24px !important;
    color: #fff !important;
  }
}
</style>
