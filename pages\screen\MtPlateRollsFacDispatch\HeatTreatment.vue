<template>
  <div class="bigBox">
    <el-row :gutter="32">
      <el-col :span="24">
        <screen-border title="当月热处理">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/coilScreen/bjsc'"
              class="screen-btn"
              @click="openView(1)">
              <el-icon class="el-icon-edit-outline" />
              操作
            </span>
          </template>
          <el-table
            :data="month_heatTreatment"
            height="360">
            <el-table-column
              type="index"
              label="序号"
              width="60" />
            <el-table-column
              prop="project"
              label="项目"
              align="center" />
            <el-table-column
              prop="planYield"
              label="计划产量(吨)"
              align="center" />
            <el-table-column
              prop="accruedYield"
              label="累计产量(吨)"
              align="center" />
            <el-table-column
              prop="accruedYield"
              label="时间进度(%)"
              align="center" />
            <el-table-column
              prop="timeProgress"
              label="产量进度(%)"
              align="center" />
            <el-table-column
              prop="yieldProgress"
              label="日需均产(吨)"
              align="center" />
            <el-table-column
              prop="averageYield"
              label="月预计产量(吨)"
              align="center" />
          </el-table>
        </screen-border>
      </el-col>
      <el-col :span="24">
        <screen-border title="班次热处理">
          <template v-slot:headerRight>
            <span
              v-command="'/screen/coilScreen/bjsc'"
              class="screen-btn"
              @click="openView(2)">
              <el-icon class="el-icon-edit-outline" />
              操作
            </span>
          </template>
          <el-table
            :data="sail_heatTreatment"
            height="calc(100vh - 650px)"
            show-summary>
            <el-table-column
              type="index"
              label="序号"
              width="60" />
            <el-table-column
              prop="sail"
              label="班次"
              align="center" />
            <el-table-column
              label="1#热处理炉"
              align="center">
              <el-table-column
                prop="planTon_1"
                label="计划吨数"
                align="center" />
              <el-table-column
                prop="actualTon_1"
                label="实际吨位"
                align="center" />
              <el-table-column
                prop="remark_1"
                label="备注"
                align="center" />
            </el-table-column>
            <el-table-column
              label="2#热处理炉"
              align="center">
              <el-table-column
                prop="planTon_2"
                label="计划吨数"
                align="center" />
              <el-table-column
                prop="actualTon_2"
                label="实际吨位"
                align="center" />
              <el-table-column
                prop="remark_2"
                label="备注"
                align="center" />
            </el-table-column>
          </el-table>
        </screen-border>
      </el-col>
    </el-row>

    <!--弹框-->
    <el-dialog
      :visible.sync="dialogBox"
      :width="'80%'"
      :close-on-click-modal="false"
      class="screen-dialog"
      @close="closeDialogBox">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <!-- <span
              class="screen-btn"
              @click="addNewRow">
              <el-icon class="el-icon-edit-outline"/>
              添加行
            </span>
            <span
              class="screen-btn"
              @click="DownloadExcel">
              <el-icon class="el-icon-download"/>
              模板
            </span>
            <span
              class="screen-btn">
              <el-upload
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="importExcel"
                :file-list="fileList"
                action=""
                accept=".xls,.xlsx">
                <span>
                  <el-icon class="el-icon-upload2"/>
                  上传
                </span>
              </el-upload>
            </span> -->
            <span
              class="screen-btn"
              @click="ExportExcel">
              <el-icon class="el-icon-download" />
              下载
            </span>
            <span
              class="screen-btn"
              @click="saveData">
              <el-icon class="el-icon-printer" />
              保存
            </span>
          </div>
          {{ title }}
        </div>
      </template>
      <el-table
        v-if="title=='当月热处理'"
        id="table"
        :data="formData"
        border>
        <el-table-column
          type="index"
          label="序号"
          width="60" />
        <el-table-column
          v-for="(item,index) in Header"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          align="center">
          <template v-slot="{ row }">
            <el-input v-model="row[item.prop]" />
            <span v-show="false">{{ row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          width="100"
          label="操作">
          <template v-slot="{ row, $index }">
            <span
              class="screen-btn"
              @click="delRow($index)">
              <el-icon class="el-icon-delete" />
              删除
            </span>
          </template>
        </el-table-column>
      </el-table>

      <el-table
        v-else
        id="table"
        :data="formData"
        height="380">
        <el-table-column
          type="index"
          label="序号"
          width="60" />
        <el-table-column
          v-for="(item,index) in Header"
          :key="index"
          :label="item.label"
          align="center">
          <template v-slot="{ row }">
            <el-input v-model="row[item.prop]" />
            <span v-show="false">{{ row[item.prop] }}</span>
          </template>
          <el-table-column
            v-for="(items,indexs) in item.child"
            :key="indexs"
            :label="items.label"
            align="center">
            <template v-slot="{ row }">
              <el-input v-model="row[items.prop]" />
              <span v-show="false">{{ row[items.prop] }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          align="center"
          width="100"
          label="操作">
          <template v-slot="{ row, $index }">
            <span
              class="screen-btn"
              @click="delRow($index)">
              <el-icon class="el-icon-delete" />
              删除
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div class="text-center">
        <span
          class="screen-btn"
          @click="addNewRow()">
          <el-icon class="el-icon-circle-plus-outline" />
          增加数据
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'
import { saveAs } from 'file-saver'
import * as echarts from 'echarts'
import moment from 'moment'

import {
  MONTH_HEATTREATMENT,
  MONTH_HEATTREATMENT_SAVE,
  SAIL_HEATTREATMENT,
  SAIL_HEATTREATMENT_SAVE
} from '@/api/screen'

export default {
  name: 'HeatTreatment',
  components: {
    // SingleBarsChart,
    // SteelBarsChart,
    ScreenBorder
  },
  props: {
    selTime: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      //当月热处理
      month_heatTreatment: [
        {
          project: '当月1#炉',
          planYield: 0,
          accruedYield: 0,
          timeProgress: 0,
          yieldProgress: 0,
          averageYield: 0,
          monYield: 0
        },
        {
          project: '当月2#炉',
          planYield: 0,
          accruedYield: 0,
          timeProgress: 0,
          yieldProgress: 0,
          averageYield: 0,
          monYield: 0
        }
      ],

      //班次热处理
      sail_heatTreatment: [
        {
          sail: '大夜班',
          planTon_1: 0,
          actualTon_1: 0,
          remark_1: '',
          planTon_2: 0,
          actualTon_2: 0,
          remark_2: ''
        },
        {
          sail: '白班',
          planTon_1: 0,
          actualTon_1: 0,
          remark_1: '',
          planTon_2: 0,
          actualTon_2: 0,
          remark_2: ''
        },
        {
          selTime: '2024-12-23',
          sail: '小夜班',
          planTon_1: 0,
          actualTon_1: 0,
          remark_1: '',
          planTon_2: 0,
          actualTon_2: 0,
          remark_2: ''
        },
        {
          selTime: '2024-12-23',
          sail: '合计',
          planTon_1: 0,
          actualTon_1: 0,
          remark_1: '',
          planTon_2: 0,
          actualTon_2: 0,
          remark_2: ''
        },
        {
          selTime: '2024-12-23',
          sail: '待热处理量',
          planTon_1: 0,
          actualTon_1: 0,
          remark_1: '',
          planTon_2: 0,
          actualTon_2: 0,
          remark_2: ''
        }
      ],

      //弹框统一
      dialogBox: false,
      title: '',
      //弹框统一表字段
      Header: [],
      //弹框统一表数据
      formData: [],
      //上传Excel
      fileList: []
    }
  },

  created() {
    this.getMonth_heatTreatment()
    this.getSail_heatTreatment()
  },
  methods: {
    //获取当月热处理数据
    async getMonth_heatTreatment() {
      let res = await post(MONTH_HEATTREATMENT, {
        selTime: this.selTime
      })
      // console.log('当月热处理', res)

      if (res.data.length != 0) {
        this.month_heatTreatment = res.data
      }
    },

    //获取班次热处理数据
    async getSail_heatTreatment() {
      let res = await post(SAIL_HEATTREATMENT, {
        selTime: this.selTime
      })
      // console.log('班次热处理', res)

      if (res.data.length != 0) {
        this.sail_heatTreatment = res.data
      }
    },
    //弹框
    openView(nub) {
      this.dialogBox = true
      if (nub == 1) {
        this.title = '当月热处理'
        this.Header = [
          {
            label: '项目',
            prop: 'project'
          },
          {
            label: '计划产量(吨)',
            prop: 'planYield'
          },
          {
            label: '累计产量(吨)',
            prop: 'accruedYield'
          },
          {
            label: '时间进度(%)',
            prop: 'timeProgress'
          },
          {
            label: '产量进度(%)',
            prop: 'yieldProgress'
          },
          {
            label: '日需均产(吨)',
            prop: 'averageYield'
          },
          {
            label: '月预计产量(吨)',
            prop: 'monYield'
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.month_heatTreatment))
      } else if (nub == 2) {
        this.title = '班次热处理'
        this.Header = [
          {
            label: '班次',
            prop: 'sail'
          },
          {
            label: '1#热处理炉',
            prop: '',
            child: [
              {
                label: '计划吨数',
                prop: 'planTon_1'
              },
              {
                label: '实际吨位',
                prop: 'actualTon_1'
              },
              {
                label: '备注',
                prop: 'remark_1'
              }
            ]
          },
          {
            label: '2#热处理炉',
            prop: '',
            child: [
              {
                label: '计划吨数',
                prop: 'planTon_2'
              },
              {
                label: '实际吨位',
                prop: 'actualTon_2'
              },
              {
                label: '备注',
                prop: 'remark_2'
              }
            ]
          }
        ]
        this.formData = JSON.parse(JSON.stringify(this.sail_heatTreatment))
      }
    },

    //添加行
    addNewRow() {
      let row = {}
      this.Header.forEach(item => {
        row[item.prop] = ''
      })

      this.formData.push(row)
    },

    //删除行
    delRow(indexs) {
      this.formData.forEach((item, index) => {
        if (indexs == index) {
          this.formData.splice(index, 1)
        }
      })
    },

    //下载模板
    DownloadExcel() {
      let data = [{}]
      this.Header.forEach(item => {
        data[0][item.prop] = item.label
      })

      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:I' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `${this.title}.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },

    //导出Excel
    ExportExcel() {
      let table = document.querySelector('#table')
      const XLSX = require('xlsx')
      let wb = XLSX.utils.table_to_book(table, { raw: true })
      let wt = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // 下载保存文件
        saveAs(
          new Blob([wt], { type: 'application/octet-stream' }),
          `${this.title}.xlsx`
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.log(e, wt)
        }
      }
      return wt
    },

    //上传Excel
    importExcel(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      let header = {}
      let letter = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      this.Header.forEach((item, index) => {
        header[item.prop] = letter[index]
      })

      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, header)
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1,请检查!')
        sheet.shift()
        // 表格信息
        let list = sheet.map(item => {
          return item
        })
        // list.reverse()
        this.$message.success('解析成功！')
        //上传数据

        console.log(list)

        list.forEach(item => {
          item.selTime = this.selTime
          this.formData.push(item)
        })
      })
    },

    //保存新增数据
    async saveData() {
      let res
      if (this.title == '当月热处理') {
        res = await post(MONTH_HEATTREATMENT_SAVE, {
          selTime: this.selTime,
          data: this.formData
        })
      } else if (this.title == '班次热处理') {
        res = await post(SAIL_HEATTREATMENT_SAVE, {
          selTime: this.selTime,
          data: this.formData
        })
      }
      // console.log('保存', res)
      if (res.status == 1) {
        this.$message.success('保存成功')
        if (this.title == '当月热处理') {
          this.getMonth_heatTreatment()
        } else if (this.title == '班次热处理') {
          this.getSail_heatTreatment()
        }

        this.closeDialogBox()
      }
    },

    //关闭弹框
    closeDialogBox() {
      this.dialogBox = false
      this.title = ''
      this.Header = []
      this.formData = []
    }
  }
}
</script>

<style scoped lang="less">
.bigBox {
  .EchartsBox {
    height: 380px;

    .setRadio {
      /deep/ .el-radio {
        color: white;
      }
    }
  }

  .border-wrapper {
    margin-bottom: 15px;
  }
}

.btn {
  /deep/ .el-button {
    font-size: 15px;
    padding: 4px 15px;
    border-radius: 4px;
  }
}

/deep/ .el-table .el-table__cell {
  font-size: 19px;
}

/deep/ .el-table .class_red {
  background: #fd0000;
}

/deep/ .el-table .class_yellow {
  background: #fdfd00;
}

/deep/ .el-table .class_orange {
  background: #f99f04;
}
</style>
