/**
 * 基础 mixins
 */
import { post } from '@/lib/Util'

export default {
  data() {
    return {}
  },
  created() {},
  methods: {
    // 匹配字典值
    getDict(value, list) {
      const match = this[list].find(item => item.value === value)
      return match ? match : {}
    },
    getProductMonth(date) {
      return {
        startDate:
          this.$moment(date)
            .subtract(this.$moment(date).format('DD') >= 26 ? 0 : 1, 'month')
            .format('yyyyMM') + '26',
        endDate:
          this.$moment(date)
            .add(this.$moment(date).format('DD') >= 26 ? 1 : 0, 'month')
            .format('yyyyMM') + '25',
        curDate: this.$moment(date).format('yyyyMMDD')
      }
    }
  }
}
