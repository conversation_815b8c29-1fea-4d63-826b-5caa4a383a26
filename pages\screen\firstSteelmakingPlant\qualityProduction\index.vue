<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="10"
          class="full-height"
        >
          <screen-border title="跟班情况">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="attendant.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div
              ref="tableHeight"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="attendant.showGridData"
                :height="tableHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  property="A_LIST"
                  label="工序"
                  width="100"/>
                <el-table-column
                  property="B_LIST"
                  label="跟班人员履职情况"
                  width="160">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.B_LIST)"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  property="C_LIST"
                  label="工序异常">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.C_LIST)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="14"
          class="full-height"
        >
          <screen-border title="品种钢钢板质量">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="()=>{
                  varietySteelQuality.save = false;
                  varietySteelQuality.dialogVisible = true
                  getvarietySteelQuality()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明录入
              </span>
              <span
                class="screen-btn"
                @click="()=>{
                  varietySteelQuality.save = true;
                  varietySteelQuality.dialogVisible = true
                  getvarietySteelQuality()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明
              </span>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="varietyQuality.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="varietyQuality.showGridData"
                :height="tableHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  property="A_LIST"
                  label="项目"/>
                <el-table-column
                  label="管线钢探伤情况"
                >
                  <el-table-column
                    property="k1"
                    label="探伤不合"/>
                  <el-table-column
                    property="k2"
                    label="探伤量"/>
                  <el-table-column
                    property="k3"
                    label="探伤合格率">
                    <template v-slot="{ row }">
                      {{ percentage(row, "k3") }}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column
                  label="抗酸管线钢探伤情况"
                >
                  <el-table-column
                    property="k4"
                    label="探伤不合"/>
                  <el-table-column
                    property="k5"
                    label="探伤量"/>
                  <el-table-column
                    property="k6"
                    label="探伤合格率">
                    <template v-slot="{ row }">
                      {{ percentage(row, "k6") }}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column
                  label="镍系钢探伤情况"
                >
                  <el-table-column
                    property="k7"
                    label="探伤不合"/>
                  <el-table-column
                    property="k8"
                    label="探伤量"/>
                  <el-table-column
                    property="k9"
                    label="探伤合格率">
                    <template v-slot="{ row }">
                      {{ percentage(row, "k9") }}
                    </template>
                  </el-table-column>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="7"
          class="full-height"
        >
          <screen-border title="炼钢生产控制">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="steelmaking.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="steelmaking.showGridData"
                :height="tableHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  property="A_LIST"
                  label="炼钢生产控制"/>
                <el-table-column
                  property="B_LIST"
                  label="品种钢">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.B_LIST)"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  property="C_LIST"
                  label="普通钢">
                  <template v-slot="{ row }">
                    <div
                      slot="content"
                      v-html="formatText(row.C_LIST)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="10"
          class="full-height"
        >
          <screen-border title="CAD情况">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="()=>{
                  cadqk.save = false;
                  cadqk.dialogVisible = true
                  getCADQK()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明录入
              </span>
              <span
                class="screen-btn"
                @click="()=>{
                  cadqk.save = true;
                  cadqk.dialogVisible = true
                  getCADQK()
              }">
                <el-icon class="el-icon-edit-outline"/>
                情况说明
              </span>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="cad.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="cad.showGridData"
                :height="tableHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  property="A_LIST"
                  width="145px"
                  label="项目"/>
                <el-table-column
                  property="B_LIST"
                  width="290px"
                  label="责任单位（责任人）"/>
                <el-table-column
                  property="k1"
                  width="145px"
                  label="昨日增加量"/>
                <el-table-column
                  property="k2"
                  width="145px"
                  label="待处理总量"/>
                <el-table-column
                  property="k3"
                  width="145px"
                  label="余材锁定量"/>
                <el-table-column
                  property="k4"
                  width="145px"
                  label="预警量"/>
                <el-table-column
                  property="k5"
                  width="145px"
                  label="超警戒量"/>
                <el-table-column
                  property="k6"
                  width="145px"
                  label="昨日处理量"/>
                <el-table-column
                  property="k7"
                  width="145px"
                  label="全月锁定量"/>
                <el-table-column
                  width="145px"
                  property="D_LIST"
                  label="处置单位"/>
                <el-table-column
                  property="E_LIST"
                  width="145px"
                  label="处置负责人"/>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="7"
          class="full-height"
        >
          <screen-border title="坯料检验">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/firstSteelmakingPlant/edit'"
                class="screen-btn"
                @click="unfinished.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="unfinished.showGridData"
                :height="tableHeight"
                class="font-table center-table"
                border>
                <el-table-column
                  property="A_LIST"
                  width="145px"
                  label="坯料检验"/>
                <el-table-column
                  property="B_LIST"
                  width="145px"
                  label="缺陷"/>
                <el-table-column
                  property="C_LIST"
                  width="145px"
                  label="主要钢种"/>
                <el-table-column
                  property="D_LIST"
                  width="145px"
                  label="吨位"/>
                <el-table-column
                  property="E_LIST"
                  width="145px"
                  label="块数"/>
                <el-table-column
                  property="F_LIST"
                  width="145px"
                  label="精整修磨（吨）"/>
                <el-table-column
                  property="G_LIST"
                  width="145px"
                  label="合计（块）"/>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <!--跟班情况-->
    <el-dialog
      :visible.sync="attendant.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="跟班情况">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('attendant')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importAttendant')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreviewAttendant"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportAttendant">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveAttendant">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          跟班情况
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="attendant.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            property="A_LIST"
            label="工序"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            label="跟班人员履职情况">
            <template v-slot="{ row }">
              <el-input
                v-model="row.B_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="C_LIST"
            label="工序异常">
            <template v-slot="{ row }">
              <el-input
                v-model="row.C_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'attendant')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('attendant')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--品种钢质量-->
    <el-dialog
      :visible.sync="varietyQuality.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="品种钢钢板质量">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('varietyQuality')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importVarietyQuality')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreviewVarietyQuality"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportVarietyQuality">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveVarietyQuality">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          品种钢钢板质量
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="varietyQuality.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            property="A_LIST"
            label="项目">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            label="管线钢探伤情况"
          >
            <el-table-column
              property="k1"
              label="探伤不合">
              <template v-slot="{ row }">
                <el-input v-model="row.k1"/>
              </template>
            </el-table-column>
            <el-table-column
              property="k2"
              label="探伤量">
              <template v-slot="{ row }">
                <el-input v-model="row.k2"/>
              </template>
            </el-table-column>
            <el-table-column
              property="k3"
              label="探伤合格率">
              <template v-slot="{ row }">
                <el-input v-model="row.k3"/>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="抗酸管线钢探伤情况"
          >
            <el-table-column
              property="k4"
              label="探伤不合">
              <template v-slot="{ row }">
                <el-input v-model="row.k4"/>
              </template>
            </el-table-column>
            <el-table-column
              property="k5"
              label="探伤量">
              <template v-slot="{ row }">
                <el-input v-model="row.k5"/>
              </template>
            </el-table-column>
            <el-table-column
              property="k6"
              label="探伤合格率">
              <template v-slot="{ row }">
                <el-input v-model="row.k6"/>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="镍系钢探伤情况"
          >
            <el-table-column
              property="k7"
              label="探伤不合">
              <template v-slot="{ row }">
                <el-input v-model="row.k7"/>
              </template>
            </el-table-column>
            <el-table-column
              property="k8"
              label="探伤量">
              <template v-slot="{ row }">
                <el-input v-model="row.k8"/>
              </template>
            </el-table-column>
            <el-table-column
              property="k9"
              label="探伤合格率">
              <template v-slot="{ row }">
                <el-input v-model="row.k9"/>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'varietyQuality')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('varietyQuality')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--炼钢生产控制-->
    <el-dialog
      :visible.sync="steelmaking.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="炼钢生产控制">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('steelmaking')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importSteelmaking')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreviewSteelmaking"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportSteelmaking">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveSteelmaking">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          炼钢生产控制
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="steelmaking.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            property="A_LIST"
            label="炼钢生产控制"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            label="品种钢">
            <template v-slot="{ row }">
              <el-input
                v-model="row.B_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="C_LIST"
            label="普通钢">
            <template v-slot="{ row }">
              <el-input
                v-model="row.C_LIST"
                :rows="4"
                type="textarea"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'steelmaking')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('steelmaking')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--CAD情况-->
    <el-dialog
      :visible.sync="cad.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="CAD情况">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('cad')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importCAD')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreviewCAD"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportCAD">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveCAD">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          CAD情况
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="cad.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="120">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            property="A_LIST"
            label="项目">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            label="责任单位（责任人）">
            <template v-slot="{ row }">
              <el-input v-model="row.B_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="k1"
            label="昨日增加量">
            <template v-slot="{ row }">
              <el-input v-model="row.k1"/>
            </template>
          </el-table-column>
          <el-table-column
            property="k2"
            label="待处理总量">
            <template v-slot="{ row }">
              <el-input v-model="row.k2"/>
            </template>
          </el-table-column>
          <el-table-column
            property="k3"
            label="余材锁定量">
            <template v-slot="{ row }">
              <el-input v-model="row.k3"/>
            </template>
          </el-table-column>
          <el-table-column
            property="k4"
            label="预警量">
            <template v-slot="{ row }">
              <el-input v-model="row.k4"/>
            </template>
          </el-table-column>
          <el-table-column
            property="k5"
            label="超警戒量">
            <template v-slot="{ row }">
              <el-input v-model="row.k5"/>
            </template>
          </el-table-column>
          <el-table-column
            property="k6"
            label="昨日处理量">
            <template v-slot="{ row }">
              <el-input v-model="row.k6"/>
            </template>
          </el-table-column>
          <el-table-column
            property="k7"
            label="全月锁定量">
            <template v-slot="{ row }">
              <el-input v-model="row.k7"/>
            </template>
          </el-table-column>
          <el-table-column
            property="D_LIST"
            label="处置单位">
            <template v-slot="{ row }">
              <el-input v-model="row.D_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="E_LIST"
            label="处置负责人">
            <template v-slot="{ row }">
              <el-input v-model="row.E_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'cad')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('cad')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--坯料钢种-->
    <el-dialog
      :visible.sync="unfinished.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="坯料检验">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="clearGridData('unfinished')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importUnfinishedData')">
                <el-upload
                  ref="upload"
                  :show-file-list="false"
                  :on-change="handlePreviewUnfinishedData"
                  :auto-upload="false"
                  :action="''"
                  style="display: inline-block">
                  <span
                    class="screen-btn">
                    <el-icon class="el-icon-edit-outline"/>
                    EXCEL导入
                  </span>
                </el-upload>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="other"
                    icon="el-icon-copy">
                    从其他日期导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              class="screen-btn"
              @click="exportUnfinishedData">
              导出
            </span>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveUnfinished">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          坯料检验
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="unfinished.gridData"
          border>
          <el-table-column
            property="SORT_NUM"
            label="序号"
            width="80">
            <template v-slot="{ row }">
              <el-input v-model="row.SORT_NUM"/>
            </template>
          </el-table-column>
          <el-table-column
            property="A_LIST"
            label="坯料检验">
            <template v-slot="{ row }">
              <el-input v-model="row.A_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            label="缺陷">
            <template v-slot="{ row }">
              <el-input v-model="row.B_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="C_LIST"
            label="主要钢种">
            <template v-slot="{ row }">
              <el-input v-model="row.C_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="D_LIST"
            label="吨位">
            <template v-slot="{ row }">
              <el-input v-model="row.D_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="E_LIST"
            label="块数">
            <template v-slot="{ row }">
              <el-input v-model="row.E_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="F_LIST"
            label="精整修磨（吨）">
            <template v-slot="{ row }">
              <el-input v-model="row.F_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="G_LIST"
            label="合计（块）">
            <template v-slot="{ row }">
              <el-input v-model="row.G_LIST"/>
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEditQuality"
                class="screen-btn"
                @click="delGridData($index, 'unfinished')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEditQuality"
          class="screen-btn"
          @click="addGridData('unfinished')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
    <!--品种钢备注-->
    <el-dialog
      :visible.sync="varietySteelQuality.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="品种钢情况说明">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality&&!varietySteelQuality.save"
              class="screen-btn"
              @click="savevarietySteelQuality">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          品种钢情况说明
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="varietySteelQuality.gridData"
          class="font-table"
          border>
          <el-table-column
            property="A_LIST"
            label="内容">
            <template
              v-slot="{ row }"
              v-if="!varietySteelQuality.save">
              <el-input
                v-model="row.A_LIST"
                :rows="4"
                type="textarea"/>
            </template>
            <template
              v-slot="{ row }"
              v-else>
              <div
                slot="content"
                v-html="formatText(row.A_LIST)"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            width="210px"
            label="图片说明">
            <template v-slot="{ row, $index }">
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.B_LIST"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item.id"
                    :id="item.id"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !varietySteelQuality.save"
                    @img-delete="handlePasteImgDeleteID($event, index, 'varietySteelQuality')"
                  />
                </li>
              </ul>
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.showPic == unfinished ? [] : row.showPic"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item"
                    :src="item"
                    :is-id="false"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !varietySteelQuality.save"
                    @img-delete="handlePasteImgDelete($event, index, 'varietySteelQuality')"
                  />
                </li>
              </ul>
              <el-upload
                v-if="!varietySteelQuality.save"
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file)=>{
                  handleChange(file, 'varietySteelQuality', $index)
                }"
                :show-file-list="false"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = $index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <!--cad备注-->
    <el-dialog
      :visible.sync="cadqk.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="CAD情况说明">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <span
              v-if="canEditQuality&&!cadqk.save"
              class="screen-btn"
              @click="saveCADQK">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          CAD情况说明
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="cadqk.gridData"
          class="font-table"
          border>
          <el-table-column
            property="A_LIST"
            label="内容">
            <template
              v-slot="{ row }"
              v-if="!cadqk.save">
              <el-input
                v-model="row.A_LIST"
                :rows="4"
                type="textarea"/>
            </template>
            <template
              v-slot="{ row }"
              v-else>
              <div
                slot="content"
                v-html="formatText(row.A_LIST)"/>
            </template>
          </el-table-column>
          <el-table-column
            property="B_LIST"
            width="210px"
            label="图片说明">
            <template v-slot="{ row, $index }">
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.B_LIST"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item.id"
                    :id="item.id"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !cadqk.save"
                    @img-delete="handlePasteImgDeleteID($event, index, 'cadqk')"
                  />
                </li>
              </ul>
              <ul class="el-upload-list el-upload-list--picture-card">
                <li
                  v-for="(item, index) in row.showPic == unfinished ? [] : row.showPic"
                  :key="index"
                  class="el-upload-list__item is-ready">
                  <img-view
                    :key="item"
                    :src="item"
                    :is-id="false"
                    :img-width="'95%'"
                    :deleteable="canEditQuality && !cadqk.save"
                    @img-delete="handlePasteImgDelete($event, index, 'cadqk')"
                  />
                </li>
              </ul>
              <el-upload
                v-if="!cadqk.save"
                ref="upload"
                :auto-upload="false"
                :http-request="httpRequest"
                :on-change="(file)=>{
                  handleChange(file, 'cadqk', $index)
                }"
                :show-file-list="false"
                multiple
                list-type="picture-card"
                action="#"
                style="display: inline"
                @click.native="editIndex = $index">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import { firstMorningMeeting } from '@/api/screen'
import lodash from 'lodash'
import { deleteFileByIds, uploadFile } from '@/api/system'
import ImgView from '@/components/ImgView.vue'

export default {
  name: 'qualityProduction',
  components: { ImgView, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      tableHeight: null,
      editIndex: 0,
      unfinished: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      attendant: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      varietyQuality: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      steelmaking: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      cad: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false
      },
      // 品种钢质量
      varietySteelQuality: {
        gridData: [],
        save: true,
        dialogVisible: false
      },
      // CADQK
      cadqk: {
        gridData: [],
        save: true,
        dialogVisible: false
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getUnfinished({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '坯料检验'
      })
      this.getAttendant({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '跟班情况'
      })
      this.getVarietyQuality({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '品种钢质量'
      })
      this.getSteelmaking({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '炼钢生产控制'
      })
      this.getCAD({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'CAD'
      })
      this.getCADQK()
      this.getvarietySteelQuality()
    }
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.tableHeight = this.$refs.tableHeight.offsetHeight
  },
  methods: {
    // 跟班
    getAttendant(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.attendant.gridData = _.cloneDeep(resData)
        this.attendant.showGridData = _.cloneDeep(resData)
      })
    },
    saveAttendant() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '跟班情况',
        data: _.map(
          _.sortBy(this.attendant.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              FLAG: '跟班情况',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              SORT_NUM: index + 1
            }
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.attendant.dialogVisible = false
        this.getAttendant({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '跟班情况'
        })
        this.loading = false
      })
    },
    importAttendant(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: '跟班情况'
      }).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.attendant.gridData = _.cloneDeep(resData)
        this.$message.success('导入成功！')
      })
    },
    exportAttendant() {
      const data = [
        {
          SORT_NUM: '序号',
          A_LIST: '工序',
          B_LIST: '跟班人员履职情况',
          C_LIST: '工序异常'
        }
      ].concat(
        _.map(_.cloneDeep(this.attendant.gridData), item => {
          let datas = {}
          _.forEach(['SORT_NUM', 'A_LIST', 'B_LIST', 'C_LIST'], items => {
            datas[items] = item[items]
          })
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `质量生产-跟班情况（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    handlePreviewAttendant(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          B_LIST: 'D',
          C_LIST: 'E'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.attendant.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    // 品种钢
    getVarietyQuality(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        let resData2 = _.map(resData, item => {
          return {
            ...item,
            ...JSON.parse(item.B_LIST)
          }
        })
        this.varietyQuality.gridData = _.cloneDeep(resData2)
        this.varietyQuality.showGridData = _.cloneDeep(resData2)
      })
    },
    saveVarietyQuality() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '品种钢质量',
        data: _.map(
          _.sortBy(this.varietyQuality.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              FLAG: '品种钢质量',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              SORT_NUM: index + 1,
              B_LIST: JSON.stringify({
                k1: item.k1,
                k2: item.k2,
                k3: item.k3,
                k4: item.k4,
                k5: item.k5,
                k6: item.k6,
                k7: item.k7,
                k8: item.k8,
                k9: item.k9
              })
            }
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.varietyQuality.dialogVisible = false
        this.getVarietyQuality({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '品种钢质量'
        })
        this.loading = false
      })
    },
    importVarietyQuality(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: '品种钢质量'
      }).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.varietyQuality.gridData = _.cloneDeep(resData)
        this.$message.success('导入成功！')
      })
    },
    exportVarietyQuality() {
      const data = [
        {
          SORT_NUM: '序号',
          A_LIST: '项目',
          k1: '管线钢探伤不和',
          k2: '管线钢探伤量',
          k3: '管线钢钢探伤合格率',
          k4: '抗酸管线钢探伤不和',
          k5: '抗酸管线钢探伤量',
          k6: '抗酸管线钢探伤合格率',
          k7: '镍系钢探伤不和',
          k8: '镍系钢探伤量',
          k9: '镍系钢探伤合格率'
        }
      ].concat(
        _.map(_.cloneDeep(this.varietyQuality.gridData), item => {
          let datas = {}
          _.forEach(
            [
              'SORT_NUM',
              'A_LIST',
              'k1',
              'k2',
              'k3',
              'k4',
              'k5',
              'k6',
              'k7',
              'k8',
              'k9'
            ],
            items => {
              datas[items] = item[items]
            }
          )
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `品种钢（${this.cDate}晨会）.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },
    handlePreviewVarietyQuality(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          k1: 'D',
          k2: 'E',
          k3: 'F',
          k4: 'G',
          k5: 'H',
          k6: 'I',
          k7: 'J',
          k8: 'K',
          k9: 'L'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.varietyQuality.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    // 品种钢备注
    async savevarietySteelQuality() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 'varietySteelQuality',
        data: _.map(
          _.sortBy(this.varietySteelQuality.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              FLAG: 'varietySteelQuality',
              SORT_NUM: index + 1
            }
          }
        )
      }
      let del = null
      if (
        this.varietySteelQuality.gridData[0].delImage &&
        this.varietySteelQuality.gridData[0].delImage.length
      ) {
        del = await post(deleteFileByIds, {
          ids: this.varietySteelQuality.gridData[0].delImage
        })
      }
      if (del == null || del.success) {
        if (
          this.varietySteelQuality.gridData[0].file &&
          this.varietySteelQuality.gridData[0].file.length
        ) {
          const formData = new FormData()
          this.varietySteelQuality.gridData[0].file.forEach(item => {
            formData.append('files', item)
          })
          post(uploadFile, formData, false, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.success) {
              params.data[0].B_LIST = JSON.stringify([
                ...params.data[0].B_LIST,
                ...res.data
              ])

              post(firstMorningMeeting.furnaceCondition, params).then(res => {
                this.getvarietySteelQuality()
                this.$message.success('保存成功')
                this.loading = false
              })
            } else {
              this.$message.warning('图片上传失败！')
              this.loading = false
            }
          })
        } else {
          params.data[0].B_LIST = JSON.stringify(params.data[0].B_LIST)
          post(firstMorningMeeting.furnaceCondition, params).then(res => {
            this.getvarietySteelQuality()
            this.$message.success('保存成功')
            this.loading = false
          })
        }
      } else {
        this.$message.error('图片删除失败！！！')
      }
    },
    getvarietySteelQuality() {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'varietySteelQuality'
      }).then(res => {
        this.varietySteelQuality.gridData = _.cloneDeep(
          _.map(res.data.length ? res.data : [{}], item => {
            return {
              ...item,
              B_LIST: item.B_LIST ? JSON.parse(item.B_LIST) : []
            }
          })
        )
      })
    },
    // 炼钢
    getSteelmaking(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.steelmaking.gridData = _.cloneDeep(resData)
        this.steelmaking.showGridData = _.cloneDeep(resData)
      })
    },
    saveSteelmaking() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '炼钢生产控制',
        data: _.map(
          _.sortBy(this.steelmaking.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              FLAG: '炼钢生产控制',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              SORT_NUM: index + 1
            }
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.steelmaking.dialogVisible = false
        this.getSteelmaking({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '炼钢生产控制'
        })
        this.loading = false
      })
    },
    importSteelmaking(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: '炼钢生产控制'
      }).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.steelmaking.gridData = _.cloneDeep(resData)
        this.$message.success('导入成功！')
      })
    },
    exportSteelmaking() {
      const data = [
        {
          SORT_NUM: '序号',
          A_LIST: '炼钢生产控制',
          B_LIST: '品种钢',
          C_LIST: '普通钢'
        }
      ].concat(
        _.map(_.cloneDeep(this.steelmaking.gridData), item => {
          let datas = {}
          _.forEach(['SORT_NUM', 'A_LIST', 'B_LIST', 'C_LIST'], items => {
            datas[items] = item[items]
          })
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `质量生产-炼钢生产控制（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    handlePreviewSteelmaking(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          B_LIST: 'D',
          C_LIST: 'E'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.steelmaking.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    // cad
    getCAD(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        let resData2 = _.map(resData, item => {
          return {
            ...item,
            ...JSON.parse(item.C_LIST)
          }
        })
        this.cad.gridData = _.cloneDeep(resData2)
        this.cad.showGridData = _.cloneDeep(resData2)
      })
    },
    saveCAD() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 'CAD',
        data: _.map(
          _.sortBy(this.cad.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              FLAG: 'CAD',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              SORT_NUM: index + 1,
              C_LIST: JSON.stringify({
                k1: item.k1,
                k2: item.k2,
                k3: item.k3,
                k4: item.k4,
                k5: item.k5,
                k6: item.k6,
                k7: item.k7,
                k8: item.k8,
                k9: item.k9
              })
            }
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.cad.dialogVisible = false
        this.getCAD({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: 'CAD'
        })
        this.loading = false
      })
    },
    importCAD(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: 'CAD'
      }).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.cad.gridData = _.cloneDeep(resData)
        this.$message.success('导入成功！')
      })
    },
    exportCAD() {
      const data = [
        {
          SORT_NUM: '序号',
          A_LIST: '项目',
          B_LIST: '责任单位（责任人）',
          k1: '昨日增加量',
          k2: '待处理总量',
          k3: '余材锁定量',
          k4: '预警量',
          k5: '超警戒量',
          k6: '昨日处理量',
          k7: '全月锁定量',
          D_LIST: '处置单位',
          E_LIST: '处置负责人'
        }
      ].concat(
        _.map(_.cloneDeep(this.cad.gridData), item => {
          let datas = {}
          _.forEach(
            [
              'SORT_NUM',
              'A_LIST',
              'B_LIST',
              'k1',
              'k2',
              'k3',
              'k4',
              'k5',
              'k6',
              'k7',
              'D_LIST',
              'E_LIST'
            ],
            items => {
              datas[items] = item[items]
            }
          )
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `CAD情况（${this.cDate}晨会）.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {}
        }
      })
    },
    handlePreviewCAD(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          B_LIST: 'D',
          k1: 'E',
          k2: 'F',
          k3: 'G',
          k4: 'H',
          k5: 'I',
          k6: 'J',
          k7: 'K',
          D_LIST: 'L',
          E_LIST: 'M'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.cad.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    // cad备注
    async saveCADQK() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: 'cadqk',
        data: _.map(
          _.sortBy(this.cadqk.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              FLAG: 'cadqk',
              SORT_NUM: index + 1
            }
          }
        )
      }
      let del = null
      if (
        this.cadqk.gridData[0].delImage &&
        this.cadqk.gridData[0].delImage.length
      ) {
        del = await post(deleteFileByIds, {
          ids: this.cadqk.gridData[0].delImage
        })
      }
      if (del == null || del.success) {
        if (this.cadqk.gridData[0].file && this.cadqk.gridData[0].file.length) {
          const formData = new FormData()
          this.cadqk.gridData[0].file.forEach(item => {
            formData.append('files', item)
          })
          post(uploadFile, formData, false, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.success) {
              params.data[0].B_LIST = JSON.stringify([
                ...params.data[0].B_LIST,
                ...res.data
              ])

              post(firstMorningMeeting.furnaceCondition, params).then(res => {
                this.getCADQK()
                this.$message.success('保存成功')
                this.loading = false
              })
            } else {
              this.$message.warning('图片上传失败！')
              this.loading = false
            }
          })
        } else {
          params.data[0].B_LIST = JSON.stringify(params.data[0].B_LIST)
          post(firstMorningMeeting.furnaceCondition, params).then(res => {
            this.getCADQK()
            this.$message.success('保存成功')
            this.loading = false
          })
        }
      } else {
        this.$message.error('图片删除失败！！！')
      }
    },
    getCADQK() {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: 'cadqk'
      }).then(res => {
        this.cadqk.gridData = _.cloneDeep(
          lodash.map(res.data.length ? res.data : [{}], item => {
            return {
              ...item,
              B_LIST: item.B_LIST ? JSON.parse(item.B_LIST) : []
            }
          })
        )
      })
    },
    // 坯料检验
    getUnfinished(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.unfinished.gridData = _.cloneDeep(resData)
        this.unfinished.showGridData = _.cloneDeep(resData)
      })
    },
    saveUnfinished() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '坯料检验',
        data: _.map(
          _.sortBy(this.unfinished.gridData, item => item.SORT_NUM),
          (item, index) => {
            return {
              ...item,
              FLAG: '坯料检验',
              PROD_DATE: this.$moment(this.cDate).format('yyyyMMDD'),
              SORT_NUM: index + 1
            }
          }
        )
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.unfinished.dialogVisible = false
        this.getUnfinished({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '坯料检验'
        })
        this.loading = false
      })
    },
    importUnfinishedData(date) {
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: '坯料检验'
      }).then(res => {
        let resData = _.sortBy(res.data, item => item.SORT_NUM)
        this.unfinished.gridData = _.cloneDeep(resData)
        this.$message.success('导入成功！')
      })
    },
    exportUnfinishedData() {
      const data = [
        {
          SORT_NUM: '序号',
          A_LIST: '坯料检验',
          B_LIST: '缺陷',
          C_LIST: '主要钢种',
          D_LIST: '吨位',
          E_LIST: '块数',
          F_LIST: '精整修磨',
          G_LIST: '合计'
        }
      ].concat(
        _.map(_.cloneDeep(this.unfinished.gridData), item => {
          let datas = {}
          _.forEach(
            [
              'SORT_NUM',
              'A_LIST',
              'B_LIST',
              'C_LIST',
              'D_LIST',
              'E_LIST',
              'F_LIST',
              'G_LIST'
            ],
            items => {
              datas[items] = item[items]
            }
          )
          return datas
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `坯料钢种（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {}
          }
        }
      )
    },
    handlePreviewUnfinishedData(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        data = LAY_EXCEL.filterImportData(data, {
          SORT_NUM: 'B',
          A_LIST: 'C',
          B_LIST: 'D',
          C_LIST: 'E',
          D_LIST: 'F',
          E_LIST: 'G',
          F_LIST: 'H',
          G_LIST: 'I'
        })
        // 去除第一行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.unfinished.gridData = sheet
          .filter(item => item.setDate !== '晨会日期')
          .map(item => {
            item.setDate =
              typeof item.setDate === 'number'
                ? LAY_EXCEL.dateCodeFormat(item.setDate, 'MM月DD日')
                : item.setDate
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    httpRequest(params) {},
    handleChange(file, row, index) {
      if (this[row].gridData[index].file == undefined) {
        this[row].gridData[index].file = [file.raw]
      } else {
        this[row].gridData[index].file.push(file.raw)
      }
      if (this[row].gridData[index].showPic == undefined) {
        this[row].gridData[index].showPic = [file.url]
      } else {
        this[row].gridData[index].showPic.push(file.url)
      }
      this[row] = _.cloneDeep(this[row])
    },
    handlePasteImgDelete(file, index, row) {
      this[row].gridData[0].file.splice(index, 1)
      this[row].gridData[0].showPic.splice(index, 1)
      this[row] = _.cloneDeep(this[row])
    },
    handlePasteImgDeleteID(file, index, row) {
      if (this[row].gridData[0].delImage == undefined) {
        this[row].gridData[0].delImage = [file.id]
      } else {
        this[row].gridData[0].delImage.push(file.id)
      }
      this[row].gridData[0].B_LIST.splice(index, 1)
      this[row] = _.cloneDeep(this[row])
    },
    //转%
    percentage(row, name) {
      return row[name] != undefined && row[name] != null
        ? row[name].toString().includes('%')
          ? row[name]
          : (row[name] * 100).toFixed(2) + '%'
        : row[name]
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
