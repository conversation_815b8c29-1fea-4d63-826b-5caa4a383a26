<template>
  <div class="content">
    <div class="content-item top">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="12"
          class="full-height">
          <screen-border title="故障汇总-责任单位">
            <bars-chart
              :bar-width="30"
              :unit="'分钟'"
              :show-label="true"
              :color="[
                '#2772f0',
                '#f5b544',
                '#51df81',
                '#edf173',
                '#e91e63',
                '#00bcd4',
                '#2196f3',
                '#3f51b5',
                '#9c27b0',
                '#009688',
              ]"
              :chart-data="spots.bar1"
              :x-data="spots.barX1"
              @selected="FaultBreakdownFind1($event)"/>
          </screen-border>
        </el-col>
        <el-col
          :span="12"
          class="full-height">
          <!-- <custom-table
            :title="'故障汇总-类型'"
            :setting="tableObj3.setting"
            :url-list="tableObj3.url.list"
            :url-save="tableObj3.url.save"
            :select-date="selectDate"
            :table-class="'big-table'"/> -->
          <screen-border title="故障汇总-类型">
            <bars-chart
              :bar-width="30"
              :show-label="true"
              :unit="'分钟'"
              :color="[
                '#2772f0',
                '#f5b544',
                '#51df81',
                '#edf173',
                '#e91e63',
                '#00bcd4',
                '#2196f3',
                '#3f51b5',
                '#9c27b0',
                '#009688',
              ]"
              :chart-data="spots1.bar1"
              :x-data="spots1.barX1"
              @selected="FaultBreakdownFind2($event)"/>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item">
      <custom-table2
        :title="'故障明细表'"
        :setting="tableObj2.setting"
        :table-row-class="tableObj2.rowClass"
        :url-list="tableObj2.url.list"
        :url-save="tableObj2.url.save"
        :get-chart-data="chartData"
        :select-date="selectDate"
        :table-class="'big-table'"/>
    </div>
  </div>
</template>

<script>
import SingleBarsChart from '@/pages/screen/C2Meeting/component/single-bars-chart'
import {
  qmsQualitySupervisionQuery,
  qmsQualitySupervisionSave
} from '@/api/screen'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import BarsChart from '@/pages/screen/B1ProductionMeeting/component/bars-chart'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import CustomTable from '@/pages/screen/B1ProductionMeeting/component/custom-table'
import CustomTable2 from '@/pages/screen/B1ProductionMeeting/component/custom-table2'
import {
  fireCuttingFind,
  fireCuttingSave,
  rollingProductionFind,
  rollingProductionSave
} from '@/api/screenC2'
import {
  FaultBreakdownFind,
  FaultBreakdownFind1,
  FaultBreakdownFind2,
  FaultBreakdownSave,
  emergencyShutdownFind,
  emergencyShutdownSave,
  faultStopBreakdownFind,
  faultStopBreakdownSave
} from '@/api/screenB1Production'
export default {
  name: 'accident',
  components: {
    CustomTable,
    CustomTable2,
    SingleBarsChart,
    ScreenBorder,
    BarsChart
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      tableObj2: {
        rowClass: ({ row }) => {
          if (!row.impactTime) return ''
          if (!row.impactTime >= 800) {
            return 'red'
          } else if (!row.impactTime >= 600) {
            return 'yellow'
          } else if (!row.impactTime >= 200) {
            return 'blue'
          }
          return ''
        },
        url: {
          save: FaultBreakdownSave,
          list: FaultBreakdownFind
        },
        setting: [
          {
            keyQuery: 'index',
            keySave: 'index',
            label: '序号',
            type: 'index'
          },
          // {
          //   keyQuery: 'category',
          //   keySave: 'category',
          //   label: '类别'
          // },
          {
            keyQuery: 'setTime',
            keySave: 'setTime',
            label: '日期'
          },
          {
            keyQuery: 'classes',
            keySave: 'classes',
            label: '班次'
          },
          {
            keyQuery: 'faultDes',
            keySave: 'faultDes',
            label: '故障描述'
          },
          {
            keyQuery: 'impactTime',
            keySave: 'impactTime',
            label: '影响时间(min)'
          },
          {
            keyQuery: 'impactNum',
            keySave: 'impactNum',
            label: '影响炉数'
          },
          {
            keyQuery: 'type',
            keySave: 'type',
            label: '类型'
          },
          {
            keyQuery: 'unit',
            keySave: 'unit',
            label: '责任单位'
          },
          {
            keyQuery: 'noplanStop',
            keySave: 'noplanStop',
            label: '非计划停机'
          }
        ]
      },
      spots: {
        bar1: [
          {
            name: '影响时间',
            data: [],
            barGap: '0.5'
          },
          {
            name: '欠产炉数',
            data: [],
            barGap: '0.5'
          }
        ],
        barX1: [
          '炼钢车间',
          '精炼车间',
          '连铸车间',
          '运行车间',
          '原料车间',
          '坯料车间',
          '外部',
          '合计'
        ]
      },
      spots1: {
        bar1: [
          {
            name: '影响时间',
            data: [],
            barGap: '0.5'
          },
          {
            name: '欠产炉数',
            data: [],
            barGap: '0.5'
          }
        ],
        barX1: ['外部', '设备', '操作', '电气', '机械', '合计']
      }
    }
  },
  computed: {
    selectedMonth: function() {
      return this.$moment(this.selectDate).format('YYYY-MM')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.FaultBreakdownFind1()
      this.FaultBreakdownFind2()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  methods: {
    chartData() {
      this.FaultBreakdownFind1()
      this.FaultBreakdownFind2()
    },
    FaultBreakdownFind1() {
      post(FaultBreakdownFind1, {
        setDate: this.cDate
      }).then(res => {
        this.spots.bar1[0].data = this.spots.barX1.map(item => {
          for (let i = 0; i < res.data.length; i++) {
            if (res.data[i].unit == item) {
              return res.data[i].impactTime
            }
          }
        })
        this.spots.bar1[1].data = this.spots.barX1.map(item => {
          for (let i = 0; i < res.data.length; i++) {
            if (res.data[i].unit == item) {
              return res.data[i].impactNum
            }
          }
        })
      })
    },
    FaultBreakdownFind2() {
      post(FaultBreakdownFind2, {
        setDate: this.cDate
      }).then(res => {
        this.spots1.bar1[0].data = this.spots1.barX1.map(item => {
          for (let i = 0; i < res.data.length; i++) {
            if (res.data[i].type == item) {
              return res.data[i].impactTime
            }
          }
        })
        console.log('aaa', this.spots1.bar1[0].data)
        this.spots1.bar1[1].data = this.spots1.barX1.map(item => {
          for (let i = 0; i < res.data.length; i++) {
            if (res.data[i].type == item) {
              return res.data[i].impactNum
            }
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
}
.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
