<template>
  <div class="content">
    <div class="content-item">
      <screen-border title="质量设备">
        <template v-slot:headerRight>
          <span
            v-command="'/screen/firstSteelmakingPlant/edit'"
            class="screen-btn"
            @click="unfinished.dialogVisible = true"
          >
            <el-icon class="el-icon-edit-outline" />
            操作
          </span>
        </template>
        <div 
          ref="table1" 
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            :data="unfinished.showGridData"
            :max-height="unfinished.maxHeight"
            :header-cell-style="
              ({ row }) => {
                if (row.length != 1) {
                  return 'display: none'
                }
              }
            "
            :span-method="spanMethod"
            class="font-table center-table"
            border
          >
            <el-table-column :label="'质量设备情况'">
              <el-table-column property="A_LIST" />
              <el-table-column property="B_LIST">
                <template v-slot="{ row }">
                  <template v-if="row.INPUT[1]">
                    {{ row.B_LIST }}
                  </template>
                  <template v-else>
                    {{ row.B_LIST }}
                  </template>
                </template>
              </el-table-column>
              <el-table-column property="C_LIST">
                <template v-slot="{ row }">
                  <template v-if="row.INPUT[2]">
                    {{ row.C_LIST }}
                  </template>
                  <template v-else>
                    {{ row.C_LIST }}
                  </template>
                </template>
              </el-table-column>
              <el-table-column property="D_LIST">
                <template v-slot="{ row }">
                  <template v-if="row.INPUT[3]">
                    <template v-if="row.A_LIST == '连铸工序' && row.D_LIST!=undefined && !(row.D_LIST.includes('正常')||row.D_LIST.includes('/'))">
                      <div style="color: red;">
                        {{ row.D_LIST }}
                      </div>
                    </template>
                    <template v-else>
                      <div>
                        {{ row.D_LIST }}
                      </div>
                    </template>
                  </template>
                  <template v-else>
                    {{ row.D_LIST }}
                  </template>
                </template>
              </el-table-column>
              <el-table-column property="E_LIST">
                <template v-slot="{ row }">
                  <template v-if="row.INPUT[4]">
                    <template v-if="row.A_LIST == '精炼工序' && row.B_LIST == 'RH' && row.E_LIST > 70">
                      <div 
                        v-if="!row.C_LIST.includes('3#RH-')" 
                        style="color: red;">
                        {{ row.E_LIST }}
                      </div>
                      <div 
                        v-else-if="row.E_LIST > 90" 
                        style="color: red;">
                        {{ row.E_LIST }}
                      </div>
                      <div v-else>
                        {{ row.E_LIST }}
                      </div>
                    </template>
                    <template v-else-if="row.A_LIST == '连铸工序' && row.E_LIST!=undefined && !(row.E_LIST.includes('正常')||row.E_LIST.includes('/'))">
                      <div style="color: red;">
                        {{ row.E_LIST }}
                      </div>
                    </template>
                    <template v-else>
                      <div>
                        {{ row.E_LIST }}
                      </div>
                    </template>
                  </template>
                  <template v-else>
                    {{ row.E_LIST }}
                  </template>
                </template>
              </el-table-column>
              <el-table-column property="F_LIST">
                <template v-slot="{ row }">
                  <template v-if="row.INPUT[5]">
                    <template v-if="row.A_LIST == '精炼工序' && row.B_LIST == 'RH' && row.F_LIST > 70">
                      <div 
                        v-if="!row.C_LIST.includes('3#RH-')" 
                        style="color: red;">
                        {{ row.F_LIST }}
                      </div>
                      <div 
                        v-else-if="row.F_LIST > 90" 
                        style="color: red;">
                        {{ row.F_LIST }}
                      </div>
                      <div v-else>
                        {{ row.F_LIST }}
                      </div>
                    </template>
                    <template v-else-if="row.A_LIST == '连铸工序' && row.F_LIST!=undefined && !(row.F_LIST.includes('正常')||row.F_LIST.includes('/'))">
                      <div style="color: red;">
                        {{ row.F_LIST }}
                      </div>
                    </template>
                    <template v-else>
                      <div>
                        {{ row.F_LIST }}
                      </div>
                    </template>
                  </template>
                  <template v-else>
                    {{ row.F_LIST }}
                  </template>
                </template>
              </el-table-column>
              <el-table-column property="G_LIST">
                <template v-slot="{ row }">
                  <template v-if="row.INPUT[6]">
                    <template v-if="row.A_LIST == '精炼工序' && row.B_LIST == 'RH' && row.G_LIST > 70">
                      <div 
                        v-if="!row.C_LIST.includes('3#RH-')" 
                        style="color: red;">
                        {{ row.G_LIST }}
                      </div>
                      <div 
                        v-else-if="row.G_LIST > 90" 
                        style="color: red;">
                        {{ row.G_LIST }}
                      </div>
                      <div v-else>
                        {{ row.G_LIST }}
                      </div>
                    </template>
                    <template v-else-if="row.A_LIST == '连铸工序' && row.G_LIST!=undefined && !(row.G_LIST.includes('正常')||row.G_LIST.includes('/'))">
                      <div style="color: red;">
                        {{ row.G_LIST }}
                      </div>
                    </template>
                    <template v-else>
                      <div>
                        {{ row.G_LIST }}
                      </div>
                    </template>
                  </template>
                  <template v-else>
                    {{ row.G_LIST }}
                  </template>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
      </screen-border>
    </div>
    <!--热处理详情-->
    <el-dialog
      :visible.sync="unfinished.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="质量设备"
    >
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"
            />
            <template v-if="canEditQuality">
              <el-dropdown
                @command="
                  handleProcessedCommand($event, 'importUnfinishedData')
                "
              >
                <span
                  command="yesterday"
                  class="screen-btn"
                  @click="
                    handleProcessedCommand($event, 'importUnfinishedData')
                  "
                >
                  <el-icon class="el-icon-edit-outline" />
                  日期导入
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item 
                    command="yesterday" 
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveUnfinished"
            >
              <el-icon class="el-icon-document-checked" />
              保存
            </span>
          </div>
          质量设备
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="unfinished.gridData"
          :header-cell-style="
            ({ row }) => {
              if (row.length != 1) {
                return 'display: none'
              }
            }
          "
          :span-method="spanMethod"
          border
        >
          <el-table-column :label="'质量设备情况'">
            <el-table-column property="A_LIST" />
            <el-table-column property="B_LIST">
              <template v-slot="{ row }">
                <template v-if="row.INPUT[1]">
                  <el-input v-model="row.B_LIST" />
                </template>
                <template v-else>
                  {{ row.B_LIST }}
                </template>
              </template>
            </el-table-column>
            <el-table-column property="C_LIST">
              <template v-slot="{ row }">
                <template v-if="row.INPUT[2]">
                  <el-input v-model="row.C_LIST" />
                </template>
                <template v-else>
                  {{ row.C_LIST }}
                </template>
              </template>
            </el-table-column>
            <el-table-column property="D_LIST">
              <template v-slot="{ row }">
                <template v-if="row.INPUT[3]">
                  <el-input v-model="row.D_LIST" />
                </template>
                <template v-else>
                  {{ row.D_LIST }}
                </template>
              </template>
            </el-table-column>
            <el-table-column property="E_LIST">
              <template v-slot="{ row }">
                <template v-if="row.INPUT[4]">
                  <el-input v-model="row.E_LIST" />
                </template>
                <template v-else>
                  {{ row.E_LIST }}
                </template>
              </template>
            </el-table-column>
            <el-table-column property="F_LIST">
              <template v-slot="{ row }">
                <template v-if="row.INPUT[5]">
                  <el-input v-model="row.F_LIST" />
                </template>
                <template v-else>
                  {{ row.F_LIST }}
                </template>
              </template>
            </el-table-column>
            <el-table-column property="G_LIST">
              <template v-slot="{ row }">
                <template v-if="row.INPUT[6]">
                  <el-input v-model="row.G_LIST" />
                </template>
                <template v-else>
                  {{ row.G_LIST }}
                </template>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择"
    >
      <template v-slot:title>
        <div class="custom-dialog-title">导入日期选择</div>
      </template>
      <el-form 
        label-width="120px" 
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"
          />
        </el-form-item>
        <div class="text-center">
          <el-button 
            type="primary" 
            @click="importHistoryData()"
          >确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import lodash from 'lodash'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import { firstMorningMeeting } from '@/api/screen'

export default {
  name: 'qualityEquipment',
  components: { ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      unfinished: {
        initializedData: [0, 2, 4, 11],
        initializedData2: [
          {
            A_LIST: '转炉工序',
            B_LIST: '脱硫',
            C_LIST: '脱硫',
            D_LIST: '1#脱硫',
            E_LIST: '1#脱硫',
            F_LIST: '2#脱硫',
            G_LIST: '2#脱硫',
            SPAN: [
              {
                rowspan: 2,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              }
            ],
            INPUT: [false, false, false, false, false, false, false]
          },
          {
            A_LIST: '转炉工序',
            B_LIST: '设备情况',
            C_LIST: '设备情况',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              }
            ],
            INPUT: [false, false, false, true, false, true, false]
          },
          {
            A_LIST: '精炼工序',
            B_LIST: 'LF',
            C_LIST: '炉座',
            D_LIST: '1#LF',
            E_LIST: '2#LF',
            F_LIST: '3#LF',
            G_LIST: '4#LF',
            SPAN: [
              {
                rowspan: 9,
                colspan: 1
              },
              {
                rowspan: 2,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, false, false, false, false]
          },
          {
            A_LIST: '精炼工序',
            B_LIST: 'LF',
            C_LIST: '设备情况',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '精炼工序',
            B_LIST: 'RH',
            C_LIST: '工位',
            D_LIST: '生产炉次',
            E_LIST: '平均真空度(Pa)',
            F_LIST: '最小真空度(Pa)',
            G_LIST: '最大真空度(Pa)',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 7,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, false, false, false, false]
          },
          {
            A_LIST: '精炼工序',
            B_LIST: 'RH',
            C_LIST: '1#RH-1',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '精炼工序',
            B_LIST: 'RH',
            C_LIST: '1#RH-2',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '精炼工序',
            B_LIST: 'RH',
            C_LIST: '2#RH-1',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '精炼工序',
            B_LIST: 'RH',
            C_LIST: '2#RH-2',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '精炼工序',
            B_LIST: 'RH',
            C_LIST: '3#RH-1',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '精炼工序',
            B_LIST: 'RH',
            C_LIST: '3#RH-2',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '项目',
            C_LIST: '项目',
            D_LIST: '1#连铸',
            E_LIST: '2#连铸',
            F_LIST: '3#连铸',
            G_LIST: '0#连铸',
            SPAN: [
              {
                rowspan: 13,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, false, false, false, false]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '大包包盖',
            C_LIST: '大包包盖',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '结晶器',
            C_LIST: '结晶器',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '电搅段',
            C_LIST: '2#段',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 2,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '电搅段',
            C_LIST: '3#段',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '扇形段',
            C_LIST: '过钢量',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 2,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '扇形段',
            C_LIST: '喷嘴检查',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '二冷水流量',
            C_LIST: '二冷水流量',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '设备水流量',
            C_LIST: '设备水流量',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '辊缝',
            C_LIST: '测量周期',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 2,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '辊缝',
            C_LIST: '精度',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '毛刺机',
            C_LIST: '毛刺机',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '连铸工序',
            B_LIST: '其他',
            C_LIST: '其他',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '坯料工序',
            B_LIST: '硫印室',
            C_LIST: '硫印室',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 2
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 1
              }
            ],
            INPUT: [false, false, false, true, true, true, true]
          },
          {
            A_LIST: '备注',
            B_LIST: '',
            C_LIST: '',
            D_LIST: '',
            E_LIST: '',
            F_LIST: '',
            G_LIST: '',
            SPAN: [
              {
                rowspan: 1,
                colspan: 1
              },
              {
                rowspan: 1,
                colspan: 6
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              },
              {
                rowspan: 0,
                colspan: 0
              }
            ],
            INPUT: [false, true, false, false, false, false, false]
          }
        ],
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getUnfinished({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '质量设备'
      })
    }
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.unfinished.maxHeight = this.$refs.table1.offsetHeight
    this.$nextTick(() => {
      console.log(this.$refs.table1.offsetHeight)
    })
  },
  methods: {
    // 获取数据
    getUnfinished(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        const initializedData2 = lodash.cloneDeep(
          this.unfinished.initializedData2
        )
        let datas = lodash.map(initializedData2, (item, index) => {
          if (res.data[index] !== undefined) {
            if (!this.unfinished.initializedData.includes(index)) {
              const dayi = ['D_LIST', 'E_LIST', 'F_LIST', 'G_LIST']
              if (index == 25) {
                dayi.push('B_LIST')
              }
              dayi.forEach(itemData => {
                item[itemData] = res.data[index][itemData]
              })
              return item
            } else {
              return item
            }
          } else {
            return item
          }
        })
        console.log(this.unfinished.initializedData2)
        this.unfinished.gridData = _.cloneDeep(datas)
        this.unfinished.showGridData = _.cloneDeep(datas)
      })
    },
    saveUnfinished() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '质量设备',
        data: lodash.map(this.unfinished.gridData, (item, index) => {
          item.PROD_DATE = this.$moment(this.cDate).format('yyyyMMDD')
          item.FLAG = '质量设备'
          item.SORT_NUM = index + 1
          return item
        })
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.unfinished.dialogVisible = false
        this.getUnfinished({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '质量设备'
        })
        this.loading = false
      })
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      return row['SPAN'][columnIndex]
    },
    importUnfinishedData(date) {
      this.loading = true
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: '质量设备'
      }).then(res => {
        this.loading = false
        const initializedData2 = lodash.cloneDeep(
          this.unfinished.initializedData2
        )
        let datas = lodash.map(initializedData2, (item, index) => {
          if (res.data[index] !== undefined) {
            if (!this.unfinished.initializedData.includes(index)) {
              const dayi = ['D_LIST', 'E_LIST', 'F_LIST', 'G_LIST']
              dayi.forEach(itemData => {
                item[itemData] = res.data[index][itemData]
              })
              return item
            } else {
              return item
            }
          } else {
            return item
          }
        })
        this.unfinished.gridData = _.cloneDeep(datas)
        this.$message.success('导入成功！')
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
