<template>
  <div class="content">
    <div class="content-item content-item-top">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="14"
          class="full-height">
          <screen-border-multi>
            <template v-slot:title>
              <div class="tabs-class">
                <div
                  v-for="(item, index) in tabList3"
                  :key="item.id"
                  :class="{'tab-pane-active': item.active}"
                  class="tab-pane"
                  @click="clickTabPane3(item, index)">
                  <div class="tab-pane-title-class">
                    <div>{{ item.title }}</div>
                    <div
                      v-if="item.active"
                      class="tab-pane-img">
                      <img
                        class="tab-pane-img2"
                        src="@/assets/images/screen/tab-pane-active-line2.png"
                        alt="">
                      <img
                        class="tab-pane-img1"
                        src="@/assets/images/screen/tab-pane-active-line.png"
                        alt="">
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-slot:default>
              <div
                v-if="active3===0"
                ref="table9"
                class="chart-wrapper">

                <div 
                  ref="table1" 
                  class="scroll-wrapper">
                  <span
                    v-command="'/screen/morningMeeting/edit'"
                    class="screen-btn"
                    style="float:right"
                    @click="factoryOrder.dialogVisible = true">
                    <el-icon class="el-icon-edit-outline"/>
                    操作
                  </span>
                  <el-table
                    v-loading="loading"
                    :data="factoryOrder.showGridData"
                    :span-method="arraySpanMethod"
                    :max-height="factoryOrder.maxHeight"
                    :row-class-name="unPlanedTotalClass"
                    border>
                    <el-table-column
                      align="center"
                      property="thFactoryOrder"
                      label="三个厂订单"/>
                    <el-table-column
                      align="center"
                      property=""
                      label="等待生产">
                      <el-table-column
                        align="center"
                        property="waitSteelmaking"
                        label="待炼钢"/>
                      <el-table-column
                        align="center"
                        property="waitSteelrolling"
                        label="待轧钢"/>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property="trackingContent"
                      label="暂缓生产">
                      <el-table-column
                        align="center"
                        property="postponeSteelmaking"
                        label="待炼钢"/>
                      <el-table-column
                        align="center"
                        property="postponeSteelrolling"
                        label="待轧钢"/>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      property="unsoldOrder"
                      label="未抛单"/>
                    <el-table-column
                      align="center"
                      property="thFactoryOrdTotal"
                      label="合计"/>
                    <el-table-column
                      align="center"
                      property="ordeNotch"
                      label="完成计划的订单缺口"/>
                    <el-table-column
                      align="center"
                      property=""
                      label="当日确认订单">
                      <el-table-column
                        align="center"
                        property="vacuumSteelConfirm"
                        label="真空钢"/>
                      <el-table-column
                        align="center"
                        property="commonSteelConfirm"
                        label="普通钢"/>
                      <el-table-column
                        align="center"
                        property="confirmTotal"
                        label="合计"/>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
              <custom-table-noheader
                v-else-if="active3===1"
                :key="'productYes5'"
                :title="'金润订单'"
                :setting="tableObj1.setting"
                :url-list="tableObj1.url.list"
                :url-save="tableObj1.url.save"
                :select-date="selectDate"/>
              <custom-table-noheader
                v-else-if="active3===2"
                :title="'金润机加工设备利用率'"
                :setting="tableObj2.setting"
                :url-list="tableObj2.url.list"
                :url-save="tableObj2.url.save"
                :select-date="selectDate"/>
            
              
            </template>

          </screen-border-multi>
          <!-- <screen-border title="三个厂订单">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/morningMeeting/edit'"
                class="screen-btn"
                @click="factoryOrder.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div 
              ref="table1" 
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="factoryOrder.showGridData"
                :span-method="arraySpanMethod"
                :max-height="factoryOrder.maxHeight"
                :row-class-name="unPlanedTotalClass"
                border>
                <el-table-column
                  align="center"
                  property="thFactoryOrder"
                  label="三个厂订单"/>
                <el-table-column
                  align="center"
                  property=""
                  label="等待生产">
                  <el-table-column
                    align="center"
                    property="waitSteelmaking"
                    label="待炼钢"/>
                  <el-table-column
                    align="center"
                    property="waitSteelrolling"
                    label="待轧钢"/>
                </el-table-column>
                <el-table-column
                  align="center"
                  property="trackingContent"
                  label="暂缓生产">
                  <el-table-column
                    align="center"
                    property="postponeSteelmaking"
                    label="待炼钢"/>
                  <el-table-column
                    align="center"
                    property="postponeSteelrolling"
                    label="待轧钢"/>
                </el-table-column>
                <el-table-column
                  align="center"
                  property="unsoldOrder"
                  label="未抛单"/>
                <el-table-column
                  align="center"
                  property="thFactoryOrdTotal"
                  label="合计"/>
                <el-table-column
                  align="center"
                  property="ordeNotch"
                  label="完成计划的订单缺口"/>
                <el-table-column
                  align="center"
                  property=""
                  label="当日确认订单">
                  <el-table-column
                    align="center"
                    property="vacuumSteelConfirm"
                    label="真空钢"/>
                  <el-table-column
                    align="center"
                    property="commonSteelConfirm"
                    label="普通钢"/>
                  <el-table-column
                    align="center"
                    property="confirmTotal"
                    label="合计"/>
                </el-table-column>
              </el-table>
            </div>
          </screen-border> -->
        </el-col>
        <el-col
          :span="10"
          class="full-height">
          <screen-border title="本月资源确认">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/morningMeeting/edit'"
                class="screen-btn"
                @click="resourceConfirm.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div 
              ref="table2" 
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="resourceConfirm.showGridData"
                :max-height="resourceConfirm.maxHeight"
                border>
                <el-table-column
                  align="center"
                  property="department"
                  label="部门"/>
                <el-table-column
                  align="center"
                  property="resources"
                  label="资源"/>
                <el-table-column
                  align="center"
                  property="confirmed"
                  label="已经确认"/>
                <el-table-column
                  align="center"
                  property="scale"
                  label="比例(%)"/>
                <el-table-column
                  align="center"
                  property="remarks"
                  label="备注"/>
              </el-table>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item content-item-bottom">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <screen-border title="备注1">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/morningMeeting/edit'"
                class="screen-btn"
                @click="parameter.dialogVisible = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="scroll-wrapper">
              <div
                class="order-note"
                v-html="formatText(note)"/>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>
    <div class="content-item content-item-bottom">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="24"
          class="full-height">
          <screen-border title="备注2">
            <template v-slot:headerRight>
              <span
                v-command="'/screen/morningMeeting/edit'"
                class="screen-btn"
                @click="parameter.dialogVisible2 = true">
                <el-icon class="el-icon-edit-outline"/>
                操作
              </span>
            </template>
            <div class="scroll-wrapper">
              <div
                class="order-note"
                v-html="formatText(note2)"/>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <!--热处理详情-->
    <el-dialog
      :visible.sync="factoryOrder.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">

            <span
              class="screen-btn"
              @click="SyncData(threeFactoryOrderTask)">
              手动同步数据
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('factoryOrder')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importfactoryOrderData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handlePreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportfactoryOrder">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="savefactoryOrder">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          三个厂订单详情
        </div>
      </template>
      <el-form
        v-loading="syncLoading" 
        :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :span-method="arraySpanMethod"
          :data="factoryOrder.gridData"
          border>
          <el-table-column
            align="center"
            property="thFactoryOrder"
            label="三个厂订单">
            <template v-slot="{ row }">
              <el-input v-model="row.thFactoryOrder" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property=""
            label="等待生产">
            <el-table-column
              align="center"
              property="waitSteelmaking"
              label="待炼钢">
              <template v-slot="{ row }">
                <el-input v-model="row.waitSteelmaking" />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property="waitSteelrolling"
              label="待轧钢">
              <template v-slot="{ row }">
                <el-input v-model="row.waitSteelrolling" />
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            align="center"
            property="trackingContent"
            label="暂缓生产">
            <el-table-column
              align="center"
              property="postponeSteelmaking"
              label="待炼钢">
              <template v-slot="{ row }">
                <el-input v-model="row.postponeSteelmaking" />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property="postponeSteelrolling"
              label="待轧钢">
              <template v-slot="{ row }">
                <el-input v-model="row.postponeSteelrolling" />
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            align="center"
            property="unsoldOrder"
            label="未抛单">
            <template v-slot="{ row }">
              <el-input v-model="row.unsoldOrder" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="thFactoryOrdTotal"
            label="合计">
            <template v-slot="{ row }">
              <el-input v-model="row.thFactoryOrdTotal" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="ordeNotch"
            label="完成计划的订单缺口">
            <template v-slot="{ row }">
              <el-input v-model="row.ordeNotch" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property=""
            label="当日确认订单">
            <el-table-column
              align="center"
              property="vacuumSteelConfirm"
              label="真空钢">
              <template v-slot="{ row }">
                <el-input v-model="row.vacuumSteelConfirm" />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property="commonSteelConfirm"
              label="普通钢">
              <template v-slot="{ row }">
                <el-input v-model="row.commonSteelConfirm" />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              property="confirmTotal"
              label="合计">
              <template v-slot="{ row }">
                <el-input v-model="row.confirmTotal" />
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index, 'factoryOrder')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEdit"
          class="screen-btn"
          @click="addGridData('factoryOrder')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--非计划改判-->
    <el-dialog
      :visible.sync="resourceConfirm.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <span
              class="screen-btn"
              @click="SyncData(resourceConfirmTask)">
              手动同步数据
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="clearGridData('resourceConfirm')">
              清空数据
            </span>
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              class="screen-input"
              @change="changeDate"/>
            <el-dropdown
              v-if="canEdit"
              @command="handleProcessedCommand($event, 'importresourceConfirmData')">
              <el-upload
                ref="upload"
                :show-file-list="false"
                :on-change="handleresourceConfirmPreview"
                :auto-upload="false"
                :action="''"
                style="display: inline-block">
                <span
                  class="screen-btn">
                  <el-icon class="el-icon-edit-outline"/>
                  EXCEL导入
                </span>
              </el-upload>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="yesterday"
                  icon="el-icon-copy">
                  从上一日导入
                </el-dropdown-item>
                <el-dropdown-item
                  command="other"
                  icon="el-icon-copy">
                  从其他日期导入
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span
              class="screen-btn"
              @click="exportresourceConfirm">
              导出
            </span>
            <span
              v-if="canEdit"
              class="screen-btn"
              @click="saveresourceConfirm">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          资源确认详情
        </div>
      </template>
      <el-form
        v-loading="syncLoading" 
        :disabled="!canEdit">
        <el-table
          v-loading="loading"
          :data="resourceConfirm.gridData"
          border>
          <el-table-column
            align="center"
            property="department"
            label="部门">
            <template v-slot="{ row }">
              <el-input v-model="row.department" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="resources"
            label="资源">
            <template v-slot="{ row }">
              <el-input v-model="row.resources" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="confirmed"
            label="已经确认">
            <template v-slot="{ row }">
              <el-input v-model="row.confirmed" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="scale"
            label="比例(%)">
            <template v-slot="{ row }">
              <el-input v-model="row.scale" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="scale"
            label="备注">
            <template v-slot="{ row }">
              <el-input v-model="row.remarks" />
            </template>
          </el-table-column>
          <el-table-column
            property="progress"
            label="操作"
            width="100">
            <template v-slot="{ row, $index }">
              <span
                v-if="canEdit"
                class="screen-btn"
                @click="delGridData($index, 'resourceConfirm')">
                <el-icon class="el-icon-delete"/>
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center">
        <span
          v-if="canEdit"
          class="screen-btn"
          @click="addGridData('resourceConfirm')">
          <el-icon class="el-icon-circle-plus-outline"/>
          增加数据
        </span>
      </div>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
    <!--订单备注-->
    <el-dialog
      :visible.sync="parameter.dialogVisible"
      :width="'800px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="订单备注1">
      <template v-slot:title>
        <div class="custom-dialog-title">
          订单备注1
        </div>
      </template>
      <el-form
        :model="formInline"
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="订单备注1">
          <el-input
            v-model="formInline.orderNote"
            :rows="6"
            type="textarea"
            placeholder="输入订单备注"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="onSubmit('orderNote')">保存</el-button>
        </div>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="parameter.dialogVisible2"
      :width="'800px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="订单备注2">
      <template v-slot:title>
        <div class="custom-dialog-title">
          订单备注2
        </div>
      </template>
      <el-form
        :model="formInline"
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="订单备注2">
          <el-input
            v-model="formInline.orderNote2"
            :rows="6"
            type="textarea"
            placeholder="输入订单备注"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="onSubmit('orderNote2')">保存</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import SteelBarsChart from '@/pages/screen/morningMeeting/component/steel-bars-chart'
import SingleBarsChart from '@/pages/screen/morningMeeting/component/single-bars-chart'
import { batchUpdateResource } from '@/api/system'
import { post } from '@/lib/Util'
import CustomTableNoheader from '@/pages/screen/C2Meeting/component/custom-table-noheader'
import ScreenBorderMulti from '@/pages/screen/firstSteelMeeting/component/screen-border-multi'
import lodash from 'lodash'
import {
  findBoardParameterByDateAndPara,
  findResourceConfirmByDate,
  findThreeFactoryOrderByDate,
  resourceConfirmTask,
  saveBoardParameter,
  saveResourceConfirm,
  saveThreeFactoryOrder,
  threeFactoryOrderTask,
  JinrunFactoryOrderFind,
  JinrunFactoryOrderSave,
  EquipmentUtilizationFind,
  EquipmentUtilizationSave
} from '@/api/screen'
import { math } from '@/lib/Math'
import * as _ from 'lodash'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
export default {
  name: 'Order',
  components: {
    SingleBarsChart,
    SteelBarsChart,
    ScreenBorder,
    ScreenBorderMulti,
    CustomTableNoheader
  },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      resourceConfirmTask: resourceConfirmTask,
      threeFactoryOrderTask: threeFactoryOrderTask,
      factoryOrder: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      resourceConfirm: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      },
      parameter: {
        dialogVisible: false,
        dialogVisible2: false
      },
      note: '',
      note2: '',
      formInline: {
        orderNote: '',
        orderNote2: ''
      },
      active3: 0,
      tabList3: [
        {
          id: '1',
          active: true,
          title: '三个厂订单'
        },
        {
          id: '2',
          active: false,
          title: '金润订单'
        },
        {
          id: '3',
          active: false,
          title: '金润机加工设备利用率'
        }
      ],
      tableObj1: {
        url: {
          save: JinrunFactoryOrderSave,
          list: JinrunFactoryOrderFind
        },
        setting: [
          {
            keyQuery: 'month',
            keySave: 'month',
            label: '月份'
          },
          {
            keyQuery: 'totalOrder',
            keySave: 'totalOrder',
            label: '总接单量(吨)'
          },
          {
            keyQuery: 'otherOrder',
            keySave: 'otherOrder',
            label: '配套件(吨)'
          },
          {
            keyQuery: 'mainOrder',
            keySave: 'mainOrder',
            label: '主营业务订单(吨)'
          }
        ]
      },
      tableObj2: {
        url: {
          save: EquipmentUtilizationSave,
          list: EquipmentUtilizationFind
        },
        setting: [
          //  {
          //    keyQuery: 'name',
          //    keySave: 'name',
          //    label: '机床名称'
          //  },
          {
            keyQuery: 'pDRILL2',
            keySave: 'pDRILL2',
            label: '平钻2'
          },
          {
            keyQuery: 'pDRILL3',
            keySave: 'pDRILL3',
            label: '平钻3'
          },
          {
            keyQuery: 'pDRILL4',
            keySave: 'pDRILL4',
            label: '平钻4'
          },
          {
            keyQuery: 'pEMILL1',
            keySave: 'pEMILL1',
            label: '平铣1'
          },
          {
            keyQuery: 'pEMILL2',
            keySave: 'pEMILL2',
            label: '平铣2'
          },
          {
            keyQuery: 'pEMILL3',
            keySave: 'pEMILL3',
            label: '平铣3'
          },
          {
            keyQuery: 'pEMILL4',
            keySave: 'pEMILL4',
            label: '平铣4'
          },
          {
            keyQuery: 'hDRILL3',
            keySave: 'hDRILL3',
            label: '弧钻3'
          },
          {
            keyQuery: 'hDRILL4',
            keySave: 'hDRILL4',
            label: '弧钻4'
          },
          {
            keyQuery: 'hDRILL5',
            keySave: 'hDRILL5',
            label: '弧钻5'
          },
          {
            keyQuery: 'hDRILL6',
            keySave: 'hDRILL6',
            label: '弧钻6'
          },
          {
            keyQuery: 'HEMILL1',
            keySave: 'HEMILL1',
            label: '弧铣1'
          },
          {
            keyQuery: 'hEMILL2',
            keySave: 'hEMILL2',
            label: '弧铣2'
          },
          {
            keyQuery: 'hEMILL3',
            keySave: 'hEMILL3',
            label: '弧铣3'
          },
          {
            keyQuery: 'hEMILL4',
            keySave: 'hEMILL4',
            label: '弧铣4'
          },
          {
            keyQuery: 'aVGU',
            keySave: 'aVGU',
            label: '平均利用率'
          }
        ]
      },
      checkOnlineList: []
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      this.getParameter()
      this.getfactoryOrder()
      this.getresourceConfirm()
    }
  },
  created() {
    this.cDate = this.selectDate
  },
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  methods: {
    calculateHeight() {
      this.factoryOrder.maxHeight = this.$refs.table1.offsetHeight
      this.resourceConfirm.maxHeight = this.$refs.table2.offsetHeight
    },
    async getParameter() {
      // 参数
      const parameters = await post(findBoardParameterByDateAndPara, {
        setDate: this.cDate
      })
      this.formInline.orderNote = this.note = this.getParam(
        'orderNote',
        parameters.data
      )
      this.formInline.orderNote2 = this.note2 = this.getParam(
        'orderNote2',
        parameters.data
      )
    },
    getParam(name, list) {
      const match = list.find(item => item.parameter === name)
      return match ? match.content : null
    },
    handlePreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          thFactoryOrder: 'A',
          waitSteelmaking: 'B',
          waitSteelrolling: 'C',
          postponeSteelmaking: 'D',
          postponeSteelrolling: 'E',
          unsoldOrder: 'F',
          thFactoryOrdTotal: 'G',
          ordeNotch: 'H',
          vacuumSteelConfirm: 'I',
          commonSteelConfirm: 'J',
          confirmTotal: 'K'
        })
        // 去除第3行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        sheet.shift()
        sheet.shift()
        // 表格信息
        this.factoryOrder.gridData = sheet.map(item => {
          return {
            thFactoryOrder: item.thFactoryOrder,
            waitSteelmaking: parseInt(item.waitSteelmaking),
            waitSteelrolling: parseInt(item.waitSteelrolling),
            postponeSteelmaking: parseInt(item.postponeSteelmaking),
            postponeSteelrolling: parseInt(item.postponeSteelrolling),
            unsoldOrder: parseInt(item.unsoldOrder),
            thFactoryOrdTotal: parseInt(item.thFactoryOrdTotal),
            ordeNotch: parseInt(item.ordeNotch),
            vacuumSteelConfirm: parseInt(item.vacuumSteelConfirm),
            commonSteelConfirm: parseInt(item.commonSteelConfirm),
            confirmTotal: parseInt(item.confirmTotal)
          }
        })
        this.$message.success('解析成功！')
      })
    },
    exportfactoryOrder() {
      const data = [
        {
          thFactoryOrder: '三个厂订单情况（' + this.cDate + '）',
          waitSteelmaking: '',
          waitSteelrolling: '',
          postponeSteelmaking: '',
          postponeSteelrolling: '',
          unsoldOrder: '',
          thFactoryOrdTotal: '',
          ordeNotch: '',
          vacuumSteelConfirm: '',
          commonSteelConfirm: '',
          confirmTotal: ''
        },
        {
          thFactoryOrder: '三个厂订单',
          waitSteelmaking: '等待生产',
          waitSteelrolling: '',
          postponeSteelmaking: '暂缓生产',
          postponeSteelrolling: '',
          unsoldOrder: '未抛单',
          thFactoryOrdTotal: '合计',
          ordeNotch: '完成计划的订单缺口',
          vacuumSteelConfirm: '当日确认订单',
          commonSteelConfirm: '',
          confirmTotal: ''
        },
        {
          thFactoryOrder: '三个厂订单',
          waitSteelmaking: '待炼钢',
          waitSteelrolling: '待轧钢',
          postponeSteelmaking: '待炼钢',
          postponeSteelrolling: '待轧钢',
          unsoldOrder: '未抛单',
          thFactoryOrdTotal: '合计',
          ordeNotch: '完成计划的订单缺口',
          vacuumSteelConfirm: '真空钢',
          commonSteelConfirm: '普通钢',
          confirmTotal: '合计'
        }
      ].concat(_.cloneDeep(this.factoryOrder.gridData))
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `三个厂订单（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {
              '!merges': LAY_EXCEL.makeMergeConfig([
                ['A1', 'K1'],
                ['A2', 'A3'],
                ['B2', 'C2'],
                ['D2', 'E2'],
                ['F2', 'F3'],
                ['G2', 'G3'],
                ['H2', 'H3'],
                ['I2', 'K2']
              ])
            }
          }
        }
      )
    },
    getfactoryOrder() {
      post(findThreeFactoryOrderByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.factoryOrder.showGridData = res.data.map(item => {
          return {
            thFactoryOrder: item.thfactoryorder,
            waitSteelmaking: item.waitsteelmaking,
            waitSteelrolling: item.waitsteelrolling,
            postponeSteelmaking: item.postponesteelmaking,
            postponeSteelrolling: item.postponesteelrolling,
            unsoldOrder: item.unsoldorder,
            thFactoryOrdTotal: item.thfactoryordtotal,
            ordeNotch: item.ordenotch,
            vacuumSteelConfirm: item.vacuumsteelconfirm,
            commonSteelConfirm: item.commonsteelconfirm,
            confirmTotal: item.confirmtotal
          }
        })
        this.factoryOrder.gridData = lodash.cloneDeep(
          this.factoryOrder.showGridData
        )
      })
    },
    savefactoryOrder() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.factoryOrder.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(saveThreeFactoryOrder, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.factoryOrder.dialogVisible = false
          this.getfactoryOrder()
        }
      })
    },
    //点检tab切换
    clickTabPane3(item, index) {
      this.tabList3.forEach(item => {
        item.active = false
      })
      item.active = true
      this.active3 = index
      console.log('aaaa', item)
      console.log('aaaa', index)
    },
    handleresourceConfirmPreview(file) {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      LAY_EXCEL.importExcel([file.raw], {}, (data, book) => {
        console.log(data)
        data = LAY_EXCEL.filterImportData(data, {
          department: 'A',
          resources: 'B',
          confirmed: 'C',
          scale: 'D',
          remarks: 'E'
        })
        // 去除第2行
        const sheet = data[0].Sheet1 || data[0].sheet1
        if (!sheet) this.$message('未找到sheet1，请检查！')
        sheet.shift()
        // 表格信息
        this.resourceConfirm.gridData = sheet
          .filter(item => item.department !== '部门')
          .map(item => {
            item.resources = Number(Number(item.resources || 0).toFixed(2))
            item.confirmed = Number(Number(item.confirmed || 0).toFixed(2))
            item.scale = item.scale.toString()
            if (item.scale.includes('%')) {
              item.scale = item.scale.replace('%', '')
            } else {
              item.scale = Number(
                math.multiply(Number(item.scale), 100).toFixed(2)
              )
            }
            return item
          })
        this.$message.success('解析成功！')
      })
    },
    exportresourceConfirm() {
      const data = [
        {
          department: '当月资源确认',
          resources: '',
          confirmed: '',
          scale: '',
          remarks: ''
        },
        {
          department: '部门',
          resources: '资源',
          confirmed: '已经确认',
          scale: '比例',
          remarks: '备注'
        }
      ].concat(
        _.cloneDeep(this.resourceConfirm.gridData).map(item => {
          item.scale = item.scale + '%'
          return item
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(
        data,
        `资源确认（${this.cDate}晨会）.xlsx`,
        'xlsx',
        {
          extend: {
            // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
            sheet1: {
              '!merges': LAY_EXCEL.makeMergeConfig([['A1', 'D1']])
            }
          }
        }
      )
    },
    getresourceConfirm() {
      post(findResourceConfirmByDate, {
        setDate: this.cDate
      }).then(res => {
        //
        this.loading = false
        this.resourceConfirm.gridData = res.data.map(item => {
          return {
            department: item.department,
            resources: item.resources,
            confirmed: item.confirmed,
            scale: item.scale,
            remarks: item.remarks
          }
        })
        this.resourceConfirm.showGridData = lodash.cloneDeep(
          this.resourceConfirm.gridData
        )
      })
    },
    saveresourceConfirm() {
      this.loading = true
      // 保存钢铁产量信息
      const params = {
        setDate: this.cDate,
        data: this.resourceConfirm.gridData.map(item => {
          item.setDate = this.cDate
          return item
        })
      }
      post(saveResourceConfirm, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.resourceConfirm.dialogVisible = false
          this.getresourceConfirm()
        }
      })
    },
    importfactoryOrderData(date) {
      post(findThreeFactoryOrderByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.factoryOrder.gridData = res.data.map(item => {
          return {
            thFactoryOrder: item.thfactoryorder,
            waitSteelmaking: item.waitsteelmaking,
            waitSteelrolling: item.waitsteelrolling,
            postponeSteelmaking: item.postponesteelmaking,
            postponeSteelrolling: item.postponesteelrolling,
            unsoldOrder: item.unsoldorder,
            thFactoryOrdTotal: item.thfactoryordtotal,
            ordeNotch: item.ordenotch,
            vacuumSteelConfirm: item.vacuumsteelconfirm,
            commonSteelConfirm: item.commonsteelconfirm,
            confirmTotal: item.confirmtotal
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    importresourceConfirmData(date) {
      post(findResourceConfirmByDate, {
        setDate: date
      }).then(res => {
        //
        this.loading = false
        this.resourceConfirm.gridData = res.data.map(item => {
          return {
            department: item.department,
            resources: item.resources,
            confirmed: item.confirmed,
            scale: item.scale
          }
        })
        if (!res.data.length) {
          this.$message.warning('该日期无数据！')
        } else {
          this.$message.success('导入成功！')
        }
      })
    },
    onSubmit(name) {
      const params = {
        data: [
          {
            parameter: name,
            content: this.formInline[name],
            setDate: this.cDate
          }
        ]
      }
      post(saveBoardParameter, params).then(res => {
        //
        this.loading = false
        if (res.status === 1) {
          this.$message.success('保存成功！')
          this.parameter.dialogVisible = false
          this.parameter.dialogVisible2 = false
          this.getParameter()
        }
      })
    },
    unPlanedTotalClass(row) {
      console.log(row)
      if (row.row.thFactoryOrder && row.row.thFactoryOrder.trim() === '总计') {
        return 'table-total'
      }
      return ''
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      //
      if (row.thFactoryOrder === '总计') {
        if (columnIndex === 1) {
          return [1, 2]
        } else if (columnIndex === 3) {
          return [1, 3]
        } else if (columnIndex === 6) {
          return [1, 1]
        } else if (columnIndex < 6 && columnIndex > 1) {
          return [0, 0]
        } else {
          return [1, 1]
        }
      }
      if (row.thFactoryOrder === '备注') {
        if (columnIndex === 1) {
          return [1, 10]
        }
        if (columnIndex < 11 && columnIndex > 1) {
          return [0, 0]
        } else {
          return [1, 1]
        }
      }
    },
    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-hold {
    height: 32px;
  }
  .content-item {
    flex: 1;
    overflow: hidden;
  }
  .content-item-top {
    min-height: 380px;
  }
  .content-item-bottom {
    flex: 0.6;
  }
  .scroll-wrapper {
    height: 100%;
    overflow: auto;
  }
}
.order-note {
  font-size: 22px;
  line-height: 1.5;
}
.tabs-class {
  display: flex;
  flex-direction: row;
  .tab-pane {
    color: #ffffffbf;
    margin-right: 21px;
  }
  .tab-pane-active {
    color: #ffffff;
  }
  .tab-pane-title-class {
    display: flex;
    flex-direction: column;
    position: relative;
    .tab-pane-img {
      .tab-pane-img2 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
      .tab-pane-img1 {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        margin-bottom: 7px;
      }
    }
  }
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .cards {
    height: 65px;
    text-align: right;
    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);
      span {
        text-align: center;
        padding: 0 5px;
        em {
          font-size: 16px;
          font-weight: bold;
          &.red {
            color: #ff2855;
          }
          &.red {
            color: #19be6b;
          }
        }
      }
      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }
      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
