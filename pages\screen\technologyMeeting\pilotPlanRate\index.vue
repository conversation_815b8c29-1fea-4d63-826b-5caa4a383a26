<template>
  <div class="content">
    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col 
          :span="14"
          class="full-height">
          <screen-border :title="'产品、工艺试制计划完成情况（试制数目）'">
            <template v-slot:headerRight>
              <span
                class="screen-btn"
                @click="changeTab(1)">
                试制计划
              </span>
              <span
                class="screen-btn"
                @click="changeTab(3)">
                试制总量
              </span>
            </template>
            <div
              ref="table1"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="pilotRate1.showGridData"
                :span-method="handleObjectSpan"
                :max-height="pilotRate1.maxHeight"
                :row-class-name="totalClass"
                size="mini"
                class="center-table"
                border>
                <el-table-column
                  property="category"
                  label="类别"/>
                <el-table-column
                  property="Zj"
                  label="总计"/>
                <el-table-column
                  property="department"
                  label="部门"/>
                <el-table-column
                  label="当月新增">
                  <el-table-column
                    property="insert"
                    label="新增"/>
                  <el-table-column
                    property="addComplete"
                    label="已完成"/>
                  <el-table-column
                    property="addIncomplete"
                    label="未完成"/>
                </el-table-column>
                <el-table-column
                  label="上月转接">
                  <el-table-column
                    property="transfer"
                    label="转接"/>
                  <el-table-column
                    property="transferComplete"
                    label="已完成"/>
                  <el-table-column
                    property="transferIncomplete"
                    label="未完成"/>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="5"
          class="full-height">
          <screen-border :title="'完成情况（试制数目）'">
            <div class="chart-wrapper">
              <div class="chart">
                <single-bars-chart
                  :show-legend="false"
                  :bar-width="35"
                  :chart-data="pilotChart1.bar1"
                  :show-toolbox="true"
                  :unit="''"
                  :x-data="pilotChart1.barX1"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="5"
          class="full-height">
          <screen-border :title="'未完成原因'">
            <div class="chart-wrapper">
              <div class="chart">
                <bars-chart
                  :show-legend="false"
                  :chart-data="pilotChart1.bar2"
                  :show-toolbox="true"
                  :label-rotate="35"
                  :unit="''"
                  :x-data="pilotChart1.barX2"/>
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>

    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="14"
          class="full-height">
          <screen-border :title="'产品、工艺试制计划完成情况（试制量）'">
            <div
              ref="table2"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="pilotRate2.showGridData"
                :span-method="handleObjectSpan"
                :max-height="pilotRate2.maxHeight"
                :row-class-name="totalClass"
                size="mini"
                class="center-table"
                border>
                <el-table-column
                  property="category"
                  label="类别"/>
                <el-table-column
                  property="Zj"
                  label="总计"/>
                <el-table-column
                  property="department"
                  label="部门"/>
                <el-table-column
                  label="当月新增">
                  <el-table-column
                    property="insert"
                    label="新增"/>
                  <el-table-column
                    property="addComplete"
                    label="已完成"/>
                  <el-table-column
                    property="addIncomplete"
                    label="未完成"/>
                </el-table-column>
                <el-table-column
                  label="上月转接">
                  <el-table-column
                    property="transfer"
                    label="转接"/>
                  <el-table-column
                    property="transferComplete"
                    label="已完成"/>
                  <el-table-column
                    property="transferIncomplete"
                    label="未完成"/>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="5"
          class="full-height">
          <screen-border :title="'完成情况（试制量）'">
            <div class="chart-wrapper">
              <div class="chart">
                <single-bars-chart
                  :show-legend="false"
                  :bar-width="35"
                  :chart-data="pilotChart2.bar1"
                  :show-toolbox="true"
                  :unit="''"
                  :x-data="pilotChart2.barX1"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="5"
          class="full-height">
          <screen-border :title="'未完成原因'">
            <div class="chart-wrapper">
              <div class="chart">
                <bars-chart
                  :show-legend="false"
                  :chart-data="pilotChart2.bar2"
                  :show-toolbox="true"
                  :label-rotate="35"
                  :unit="''"
                  :x-data="pilotChart2.barX2"/>
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
    <div class="content-hold"/>

    <div class="content-item">
      <el-row
        :gutter="32"
        class="full-height">
        <el-col
          :span="14"
          class="full-height">
          <screen-border :title="'产品、工艺试制计划完成情况（炼钢量）'">
            <div
              ref="table3"
              class="scroll-wrapper">
              <el-table
                v-loading="loading"
                :data="pilotRate3.showGridData"
                :span-method="handleObjectSpan"
                :max-height="pilotRate3.maxHeight"
                :row-class-name="totalClass"
                size="mini"
                class="center-table"
                border>
                <el-table-column
                  property="category"
                  label="类别"/>
                <el-table-column
                  property="Zj"
                  label="总计"/>
                <el-table-column
                  property="department"
                  label="部门"/>
                <el-table-column
                  label="当月新增">
                  <el-table-column
                    property="insert"
                    label="新增"/>
                  <el-table-column
                    property="addComplete"
                    label="已完成"/>
                  <el-table-column
                    property="addIncomplete"
                    label="未完成"/>
                </el-table-column>
                <el-table-column
                  label="上月转接">
                  <el-table-column
                    property="transfer"
                    label="转接"/>
                  <el-table-column
                    property="transferComplete"
                    label="已完成"/>
                  <el-table-column
                    property="transferIncomplete"
                    label="未完成"/>
                </el-table-column>
              </el-table>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="5"
          class="full-height">
          <screen-border :title="'完成情况（炼钢量）'">
            <div class="chart-wrapper">
              <div class="chart">
                <single-bars-chart
                  :show-legend="false"
                  :bar-width="35"
                  :chart-data="pilotChart3.bar1"
                  :show-toolbox="true"
                  :unit="''"
                  :x-data="pilotChart3.barX1"/>
              </div>
            </div>
          </screen-border>
        </el-col>
        <el-col
          :span="5"
          class="full-height">
          <screen-border :title="'未完成原因'">
            <div class="chart-wrapper">
              <div class="chart">
                <bars-chart
                  :show-legend="false"
                  :chart-data="pilotChart3.bar2"
                  :show-toolbox="true"
                  :label-rotate="35"
                  :unit="''"
                  :x-data="pilotChart3.barX2"/>
              </div>
            </div>
          </screen-border>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import {
  checklistBySetDate,
  checklistSave,
  findTpNum,
  findTpQuantity
} from '@/api/screenTechnolagy'
import moment from 'moment'
import SingleBarsChart from '@/pages/screen/technologyMeeting/component/single-bars-chart'
import BarsChart from '@/pages/screen/technologyMeeting/component/bars-chart'
import { post } from '@/lib/Util'
import lodash from 'lodash'

export default {
  name: 'pilotPlanRate',
  components: { BarsChart, SingleBarsChart, ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      editIndex: null,
      pilotRate1: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      pilotRate2: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      pilotRate3: {
        gridData: [],
        showGridData: [],
        gridMerge: [],
        dialogVisible: false,
        maxHeight: null
      },
      pilotChart1: {
        bar1: [],
        barX1: [],
        bar2: [],
        barX2: []
      },
      pilotChart2: {
        bar1: [],
        barX1: []
      },
      pilotChart3: {
        bar1: [],
        barX1: []
      }
    }
  },
  computed: {
    prevDate: function() {
      // 初始化数据
      return moment(this.cDate)
        .subtract(1, 'month')
        .format('yyyy-MM')
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = moment(this.selectDate).format('YYYY-MM')
    },
    cDate: function() {
      // 初始化数据
      this.loadData()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.calculate)
  },
  created() {
    this.cDate = moment(this.selectDate).format('YYYY-MM')
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.calculate()
    window.addEventListener('resize', this.calculate)
  },
  methods: {
    loadData() {
      this.getPilotRate()
    },
    // 获取数据
    getPilotRate() {
      post(findTpNum, {
        setDate: this.cDate
      }).then(res => {
        this.loading = false
        this.pilotRate1.showGridData = res
        this.pilotChart1.barX1 = res.map(item => item.department)
        this.pilotChart1.bar1 = res.map(item => {
          return {
            value: item.lj - item.ljwwc,
            plan: item.lj,
            finished: true
          }
        })
        this.pilotChart1.barX2 = [
          '生产计划',
          '检试验',
          '设备',
          '技术',
          '订单交付',
          '外委',
          '现场执行'
        ]
        this.pilotChart1.bar2 = [
          {
            name: '技术研发处',
            data: [
              res[0].scjh,
              res[0].jsy,
              res[0].sb,
              res[0].js,
              res[0].ddjf,
              res[0].ww,
              res[0].xczxyy
            ]
          },
          {
            name: '研究院',
            data: [
              res[1].scjh,
              res[1].jsy,
              res[1].sb,
              res[1].js,
              res[1].ddjf,
              res[1].ww,
              res[1].xczxyy
            ]
          }
        ]
        this.formatSpanData(this.pilotRate1.showGridData)
      })

      post(findTpQuantity, {
        setDate: this.cDate
      }).then(res => {
        this.loading = false
        const data1 = res.filter(item => item.category === '试制量')
        this.pilotRate2.showGridData = data1
        this.pilotChart2.barX1 = data1.map(item => item.department)
        this.pilotChart2.bar1 = data1.map(item => {
          return {
            value: item.lj - item.ljwwc,
            plan: item.lj,
            finished: true
          }
        })
        this.pilotChart2.barX2 = [
          '生产计划',
          '检试验',
          '设备',
          '技术',
          '订单交付',
          '外委',
          '现场执行'
        ]
        this.pilotChart2.bar2 = [
          {
            name: '技术研发处',
            data: [
              data1[0].scjh,
              data1[0].jsy,
              data1[0].sb,
              data1[0].js,
              data1[0].ddjf,
              data1[0].ww,
              data1[0].xczxyy
            ]
          },
          {
            name: '研究院',
            data: [
              data1[1].scjh,
              data1[1].jsy,
              data1[1].sb,
              data1[1].js,
              data1[1].ddjf,
              data1[1].ww,
              data1[1].xczxyy
            ]
          }
        ]
        const data2 = res.filter(item => item.category === '炼钢量')
        this.pilotRate3.showGridData = data2
        this.pilotChart3.barX1 = data1.map(item => item.department)
        this.pilotChart3.bar1 = data2.map(item => {
          return {
            value: item.lj - item.ljwwc,
            plan: item.lj,
            finished: true
          }
        })
        this.pilotChart3.barX2 = [
          '生产计划',
          '检试验',
          '设备',
          '技术',
          '订单交付',
          '外委',
          '现场执行'
        ]
        this.pilotChart3.bar2 = [
          {
            name: '技术研发处',
            data: [
              data2[0].scjh,
              data2[0].jsy,
              data2[0].sb,
              data2[0].js,
              data2[0].ddjf,
              data2[0].ww,
              data2[0].xczxyy
            ]
          },
          {
            name: '研究院',
            data: [
              data2[1].scjh,
              data2[1].jsy,
              data2[1].sb,
              data2[1].js,
              data2[1].ddjf,
              data2[1].ww,
              data2[1].xczxyy
            ]
          }
        ]
        this.formatSpanData(this.pilotRate3.showGridData)
      })
    },
    calculate() {
      this.pilotRate1.maxHeight = this.$refs.table1.offsetHeight
      this.pilotRate2.maxHeight = this.$refs.table2.offsetHeight
      this.pilotRate3.maxHeight = this.$refs.table3.offsetHeight
    },
    totalClass(row) {
      if (row.row.serialNumber && row.row.serialNumber.trim() === '合计') {
        return 'table-total'
      }
      return ''
    },
    // 通知改变tab
    changeTab(tab) {
      this.$emit('tabChange', tab)
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  min-height: 600px;
  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;

    .scroll-wrapper {
      height: 100%;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}
.edit-btn {
  margin: 0 3px;
}
.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }
  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
