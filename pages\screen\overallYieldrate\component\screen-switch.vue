<template>
  <el-cascader
    v-model="url"
    :options="options"
    :props="{ expandTrigger: 'hover' }"
    class="screen-input"
    style="width: 160px"
    @change="handleChange"/>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'screen-switch',
  data: () => {
    return {
      url: '',
      urls: [],
      options: [{}]
    }
  },
  computed: {
    ...mapState('menu', ['allMenus'])
  },
  watch: {
    allMenus: function(newValue) {
      this.formatList(newValue)
    }
  },
  created() {
    this.url = this.$route.path
    this.formatList(this.allMenus)
  },
  methods: {
    handleChange(value) {
      this.$router.push(value.length >= 2 ? value[1] : value[0])
    },
    formatList(list) {
      const options = list
        .filter(item => item.code.indexOf('screen') !== -1)
        .map(item => {
          return {
            name: item.name,
            label: item.name,
            value: item.url
          }
        })
      const firstSteels = options
        .filter(item => item.name.indexOf('第一炼钢厂') !== -1)
        .map(item => {
          item.label = item.name.replace('第一炼钢厂', '').replace('看板', '')
          return item
        })
      options.push({
        name: '',
        label: '第一炼钢厂看板',
        children: firstSteels
      })
      this.options = options.filter(
        item => item.name.indexOf('第一炼钢厂') === -1
      )
    }
  }
}
</script>

<style scoped lang="less">
.screen-input {
  width: 130px;
  /deep/ .el-input__inner {
    padding-left: 7px;
  }
}
</style>
