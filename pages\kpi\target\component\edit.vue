<template>
  <div>
    <el-dialog
      :title="title + '指标'"
      :visible.sync="visible"
      :width="'600px'"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="150px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="指标描述"
          prop="name"
        >
          <el-input
            v-model="formData.describe"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入指标描述"
          />
        </el-form-item>
        <el-form-item
          label="指标名称"
          prop="name"
        >
          <el-input
            v-model="formData.name"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入指标名称"
          />
        </el-form-item>
        <el-form-item
          label="功能"
          prop="feature"
        >
          <el-select
            v-model="formData.feature"
            :style="{width: '100%'}"
            size="small"
            clearable
            placeholder="请选择功能"
          >
            <el-option
              v-for="(item, index) in kpiFunction"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="其他功能分类"
          prop="userNo"
        >
          <el-input
            v-model="formData.userNo"
            :style="{width: '100%'}"
            clearable
            placeholder="请选择其他功能分类"
          />
        </el-form-item>
        <el-form-item
          label="厂区"
          prop="factory"
        >
          <el-select
            v-model="formData.factory"
            :style="{width: '100%'}"
            size="small"
            clearable
            placeholder="请选择厂区"
          >
            <el-option
              v-for="(item, index) in factoryList"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          label="层级"
          prop="rank"
        >
          <el-select
            v-model="formData.rank"
            :style="{width: '100%'}"
            size="small"
            clearable
            placeholder="请选择层级"
          >
            <el-option
              v-for="(item, index) in levelList"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="上级指标"
          prop="parentId"
        >
          <select-kpi
            v-model="formData.parentId"
            :parent-name="formData.parentName"/>
        </el-form-item>
        <el-form-item
          label="协同跳转url"
          prop="pageDirection"
        >
          <el-input
            v-model="formData.pageDirection"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入协同跳转url"
          />
        </el-form-item>
        <el-form-item
          label="状态"
          prop="remark"
        >
          <el-switch
            v-model="formData.status"
            :active-value="0"
            :inactive-value="1"
            active-text="启用"
            inactive-text="废弃"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
        >
          <el-input
            v-model="formData.remark"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="show">取消</el-button>
        <el-button
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { roleAdd, roleEdit, userAdd, userEdit } from '@/api/system'
import { kpiSave } from '@/api/kpi'
import { ENUM } from '@/lib/Constant'
import SelectKpi from '@/components/SelectKpi'

export default {
  components: { SelectKpi },
  mixins: [EditMixins],
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  data() {
    return {
      visible: false,
      url: {
        edit: kpiSave,
        add: kpiSave
      },
      kpiFunction: ENUM.kpiFunction,
      factoryList: ENUM.factoryList,
      levelList: ENUM.levelList,
      statusList: [
        {
          value: 0,
          label: '正常',
          type: 'success'
        },
        {
          value: 1,
          label: '废弃',
          type: 'warning'
        }
      ],
      formData: {
        describe: null,
        factory: null,
        feature: null,
        name: null,
        otherType: null,
        pageDirection: null,
        rank: null,
        remark: null,
        status: 0,
        parentName: null
      },
      rules: {
        describe: [
          {
            required: true,
            message: '请输入指标描述',
            trigger: 'change'
          }
        ],
        factory: [
          {
            required: true,
            message: '请选择工厂',
            trigger: 'change'
          }
        ],
        feature: [
          {
            required: true,
            message: '请选择功能',
            trigger: 'change'
          }
        ],
        rank: [
          {
            required: true,
            message: '请选择层级',
            type: 'number',
            trigger: 'change'
          }
        ],
        parentId: [
          {
            required: true,
            message: '请选择上级指标',
            type: 'number'
          }
        ],
        status: [
          {
            required: true,
            message: '请选择状态',
            type: 'number',
            trigger: 'change'
          }
        ]
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    console.log('编辑页面')
  },
  methods: {
    show() {
      console.log(this.formData.parentName)
    }
  }
}
</script>
<style scoped>
</style>
