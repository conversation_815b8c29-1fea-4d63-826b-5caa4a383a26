<template>
  <div>
    <el-dialog
      :title="title + '指标'"
      :visible.sync="visible"
      :width="'600px'"
      :top="'40px'"
      :close-on-click-modal="false"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="150px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="指标名称"
          prop="name"
        >
          <el-input
            v-model="formData.name"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入指标名称"
          />
        </el-form-item>
        <el-form-item
          label="主题域"
          prop="feature"
        >
          <el-select
            v-model="formData.feature"
            :style="{width: '100%'}"
            size="small"
            clearable
            placeholder="请选择主题域"
          >
            <el-option
              v-for="(item, index) in kpiFunction"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="厂区/处室"
          prop="factory"
        >
          <el-select
            v-model="formData.factory"
            :style="{width: '100%'}"
            size="small"
            clearable
            placeholder="请选择厂区/处室"
          >
            <el-option
              v-for="(item, index) in factoryList"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          label="层级"
          prop="rank"
        >
          <el-select
            v-model="formData.rank"
            :style="{width: '100%'}"
            size="small"
            clearable
            placeholder="请选择层级"
          >
            <el-option
              v-for="(item, index) in levelList"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="formData.rank != 1"
          label="上级指标"
          prop="parentId"
        >
          <select-kpi
            v-model="formData.parentId"
            :parent-name="formData.parentName"/>
        </el-form-item>
        <el-form-item
          label="指标分类"
          prop="cid"
        >
          <select-kpi-category
            v-model="formData.cid"
            :style="{width: '100%'}"/>
        </el-form-item>
        <el-form-item
          label="跳转url"
          prop="pageDirection"
        >
          <el-input
            v-model="formData.pageDirection"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入跳转url"
          />
        </el-form-item>
        <el-form-item
          label="状态"
          prop="remark"
        >
          <el-switch
            v-model="formData.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="废弃"
          />
        </el-form-item>
        <el-form-item
          label="指标说明"
          prop="kpiDes"
        >
          <textarea
            v-model="formData.kpiDes"
            :style="{width: '100%', height: '10vh', borderColor: '#CFD3E2'}"
            clearable
            placeholder="请输入指标说明"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
        >
          <textarea
            v-model="formData.remark"
            :style="{width: '100%', height: '10vh', borderColor: '#CFD3E2'}"
            clearable
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { roleAdd, roleEdit, userAdd, userEdit } from '@/api/system'
import { kpiSave } from '@/api/kpi'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import SelectKpi from '@/components/SelectKpiZB'
import SelectKpiCategory from '@/components/SelectKpiCategory'

export default {
  components: { SelectKpiCategory, SelectKpi },
  mixins: [EditMixins],
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: kpiSave,
        add: kpiSave
      },
      kpiFunction: ENUM.kpiFunction,
      factoryList: [
        {
          value: 0,
          label: '生产处'
        },
        {
          value: 1,
          label: '质量处'
        },
        {
          value: 2,
          label: '设备处'
        },
        {
          value: 3,
          label: '综合处'
        },
        {
          value: 4,
          label: '技术研发处'
        },
        {
          value: 5,
          label: '第一炼钢厂'
        },
        {
          value: 6,
          label: '中厚板卷厂'
        },
        {
          value: 7,
          label: '宽厚板卷厂'
        },
        {
          value: 8,
          label: '中板厂'
        },
        {
          value: 9,
          label: '金石材料厂'
        },
        {
          value: 10,
          label: '金润智能制造厂'
        }
      ],
      levelList: ENUM.levelList,
      teamList: ENUM.teamList,
      gradeList: ENUM.gradeList,
      statusList: [
        {
          value: 1,
          label: '正常',
          type: 'success'
        },
        {
          value: 0,
          label: '废弃',
          type: 'warning'
        }
      ],
      formData: {
        factory: null,
        features: null,
        feature: null,
        cid: null,
        name: null,
        otherType: null,
        pageDirection: null,
        rank: null,
        remark: null,
        kpiDes: null,
        status: 1, // 0:废弃 1:启用
        team: null,
        parentId: null,
        parentName: null
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入指标名称',
            trigger: 'change'
          }
        ],
        factory: [
          {
            required: true,
            message: '请选择工厂',
            trigger: 'change'
          }
        ],
        feature: [
          {
            required: true,
            message: '请选择功能',
            trigger: 'change'
          }
        ],
        rank: [
          {
            required: true,
            message: '请选择层级',
            type: 'number',
            trigger: 'change'
          }
        ],
        cid: [
          {
            required: true,
            message: '请选择指标分类',
            type: 'number'
          }
        ],
        kpiDes: [
          {
            required: true,
            message: '请填写指标说明'
          }
        ],
        status: [
          {
            required: true,
            message: '请选择状态',
            type: 'number',
            trigger: 'change'
          }
        ]
      }
    }
  },
  computed: {},
  // watch: {
  //   'formData.grade': function(value) {
  //     if (value === 3) {
  //       this.formData.team = '甲'
  //     } else {
  //       this.formData.team = null
  //     }
  //   }
  // },
  created() {
    console.log('编辑页面')
  },
  methods: {
    show() {},
    submitBefore() {
      //提交前处理方法
      // this.formData.feature = this.formData.features.join(',')
    },
    handelConfirm() {
      if (this.loading) return

      if (
        this.formData.rank != 1 &&
        this.formData.rank != null &&
        (this.formData.parentId === '' || this.formData.parentId === null)
      ) {
        alert('请填写上级指标!')
        return
      }
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          this.loading = true

          if (this.editType === 'edit') {
            if (!this.url || !this.url.edit) {
              this.$message.warning('请设置url.edit属性!')
              this.loading = false
              return
            }
            this.submitBefore()
            post(this.url.edit, this.formData)
              .then(res => {
                if (res.success) {
                  this.submitAfter(res)
                  this.$emit('refresh-tree') //刷新树组件
                  this.$message.success(res.data)
                  this.close()
                }
              })
              .finally(() => {
                this.loading = false
              })
          } else if (this.editType === 'add') {
            if (!this.url || !this.url.add) {
              this.$message.warning('请设置url.add属性!')
              this.loading = false
              return
            }
            this.submitBefore()
            post(this.url.add, this.formData)
              .then(res => {
                if (res.success) {
                  this.submitAfter(res)
                  this.$emit('refresh-tree') //刷新树组件
                  this.$message.success(res.data)
                  this.close()
                } else {
                  this.$message.error(res.message)
                }
              })
              .finally(() => {
                this.loading = false
              })
          }
        })
      }
    }
  }
}
</script>
<style scoped>
</style>
