<template>
  <div class="bars-chart-group">
    <div 
      v-for="(item, index) in chartData" 
      :key="index"
      class="bars-chart-container">
      <!-- 显示当前最大值，帮助用户理解y轴刻度 -->
      <div class="max-value">{{ Math.round(item.maxValue) }}</div>
      <bars-chart
        :chart-data="[{ name: item.title, data: item.data }]"
        :x-data="getXData(item)"
        :show-legend="false"
        :show-label="false"
        :bar-background="true"
        :bar-width="20"
        :height="height"
        :label-rotate="0"
        :unit="item.unit"
        :y-axis-max="item.maxValue"
        :hide-axis-name="true"
        @barDataUpdated="(total) => handleBarDataUpdate(index, total)"/>
    </div>
  </div>
</template>

<script>
import BarsChart from '@/pages/screen/MtPlateRollsFacDispatch/component/bars-chart.vue'

export default {
  name: 'BarsChartGroup',
  components: {
    BarsChart
  },
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    height: {
      type: Number,
      default: 240
    },
    // 新增属性来区分是主线还是热处理
    isHeatTreatment: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    getXData(item) {
      return item.data ? item.data.map(d => d.name) : []
    },
    handleBarDataUpdate(index, total) {
      // 只有在非热处理模式下才发出事件更新卡片值
      if (!this.isHeatTreatment) {
        this.$emit('totalUpdated', { index, total })
      }
    }
  }
}
</script>

<style scoped lang="less">
.bars-chart-group {
  display: flex;
  width: 100%;
  height: 100%;

  .bars-chart-container {
    flex: 1;
    height: 100%;
    position: relative;

    .max-value {
      display: none;
      position: absolute;
      top: 0;
      left: 5px;
      color: red;
      font-size: 12px;
      z-index: 2;
      background-color: rgba(4, 26, 33, 0.7);
      padding: 2px 4px;
      border-radius: 2px;
    }
  }
}
</style>
