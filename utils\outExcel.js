export const outExcel = (exportUrl, outData, exportName) => {
  return window.$nuxt
    .$axios({
      method: 'post',
      url: exportUrl,
      data: outData,
      responseType: 'blob'
    })
    .then(res => {
      var blob = new Blob([res.data], {
        type: 'application/actet-stream;charset=utf-8'
      })
      var contentDisposition = res.headers['content-disposition'] //从response的headers中获取filename, 后端response.setHeader("Content-disposition", "attachment; filename=xxxx.docx") 设置的文件名;
      var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
      var result = patt.exec(contentDisposition)
      var filename = exportName
      var downloadElement = document.createElement('a')
      var href = window.URL.createObjectURL(blob) //创建下载的链接
      downloadElement.style.display = 'none'
      downloadElement.href = href
      downloadElement.download = filename //下载后文件名
      document.body.appendChild(downloadElement)
      downloadElement.click() //点击下载
      document.body.removeChild(downloadElement) //下载完成移除元素
      window.URL.revokeObjectURL(href) //释放掉blob对象
      return res // Return the response for chaining
    })
}
