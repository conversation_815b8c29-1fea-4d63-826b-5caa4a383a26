<template>
  <div class="page-content">
    <div class="page-operate">
      <div class="">
        <el-button
          icon="el-icon-back"
          type="primary"
          @click="close"
        >返回
        </el-button>
      </div>
      <div class="search-wrapper">
        <el-form
          ref="searchForm"
          size="mini"
          inline
        >
          <el-form-item
            prop="name"
          >
            <el-date-picker
              v-model="searchValue"
              :clearable="false"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="right"
              @change="queryEcharts()"/>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              type="primary"
              @click="queryEcharts()"
            >查询
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div
      style="padding-bottom: 20px">
      <div style="height: 320px; margin-bottom: 20px">
        <el-row 
          :gutter="25" 
          class="full-height">
          <el-col
            :span="8"
            class="full-height">
            <div
              class="full-height overflow-auto page-card shadow-light">
              <div
                ref="tableTeamWrapper"
                class="full-height">
                <el-table
                  v-loading="loading"
                  :data="shiftData"
                  :size="'small'"
                  :max-height="teamMaxHeight"
                  :row-class-name="getRowClass"
                  @row-click="getShiftDetailBy1903">
                  <el-table-column
                    width="60"
                    align="center"
                    label="班组"
                    prop="shift"/>
                  <el-table-column
                    width="60"
                    align="center"
                    label="块数/块"
                    prop="count"/>
                  <el-table-column
                    width="100"
                    align="center"
                    label="机时块数/块"
                    prop="numhour"/>
                  <el-table-column
                    width="100"
                    align="center"
                    label="生产时间/min"
                    prop="workTime"/>
                  <el-table-column
                    width="100"
                    align="center"
                    label="停产时间/min"
                    prop="stopWorkTime"/>
                  <el-table-column
                    width="100"
                    align="center"
                    label="总时间/min"
                    prop="allTime"/>
                  <el-table-column
                    width="100"
                    align="center"
                    label="作业率/%"
                    prop="wokeRate"/>
                  <el-table-column
                    width="80"
                    align="center"
                    label="停机率/%"
                    prop="stopRate"/>
                  <el-table-column
                    width="80"
                    align="center"
                    label="块数贡献值"
                    prop="blockContributeValue"/>
                  <el-table-column
                    width="80"
                    align="center"
                    label="平均机时块数贡献值"
                    prop="avgMachineBlockContrVal"/>
                  <el-table-column
                    width="80"
                    align="center"
                    label="平均每班块数贡献值"
                    prop="avgTeamBlockContrVal"/>
                </el-table>
              </div>
            </div>
          </el-col>
          <el-col
            :span="16"
            class="full-height">
            <div 
              class="page-card shadow-light full-height" 
              style="padding: 1px">
              <div
                id="hourOutPutEcharts"
                class="stat"/>
            </div>
          </el-col>
        </el-row>
      </div>
      <div
        ref="tableWrapper"
        class="page-card shadow-light"
        style="margin-bottom: 20px">
        <div>
          <el-form
            ref="searchForm"
            size="mini"
            inline
          >
            <el-form-item
              prop="name"
            >
              <el-input
                v-model="searchKey"
                placeholder="输入板坯号搜索"
                @input="currentPage = 1"/>
            </el-form-item>
            <el-form-item
              prop="name"
            >
              <el-input
                v-model="searchDeviation"
                placeholder="输入贡献偏差值"
                @input="currentPage = 1"/>
            </el-form-item>
            <el-form-item
              :label="'是否可信:'"
              prop="name"
            >
              <el-radio 
                v-model="searchMoReliable" 
                :label="true">是</el-radio>
              <el-radio 
                v-model="searchMoReliable" 
                :label="false">否</el-radio>
              <el-radio 
                v-model="searchMoReliable" 
                :label="''">全部</el-radio>
            </el-form-item>
            <el-form-item
              :label="'适配结果:'"
              prop="name"
            >
              <el-select 
                v-model="dealResult" 
                clearable>
                <el-option 
                  v-for="(item, index) in matchList"
                  :key="index"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              prop="standardNo"
            >
              <el-input
                v-model="searchForm.otherSteelType"
                suffix-icon="el-icon-search"
                clearable
                placeholder="输入国标钢种号"
                style="width:120px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              prop="standardNo"
            >
              <el-input
                v-model="searchForm.standardNo"
                suffix-icon="el-icon-search"
                clearable
                placeholder="输入轧制钢种"
                style="width:120px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              prop="standardNo"
              label="厚度区间"
            >
              <el-input-number
                v-model="searchForm.thicknessDown"
                :min="0"
                :max="searchForm.thicknessUp"
                :controls="false"
                style="width: 70px"/>
              —
              <el-input-number
                v-model="searchForm.thicknessUp"
                :min="searchForm.thicknessDown || 0"
                :max="100000"
                :controls="false"
                style="width: 70px"/>
            </el-form-item>
            <el-form-item
              prop="widthDown"
              label="宽度区间"
            >
              <el-input-number
                v-model="searchForm.widthDown"
                :min="0"
                :max="searchForm.widthUp"
                :controls="false"
                style="width: 70px"/>
              —
              <el-input-number
                v-model="searchForm.widthUp"
                :min="searchForm.widthDown || 0"
                :max="100000"
                :controls="false"
                style="width: 70px"/>
            </el-form-item>
            <el-form-item>
              <el-button
                icon="el-icon-search"
                type="primary"
                @click="exportDetailExcel()"
              >导出
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table
          v-loading="loading"
          :data="detailData"
          :cell-style="cellStyle"
          :row-class-name="getRowClass"
          max-height="450"
          @cell-dblclick="selectBySpec">
          <el-table-column
            width="100"
            label="板坯号"
            align="center"
            property="slabNo"
            fixed/>
          <el-table-column
            width="140"
            align="center"
            label="轧制开始时间"
            property="millStaDate"
            fixed/>
          <el-table-column
            width="140"
            align="center"
            label="轧制结束时间"
            property="millEndDate"
            fixed/>
          <el-table-column
            width="80"
            align="center"
            label="班组"
            property="team">
            <template v-slot="{ row }">
              {{ getDict(row.team, 'teamList').label }}
            </template>
          </el-table-column>
          <el-table-column
            width="80"
            align="center"
            label="保性能"
            property="matrFl">
            <template v-slot="{ row }">
              <el-tag
                v-if="row.matrFl !== null"
                :type="getDict(row.matrFl, 'matrFlList').tag"
                disable-transitions>{{ getDict(row.matrFl, 'matrFlList').label }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            width="100"
            align="center"
            label="国标钢种"
            property="modelGrade"/>
          <el-table-column
            show-overflow-tooltip
            width="160"
            align="center"
            label="轧制钢种"
            property="standardNo"/>
          <el-table-column
            show-overflow-tooltip
            width="120"
            align="center"
            label="坯料钢种"
            property="steelType"/>
          <el-table-column
            width="80"
            align="center"
            label="订单厚度"
            property="thickness"/>
          <el-table-column
            width="80"
            align="center"
            label="订单宽度"
            property="width"/>
          <el-table-column
            show-overflow-tooltip
            width="100"
            align="center"
            label="适配结果"
            property="dealResult">
            <template v-slot="{ row }">
              {{ getDict(row.dealResult, 'matchList').label }}
            </template>
          </el-table-column>
          <el-table-column
            width="80"
            align="center"
            label="厚度适配"
            property="thkAdapt"/>
          <el-table-column
            width="80"
            align="center"
            label="厚度适配率"
            property="thkDifRate"/>
          <el-table-column
            width="80"
            align="center"
            label="宽度适配"
            property="widAdapt"/>
          <el-table-column
            width="80"
            align="center"
            label="宽度适配率"
            property="widDifRate"/>
          <el-table-column
            width="80"
            align="center"
            label="坯料厚度"
            property="slabThk"/>
          <el-table-column
            width="100"
            align="center"
            label="轧制模式"
            property="rollingMode">
            <template v-slot="{ row }">
              {{ getRollingMode(row.rollingMode) }}
            </template>
          </el-table-column>
          <el-table-column
            width="80"
            align="center"
            label="加热模式"
            property="heatCode">
            <template v-slot="{ row }">
              {{ row.heatCode == 0 ? '单炉' : '双炉' }}
            </template>
          </el-table-column>
          <el-table-column
            width="80"
            label="子板数量"
            align="center"
            property="subNum"/>
          <el-table-column
            width="80"
            label="子板重量"
            align="center"
            property="subWgt"/>
          <el-table-column
            width="100"
            label="历史轧制次数"
            align="center"
            property="mergeSlabNum"/>
          <el-table-column
            width="100"
            align="center"
            label="历史轧制重量"
            property="mergeSlabWgt"/>
          <el-table-column
            width="80"
            align="center"
            label="板坯重量"
            property="slabWgt"/>
          <el-table-column
            width="80"
            align="center"
            label="间隔时间"
            property="intervalTime"/>
          <el-table-column
            width="80"
            align="center"
            label="纯轧时间"
            property="rollTime"/>
          <el-table-column
            width="80"
            align="center"
            label="节奏时间"
            property="paceTime"/>
          <el-table-column
            width="100"
            align="center"
            label="实际机时产量"
            class-name="color-y"
            property="realMachineOutput"/>
          <el-table-column
            width="100"
            align="center"
            label="计划机时产量"
            class-name="color-y"
            property="planMachineOutput"/>
          <el-table-column
            width="80"
            label="贡献值"
            align="center"
            class-name="color-y"
            property="contributeValue"/>
          <el-table-column
            width="80"
            label="可信度"
            align="center"
            property="moReliable">
            <template v-slot="{ row }">
              <el-tag
                v-if="row.moReliable !== null"
                :type="getDict(row.moReliable, 'moReliableList').tag"
                disable-transitions>{{ getDict(row.moReliable, 'moReliableList').label }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            width="100"
            align="center"
            label="近一年最好值"
            property="bestInMat"/>
          <el-table-column
            width="100"
            align="center"
            label="近一年最差值"
            property="worstInMat"/>
          <el-table-column
            width="100"
            align="center"
            label="近一年中位数"
            property="midInMat"/>
          <el-table-column
            width="80"
            label="实际机时块数"
            align="center"
            property="realMachineBlocks"/>
          <el-table-column
            width="80"
            label="计划机时块数"
            align="center"
            property="planMachineBlocks"/>
          <el-table-column
            width="80"
            label="块数贡献值"
            align="center"
            property="blockContributeValue"/>
          <el-table-column
            width="80"
            label="机时块数贡献值"
            align="center"
            property="blockContributeValueHour"/>
          <el-table-column
            width="100"
            align="center"
            label="近一年机时块数最好值"
            property="bestMachineBlocks"/>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40, 50, 100, 150]"
          :page-size="pageSize"
          :total="recordCount"
          background
          align="right"
          layout="total, sizes, prev, pager, next, jumper"
          style="margin-top: 5px"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </div>
      <div
        class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="summaryData"
          :summary-method="getSummaries"
          show-summary
          @cell-dblclick="selectBymillDate">
          <el-table-column
            width="100"
            label="日期"
            align="center"
            property="millDate"/>
          <el-table-column
            width="150"
            align="center"
            label="实际块数"
            property="realBlocks"/>
          <el-table-column
            width="150"
            align="center"
            label="计划块数"
            property="planBlocks"/>
          <el-table-column
            width="150"
            align="center"
            label="块数贡献值"
            property="blockContributeValue"/>
          <el-table-column
            align="center"
            label="实际产量/t"
            property="designWgt"/>
          <el-table-column
            align="center"
            label="预测产量/t"
            property="planDesignWgt"/>
          <el-table-column
            align="center"
            label="贡献值/t"
            property="contributeValue"/>
          <el-table-column
            width="150"
            align="center"
            label="偏差率/%"
            property="contributeRate"/>
        </el-table>
      </div>
      <el-dialog
        :visible.sync="showExportEcharts"
        :title="'(板坯号：' + currentSlabNo.slabNo + '）此品规历史数据  趋势图'"
        width="70%"
        @open="open()"
      >
        <p class="text-right">
          <el-radio-group 
            v-model="specActive" 
            @change="setSpec">
            <el-radio-button label="realMachineOutput">机时产量</el-radio-button>
            <el-radio-button label="machineBlock">机时块数</el-radio-button>
          </el-radio-group>
        </p>
        <div
          id="specEcharts"
          style="width:100%;height:500px"
        />
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import {
  analysisEvaluationFind,
  getContributeValSummary,
  getSubSlabHistoryById,
  getTeamProdOverview,
  rollingSteelActualFind
} from '@/api/kpi'
import * as _ from 'lodash'

export default {
  name: 'outputSearch',
  props: {
    selectDate: {
      type: String,
      default: ''
    },
    team: {
      type: String,
      default: ''
    },
    factory: {
      type: Number,
      default: 0
    },
    period: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showExportEcharts: false,
      msgNum: 0,
      specEcharts: null,
      specList: [],
      specActive: 'realMachineOutput',
      specOptions: {
        title: { left: 'center', text: '机时产量趋势图' },
        grid: {
          right: '10px',
          left: '14px',
          containLabel: true
          // bottom: '60%'
        },
        tooltip: {
          trigger: 'axis',
          confine: false,
          axisPointer: {
            type: 'cross'
          },
          formatter: params => {
            // console.log(params)
            const data1 = params[0].data.value
            if (data1[17]) return ''
            const el = `
            ${data1[2]} <br>
            板坯号：${data1[3]} <br>
            <table class="tool-table">
            <tr>
              <td>订单规格</td>
              <td>${data1[5]}*${data1[6]}*${data1[7]}*${data1[8]}*${
              data1[9]
            }</td>
            </tr>
            <tr>
              <td>机时产量[t/h]</td>
              <td>${data1[18]}</td>
            </tr>
            <tr>
              <td>保性能</td>
              <td>${this.getDict(data1[11], 'matrFlList').label}</td>
            </tr>
            <tr>
              <td>轧制钢种</td>
              <td>${data1[12]}</td>
            </tr>
            <tr>
              <td>坯料钢种</td>
              <td>${data1[13]}</td>
            </tr>
            <tr>
              <td>坯料厚度</td>
              <td>${data1[14]}</td>
            </tr>
            <tr>
              <td>轧制模式</td>
              <td>${this.getRollingMode(data1[15])}</td>
            </tr>
            <tr>
              <td>加热模式</td>
              <td>${data1[16] == 0 ? '单炉' : '双炉'}</td>
            </tr>
            <tr>
              <td>机时块数[块/h]</td>
              <td>${data1[4]}</td>
            </tr>
            </table>
            `
            return el
          }
        },
        dataZoom: [
          {
            show: true,
            realtime: true,
            height: 15, //这里可以设置dataZoom的尺寸
            start: 0,
            end: 100
          },
          {
            type: 'inside',
            realtime: true,
            start: 0,
            end: 100
          }
        ],
        toolbox: {
          feature: {
            // dataView: { show: true, readOnly: false },
            // restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          scale: true,
          min: 0,
          name: '产量[t/h]',
          position: 'left',
          alignTicks: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#5470C6'
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
        series: [
          {
            name: '机时产量[t/h]',
            type: 'bar',
            barMaxWidth: 60,
            // data: []
            data: []
          }
        ]
      },
      searchKey: '',
      searchDeviation: '',
      searchMoReliable: '',
      searchForm: {},
      shift: '',
      loading: false,
      totalData: [], // 全量数据1
      shiftData: [],
      summaryData: [], // 汇总数据
      tableMaxHeight: null,
      teamMaxHeight: null,
      currentPage: 1,
      pageSize: 10,
      selectSlabNo: '',
      dealResult: null,
      searchValue: null,
      activeId: null,
      hourOutPutEcharts: null,
      hourOutPutOption: {
        // legend: ['产量[t/h]', '参考产量[t/h]', '累计提高产量[t]'],
        tooltip: {
          trigger: 'axis',
          confine: true,
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            console.log(params)
            const groups = _.groupBy(params, 'seriesName')
            const $els = {}
            $els['output'] = groups['output']
              .map(item => `<td>${item.data[1]}</td>`)
              .join('')
            $els['size'] = groups['output']
              .map(
                item =>
                  `<td>${item.data[3]}*${item.data[4]}*${item.data[5]}*${
                    item.data[6]
                  }*${item.data[7]}</td>`
              )
              .join('')
            $els['planOutput'] = groups['planOutput']
              .map(item => `<td>${item.data[1] || 0}</td>`)
              .join('')
            $els['bestOutput'] = groups['bestOutput']
              .map(item => `<td>${item.data[1] || 0}</td>`)
              .join('')
            $els['worstOutput'] = groups['worstOutput']
              .map(item => `<td>${item.data[1] || 0}</td>`)
              .join('')
            $els['contributeValue'] = groups['contribute']
              .map(item => `<td>${item.data[1]}</td>`)
              .join('')
            $els['blocks'] = groups['contribute']
              .map(item => `<td>${item.data[2]}</td>`)
              .join('')
            const el = `
            ${params[0].data[0]} <br>
            板坯号：${params[0].data[2]} <br>
            <table class="tool-table">
            <tr>
              <td>订单规格</td>
              ${$els['size']}
            </tr>
            <tr>
              <td>实际机时产量[t/h]</td>
              ${$els['output']}
            </tr>
            <tr>
              <td>计划机时产量[t/h]</td>
              ${$els['planOutput']}
            </tr>
            <tr>
              <td>近一年最好值[t/h]</td>
              ${$els['bestOutput']}
            </tr>
            <tr>
              <td>近一年最差值[t/h]</td>
              ${$els['worstOutput']}
            </tr>
            <tr>
              <td>机时块数[块/h]</td>
              ${$els['blocks']}
            </tr>
            <tr>
              <td>贡献值[t]</td>
              ${$els['contributeValue']}
            </tr>
            </table>
            `
            return el
          }
        },
        grid: {
          right: '15px',
          left: '5px',
          bottom: '60px',
          containLabel: true
        },
        dataZoom: [
          {
            show: true,
            realtime: true,
            start: 0,
            end: 10
          },
          {
            type: 'inside',
            realtime: true,
            start: 0,
            end: 10
          }
        ],
        toolbox: {
          feature: {
            // dataView: { show: true, readOnly: false },
            // restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        xAxis: [
          {
            name: 'times',
            type: 'time',
            axisTick: {
              alignWithLabel: true
            },
            min: '',
            max: ''
          }
        ],
        yAxis: [
          {
            type: 'value',
            scale: true,
            name: '产量[t/h]',
            position: 'left',
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#5470C6'
              }
            },
            axisLabel: {
              formatter: '{value} t/h'
            }
          },
          {
            type: 'value',
            scale: true,
            name: '贡献值[t]',
            position: 'right',
            alignTicks: true,
            // offset: 80,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#fe8104'
              }
            },
            axisLabel: {
              formatter: '{value} t'
            }
          }
        ],
        series: [
          {
            name: 'output',
            type: 'bar',
            yAxisIndex: 0,
            data: []
          },
          {
            name: 'planOutput',
            type: 'scatter',
            yAxisIndex: 0,
            data: []
          },
          {
            name: 'contribute',
            type: 'line',
            yAxisIndex: 1,
            data: [],
            currentGroupId: ''
          },
          {
            name: 'bestOutput',
            type: 'scatter',
            yAxisIndex: 0,
            data: []
          },
          {
            name: 'worstOutput',
            type: 'scatter',
            yAxisIndex: 0,
            data: []
          }
        ]
      },
      teamList: [
        {
          label: '甲',
          value: 'A'
        },
        {
          label: '乙',
          value: 'B'
        },
        {
          label: '丙',
          value: 'C'
        },
        {
          label: '丁',
          value: 'D'
        },
        {
          label: '汇总',
          value: ''
        }
      ],
      // 性能要求列表
      matrFlList: ENUM.matrFlList,
      moReliableList: ENUM.moReliableList,
      matchList: ENUM.matchType8,
      currentSlabNo: {}
    }
  },
  computed: {
    filterTable: function() {
      return this.totalData.filter(
        item =>
          (!this.searchKey || item.slabNo.indexOf(this.searchKey) !== -1) &&
          (!this.searchDeviation ||
            (item.contributeValue <= -1 * Math.abs(this.searchDeviation) ||
              item.contributeValue >= Math.abs(this.searchDeviation))) &&
          (this.searchMoReliable === '' ||
            item.moReliable === this.searchMoReliable) &&
          (this.dealResult === null ||
            this.dealResult === '' ||
            item.dealResult === this.dealResult) &&
          (!this.searchForm.standardNo ||
            item.standardNo.indexOf(this.searchForm.standardNo) !== -1) &&
          (!this.searchForm.steelType ||
            item.modelGrade.indexOf(this.searchForm.steelType) !== -1) &&
          (!this.searchForm.thicknessUp ||
            item.thickness <= this.searchForm.thicknessUp) &&
          (!this.searchForm.thicknessDown ||
            item.thickness >= this.searchForm.thicknessDown) &&
          (!this.searchForm.widthUp || item.width <= this.searchForm.widthUp) &&
          (!this.searchForm.widthDown ||
            item.width >= this.searchForm.widthDown)
      )
    },
    recordCount: function() {
      return this.filterTable.length
    },
    detailData: function() {
      return this.filterTable
        .slice(
          (this.currentPage - 1) * this.pageSize,
          this.currentPage * this.pageSize
        )
        .map(item => {
          // 计算机时块数贡献值
          item.blockContributeValueHour = math.subtract(
            item.realMachineBlocks,
            item.planMachineBlocks
          )
          return item
        })
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  mounted() {
    // 初始化时间
    if (this.selectDate) {
      this.searchValue = [
        this.$moment(this.selectDate).format('yyyy-MM-DD'),
        this.$moment(this.selectDate).format('yyyy-MM-DD')
      ]
    } else {
      this.searchValue = [
        this.$moment()
          .subtract(1, 'day')
          .format('yyyy-MM-DD'),
        this.$moment()
          .subtract(1, 'day')
          .format('yyyy-MM-DD')
      ]
    }

    this.$nextTick(() => {
      // 初始化图表
      this.hourOutPutEcharts = this.$echarts.init(
        document.getElementById('hourOutPutEcharts')
      )
      this.hourOutPutEcharts.on('click', async param => {
        console.log(param)
        const slabNo = param.data[2]
        this.selectSlabNo = slabNo
        const $index = this.totalData.findIndex(item => item.slabNo === slabNo)
        this.currentPage = parseInt($index / 8) + 1
        this.handleCurrentChange(this.currentPage)
      })
      window.addEventListener('resize', this.resizeChart)
      this.hourOutPutEcharts.setOption(this.hourOutPutOption)
    })
    this.tableMaxHeight = this.$refs.tableWrapper.offsetHeight - 70
    this.teamMaxHeight = this.$refs.tableTeamWrapper.offsetHeight

    // 匹配班组数据
    const match = this.teamList.find(item => this.team === item.value)
    this.shift = match ? match.label : '汇总'

    // 查询图表数据
    this.queryEcharts()
  },
  methods: {
    close() {
      this.$emit('cancel')
    },
    async pyShiftStat(start, end, team) {
      const end1 = this.$moment(end)
        .subtract(-1, 'day')
        .format('yyyy-MM-DD')
      let data = await post(getTeamProdOverview, {
        startDate: start,
        endDate: end1,
        factory: this.factory
      })
      this.shiftData = data.data
      let summary = await post(getContributeValSummary, {
        startDate: start,
        endDate: end,
        factory: this.factory,
        team
      })
      this.summaryData = summary.data
    },
    async getShiftDetailBy1903(row, column, event) {
      this.shift = row.shift
      this.queryEcharts()
    },
    async queryEcharts() {
      this.loading = true
      const match = this.teamList.find(item => this.shift === item.label)
      const team = match ? match.value : ''
      this.pyShiftStat(this.searchValue[0], this.searchValue[1], team)
      let data = await post(rollingSteelActualFind, {
        startDate: this.searchValue[0],
        endDate: this.searchValue[1],
        team,
        factory: this.factory,
        page: 0,
        size: 100000
      })
      this.hourOutPutOption.xAxis[0].min = ''
      this.hourOutPutOption.xAxis[0].max = ''
      this.hourOutPutOption.series[0].data = []
      this.hourOutPutOption.series[1].data = []
      this.hourOutPutOption.series[2].data = []
      this.hourOutPutOption.series[3].data = []
      this.hourOutPutOption.series[4].data = []
      this.loading = false
      if (data.data.content.length) {
        this.selectSlabNo = null
        //console.log(data.data[0], '曲线数据')
        let count = data.data.content.length
        let dataArray = data.data.content.map(item => {
          if (!item.moReliable) {
            item.planMachineOutput = item.realMachineOutput
            item.contributeValue = 0
          }
          return item
        })
        this.hourOutPutOption.xAxis[0].min = dataArray[0].millEndDate
        this.hourOutPutOption.xAxis[0].max = dataArray[count - 1].millEndDate
        let sum = 0
        let yeild = 0
        for (let i = 0; i < count; i++) {
          if (dataArray[i].millStartDate === 'null') {
            dataArray[i].millStartDate = ''
          }
          if (dataArray[i].millEndDate === 'null') {
            dataArray[i].millEndDate = ''
          }
          // 实绩产量
          this.hourOutPutOption.series[0].data.push([
            dataArray[i].millEndDate,
            dataArray[i].realMachineOutput,
            dataArray[i].slabNo, // 板坯号
            dataArray[i].thickness, // 订单厚度
            dataArray[i].width, // 订单宽度
            dataArray[i].length, // 订单长度
            dataArray[i].subWgt, // 订单重量
            dataArray[i].subNum // 订单数量
          ])
          // 目标产量
          this.hourOutPutOption.series[1].data.push([
            dataArray[i].millEndDate,
            dataArray[i].planMachineOutput,
            dataArray[i].slabNo // 板坯号
          ])
          // 一年最好值
          this.hourOutPutOption.series[3].data.push([
            dataArray[i].millEndDate,
            dataArray[i].bestInMat,
            dataArray[i].slabNo // 板坯号
          ])
          // 一年最差值
          this.hourOutPutOption.series[4].data.push([
            dataArray[i].millEndDate,
            dataArray[i].worstInMat,
            dataArray[i].slabNo // 板坯号
          ])
          // 贡献值
          yeild = (3600 / parseFloat(dataArray[i].paceTime)).toFixed(2) // 机时块数
          dataArray[i].yeild = yeild
          this.hourOutPutOption.series[2].data.push([
            dataArray[i].millEndDate,
            dataArray[i].contributeValue,
            dataArray[i].yeild
          ])
        }
        //console.log(this.hourOutPutOption.series[2], 'shuju')
        this.totalData = dataArray
        this.hourOutPutEcharts.setOption(this.hourOutPutOption)
        //console.log('个数', count)
        //console.log('开始时间', data.data[0].END_TIME2)
        //console.log('结束时间', data.data[count - 1].END_TIME2)
      } else {
        this.$message({
          message: '查询无数据',
          type: 'warning'
        })
      }
      this.loading = false
      //this.shiftData = data.data
      //console.log(data.data, '接口数据')
    },
    exportDetailExcel() {
      if (!window.LAY_EXCEL) {
        const a = require('lay-excel')
      }
      const data = [
        {
          slabNo: '板坯号',
          millStaDate: '轧制开始时间',
          millEndDate: '轧制结束时间',
          team: '班组',
          matrFl: '保性能',
          modelGrade: '国标钢种',
          standardNo: '轧制钢种',
          steelType: '坯料钢种',
          thickness: '订单厚度',
          width: '订单宽度',
          dealResult: '适配结果',
          thkAdapt: '厚度适配',
          thkDifRate: '厚度适配率',
          widAdapt: '宽度适配',
          widDifRate: '宽度适配率',
          rollingMode: '轧制模式',
          heatCode: '加热模式',
          subNum: '子板数量',
          subWgt: '子板重量',
          mergeSlabNum: '历史轧制次数',
          mergeSlabWgt: '历史轧制重量',
          intervalTime: '间隔时间',
          rollTime: '纯轧时间',
          paceTime: '节奏时间',
          realMachineOutput: '实际机时产量',
          planMachineOutput: '计划机时产量',
          contributeValue: '贡献值',
          moReliable: '可信度',
          bestInMat: '近一年最好值',
          worstInMat: '近一年最差值',
          midInMat: '近一年中位数',
          realMachineBlocks: '实际机时块数',
          planMachineBlocks: '计划机时块数',
          blockContributeValue: '块数贡献值',
          blockContributeValueHour: '机时块数贡献值',
          bestMachineBlocks: '近一年机时块数最好值'
        }
      ].concat(
        _.cloneDeep(this.filterTable).map((item, index) => {
          item.team = this.getDict(item.team, 'teamList').label
          item.dealResult = this.getDict(item.dealResult, 'matchList').label
          item.matrFl = this.getDict(item.matrFl, 'matrFlList').label
          item.moReliable = this.getDict(
            item.moReliable,
            'moReliableList'
          ).label
          item.heatCode = item.heatCode == 0 ? '单炉' : '双炉'
          item.rollingMode = this.getRollingMode(item.rollingMode)
          return {
            slabNo: item.slabNo,
            millStaDate: item.millStaDate,
            millEndDate: item.millEndDate,
            team: item.team,
            matrFl: item.matrFl,
            modelGrade: item.modelGrade,
            standardNo: item.standardNo,
            steelType: item.steelType,
            thickness: item.thickness,
            width: item.width,
            dealResult: item.dealResult,
            thkAdapt: item.thkAdapt,
            thkDifRate: item.thkDifRate,
            widAdapt: item.widAdapt,
            widDifRate: item.widDifRate,
            rollingMode: item.rollingMode,
            heatCode: item.heatCode,
            subNum: item.subNum,
            subWgt: item.subWgt,
            mergeSlabNum: item.mergeSlabNum,
            mergeSlabWgt: item.mergeSlabWgt,
            intervalTime: item.intervalTime,
            rollTime: item.rollTime,
            paceTime: item.paceTime,
            realMachineOutput: item.realMachineOutput,
            planMachineOutput: item.planMachineOutput,
            contributeValue: item.contributeValue,
            moReliable: item.moReliable,
            bestInMat: item.bestInMat,
            worstInMat: item.worstInMat,
            midInMat: item.midInMat,
            realMachineBlocks: item.realMachineBlocks,
            planMachineBlocks: item.planMachineBlocks,
            blockContributeValue: item.blockContributeValue,
            blockContributeValueHour: math.subtract(
              item.realMachineBlocks,
              item.planMachineBlocks
            ),
            bestMachineBlocks: item.bestMachineBlocks
          }
        })
      )
      const len = data.length
      LAY_EXCEL.setExportCellStyle(
        data,
        'A1:O' + len - 1,
        {
          s: {
            border: {
              // 设置边框
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center'
            }
          }
        },
        function(cell, newCell, row, config, currentRow, currentCol, fieldKey) {
          // 回调参数，cell:原有数据，newCell:根据批量设置规则自动生成的样式，row:所在行数据，config:传入的配置,currentRow:当前行索引,currentCol:当前列索引，fieldKey:当前字段索引
          return newCell
        }
      )
      LAY_EXCEL.exportExcel(data, `生产效率评价明细.xlsx`, 'xlsx', {
        extend: {
          // extend 中可以指定某个 sheet 的属性，如果不指定 sheet 则所有 sheet 套用同一套属性
          sheet1: {
            '!merges': LAY_EXCEL.makeMergeConfig([])
          }
        }
      })
    },
    ///表格点击弹框
    resizeChart() {
      this.hourOutPutEcharts && this.hourOutPutEcharts.resize()
    },
    open() {
      this.$nextTick(() => {
        //  执行echarts方法
        //console.log(document.getElementById('specEcharts'), 'dom')
        this.specEcharts = this.$echarts.init(
          document.getElementById('specEcharts')
        )
      })
    },
    async selectBySpec(row) {
      this.currentSlabNo = row
      this.showExportEcharts = true
      let data = await post(getSubSlabHistoryById, {
        id: row.id
      })
      this.specList =
        data.data.map(item => {
          item.machineBlock = Number(
            (3600 / parseFloat(item.paceTime)).toFixed(2)
          )
          return item
        }) || []
      this.setSpec()
    },
    setSpec() {
      const keyName = this.specActive
      //console.log('根据钢种查询', data.data.length)
      this.specOptions.xAxis.data = []
      this.specOptions.series[0].data = []
      if (this.specList.length) {
        let max = this.specList[0][keyName]
        let min = this.specList[0][keyName]
        const middleIndexs = []
        const showArr = []
        const group = _.groupBy(this.specList, 'spareField')
        Object.keys(group).forEach(key => {
          const arr = _.sortBy(group[key], function(o) {
            return o[keyName]
          })
          console.log(arr)
          // 记录中位数
          if (arr.length > 2) {
            if (arr.length % 2 === 1) {
              middleIndexs.push(arr[parseInt(arr.length / 2)].id)
            } else {
              middleIndexs.push(arr[parseInt(arr.length / 2) - 1].id)
              middleIndexs.push(arr[parseInt(arr.length / 2)].id)
            }
          }
          // 插入中间数据
          showArr.push(...arr)
          showArr.push({ hidden: true })
          this.specOptions.xAxis.data.push(
            ...arr.map((item, index) => index + 1)
          )
          this.specOptions.xAxis.data.push('')
        })
        showArr.pop()
        this.specOptions.xAxis.data.pop()
        for (let i = 0; i < showArr.length; i++) {
          if (Number(max) < Number(showArr[i][keyName]))
            max = Number(showArr[i][keyName])
          if (Number(min) > Number(showArr[i][keyName]))
            min = Number(showArr[i][keyName])
          this.specOptions.series[0].data.push({
            value: [
              i,
              showArr[i][keyName],
              showArr[i].millEndDate,
              showArr[i].slabNo, // 机时块数
              showArr[i].machineBlock,
              showArr[i].thickness, // 订单厚度
              showArr[i].width, // 订单宽度
              showArr[i].length, // 订单长度
              showArr[i].subWgt, // 订单重量
              showArr[i].subNum, // 订单数量
              showArr[i].contributeValue,
              showArr[i].matrFl,
              showArr[i].standardNo,
              showArr[i].steelType,
              showArr[i].slabThk,
              showArr[i].rollingMode,
              showArr[i].heatCode,
              !!showArr[i].hidden,
              showArr[i].realMachineOutput
            ],
            itemStyle: {
              color: middleIndexs.includes(showArr[i].id)
                ? '#30934f'
                : '#5470c6'
            }
          })
        }
        if (this.specList.length <= 2) {
          min = 0
        }
        this.specOptions.yAxis.min = Math.round(
          Number(max - (max - min) * 5 > 0 ? max - (max - min) * 5 : 0)
        )
        this.specOptions.yAxis.name =
          this.specActive === 'realMachineOutput' ? '产量[t/h]' : '产量[块/h]'
        this.specOptions.title.text =
          this.specActive === 'realMachineOutput'
            ? '机时产量趋势图'
            : '机时块数趋势图'
      }
      this.specEcharts.setOption(this.specOptions)
    },
    // 双击
    selectBymillDate(row) {
      this.searchValue = [row.millDate, row.millDate]
      this.queryEcharts()
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      let style = {
        padding: '0.75vh 0px 0.75vh 0px'
        // color: 'blue',
        // fontWeight: 700,
        // textAlign: 'center'
      }
      return style
    },
    cellShiftStyle({ row, column, rowIndex, columnIndex }) {
      let style = {
        padding: '1.5vh 0px 1.5vh 0px'
        // color: 'blue',
        // fontWeight: 700,
        // textAlign: 'center'
      }
      return style
    },
    getDict(value, list) {
      const match = this[list].find(item => item.value == value)
      return match ? match : {}
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.pageSize = val
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
    },
    getRollingMode(rollingMode) {
      if (rollingMode == 51) {
        return '单坯'
      } else if (rollingMode == 42) {
        return '双坯'
      } else {
        return `多坯（${rollingMode
          .toString()
          .substring(1, rollingMode.toString().length)}）`
      }
    },
    getRowClass({ row }) {
      if (this.currentSlabNo.id && row.id === this.currentSlabNo.id) {
        return 'select-row'
      }
      if (this.selectSlabNo && this.selectSlabNo === row.slabNo) {
        return 'select-row'
      }
      console.log(row.shift, this.shift)
      if (row.shift === this.shift) {
        return 'select-row'
      }
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (column.property === 'contributeRate') {
          const sumContribute = data
            .map(item => Number(item['contributeValue']))
            .reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return math.add(prev + curr)
              } else {
                return prev
              }
            }, 0)
          const sumWgt = data
            .map(item => Number(item['designWgt']))
            .reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return math.add(prev + curr)
              } else {
                return prev
              }
            }, 0)
          sums[index] = (math.divide(sumContribute, sumWgt) * 100).toFixed(2)
          return
        }
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values
            .reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return math.add(prev + curr)
              } else {
                return prev
              }
            }, 0)
            .toFixed(2)
          if (column.property === 'paceTime') {
            sums[index] = (sums[index] / data.length).toFixed(2)
          }
        } else {
          sums[index] = 'N/A'
        }
      })
      console.log(sums)
      return sums
    }
  }
}
import '@/assets/css/global.less'
import { ENUM } from '@/lib/Constant'
import { math } from '@/lib/Math'
</script>

<style scoped lang="less">
.select {
  background: white;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.1);
  height: 10%;
  font-size: 18px;
}
.stat {
  background: white;
  height: 100%;
  width: 100%;
}
.rateTable {
  height: 100%;
  /*border-style: dashed;*/
}
.DataTable {
  background: white;
  width: 100%;
  margin-top: 25px;
}
/deep/ .el-pagination__total {
  float: left;
}
/deep/ .el-pagination__sizes {
  float: left;
}
/deep/ .select-row {
  background: #91c8ff;
  .color-y {
    background: #90c8ff;
  }
}
/deep/ .color-y {
  background: #faf3b0;
}
/deep/ .tool-table {
  border: 1px solid #eee;
  td {
    border: 1px solid #ddd;
    padding: 2px 4px;
  }
}
</style>
