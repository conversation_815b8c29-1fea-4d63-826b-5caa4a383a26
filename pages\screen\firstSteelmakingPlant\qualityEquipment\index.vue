<template>
  <div class="content">
    <div class="content-item">
      <screen-border title="质量设备">
        <template v-slot:headerRight>
          <span
            v-command="'/screen/firstSteelmakingPlant/edit'"
            class="screen-btn"
            @click="unfinished.dialogVisible = true">
            <el-icon class="el-icon-edit-outline"/>
            操作
          </span>
        </template>
        <div
          ref="table1"
          class="scroll-wrapper">
          <el-table
            v-loading="loading"
            :data="unfinished.showGridData"
            :max-height="unfinished.maxHeight"
            :header-cell-style="({ row })=>{
              if (row.length != 1) {
                return 'display: none'
              }
            }"
            :span-method="spanMethod"
            class="font-table center-table"
            border>
            <el-table-column
              :label="'质量设备情况'">
              <el-table-column
                property="k1"/>
              <el-table-column
                property="k2">
                <template v-slot="{ row }">
                  <div
                    slot="content"
                    v-html="formatText(row.k2)"/>
                </template>
              </el-table-column>
              <el-table-column
                property="k3"/>
              <el-table-column
                property="k4"/>
              <el-table-column
                property="k5"/>
              <el-table-column
                property="k6"/>
              <el-table-column
                property="k7"/>
              <el-table-column
                property="k8"/>
            </el-table-column>
          </el-table>
        </div>
      </screen-border>
    </div>
    <!--热处理详情-->
    <el-dialog
      :visible.sync="unfinished.dialogVisible"
      :width="'1200px'"
      :close-on-click-modal="false"
      class="screen-dialog"
      title="质量设备">
      <template v-slot:title>
        <div class="custom-dialog-title">
          <div class="btn-box">
            <el-date-picker
              v-model="cDate"
              :clearable="false"
              :size="'mini'"
              :value-format="'yyyy-MM-dd'"
              @change="changeDate"/>
            <template
              v-if="canEditQuality">
              <el-dropdown @command="handleProcessedCommand($event, 'importUnfinishedData')">
                <span
                  command="yesterday"
                  class="screen-btn"
                  @click="handleProcessedCommand($event, 'importUnfinishedData')">
                  <el-icon class="el-icon-edit-outline"/>
                  日期导入
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="yesterday"
                    icon="el-icon-copy">
                    从上一日导入
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <span
              v-if="canEditQuality"
              class="screen-btn"
              @click="saveUnfinished">
              <el-icon class="el-icon-document-checked"/>
              保存
            </span>
          </div>
          质量设备
        </div>
      </template>
      <el-form :disabled="!canEditQuality">
        <el-table
          v-loading="loading"
          :data="unfinished.gridData"
          :header-cell-style="({ row })=>{
            if (row.length != 1) {
              return 'display: none'
            }
          }"
          :span-method="spanMethod"
          border>
          <el-table-column
            :label="'质量设备情况'">
            <el-table-column
              width="100"
              property="k1"/>
            <el-table-column
              width="120"
              property="k2">
              <template v-slot="{ row }">
                <template v-if="row.bz">
                  <el-input
                    v-model="row.k2"
                    :rows="4"
                    type="textarea"/>
                </template>
                <template v-else-if="row.index1In">
                  <el-input v-model="row.k2"/>
                </template>
                <template v-else>
                  {{ row.k2 }}
                </template>
              </template>
            </el-table-column>
            <el-table-column
              width="120"
              property="k3">
              <template v-slot="{ row }">
                <template v-if="row.index1In">
                  <el-input v-model="row.k3"/>
                </template>
                <template v-else>
                  {{ row.k3 }}
                </template>
              </template>
            </el-table-column>
            <el-table-column
              width="160"
              property="k4">
              <template v-slot="{ row }">
                <template v-if="!row.index2In">
                  <el-input v-model="row.k4"/>
                </template>
                <template v-else>
                  {{ row.k4 }}
                </template>
              </template>
            </el-table-column>
            <el-table-column
              width="160"
              property="k5">
              <template v-slot="{ row }">
                <template v-if="!row.index2In">
                  <el-input v-model="row.k5"/>
                </template>
                <template v-else>
                  {{ row.k5 }}
                </template>
              </template>
            </el-table-column>
            <el-table-column
              width="160"
              property="k6">
              <template v-slot="{ row }">
                <template v-if="!row.index2In">
                  <el-input v-model="row.k6"/>
                </template>
                <template v-else>
                  {{ row.k6 }}
                </template>
              </template>
            </el-table-column>
            <el-table-column
              width="160"
              property="k7">
              <template v-slot="{ row }">
                <template v-if="!row.index2In">
                  <el-input v-model="row.k7"/>
                </template>
                <template v-else>
                  {{ row.k7 }}
                </template>
              </template>
            </el-table-column>
            <el-table-column
              width="160"
              property="k8">
              <template v-slot="{ row }">
                <template v-if="!row.index2In">
                  <el-input v-model="row.k8"/>
                </template>
                <template v-else>
                  {{ row.k8 }}
                </template>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'400px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="导入日期选择">
      <template v-slot:title>
        <div class="custom-dialog-title">
          导入日期选择
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导入日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            type="primary"
            @click="importHistoryData()">确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ScreenBorder from '@/pages/screen/morningMeeting/component/screen-border'
import ScreenMixins from '@/pages/screen/morningMeeting/component/ScreenMixins'
import lodash from 'lodash'
import * as _ from 'lodash'
import { post } from '@/lib/Util'
import { firstMorningMeeting } from '@/api/screen'

export default {
  name: 'qualityEquipment',
  components: { ScreenBorder },
  mixins: [ScreenMixins],
  props: {
    selectDate: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      cDate: '',
      loading: false,
      unfinished: {
        initializedData: [
          {},
          {
            k2: 'A_LIST',
            k6: 'E_LIST'
          },
          {},
          {
            k2: 'A_LIST',
            k3: 'B_LIST',
            k4: 'C_LIST',
            k5: 'D_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {},
          {
            k4: 'C_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {
            k4: 'C_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {
            k4: 'C_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {
            k4: 'C_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {
            k4: 'C_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {
            k4: 'C_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {
            k4: 'C_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {
            k4: 'C_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {
            k4: 'C_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {
            k4: 'C_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {
            k4: 'C_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {
            k4: 'C_LIST',
            k6: 'E_LIST',
            k7: 'F_LIST',
            k8: 'G_LIST'
          },
          {
            k4: 'C_LIST'
          },
          {
            k2: 'A_LIST'
          }
        ],
        initializedData2: [
          {
            k1: '转炉工序',
            k2: '1#脱硫',
            k6: '2#脱硫',
            index2In: true
          },
          {
            k1: '转炉工序',
            index1In: true
          },
          {
            k1: '精炼工序',
            k2: '1#LF',
            k3: '2#LF',
            k4: '3#LF',
            k5: '4#LF',
            k6: '1#RH',
            k7: '2#RH',
            k8: '3#RH',
            index2In: true
          },
          {
            k1: '精炼工序',
            index1In: true
          },
          {
            k1: '精炼工序',
            k2: '项目',
            k4: '1#连铸',
            k6: '2#连铸',
            k7: '3#连铸',
            k8: '0#连铸',
            index2In: true
          },
          {
            k1: '精炼工序',
            k2: '大包包盖'
          },
          {
            k1: '精炼工序',
            k2: '结晶器'
          },
          {
            k1: '精炼工序',
            k2: '电搅段',
            k3: '2#段'
          },
          {
            k1: '精炼工序',
            k2: '电搅段',
            k3: '3#段'
          },
          {
            k1: '精炼工序',
            k2: '扇形段',
            k3: '过钢量'
          },
          {
            k1: '精炼工序',
            k2: '扇形段',
            k3: '喷嘴检查'
          },
          {
            k1: '精炼工序',
            k2: '二冷水流量'
          },
          {
            k1: '精炼工序',
            k2: '设备水流量'
          },
          {
            k1: '精炼工序',
            k2: '辊缝',
            k3: '测量周期'
          },
          {
            k1: '精炼工序',
            k2: '辊缝',
            k3: '精度'
          },
          {
            k1: '精炼工序',
            k2: '毛刺机'
          },
          {
            k1: '精炼工序',
            k2: '其他'
          },
          {
            k1: '坯料工序',
            k2: '硫印室'
          },
          {
            k1: '备注',
            index1In: true,
            bz: true
          }
        ],
        gridData: [],
        showGridData: [],
        gridMerge: [],
        maxHeight: null,
        dialogVisible: false
      }
    }
  },
  watch: {
    selectDate: function() {
      this.cDate = this.selectDate
    },
    cDate: function() {
      // 初始化数据
      this.getUnfinished({
        startTime: this.$moment(this.cDate).format('yyyyMMDD'),
        endTime: this.$moment(this.cDate).format('yyyyMMDD'),
        FLAG: '质量设备'
      })
    }
  },
  created() {
    this.cDate = this.selectDate
    this.mergeArr = ['qualitySystem']
  },
  mounted() {
    this.unfinished.maxHeight = this.$refs.table1.offsetHeight
    this.$nextTick(() => {
      console.log(this.$refs.table1.offsetHeight)
    })
  },
  methods: {
    // 获取数据
    getUnfinished(data) {
      post(firstMorningMeeting.furnaceConditionInit, data).then(res => {
        console.log(res)
        let saveData = this.unfinished.initializedData
        let datas = lodash.map(
          this.unfinished.initializedData2,
          (item, index) => {
            let resData = lodash.filter(
              res.data,
              item => item.SORT_NUM - 1 === index
            )
            if (resData.length) {
              let objData = {}
              lodash.forEach(Object.keys(saveData[index]), item => {
                objData[item] = resData[0][saveData[index][item]]
              })
              return {
                ...objData,
                ...item
              }
            } else {
              return item
            }
          }
        )
        this.unfinished.gridData = _.cloneDeep(datas)
        this.unfinished.showGridData = _.cloneDeep(datas)
      })
    },
    saveUnfinished() {
      this.loading = true
      const params = {
        prodDate: this.$moment(this.cDate).format('yyyyMMDD'),
        flag: '质量设备',
        data: lodash.map(this.unfinished.gridData, (item, index) => {
          let objData = {}
          objData.PROD_DATE = this.$moment(this.cDate).format('yyyyMMDD')
          objData.FLAG = '质量设备'
          objData.SORT_NUM = index + 1
          lodash.forEach(
            Object.keys(this.unfinished.initializedData[index]),
            items => {
              objData[this.unfinished.initializedData[index][items]] =
                item[items]
            }
          )
          return objData
        })
      }
      post(firstMorningMeeting.furnaceCondition, params).then(res => {
        this.unfinished.dialogVisible = false
        this.getUnfinished({
          startTime: this.$moment(this.cDate).format('yyyyMMDD'),
          endTime: this.$moment(this.cDate).format('yyyyMMDD'),
          FLAG: '质量设备'
        })
        this.loading = false
      })
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return {
            rowspan: 2,
            colspan: 1
          }
        }
        if (rowIndex === 2) {
          return {
            rowspan: 15,
            colspan: 1
          }
        }
        if (
          [1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16].includes(
            rowIndex
          )
        ) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      if ([1, 5].includes(columnIndex)) {
        if ([0, 1].includes(rowIndex)) {
          return {
            rowspan: 1,
            colspan: 4
          }
        }
      }
      if (columnIndex === 1) {
        if ([6, 4, 5, 12, 11, 15, 16, 17].includes(rowIndex)) {
          return {
            rowspan: 1,
            colspan: 2
          }
        }
        if ([7, 9, 13].includes(rowIndex)) {
          return {
            rowspan: 2,
            colspan: 1
          }
        }
        if ([8, 10, 14].includes(rowIndex)) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
        if (rowIndex === 18) {
          return {
            rowspan: 1,
            colspan: 7
          }
        }
      }
      if (columnIndex === 2) {
        if ([6, 4, 5, 12, 11, 15, 16, 17].includes(rowIndex)) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      if (columnIndex === 3) {
        if ([4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16].includes(rowIndex)) {
          return {
            rowspan: 1,
            colspan: 2
          }
        }
        if (rowIndex === 17) {
          return {
            rowspan: 1,
            colspan: 6
          }
        }
      }
      if (columnIndex === 4) {
        if ([4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16].includes(rowIndex)) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      if ([2, 3, 4, 6, 7].includes(columnIndex)) {
        if ([0, 1].includes(rowIndex)) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      if ([4, 5, 6, 7].includes(columnIndex)) {
        if ([17].includes(rowIndex)) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      if ([4, 5, 6, 7, 2, 3].includes(columnIndex)) {
        if ([18].includes(rowIndex)) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    importUnfinishedData(date) {
      this.loading = true
      post(firstMorningMeeting.furnaceConditionInit, {
        startTime: this.$moment(date).format('yyyyMMDD'),
        endTime: this.$moment(date).format('yyyyMMDD'),
        FLAG: '质量设备'
      }).then(res => {
        this.loading = false
        let saveData = this.unfinished.initializedData
        let datas = lodash.map(
          this.unfinished.initializedData2,
          (item, index) => {
            let resData = lodash.filter(
              res.data,
              item => item.SORT_NUM - 1 === index
            )
            if (resData.length) {
              let objData = {}
              lodash.forEach(Object.keys(saveData[index]), item => {
                objData[item] = resData[0][saveData[index][item]]
              })
              return {
                ...objData,
                ...item
              }
            } else {
              return item
            }
          }
        )
        this.unfinished.gridData = _.cloneDeep(datas)
        this.$message.success('导入成功！')
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  width: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;

  .content-hold {
    height: 32px;
  }

  .content-item {
    flex: 1;
    overflow: hidden;
    .scroll-wrapper {
      height: 100%;
      overflow: auto;
    }
  }
}

.fail-reason {
  display: flex;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;

  span:first-child {
    white-space: nowrap;
    font-size: 16px;
    line-height: 24px;
    color: #ffffff;
  }
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cards {
    height: 65px;
    text-align: right;

    .card {
      display: inline-block;
      margin-left: 2px;
      text-align: left;
      min-width: 85px;
      line-height: 28px;
      font-size: 14px;
      border: 1px solid rgba(31, 198, 255, 0.5);

      span {
        text-align: center;
        padding: 0 5px;

        em {
          font-size: 16px;
          font-weight: bold;

          &.red {
            color: #ff2855;
          }

          &.red {
            color: #19be6b;
          }
        }
      }

      .name {
        display: block;
        background: rgba(31, 198, 255, 0.5);
      }

      .num {
        display: block;
      }
    }
  }

  .chart {
    flex: 1;
    overflow: hidden;
  }
}
</style>
