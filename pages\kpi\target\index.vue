<template>

  <div
    class="page-content"
    style="height: 100%;position: relative"
  >
    <div class="page-tit">
      目标管理周期：
      <el-date-picker
        v-model="periodStr"
        :picker-options="pickerOptions"
        type="month"
        value-format="yyyy-MM"
        placeholder="选择月份"
        @change="handleClick(editNode);loadTree(treeData.cid)"/>
    </div>
    <el-container
      class="full-height"
      style="height: calc(100% - 42px)">
      <el-aside
        width="270px"
        style="overflow: unset; margin-right: 20px">
        <div class="tree-left shadow-light full-height">
          <div class="tree-tit">
            指标分类
          </div>
          <div class="tree-box">
            <el-tree
              ref="tree"
              :data="data"
              :props="defaultProps"
              highlight-current
              node-key="id"
            >
              <template
                v-slot="{node, data}"
              >
                <span
                  :class="{'first-node': node.level === 1}"
                  :title="node.label"
                  class="custom-tree-node"
                  @click.stop.prevent="handleNodeClick(node)"
                >
                  <span>{{ node.label }}</span>
                </span>

              </template>
            </el-tree>
          </div>
        </div>
      </el-aside>
      <el-container>
        <el-main style="overflow: unset; padding: 0;">
          <div
            class="full-height page-card shadow-light"
          >
            <el-empty
              v-if="!treeData.name"
              description="暂未选择分类"/>
            <el-row
              v-else
              :gutter="40"
              class="full-height page-right">
              <el-col
                :span="16"
                class="full-height">
                <div
                  class="full-height overflow-auto"
                  style="height: calc(100% - 45px); margin-bottom: 10px">
                  <kpi-node
                    :node="treeData"
                    :only-warning="onlyWarning"
                    :rank="rank"
                    :show-chart="false"
                    :num-name="'basicValue'"
                    :num-editable="canEdit"
                    @handleClick="handleClick"/>
                </div>
                <div 
                  v-if="canEdit" 
                  class="text-right">
                  <el-button
                    type="primary"
                    @click="saveTarget()">保存</el-button>
                  <el-button
                    @click="loadTree(treeData.cid)">重置</el-button>
                </div>
              </el-col>
              <el-col
                :span="8"
                class="full-height">
                <div class="full-height overflow-auto">
                  <div class="tree-tit">
                    {{ editNode.name }}
                  </div>
                  <div class="history-chart">
                    <div
                      id="chart"
                      class="chart"/>
                  </div>
                  <div
                    v-if="gridData.length"
                    class="history-table">
                    <el-table
                      :data="gridData"
                      border>
                      <el-table-column
                        property="type"
                        label="数据类型"/>
                      <el-table-column
                        min-width="60"
                        property="period"
                        label="周期"/>
                      <el-table-column
                        min-width="60"
                        property="value"
                        label="值"/>
                      <el-table-column
                        width="80"
                        property="ruleStatus"
                        label="操作">
                        <template
                          v-slot="{row}"
                        >
                          <el-button
                            v-if="canEdit"
                            size="mini"
                            type="text"
                            @click="handleSync(row)"
                          >同步数据
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>

</template>

<script>
import { post } from '@/lib/Util'
import {
  deleteKpiIndex,
  findByTypeGroupByFeature,
  findKpiDatas,
  findKpiDataTree,
  findKpiTargetTree,
  saveByTargetMark
} from '@/api/kpi'
import Edit from './component/edit'
import { ENUM } from '@/lib/Constant'
import { findBySpecification } from '@/api/kpi'
import KpiNode from '@/components/kpiTree/KpiNode'
import moment from 'moment'

export default {
  name: 'kpi-target',
  components: {
    KpiNode,
    Edit
  },
  data: () => {
    return {
      url: {
        list: findBySpecification, //分页接口地址
        delete: deleteKpiIndex //删除接口地址
      },
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      type: '0',
      factoryList: ENUM.factoryListForecast,
      kpiFunction: ENUM.kpiFunction,
      levelList: ENUM.levelList,
      statusList: [
        {
          value: 0,
          label: '正常',
          type: 'success'
        },
        {
          value: 1,
          label: '废弃',
          type: 'warning'
        }
      ],
      onlyWarning: false,
      rank: 3,
      cid: null, // 当前cid
      warningColor: '#ee6666',
      treeData: {},
      editNode: {},
      gridData: [],
      periodStr: '',
      period: {
        year: null,
        month: null
      },
      chart: null,
      chartOption: null
    }
  },
  computed: {
    pickerOptions: function() {
      return {
        disabledDate: time => {
          return (
            time.getTime() > this.$moment().month(this.$moment().month() + 1)
          )
        }
      }
    },
    canEdit: function() {
      // console.log(moment().format('yyyy-MM-DD'), this.cDate)
      return (
        moment()
          .subtract(1, 'month')
          .format('yyyy-MM') < this.periodStr
      )
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.periodStr = this.$moment()
      .month(this.$moment().month() + 1)
      .format('yyyy-MM')
    this.$nextTick(() => {
      this.loadData()
    })
  },
  methods: {
    // 左侧树
    async loadData() {
      const { data } = await post(findByTypeGroupByFeature, { type: this.type })
      // this.data = data
      const list = this.kpiFunction.map(item => {
        item.id = 'kpi' + item.value
        item.name = item.label
        return item
      })
      if (data) {
        this.data = list
          .map(item => {
            item.children = data
              .filter(kpi => kpi.FEATURE == item.value)
              .map(i => {
                i.id = i.ID
                i.name = i.NAME
                i.isShow = !!i.ISSHOW
                i.feature = i.FEATURE
                i.type = i.TYPE
                return i
              })
            return item
          })
          .filter(item => item.children.length)
      }
    },
    async handleNodeClick(data) {
      //
      if (data.level === 1) return
      this.loadTree(data.data.ID)
    },
    async loadTree(cid) {
      this.onlyWarning = false
      this.loading = true
      this.reset()
      if (!cid) return
      const data = await post(findKpiTargetTree, {
        cid: cid,
        period: this.periodStr
      })
      if (data.success) {
        this.treeData = data.data.length ? data.data[0] : {}
        this.treeData.cid = cid
      }
      console.log(this.treeData)
      this.loading = false
    },
    reset() {
      this.treeData = {}
      this.editNode = {}
      this.chart = null
      this.gridData = []
    },
    handleClick(e) {
      console.log(e)
      // {"month":"11","year":"2022","kid":"1"}
      if (!e) return
      this.editNode = e
      this.gridData = []
      post(findKpiDatas, {
        month: this.periodStr.substring(5, 7),
        year: this.periodStr.substring(0, 4),
        kid: this.editNode.kid
      }).then(res => {
        // avg
        // historical
        // max
        // min
        // productionForecast
        // warningDatas
        if (!res.success) return
        this.gridData = [
          {
            type: '系统预测值',
            period: res.data.productionForecast.period,
            value:
              typeof res.data.productionForecast.data === 'number'
                ? res.data.productionForecast.data.toFixed(2)
                : '暂无数据'
          },
          {
            type: '历史平均值',
            period: res.data.avg.period,
            value:
              typeof res.data.avg.data === 'number'
                ? res.data.avg.data.toFixed(2)
                : '暂无数据'
          },
          {
            type: '历史同期值',
            period: res.data.historical.period,
            value:
              typeof res.data.historical.data === 'number'
                ? res.data.historical.data.toFixed(2)
                : '暂无数据'
          },
          {
            type: '历史最佳',
            period: res.data.max.period,
            value:
              typeof res.data.max.data === 'number'
                ? res.data.max.data.toFixed(2)
                : '暂无数据'
          },
          {
            type: '历史最差',
            period: res.data.min.period,
            value:
              typeof res.data.min.data === 'number'
                ? res.data.min.data.toFixed(2)
                : '暂无数据'
          }
        ]
        this.drawChart(res.data)
      })
    },
    handleSync(row) {
      this.editNode.basicValue = row.value
    },

    // 保存目标值
    saveTarget() {
      const params = {}
      this.getValues(params, this.treeData)
      console.log(params)
      post(saveByTargetMark + '/' + this.periodStr, params).then(res => {
        console.log(res)
        if (res.success) {
          this.$message.success('保存成功！')
        }
      })
    },

    // 递归获取目标值
    getValues(params, tree) {
      params[tree.kid] = tree.basicValue || 0
      tree.children.length &&
        tree.children.forEach(item => {
          this.getValues(params, item)
        })
    },

    drawChart(data) {
      if (!this.chart) {
        this.chart = this.$echarts.init(
          document.getElementById('chart'),
          'light'
        )
      }
      this.chartOption = {
        xAxis: {
          type: 'category',
          data: data.periods
        },
        yAxis: {
          type: 'value'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        grid: {
          top: '3%',
          left: '3%',
          right: '3%',
          bottom: '3%',
          containLabel: true
        },
        series: [
          {
            data: data.values,
            type: 'line'
          }
        ]
      }
      this.chart.setOption(this.chartOption)
      // 监听窗口改动
      window.addEventListener('resize', () => {
        this.chart && this.chart.resize()
      })
    }
  }
}
</script>

<style
  scoped
  lang="less"
>
.page-right {
  &:before {
    display: block;
    content: '';
    position: absolute;
    right: 33.6%;
    top: 20px;
    bottom: 20px;
    border: 1px solid #eee;
  }
}

.table-pagination {
  margin-top: 20px;
}

.page-tit {
  margin-bottom: 12px;
  font-size: 20px;
  line-height: 1.5;
  color: #333;
}
.tree-tit {
  margin-bottom: 5px;
  padding: 10px;
  font-size: 18px;
  line-height: 1.5;
}
.history-chart {
  padding-top: 60%;
  position: relative;
  margin-bottom: 20px;
  .chart {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
}
.tree-left {
  overflow: auto;
  padding: 5px;
  background: #fff;
  border: 1px solid #eee;
  .tree-box {
    height: calc(100% - 55px);
    overflow: auto;
  }
}

.custom-tree-node {
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.first-node {
  font-size: 16px;
}

/deep/ .el-tree-node {
  margin: 5px 0;
}

/deep/ .el-tree > .el-tree-node {
  margin: 0 0 12px;
}

.contextmenu {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  background: #fff;
  font-size: 14px;
  color: #666;
  border: 1px solid #e9e9e9;
  min-width: 250px;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);

  li {
    padding: 7px 16px;
    cursor: pointer;

    &:hover {
      background: #f4f4f5;
    }
  }

  li:last-child {
    border-top: 1px solid #e9e9e9;
  }
}
</style>
