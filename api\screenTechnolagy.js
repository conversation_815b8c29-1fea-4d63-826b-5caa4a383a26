const path = 'mesAPI/'
const pathEnergy = 'ems/'

// 试制计划
export const progressReportingSave = path + 'progressReporting/saveAll'
export const progressReportingFindAllBySetDate =
  path + 'progressReporting/findAllBySetDate'
export const progressReportingDeleteAllById =
  path + 'progressReporting/deleteAllById'

// 完成率
export const findTpQuantity = path + 'progressReporting/findTpQuantity'
export const findTpNum = path + 'progressReporting/findTpNum'

// 总量
export const findTppTotal = path + 'progressReporting/findTppTotal'

// 工艺绩效
export const checklistBySetDate = path + 'checklist/findAllBySetDate'

//施工作业公示
export const constructionWorkInfoFindAllDate =
  path + 'constructionWorkInfo/findAllDate'
export const constructionWorkInfoDelById = path + 'constructionWorkInfo/delById'
export const constructionWorkInfoSaveAll = path + 'constructionWorkInfo/saveAll'
export const checklistSave = path + 'checklist/saveAll'

export const checklistDelete = path + 'checklist/deleteAllById'

//各科室检查次数
export const findCountDeptBySetDate = path + 'checklist/findCountDeptBySetDate'

//各产线扣分
export const findDpPltBySetDate = path + 'checklist/findDpPltBySetDate'

//统计各产线检查次数
export const findCountPltBySetDate = path + 'checklist/findCountPltBySetDate'

// 协调事项
//查询
export const coordinationFindAll =
  path + 'rdmsCoordinationMatters/findAllBySetDate'
export const findCoordinationItemByDate =
  path + 'CoordinationItem/findCoordinationItemByDate'
// 新增
export const coordinationSaveAll = path + 'rdmsCoordinationMatters/saveAll'
export const CoordinationItemSave = path + 'CoordinationItem/saveItems'
// 删除
export const coordinationDelete = path + 'rdmsCoordinationMatters/deleteAllById'
export const CoordinationItemdeleteById = path + 'CoordinationItem/deleteById'
// 轧钢检验
//查询
export const steelRollingInspectionFindAll =
  path + 'steelRollingInspection/findAllBySetDate'
// 新增
export const steelRollingInspectionSaveAll =
  path + 'steelRollingInspection/saveAll'

// 轧钢工艺命中
export const steelRollingRate = path + 'technology/steelRolling'
// 炼钢工艺命中
export const steelMakingPhr = path + 'technology/steelMakingPhr'
export const findAlarmCount = path + 'QualitySystem/findAlarmCount'

// 轧钢目标命中
export const rollingParameters = path + 'technology/rollingParameters'
// 极值目标命中率
export const peakTargetHitRate = path + 'technology/peakTargetHitRate'
// 炼钢大于目标
export const steelMakingCompositionMax = path + 'technology/findMaxBySetDate'
// 炼钢小于目标
export const steelMakingCompositionMin = path + 'technology/findMinBySetDate'
// 轧钢工艺命中率C1C3   曲线
export const steelRollingC1C3 = path + 'technology/steelRollingC1C3'

// 麻水板形
export const passRateFind = path + 'passRate/findAllBySetDate'
export const passRateSave = path + 'passRate/saveAll'

//技术研发处看板 - 轧钢工艺 - 入水温度
export const steelEntry = path + 'technology/steelEntry'

// 品种合格率
export const breedTargetFind = path + 'breedTarget/findAllBySetDate'
export const breedTargetSave = path + 'breedTarget/saveAll'

// 性能一次合格率
export const pfmcFirstPassRateFind = path + 'pfmcFirstPassRate/findAllBySetDate'
export const pfmcFirstPassRateSave = path + 'pfmcFirstPassRate/saveAll'

// 性能一次合格率
export const unplannedDayFind = path + 'unplannedDay/findAllBySetDate'
export const unplannedDaySave = path + 'unplannedDay/saveAll'

// 板形、麻水合格率
export const findSteelRollingBySetDate =
  path + 'passRate/findSteelRollingBySetDate'

// 重点订单
export const findKeyOrderByDate = path + 'KeyOrders/findKeyOrderByDate'
export const saveKeyOrders = path + 'KeyOrders/saveAll'

// Ni系
export const findNiYieldByDate = path + 'NiSteel/findNiYieldByDate'
export const saveNiYieldLoss = path + '/NiSteel/saveNiYieldLoss'
export const findNiYieldLossByDate = path + 'NiSteel/findNiYieldLossByDate'
export const NiSteelSaveAll = path + '/NiSteel/saveAll'

export const findNiYieldLossInfoByDate =
  path + 'NiSteel/findNiYieldLossInfoByDate'

// 品种合格率指标维护
export const breedTargetSaveAllConfig = path + 'breedTarget/saveAllConfig'
export const breedTargetFindConfig = path + 'breedTarget/findConfig'

// 技术研发处看板-->性能检验-->轧钢检验批次一次合格率
export const steelRollingFindAll =
  path + 'steelRollingInspection/findAllByBetweenDate'
