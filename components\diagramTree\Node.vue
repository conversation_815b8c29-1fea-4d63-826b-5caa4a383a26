<template>
  <div class="node">
    <el-popover
      :open-delay="200"
      placement="left"
      width="400"
      trigger="hover"
      @show="getRule">
      <el-table :data="gridData">
        <el-table-column
          property="ruleName"
          label="规则名"/>
        <el-table-column
          min-width="60"
          property="targetValue"
          label="目标值"/>
        <el-table-column
          min-width="60"
          property="resultValue"
          label="实际值"/>
        <el-table-column
          width="80"
          property="ruleStatus"
          label="预警状态">
          <template
            v-slot="{row}"
          >
            <el-tag
              :type="row.ruleStatus ? 'danger' : 'success'"
              disable-transitions
            >{{ row.ruleStatus ? '预警中' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div
        slot="reference"
        class="node-inner"
        @click="showChart">
        <div class="node-describe">
          {{ node.name }}<br>
          <span class="result">
            <template v-if="node.coreRid">
              {{ getCoreResultValue(node) }} <small>{{ node.unit }}</small>
            </template>
          </span>
        </div>
        <el-icon
          v-if="node.children && node.children.length"
          :class="node.hiddenChildren ? 'el-icon-caret-right' : 'el-icon-caret-left'"
          :title="node.hiddenChildren ? '展开' : '收起'"
          class="node-arrow"
          @click.native.prevent.stop="changeStatus"/>
      </div>
    </el-popover>
    <node-chart
      v-if="chartVisible"
      ref="nodeChart"
      v-model="chartVisible"
      :node="node"
      append-to-body/>
  </div>
</template>

<script>
import { getCoreResultValue, post } from '@/lib/Util'
import { findResultValueOfMonthAndDay, findRulesOfKpi } from '@/api/kpi'
import NodeChart from '@/components/kpiTree/NodeChart'

export default {
  name: 'Node',
  components: { NodeChart },
  props: {
    node: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {}
    }
  },
  data() {
    return {
      gridData: [],
      loading: false,
      chartVisible: false
    }
  },
  mounted() {
    this.loading = true
  },
  methods: {
    getRule() {
      if (this.gridData.length) return
      post(findRulesOfKpi, { kid: this.node.kid })
        .then(res => {
          this.gridData = res.data
          this.loading = false
        })
        .catch(e => {
          this.loading = false
        })
    },
    getCoreResultValue(item) {
      return getCoreResultValue(item)
    },
    changeStatus() {
      this.$emit('changeStatus')
    },
    showChart() {
      // this.chartVisible = true
      this.$emit('nodeClick')
    }
  }
}
</script>

<style scoped lang="less">
.node-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .node-describe {
    margin-right: 15px;
    small {
      color: #666;
      font-size: 10px;
    }
  }
  .node-arrow {
    cursor: pointer;
  }
  span {
    display: block;
    color: #5e93ed;
    font-weight: 600;
    font-size: 20px;
    line-height: 28px;
    height: 28px;
  }
  &.trend {
    border-color: #ffa958;
    background: #fff6ee;
    span {
      color: #ffa958;
    }
  }
}
.node {
  &:focus {
    outline: none;
  }
}
</style>
