<template>
  <div>
    <el-dialog
      :title="title + '指标'"
      :visible.sync="visible"
      :width="'600px'"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="150px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="指标名称"
          prop="name"
        >
          <el-input
            v-model="formData.name"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入指标名称"
          />
        </el-form-item>
        <el-form-item
          label="分类"
          prop="category"
        >
          <el-select
            v-model="formData.category"
            :style="{width: '100%'}"
            size="small"
            clearable
            placeholder="请选择分类"
          >
            <el-option
              v-for="(item, index) in kpiFunction"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          label="层级"
          prop="rank"
        >
          <el-select
            v-model="formData.rank"
            :style="{width: '100%'}"
            size="small"
            disabled
            placeholder="请选择层级"
          >
            <el-option
              v-for="(item, index) in levelList"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="formData.pid !== 0 && editType === 'add'"
          label="上级指标"
          disabled
          prop="pid"
        >
          <el-input
            v-model="formData.parentName"
            :style="{width: '100%'}"
            disabled
            placeholder="上级指标"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { saveKpiIndex } from '@/api/kpi'
import { ENUM } from '@/lib/Constant'
import SelectKpi from '@/components/SelectKpi'
import { cloneDeep } from 'lodash/lang'

export default {
  components: { SelectKpi },
  mixins: [EditMixins],
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  data() {
    return {
      visible: false,
      url: {
        edit: saveKpiIndex,
        add: saveKpiIndex
      },
      kpiFunction: [
        {
          value: 'A',
          label: 'A'
        },
        {
          value: 'B',
          label: 'B'
        },
        {
          value: 'C',
          label: 'C'
        },
        {
          value: 'D',
          label: 'D'
        },
        {
          value: 'E',
          label: 'E'
        },
        {
          value: 'F',
          label: 'F'
        }
      ],
      factoryList: ENUM.factoryList,
      levelList: ENUM.levelList,
      statusList: [
        {
          value: 0,
          label: '正常',
          type: 'success'
        },
        {
          value: 1,
          label: '废弃',
          type: 'warning'
        }
      ],
      formData: {
        category: null,
        name: null,
        rank: null,
        pid: null,
        parentName: null
      },
      rules: {
        category: [
          {
            required: true,
            message: '请选择分类',
            trigger: 'change'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'change'
          }
        ],
        rank: [
          {
            required: true,
            message: '请选择层级',
            type: 'number',
            trigger: 'change'
          }
        ],
        pid: [
          {
            required: true,
            message: '请选择上级指标'
          }
        ]
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    console.log('编辑页面')
  },
  methods: {
    submitAfter(e) {
      this.$emit('success', this.editType, e.data)
    }
  }
}
</script>
<style scoped>
</style>
